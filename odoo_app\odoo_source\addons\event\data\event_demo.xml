<?xml version="1.0"?>
<odoo><data>

    <!-- Event -->
    <record id="event.event_0" model="event.event">
        <field name="name">Design Fair Los Angeles</field>
        <field name="user_id" ref="base.user_demo"/>
        <field name="date_begin" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d 08:00:00')"/>
        <field name="date_end" eval="(DateTime.now() + timedelta(days=14)).strftime('%Y-%m-%d 18:00:00')"/>
        <field name="seats_limited">True</field>
        <field name="seats_max">50</field>
        <field name="address_id" ref="event.res_partner_location_2"/>
        <field name="date_tz">America/Los_Angeles</field>
        <field name="event_type_id" ref="event_type_0"/>
        <field name="stage_id" ref="event_stage_booked"/>
        <field name="tag_ids" eval="[(4, ref('event.event_tag_category_1_tag_1')), (4, ref('event.event_tag_category_2_tag_1'))]"/>
        <field name="ticket_instructions" type="html">
<div class="text-center fw-bold py-3">Important ticket information</div>
<ul>
    <li>Please come <b>at least</b> 30 minutes before the beginning of the event.</li>
    <li>Tickets can be printed or scanned directly from your phone.</li>
    <li>If you don't have this ticket, you will <b>not</b> be allowed entry!</li>
</ul>
        </field>
    </record>
    <record id="event_0_ticket_0" model="event.event.ticket">
        <field name="name">Free</field>
        <field name="description">Free entrance, no food !</field>
        <field name="event_id" ref="event.event_0"/>
        <field name="start_sale_datetime" eval="(DateTime.today() + timedelta(days=5)).strftime('%Y-%m-%d 00:00:00')"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(days=10)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">0</field>
    </record>
    <record id="event_0_ticket_1" model="event.event.ticket">
        <field name="name">Standard</field>
        <field name="description">For only 10, you gain access to catering. Yum yum.</field>
        <field name="event_id" ref="event.event_0"/>
        <field name="start_sale_datetime" eval="(DateTime.today() + timedelta(days=5)).strftime('%Y-%m-%d 00:00:00')"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(days=10)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">50</field>
    </record>
    <record id="event_0_ticket_2" model="event.event.ticket">
        <field name="name">VIP</field>
        <field name="description">You are truly among the best.</field>
        <field name="event_id" ref="event.event_0"/>
        <field name="start_sale_datetime" eval="(DateTime.today() + timedelta(days=5)).strftime('%Y-%m-%d 00:00:00')"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(days=10)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">10</field>
    </record>

    <record id="event.event_1" model="event.event">
        <field name="name">Great Reno Ballon Race</field>
        <field name="user_id" ref="base.user_admin"/>
        <field eval="(DateTime.today()+ timedelta(days=100)).strftime('%Y-%m-%d 20:15:00')" name="date_begin"/>
        <field eval="(DateTime.today()+ timedelta(days=101)).strftime('%Y-%m-%d 00:30:00')" name="date_end"/>
        <field name="event_type_id" ref="event_type_2"/>
        <field name="address_id" ref="event.res_partner_location_0"/>
        <field name="stage_id" ref="event_stage_booked"/>
        <field name="kanban_state">blocked</field>
        <field name="tag_ids" eval="[(4, ref('event.event_tag_category_1_tag_4')), (4, ref('event.event_tag_category_2_tag_3'))]"/>
    </record>

    <record id="message_event_1_0" model="mail.message">
        <field name="model">event.event</field>
        <field name="res_id" ref="event.event_1"/>
        <field name="body" type="html"><p>Hello Marc Demo,<br/>
            Our flight authorizations have been revoked due to insurance issues.<br/>
            Could you take care of it as soon as possible ?</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_admin"/>
    </record>
    <record id="message_event_1_1" model="mail.message">
        <field name="model">event.event</field>
        <field name="res_id" ref="event.event_1"/>
        <field name="parent_id" ref="message_event_1_0"/>
        <field name="body" type="html"><p>Hi Mitchell Admin,<br/>I will take care of it today !</p></field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <record id="message_event_1_2" model="mail.message">
        <field name="model">event.event</field>
        <field name="res_id" ref="event.event_1"/>
        <field name="parent_id" ref="message_event_1_1"/>
        <field name="body" type="html"><p>Great ! This event will stay "blocked" until it is fixed.<br/>
        Feel free to green it once everything is in order.</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_admin"/>
    </record>
    <record id="activity_event_1_0" model="mail.activity">
        <field name="res_id" ref="event.event_1" />
        <field name="res_model_id" ref="event.model_event_event"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call"/>
        <field name="summary">Call the local state house.</field>
        <field name="date_deadline" eval="DateTime.today()"/>
        <field name="create_uid" ref="base.user_demo"/>
        <field name="user_id" ref="base.user_demo"/>
    </record>

    <record id="event_2" model="event.event">
        <field name="name">Conference for Architects</field>
        <field name="user_id" ref="base.user_admin"/>
        <field eval="(DateTime.today()+ timedelta(days=5)).strftime('%Y-%m-%d 07:00:00')" name="date_begin"/>
        <field eval="(DateTime.today()+ timedelta(days=5)).strftime('%Y-%m-%d 16:30:00')" name="date_end"/>
        <field name="event_type_id" ref="event_type_data_conference"/>
        <field name="address_id" ref="event.res_partner_location_2"/>
        <field name="seats_limited">True</field>
        <field name="seats_max">200</field>
        <field name="stage_id" ref="event_stage_booked"/>
        <field name="tag_ids" eval="[(4, ref('event.event_tag_category_1_tag_4')), (4, ref('event.event_tag_category_2_tag_1'))]"/>
    </record>
    <record id="event_2_ticket_1" model="event.event.ticket">
        <field name="name">Standard</field>
        <field name="event_id" ref="event.event_2"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(90)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">50</field>
    </record>
    <record id="event_2_ticket_2" model="event.event.ticket">
        <field name="name">VIP</field>
        <field name="event_id" ref="event.event_2"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(60)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">5</field>
    </record>
    <record id="activity_event_2_0" model="mail.activity">
        <field name="res_id" ref="event.event_2" />
        <field name="res_model_id" ref="event.model_event_event"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call"/>
        <field name="summary">Call the caterer.</field>
        <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=3)).strftime('%Y-%m-%d %H:%M')"/>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="event_2_mail_0" model="event.mail">
        <field name="event_id" ref="event.event_2"/>
        <field name="template_ref" eval="'mail.template,%i' % ref('event.event_subscription')"/>
    </record>

    <record id="event.event_3" model="event.event">
        <field name="name">Live Music Festival</field>
        <field name="user_id" ref="base.user_demo"/>
        <field name="date_begin" eval="(DateTime.today()+ timedelta(days=130)).strftime('%Y-%m-%d 20:15:00')"/>
        <field name="date_end" eval="(DateTime.today()+ timedelta(days=133)).strftime('%Y-%m-%d 00:30:00')"/>
        <field name="date_tz">Europe/London</field>
        <field name="event_type_id" ref="event_type_0"/>
        <field name="address_id" ref="event.res_partner_location_1"/>
        <field name="stage_id" ref="event_stage_announced"/>
        <field name="tag_ids" eval="[(4, ref('event.event_tag_category_1_tag_3')), (4, ref('event.event_tag_category_2_tag_2'))]"/>
    </record>
    <record id="event_3_ticket_0" model="event.event.ticket">
        <field name="name">Standard</field>
        <field name="event_id" ref="event.event_3"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(days=20)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">1200</field>
    </record>
    <record id="event_3_ticket_1" model="event.event.ticket">
        <field name="name">VIP</field>
        <field name="event_id" ref="event.event_3"/>
        <field name="end_sale_datetime" eval="(DateTime.today() + timedelta(days=20)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">50</field>
    </record>
    <record id="activity_event_3_0" model="mail.activity">
        <field name="res_id" ref="event.event_3" />
        <field name="res_model_id" ref="event.model_event_event"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call"/>
        <field name="summary">Prepare interview with local media.</field>
        <field name="date_deadline" eval="DateTime.today().strftime('%Y-%m-%d %H:%M')"/>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="event_3_mail_0" model="event.mail">
        <field name="event_id" ref="event.event_3"/>
        <field name="template_ref" eval="'mail.template,%i' % ref('event.event_subscription')"/>
    </record>

    <!-- EVENT_4: very limited, intended to test seats reservation -->
    <record id="event.event_4" model="event.event">
        <field name="name">Business workshops</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="date_begin" eval="(DateTime.today() - timedelta(days=5)).strftime('%Y-%m-%d 18:00:00')"/>
        <field name="date_end" eval="(DateTime.today() - timedelta(days=5)).strftime('%Y-%m-%d 22:30:00')"/>
        <field name="seats_limited">True</field>
        <field name="seats_max">4</field>
        <field name="address_id" ref="event.res_partner_location_2"/>
        <field name="date_tz">America/Los_Angeles</field>
        <field name="event_type_id" ref="event_type_1"/>
        <field name="stage_id" ref="event_stage_done"/>
        <field name="kanban_state">done</field>
        <field name="tag_ids" eval="[(4, ref('event.event_tag_category_1_tag_4')), (4, ref('event.event_tag_category_2_tag_1'))]"/>
    </record>
    <record id="event_4_ticket_0" model="event.event.ticket">
        <field name="name">General Admission</field>
        <field name="event_id" ref="event.event_4"/>
        <field name="end_sale_datetime" eval="(DateTime.today() - timedelta(30)).strftime('%Y-%m-%d 23:00:00')"/>
        <field name="seats_max">4</field>
    </record>
    <record id="activity_event_4_0" model="mail.activity">
        <field name="res_id" ref="event.event_4" />
        <field name="res_model_id" ref="event.model_event_event"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call"/>
        <field name="summary">Prepare after movie.</field>
        <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=3)).strftime('%Y-%m-%d %H:%M')"/>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <record id="event.event_5" model="event.event">
        <field name="name">Hockey Tournament</field>
        <field name="user_id" ref="base.user_demo"/>
        <field eval="(DateTime.today()+ timedelta(days=370)).strftime('%Y-%m-%d 09:00:00')" name="date_begin"/>
        <field eval="(DateTime.today()+ timedelta(days=371)).strftime('%Y-%m-%d 17:00:00')" name="date_end"/>
        <field name="event_type_id" ref="event_type_2"/>
        <field name="address_id" ref="event.res_partner_location_1"/>
        <field name="tag_ids" eval="[(6, 0, [ref('event.event_tag_category_1_tag_2'), ref('event.event_tag_category_2_tag_3')])]"/>
    </record>

    <record id="event.event_6" model="event.event">
        <field name="name">An unpublished event</field>
        <field name="user_id" ref="base.user_admin"/>
        <field eval="(DateTime.today()+ timedelta(days=30)).strftime('%Y-%m-%d 09:30:00')" name="date_begin"/>
        <field eval="(DateTime.today()+ timedelta(days=30)).strftime('%Y-%m-%d 17:30:00')" name="date_end"/>
        <field name="event_type_id" ref="event_type_0"/>
        <field name="address_id" ref="event.res_partner_location_1"/>
    </record>

    <record id="event.event_7" model="event.event">
        <field name="name">OpenWood Collection Online Reveal</field>
        <field name="date_tz">Europe/Brussels</field>
        <field name="event_type_id" ref="event_type_0"/>
        <field name="stage_id" ref="event.event_stage_booked"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="auto_confirm" eval="True"/>
        <field name="date_begin" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d 05:00:00')"/>
        <field name="date_end" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 15:00:00')"/>
        <field name="address_id" eval="False"/>
        <field name="tag_ids" eval="[(4, ref('event.event_tag_category_3_tag_1'))]"/>
        <field name="description" type="html">
<div class="oe_structure">
    <h5>The finest OpenWood furnitures are coming to your house in a brand new collection</h5>
    <p>And this time, we go fully ONLINE! Meet us in our live streams from the comfort of your house.<br/>
    Special discount codes will be handed out during the various streams, make sure to be there on time.</p>
    <p class="mb-3">For any additional information, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    <div class="bg-light rounded-end border-start border-secondary p-3 mb-5" style="border-start-width: 3px !important;">
        <p class="mb-1">This event is fully online and FREE, if you have paid for tickets, you should get a refund.<br/>
        It will require a good Internet connection to get the best video quality.</p>
    </div>
</div>
        </field>
    </record>
    <record id="event_7_ticket_1" model="event.event.ticket">
        <field name="name">Standard</field>
        <field name="event_id" ref="event.event_7"/>
        <field name="end_sale_datetime" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d 15:00:00')"/>
    </record>
    <record id="event_7_ticket_2" model="event.event.ticket">
        <field name="name">VIP</field>
        <field name="event_id" ref="event.event_7"/>
        <field name="end_sale_datetime" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d 15:00:00')"/>
        <field name="seats_max">10</field>
    </record>

</data></odoo>
