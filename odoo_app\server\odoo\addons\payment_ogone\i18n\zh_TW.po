# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ogone
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>doo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_userid
msgid "API User ID"
msgstr "API 使用者 ID"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_password
msgid "API User Password"
msgstr "API 使用者密碼"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__code
msgid "Code"
msgstr "程式碼"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "無法建立與 API 的連線。"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_hash_function
msgid "Hash function"
msgstr "雜湊函數"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__code__ogone
#: model:payment.provider,name:payment_ogone.payment_provider_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_pspid
msgid "PSPID"
msgstr "PSPID"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "收到的付款狀態無效的資料：%s"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_in
msgid "SHA Key IN"
msgstr "SHA Key IN"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_out
msgid "SHA Key OUT"
msgstr "SHA Key OUT"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha1
msgid "SHA1"
msgstr "SHA1"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha256
msgid "SHA256"
msgstr "SHA256"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha512
msgid "SHA512"
msgstr "SHA512"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Storing your payment details is necessary for future use."
msgstr "儲存你的付款詳情是必要的，以便將來使用。"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_userid
msgid "The ID solely used to identify the API user with Ogone"
msgstr "只用於向 Ogone 識別 API 用戶的識別碼"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_pspid
msgid "The ID solely used to identify the account with Ogone"
msgstr "只用於向 Ogone 識別該帳戶的識別碼"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "與 API 通訊失敗。"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The payment has been declined: %s"
msgstr "付款已被拒絕：%s"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "此付款服務商的技術代碼。"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "交易未有連結至代碼。"

#. module: payment_ogone
#: model_terms:ir.ui.view,arch_db:payment_ogone.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Stripe</strong>."
msgstr ""
"此服務商已被棄用。\n"
"                    請考慮將它設為停用，並轉用 <strong>Stripe</strong>。"

#. module: payment_ogone
#: model_terms:payment.provider,auth_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been authorized."
msgstr "您的付款已獲授權。"

#. module: payment_ogone
#: model_terms:payment.provider,cancel_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been cancelled."
msgstr "您的付款已被取消。"

#. module: payment_ogone
#: model_terms:payment.provider,pending_msg:payment_ogone.payment_provider_ogone
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "您的付款已成功處理，但正在等待批准。"

#. module: payment_ogone
#: model_terms:payment.provider,done_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been successfully processed."
msgstr "你的付款已成功處理。"
