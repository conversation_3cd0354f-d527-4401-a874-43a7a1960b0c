<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="mailbox — Manipulate mailboxes in various formats" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/mailbox.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/mailbox.py This module defines two classes, Mailbox and Message, for accessing and manipulating on-disk mailboxes and the messages they contain. Mailbox offers a dictionary-like ma..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/mailbox.py This module defines two classes, Mailbox and Message, for accessing and manipulating on-disk mailboxes and the messages they contain. Mailbox offers a dictionary-like ma..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>mailbox — Manipulate mailboxes in various formats &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="mimetypes — Map filenames to MIME types" href="mimetypes.html" />
    <link rel="prev" title="json — JSON encoder and decoder" href="json.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/mailbox.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a><ul>
<li><a class="reference internal" href="#mailbox-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> objects</a><ul>
<li><a class="reference internal" href="#maildir-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> objects</a></li>
<li><a class="reference internal" href="#mbox-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> objects</a></li>
<li><a class="reference internal" href="#mh-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> objects</a></li>
<li><a class="reference internal" href="#babyl-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Babyl</span></code> objects</a></li>
<li><a class="reference internal" href="#mmdf-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDF</span></code> objects</a></li>
</ul>
</li>
<li><a class="reference internal" href="#message-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> objects</a><ul>
<li><a class="reference internal" href="#maildirmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#mboxmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#mhmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#babylmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#mmdfmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> objects</a></li>
</ul>
</li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="json.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mimetypes.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mailbox.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mimetypes.html" title="mimetypes — Map filenames to MIME types"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="json.html" title="json — JSON encoder and decoder"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" accesskey="U">Internet Data Handling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-mailbox">
<span id="mailbox-manipulate-mailboxes-in-various-formats"></span><h1><a class="reference internal" href="#module-mailbox" title="mailbox: Manipulate mailboxes in various formats"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code></a> — Manipulate mailboxes in various formats<a class="headerlink" href="#module-mailbox" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/mailbox.py">Lib/mailbox.py</a></p>
<hr class="docutils" />
<p>This module defines two classes, <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> and <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a>, for
accessing and manipulating on-disk mailboxes and the messages they contain.
<code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> offers a dictionary-like mapping from keys to messages.
<code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> extends the <a class="reference internal" href="email.message.html#module-email.message" title="email.message: The base class representing email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.message</span></code></a> module’s
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> class with format-specific state and behavior.
Supported mailbox formats are Maildir, mbox, MH, Babyl, and MMDF.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="email.html#module-email" title="email: Package supporting the parsing, manipulating, and generating email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code></a></dt><dd><p>Represent and manipulate messages.</p>
</dd>
</dl>
</div>
<section id="mailbox-objects">
<span id="id1"></span><h2><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> objects<a class="headerlink" href="#mailbox-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.Mailbox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">Mailbox</span></span><a class="headerlink" href="#mailbox.Mailbox" title="Link to this definition">¶</a></dt>
<dd><p>A mailbox, which may be inspected and modified.</p>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> class defines an interface and is not intended to be
instantiated.  Instead, format-specific subclasses should inherit from
<code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> and your code should instantiate a particular subclass.</p>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> interface is dictionary-like, with small keys
corresponding to messages. Keys are issued by the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance
with which they will be used and are only meaningful to that <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code>
instance. A key continues to identify a message even if the corresponding
message is modified, such as by replacing it with another message.</p>
<p>Messages may be added to a <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance using the set-like
method <a class="reference internal" href="#mailbox.Mailbox.add" title="mailbox.Mailbox.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add()</span></code></a> and removed using a <code class="docutils literal notranslate"><span class="pre">del</span></code> statement or the set-like
methods <a class="reference internal" href="#mailbox.Mailbox.remove" title="mailbox.Mailbox.remove"><code class="xref py py-meth docutils literal notranslate"><span class="pre">remove()</span></code></a> and <a class="reference internal" href="#mailbox.Mailbox.discard" title="mailbox.Mailbox.discard"><code class="xref py py-meth docutils literal notranslate"><span class="pre">discard()</span></code></a>.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> interface semantics differ from dictionary semantics in some
noteworthy ways. Each time a message is requested, a new representation
(typically a <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> instance) is generated based upon the current
state of the mailbox. Similarly, when a message is added to a
<code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance, the provided message representation’s contents are
copied. In neither case is a reference to the message representation kept by
the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance.</p>
<p>The default <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> iterates over message
representations, not keys as the default <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dictionary</span></code></a>
iterator does. Moreover, modification of a
mailbox during iteration is safe and well-defined. Messages added to the
mailbox after an iterator is created will not be seen by the
iterator. Messages removed from the mailbox before the iterator yields them
will be silently skipped, though using a key from an iterator may result in a
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception if the corresponding message is subsequently
removed.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Be very cautious when modifying mailboxes that might be simultaneously
changed by some other process.  The safest mailbox format to use for such
tasks is <a class="reference internal" href="#mailbox.Maildir" title="mailbox.Maildir"><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code></a>; try to avoid using single-file formats such as
<a class="reference internal" href="#mailbox.mbox" title="mailbox.mbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code></a> for
concurrent writing.  If you’re modifying a mailbox, you <em>must</em> lock it by
calling the <a class="reference internal" href="#mailbox.Mailbox.lock" title="mailbox.Mailbox.lock"><code class="xref py py-meth docutils literal notranslate"><span class="pre">lock()</span></code></a> and <a class="reference internal" href="#mailbox.Mailbox.unlock" title="mailbox.Mailbox.unlock"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unlock()</span></code></a> methods <em>before</em> reading any
messages in the file or making any changes by adding or deleting a
message.  Failing to lock the mailbox runs the risk of losing messages or
corrupting the entire mailbox.</p>
</div>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.add">
<span class="sig-name descname"><span class="pre">add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.add" title="Link to this definition">¶</a></dt>
<dd><p>Add <em>message</em> to the mailbox and return the key that has been assigned to
it.</p>
<p>Parameter <em>message</em> may be a <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> instance, an
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> instance, a string, a byte string, or a
file-like object (which should be open in binary mode). If <em>message</em> is
an instance of the
appropriate format-specific <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass (e.g., if it’s an
<a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> instance and this is an <a class="reference internal" href="#mailbox.mbox" title="mailbox.mbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code></a> instance), its
format-specific information is used. Otherwise, reasonable defaults for
format-specific information are used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Support for binary input was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.remove">
<span class="sig-name descname"><span class="pre">remove</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.remove" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Mailbox.__delitem__">
<span class="sig-name descname"><span class="pre">__delitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.__delitem__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Mailbox.discard">
<span class="sig-name descname"><span class="pre">discard</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.discard" title="Link to this definition">¶</a></dt>
<dd><p>Delete the message corresponding to <em>key</em> from the mailbox.</p>
<p>If no such message exists, a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception is raised if the
method was called as <a class="reference internal" href="#mailbox.Mailbox.remove" title="mailbox.Mailbox.remove"><code class="xref py py-meth docutils literal notranslate"><span class="pre">remove()</span></code></a> or <a class="reference internal" href="#mailbox.Mailbox.__delitem__" title="mailbox.Mailbox.__delitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__delitem__()</span></code></a> but no
exception is raised if the method was called as <a class="reference internal" href="#mailbox.Mailbox.discard" title="mailbox.Mailbox.discard"><code class="xref py py-meth docutils literal notranslate"><span class="pre">discard()</span></code></a>. The
behavior of <a class="reference internal" href="#mailbox.Mailbox.discard" title="mailbox.Mailbox.discard"><code class="xref py py-meth docutils literal notranslate"><span class="pre">discard()</span></code></a> may be preferred if the underlying mailbox
format supports concurrent modification by other processes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.__setitem__">
<span class="sig-name descname"><span class="pre">__setitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.__setitem__" title="Link to this definition">¶</a></dt>
<dd><p>Replace the message corresponding to <em>key</em> with <em>message</em>. Raise a
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception if no message already corresponds to <em>key</em>.</p>
<p>As with <a class="reference internal" href="#mailbox.Mailbox.add" title="mailbox.Mailbox.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add()</span></code></a>, parameter <em>message</em> may be a <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a>
instance, an <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> instance, a string, a byte
string, or a file-like object (which should be open in binary mode). If
<em>message</em> is an
instance of the appropriate format-specific <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass
(e.g., if it’s an <a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> instance and this is an
<a class="reference internal" href="#mailbox.mbox" title="mailbox.mbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code></a> instance), its format-specific information is
used. Otherwise, the format-specific information of the message that
currently corresponds to <em>key</em> is left unchanged.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.iterkeys">
<span class="sig-name descname"><span class="pre">iterkeys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.iterkeys" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> over all keys</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.keys">
<span class="sig-name descname"><span class="pre">keys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.keys" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#mailbox.Mailbox.iterkeys" title="mailbox.Mailbox.iterkeys"><code class="xref py py-meth docutils literal notranslate"><span class="pre">iterkeys()</span></code></a>, except that a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> is returned
rather than an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.itervalues">
<span class="sig-name descname"><span class="pre">itervalues</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.itervalues" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Mailbox.__iter__">
<span class="sig-name descname"><span class="pre">__iter__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.__iter__" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> over representations of all messages.
The messages are represented
as instances of the appropriate format-specific <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass
unless a custom message factory was specified when the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code>
instance was initialized.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The behavior of <a class="reference internal" href="#mailbox.Mailbox.__iter__" title="mailbox.Mailbox.__iter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__iter__()</span></code></a> is unlike that of dictionaries, which
iterate over keys.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.values">
<span class="sig-name descname"><span class="pre">values</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.values" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#mailbox.Mailbox.itervalues" title="mailbox.Mailbox.itervalues"><code class="xref py py-meth docutils literal notranslate"><span class="pre">itervalues()</span></code></a>, except that a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> is returned
rather than an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.iteritems">
<span class="sig-name descname"><span class="pre">iteritems</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.iteritems" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> over (<em>key</em>, <em>message</em>) pairs, where <em>key</em> is
a key and <em>message</em> is a message representation. The messages are
represented as instances of the appropriate format-specific
<a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass unless a custom message factory was specified
when the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance was initialized.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.items">
<span class="sig-name descname"><span class="pre">items</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.items" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#mailbox.Mailbox.iteritems" title="mailbox.Mailbox.iteritems"><code class="xref py py-meth docutils literal notranslate"><span class="pre">iteritems()</span></code></a>, except that a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> of pairs is
returned rather than an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> of pairs.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.get" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Mailbox.__getitem__">
<span class="sig-name descname"><span class="pre">__getitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.__getitem__" title="Link to this definition">¶</a></dt>
<dd><p>Return a representation of the message corresponding to <em>key</em>. If no such
message exists, <em>default</em> is returned if the method was called as
<a class="reference internal" href="#mailbox.Mailbox.get" title="mailbox.Mailbox.get"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get()</span></code></a> and a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception is raised if the method was
called as <code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code>. The message is represented as an instance
of the appropriate format-specific <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass unless a
custom message factory was specified when the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance
was initialized.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.get_message">
<span class="sig-name descname"><span class="pre">get_message</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.get_message" title="Link to this definition">¶</a></dt>
<dd><p>Return a representation of the message corresponding to <em>key</em> as an
instance of the appropriate format-specific <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass, or
raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception if no such message exists.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.get_bytes">
<span class="sig-name descname"><span class="pre">get_bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.get_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Return a byte representation of the message corresponding to <em>key</em>, or
raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception if no such message exists.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.get_string">
<span class="sig-name descname"><span class="pre">get_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.get_string" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representation of the message corresponding to <em>key</em>, or
raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception if no such message exists.  The
message is processed through <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> to
convert it to a 7bit clean representation.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.get_file">
<span class="sig-name descname"><span class="pre">get_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.get_file" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like</span></a> representation of the
message corresponding to <em>key</em>,
or raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception if no such message exists.  The
file-like object behaves as if open in binary mode.  This file should be
closed once it is no longer needed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The file object really is a <a class="reference internal" href="../glossary.html#term-binary-file"><span class="xref std std-term">binary file</span></a>; previously it was
incorrectly returned in text mode.  Also, the <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a>
now supports the <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> protocol: you can use a
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement to automatically close it.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike other representations of messages,
<a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like</span></a> representations are not
necessarily independent of the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance that
created them or of the underlying mailbox.  More specific documentation
is provided by each subclass.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.__contains__">
<span class="sig-name descname"><span class="pre">__contains__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.__contains__" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>key</em> corresponds to a message, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.__len__">
<span class="sig-name descname"><span class="pre">__len__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.__len__" title="Link to this definition">¶</a></dt>
<dd><p>Return a count of messages in the mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.clear">
<span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.clear" title="Link to this definition">¶</a></dt>
<dd><p>Delete all messages from the mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.pop">
<span class="sig-name descname"><span class="pre">pop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.pop" title="Link to this definition">¶</a></dt>
<dd><p>Return a representation of the message corresponding to <em>key</em> and delete
the message. If no such message exists, return <em>default</em>. The message is
represented as an instance of the appropriate format-specific
<a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass unless a custom message factory was specified
when the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance was initialized.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.popitem">
<span class="sig-name descname"><span class="pre">popitem</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.popitem" title="Link to this definition">¶</a></dt>
<dd><p>Return an arbitrary (<em>key</em>, <em>message</em>) pair, where <em>key</em> is a key and
<em>message</em> is a message representation, and delete the corresponding
message. If the mailbox is empty, raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception. The
message is represented as an instance of the appropriate format-specific
<a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> subclass unless a custom message factory was specified
when the <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance was initialized.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.update">
<span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.update" title="Link to this definition">¶</a></dt>
<dd><p>Parameter <em>arg</em> should be a <em>key</em>-to-<em>message</em> mapping or an iterable of
(<em>key</em>, <em>message</em>) pairs. Updates the mailbox so that, for each given
<em>key</em> and <em>message</em>, the message corresponding to <em>key</em> is set to
<em>message</em> as if by using <a class="reference internal" href="#mailbox.Mailbox.__setitem__" title="mailbox.Mailbox.__setitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__setitem__()</span></code></a>. As with <a class="reference internal" href="#mailbox.Mailbox.__setitem__" title="mailbox.Mailbox.__setitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__setitem__()</span></code></a>,
each <em>key</em> must already correspond to a message in the mailbox or else a
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception will be raised, so in general it is incorrect
for <em>arg</em> to be a <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike with dictionaries, keyword arguments are not supported.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.flush" title="Link to this definition">¶</a></dt>
<dd><p>Write any pending changes to the filesystem. For some <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a>
subclasses, changes are always written immediately and <code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code> does
nothing, but you should still make a habit of calling this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.lock">
<span class="sig-name descname"><span class="pre">lock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.lock" title="Link to this definition">¶</a></dt>
<dd><p>Acquire an exclusive advisory lock on the mailbox so that other processes
know not to modify it. An <a class="reference internal" href="#mailbox.ExternalClashError" title="mailbox.ExternalClashError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExternalClashError</span></code></a> is raised if the lock
is not available. The particular locking mechanisms used depend upon the
mailbox format.  You should <em>always</em> lock the mailbox before making any
modifications to its contents.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.unlock">
<span class="sig-name descname"><span class="pre">unlock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.unlock" title="Link to this definition">¶</a></dt>
<dd><p>Release the lock on the mailbox, if any.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Mailbox.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Mailbox.close" title="Link to this definition">¶</a></dt>
<dd><p>Flush the mailbox, unlock it if necessary, and close any open files. For
some <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> subclasses, this method does nothing.</p>
</dd></dl>

</dd></dl>

<section id="maildir-objects">
<span id="mailbox-maildir"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> objects<a class="headerlink" href="#maildir-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.Maildir">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">Maildir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dirname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">create</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> for mailboxes in Maildir format. Parameter
<em>factory</em> is a callable object that accepts a file-like message representation
(which behaves as if opened in binary mode) and returns a custom representation.
If <em>factory</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> is used as the default message
representation. If <em>create</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the mailbox is created if it does not
exist.</p>
<p>If <em>create</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> and the <em>dirname</em> path exists, it will be treated as
an existing maildir without attempting to verify its directory layout.</p>
<p>It is for historical reasons that <em>dirname</em> is named as such rather than <em>path</em>.</p>
<p>Maildir is a directory-based mailbox format invented for the qmail mail
transfer agent and now widely supported by other programs. Messages in a
Maildir mailbox are stored in separate files within a common directory
structure. This design allows Maildir mailboxes to be accessed and modified
by multiple unrelated programs without data corruption, so file locking is
unnecessary.</p>
<p>Maildir mailboxes contain three subdirectories, namely: <code class="file docutils literal notranslate"><span class="pre">tmp</span></code>,
<code class="file docutils literal notranslate"><span class="pre">new</span></code>, and <code class="file docutils literal notranslate"><span class="pre">cur</span></code>. Messages are created momentarily in the
<code class="file docutils literal notranslate"><span class="pre">tmp</span></code> subdirectory and then moved to the <code class="file docutils literal notranslate"><span class="pre">new</span></code> subdirectory to
finalize delivery. A mail user agent may subsequently move the message to the
<code class="file docutils literal notranslate"><span class="pre">cur</span></code> subdirectory and store information about the state of the message
in a special “info” section appended to its file name.</p>
<p>Folders of the style introduced by the Courier mail transfer agent are also
supported. Any subdirectory of the main mailbox is considered a folder if
<code class="docutils literal notranslate"><span class="pre">'.'</span></code> is the first character in its name. Folder names are represented by
<code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> without the leading <code class="docutils literal notranslate"><span class="pre">'.'</span></code>. Each folder is itself a Maildir
mailbox but should not contain other folders. Instead, a logical nesting is
indicated using <code class="docutils literal notranslate"><span class="pre">'.'</span></code> to delimit levels, e.g., “Archived.2005.07”.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="mailbox.Maildir.colon">
<span class="sig-name descname"><span class="pre">colon</span></span><a class="headerlink" href="#mailbox.Maildir.colon" title="Link to this definition">¶</a></dt>
<dd><p>The Maildir specification requires the use of a colon (<code class="docutils literal notranslate"><span class="pre">':'</span></code>) in certain
message file names. However, some operating systems do not permit this
character in file names, If you wish to use a Maildir-like format on such
an operating system, you should specify another character to use
instead. The exclamation point (<code class="docutils literal notranslate"><span class="pre">'!'</span></code>) is a popular choice. For
example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mailbox</span>
<span class="n">mailbox</span><span class="o">.</span><span class="n">Maildir</span><span class="o">.</span><span class="n">colon</span> <span class="o">=</span> <span class="s1">&#39;!&#39;</span>
</pre></div>
</div>
<p>The <code class="xref py py-attr docutils literal notranslate"><span class="pre">colon</span></code> attribute may also be set on a per-instance basis.</p>
</dd></dl>

<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> instances have all of the methods of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> in
addition to the following:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.list_folders">
<span class="sig-name descname"><span class="pre">list_folders</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.list_folders" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of the names of all folders.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.get_folder">
<span class="sig-name descname"><span class="pre">get_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">folder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.get_folder" title="Link to this definition">¶</a></dt>
<dd><p>Return a <code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> instance representing the folder whose name is
<em>folder</em>. A <a class="reference internal" href="#mailbox.NoSuchMailboxError" title="mailbox.NoSuchMailboxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NoSuchMailboxError</span></code></a> exception is raised if the folder
does not exist.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.add_folder">
<span class="sig-name descname"><span class="pre">add_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">folder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.add_folder" title="Link to this definition">¶</a></dt>
<dd><p>Create a folder whose name is <em>folder</em> and return a <code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code>
instance representing it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.remove_folder">
<span class="sig-name descname"><span class="pre">remove_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">folder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.remove_folder" title="Link to this definition">¶</a></dt>
<dd><p>Delete the folder whose name is <em>folder</em>. If the folder contains any
messages, a <a class="reference internal" href="#mailbox.NotEmptyError" title="mailbox.NotEmptyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotEmptyError</span></code></a> exception will be raised and the folder
will not be deleted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.clean">
<span class="sig-name descname"><span class="pre">clean</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.clean" title="Link to this definition">¶</a></dt>
<dd><p>Delete temporary files from the mailbox that have not been accessed in the
last 36 hours. The Maildir specification says that mail-reading programs
should do this occasionally.</p>
</dd></dl>

<p>Some <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> methods implemented by <code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> deserve special
remarks:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.add">
<span class="sig-name descname"><span class="pre">add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.add" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Maildir.__setitem__">
<span class="sig-name descname"><span class="pre">__setitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.__setitem__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Maildir.update">
<span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.update" title="Link to this definition">¶</a></dt>
<dd><div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>These methods generate unique file names based upon the current process
ID. When using multiple threads, undetected name clashes may occur and
cause corruption of the mailbox unless threads are coordinated to avoid
using these methods to manipulate the same mailbox simultaneously.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.flush" title="Link to this definition">¶</a></dt>
<dd><p>All changes to Maildir mailboxes are immediately applied, so this method
does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.lock">
<span class="sig-name descname"><span class="pre">lock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.lock" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Maildir.unlock">
<span class="sig-name descname"><span class="pre">unlock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.unlock" title="Link to this definition">¶</a></dt>
<dd><p>Maildir mailboxes do not support (or require) locking, so these methods do
nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.close" title="Link to this definition">¶</a></dt>
<dd><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> instances do not keep any open files and the underlying
mailboxes do not support locking, so this method does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Maildir.get_file">
<span class="sig-name descname"><span class="pre">get_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Maildir.get_file" title="Link to this definition">¶</a></dt>
<dd><p>Depending upon the host platform, it may not be possible to modify or
remove the underlying message while the returned file remains open.</p>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://www.courier-mta.org/maildir.html">maildir man page from Courier</a></dt><dd><p>A specification of the format. Describes a common extension for
supporting folders.</p>
</dd>
<dt><a class="reference external" href="https://cr.yp.to/proto/maildir.html">Using maildir format</a></dt><dd><p>Notes on Maildir by its inventor. Includes an updated name-creation scheme and
details on “info” semantics.</p>
</dd>
</dl>
</div>
</section>
<section id="mbox-objects">
<span id="mailbox-mbox"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> objects<a class="headerlink" href="#mbox-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.mbox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">mbox</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">create</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mbox" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> for mailboxes in mbox format. Parameter <em>factory</em>
is a callable object that accepts a file-like message representation (which
behaves as if opened in binary mode) and returns a custom representation. If
<em>factory</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> is used as the default message
representation. If <em>create</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the mailbox is created if it does not
exist.</p>
<p>The mbox format is the classic format for storing mail on Unix systems. All
messages in an mbox mailbox are stored in a single file with the beginning of
each message indicated by a line whose first five characters are “From “.</p>
<p>Several variations of the mbox format exist to address perceived shortcomings in
the original. In the interest of compatibility, <code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> implements the
original format, which is sometimes referred to as <em class="dfn">mboxo</em>. This means that
the <em class="mailheader">Content-Length</em> header, if present, is ignored and that any
occurrences of “From “ at the beginning of a line in a message body are
transformed to “&gt;From “ when storing the message, although occurrences of “&gt;From
“ are not transformed to “From “ when reading the message.</p>
<p>Some <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> methods implemented by <code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> deserve special
remarks:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mbox.get_file">
<span class="sig-name descname"><span class="pre">get_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mbox.get_file" title="Link to this definition">¶</a></dt>
<dd><p>Using the file after calling <a class="reference internal" href="#mailbox.Mailbox.flush" title="mailbox.Mailbox.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> or
<a class="reference internal" href="#mailbox.Mailbox.close" title="mailbox.Mailbox.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> on the <code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> instance may yield
unpredictable results or raise an exception.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mbox.lock">
<span class="sig-name descname"><span class="pre">lock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mbox.lock" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.mbox.unlock">
<span class="sig-name descname"><span class="pre">unlock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mbox.unlock" title="Link to this definition">¶</a></dt>
<dd><p>Three locking mechanisms are used—dot locking and, if available, the
<code class="xref c c-func docutils literal notranslate"><span class="pre">flock()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">lockf()</span></code> system calls.</p>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="http://www.tin.org/bin/man.cgi?section=5&amp;topic=mbox">mbox man page from tin</a></dt><dd><p>A specification of the format, with details on locking.</p>
</dd>
<dt><a class="reference external" href="https://www.jwz.org/doc/content-length.html">Configuring Netscape Mail on Unix: Why The Content-Length Format is Bad</a></dt><dd><p>An argument for using the original mbox format rather than a variation.</p>
</dd>
<dt><a class="reference external" href="https://www.loc.gov/preservation/digital/formats/fdd/fdd000383.shtml">“mbox” is a family of several mutually incompatible mailbox formats</a></dt><dd><p>A history of mbox variations.</p>
</dd>
</dl>
</div>
</section>
<section id="mh-objects">
<span id="mailbox-mh"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> objects<a class="headerlink" href="#mh-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.MH">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">MH</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">create</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> for mailboxes in MH format. Parameter <em>factory</em>
is a callable object that accepts a file-like message representation (which
behaves as if opened in binary mode) and returns a custom representation. If
<em>factory</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> is used as the default message
representation. If <em>create</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the mailbox is created if it does not
exist.</p>
<p>MH is a directory-based mailbox format invented for the MH Message Handling
System, a mail user agent. Each message in an MH mailbox resides in its own
file. An MH mailbox may contain other MH mailboxes (called <em class="dfn">folders</em>) in
addition to messages. Folders may be nested indefinitely. MH mailboxes also
support <em class="dfn">sequences</em>, which are named lists used to logically group
messages without moving them to sub-folders. Sequences are defined in a file
called <code class="file docutils literal notranslate"><span class="pre">.mh_sequences</span></code> in each folder.</p>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> class manipulates MH mailboxes, but it does not attempt to
emulate all of <strong class="program">mh</strong>’s behaviors. In particular, it does not modify
and is not affected by the <code class="file docutils literal notranslate"><span class="pre">context</span></code> or <code class="file docutils literal notranslate"><span class="pre">.mh_profile</span></code> files that
are used by <strong class="program">mh</strong> to store its state and configuration.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> instances have all of the methods of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> in addition
to the following:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.list_folders">
<span class="sig-name descname"><span class="pre">list_folders</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.list_folders" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of the names of all folders.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.get_folder">
<span class="sig-name descname"><span class="pre">get_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">folder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.get_folder" title="Link to this definition">¶</a></dt>
<dd><p>Return an <code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> instance representing the folder whose name is
<em>folder</em>. A <a class="reference internal" href="#mailbox.NoSuchMailboxError" title="mailbox.NoSuchMailboxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NoSuchMailboxError</span></code></a> exception is raised if the folder
does not exist.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.add_folder">
<span class="sig-name descname"><span class="pre">add_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">folder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.add_folder" title="Link to this definition">¶</a></dt>
<dd><p>Create a folder whose name is <em>folder</em> and return an <code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> instance
representing it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.remove_folder">
<span class="sig-name descname"><span class="pre">remove_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">folder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.remove_folder" title="Link to this definition">¶</a></dt>
<dd><p>Delete the folder whose name is <em>folder</em>. If the folder contains any
messages, a <a class="reference internal" href="#mailbox.NotEmptyError" title="mailbox.NotEmptyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotEmptyError</span></code></a> exception will be raised and the folder
will not be deleted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.get_sequences">
<span class="sig-name descname"><span class="pre">get_sequences</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.get_sequences" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary of sequence names mapped to key lists. If there are no
sequences, the empty dictionary is returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.set_sequences">
<span class="sig-name descname"><span class="pre">set_sequences</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequences</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.set_sequences" title="Link to this definition">¶</a></dt>
<dd><p>Re-define the sequences that exist in the mailbox based upon <em>sequences</em>,
a dictionary of names mapped to key lists, like returned by
<a class="reference internal" href="#mailbox.MH.get_sequences" title="mailbox.MH.get_sequences"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_sequences()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.pack">
<span class="sig-name descname"><span class="pre">pack</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.pack" title="Link to this definition">¶</a></dt>
<dd><p>Rename messages in the mailbox as necessary to eliminate gaps in
numbering.  Entries in the sequences list are updated correspondingly.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Already-issued keys are invalidated by this operation and should not be
subsequently used.</p>
</div>
</dd></dl>

<p>Some <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> methods implemented by <code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> deserve special
remarks:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.remove">
<span class="sig-name descname"><span class="pre">remove</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.remove" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.MH.__delitem__">
<span class="sig-name descname"><span class="pre">__delitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.__delitem__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.MH.discard">
<span class="sig-name descname"><span class="pre">discard</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.discard" title="Link to this definition">¶</a></dt>
<dd><p>These methods immediately delete the message. The MH convention of marking
a message for deletion by prepending a comma to its name is not used.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.lock">
<span class="sig-name descname"><span class="pre">lock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.lock" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.MH.unlock">
<span class="sig-name descname"><span class="pre">unlock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.unlock" title="Link to this definition">¶</a></dt>
<dd><p>Three locking mechanisms are used—dot locking and, if available, the
<code class="xref c c-func docutils literal notranslate"><span class="pre">flock()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">lockf()</span></code> system calls. For MH mailboxes, locking
the mailbox means locking the <code class="file docutils literal notranslate"><span class="pre">.mh_sequences</span></code> file and, only for the
duration of any operations that affect them, locking individual message
files.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.get_file">
<span class="sig-name descname"><span class="pre">get_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.get_file" title="Link to this definition">¶</a></dt>
<dd><p>Depending upon the host platform, it may not be possible to remove the
underlying message while the returned file remains open.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.flush" title="Link to this definition">¶</a></dt>
<dd><p>All changes to MH mailboxes are immediately applied, so this method does
nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MH.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MH.close" title="Link to this definition">¶</a></dt>
<dd><p><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> instances do not keep any open files, so this method is
equivalent to <a class="reference internal" href="#mailbox.MH.unlock" title="mailbox.MH.unlock"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unlock()</span></code></a>.</p>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://www.nongnu.org/nmh/">nmh - Message Handling System</a></dt><dd><p>Home page of <strong class="program">nmh</strong>, an updated version of the original <strong class="program">mh</strong>.</p>
</dd>
<dt><a class="reference external" href="https://rand-mh.sourceforge.io/book/">MH &amp; nmh: Email for Users &amp; Programmers</a></dt><dd><p>A GPL-licensed book on <strong class="program">mh</strong> and <strong class="program">nmh</strong>, with some information
on the mailbox format.</p>
</dd>
</dl>
</div>
</section>
<section id="babyl-objects">
<span id="mailbox-babyl"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">Babyl</span></code> objects<a class="headerlink" href="#babyl-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.Babyl">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">Babyl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">create</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Babyl" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> for mailboxes in Babyl format. Parameter
<em>factory</em> is a callable object that accepts a file-like message representation
(which behaves as if opened in binary mode) and returns a custom representation.
If <em>factory</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> is used as the default message
representation. If <em>create</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the mailbox is created if it does not
exist.</p>
<p>Babyl is a single-file mailbox format used by the Rmail mail user agent
included with Emacs. The beginning of a message is indicated by a line
containing the two characters Control-Underscore (<code class="docutils literal notranslate"><span class="pre">'\037'</span></code>) and Control-L
(<code class="docutils literal notranslate"><span class="pre">'\014'</span></code>). The end of a message is indicated by the start of the next
message or, in the case of the last message, a line containing a
Control-Underscore (<code class="docutils literal notranslate"><span class="pre">'\037'</span></code>) character.</p>
<p>Messages in a Babyl mailbox have two sets of headers, original headers and
so-called visible headers. Visible headers are typically a subset of the
original headers that have been reformatted or abridged to be more
attractive. Each message in a Babyl mailbox also has an accompanying list of
<em class="dfn">labels</em>, or short strings that record extra information about the
message, and a list of all user-defined labels found in the mailbox is kept
in the Babyl options section.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Babyl</span></code> instances have all of the methods of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> in
addition to the following:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Babyl.get_labels">
<span class="sig-name descname"><span class="pre">get_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Babyl.get_labels" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of the names of all user-defined labels used in the mailbox.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The actual messages are inspected to determine which labels exist in
the mailbox rather than consulting the list of labels in the Babyl
options section, but the Babyl section is updated whenever the mailbox
is modified.</p>
</div>
</dd></dl>

<p>Some <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> methods implemented by <code class="xref py py-class docutils literal notranslate"><span class="pre">Babyl</span></code> deserve special
remarks:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Babyl.get_file">
<span class="sig-name descname"><span class="pre">get_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Babyl.get_file" title="Link to this definition">¶</a></dt>
<dd><p>In Babyl mailboxes, the headers of a message are not stored contiguously
with the body of the message. To generate a file-like representation, the
headers and body are copied together into an <a class="reference internal" href="io.html#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BytesIO</span></code></a> instance,
which has an API identical to that of a
file. As a result, the file-like object is truly independent of the
underlying mailbox but does not save memory compared to a string
representation.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.Babyl.lock">
<span class="sig-name descname"><span class="pre">lock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Babyl.lock" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.Babyl.unlock">
<span class="sig-name descname"><span class="pre">unlock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Babyl.unlock" title="Link to this definition">¶</a></dt>
<dd><p>Three locking mechanisms are used—dot locking and, if available, the
<code class="xref c c-func docutils literal notranslate"><span class="pre">flock()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">lockf()</span></code> system calls.</p>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://quimby.gnus.org/notes/BABYL">Format of Version 5 Babyl Files</a></dt><dd><p>A specification of the Babyl format.</p>
</dd>
<dt><a class="reference external" href="https://www.gnu.org/software/emacs/manual/html_node/emacs/Rmail.html">Reading Mail with Rmail</a></dt><dd><p>The Rmail manual, with some information on Babyl semantics.</p>
</dd>
</dl>
</div>
</section>
<section id="mmdf-objects">
<span id="mailbox-mmdf"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDF</span></code> objects<a class="headerlink" href="#mmdf-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.MMDF">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">MMDF</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">create</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDF" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> for mailboxes in MMDF format. Parameter <em>factory</em>
is a callable object that accepts a file-like message representation (which
behaves as if opened in binary mode) and returns a custom representation. If
<em>factory</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a> is used as the default message
representation. If <em>create</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the mailbox is created if it does not
exist.</p>
<p>MMDF is a single-file mailbox format invented for the Multichannel Memorandum
Distribution Facility, a mail transfer agent. Each message is in the same
form as an mbox message but is bracketed before and after by lines containing
four Control-A (<code class="docutils literal notranslate"><span class="pre">'\001'</span></code>) characters. As with the mbox format, the
beginning of each message is indicated by a line whose first five characters
are “From “, but additional occurrences of “From “ are not transformed to
“&gt;From “ when storing messages because the extra message separator lines
prevent mistaking such occurrences for the starts of subsequent messages.</p>
<p>Some <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> methods implemented by <code class="xref py py-class docutils literal notranslate"><span class="pre">MMDF</span></code> deserve special
remarks:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDF.get_file">
<span class="sig-name descname"><span class="pre">get_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDF.get_file" title="Link to this definition">¶</a></dt>
<dd><p>Using the file after calling <a class="reference internal" href="#mailbox.Mailbox.flush" title="mailbox.Mailbox.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> or
<a class="reference internal" href="#mailbox.Mailbox.close" title="mailbox.Mailbox.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> on the <code class="xref py py-class docutils literal notranslate"><span class="pre">MMDF</span></code> instance may yield
unpredictable results or raise an exception.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDF.lock">
<span class="sig-name descname"><span class="pre">lock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDF.lock" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mailbox.MMDF.unlock">
<span class="sig-name descname"><span class="pre">unlock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDF.unlock" title="Link to this definition">¶</a></dt>
<dd><p>Three locking mechanisms are used—dot locking and, if available, the
<code class="xref c c-func docutils literal notranslate"><span class="pre">flock()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">lockf()</span></code> system calls.</p>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="http://www.tin.org/bin/man.cgi?section=5&amp;topic=mmdf">mmdf man page from tin</a></dt><dd><p>A specification of MMDF format from the documentation of tin, a newsreader.</p>
</dd>
<dt><a class="reference external" href="https://en.wikipedia.org/wiki/MMDF">MMDF</a></dt><dd><p>A Wikipedia article describing the Multichannel Memorandum Distribution
Facility.</p>
</dd>
</dl>
</div>
</section>
</section>
<section id="message-objects">
<span id="mailbox-message-objects"></span><h2><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> objects<a class="headerlink" href="#message-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.Message">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">Message</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.Message" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of the <a class="reference internal" href="email.message.html#module-email.message" title="email.message: The base class representing email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.message</span></code></a> module’s
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a>. Subclasses of <code class="xref py py-class docutils literal notranslate"><span class="pre">mailbox.Message</span></code> add
mailbox-format-specific state and behavior.</p>
<p>If <em>message</em> is omitted, the new instance is created in a default, empty state.
If <em>message</em> is an <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> instance, its contents are
copied; furthermore, any format-specific information is converted insofar as
possible if <em>message</em> is a <code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> instance. If <em>message</em> is a string,
a byte string,
or a file, it should contain an <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>-compliant message, which is read
and parsed.  Files should be open in binary mode, but text mode files
are accepted for backward compatibility.</p>
<p>The format-specific state and behaviors offered by subclasses vary, but in
general it is only the properties that are not specific to a particular
mailbox that are supported (although presumably the properties are specific
to a particular mailbox format). For example, file offsets for single-file
mailbox formats and file names for directory-based mailbox formats are not
retained, because they are only applicable to the original mailbox. But state
such as whether a message has been read by the user or marked as important is
retained, because it applies to the message itself.</p>
<p>There is no requirement that <code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> instances be used to represent
messages retrieved using <a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> instances. In some situations, the
time and memory required to generate <code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> representations might
not be acceptable. For such situations, <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instances also
offer string and file-like representations, and a custom message factory may
be specified when a <code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> instance is initialized.</p>
</dd></dl>

<section id="maildirmessage-objects">
<span id="mailbox-maildirmessage"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> objects<a class="headerlink" href="#maildirmessage-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.MaildirMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">MaildirMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage" title="Link to this definition">¶</a></dt>
<dd><p>A message with Maildir-specific behaviors. Parameter <em>message</em> has the same
meaning as with the <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> constructor.</p>
<p>Typically, a mail user agent application moves all of the messages in the
<code class="file docutils literal notranslate"><span class="pre">new</span></code> subdirectory to the <code class="file docutils literal notranslate"><span class="pre">cur</span></code> subdirectory after the first time
the user opens and closes the mailbox, recording that the messages are old
whether or not they’ve actually been read. Each message in <code class="file docutils literal notranslate"><span class="pre">cur</span></code> has an
“info” section added to its file name to store information about its state.
(Some mail readers may also add an “info” section to messages in
<code class="file docutils literal notranslate"><span class="pre">new</span></code>.)  The “info” section may take one of two forms: it may contain
“2,” followed by a list of standardized flags (e.g., “2,FR”) or it may
contain “1,” followed by so-called experimental information. Standard flags
for Maildir messages are as follows:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Flag</p></th>
<th class="head"><p>Meaning</p></th>
<th class="head"><p>Explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>D</p></td>
<td><p>Draft</p></td>
<td><p>Under composition</p></td>
</tr>
<tr class="row-odd"><td><p>F</p></td>
<td><p>Flagged</p></td>
<td><p>Marked as important</p></td>
</tr>
<tr class="row-even"><td><p>P</p></td>
<td><p>Passed</p></td>
<td><p>Forwarded, resent, or bounced</p></td>
</tr>
<tr class="row-odd"><td><p>R</p></td>
<td><p>Replied</p></td>
<td><p>Replied to</p></td>
</tr>
<tr class="row-even"><td><p>S</p></td>
<td><p>Seen</p></td>
<td><p>Read</p></td>
</tr>
<tr class="row-odd"><td><p>T</p></td>
<td><p>Trashed</p></td>
<td><p>Marked for subsequent deletion</p></td>
</tr>
</tbody>
</table>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> instances offer the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.get_subdir">
<span class="sig-name descname"><span class="pre">get_subdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.get_subdir" title="Link to this definition">¶</a></dt>
<dd><p>Return either “new” (if the message should be stored in the <code class="file docutils literal notranslate"><span class="pre">new</span></code>
subdirectory) or “cur” (if the message should be stored in the <code class="file docutils literal notranslate"><span class="pre">cur</span></code>
subdirectory).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A message is typically moved from <code class="file docutils literal notranslate"><span class="pre">new</span></code> to <code class="file docutils literal notranslate"><span class="pre">cur</span></code> after its
mailbox has been accessed, whether or not the message is has been
read. A message <code class="docutils literal notranslate"><span class="pre">msg</span></code> has been read if <code class="docutils literal notranslate"><span class="pre">&quot;S&quot;</span> <span class="pre">in</span> <span class="pre">msg.get_flags()</span></code> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.set_subdir">
<span class="sig-name descname"><span class="pre">set_subdir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subdir</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.set_subdir" title="Link to this definition">¶</a></dt>
<dd><p>Set the subdirectory the message should be stored in. Parameter <em>subdir</em>
must be either “new” or “cur”.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.get_flags">
<span class="sig-name descname"><span class="pre">get_flags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.get_flags" title="Link to this definition">¶</a></dt>
<dd><p>Return a string specifying the flags that are currently set. If the
message complies with the standard Maildir format, the result is the
concatenation in alphabetical order of zero or one occurrence of each of
<code class="docutils literal notranslate"><span class="pre">'D'</span></code>, <code class="docutils literal notranslate"><span class="pre">'F'</span></code>, <code class="docutils literal notranslate"><span class="pre">'P'</span></code>, <code class="docutils literal notranslate"><span class="pre">'R'</span></code>, <code class="docutils literal notranslate"><span class="pre">'S'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'T'</span></code>. The empty string
is returned if no flags are set or if “info” contains experimental
semantics.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.set_flags">
<span class="sig-name descname"><span class="pre">set_flags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.set_flags" title="Link to this definition">¶</a></dt>
<dd><p>Set the flags specified by <em>flags</em> and unset all others.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.add_flag">
<span class="sig-name descname"><span class="pre">add_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.add_flag" title="Link to this definition">¶</a></dt>
<dd><p>Set the flag(s) specified by <em>flag</em> without changing other flags. To add
more than one flag at a time, <em>flag</em> may be a string of more than one
character. The current “info” is overwritten whether or not it contains
experimental information rather than flags.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.remove_flag">
<span class="sig-name descname"><span class="pre">remove_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.remove_flag" title="Link to this definition">¶</a></dt>
<dd><p>Unset the flag(s) specified by <em>flag</em> without changing other flags. To
remove more than one flag at a time, <em>flag</em> maybe a string of more than
one character.  If “info” contains experimental information rather than
flags, the current “info” is not modified.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.get_date">
<span class="sig-name descname"><span class="pre">get_date</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.get_date" title="Link to this definition">¶</a></dt>
<dd><p>Return the delivery date of the message as a floating-point number
representing seconds since the epoch.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.set_date">
<span class="sig-name descname"><span class="pre">set_date</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">date</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.set_date" title="Link to this definition">¶</a></dt>
<dd><p>Set the delivery date of the message to <em>date</em>, a floating-point number
representing seconds since the epoch.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.get_info">
<span class="sig-name descname"><span class="pre">get_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.get_info" title="Link to this definition">¶</a></dt>
<dd><p>Return a string containing the “info” for a message. This is useful for
accessing and modifying “info” that is experimental (i.e., not a list of
flags).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MaildirMessage.set_info">
<span class="sig-name descname"><span class="pre">set_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">info</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MaildirMessage.set_info" title="Link to this definition">¶</a></dt>
<dd><p>Set “info” to <em>info</em>, which should be a string.</p>
</dd></dl>

</dd></dl>

<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> or <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a> instance, the <em class="mailheader">Status</em>
and <em class="mailheader">X-Status</em> headers are omitted and the following conversions
take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> or <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a>
state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“cur” subdirectory</p></td>
<td><p>O flag</p></td>
</tr>
<tr class="row-odd"><td><p>F flag</p></td>
<td><p>F flag</p></td>
</tr>
<tr class="row-even"><td><p>R flag</p></td>
<td><p>A flag</p></td>
</tr>
<tr class="row-odd"><td><p>S flag</p></td>
<td><p>R flag</p></td>
</tr>
<tr class="row-even"><td><p>T flag</p></td>
<td><p>D flag</p></td>
</tr>
</tbody>
</table>
<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“cur” subdirectory</p></td>
<td><p>“unseen” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>“cur” subdirectory and S flag</p></td>
<td><p>no “unseen” sequence</p></td>
</tr>
<tr class="row-even"><td><p>F flag</p></td>
<td><p>“flagged” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>R flag</p></td>
<td><p>“replied” sequence</p></td>
</tr>
</tbody>
</table>
<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“cur” subdirectory</p></td>
<td><p>“unseen” label</p></td>
</tr>
<tr class="row-odd"><td><p>“cur” subdirectory and S flag</p></td>
<td><p>no “unseen” label</p></td>
</tr>
<tr class="row-even"><td><p>P flag</p></td>
<td><p>“forwarded” or “resent” label</p></td>
</tr>
<tr class="row-odd"><td><p>R flag</p></td>
<td><p>“answered” label</p></td>
</tr>
<tr class="row-even"><td><p>T flag</p></td>
<td><p>“deleted” label</p></td>
</tr>
</tbody>
</table>
</section>
<section id="mboxmessage-objects">
<span id="mailbox-mboxmessage"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> objects<a class="headerlink" href="#mboxmessage-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.mboxMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">mboxMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage" title="Link to this definition">¶</a></dt>
<dd><p>A message with mbox-specific behaviors. Parameter <em>message</em> has the same meaning
as with the <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> constructor.</p>
<p>Messages in an mbox mailbox are stored together in a single file. The
sender’s envelope address and the time of delivery are typically stored in a
line beginning with “From “ that is used to indicate the start of a message,
though there is considerable variation in the exact format of this data among
mbox implementations. Flags that indicate the state of the message, such as
whether it has been read or marked as important, are typically stored in
<em class="mailheader">Status</em> and <em class="mailheader">X-Status</em> headers.</p>
<p>Conventional flags for mbox messages are as follows:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Flag</p></th>
<th class="head"><p>Meaning</p></th>
<th class="head"><p>Explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R</p></td>
<td><p>Read</p></td>
<td><p>Read</p></td>
</tr>
<tr class="row-odd"><td><p>O</p></td>
<td><p>Old</p></td>
<td><p>Previously detected by MUA</p></td>
</tr>
<tr class="row-even"><td><p>D</p></td>
<td><p>Deleted</p></td>
<td><p>Marked for subsequent deletion</p></td>
</tr>
<tr class="row-odd"><td><p>F</p></td>
<td><p>Flagged</p></td>
<td><p>Marked as important</p></td>
</tr>
<tr class="row-even"><td><p>A</p></td>
<td><p>Answered</p></td>
<td><p>Replied to</p></td>
</tr>
</tbody>
</table>
<p>The “R” and “O” flags are stored in the <em class="mailheader">Status</em> header, and the
“D”, “F”, and “A” flags are stored in the <em class="mailheader">X-Status</em> header. The
flags and headers typically appear in the order mentioned.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> instances offer the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mboxMessage.get_from">
<span class="sig-name descname"><span class="pre">get_from</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage.get_from" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representing the “From “ line that marks the start of the
message in an mbox mailbox. The leading “From “ and the trailing newline
are excluded.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mboxMessage.set_from">
<span class="sig-name descname"><span class="pre">set_from</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">from_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage.set_from" title="Link to this definition">¶</a></dt>
<dd><p>Set the “From “ line to <em>from_</em>, which should be specified without a
leading “From “ or trailing newline. For convenience, <em>time_</em> may be
specified and will be formatted appropriately and appended to <em>from_</em>. If
<em>time_</em> is specified, it should be a <a class="reference internal" href="time.html#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">time.struct_time</span></code></a> instance, a
tuple suitable for passing to <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">True</span></code> (to use
<a class="reference internal" href="time.html#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.gmtime()</span></code></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mboxMessage.get_flags">
<span class="sig-name descname"><span class="pre">get_flags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage.get_flags" title="Link to this definition">¶</a></dt>
<dd><p>Return a string specifying the flags that are currently set. If the
message complies with the conventional format, the result is the
concatenation in the following order of zero or one occurrence of each of
<code class="docutils literal notranslate"><span class="pre">'R'</span></code>, <code class="docutils literal notranslate"><span class="pre">'O'</span></code>, <code class="docutils literal notranslate"><span class="pre">'D'</span></code>, <code class="docutils literal notranslate"><span class="pre">'F'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'A'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mboxMessage.set_flags">
<span class="sig-name descname"><span class="pre">set_flags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage.set_flags" title="Link to this definition">¶</a></dt>
<dd><p>Set the flags specified by <em>flags</em> and unset all others. Parameter <em>flags</em>
should be the concatenation in any order of zero or more occurrences of
each of <code class="docutils literal notranslate"><span class="pre">'R'</span></code>, <code class="docutils literal notranslate"><span class="pre">'O'</span></code>, <code class="docutils literal notranslate"><span class="pre">'D'</span></code>, <code class="docutils literal notranslate"><span class="pre">'F'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'A'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mboxMessage.add_flag">
<span class="sig-name descname"><span class="pre">add_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage.add_flag" title="Link to this definition">¶</a></dt>
<dd><p>Set the flag(s) specified by <em>flag</em> without changing other flags. To add
more than one flag at a time, <em>flag</em> may be a string of more than one
character.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.mboxMessage.remove_flag">
<span class="sig-name descname"><span class="pre">remove_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.mboxMessage.remove_flag" title="Link to this definition">¶</a></dt>
<dd><p>Unset the flag(s) specified by <em>flag</em> without changing other flags. To
remove more than one flag at a time, <em>flag</em> maybe a string of more than
one character.</p>
</dd></dl>

</dd></dl>

<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> instance, a “From “ line is generated based upon the
<a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> instance’s delivery date, and the following conversions
take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag</p></td>
<td><p>S flag</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>“cur” subdirectory</p></td>
</tr>
<tr class="row-even"><td><p>D flag</p></td>
<td><p>T flag</p></td>
</tr>
<tr class="row-odd"><td><p>F flag</p></td>
<td><p>F flag</p></td>
</tr>
<tr class="row-even"><td><p>A flag</p></td>
<td><p>R flag</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag and O flag</p></td>
<td><p>no “unseen” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>“unseen” sequence</p></td>
</tr>
<tr class="row-even"><td><p>F flag</p></td>
<td><p>“flagged” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>A flag</p></td>
<td><p>“replied” sequence</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag and O flag</p></td>
<td><p>no “unseen” label</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>“unseen” label</p></td>
</tr>
<tr class="row-even"><td><p>D flag</p></td>
<td><p>“deleted” label</p></td>
</tr>
<tr class="row-odd"><td><p>A flag</p></td>
<td><p>“answered” label</p></td>
</tr>
</tbody>
</table>
<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a>
instance, the “From “ line is copied and all flags directly correspond:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag</p></td>
<td><p>R flag</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>O flag</p></td>
</tr>
<tr class="row-even"><td><p>D flag</p></td>
<td><p>D flag</p></td>
</tr>
<tr class="row-odd"><td><p>F flag</p></td>
<td><p>F flag</p></td>
</tr>
<tr class="row-even"><td><p>A flag</p></td>
<td><p>A flag</p></td>
</tr>
</tbody>
</table>
</section>
<section id="mhmessage-objects">
<span id="mailbox-mhmessage"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> objects<a class="headerlink" href="#mhmessage-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.MHMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">MHMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MHMessage" title="Link to this definition">¶</a></dt>
<dd><p>A message with MH-specific behaviors. Parameter <em>message</em> has the same meaning
as with the <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> constructor.</p>
<p>MH messages do not support marks or flags in the traditional sense, but they
do support sequences, which are logical groupings of arbitrary messages. Some
mail reading programs (although not the standard <strong class="program">mh</strong> and
<strong class="program">nmh</strong>) use sequences in much the same way flags are used with other
formats, as follows:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Sequence</p></th>
<th class="head"><p>Explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>unseen</p></td>
<td><p>Not read, but previously detected by MUA</p></td>
</tr>
<tr class="row-odd"><td><p>replied</p></td>
<td><p>Replied to</p></td>
</tr>
<tr class="row-even"><td><p>flagged</p></td>
<td><p>Marked as important</p></td>
</tr>
</tbody>
</table>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> instances offer the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MHMessage.get_sequences">
<span class="sig-name descname"><span class="pre">get_sequences</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MHMessage.get_sequences" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of the names of sequences that include this message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MHMessage.set_sequences">
<span class="sig-name descname"><span class="pre">set_sequences</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequences</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MHMessage.set_sequences" title="Link to this definition">¶</a></dt>
<dd><p>Set the list of sequences that include this message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MHMessage.add_sequence">
<span class="sig-name descname"><span class="pre">add_sequence</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequence</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MHMessage.add_sequence" title="Link to this definition">¶</a></dt>
<dd><p>Add <em>sequence</em> to the list of sequences that include this message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MHMessage.remove_sequence">
<span class="sig-name descname"><span class="pre">remove_sequence</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequence</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MHMessage.remove_sequence" title="Link to this definition">¶</a></dt>
<dd><p>Remove <em>sequence</em> from the list of sequences that include this message.</p>
</dd></dl>

</dd></dl>

<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“unseen” sequence</p></td>
<td><p>no S flag</p></td>
</tr>
<tr class="row-odd"><td><p>“replied” sequence</p></td>
<td><p>R flag</p></td>
</tr>
<tr class="row-even"><td><p>“flagged” sequence</p></td>
<td><p>F flag</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> or <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a> instance, the <em class="mailheader">Status</em>
and <em class="mailheader">X-Status</em> headers are omitted and the following conversions
take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> or <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a>
state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“unseen” sequence</p></td>
<td><p>no R flag</p></td>
</tr>
<tr class="row-odd"><td><p>“replied” sequence</p></td>
<td><p>A flag</p></td>
</tr>
<tr class="row-even"><td><p>“flagged” sequence</p></td>
<td><p>F flag</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“unseen” sequence</p></td>
<td><p>“unseen” label</p></td>
</tr>
<tr class="row-odd"><td><p>“replied” sequence</p></td>
<td><p>“answered” label</p></td>
</tr>
</tbody>
</table>
</section>
<section id="babylmessage-objects">
<span id="mailbox-babylmessage"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> objects<a class="headerlink" href="#babylmessage-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.BabylMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">BabylMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage" title="Link to this definition">¶</a></dt>
<dd><p>A message with Babyl-specific behaviors. Parameter <em>message</em> has the same
meaning as with the <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> constructor.</p>
<p>Certain message labels, called <em class="dfn">attributes</em>, are defined by convention
to have special meanings. The attributes are as follows:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Label</p></th>
<th class="head"><p>Explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>unseen</p></td>
<td><p>Not read, but previously detected by MUA</p></td>
</tr>
<tr class="row-odd"><td><p>deleted</p></td>
<td><p>Marked for subsequent deletion</p></td>
</tr>
<tr class="row-even"><td><p>filed</p></td>
<td><p>Copied to another file or mailbox</p></td>
</tr>
<tr class="row-odd"><td><p>answered</p></td>
<td><p>Replied to</p></td>
</tr>
<tr class="row-even"><td><p>forwarded</p></td>
<td><p>Forwarded</p></td>
</tr>
<tr class="row-odd"><td><p>edited</p></td>
<td><p>Modified by the user</p></td>
</tr>
<tr class="row-even"><td><p>resent</p></td>
<td><p>Resent</p></td>
</tr>
</tbody>
</table>
<p>By default, Rmail displays only visible headers. The <code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code>
class, though, uses the original headers because they are more
complete. Visible headers may be accessed explicitly if desired.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> instances offer the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.get_labels">
<span class="sig-name descname"><span class="pre">get_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.get_labels" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of labels on the message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.set_labels">
<span class="sig-name descname"><span class="pre">set_labels</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">labels</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.set_labels" title="Link to this definition">¶</a></dt>
<dd><p>Set the list of labels on the message to <em>labels</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.add_label">
<span class="sig-name descname"><span class="pre">add_label</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">label</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.add_label" title="Link to this definition">¶</a></dt>
<dd><p>Add <em>label</em> to the list of labels on the message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.remove_label">
<span class="sig-name descname"><span class="pre">remove_label</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">label</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.remove_label" title="Link to this definition">¶</a></dt>
<dd><p>Remove <em>label</em> from the list of labels on the message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.get_visible">
<span class="sig-name descname"><span class="pre">get_visible</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.get_visible" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> instance whose headers are the message’s
visible headers and whose body is empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.set_visible">
<span class="sig-name descname"><span class="pre">set_visible</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">visible</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.set_visible" title="Link to this definition">¶</a></dt>
<dd><p>Set the message’s visible headers to be the same as the headers in
<em>message</em>.  Parameter <em>visible</em> should be a <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> instance, an
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> instance, a string, or a file-like object
(which should be open in text mode).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.BabylMessage.update_visible">
<span class="sig-name descname"><span class="pre">update_visible</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.BabylMessage.update_visible" title="Link to this definition">¶</a></dt>
<dd><p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> instance’s original headers are modified, the
visible headers are not automatically modified to correspond. This method
updates the visible headers as follows: each visible header with a
corresponding original header is set to the value of the original header,
each visible header without a corresponding original header is removed,
and any of <em class="mailheader">Date</em>, <em class="mailheader">From</em>, <em class="mailheader">Reply-To</em>,
<em class="mailheader">To</em>, <em class="mailheader">CC</em>, and <em class="mailheader">Subject</em> that are
present in the original headers but not the visible headers are added to
the visible headers.</p>
</dd></dl>

</dd></dl>

<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“unseen” label</p></td>
<td><p>no S flag</p></td>
</tr>
<tr class="row-odd"><td><p>“deleted” label</p></td>
<td><p>T flag</p></td>
</tr>
<tr class="row-even"><td><p>“answered” label</p></td>
<td><p>R flag</p></td>
</tr>
<tr class="row-odd"><td><p>“forwarded” label</p></td>
<td><p>P flag</p></td>
</tr>
</tbody>
</table>
<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> or <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a> instance, the <em class="mailheader">Status</em>
and <em class="mailheader">X-Status</em> headers are omitted and the following conversions
take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> or <a class="reference internal" href="#mailbox.MMDFMessage" title="mailbox.MMDFMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code></a>
state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“unseen” label</p></td>
<td><p>no R flag</p></td>
</tr>
<tr class="row-odd"><td><p>“deleted” label</p></td>
<td><p>D flag</p></td>
</tr>
<tr class="row-even"><td><p>“answered” label</p></td>
<td><p>A flag</p></td>
</tr>
</tbody>
</table>
<p>When a <code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“unseen” label</p></td>
<td><p>“unseen” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>“answered” label</p></td>
<td><p>“replied” sequence</p></td>
</tr>
</tbody>
</table>
</section>
<section id="mmdfmessage-objects">
<span id="mailbox-mmdfmessage"></span><h3><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> objects<a class="headerlink" href="#mmdfmessage-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="mailbox.MMDFMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">MMDFMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage" title="Link to this definition">¶</a></dt>
<dd><p>A message with MMDF-specific behaviors. Parameter <em>message</em> has the same meaning
as with the <a class="reference internal" href="#mailbox.Message" title="mailbox.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> constructor.</p>
<p>As with message in an mbox mailbox, MMDF messages are stored with the
sender’s address and the delivery date in an initial line beginning with
“From “.  Likewise, flags that indicate the state of the message are
typically stored in <em class="mailheader">Status</em> and <em class="mailheader">X-Status</em> headers.</p>
<p>Conventional flags for MMDF messages are identical to those of mbox message
and are as follows:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Flag</p></th>
<th class="head"><p>Meaning</p></th>
<th class="head"><p>Explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R</p></td>
<td><p>Read</p></td>
<td><p>Read</p></td>
</tr>
<tr class="row-odd"><td><p>O</p></td>
<td><p>Old</p></td>
<td><p>Previously detected by MUA</p></td>
</tr>
<tr class="row-even"><td><p>D</p></td>
<td><p>Deleted</p></td>
<td><p>Marked for subsequent deletion</p></td>
</tr>
<tr class="row-odd"><td><p>F</p></td>
<td><p>Flagged</p></td>
<td><p>Marked as important</p></td>
</tr>
<tr class="row-even"><td><p>A</p></td>
<td><p>Answered</p></td>
<td><p>Replied to</p></td>
</tr>
</tbody>
</table>
<p>The “R” and “O” flags are stored in the <em class="mailheader">Status</em> header, and the
“D”, “F”, and “A” flags are stored in the <em class="mailheader">X-Status</em> header. The
flags and headers typically appear in the order mentioned.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> instances offer the following methods, which are
identical to those offered by <a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDFMessage.get_from">
<span class="sig-name descname"><span class="pre">get_from</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage.get_from" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representing the “From “ line that marks the start of the
message in an mbox mailbox. The leading “From “ and the trailing newline
are excluded.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDFMessage.set_from">
<span class="sig-name descname"><span class="pre">set_from</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">from_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage.set_from" title="Link to this definition">¶</a></dt>
<dd><p>Set the “From “ line to <em>from_</em>, which should be specified without a
leading “From “ or trailing newline. For convenience, <em>time_</em> may be
specified and will be formatted appropriately and appended to <em>from_</em>. If
<em>time_</em> is specified, it should be a <a class="reference internal" href="time.html#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">time.struct_time</span></code></a> instance, a
tuple suitable for passing to <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">True</span></code> (to use
<a class="reference internal" href="time.html#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.gmtime()</span></code></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDFMessage.get_flags">
<span class="sig-name descname"><span class="pre">get_flags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage.get_flags" title="Link to this definition">¶</a></dt>
<dd><p>Return a string specifying the flags that are currently set. If the
message complies with the conventional format, the result is the
concatenation in the following order of zero or one occurrence of each of
<code class="docutils literal notranslate"><span class="pre">'R'</span></code>, <code class="docutils literal notranslate"><span class="pre">'O'</span></code>, <code class="docutils literal notranslate"><span class="pre">'D'</span></code>, <code class="docutils literal notranslate"><span class="pre">'F'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'A'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDFMessage.set_flags">
<span class="sig-name descname"><span class="pre">set_flags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage.set_flags" title="Link to this definition">¶</a></dt>
<dd><p>Set the flags specified by <em>flags</em> and unset all others. Parameter <em>flags</em>
should be the concatenation in any order of zero or more occurrences of
each of <code class="docutils literal notranslate"><span class="pre">'R'</span></code>, <code class="docutils literal notranslate"><span class="pre">'O'</span></code>, <code class="docutils literal notranslate"><span class="pre">'D'</span></code>, <code class="docutils literal notranslate"><span class="pre">'F'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'A'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDFMessage.add_flag">
<span class="sig-name descname"><span class="pre">add_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage.add_flag" title="Link to this definition">¶</a></dt>
<dd><p>Set the flag(s) specified by <em>flag</em> without changing other flags. To add
more than one flag at a time, <em>flag</em> may be a string of more than one
character.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mailbox.MMDFMessage.remove_flag">
<span class="sig-name descname"><span class="pre">remove_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailbox.MMDFMessage.remove_flag" title="Link to this definition">¶</a></dt>
<dd><p>Unset the flag(s) specified by <em>flag</em> without changing other flags. To
remove more than one flag at a time, <em>flag</em> maybe a string of more than
one character.</p>
</dd></dl>

</dd></dl>

<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> instance, a “From “ line is generated based upon the
<a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> instance’s delivery date, and the following conversions
take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MaildirMessage" title="mailbox.MaildirMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag</p></td>
<td><p>S flag</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>“cur” subdirectory</p></td>
</tr>
<tr class="row-even"><td><p>D flag</p></td>
<td><p>T flag</p></td>
</tr>
<tr class="row-odd"><td><p>F flag</p></td>
<td><p>F flag</p></td>
</tr>
<tr class="row-even"><td><p>A flag</p></td>
<td><p>R flag</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.MHMessage" title="mailbox.MHMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag and O flag</p></td>
<td><p>no “unseen” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>“unseen” sequence</p></td>
</tr>
<tr class="row-even"><td><p>F flag</p></td>
<td><p>“flagged” sequence</p></td>
</tr>
<tr class="row-odd"><td><p>A flag</p></td>
<td><p>“replied” sequence</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> instance is created based upon a
<a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> instance, the following conversions take place:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.BabylMessage" title="mailbox.BabylMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag and O flag</p></td>
<td><p>no “unseen” label</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>“unseen” label</p></td>
</tr>
<tr class="row-even"><td><p>D flag</p></td>
<td><p>“deleted” label</p></td>
</tr>
<tr class="row-odd"><td><p>A flag</p></td>
<td><p>“answered” label</p></td>
</tr>
</tbody>
</table>
<p>When an <code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> instance is created based upon an
<a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> instance, the “From “ line is copied and all flags directly
correspond:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Resulting state</p></th>
<th class="head"><p><a class="reference internal" href="#mailbox.mboxMessage" title="mailbox.mboxMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code></a> state</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>R flag</p></td>
<td><p>R flag</p></td>
</tr>
<tr class="row-odd"><td><p>O flag</p></td>
<td><p>O flag</p></td>
</tr>
<tr class="row-even"><td><p>D flag</p></td>
<td><p>D flag</p></td>
</tr>
<tr class="row-odd"><td><p>F flag</p></td>
<td><p>F flag</p></td>
</tr>
<tr class="row-even"><td><p>A flag</p></td>
<td><p>A flag</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="exceptions">
<h2>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h2>
<p>The following exception classes are defined in the <code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> module:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="mailbox.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#mailbox.Error" title="Link to this definition">¶</a></dt>
<dd><p>The based class for all other module-specific exceptions.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="mailbox.NoSuchMailboxError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">NoSuchMailboxError</span></span><a class="headerlink" href="#mailbox.NoSuchMailboxError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a mailbox is expected but is not found, such as when instantiating a
<a class="reference internal" href="#mailbox.Mailbox" title="mailbox.Mailbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code></a> subclass with a path that does not exist (and with the <em>create</em>
parameter set to <code class="docutils literal notranslate"><span class="pre">False</span></code>), or when opening a folder that does not exist.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="mailbox.NotEmptyError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">NotEmptyError</span></span><a class="headerlink" href="#mailbox.NotEmptyError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a mailbox is not empty but is expected to be, such as when deleting
a folder that contains messages.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="mailbox.ExternalClashError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">ExternalClashError</span></span><a class="headerlink" href="#mailbox.ExternalClashError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when some mailbox-related condition beyond the control of the program
causes it to be unable to proceed, such as when failing to acquire a lock that
another program already holds a lock, or when a uniquely generated file name
already exists.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="mailbox.FormatError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mailbox.</span></span><span class="sig-name descname"><span class="pre">FormatError</span></span><a class="headerlink" href="#mailbox.FormatError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the data in a file cannot be parsed, such as when an <a class="reference internal" href="#mailbox.MH" title="mailbox.MH"><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code></a>
instance attempts to read a corrupted <code class="file docutils literal notranslate"><span class="pre">.mh_sequences</span></code> file.</p>
</dd></dl>

</section>
<section id="examples">
<span id="mailbox-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>A simple example of printing the subjects of all messages in a mailbox that seem
interesting:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mailbox</span>
<span class="k">for</span> <span class="n">message</span> <span class="ow">in</span> <span class="n">mailbox</span><span class="o">.</span><span class="n">mbox</span><span class="p">(</span><span class="s1">&#39;~/mbox&#39;</span><span class="p">):</span>
    <span class="n">subject</span> <span class="o">=</span> <span class="n">message</span><span class="p">[</span><span class="s1">&#39;subject&#39;</span><span class="p">]</span>       <span class="c1"># Could possibly be None.</span>
    <span class="k">if</span> <span class="n">subject</span> <span class="ow">and</span> <span class="s1">&#39;python&#39;</span> <span class="ow">in</span> <span class="n">subject</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">subject</span><span class="p">)</span>
</pre></div>
</div>
<p>To copy all mail from a Babyl mailbox to an MH mailbox, converting all of the
format-specific information that can be converted:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mailbox</span>
<span class="n">destination</span> <span class="o">=</span> <span class="n">mailbox</span><span class="o">.</span><span class="n">MH</span><span class="p">(</span><span class="s1">&#39;~/Mail&#39;</span><span class="p">)</span>
<span class="n">destination</span><span class="o">.</span><span class="n">lock</span><span class="p">()</span>
<span class="k">for</span> <span class="n">message</span> <span class="ow">in</span> <span class="n">mailbox</span><span class="o">.</span><span class="n">Babyl</span><span class="p">(</span><span class="s1">&#39;~/RMAIL&#39;</span><span class="p">):</span>
    <span class="n">destination</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">mailbox</span><span class="o">.</span><span class="n">MHMessage</span><span class="p">(</span><span class="n">message</span><span class="p">))</span>
<span class="n">destination</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
<span class="n">destination</span><span class="o">.</span><span class="n">unlock</span><span class="p">()</span>
</pre></div>
</div>
<p>This example sorts mail from several mailing lists into different mailboxes,
being careful to avoid mail corruption due to concurrent modification by other
programs, mail loss due to interruption of the program, or premature termination
due to malformed messages in the mailbox:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mailbox</span>
<span class="kn">import</span> <span class="nn">email.errors</span>

<span class="n">list_names</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;python-list&#39;</span><span class="p">,</span> <span class="s1">&#39;python-dev&#39;</span><span class="p">,</span> <span class="s1">&#39;python-bugs&#39;</span><span class="p">)</span>

<span class="n">boxes</span> <span class="o">=</span> <span class="p">{</span><span class="n">name</span><span class="p">:</span> <span class="n">mailbox</span><span class="o">.</span><span class="n">mbox</span><span class="p">(</span><span class="s1">&#39;~/email/</span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">name</span><span class="p">)</span> <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">list_names</span><span class="p">}</span>
<span class="n">inbox</span> <span class="o">=</span> <span class="n">mailbox</span><span class="o">.</span><span class="n">Maildir</span><span class="p">(</span><span class="s1">&#39;~/Maildir&#39;</span><span class="p">,</span> <span class="n">factory</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span>

<span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="n">inbox</span><span class="o">.</span><span class="n">iterkeys</span><span class="p">():</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">message</span> <span class="o">=</span> <span class="n">inbox</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
    <span class="k">except</span> <span class="n">email</span><span class="o">.</span><span class="n">errors</span><span class="o">.</span><span class="n">MessageParseError</span><span class="p">:</span>
        <span class="k">continue</span>                <span class="c1"># The message is malformed. Just leave it.</span>

    <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">list_names</span><span class="p">:</span>
        <span class="n">list_id</span> <span class="o">=</span> <span class="n">message</span><span class="p">[</span><span class="s1">&#39;list-id&#39;</span><span class="p">]</span>
        <span class="k">if</span> <span class="n">list_id</span> <span class="ow">and</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">list_id</span><span class="p">:</span>
            <span class="c1"># Get mailbox to use</span>
            <span class="n">box</span> <span class="o">=</span> <span class="n">boxes</span><span class="p">[</span><span class="n">name</span><span class="p">]</span>

            <span class="c1"># Write copy to disk before removing original.</span>
            <span class="c1"># If there&#39;s a crash, you might duplicate a message, but</span>
            <span class="c1"># that&#39;s better than losing a message completely.</span>
            <span class="n">box</span><span class="o">.</span><span class="n">lock</span><span class="p">()</span>
            <span class="n">box</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">message</span><span class="p">)</span>
            <span class="n">box</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
            <span class="n">box</span><span class="o">.</span><span class="n">unlock</span><span class="p">()</span>

            <span class="c1"># Remove original message</span>
            <span class="n">inbox</span><span class="o">.</span><span class="n">lock</span><span class="p">()</span>
            <span class="n">inbox</span><span class="o">.</span><span class="n">discard</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
            <span class="n">inbox</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
            <span class="n">inbox</span><span class="o">.</span><span class="n">unlock</span><span class="p">()</span>
            <span class="k">break</span>               <span class="c1"># Found destination, so stop looking.</span>

<span class="k">for</span> <span class="n">box</span> <span class="ow">in</span> <span class="n">boxes</span><span class="o">.</span><span class="n">itervalues</span><span class="p">():</span>
    <span class="n">box</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a><ul>
<li><a class="reference internal" href="#mailbox-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> objects</a><ul>
<li><a class="reference internal" href="#maildir-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> objects</a></li>
<li><a class="reference internal" href="#mbox-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> objects</a></li>
<li><a class="reference internal" href="#mh-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> objects</a></li>
<li><a class="reference internal" href="#babyl-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Babyl</span></code> objects</a></li>
<li><a class="reference internal" href="#mmdf-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDF</span></code> objects</a></li>
</ul>
</li>
<li><a class="reference internal" href="#message-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> objects</a><ul>
<li><a class="reference internal" href="#maildirmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#mboxmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#mhmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#babylmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> objects</a></li>
<li><a class="reference internal" href="#mmdfmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> objects</a></li>
</ul>
</li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="json.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mimetypes.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mailbox.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mimetypes.html" title="mimetypes — Map filenames to MIME types"
             >next</a> |</li>
        <li class="right" >
          <a href="json.html" title="json — JSON encoder and decoder"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>