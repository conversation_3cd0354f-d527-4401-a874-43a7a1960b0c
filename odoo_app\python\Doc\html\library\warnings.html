<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="warnings — Warning control" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/warnings.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/warnings.py Warning messages are typically issued in situations where it is useful to alert the user of some condition in a program, where that condition (normally) doesn’t warrant..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/warnings.py Warning messages are typically issued in situations where it is useful to alert the user of some condition in a program, where that condition (normally) doesn’t warrant..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>warnings — Warning control &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="dataclasses — Data Classes" href="dataclasses.html" />
    <link rel="prev" title="__main__ — Top-level code environment" href="__main__.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/warnings.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a><ul>
<li><a class="reference internal" href="#warning-categories">Warning Categories</a></li>
<li><a class="reference internal" href="#the-warnings-filter">The Warnings Filter</a><ul>
<li><a class="reference internal" href="#describing-warning-filters">Describing Warning Filters</a></li>
<li><a class="reference internal" href="#default-warning-filter">Default Warning Filter</a></li>
<li><a class="reference internal" href="#overriding-the-default-filter">Overriding the default filter</a></li>
</ul>
</li>
<li><a class="reference internal" href="#temporarily-suppressing-warnings">Temporarily Suppressing Warnings</a></li>
<li><a class="reference internal" href="#testing-warnings">Testing Warnings</a></li>
<li><a class="reference internal" href="#updating-code-for-new-versions-of-dependencies">Updating Code For New Versions of Dependencies</a></li>
<li><a class="reference internal" href="#available-functions">Available Functions</a></li>
<li><a class="reference internal" href="#available-context-managers">Available Context Managers</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="__main__.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="dataclasses.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dataclasses</span></code> — Data Classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/warnings.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="dataclasses.html" title="dataclasses — Data Classes"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="__main__.html" title="__main__ — Top-level code environment"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-warnings">
<span id="warnings-warning-control"></span><h1><a class="reference internal" href="#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> — Warning control<a class="headerlink" href="#module-warnings" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/warnings.py">Lib/warnings.py</a></p>
<hr class="docutils" id="index-0" />
<p>Warning messages are typically issued in situations where it is useful to alert
the user of some condition in a program, where that condition (normally) doesn’t
warrant raising an exception and terminating the program.  For example, one
might want to issue a warning when a program uses an obsolete module.</p>
<p>Python programmers issue warnings by calling the <a class="reference internal" href="#warnings.warn" title="warnings.warn"><code class="xref py py-func docutils literal notranslate"><span class="pre">warn()</span></code></a> function defined
in this module.  (C programmers use <a class="reference internal" href="../c-api/exceptions.html#c.PyErr_WarnEx" title="PyErr_WarnEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_WarnEx()</span></code></a>; see
<a class="reference internal" href="../c-api/exceptions.html#exceptionhandling"><span class="std std-ref">Exception Handling</span></a> for details).</p>
<p>Warning messages are normally written to <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>, but their disposition
can be changed flexibly, from ignoring all warnings to turning them into
exceptions.  The disposition of warnings can vary based on the <a class="reference internal" href="#warning-categories"><span class="std std-ref">warning category</span></a>, the text of the warning message, and the source location where it
is issued.  Repetitions of a particular warning for the same source location are
typically suppressed.</p>
<p>There are two stages in warning control: first, each time a warning is issued, a
determination is made whether a message should be issued or not; next, if a
message is to be issued, it is formatted and printed using a user-settable hook.</p>
<p>The determination whether to issue a warning message is controlled by the
<a class="reference internal" href="#warning-filter"><span class="std std-ref">warning filter</span></a>, which is a sequence of matching rules and actions. Rules can be
added to the filter by calling <a class="reference internal" href="#warnings.filterwarnings" title="warnings.filterwarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">filterwarnings()</span></code></a> and reset to its default
state by calling <a class="reference internal" href="#warnings.resetwarnings" title="warnings.resetwarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">resetwarnings()</span></code></a>.</p>
<p>The printing of warning messages is done by calling <a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a>, which
may be overridden; the default implementation of this function formats the
message by calling <a class="reference internal" href="#warnings.formatwarning" title="warnings.formatwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">formatwarning()</span></code></a>, which is also available for use by
custom implementations.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="logging.html#logging.captureWarnings" title="logging.captureWarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">logging.captureWarnings()</span></code></a> allows you to handle all warnings with
the standard logging infrastructure.</p>
</div>
<section id="warning-categories">
<span id="id1"></span><h2>Warning Categories<a class="headerlink" href="#warning-categories" title="Link to this heading">¶</a></h2>
<p>There are a number of built-in exceptions that represent warning categories.
This categorization is useful to be able to filter out groups of warnings.</p>
<p>While these are technically
<a class="reference internal" href="exceptions.html#warning-categories-as-exceptions"><span class="std std-ref">built-in exceptions</span></a>, they are
documented here, because conceptually they belong to the warnings mechanism.</p>
<p>User code can define additional warning categories by subclassing one of the
standard warning categories.  A warning category must always be a subclass of
the <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a> class.</p>
<p>The following warnings category classes are currently defined:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Class</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a></p></td>
<td><p>This is the base class of all warning
category classes.  It is a subclass of
<a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="exceptions.html#UserWarning" title="UserWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UserWarning</span></code></a></p></td>
<td><p>The default category for <a class="reference internal" href="#warnings.warn" title="warnings.warn"><code class="xref py py-func docutils literal notranslate"><span class="pre">warn()</span></code></a>.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a></p></td>
<td><p>Base category for warnings about deprecated
features when those warnings are intended for
other Python developers (ignored by default,
unless triggered by code in <code class="docutils literal notranslate"><span class="pre">__main__</span></code>).</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="exceptions.html#SyntaxWarning" title="SyntaxWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxWarning</span></code></a></p></td>
<td><p>Base category for warnings about dubious
syntactic features.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="exceptions.html#RuntimeWarning" title="RuntimeWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeWarning</span></code></a></p></td>
<td><p>Base category for warnings about dubious
runtime features.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="exceptions.html#FutureWarning" title="FutureWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FutureWarning</span></code></a></p></td>
<td><p>Base category for warnings about deprecated
features when those warnings are intended for
end users of applications that are written in
Python.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="exceptions.html#PendingDeprecationWarning" title="PendingDeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PendingDeprecationWarning</span></code></a></p></td>
<td><p>Base category for warnings about features
that will be deprecated in the future
(ignored by default).</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="exceptions.html#ImportWarning" title="ImportWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportWarning</span></code></a></p></td>
<td><p>Base category for warnings triggered during
the process of importing a module (ignored by
default).</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="exceptions.html#UnicodeWarning" title="UnicodeWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeWarning</span></code></a></p></td>
<td><p>Base category for warnings related to
Unicode.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="exceptions.html#BytesWarning" title="BytesWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BytesWarning</span></code></a></p></td>
<td><p>Base category for warnings related to
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a></p></td>
<td><p>Base category for warnings related to
resource usage (ignored by default).</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Previously <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> and <a class="reference internal" href="exceptions.html#FutureWarning" title="FutureWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FutureWarning</span></code></a> were
distinguished based on whether a feature was being removed entirely or
changing its behaviour. They are now distinguished based on their
intended audience and the way they’re handled by the default warnings
filters.</p>
</div>
</section>
<section id="the-warnings-filter">
<span id="warning-filter"></span><h2>The Warnings Filter<a class="headerlink" href="#the-warnings-filter" title="Link to this heading">¶</a></h2>
<p>The warnings filter controls whether warnings are ignored, displayed, or turned
into errors (raising an exception).</p>
<p>Conceptually, the warnings filter maintains an ordered list of filter
specifications; any specific warning is matched against each filter
specification in the list in turn until a match is found; the filter determines
the disposition of the match.  Each entry is a tuple of the form (<em>action</em>,
<em>message</em>, <em>category</em>, <em>module</em>, <em>lineno</em>), where:</p>
<ul>
<li><p><em>action</em> is one of the following strings:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Value</p></th>
<th class="head"><p>Disposition</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&quot;default&quot;</span></code></p></td>
<td><p>print the first occurrence of matching
warnings for each location (module +
line number) where the warning is issued</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&quot;error&quot;</span></code></p></td>
<td><p>turn matching warnings into exceptions</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&quot;ignore&quot;</span></code></p></td>
<td><p>never print matching warnings</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&quot;always&quot;</span></code></p></td>
<td><p>always print matching warnings</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&quot;module&quot;</span></code></p></td>
<td><p>print the first occurrence of matching
warnings for each module where the warning
is issued (regardless of line number)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&quot;once&quot;</span></code></p></td>
<td><p>print only the first occurrence of matching
warnings, regardless of location</p></td>
</tr>
</tbody>
</table>
</li>
<li><p><em>message</em> is a string containing a regular expression that the start of
the warning message must match, case-insensitively.  In <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> and
<span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNINGS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNINGS</span></code></a>, <em>message</em> is a literal string that the start of the
warning message must contain (case-insensitively), ignoring any whitespace at
the start or end of <em>message</em>.</p></li>
<li><p><em>category</em> is a class (a subclass of <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a>) of which the warning
category must be a subclass in order to match.</p></li>
<li><p><em>module</em> is a string containing a regular expression that the start of the
fully qualified module name must match, case-sensitively.  In <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> and
<span class="target" id="index-2"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNINGS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNINGS</span></code></a>, <em>module</em> is a literal string that the
fully qualified module name must be equal to (case-sensitively), ignoring any
whitespace at the start or end of <em>module</em>.</p></li>
<li><p><em>lineno</em> is an integer that the line number where the warning occurred must
match, or <code class="docutils literal notranslate"><span class="pre">0</span></code> to match all line numbers.</p></li>
</ul>
<p>Since the <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a> class is derived from the built-in <a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>
class, to turn a warning into an error we simply raise <code class="docutils literal notranslate"><span class="pre">category(message)</span></code>.</p>
<p>If a warning is reported and doesn’t match any registered filter then the
“default” action is applied (hence its name).</p>
<section id="describing-warning-filters">
<span id="id2"></span><h3>Describing Warning Filters<a class="headerlink" href="#describing-warning-filters" title="Link to this heading">¶</a></h3>
<p>The warnings filter is initialized by <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> options passed to the Python
interpreter command line and the <span class="target" id="index-3"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNINGS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNINGS</span></code></a> environment variable.
The interpreter saves the arguments for all supplied entries without
interpretation in <a class="reference internal" href="sys.html#sys.warnoptions" title="sys.warnoptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.warnoptions</span></code></a>; the <a class="reference internal" href="#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module parses these
when it is first imported (invalid options are ignored, after printing a
message to <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>).</p>
<p>Individual warnings filters are specified as a sequence of fields separated by
colons:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">action</span><span class="p">:</span><span class="n">message</span><span class="p">:</span><span class="n">category</span><span class="p">:</span><span class="n">module</span><span class="p">:</span><span class="n">line</span>
</pre></div>
</div>
<p>The meaning of each of these fields is as described in <a class="reference internal" href="#warning-filter"><span class="std std-ref">The Warnings Filter</span></a>.
When listing multiple filters on a single line (as for
<span class="target" id="index-4"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNINGS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNINGS</span></code></a>), the individual filters are separated by commas and
the filters listed later take precedence over those listed before them (as
they’re applied left-to-right, and the most recently applied filters take
precedence over earlier ones).</p>
<p>Commonly used warning filters apply to either all warnings, warnings in a
particular category, or warnings raised by particular modules or packages.
Some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">default</span>                      <span class="c1"># Show all warnings (even those ignored by default)</span>
<span class="n">ignore</span>                       <span class="c1"># Ignore all warnings</span>
<span class="n">error</span>                        <span class="c1"># Convert all warnings to errors</span>
<span class="n">error</span><span class="p">::</span><span class="ne">ResourceWarning</span>       <span class="c1"># Treat ResourceWarning messages as errors</span>
<span class="n">default</span><span class="p">::</span><span class="ne">DeprecationWarning</span>  <span class="c1"># Show DeprecationWarning messages</span>
<span class="n">ignore</span><span class="p">,</span><span class="n">default</span><span class="p">:::</span><span class="n">mymodule</span>    <span class="c1"># Only report warnings triggered by &quot;mymodule&quot;</span>
<span class="n">error</span><span class="p">:::</span><span class="n">mymodule</span>             <span class="c1"># Convert warnings to errors in &quot;mymodule&quot;</span>
</pre></div>
</div>
</section>
<section id="default-warning-filter">
<span id="id3"></span><h3>Default Warning Filter<a class="headerlink" href="#default-warning-filter" title="Link to this heading">¶</a></h3>
<p>By default, Python installs several warning filters, which can be overridden by
the <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> command-line option, the <span class="target" id="index-5"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNINGS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNINGS</span></code></a> environment
variable and calls to <a class="reference internal" href="#warnings.filterwarnings" title="warnings.filterwarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">filterwarnings()</span></code></a>.</p>
<p>In regular release builds, the default warning filter has the following entries
(in order of precedence):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">default</span><span class="p">::</span><span class="ne">DeprecationWarning</span><span class="p">:</span><span class="n">__main__</span>
<span class="n">ignore</span><span class="p">::</span><span class="ne">DeprecationWarning</span>
<span class="n">ignore</span><span class="p">::</span><span class="ne">PendingDeprecationWarning</span>
<span class="n">ignore</span><span class="p">::</span><span class="ne">ImportWarning</span>
<span class="n">ignore</span><span class="p">::</span><span class="ne">ResourceWarning</span>
</pre></div>
</div>
<p>In a <a class="reference internal" href="../using/configure.html#debug-build"><span class="std std-ref">debug build</span></a>, the list of default warning filters is empty.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> is now ignored by default in addition to
<a class="reference internal" href="exceptions.html#PendingDeprecationWarning" title="PendingDeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PendingDeprecationWarning</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> is once again shown by default when triggered
directly by code in <code class="docutils literal notranslate"><span class="pre">__main__</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="exceptions.html#BytesWarning" title="BytesWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BytesWarning</span></code></a> no longer appears in the default filter list and is
instead configured via <a class="reference internal" href="sys.html#sys.warnoptions" title="sys.warnoptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.warnoptions</span></code></a> when <a class="reference internal" href="../using/cmdline.html#cmdoption-b"><code class="xref std std-option docutils literal notranslate"><span class="pre">-b</span></code></a> is specified
twice.</p>
</div>
</section>
<section id="overriding-the-default-filter">
<span id="warning-disable"></span><h3>Overriding the default filter<a class="headerlink" href="#overriding-the-default-filter" title="Link to this heading">¶</a></h3>
<p>Developers of applications written in Python may wish to hide <em>all</em> Python level
warnings from their users by default, and only display them when running tests
or otherwise working on the application. The <a class="reference internal" href="sys.html#sys.warnoptions" title="sys.warnoptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.warnoptions</span></code></a> attribute
used to pass filter configurations to the interpreter can be used as a marker to
indicate whether or not warnings should be disabled:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span>

<span class="k">if</span> <span class="ow">not</span> <span class="n">sys</span><span class="o">.</span><span class="n">warnoptions</span><span class="p">:</span>
    <span class="kn">import</span> <span class="nn">warnings</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">simplefilter</span><span class="p">(</span><span class="s2">&quot;ignore&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Developers of test runners for Python code are advised to instead ensure that
<em>all</em> warnings are displayed by default for the code under test, using code
like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span>

<span class="k">if</span> <span class="ow">not</span> <span class="n">sys</span><span class="o">.</span><span class="n">warnoptions</span><span class="p">:</span>
    <span class="kn">import</span> <span class="nn">os</span><span class="o">,</span> <span class="nn">warnings</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">simplefilter</span><span class="p">(</span><span class="s2">&quot;default&quot;</span><span class="p">)</span> <span class="c1"># Change the filter in this process</span>
    <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="s2">&quot;PYTHONWARNINGS&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;default&quot;</span> <span class="c1"># Also affect subprocesses</span>
</pre></div>
</div>
<p>Finally, developers of interactive shells that run user code in a namespace
other than <code class="docutils literal notranslate"><span class="pre">__main__</span></code> are advised to ensure that <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a>
messages are made visible by default, using code like the following (where
<code class="docutils literal notranslate"><span class="pre">user_ns</span></code> is the module used to execute code entered interactively):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">warnings</span>
<span class="n">warnings</span><span class="o">.</span><span class="n">filterwarnings</span><span class="p">(</span><span class="s2">&quot;default&quot;</span><span class="p">,</span> <span class="n">category</span><span class="o">=</span><span class="ne">DeprecationWarning</span><span class="p">,</span>
                                   <span class="n">module</span><span class="o">=</span><span class="n">user_ns</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;__name__&quot;</span><span class="p">))</span>
</pre></div>
</div>
</section>
</section>
<section id="temporarily-suppressing-warnings">
<span id="warning-suppress"></span><h2>Temporarily Suppressing Warnings<a class="headerlink" href="#temporarily-suppressing-warnings" title="Link to this heading">¶</a></h2>
<p>If you are using code that you know will raise a warning, such as a deprecated
function, but do not want to see the warning (even when warnings have been
explicitly configured via the command line), then it is possible to suppress
the warning using the <a class="reference internal" href="#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-class docutils literal notranslate"><span class="pre">catch_warnings</span></code></a> context manager:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">warnings</span>

<span class="k">def</span> <span class="nf">fxn</span><span class="p">():</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;deprecated&quot;</span><span class="p">,</span> <span class="ne">DeprecationWarning</span><span class="p">)</span>

<span class="k">with</span> <span class="n">warnings</span><span class="o">.</span><span class="n">catch_warnings</span><span class="p">():</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">simplefilter</span><span class="p">(</span><span class="s2">&quot;ignore&quot;</span><span class="p">)</span>
    <span class="n">fxn</span><span class="p">()</span>
</pre></div>
</div>
<p>While within the context manager all warnings will simply be ignored. This
allows you to use known-deprecated code without having to see the warning while
not suppressing the warning for other code that might not be aware of its use
of deprecated code.  Note: this can only be guaranteed in a single-threaded
application. If two or more threads use the <a class="reference internal" href="#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-class docutils literal notranslate"><span class="pre">catch_warnings</span></code></a> context
manager at the same time, the behavior is undefined.</p>
</section>
<section id="testing-warnings">
<span id="warning-testing"></span><h2>Testing Warnings<a class="headerlink" href="#testing-warnings" title="Link to this heading">¶</a></h2>
<p>To test warnings raised by code, use the <a class="reference internal" href="#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-class docutils literal notranslate"><span class="pre">catch_warnings</span></code></a> context
manager. With it you can temporarily mutate the warnings filter to facilitate
your testing. For instance, do the following to capture all raised warnings to
check:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">warnings</span>

<span class="k">def</span> <span class="nf">fxn</span><span class="p">():</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;deprecated&quot;</span><span class="p">,</span> <span class="ne">DeprecationWarning</span><span class="p">)</span>

<span class="k">with</span> <span class="n">warnings</span><span class="o">.</span><span class="n">catch_warnings</span><span class="p">(</span><span class="n">record</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="k">as</span> <span class="n">w</span><span class="p">:</span>
    <span class="c1"># Cause all warnings to always be triggered.</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">simplefilter</span><span class="p">(</span><span class="s2">&quot;always&quot;</span><span class="p">)</span>
    <span class="c1"># Trigger a warning.</span>
    <span class="n">fxn</span><span class="p">()</span>
    <span class="c1"># Verify some things</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">w</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span>
    <span class="k">assert</span> <span class="nb">issubclass</span><span class="p">(</span><span class="n">w</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">category</span><span class="p">,</span> <span class="ne">DeprecationWarning</span><span class="p">)</span>
    <span class="k">assert</span> <span class="s2">&quot;deprecated&quot;</span> <span class="ow">in</span> <span class="nb">str</span><span class="p">(</span><span class="n">w</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">message</span><span class="p">)</span>
</pre></div>
</div>
<p>One can also cause all warnings to be exceptions by using <code class="docutils literal notranslate"><span class="pre">error</span></code> instead of
<code class="docutils literal notranslate"><span class="pre">always</span></code>. One thing to be aware of is that if a warning has already been
raised because of a <code class="docutils literal notranslate"><span class="pre">once</span></code>/<code class="docutils literal notranslate"><span class="pre">default</span></code> rule, then no matter what filters are
set the warning will not be seen again unless the warnings registry related to
the warning has been cleared.</p>
<p>Once the context manager exits, the warnings filter is restored to its state
when the context was entered. This prevents tests from changing the warnings
filter in unexpected ways between tests and leading to indeterminate test
results. The <a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a> function in the module is also restored to
its original value.  Note: this can only be guaranteed in a single-threaded
application. If two or more threads use the <a class="reference internal" href="#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-class docutils literal notranslate"><span class="pre">catch_warnings</span></code></a> context
manager at the same time, the behavior is undefined.</p>
<p>When testing multiple operations that raise the same kind of warning, it
is important to test them in a manner that confirms each operation is raising
a new warning (e.g. set warnings to be raised as exceptions and check the
operations raise exceptions, check that the length of the warning list
continues to increase after each operation, or else delete the previous
entries from the warnings list before each new operation).</p>
</section>
<section id="updating-code-for-new-versions-of-dependencies">
<span id="warning-ignored"></span><h2>Updating Code For New Versions of Dependencies<a class="headerlink" href="#updating-code-for-new-versions-of-dependencies" title="Link to this heading">¶</a></h2>
<p>Warning categories that are primarily of interest to Python developers (rather
than end users of applications written in Python) are ignored by default.</p>
<p>Notably, this “ignored by default” list includes <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a>
(for every module except <code class="docutils literal notranslate"><span class="pre">__main__</span></code>), which means developers should make sure
to test their code with typically ignored warnings made visible in order to
receive timely notifications of future breaking API changes (whether in the
standard library or third party packages).</p>
<p>In the ideal case, the code will have a suitable test suite, and the test runner
will take care of implicitly enabling all warnings when running tests
(the test runner provided by the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> module does this).</p>
<p>In less ideal cases, applications can be checked for use of deprecated
interfaces by passing <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-Wd</span></code></a> to the Python interpreter (this is
shorthand for <code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span> <span class="pre">default</span></code>) or setting <code class="docutils literal notranslate"><span class="pre">PYTHONWARNINGS=default</span></code> in
the environment. This enables default handling for all warnings, including those
that are ignored by default. To change what action is taken for encountered
warnings you can change what argument is passed to <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> (e.g.
<code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span> <span class="pre">error</span></code>). See the <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> flag for more details on what is
possible.</p>
</section>
<section id="available-functions">
<span id="warning-functions"></span><h2>Available Functions<a class="headerlink" href="#available-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="warnings.warn">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">warn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stacklevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip_file_prefixes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.warn" title="Link to this definition">¶</a></dt>
<dd><p>Issue a warning, or maybe ignore it or raise an exception.  The <em>category</em>
argument, if given, must be a <a class="reference internal" href="#warning-categories"><span class="std std-ref">warning category class</span></a>; it
defaults to <a class="reference internal" href="exceptions.html#UserWarning" title="UserWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UserWarning</span></code></a>.  Alternatively, <em>message</em> can be a <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a> instance,
in which case <em>category</em> will be ignored and <code class="docutils literal notranslate"><span class="pre">message.__class__</span></code> will be used.
In this case, the message text will be <code class="docutils literal notranslate"><span class="pre">str(message)</span></code>. This function raises an
exception if the particular warning issued is changed into an error by the
<a class="reference internal" href="#warning-filter"><span class="std std-ref">warnings filter</span></a>.  The <em>stacklevel</em> argument can be used by wrapper
functions written in Python, like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">deprecated_api</span><span class="p">(</span><span class="n">message</span><span class="p">):</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="n">message</span><span class="p">,</span> <span class="ne">DeprecationWarning</span><span class="p">,</span> <span class="n">stacklevel</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>This makes the warning refer to <code class="docutils literal notranslate"><span class="pre">deprecated_api</span></code>’s caller, rather than to
the source of <code class="docutils literal notranslate"><span class="pre">deprecated_api</span></code> itself (since the latter would defeat the
purpose of the warning message).</p>
<p>The <em>skip_file_prefixes</em> keyword argument can be used to indicate which
stack frames are ignored when counting stack levels. This can be useful when
you want the warning to always appear at call sites outside of a package
when a constant <em>stacklevel</em> does not fit all call paths or is otherwise
challenging to maintain. If supplied, it must be a tuple of strings. When
prefixes are supplied, stacklevel is implicitly overridden to be <code class="docutils literal notranslate"><span class="pre">max(2,</span>
<span class="pre">stacklevel)</span></code>. To cause a warning to be attributed to the caller from
outside of the current package you might write:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># example/lower.py</span>
<span class="n">_warn_skips</span> <span class="o">=</span> <span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">),)</span>

<span class="k">def</span> <span class="nf">one_way</span><span class="p">(</span><span class="n">r_luxury_yacht</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">t_wobbler_mangrove</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">r_luxury_yacht</span><span class="p">:</span>
        <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Please migrate to t_wobbler_mangrove=.&quot;</span><span class="p">,</span>
                      <span class="n">skip_file_prefixes</span><span class="o">=</span><span class="n">_warn_skips</span><span class="p">)</span>

<span class="c1"># example/higher.py</span>
<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">lower</span>

<span class="k">def</span> <span class="nf">another_way</span><span class="p">(</span><span class="o">**</span><span class="n">kw</span><span class="p">):</span>
    <span class="n">lower</span><span class="o">.</span><span class="n">one_way</span><span class="p">(</span><span class="o">**</span><span class="n">kw</span><span class="p">)</span>
</pre></div>
</div>
<p>This makes the warning refer to both the <code class="docutils literal notranslate"><span class="pre">example.lower.one_way()</span></code> and
<code class="docutils literal notranslate"><span class="pre">package.higher.another_way()</span></code> call sites only from calling code living
outside of <code class="docutils literal notranslate"><span class="pre">example</span></code> package.</p>
<p><em>source</em>, if supplied, is the destroyed object which emitted a
<a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added <em>source</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added <em>skip_file_prefixes</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="warnings.warn_explicit">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">warn_explicit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">registry</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module_globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.warn_explicit" title="Link to this definition">¶</a></dt>
<dd><p>This is a low-level interface to the functionality of <a class="reference internal" href="#warnings.warn" title="warnings.warn"><code class="xref py py-func docutils literal notranslate"><span class="pre">warn()</span></code></a>, passing in
explicitly the message, category, filename and line number, and optionally the
module name and the registry (which should be the <code class="docutils literal notranslate"><span class="pre">__warningregistry__</span></code>
dictionary of the module).  The module name defaults to the filename with
<code class="docutils literal notranslate"><span class="pre">.py</span></code> stripped; if no registry is passed, the warning is never suppressed.
<em>message</em> must be a string and <em>category</em> a subclass of <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a> or
<em>message</em> may be a <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a> instance, in which case <em>category</em> will be
ignored.</p>
<p><em>module_globals</em>, if supplied, should be the global namespace in use by the code
for which the warning is issued.  (This argument is used to support displaying
source for modules found in zipfiles or other non-filesystem import
sources).</p>
<p><em>source</em>, if supplied, is the destroyed object which emitted a
<a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Add the <em>source</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="warnings.showwarning">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">showwarning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.showwarning" title="Link to this definition">¶</a></dt>
<dd><p>Write a warning to a file.  The default implementation calls
<code class="docutils literal notranslate"><span class="pre">formatwarning(message,</span> <span class="pre">category,</span> <span class="pre">filename,</span> <span class="pre">lineno,</span> <span class="pre">line)</span></code> and writes the
resulting string to <em>file</em>, which defaults to <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>.  You may replace
this function with any callable by assigning to <code class="docutils literal notranslate"><span class="pre">warnings.showwarning</span></code>.
<em>line</em> is a line of source code to be included in the warning
message; if <em>line</em> is not supplied, <a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a> will
try to read the line specified by <em>filename</em> and <em>lineno</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="warnings.formatwarning">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">formatwarning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.formatwarning" title="Link to this definition">¶</a></dt>
<dd><p>Format a warning the standard way.  This returns a string which may contain
embedded newlines and ends in a newline.  <em>line</em> is a line of source code to
be included in the warning message; if <em>line</em> is not supplied,
<a class="reference internal" href="#warnings.formatwarning" title="warnings.formatwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">formatwarning()</span></code></a> will try to read the line specified by <em>filename</em> and
<em>lineno</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="warnings.filterwarnings">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">filterwarnings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">action</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Warning</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">append</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.filterwarnings" title="Link to this definition">¶</a></dt>
<dd><p>Insert an entry into the list of <a class="reference internal" href="#warning-filter"><span class="std std-ref">warnings filter specifications</span></a>.  The entry is inserted at the front by default; if
<em>append</em> is true, it is inserted at the end.  This checks the types of the
arguments, compiles the <em>message</em> and <em>module</em> regular expressions, and
inserts them as a tuple in the list of warnings filters.  Entries closer to
the front of the list override entries later in the list, if both match a
particular warning.  Omitted arguments default to a value that matches
everything.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="warnings.simplefilter">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">simplefilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">action</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Warning</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">append</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.simplefilter" title="Link to this definition">¶</a></dt>
<dd><p>Insert a simple entry into the list of <a class="reference internal" href="#warning-filter"><span class="std std-ref">warnings filter specifications</span></a>.  The meaning of the function parameters is as for
<a class="reference internal" href="#warnings.filterwarnings" title="warnings.filterwarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">filterwarnings()</span></code></a>, but regular expressions are not needed as the filter
inserted always matches any message in any module as long as the category and
line number match.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="warnings.resetwarnings">
<span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">resetwarnings</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#warnings.resetwarnings" title="Link to this definition">¶</a></dt>
<dd><p>Reset the warnings filter.  This discards the effect of all previous calls to
<a class="reference internal" href="#warnings.filterwarnings" title="warnings.filterwarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">filterwarnings()</span></code></a>, including that of the <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> command line options
and calls to <a class="reference internal" href="#warnings.simplefilter" title="warnings.simplefilter"><code class="xref py py-func docutils literal notranslate"><span class="pre">simplefilter()</span></code></a>.</p>
</dd></dl>

</section>
<section id="available-context-managers">
<h2>Available Context Managers<a class="headerlink" href="#available-context-managers" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="warnings.catch_warnings">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">warnings.</span></span><span class="sig-name descname"><span class="pre">catch_warnings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">record</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">action</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Warning</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">append</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#warnings.catch_warnings" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that copies and, upon exit, restores the warnings filter
and the <a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a> function.
If the <em>record</em> argument is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> (the default) the context manager
returns <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-class docutils literal notranslate"><span class="pre">None</span></code></a> on entry. If <em>record</em> is <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, a list is
returned that is progressively populated with objects as seen by a custom
<a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a> function (which also suppresses output to <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>).
Each object in the list has attributes with the same names as the arguments to
<a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a>.</p>
<p>The <em>module</em> argument takes a module that will be used instead of the
module returned when you import <a class="reference internal" href="#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> whose filter will be
protected. This argument exists primarily for testing the <a class="reference internal" href="#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a>
module itself.</p>
<p>If the <em>action</em> argument is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, the remaining arguments are
passed to <a class="reference internal" href="#warnings.simplefilter" title="warnings.simplefilter"><code class="xref py py-func docutils literal notranslate"><span class="pre">simplefilter()</span></code></a> as if it were called immediately on
entering the context.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-class docutils literal notranslate"><span class="pre">catch_warnings</span></code></a> manager works by replacing and
then later restoring the module’s
<a class="reference internal" href="#warnings.showwarning" title="warnings.showwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">showwarning()</span></code></a> function and internal list of filter
specifications.  This means the context manager is modifying
global state and therefore is not thread-safe.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <em>action</em>, <em>category</em>, <em>lineno</em>, and <em>append</em> parameters.</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a><ul>
<li><a class="reference internal" href="#warning-categories">Warning Categories</a></li>
<li><a class="reference internal" href="#the-warnings-filter">The Warnings Filter</a><ul>
<li><a class="reference internal" href="#describing-warning-filters">Describing Warning Filters</a></li>
<li><a class="reference internal" href="#default-warning-filter">Default Warning Filter</a></li>
<li><a class="reference internal" href="#overriding-the-default-filter">Overriding the default filter</a></li>
</ul>
</li>
<li><a class="reference internal" href="#temporarily-suppressing-warnings">Temporarily Suppressing Warnings</a></li>
<li><a class="reference internal" href="#testing-warnings">Testing Warnings</a></li>
<li><a class="reference internal" href="#updating-code-for-new-versions-of-dependencies">Updating Code For New Versions of Dependencies</a></li>
<li><a class="reference internal" href="#available-functions">Available Functions</a></li>
<li><a class="reference internal" href="#available-context-managers">Available Context Managers</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="__main__.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="dataclasses.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dataclasses</span></code> — Data Classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/warnings.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="dataclasses.html" title="dataclasses — Data Classes"
             >next</a> |</li>
        <li class="right" >
          <a href="__main__.html" title="__main__ — Top-level code environment"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>