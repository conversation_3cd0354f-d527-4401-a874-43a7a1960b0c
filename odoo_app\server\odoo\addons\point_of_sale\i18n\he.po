# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>fur A Banter <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# yotam katz<PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# NoaFarkash, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>r, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# דו<PERSON><PERSON> <Du<PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Orel <PERSON>many, 2024
# yael terner, 2024
# <AUTHOR> <EMAIL>, 2024
# Uri Segman, 2024
# tomerlayline, 2025
# <AUTHOR> <EMAIL>, 2025
# <AUTHOR> <EMAIL>, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"This issue occurs because the quantity becomes zero after rounding during the conversion. To fix this, adjust the conversion factors or rounding method to ensure that even the smallest quantity in the original unit does not round down to zero in the target unit."
msgstr ""
"\n"
"הבעיה הזו מתרחשת משום שהכמות הופכת לאפס לאחר עיגול במהלך ההמרה. כדי לתקן זאת, יש להתאים את מקדמי ההמרה או את שיטת העיגול, כך שגם הכמות הקטנה ביותר ביחידת המקור לא תעוגל לאפס ביחידת היעד."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid " - From \"%s\" to \"%s\""
msgstr "- מ - \"%s\" ל -\"%s\""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - closing"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - opening"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "החזר כספי"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "השתמש במדפסת המחוברת לקופסת IoT"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "% Disc"
msgstr "%הנחה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "%(vatLabel)s: %(vatId)s"
msgstr "%(vatLabel)s: %(vatId)s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s תשלום קופה של %s ב- %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "%s customer(s) found for \"%s\"."
msgstr "%s לקוחות נמצאו עבור \"%s\"."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "%s fiscal position(s) added to the configuration."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order?"
msgstr "%s יש סכום כולל של %s, האם אתה בטוח שברצונך למחוק הזמנה זו?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "%s product(s) found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s untaxed"
msgstr "%s ללא מע\"מ"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s with %s"
msgstr "%s עם %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(חילוץ עבור %(session)s)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(מעודכן לפתיחה)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "+ New Shop"
msgstr "+ חנות חדשה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "/pos/ticket and use the code below to request an invoice online"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "0.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "00014-001-0001"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10"
msgstr "10"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "100.00"
msgstr "100.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "1000"
msgstr "1000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000"
msgstr "10000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000.00"
msgstr "10000.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "123.45"
msgstr "1234567890"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "1234567890"
msgstr "1234567890"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "2-03-2000 9:00 AM"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "45"
msgstr "45"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__ticket_code
msgid ""
"5 digits alphanumeric code to be used by portal user to request an invoice"
msgstr "קוד אלפאנומרי בן 5 תווים שישמש את משתמש הפורטל לבקשת חשבונית."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "5.00"
msgstr "5.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "50.00"
msgstr "50.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "567789"
msgstr "567789"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "95.00"
msgstr "95.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "987657"
msgstr "987657"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "99.99"
msgstr "99.99"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> ערוך"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>How to manage tax-included prices"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"<p>Dear %(client_name)s,<br/>Here is your electronic ticket for the "
"%(pos_name)s. </p>"
msgstr ""
"<p>לכבוד %(client_name)s,<br/>להלן הכרטיס האלקטרוני שלך עבור %(pos_name)s. "
"</p>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Barcodes</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<span class=\"o_form_label\"> ברקודים</span>\n"
"                                   <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Cash Register</span>"
msgstr "<span class=\"o_stat_text\">קופת מזומן</span> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Journal Items</span>"
msgstr "<span class=\"o_stat_text\">תנועות יומן</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>דלג על מסך התצוגה המקדימה</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "<span invisible=\"is_total_cost_computed\">TBD</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Balance</span>"
msgstr "<span>יתרה</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Closing</span>"
msgstr "<span>סגירה</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>דו\"חות</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>תצוגה</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong>&gt; מסופי תשלום</strong>\n"
"                               כדי להתקין מסוף תשלום וליצור שיטת תשלום משולבת באופן מלא."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Amount of discounts</strong>:"
msgstr "<strong>סכום ההנחות</strong>:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<strong>Amounting to:</strong>"
msgstr "<strong>בסכום של:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Config names</strong>"
msgstr "<strong> הגדר שמות</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>End of session note:</strong>"
msgstr "<strong>הערת סגירת משמרת:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Number of discounts</strong>:"
msgstr "<strong> מספר של הנחות</strong>:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Opening of session note:</strong>"
msgstr "<strong>הערת פתיחת משמרת:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "<strong>Source Invoice:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Total</strong>"
msgstr "<strong>סה\"כ</strong>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "לחיצה על \"אישור\" תאשר את התשלום?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "נדרש שם לקוח"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"מזהה ייחודי כלל מערכתי עבור תצורת קופה זו, למניעת חוסר התאמה בנתונים שנוצרו "
"על ידי הלקוח."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr "מספר רצף שמצטבר בכל פעם שמשתמש מחדש את הפעלת המשמרת"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "מספר סידורי העולה בערכו עם כל הזמנה חדשה"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr "משמרת היא פרק זמן, בדרך כלל של יום אחד, שבה אתה מוכר דרך הקופה."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"ישנו תהליך מכירה פתוח בקופה זו. ישנן הגדרות שניתן לשנות רק לאחר סגירת תהליך "
"המכירה."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "מספר רצף ייחודי למשמרת עבור הזמנה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "טקסט קצר שיוכנס ככותרת התחתונה בקבלה המודפסת."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "טקסט קצר שיוכנס ככותרת בקבלה המודפסת."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:point_of_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"מוצר מנוהל מלאי הוא מוצר שעבורו מתבצע ניהול מלאי במערכת. יש להתקין את יישום "
"המלאי.מוצר לא מנוהל מלאי הוא מוצר אשר לא מתבצע עבורו ניהול מלאי במערכת.שירות"
" הוא מוצר לא חומרי שאתה מספק."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"A valid product already exists for Point of Sale. Therefore, demonstration "
"products cannot be loaded."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "ALL POS"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "סכום"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "קבל טיפים של לקוחות או המר את העודף שלהם לטיפ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a PayTM payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "קבל תשלומים באמצעות מסוף תשלום של Six"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Stripe payment terminal"
msgstr "אפשר תשלומים עם מסוף תשלומים Stripe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "קבל תשלומים באמצעות מסוף תשלומים של Vantiv"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "קבל תשלומים באמצעות מסוף תשלומים של Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_warning
msgid "Access warning"
msgstr "אזהרת גישה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "חשבון"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "חשבון עיגול מזומן"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "תבנית לוח חשבונות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "תנועת חשבון"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "הנהלת חשבונות"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "יומן הנהלת חשבונות המשמש ליצירת חשבוניות."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_journal_id
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"יומן הנהלת חשבונות המשמש לרישום פקודות יומן של סשן הקופה (POS) ותשלומים של "
"חשבוניות מהקופה."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "פעיל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#, python-format
msgid "Add"
msgstr "הוסף"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
#, python-format
msgid "Add Customer Note"
msgstr "הוסף הערות לקוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Add Tip"
msgstr "הוסף טיפ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Add a 5-digit code on the receipt to allow the user to request the invoice "
"for an order on the portal."
msgstr ""
"הוסף קוד בן 5 ספרות על גבי הקבלה, שיאפשר ללקוח לבקש את החשבונית עבור ההזמנה "
"דרך הפורטל."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid ""
"Add a QR code on the ticket, which the user can scan to request the invoice "
"linked to its order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Add a closing note..."
msgstr "הוסף הערת סגירה..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a custom message to header and footer"
msgstr "הוסף הודעה מותאמת אישית בכותרת העליונה והתחתונה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Add a customer"
msgstr "הוסף לקוח"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "הוסף אמצעי תשלום חדש"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid "Add a new restaurant order printer"
msgstr "הוסף מדפסת הזמנות חדשה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.xml:0
#, python-format
msgid "Add a note..."
msgstr "הוסף הערה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Add an opening note..."
msgstr "הוסף הערת פתיחה..."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Add to order"
msgstr "הוסף להזמנה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required information:"
msgstr "מידע נוסף נדרש:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required invoicing information:"
msgstr "מידע נוסף נדרש בנושא חשבוניות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required user information:"
msgstr "מידע נוסף נדרש על המשתמש"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Address"
msgstr "כתובת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adds a button to set a global discount"
msgstr "מוסיף כפתור להנחה גלובלית"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Advanced Cash Control"
msgstr "בקרת מזומן מתקדמת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "מסוף תשלום Adyen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "All active orders"
msgstr "כל ההזמנות הפתוחות"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"כל המחירונים הזמינים חייבים להיות באותו מטבע כמו של החברה או כמו  שמוגדר "
"ביומן מכירות בקופה זו אם אתה משתמש ביישום הנהלת חשבונות."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"כל אמצעי התשלום חייבים להיות באותו מטבע כמו יומן המכירות או מטבע החברה אם זה"
" לא מוגדר."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "כל שורות המכירה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow Ship Later"
msgstr "אפשר שילוח מאוחר יותר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow cashiers to set a discount per line"
msgstr "אפשר לקופאים להגדיר הנחה פר שורת הזמנה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to access each other's active orders"
msgstr "לאפשר גישה להזמנות הפעילות אחד של השני"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to log and switch between selected Employees"
msgstr "אפשר התחברות והחלפה בין עובדים "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allowed"
msgstr "מותר"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr "מאפשר לבדוק האם כל העלויות הכוללות של שורות ההזמנה כבר חושבו."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr "מאפשר לדעת אם העלות הכוללת כבר חושבה או לא."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
#, python-format
msgid "Amount"
msgstr "סכום כולל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "סכום הפרש מאושר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "סכום לאיזון היתרה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "סה\"כ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"אירעה שגיאה בעת ניסיון לסגור את המשמרת. תועבר למערכת הניהול לצורך סגירה "
"ידנית של המשמרת."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr "אירעה שגיאה בעת טעינת מחירי המוצר. ודא שכל המחירונים זמינים בקופה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/main.js:0
#, python-format
msgid "An error occurred while loading the Point of Sale: \n"
msgstr "אירעה שגיאה בעת טעינת הקופה:\n"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "מזהה פנימי של הקופה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__name
msgid "An internal identification of the printer"
msgstr "מזהה פנימי של המדפסת"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "משמרת נוספת כבר נפתחה לקופה זו."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "בארכיון"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "האם אתה בטוח שהלקוח מעוניין לשלם"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "בהקדם האפשרי"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (faster)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr "בזמן אמת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "תכונות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Authorized Difference"
msgstr "הפרש מורשה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__auto_validate_terminal_payment
msgid "Auto Validate Terminal Payment"
msgstr "אימות אוטומטי של תשלום מסוף"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr "משמרת שנוצרת אוטומטית להזמנות יתומות, מתעלמת מהאילוצים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "הדפסת קבלות אוטומטית"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "פתח מגירת מזומנים אוטומטית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validate order"
msgstr "אימות הזמנה באופן אוטומטי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_pos_config__auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validates orders paid with a payment terminal."
msgstr "מאמת אוטומטית הזמנות ששולמו באמצעות מסוף תשלום."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Available"
msgstr "זמין"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "קטגוריות מוצרים זמינות של קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_available_pricelist_ids
msgid "Available Pricelists"
msgstr "מחירון זמין "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "זמין בקופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "מחיר ממוצע"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "BACK"
msgstr "חזור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Back"
msgstr "חזור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Backend"
msgstr "השאר משמרת פתוחה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_background_image_1920
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_background_image_1920
msgid "Background Image"
msgstr "תמונת רקע"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Badge ID"
msgstr "מספר תג"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Balance"
msgstr "יתרה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "בנק"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "תשלומים בהעברה בנקאית"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "שורת דף בנק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Barcode"
msgstr "ברקוד"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "מונחי ברקוד"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "כלל ברקוד"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "סורק ברקוד"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "סורק ברקודים או קורא כרטיסים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Base"
msgstr "בסיס"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "סכום בסיס"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "הפרש לפני סגירה "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Billing address:"
msgstr "כתובת לחיוב:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "חשבוניות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Bills & Receipts"
msgstr "קבלה בסיסית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Buffer:"
msgstr "באפר:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "עוקף הדפסת דפדפן ומדפיס באמצעות פרוקסי החומרה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "CANCELLED"
msgstr "בוטל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "CASH"
msgstr "מזומן"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "עודף"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Can't change customer"
msgstr "טעות בשינוי לקוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "בטל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "בטל בקשת תשלום"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "לא ניתן לערוך טיפ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "לא ניתן להחזיר עודף כאשר לא בוצע תשלום במזומן"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "שם מחזיק/ת הכרטיס"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#: model:pos.payment.method,name:point_of_sale.payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Cash"
msgstr "מזומן"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Cash %s"
msgstr "מזומן %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash In"
msgstr "הפקדת מזומן"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "מזומן נכנס/ יוצא"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "יומן מזומנים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_line_ids
msgid "Cash Lines"
msgstr "שורות מזומן"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Cash Move 1"
msgstr "העברת מזומן 1"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Cash Opening"
msgstr "יתרת מזומן לפתיחת קופה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash Out"
msgstr "הוצאת מזומן"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Rounding"
msgstr "עיגול מזומן"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_rounding
msgid "Cash Rounding (PoS)"
msgstr "עיגול מזומן (קופה)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "עיגול מזומן"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Cash control - closing"
msgstr "מסך סגירת קופה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.js:0
#, python-format
msgid "Cash control - opening"
msgstr "מסך פתיחת קופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Loss)"
msgstr "הפרש מזומנים שנצפה במהלך הספירה (הפסד)"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Profit)"
msgstr "הפרש מזומנים שנצפה במהלך הספירה (רווח)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.js:0
#, python-format
msgid "Cash in / out"
msgstr "מזומן נכנס \\ יוצא"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register"
msgstr "רישום בקופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_rounding_method
msgid "Cash rounding"
msgstr "עיגול מזומן"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "מגירת מזומנים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
#, python-format
msgid "Cashier"
msgstr "קופאי"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"קטגוריות משמשות לעיון במוצרים שלך דרך\n"
"                ממשק מסך מגע."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/category_selector/category_selector.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "קטגוריה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "שם קטגוריה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_ids
msgid "Category used in the Point of Sale."
msgstr "קטגוריה בשימוש בקופה."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "כסאות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Change"
msgstr "עודף"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Change Tip"
msgstr "שנה טיפ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Change:"
msgstr "עודף:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr "בדוק אם יש לשקול את המוצר באמצעות אינטגרצית חומרת משקל."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "סמן אם ברצונך שמוצר זה יופיע בקופה."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr "סמן אם ברצונך לקבץ מוצרים מקטגוריה זו בהזמנות קופה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "בדוק את קופת המזומן בפתיחה ובסגירה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"בדוק את חיבור האינטרנט ואז נסה לסנכרן שוב על ידי לחיצה על כפתור ה-WiFi האדום"
" (בפינה הימנית העליונה של המסך)."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "תתי קטגוריות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"בחר במעמד פיסקלי מסוים בהזמנה בהתאם לסוג הלקוח (פטור ממס, במקום לעומת טייק "
"אווי, וכו')."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "City"
msgstr "עיר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Click here to close the session"
msgstr "לחץ כאן על מנת לסגור את הקופה"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "לקוח"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Close"
msgstr "סגור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "סגור משמרת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "סגור משמרת ורשום תנועות"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "סגור אשף משמרת"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "נסגר ונרשם"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "בקרת סגירה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "תאריך סגירה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr "הערות סגירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing Session"
msgstr "סגירת קופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "הפרש סגירת קופה ב- %s(%s)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing note"
msgstr "הערת סגירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Closing session error"
msgstr "טעות בסגירת קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "ערך מטבע/שטר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#, python-format
msgid "Coins/Bills"
msgstr "מטבעות/שטרות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__combo_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__combo_ids
msgid "Combinations"
msgstr "שילובים"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "שלב %s תשלומי קופה מ%s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_id
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__detailed_type__combo
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__type__combo
msgid "Combo"
msgstr "קומבו"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_combo
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Combo Choices"
msgstr "שילוב מוצרים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "שורות קומבו"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combo Name"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "Combo products cannot contains variants or attributes"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combos"
msgstr "שילובים"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "חברה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_company_has_template
msgid "Company has chart of accounts"
msgstr "לחברה יש לוח חשבונות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Complete the selection to proceed"
msgstr "יש להשלים את הבחירה כדי להמשיך"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "תצורה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "תצורה &gt; הגדרות"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Configure at least one Point of Sale."
msgstr "הגדר נקודת מכירה אחת לפחות."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#, python-format
msgid "Confirm"
msgstr "אשר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#, python-format
msgid "Confirm?"
msgstr "לאשר?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect device to your PoS without an IoT Box"
msgstr "חבר מכשיר לקופה שלך ללא קופסת loT"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "חבר מכשירים לקופה שלך ללא קופסת IoT"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect devices using an IoT Box"
msgstr "חבר מכשירים עם loT Box"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connected Devices"
msgstr "מכשירים מחוברים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "מחובר, ללא בבעלות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "מתחבר לפרוקסי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Connection error"
msgstr "שגיאת התחברות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "החיבור לקופסת IoT נכשל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "החיבור למדפסת נכשל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model,name:point_of_sale.model_res_partner
#, python-format
msgid "Contact"
msgstr "איש קשר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Continue"
msgstr "המשך"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "המשך מכירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "Continue with limited functionalities"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"Conversion Error: The following unit of measure conversions result in a zero"
" quantity due to rounding:"
msgstr "שגיאת המרה: ההמרות הבאות של יחידות מידה מניבות כמות אפסית עקב עיגול:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "יחס המרה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "שער המרה ממטבע החברה למטבע הזמנה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Cost:"
msgstr "עלות:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Counted"
msgstr "נספר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Country"
msgstr "ארץ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__country_code
msgid "Country Code"
msgstr "קוד ארץ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Create"
msgstr "צור"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "צור הזמנת קופה חדשה"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Create a new PoS"
msgstr "צור קופה חדשה"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "צור וריאנט מוצר חדש"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "שער מטבע"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "משמרת נוכחית"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "אחראי משמרת נוכחית"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "מצב משמרת נוכחית"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "מותאם"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_header_or_footer
msgid "Custom Header & Footer"
msgstr "כותרת עליונה ותחתונה מותאמות אישית"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__custom_attribute_value_ids
msgid "Custom Values"
msgstr "ערכים מותאמים אישית"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "לקוח"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "חשבון לקוח"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Customer Display"
msgstr "תצוגת לקוח"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "תצוגה מול לקוח"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "חשבונית לקוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "הערות לקוח"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__access_url
msgid "Customer Portal URL"
msgstr "כתובת אתר של פורטל לקוחות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Customer Required"
msgstr "דרוש לקוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen Connected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Customer Screen Unsupported. Please upgrade the IoT Box"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "נדרש לקוח עבור%s אמצעי התשלום."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "לקוחות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_PRODUCT_NAME"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_REF"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "DETAILS"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Daily Sale"
msgstr "מכירה יומית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Daily Sales Report"
msgstr "דו\"ח מכירות יומי"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "Daily sessions hold sales from your Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "לוח בקרה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#, python-format
msgid "DatePicker"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Days"
msgstr "ימים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Debug Window"
msgstr "חלון ניפוי שגיאות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default"
msgstr "ברירת מחדל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "חשבון לקוחות דיפולטי (קופה)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "מעמד פיסקלי ברירת מחדל"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Journals"
msgstr "יומנים ברירת מחדל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Default Price"
msgstr "מחיר ברירת מחדל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_id
msgid "Default Pricelist"
msgstr "מחירון ברירת מחדל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "מיסים במכירה ברירת מחדל"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "מיסי מכירות ברירת מחדל"

#. module: point_of_sale
#: model:account.tax,name:point_of_sale.pos_taxes_0
msgid "Default Tax for PoS"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Temporary Account"
msgstr "חשבון זמני ברירת מחדל"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default journals for orders and invoices"
msgstr "יומנים ברירת מחדל להזמנות וחשבוניות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "מס מכירות ברירת מחדל עבור מוצרים"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "יחידת מידה ברירת מחדל המשמשת לביצוע כל פעולות המלאי."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "הגדר קטגוריה חדשה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "הגדר את המטבע הקטן ביותר של המטבע המשמש לתשלום במזומן"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "עיכוב באימות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Delete"
msgstr "מחק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "מחק הזמנות ששולמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Paid Orders?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "מחק הזמנות שלא שולמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Unpaid Orders?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo 3-03-2000 5:00 PM"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo Name"
msgstr "שם דמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "Demo products are no longer available"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "מארגן שולחנות"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "פד לשולחן"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "שולחנות עבודה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "חשבון יעד"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "חשבון היעד הוא לקריאה בלבד"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Difference"
msgstr "הפרש"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "הבדל בעת סגירת משמרת בקופה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "ההפרש בין יתרת הסגירה התיאורטית לבין יתרת הסגירה בפועל."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "תמצית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "הנחה %"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "הנחה:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Discard"
msgstr "בטל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Disconnected"
msgstr "התנתק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
#, python-format
msgid "Discount"
msgstr "הנחה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "הנחה (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "הודעת הנחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Discount:"
msgstr "הנחה"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "מוצר בהנחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Discounts"
msgstr "הנחות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Discounts:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Dismiss"
msgstr "שחרר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Display orders on the preparation display"
msgstr "הצג הזמנות על מסך ההכנה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "אין לך גישה, דלג על נתונים אלה עבור תקציר דוא\"ל של המשתמשים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"Do you want to accept payments difference and post a profit/loss journal "
"entry?"
msgstr "האם אתה רוצה להסכים להפרש בתשלומים וליצור פקודת יומן של רווח/הפסד?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "האם ברצונך לפתוח את רשימת הלקוחות כדי לבחור לקוח?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Do you want to print using the web printer? "
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "הורד הזמנות ששולמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "הורד הזמנות שלא שולמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Download a report with all the sales of the current PoS Session"
msgstr "הורד דו\"ח המפרט את המכירות של המשמרת הנוכחית"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Download error traceback"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"            Proxy where the printer can be found, and a list of product categories.\n"
"            An Order Printer will only print updates for products belonging to one of\n"
"            its categories."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Edit"
msgstr "ערוך"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Electronic Scale"
msgstr "משקל אלקטרוני"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Email"
msgstr "דוא\"ל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Email sent."
msgstr "דוא\"ל נשלח"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"עובדים יכולים לסרוק את התג שלהם או להזין קוד סודי כדי להיכנס למשמרת קופה. "
"אישורים אלה ניתנים להגדרה בכרטיסייה * הגדרות משאבי אנוש * בטופס העובד."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Empty Order"
msgstr "הזמנה ריקה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"הפעל סריקת ברקוד עם סורק ברקוד מחובר מרחוק והחלקת כרטיסים עם קורא כרטיסים של"
" Vantiv."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "אפשר אינטגרצית משקל אלקטרוני."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "שגיאה בהעלאת תמונה. נא לנסות שוב."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "תאריך סיום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "יתרת סגירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error"
msgstr "שגיאה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Error with Traceback"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "שגיאה! לא ניתן ליצור קטגוריות רקורסיביות."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "שגיאה: אין חיבור אינטרנט"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "שורות הזמנה קיימות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Exit Pos"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Expected"
msgstr "צפוי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Expected delivery:"
msgstr "מועד אספקה משוער"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "ייצא הזמנות ששולמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "ייצא הזמנות שלא שולמו"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "מידע נוסף"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "בד"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "בחירות שנכשלו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "כשלון בהדפסת השינוי בהזמנה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Financials"
msgstr "פיננסים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "ייבוא הזמנות הסתיים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "סוג תנועה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "סוג תנועה לא נמצא"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "סוגי תנועה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Pricelists"
msgstr "מחירונים גמישים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Taxes"
msgstr "מיסים גמישים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Footer"
msgstr "כותרת תחתונה "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "למסכי מגע תעשייתיים לא מדויקים."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "סגור את המשמרת בכוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force Done"
msgstr "אלץ בוצע"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force done"
msgstr "אלץ בוצע"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "סגירת חשבון עם יתרה פתוחה (בכפייה)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"כופה הזנת לקוח בעת שימוש בשיטת תשלום זו ומפצל את פקודות היומן עבור כל לקוח. "
"פעולה זו עלולה להאט את תהליך הסגירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Free"
msgstr "פנוי"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "מתשלום חשבוניות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "שם מוצר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid "Generate a code on ticket"
msgstr "צור קוד על קבלה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Generation of your order references"
msgstr "יצירת מזהי ההזמנות שלך"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Get my invoice"
msgstr "קבל את החשבונית שלי"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "נותן את סדר הרצף בעת הצגת רשימה של קטגוריות מוצרים."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_discount
msgid "Global Discounts"
msgstr "הנחה כללית"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "Go Back"
msgstr "חזור"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "עבור ל-"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "יותר מהמותר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "קבץ מוצרים בקופה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "חיבור HTTPS לקופסת IoT נכשל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "אירועי חומרה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "סטטוס חומרה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_has_active_session
msgid "Has Active Session"
msgstr "משמרת פעילה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "קיימת בקרת מזומנים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__has_image
msgid "Has Image"
msgstr "בעל תמונה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "שורות להחזר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Header"
msgstr "כותרת עליונה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Category Images"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Product Images"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "הסתר שימוש במסוף תשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "מזהה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "חשוב: דוח שגיאות מיישום קופה של Odoo "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid "IP Address"
msgstr "כתובת IP"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "מזהה לקוח"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"אם אתה שולח את כל המוצרים בבת אחת, הזמנת המשלוח תתוזמן על סמך זמן האספקה "
"הארוך ביותר. אחרת, על סמך זמן האספקה הקצר ביותר."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "תצוגת iface מול לקוחות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__image
msgid "Image"
msgstr "תמונה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Import Orders"
msgstr "ייבא הזמנות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "שפר את הניווט למסכי מגע תעשייתיים לא מדויקים"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "בתהליך"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "כדי למחוק מכירה, עליה להיות חדשה או מבוטלת."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time (accurate but slower)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "כתובת שגויה למשלוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "הודעת מידע"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_start_categ_id
msgid "Initial Category"
msgstr "קטגוריה ראשונית"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"הגדרת לוח חשבונות מההגדרות הכלליות של\n"
"                יישום הנהלת חשבונות ייצור אמצעי תשלום בנק ומזומן\n"
"                אוטומטית."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "תשלומי כרטיס משולבים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "חשבון ארעי"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Intermediary account used for unidentified customers."
msgstr "חשבון מתווך המשמש ללקוחות לא מזוהים"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "קטגוריות פנימיות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "הערות פנימיות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Invalid action"
msgstr "פעולה שגויה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Invalid email."
msgstr "מייל שגוי."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "מלאי"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "ניהול מלאי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "חשבונית מס"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Invoice Journal"
msgstr "יומן חשבוניות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Invoice Name"
msgstr "שם חשבונית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Invoice Request"
msgstr "בקשת חשבונית"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "תשלום חשבונית עבור (%s) באמצעות %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Invoiced"
msgstr "חויב"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Invoices"
msgstr "חשבוניות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Invoicing confirmation"
msgstr "אישור חשבונית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box"
msgstr "קופסת IoT"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box IP Address"
msgstr "כתובת IP של קופסת IoT "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "הוא חיוב"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__is_kiosk_mode
msgid "Is Kiosk Mode"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "זיכוי בוצע"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "האם עלות כוללת מחושבת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "הוא משתמש במטבע החברה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "בר /מסעדה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "האם הנהלת החשבונות המלאה מותקנת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "האם טופל?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "האם התשלום השתנה?"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"חל איסור לשנות מס בשימוש בהזמנת קופה שלא נרשמה. עליך לסגור את משמרות הקופה "
"לפני שינוי המס."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "It is not allowed to mix refunds and sales"
msgstr "אסור לשלב החזרים ומכירות באותה פעולה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "It is possible to print your tickets by making use of an IoT Box."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "בדיקות JS"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Journal"
msgstr "יומן"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "תנועת יומן"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Journal Items"
msgstr "תנועות יומן"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "מדד ערך כולל של קופה"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "מנורת לד"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "תווית"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Language"
msgstr "שפה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Laptop model x"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "פסי גלילה גדולים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "מזומן סגירה משמרת אחרונה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "תאריך סגירה משמרת אחרונה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last preparation change"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last printed state of the order"
msgstr "המצב האחרון שהוזמנה בו ההזמנה"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "עור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Leave a reason here"
msgstr "כתוב סיבה כאן"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr "השאר ריק כדי להשתמש בחשבון ברירת המחדל מהגדרות החברה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"השאר ריק כדי להשתמש בחשבון ברירת המחדל מהגדרות החברה.\n"
"פעולה זו תעקוף את חשבון הלקוחות של החברה (לצורך הקופה) המשמש ברישומים חשבונאיים."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "השאר ריק כדי להשתמש בחשבון תקבולים של הלקוח"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "מגש מכתבים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_manual_discount
msgid "Line Discounts"
msgstr "שורת הנחות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "שורה מס'"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Load Order"
msgstr "טען הזמנה"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_product_menu
msgid "Load Product Menu"
msgstr "טען תפריט מוצרים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Loading Image Error"
msgstr "שגיאה בהצגת דימוי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Loading..."
msgstr "טוען..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "תצוגה מול לקוחות מקומיים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "מספר רצף כניסה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Logo"
msgstr "לוגו"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "שם אצווה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Lot Number"
msgstr "מספר אצווה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "נדרש מספר סידורי או מספר אצווה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "מספר סידורי או אצווה"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "לוח מגנטי"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "בצע תשלום"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"הפוך מספר מחירונים זמינים לקופה. תוכל גם להחיל מחירונים על לקוחות ספציפיים "
"מטופס איש הקשר שלהם (בכרטיסיית מכירות). כדי להיות תקף, מחירון זה חייב להיות "
"רשום כאן כמחירון זמין, אחרת מחירון ברירת המחדל יחול."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"וודא שאתה משתמש ב- IoT Box v18.12 ומעלה. נווט ל %s כדי לקבל את האישור של "
"קופסת ה- IoT שלך."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion that will grant customers discounts or gifts"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Marc Demo"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "מרווח"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "רווח (%)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Margin:"
msgstr "שולי רווח:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Margins & Costs"
msgstr "רווח & עלות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "חריגה מהערך המרבי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "הגעת לערך המרבי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid ""
"Meanwhile connection is back, Odoo Point of Sale will operate limited "
"operations. Check your connection or continue with limited functionalities"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "אמצעי תשלום"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Method Name"
msgstr "שם שיטה"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Misc"
msgstr "שונות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Mobile"
msgstr "נייד"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Module Pos Hr"
msgstr "מודול קופה משאבי אנוש"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "עמדת צג"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "More info"
msgstr "מידע נוסף"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "More settings:"
msgstr "הגדרות נוספות:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#, python-format
msgid "More..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Multi Employees per Session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Multiple Invoiced Orders Selected"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "המשמרות שלי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "חדש"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "הערה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
#, python-format
msgid "Name"
msgstr "שם"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Need customer to invoice"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Network Error"
msgstr "שגיאת רשת"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "חדש"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "New Order"
msgstr "הזמנה חדשה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "משמרת חדשה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.js:0
#, python-format
msgid "New amount"
msgstr "כמות חדשה"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "מתקן לעיתונים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Next Order List"
msgstr "לרשימת הזמנות הבאה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "No"
msgstr "לא"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "No Point of Sale selected"
msgstr "לא נבחרה קופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "No Taxes"
msgstr "אין מיסים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid ""
"No barcode nomenclature has been configured. This can be changed in the "
"configuration settings."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr "לא נמצא תדפיס מזומן עבור משמרת זו. לא ניתן לרשום מזומנים שהוחזרו."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"No chart of account configured, go to the \"configuration / settings\" menu,"
" and install one from the Invoicing tab."
msgstr ""
"אין לוח חשבונות מוגדר. עבור לתפריט \"הגדרות / קונפיגורציה\" והתקן אחד "
"מכרטיסיית החשבוניות."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "אין מידע עדיין"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "אין קישור לחשבונית עבור %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "No more customer found for \"%s\"."
msgstr "לא נמצא לקוח עבור \"%s\"."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "No more product found for \"%s\"."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__num_of_products
msgid "No of Products"
msgstr "מס' מוצרים"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "No orders found"
msgstr "לא נמצאו הזמנות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products available. Explore"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products found for"
msgstr "לא נמצאו מוצרים עבור"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "No sale order found."
msgstr "לא נמצאה הזמנת לקוח."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "No sessions found"
msgstr "לא נמצאו משמרות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "מונחים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "None"
msgstr "אף אחד"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Not Categorized"
msgstr "לא נמצא בקטגוריה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "לא חויב"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "Note"
msgstr "הערה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "הערות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "מספר הדפסה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "מספר הזמנות עם החזר כספי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_rescue_session
msgid "Number of Rescue Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Number of transactions:"
msgstr "מספר עסקאות:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "OK"
msgstr "אישור"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.office_combo_product_template
msgid "Office combo"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Offline Orders"
msgstr "הזמנות לא מקוונות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#, python-format
msgid "Ok"
msgstr "אישור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Ongoing"
msgstr "מתמשך"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Only administrators can edit receipt headers and footers"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "החל עיגול על מזומן בלבד"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr "ניתן להשתמש רק ביומנים מסוג 'מזומן' או 'בנק' עם שיטות תשלום/"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only on cash methods"
msgstr "רק בשיטות מזומן "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"רק משתמשים עם הרשאות מנהל עבור יישום קופה יכולים לשנות את מחירי המוצר "
"בהזמנות."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "פתח קופת מזומן"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "פתח משמרות קופה המשתמשות באמצעי תשלום זה."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "פתח משמרת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open session"
msgstr "פתח משמרת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "נפתחה ע\"י"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "נפתחה ע\"י"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Opening"
msgstr "יתרת פתיחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening Cash Control"
msgstr "מסך ניהול פתיחת קופה"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "בקרת פתיחה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "תאריך פתיחה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "הערות פתיחה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Opening balance summed to all cash transactions."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening cash"
msgstr "כמות מזומן בפתיחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening note"
msgstr "הערת פתיחה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation Type"
msgstr "סוג פעולה  "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "סוגי פעולה המופיעים בלוח בקרה של המלאי."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "הזמנה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Order %s"
msgstr "הזמנה %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "הזמנה %s לא שולמה במלואה."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "כמות הזמנות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "תאריך הזמנה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "Order IDs Sequence"
msgstr "רצף מזהי הזמנות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "רצף מזהי שורות הזמנה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "שורות הזמנה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tracking_number
#, python-format
msgid "Order Number"
msgstr "מספר הזמנה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "מדפסת הזמנה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_printer_ids
msgid "Order Printers"
msgstr "מדפסות הזמנה"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"            order updates in the kitchen/bar when the waiter updates the order."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order Ref"
msgstr "מזהה הזמנה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Order Reference"
msgstr "מזהה הזמנה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "מספר רצף הזמנה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "שורות הזמנה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Order number"
msgstr "מספר הזמנה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order reference"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "Order saved for later"
msgstr "הזמנה נשמרה להשלמה בהמשך"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "הזמנות"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "ניתוח נתוני הזמנות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__lst_price
msgid "Original Price"
msgstr "מחיר מקור"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Other Devices"
msgstr "מכשירים אחרים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "מידע נוסף"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Others"
msgstr "אחר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "חשבון תקבולים/ תשלומים בתהליך"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "הזמנות קופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "הזמנת קופה %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "שורת הזמנה של קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "שורות הזמנה של קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "הזמנות קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "שורות הזמנות קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "אמצעי תשלום בנקודת מכירה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer_form
msgid "POS Printer"
msgstr "מדפסת קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "קטגורית מוצר של קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "מכירות קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "משמרת קופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "שורת הזמנת קופה %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "שולם"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "קטגוריית אם"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Partner"
msgstr "לקוח/ספק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Pay"
msgstr "שלם"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "שלם על הזמנה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PayTM"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_paytm
msgid "PayTM Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "תשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "תאריך אסמכתא"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "אמצעי תשלום"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Methods"
msgstr "אמצעי תשלום"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Payment Name Demo"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "מידע על קבלת תשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "מזהה תשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "סטטוס תשלום"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Payment Successful"
msgstr "התשלום בוצע בהצלחה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "מסופי תשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "מזהה עסקת תשלום"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Payment method"
msgstr "אמצעי תשלום"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment methods available"
msgstr "אמצעי תשלום זמינים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment request pending"
msgstr "בקשת תשלום ממתינה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment reversed"
msgstr "תשלום בוטל"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "תשלומים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Payments Difference"
msgstr "הפרש בתשלומים!"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "אמצעי תשלום"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Payments in"
msgstr "תשלומים ב"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Payments:"
msgstr "תשלומים"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr "האדם המשתמש בקופה.יכול להיות מחליף, עובד זמני או מתלמד."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Phone"
msgstr "טלפון"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pick which product categories are available"
msgstr "בחירת קטגוריות מוצרים זמינות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "ליקוט"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "כמות ליקוטים"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "ליקוט של קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking_type
msgid "Picking Type"
msgstr "סוג ליקוט"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "ליקוטים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Picture"
msgstr "תמונה"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "פלסטיק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "אנא אשר כמות גדולה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_combo.py:0
#, python-format
msgid "Please add products in combo."
msgstr "הוסף מוצרים בשילוב"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "אנא בדוק אם קופסת IoT עדיין מחוברת."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue. "
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"אנא סגור את כל משמרות הקופה בתקופה זו לפני שתסגור את הקופה. המשמרות הפתוחות "
"הן: %s "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Please close and validate the following open PoS Sessions before modifying this payment method.\n"
"Open sessions: %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "אנא קבע את תצורת אמצעי התשלום בקופה שלך."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Please create/select a Point of Sale above to show the configuration "
"options."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "אנה הגדר חשבון הכנסות עבור מוצר זה: \"%s\" (מזהה:%d)"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please define income account for this product: '%s' (id:%d)."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Please enter your billing information <small class=\"text-muted\">or</small>"
msgstr "אנא הזן את פרטי התשלום שלך<small class=\"text-muted\">או</small>"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "Please fill all the required fields."
msgstr "נא למלא את כל השדות הדרושים."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Loss Account. This account will be "
"used to record cash difference."
msgstr ""
"נא היכנס ל %s יומן והגדר חשבון הפסד. חשבון זה ישמש לרישום הפרש מזומנים."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Profit Account. This account will "
"be used to record cash difference."
msgstr ""
"נא הכנס ל %s יומן והגדר חשבון רווח. חשבון זה ישמש לרישום הפרש מזומנים."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "אנא הדפס את החשבונית מצד השרת"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "אנא הגדר לקוח עבור המכירה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "אנא בחר אמצעי תשלום."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "אנא בחר לקוח"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Interface"
msgstr "ממשק קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_attribute_custom_value__pos_order_line_id
msgid "PoS Order Line"
msgstr "קופה - שורת הזמנה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "הזמנות קופה"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Product Categories"
msgstr "קטגוריות מוצרים של קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "קטגורית מוצר של קופה"

#. module: point_of_sale
#: model:account.tax.group,name:point_of_sale.pos_taxe_group_0
msgid "PoS Taxes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "PoS order %s can not be processed"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "קופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "ניתוח נתוני מכירות של קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_ids
msgid "Point of Sale Category"
msgstr "קטגוריה של קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "תצורת קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "תצורת קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "דו\"ח קופה יומי"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "פרטים של קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "דוח פרטים של קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "דוח חשבוניות של קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Point of Sale Journal"
msgstr "יומן נקודת מכירה"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_tree
msgid "Point of Sale List"
msgstr "רשימת קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "אשף יצירת תשלום קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "קבוצת מנהלים של קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "סוג פעולה של קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "שורות הזמנה של קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "הזמנות קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "דוח הזמנות קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "אמצעי תשלום קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "תשלומי קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_printer
msgid "Point of Sale Printer"
msgstr "מדפסת קופה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "משמרת קופה "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "קבוצת משתמשי קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_list
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Point of Sales"
msgstr "נקודות מכירה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_url
msgid "Portal Access URL"
msgstr "כתובת גישה לפורטל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_allowed_pricelist_ids
msgid "Pos Allowed Pricelist"
msgstr "מחירון קופה מותר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "הגדרות קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "הזמנת קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "כמות הזמנות קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "שורת הזמנה בקופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "תשלום בנקודת מכירה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "קטגוריות מוצרים של קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_refunded_invoice_ids
msgid "Pos Refunded Invoice"
msgstr "חשבוניות זיכוי של קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_selectable_categ_ids
msgid "Pos Selectable Categ"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "משמרת קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "משך משמרת קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "מצב משמרת קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "שם משתמש משמרת קופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "משמרות קופה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "כמות חיובית אינה מותרת."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "נרשם"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Powered by"
msgstr "מופעל ע\"י"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Powered by Odoo"
msgstr "מופעל ע\"י Odoo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_preparation_display
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation Display"
msgstr "תצוגת הכנה"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_printer_form
#: model:ir.ui.menu,name:point_of_sale.menu_pos_preparation_printer
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer
msgid "Preparation Printers"
msgstr "הכנת הדפסות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "רשימת הזמנות קודמת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Price"
msgstr "מחיר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Price Control"
msgstr "בקרת מחיר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_price
msgid "Price Extra"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Price excl. Tax:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_extra
msgid "Price extra"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#, python-format
msgid "Price list"
msgstr "מחירון"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "מחיר מוצר "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "מחירון"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "מחירונים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "תמחור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "הדפס"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/reprint_receipt_button/reprint_receipt_button.xml:0
#, python-format
msgid "Print Receipt"
msgstr "הדפס קבלה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Print a QR code on the receipt to allow the user to easily request the "
"invoice for an order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "הדפס דוח עם כל מכירות משמרת הקופה הנוכחית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "הדפס הזמנות במטבח, בבר וכו'."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "הדפס קבלות באופן אוטומטי לאחר רישום התשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_via_proxy
msgid "Print via Proxy"
msgstr "הדפס דרך פרוקסי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "קטגוריות מוצרים מודפסות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Printer"
msgstr "מדפסת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__name
msgid "Printer Name"
msgstr "שם מדפסת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "סוג מדפסת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Printers"
msgstr "מדפסות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing error"
msgstr "כשלון בהדפסה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Printing failed"
msgstr "כשלון בהדפסה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "הדפסה לא נתמכת בחלק מהדפדפנים"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "קבוצת רכש"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Product"
msgstr "מוצר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "ערך מותאם אישית של תכונת מוצר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo_line
msgid "Product Combo Items"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_combo
msgid "Product Combos"
msgstr "שילובי מוצרים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "פרטי מוצר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__base_price
msgid "Product Price"
msgstr "מחיר מוצר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "מחירי המוצרים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "מוצר קטגוריות מוצרים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "כמות מוצרים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "סוג מוצר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "יחידת מידה של מוצר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "יחידת מידה של מוצר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "קטגוריות יחידת מידה של מוצר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
msgid "Product Variant"
msgstr "וריאנט מוצר"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "וריאנטים של מוצר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo
msgid "Product combo choices"
msgstr "בחירות שילוב מוצרים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Product information"
msgstr "פרטי מוצר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product prices on receipts"
msgstr "מחירי מוצרים בקבלות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tipproduct
msgid "Product tips"
msgstr "טיפים למוצרים"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "מוצרים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__combo_line_ids
msgid "Products in Combo"
msgstr "שילוב מוצרים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Products:"
msgstr "מוצרים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "מבצעים, קופונים, כרטיסי מתנה ותוכנית נאמנות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "פרוקסי מחובר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "פרוקסי מנותק"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "כתובת IP פרוקסי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "אזהרת פרוקסי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Qty"
msgstr "כמות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "כמות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "REASON"
msgstr "סיבה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "REFUNDED:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "קרא משקל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "מוכן לפתוח את  ה<b>קופה</b> שלך?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Reason"
msgstr "סיבה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Receipt"
msgstr "קבלה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "קבלה %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_footer
msgid "Receipt Footer"
msgstr "כותרת תחתונה קבלה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_header
msgid "Receipt Header"
msgstr "כותרת עליונה קבלה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "מספר קבלה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Receipt Printer"
msgstr "מדפסת קבלות"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "רשום תשלומים עם מסוף ביומן זה."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Recovery Session"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Ref 876787"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "רענן את התצוגה"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Refund"
msgstr "החזר כספי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "הזמנות עם החזר כספי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Refund and Sales not allowed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunded"
msgstr "הוחזר"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr ""
" \n"
"הזמנה שהוחזרה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "שורת הזמנה שהוחזרה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "הזמנות שהוחזרו"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "כמות ההזמנות שהוחזרו"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "כמות שהוחזרה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunding"
msgstr "מזכה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "החזרים כספיים"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Reload POS Menu"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Remaining"
msgstr "נותרו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Remove"
msgstr "הסר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Replenishment"
msgstr "עיתוד מלאי"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "דו\"חות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "הדפס חשבונית בשנית"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Request invoice"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Request sent"
msgstr "בקשה נשלחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Reset"
msgstr "אפס"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restaurant Mode"
msgstr "מצב מסעדה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_limit_categories
msgid "Restrict Categories"
msgstr "הגבל קטגוריות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "הגבל שינויים במחירים למנהלים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restrict price modification to managers"
msgstr "הגבל שינויים במחירים למנהלים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Resume Order"
msgstr "המשך בהזמנה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Retry"
msgstr "נסה שוב"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "החזר מוצרים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "הוחזר"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of POS closing entry %s for order %s from session %s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "היפוך של: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "בקשה לביטול נשלחה למסוף"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse"
msgstr "צור זיכוי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "בטל תשלום"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Review"
msgstr "בדוק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Rounding"
msgstr "עיגול"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr "שיטת עיגול"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "הרץ בדיקות JS של הקופה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "SN"
msgstr "מספר סידורי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "SOLD:"
msgstr "נמכר:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "כמות שורות מכירה"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "שורת מכירה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Sales"
msgstr "מכירות"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "פרטי מכירות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
msgid "Sales Journal"
msgstr "יומן מכירות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Closing Note"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Config Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Opening Note"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Save"
msgstr "שמור"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "שמור דף זה וחזור לכאן כדי להגדיר את התכונה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scale"
msgstr "קנה מידה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan"
msgstr "סרוק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "סרוק EAN-13"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Scan me to request an invoice for your purchase."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "סרוק דרך פרוקסי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scanner"
msgstr "סורק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Search Customers..."
msgstr "חפש לקוחות..."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Search Orders..."
msgstr "חפש הזמנות..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "חפש הזמנת לקוח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Search more"
msgstr "חפש עוד"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_token
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__access_token
msgid "Security Token"
msgstr "אסימון אבטחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#, python-format
msgid "Select"
msgstr "בחר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "בחר סוג תנועה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Select PoS to start sharing orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select a payment method to validate the order."
msgstr "בחר אמצעי תשלום על מנת לאשר את ההזמנה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Select the pricelist"
msgstr "בחר את המחירון"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select the shipping date"
msgstr "בחירת תאריך משלוח"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__attribute_value_ids
msgid "Selected Attributes"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell products and deliver them later."
msgstr "מכור מוצרים ושלח אותם במועד מאוחר יותר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send"
msgstr "שלח"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "שלח בקשת תשלום"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Send by email"
msgstr "שלח בדוא\"ל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "שליחת מייל נכשלה. נא לנסות שוב."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending in progress."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__sequence
msgid "Sequence"
msgstr "רצף"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "מספר רצף"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "מספר סידורי/ מספר אצווה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Served by"
msgstr "קיבל שירות ע\"י"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Server Error"
msgstr "שגיאת שרת"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "משמרת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session Control"
msgstr "בקרת משמרת"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "מזהה משמרת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session ID:"
msgstr "מזהה משמרת:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "פקודת יומן משמרת"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_daily_sales_reports
#: model:ir.ui.menu,name:point_of_sale.menu_report_daily_details
msgid "Session Report"
msgstr "משמרת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Session ids:"
msgstr "מזהי משמרות:"

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "משמרת פתוחה יותר מ- 7 ימים"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "הפעלות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set Maximum Difference"
msgstr "הגדר הפרש מקסמלי מותר בספירת הקופה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Set Weight"
msgstr "הגדר משקל"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"הגדר הפרש מקסימלי מורשה בין הסכום הצפוי לבין הסכום שנספר בקופה כאשר סוגרים "
"משמרת"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"הגדר הפרש מקסימלי מורשה בין הסכום הצפוי לבין הסכום שנספר בקופה כאשר סוגרים "
"משמרת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "הגדר סוג תנועה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "קבע מחירים מרובים למוצר, הנחות אוטומטיות וכו '."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new discount"
msgstr "הגדר את ההנחה החדשה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "הגדר כמות חדשה"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
msgid "Settings"
msgstr "הגדרות"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Settings on this page will apply to this point of sale."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Share Open Orders"
msgstr "שתף הזמנות פתוחות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_ship_later
#, python-format
msgid "Ship Later"
msgstr "משלוח לאחר מכירה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__shipping_date
msgid "Shipping Date"
msgstr "תאריך משלוח"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "Shipping Policy"
msgstr "מדיניות משלוח"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Shop"
msgstr "חנות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Shopping cart"
msgstr "עגלת קניות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Category Images"
msgstr "הצג תמונות קטגוריה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Product Images"
msgstr "הצגת תמונות מוצר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show checkout to customers through a second display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "הצג ביקורת יציאה ללקוחות עם מסך המחובר מרחוק."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Show checkout to customers."
msgstr "הראה הצג את תהליך התשלום ללקוחות."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Show employee login screen"
msgstr "הצג את מסך הכניסה לעובדים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs on product information"
msgstr "הצג את הרווח שולי והעלויות במידע על המוצר."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_preparation_display
msgid "Show orders on the preparation display screen."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Sign in"
msgstr "התחבר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "שש"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "גודל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "דלק על מסך תצוגה מקדימה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__skip_change
msgid "Skip line when sending ticket to kitchen printers."
msgstr "דלג על שורה כשנשלחת הזמנה למדפסות מטבח."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "מדף קטן"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "כמה מספרים סידורים\\מספרי אצווה חסרים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"לא ניתן היה לשלוח הזמנות מסוימות לשרת בגלל שגיאות תצורה. אתה יכול לצאת "
"מהקופה אך אל תסגור את המשמרת לפני שהבעיה נפתרת."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"לא ניתן היה לשלוח הזמנות מסוימות לשרת בגלל בעיות בחיבור לאינטרנט. אתה יכול "
"לצאת מהקופה, אך אל תסגור את המשמרת לפני שהבעיה נפתרת."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr "חלק (אם לא כל) מהעיבוד לאחר הסנכרון נכשל לאחר סנכרון ההזמנה."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Specific route"
msgstr "מסלול מסויים"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "ציין את המספר הסידורי או מספר האצווה של המוצר בשורת ההזמנה בקופה."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_route_id
msgid "Spefic route for products delivered later."
msgstr "מסלולי ספציפי למוצרים שיסופקו במועד מאוחר יותר."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_start_category
msgid "Start Category"
msgstr "סוג מוצר תחילי"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.js:0
#, python-format
msgid "Start adding products"
msgstr "להתחיל להוסיף מוצרים"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Start category should belong in the available categories."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Start selling from a default product category"
msgstr "התחל למכור מקטגוריית מוצרים ברירת מחדל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "יתרת התחלה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "State"
msgstr "מדינה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "סטטוס"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "כלל מלאי "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock input for %s"
msgstr "קלט מלאי ל %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock output for %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "יש לעדכן מלאי בסגירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Street"
msgstr "רחוב"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Stripe"
msgstr "Stripe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_stripe
msgid "Stripe Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
msgid "Subtotal"
msgstr "סיכום ביניים"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "סיכום ביניים ללא מע\"מ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "סכום ביניים עם או ללא הנחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "יובא בהצלחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "סך סכומי הביניים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Summary"
msgstr "תקציר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Switch Product View"
msgstr "החלף תצוגת מוצר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "הסנכרון מחובר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "הסנכרון מתחבר"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "הסנכרון מנותק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "שגיאת סנכרון"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "סה\"כ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "מס"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "סכום המס"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tax_included
msgid "Tax Display"
msgstr "תצוגת מס"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Tax ID"
msgstr "ח.פ / ע.מ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "Tax ID: %(vatId)s"
msgstr "ח.פ/ת.ז %(vatId)s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Name"
msgstr "שם המס"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "ערך בחירה משטר מס"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "מחיר ללא מע\"מ"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "מחיר כולל מע\"מ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "מיסים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on refunds"
msgstr "מיסים על זיכויים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on sales"
msgstr "מסים על מכירות"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "מיסים שחלים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Taxes:"
msgstr "מע\"מ:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuff"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuffs"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Tel:"
msgstr "טלפון:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Thank you for your purchase!"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The %s must be filled in your details."
msgstr "יש למלא את %s בפרטים שלך"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "כתובת ה-IP או hostname של חומרת הפרוקסי של המדפסת"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"קוד ארץ ISO בשני תווים. \n"
"ניתן להשתמש בשדה זה לחיפוש מהיר."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, customer, employee or action "
"associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr "הקופה תומכת רק בשיטה \" הוספת שורת עיגול\"."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The Point of Sale order with the following reference %s was received by the Odoo server, but the order processing phase failed.\n"
"The datas received from the point of sale has been saved in the attachments.\n"
"Please contact your support service to assist you on restoring it"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The Ticket Number should be at least 14 characters long."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the transaction.\n"
"The rounding precision is %s so you should set %s or %s as payment amount instead of %s."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "מחירון ברירת המחדל חייב להיכלל במחירונים הזמינים."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""
"רשימת המחירים ברירת המחדל חייבת להשתייך או לאף חברה, או לחברה של הקופה."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default tip product is missing. Please manually specify the tip product."
" (See Tips field.)"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.desk_organizer_product_template
msgid ""
"The desk organiser is perfect for storing all kinds of small things and "
"since the 5 boxes are loose, you can move and place them in the way that "
"suits you and your things best."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "The function to load %s has not been implemented."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"ה hostname או כתובת ה- ip של פרוקסי החומרה, ייזוהו אוטומטית אם הם נשארים "
"ריקים."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"יומן החשבוניות חייב להיות באותו מטבע כמו יומן המכירות או מטבע החברה אם זה לא"
" מוגדר."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"The maximum difference allowed is %s.\n"
"Please contact your manager to accept the closing difference."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_bill.py:0
#, python-format
msgid "The name of the Coins/Bills must be a number."
msgstr "השם על המטבעות\\שטרות צריך להיות מספר."

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "מספר הזמנות הקופה הקשורות ללקוח זה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "ההזמנה לא נשלחה לשרת עקב תקלה שסיבתה לא ידועה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "The order has been already paid."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr "ההזמנה סונכרנה קודם לכן. אנא מלא את החשבונית מצד השרת עבור ההזמנה:"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"הקופה תציג קטגורית מוצרים זו כברירת מחדל. אם לא מוגדרת קטגוריה כלשהי, כל "
"המוצרים הזמינים יוצגו."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"הקופה תציג רק מוצרים שנמצאים באחד מעצי הקטגוריה שנבחרו. אם לא מוגדרת קטגוריה"
" כלשהי, כל המוצרים הזמינים יוצגו"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured if any."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "שער המטבע למטבע השער החל ביום ההזמנה"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr "דילוג על מסך הקבלה אם ניתן להדפיס את הקבלה באופן אוטומטי."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "הקבלה תודפס אוטומטית בסוף כל הזמנה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo_line__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"מחיר המכירה מנוהל מתבנית המוצר. לחץ על הלחצן 'הגדר וריאנטים' כדי לקבוע את "
"מחירי התכונות הנוספים."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "הלקוח שנבחר צריך כתובת."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr "המחירונים הנבחרים חייבים להיות לא שייכים לשום חברה או לחברה של הקופה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "השרת נתקל בשגיאה במהלך קבלת ההזמנה שלך."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The session has been already closed by another User. All sales completed in "
"the meantime have been saved in a Rescue Session, which can be reviewed "
"anytime and posted to Accounting from Point of Sale's dashboard."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr "המשמרת נפתחה למשך זמן ארוך במיוחד. אנא שקול לסגור אותה."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"העסקאות מעובדות על ידי Adyen. הגדר את אישורי ה- Adyen שלך באמצעי התשלום "
"הקשור."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_paytm
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by PayTM. Set your PayTM credentials on the "
"related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_stripe
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Stripe. Set your Stripe credentials on the"
" related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"העסקאות מעובדות על ידי Vantiv. הגדר את אישורי ה- Vantiv שלך באמצעי התשלום "
"הקשור."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo__base_price
msgid ""
"The value from which pro-rating of the component price is based. This is to "
"ensure that whatever product the user chooses for a component, it will "
"always be they same price."
msgstr ""
"הערך שממנו מבוסס הדירוג היחסי של מחיר הרכיב. זאת כדי להבטיח שכל מוצר שהמשתמש"
" יבחר עבור רכיב, תמיד יהיה אותו מחיר."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "יתרת סגירה תיאורטית"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "אין מוצרים בקטגוריה הזו."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"במשמרת יש עדיין הזמנות במצב טיוטה. שלם או בטל את ההזמנות הבאות לאימות המשמרת:\n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "כבר קיים תשלום אלקטרוני."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"לא מוגדר לוח חשבונות בחברה. אנא עבור להגדרות הנהלת חשבונות להגדרת לוח "
"חשבונות."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"בקופה זו אין אמצעי תשלום במזומן לטיפול בעודף.\n"
"\n"
"אנא שלם את הסכום המדויק או הוסף אמצעי תשלום במזומן בתצורת הקופה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash payment method for this PoS Session"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"This cash payment method is already used in another Point of Sale.\n"
"A new cash payment method should be created for this Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"שדה זה מציין את ההפרש המקסימלי המותר בין היתרה הסופית והמזומן התיאורטי בעת "
"סגירת משמרת, עבור משתמשים שהם לא מנהלים. אם יגיע למקסימום זה, למשתמש תהיה "
"הודעת שגיאה בסיום המשמרת שבה נאמר שהוא צריך ליצור קשר עם המנהל שלו."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"שדה זה נמצא שם כדי להעביר את המזהה של מנהל קבוצת הקופה לקליינט של הקופה."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr "שדה זה שם כדי להעביר את המזהה של קבוצת המשתמשים בקופה לקליינט קופה."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "This invoice has been created from the point of sale session: %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"זה שימושי למסעדות עם שירותים במקום ושירותי טייק אווי שיש להם שיעורי מס "
"מסוימים."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with payment method %s that is being used by "
"order %s in the active pos session %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"פעולה זו תהרוס את כל ההזמנות שטרם שולמו בדפדפן. תאבד את כל הנתונים שלא נשמרו"
" ותצא מהקופה. לא ניתן לבטל פעולה זו."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"פעולה זו תמחק לצמיתות את כל ההזמנות המשולמות מהאחסון המקומי. כל המידע יאבד. "
"לא ניתן לבטל פעולה זו."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "This order is empty"
msgstr "הזמנה זו ריקה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr "ההזמנה הזו לא מסונכרנת עם השרת. וודאו סנכרון ונסו שוב."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This product is used as reference on customer receipts."
msgstr "מוצר זה משמש כמזהה בקבלות לקוח."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"רצף זה נוצר אוטומטית על ידי Odoo אך אתה יכול לשנות אותו כדי להתאים אישית את "
"מספרי המזהים של שורות ההזמנות שלך."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"רצף זה נוצר אוטומטית על ידי Odoo אך אתה יכול לשנות אותו כדי להתאים אישית את "
"מספרי המזהים של ההזמנות שלך."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "משמרת זו כבר סגורה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "מס זה חל על כל מוצר חדש שנוצר בקטלוג."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Those settings are common to all PoS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__ticket_code
msgid "Ticket Code"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Ticket Nr"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Tip"
msgstr "עצה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "סכום הטיפ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tip Product"
msgstr "מוצר טיפ"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tips"
msgstr "טיפים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "טיפים:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "לסגור"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "To Pay"
msgstr "לשלם"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "To Refund:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "לשקול עם משקל"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"To delete a product, make sure all point of sale sessions are closed.\n"
"\n"
"Deleting a product available in a session would be like attempting to snatch a hamburger from a customer’s hand mid-bite; chaos will ensue as ketchup and mayo go flying everywhere!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "לחיוב"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "כדי לרשום הזמנות חדשות, התחל משמרת חדשה."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "להחזרת מוצרים, עליך לפתוח משמרת בקופה %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "סה\"כ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Total (Tax excluded)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "סך עסקאות מזומן"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "עלות כוללת:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "סה\"כ הנחה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Total Due"
msgstr "יתרה לתשלום"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "רווח שולי כולל"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "סה\"כ שולם (עם עיגול)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "סכום התשלומים הכולל"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "מחיר כולל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Price excl. Tax:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "הסכום הכולל של התשלום."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "עלות כוללת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "כמות כוללת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "סה\"כ:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "עסקה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "עסקה בוטלה"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "העברה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Trusted POS"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__trusted_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_trusted_config_ids
msgid "Trusted Point of Sale Configurations"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__type
msgid "Type"
msgstr "סוג"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "סוג הכרטיס שבשימוש"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "UNSELECT"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"לא ניתן לסגור ולאמת את המשמרת.\n"
"אנא הגדר חשבון מס מתאים בכל שורת חלוקה מחדש של המסים הבאים: \n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "לא ניתן להוריד את החשבונית."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid "Unable to parse barcode"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "בעיה בסנכרון הזמנה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Unique Code:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Unique code"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Unit"
msgstr "יחידה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "מחיר יחידה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#, python-format
msgid "Unknown Barcode:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "שגיאה לא ידועה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "Unselect"
msgstr "הסרת בחירה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "סוג קובץ לא נתמך"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Unsupported search operation"
msgstr "פעולת חיפוש לא נתמכת"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Unsynced order"
msgstr "הזמנה לא מסונכרנת"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "יחידת מידה"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "עדכן את כמויות המלאי "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Use QR code on ticket"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "השתמש במסוף תשלום"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_use_pricelist
msgid "Use a pricelist."
msgstr "השתמש במחירון."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr "השתמש בברקוד כדי לסרוק מוצרים, לקוחות, כרטיסים ועוד"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use fiscal positions to get different taxes by order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Used to record product pickings. Products are consumed from its default "
"source location."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "משתמש"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "תוויות משתמש"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__uuid
msgid "Uuid"
msgstr "מזהה ייחודי גלובלי"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Validate"
msgstr "אשר"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (ארה\"ב וקנדה)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "מסוף תשלומים של Vantiv"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Waiting for card"
msgstr "ממתין לכרטיס"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "יחידת מדף קיר"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Warehouse"
msgstr "מחסן"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_warehouse_id
msgid "Warehouse (PoS)"
msgstr "מחסן (קופה)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Weighing"
msgstr "שקילה"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "מוצר משוקלל"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "כאשר כל המוצרים מוכנים"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid ""
"When disabled, only PoS manager can view the margin and cost of product "
"among the Product info."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"בכל פעם שאתה סוגר משמרת, נוצרת רשומה אחת ביומן הנהלת חשבונות שלהלן עבור כל "
"ההזמנות שלא חויבו. חשבוניות נרשמות בהנהלת חשבונות בנפרד."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "לוח מחיק"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "טוש מחיק"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "With a"
msgstr "עם "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Yes"
msgstr "כן"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"אינך רשאי לשנות את תצורת עיגול המזומן בזמן שמשמרת קופה המשתמשת בתצורה זו "
"פתוחה."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "You can go to"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can only unlink PoS order lines that are related to orders in new or "
"cancelled state."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid "You cannot archive '%s' as it is used by a POS configuration '%s'."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"אין אפשרות לסגור את הקופה כאשר החשבוניות לא נרשמו.\n"
"חשבוניות:%s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "אתה לא יכול לסגור את המשמרת כאשר יש הזמנות במצב טיוטה"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "אינך יכול ליצור סשן לפני תאריך הנעילה החשבונאי."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You cannot delete a rounding method that is used in a Point of Sale "
"configuration."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You cannot invoice orders belonging to different companies."
msgstr "אין אפשרות להנפיק חשבוניות עבור הזמנות השייכות לחברות שונות."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "You cannot save an empty order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You cannot share open orders with configuration that does not use the same "
"currency."
msgstr "אין אפשרות לשתף הזמנות פתוחות עם קונפיגורציה שאינה משתמשת באותה מטבע."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You cannot use the same journal on multiples cash payment methods."
msgstr "אין אפשרות להשתמש באותו יומן עבור מספר שיטות תשלום במזומן."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You do not have permission to open a POS session. Please try opening a "
"session with a different user"
msgstr "אין לך הרשאה לפתוח משמרת בקופה . אנא נסה לפתוח משמרת עם משתמש אחר."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"You have selected orderlines from multiple invoiced orders. To proceed "
"refund, please select orderlines from the same invoiced order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"עליך להגדיר מוצר לכל דבר שאתם מוכר דרך\n"
"                ממשק הקופה."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "You must first remove this product from the %s combo"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "עליך לשייך קופה למשמרת שלך."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "You're offline"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Your Order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Your address is missing or incomplete. <br/>\n"
"                                Please make sure to"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Zip"
msgstr "מיקוד"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "available,"
msgstr "זמין,"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "before continuing."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "שייך למשמרת אחרת:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_tree
msgid "combos"
msgstr "שילובים"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "create your own"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "demo data"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "discount"
msgstr "הנחה"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "e.g. Burger Menu"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "לדוג' מזומן"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Company Address, Website"
msgstr "למשל כתובת חברה, אתר אינטרנט "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "לדוג' חנות בת\"א"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "למשל מדיניות החזרת מוצרים, תודה  שקניתם אצלנו!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "למשל משקאות קלים"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "ePos Printer"
msgstr "מדפסת ePos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "fill all relevant information"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "for"
msgstr "ל"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "for an order of"
msgstr "עבור הזמנה של"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "forecasted"
msgstr "תחזית"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "in"
msgstr "ב"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "in this category."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#, python-format
msgid "items"
msgstr "פריטים"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "הערה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "or"
msgstr "או"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "orders:"
msgstr "הזמנות:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "outstanding rescue session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "paid orders"
msgstr "הזמנות ששולמו"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "qx9h1"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "החזרה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the invoice"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the receipt"
msgstr "הקבלה"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "הזמנות שלא שולמו"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "לא ניתן לייבא הזמנות שלא שולמו"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "באמצעות"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "היו כפילויות של הזמנות קיימות"
