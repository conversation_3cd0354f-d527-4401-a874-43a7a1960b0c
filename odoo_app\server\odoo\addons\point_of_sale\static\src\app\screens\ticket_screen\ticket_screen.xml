<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.TicketScreen">
        <div class="ticket-screen screen h-100 bg-100" t-att-class="{ 'd-none': !props.isShown }">
            <div class="screen-full-width d-flex w-100 h-100">
                <div t-if="!ui.isSmall || pos.ticket_screen_mobile_pane === 'left'" class="rightpane pane-border d-flex flex-column flex-grow-1 w-100 h-100 h-lg-100 bg-300 overflow-y-auto">
                    <div class="controls d-flex align-items-center justify-content-between mt-1 mt-lg-0 p-2 bg-400">
                        <t t-if="!ui.isSmall">
                            <div class="buttons d-flex gap-2">
                                <button t-if="!ui.isSmall" class="discard btn btn-lg btn-light" t-on-click="() => this.closeTicketScreen()">
                                    <span class="search-icon">
                                        <i class="fa fa-angle-double-left"/>
                                    </span>
                                    <t t-if="!ui.isSmall">
                                        Back
                                    </t>
                                </button>
                                <button t-if="allowNewOrders" class="highlight btn btn-lg btn-primary" t-on-click="() => this.onCreateNewOrder()">New Order</button>
                            </div>
                        </t>
                        <t t-if="ui.isSmall and allowNewOrders">
                            <button class="highlight btn btn-lg btn-primary" t-on-click="() => this.onCreateNewOrder()">
                                <span class="search-icon">
                                    <i class="fa fa-plus" aria-hidden="true"/>
                                </span>
                            </button>
                        </t>
                        <SearchBar
                            config="getSearchBarConfig()"
                            placeholder="constructor.searchPlaceholder"
                            onSearch.bind="onSearch"
                            onFilterSelected.bind="onFilterSelected" />
                        <div t-if="shouldShowPageControls()" class="item">
                            <div class="page-controls">
                                <div class="previous" t-on-click="() => this.onPrevPage()">
                                    <i class="fa fa-fw fa-caret-left" role="img" aria-label="Previous Order List" title="Previous Order List"></i>
                                </div>
                                <div class="next" t-on-click="() => this.onNextPage()">
                                    <i class="fa fa-fw fa-caret-right" role="img" aria-label="Next Order List" title="Next Order List"></i>
                                </div>
                            </div>
                            <div class="page">
                                <span><t t-esc="getPageNumber()" /></span>
                            </div>
                        </div>
                    </div>
                    <div class="orders overflow-y-auto flex-grow-1">
                        <t t-set="_filteredOrderList" t-value="getFilteredOrderList()" />
                        <t t-if="_filteredOrderList.length !== 0">
                            <div class="header-row d-flex text-bg-700 fw-bolder" t-att-class="{ 'd-none': ui.isSmall }">
                                <div class="col wide p-2">Date</div>
                                <div class="col wide p-2">Receipt Number</div>
                                <div class="col wide p-2">Order number</div>
                                <div class="col p-2">Customer</div>
                                <div class="col wide p-2" t-if="showCardholderName()">Cardholder Name</div>
                                <div class="col p-2">Cashier</div>
                                <div class="col end p-2">Total</div>
                                <div class="col narrow p-2">Status</div>
                                <div class="col very-narrow p-2" name="delete"></div>
                            </div>
                            <t t-if="!ui.isSmall" t-foreach="_filteredOrderList" t-as="order" t-key="order.cid">
                                <div class="order-row" t-att-class="{ 'highlight bg-primary text-white': isHighlighted(order) }"
                                    t-on-click="() => this.onClickOrder(order)" t-on-dblclick="() => order.locked ? ()=>{} : this._setOrder(order)" >
                                    <div class="col wide p-2 ">
                                        <div><t t-esc="getDate(order)"></t></div>
                                    </div>
                                    <div class="col wide p-2">
                                        <div><t t-esc="order.name"></t></div>
                                    </div>
                                    <div class="col wide p-2">
                                        <div><t t-esc="order.trackingNumber"></t></div>
                                    </div>
                                    <div class="col p-2">
                                        <div><t t-esc="getPartner(order)"></t></div>
                                    </div>
                                    <div t-if="showCardholderName()" class="col p-2">
                                        <div><t t-esc="getCardholderName(order)"></t></div>
                                    </div>
                                    <div class="col p-2">
                                        <t t-esc="getCashier(order)"></t>
                                    </div>
                                    <div class="col end p-2">
                                        <div><t t-esc="getTotal(order)"></t></div>
                                    </div>
                                    <div class="col narrow p-2">
                                        <div><t t-esc="getStatus(order)"></t></div>
                                    </div>
                                    <div t-if="!shouldHideDeleteButton(order)" class="col very-narrow delete-button p-2" name="delete" t-on-click.stop="() => this.onDeleteOrder(order)">
                                        <i class="fa fa-trash" aria-hidden="true"/>
                                    </div>
                                    <div t-else="" class="col very-narrow p-2"></div>
                                </div>
                            </t>
                            <t t-if="ui.isSmall" t-foreach="_filteredOrderList" t-as="order" t-key="order.cid">
                                <div class="mobileOrderList order-row" t-att-class="{ 'highlight bg-primary text-white': isHighlighted(order) }"
                                    t-on-click="() => this.onClickOrder(order)" t-on-dblclick="() =>  order.locked ? ()=>{} : this._setOrder(order)" >
                                    <div class="col p-2 d-flex justify-content-between align-items-center">
                                        <div><t t-esc="order.name"></t> / <t t-esc="order.trackingNumber"></t></div>
                                        <div><t t-esc="getTotal(order)"></t></div>
                                    </div>
                                    <div class="col p-2 d-flex justify-content-between align-items-center">
                                        <div><t t-esc="getDate(order)"></t></div>
                                        <div class="orderStatus"><t t-esc="getStatus(order)"></t></div>
                                    </div>
                                    <div class="col p-2">
                                        <div t-if="!shouldHideDeleteButton(order)" class="col very-narrow delete-button p-2 rounded-2" name="delete" t-on-click.stop="() => this.onDeleteOrder(order)">
                                            <i class="fa fa-trash" aria-hidden="true"/> Delete
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </t>
                        <CenteredIcon t-else="" icon="'fa-shopping-cart fa-4x'" text="'No orders found'"/>
                    </div>
                    <div class="switchpane d-flex w-100" t-if="ui.isSmall">
                        <t t-set="_selectedSyncedOrder" t-value="getSelectedOrder()" />
                        <button class="btn-switchpane load-order-button primary btn btn-primary rounded-0 w-50 fw-bolder py-3" t-if="!isOrderSynced" t-on-click="() => this._setOrder(_selectedSyncedOrder)">
                            <span class="fs-1 d-block">Load Order</span>
                        </button>
                        <button class="btn-switchpane flex-fill btn rounded-0 fw-bolder secondary review-button" t-att-class="{'btn-primary': isOrderSynced, 'btn-secondary': !isOrderSynced}" t-on-click="switchPane">
                            <span class="fs-1 d-block">Review</span>
                        </button>
                    </div>
                </div>
                <div class="leftpane d-flex flex-column flex-grow-1 w-100 h-100 h-lg-100 bg-200" t-if="!ui.isSmall || pos.ticket_screen_mobile_pane === 'right'">
                    <t t-set="_selectedSyncedOrder" t-value="getSelectedOrder()" />
                    <t t-set="_selectedOrderlineId" t-value="getSelectedOrderlineId()" />
                    <t t-if="_selectedSyncedOrder?.get_orderlines()?.length" > 
                        <div t-if="isOrderSynced" t-att-class="{ 'highlight text-danger': _state.ui.highlightHeaderNote }" class="text-bg-view py-2 px-3 border-bottom" >
                            Select the product(s) to refund and set the quantity
                        </div>
                        <OrderWidget lines="_selectedSyncedOrder.orderlines" t-slot-scope="scope"
                            total="env.utils.formatCurrency(_selectedSyncedOrder.get_total_with_tax())"
                            tax="!env.utils.floatIsZero(_selectedSyncedOrder.get_total_tax()) and env.utils.formatCurrency(_selectedSyncedOrder.get_total_tax()) or ''">
                            <t t-set="line" t-value="scope.line" />
                            <Orderline line="line.getDisplayData()"
                                class="{'selected': line.id === getSelectedOrderlineId()}"
                                t-on-click="() => this.onClickOrderline(line)">
                                <t t-set="toRefundDetail" t-value="line.pos.toRefundLines[line.id]" />
                                <li t-if="!line.pos.isProductQtyZero(line.refunded_qty)"
                                    class="info refund-note mt-1">
                                    <strong t-esc="env.utils.formatProductQty(line.refunded_qty)" />
                                    <span> Refunded</span>
                                </li>
                                <li t-if="!line.pos.isProductQtyZero(toRefundDetail?.qty)" class="info to-refund-highlight refund-note mt-1 fw-bold text-primary">
                                    <t t-set="_destinationOrderUid" t-value="toRefundDetail.destinationOrderUid"/>
                                    <t t-set="refundQty" t-value="env.utils.formatProductQty(toRefundDetail.qty)"/>
                                    <t t-if="_destinationOrderUid">
                                        Refunding <t t-esc="refundQty" /> in 
                                        <span t-esc="_destinationOrderUid" 
                                            class="order-link ms-1 text-decoration-underline cursor-pointer" 
                                            t-on-click.stop="() => this.onClickRefundOrderUid(_destinationOrderUid)" />
                                    </t>
                                    <t t-else="">
                                        To Refund: <t t-esc="refundQty" />
                                    </t>
                                </li>
                            </Orderline>
                        </OrderWidget>
                        <div class="pads border-top">
                            <t t-if="isOrderSynced">
                                <div class="control-buttons d-flex flex-wrap border-bottom overflow-hidden bg-300">
                                    <InvoiceButton
                                        onInvoiceOrder.bind="onInvoiceOrder"
                                        order="_selectedSyncedOrder" />
                                    <ReprintReceiptButton order="_selectedSyncedOrder" />
                                </div>
                                <div class="subpads d-flex">
                                    <ActionpadWidget
                                        partner="getSelectedPartner()"
                                        actionName="constructor.numpadActionName"
                                        actionType="'refund'"
                                        actionToTrigger.bind="onDoRefund"
                                        isActionButtonHighlighted="getHasItemsToRefund()" />
                                    <Numpad buttons="getNumpadButtons()" class="'max-width-325px'"/>
                                </div>
                            </t>
                            <div t-else="" class="pads border-top" >
                                <button class="button validation load-order-button w-100 btn btn-lg btn-primary rounded-0 fw-bolder py-3" t-on-click="() => this._setOrder(_selectedSyncedOrder)">
                                    <span class="fs-1 d-block">Load Order</span>
                                </button>
                            </div>
                        </div>
                    </t>
                    <CenteredIcon t-else="" icon="'fa-shopping-cart fa-4x'" text="'Select an order'" class="'bg-100'"/>
                </div>
            </div>
        </div>
    </t>

</templates>
