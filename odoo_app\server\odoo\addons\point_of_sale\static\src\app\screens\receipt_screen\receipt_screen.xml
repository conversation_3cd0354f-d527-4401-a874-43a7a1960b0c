<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.ReceiptScreen">
        <div class="receipt-screen screen h-100 bg-100">
            <div class="screen-content d-flex flex-column h-100">
                <div class="top-content d-flex align-items-center py-3 border-bottom text-center" t-if="!ui.isSmall">
                    <div class="top-content-center flex-grow-1">
                        <h1 class="mb-0">
                            <t t-esc="orderAmountPlusTip" />
                        </h1>
                    </div>
                </div>
                <div class="default-view d-flex flex-lg-row flex-column overflow-hidden flex-grow-1">
                    <div class="actions d-flex flex-column justify-content-between flex-lg-grow-1 flex-grow-0 flex-shrink-1 flex-basis-0">
                        <div class="d-flex flex-column m-4">
                            <h1>Payment Successful</h1>
                            <div class="buttons my-3">
                                <button class="button print btn btn-lg btn-secondary w-100 py-3" t-on-click="printReceipt">
                                    <i class="fa fa-print ms-2" t-ref="order-print-receipt-button"></i> Print Receipt
                                </button>
                            </div>
                            <form t-on-submit.prevent="onSendEmail" class="send-email d-flex">
                                <div class="input-email input-group">
                                    <input type="email" class="form-control py-3 bg-view" t-attf-placeholder="Email: {{currentOrder.getEmailItems().join(', ')}}" t-model="orderUiState.inputEmail" />
                                    <button class="send btn px-5" t-att-class="isValidEmail() ? 'highlight btn btn-primary' : 'btn-secondary disabled'" type="submit">
                                        <i class="fa fa-paper-plane" aria-hidden="true" t-ref="order-mail-receipt-button" />
                                    </button>
                                </div>
                            </form>
                            <div class="notice mt-2">
                                <div t-if="orderUiState.emailSuccessful !== null" t-attf-class="{{ orderUiState.emailSuccessful ? 'successful text-success' : 'failed text-danger' }}">
                                    <t t-esc="orderUiState.emailNotice"></t>
                                </div>
                            </div>
                        </div>
                        <t t-set="splittedOrder" t-value="this.currentOrder.originalSplittedOrder and !this.currentOrder.originalSplittedOrder.finalized"/>
                        <div t-if="!ui.isSmall" class="validation-buttons d-flex w-100 gap-1 sticky-bottom">
                            <button class="button next validation btn btn-primary w-100 py-5 rounded-0 fs-2" t-att-class="{ highlight: !locked }" t-if="!splittedOrder" t-on-click="orderDone" name="done">
                                <i class="oi oi-chevron-right" role="img" aria-label="Pay" title="Pay" />
                                New Order
                            </button>
                            <button t-if="isResumeVisible() and !splittedOrder" class="button next validation btn btn-primary w-100 py-5 rounded-0 fs-2" t-att-class="{ highlight: !locked }" t-on-click="resumeOrder" name="resume">
                                <i class="oi oi-chevron-right" role="img" aria-label="Pay" title="Pay" />
                                Resume Order
                            </button>
                            <button t-if="splittedOrder" class="button next validation btn btn-primary w-100 py-5 rounded-0 fs-2" t-att-class="{ highlight: !locked }" t-on-click="continueSplitting" name="resume">
                                <i class="fa fa-chevron-right" role="img" aria-label="Pay" title="Pay" />
                                Continue
                            </button>
                        </div>
                    </div>
                    <div class="pos-receipt-container d-flex flex-grow-1 flex-lg-grow-0 user-select-none justify-content-center bg-200 text-center overflow-hidden">
                        <div class="d-inline-block m-3 p-3 border rounded bg-view text-start overflow-y-auto">
                            <OrderReceipt data="pos.get_order().export_for_printing()" formatCurrency="env.utils.formatCurrency" />
                        </div>
                    </div>
                </div>
                <div t-if="ui.isSmall" class="switchpane d-flex h-12">
                    <div class="btn-switchpane validation-button btn btn-primary flex-fill d-flex justify-content-center align-items-center rounded-0 fw-bolder fs-1" t-att-class="{ highlight: !locked }" t-if="!splittedOrder" t-on-click="orderDone" name="done">
                                New Order
                    </div>
                    <div t-if="isResumeVisible() and !splittedOrder" class="btn-switchpane validation-button btn btn-primary flex-fill d-flex justify-content-center align-items-center rounded-0 fw-bolder fs-1" t-att-class="{ highlight: !locked }" t-on-click="resumeOrder" name="resume">
                                Resume Order
                    </div>
                    <div t-if="splittedOrder" class="btn-switchpane validation-button btn btn-primary flex-fill d-flex justify-content-center align-items-center rounded-0 fw-bolder fs-1" t-att-class="{ highlight: !locked }" t-on-click="continueSplitting" name="resume">
                                Continue
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
