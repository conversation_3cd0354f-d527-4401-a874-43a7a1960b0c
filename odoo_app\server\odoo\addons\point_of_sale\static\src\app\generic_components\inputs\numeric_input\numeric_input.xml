<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.NumericInput">
        <div t-attf-class="{{props.class}} d-flex align-items-center input-group" >
            <button class="btn btn-secondary border" t-on-click="() => this.setValue(this.getValue() - 1)" t-att-disabled="getValue() lte props.min">
                <i class="fa fa-minus center"/>
            </button>
            <input class="o_input form-control border text-center text-bg-light" type="number"
                    t-on-focus="ev=>ev.target.select()"
                    t-att-value="getValue()"
                    t-att-min="props.min"
                    t-on-input="(event) => this.setValue(parseInt(event.target.value))" />
            <button class="btn btn-secondary border" t-on-click="() => this.setValue(this.getValue() + 1)">
                <i class="fa fa-plus"/>
            </button>
        </div>
    </t>
</templates>
