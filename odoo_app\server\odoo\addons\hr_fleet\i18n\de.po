# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_fleet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_form_inherit_hr
msgid ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Employee</span>"
msgstr ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Mitarbeiter</span>"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Vorlage des Aktivitätsplans"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Zuweisung"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_view_list
msgid "Attachments"
msgstr "Dateianhänge"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
#, python-format
msgid "Cannot remove address from employees with linked cars."
msgstr ""
"Die Adresse von Mitarbeitern mit verknüpften Fahrzeugen kann nicht entfernt "
"werden."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__employee_cars_count
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__employee_cars_count
msgid "Cars"
msgstr "Autos"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.hr_departure_wizard_view_form
msgid "Company Car"
msgstr "Firmenwagen"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
msgid "Current Driver"
msgstr "Aktueller Fahrer"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Austrittassistent"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "Driver"
msgstr "Fahrer"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_contract__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_services__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_odometer__driver_employee_id
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_services_view_form_inherit_hr
msgid "Driver (Employee)"
msgstr "Fahrer (Mitarbeiter)"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Fahrerhistorie eines Fahrzeugs"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_search_inherit_hr
msgid "Employee"
msgstr "Mitarbeiter"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
#, python-format
msgid "Employee %s is not linked to a vehicle."
msgstr "Mitarbeiter %s ist mit keinem Fahrzeug verknüpft."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_name
msgid "Employee Name"
msgstr "Name des Mitarbeiters"

#. module: hr_fleet
#: model:ir.model.fields.selection,name:hr_fleet.selection__mail_activity_plan_template__responsible_type__fleet_manager
msgid "Fleet Manager"
msgstr "Fuhrpark-Manager"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
#, python-format
msgid "Fleet Manager is limited to Employee plans."
msgstr "Fuhrparkmanager ist auf Mitarbeiterpläne beschränkt."

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Fleet Mobility Card"
msgstr "Fuhrpark Mobility Card"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__future_driver_employee_id
msgid "Future Driver (Employee)"
msgstr "Zukünftiger Fahrer (Mitarbeiter)"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
#, python-format
msgid "History Employee Cars"
msgstr "Historie der Firmenwagen von Mitarbeitern"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__license_plate
msgid "License Plate"
msgstr "Amtliches Kennzeichen"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__mobility_card
msgid "Mobility Card"
msgstr "Mobility Card"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__attachment_number
msgid "Number of Attachments"
msgstr "Anzahl der Anhänge"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Fahrtenbuch für ein Fahrzeug"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee_public
msgid "Public Employee"
msgstr "Öffentlicher Mitarbeiter"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Related Employee"
msgstr "Verknüpfter Mitarbeiter"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release Company Car"
msgstr "Firmenwagen freigeben"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Fahrzeugservices"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
#, python-format
msgid "The vehicle of employee %(employee)s is not linked to a fleet manager."
msgstr ""
"Das Fahrzeug des Mitarbeiters %(employee)s ist mit keinem Fuhrparkmanager "
"verknüpft."

#. module: hr_fleet
#. odoo-javascript
#: code:addons/hr_fleet/static/src/views/hr_fleet_kanban/hr_fleet_kanban_controller.xml:0
#, python-format
msgid "Upload"
msgstr "Hochladen"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_res_users
msgid "User"
msgstr "Benutzer"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr "Fahrzeug"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Fahrzeugvertrag"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__car_ids
msgid "Vehicles (private)"
msgstr "Fahrzeuge (privat)"
