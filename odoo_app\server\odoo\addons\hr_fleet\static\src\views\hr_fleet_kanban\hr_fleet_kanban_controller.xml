<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="hr_fleet.HrFleetKanbanController.Buttons" t-inherit="web.KanbanView.Buttons" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('o_cp_buttons')]" position="inside">
            <input type="file" multiple="true" t-ref="uploadFileInput" class="o_input_file o_hidden" t-on-change.stop="onInputChange"/>
            <button type="button" t-att-class="'btn ' + (!env.isSmall ? 'btn-primary' : 'btn-secondary')" t-on-click="() => this.uploadFileInput.el.click()">
                Upload
            </button>
        </xpath>
    </t>
</templates>
