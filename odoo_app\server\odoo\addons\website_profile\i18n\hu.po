# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_profile
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# sixsix six, 2023
# <AUTHOR> <EMAIL>, 2023
# 5768b353f27900ae76ad88cc42dfd5b8_3bb349f, 2023
# <PERSON>, 2023
# krn<PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# Val<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Val<PERSON>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"!<br/>\n"
"            Did not receive it?"
msgstr ""
"!<br/>\n"
"            Nem kapta meg?"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "(not verified)"
msgstr "(nem ellenőrzött)"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
". Collect points on the forum or on the eLearning platform. Those points "
"will make you reach new ranks."
msgstr ""
". Gyűjts pontokat a fórumon vagy az e-learning platformon. Ezek a pontok "
"segítenek új rangokat elérni."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid ". Try another search."
msgstr ". Próbálj másik keresést."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"fa fa-arrow-right\"/> Get Badges"
msgstr "<i class=\"fa fa-arrow-right\"/> Jelvények megszerzése"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-close me-1\"/>Cancel"
msgstr "<i class=\"fa fa-close me-1\"/>Mégse"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"Edit\"/>"
msgstr ""
"<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" "
"title=\"Szerkesztés\"/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-1\"/>EDIT"
msgstr "<i class=\"fa fa-pencil me-1\"/>SZERKESZTÉS"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Szerkesztés"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-2\"/>EDIT PROFILE"
msgstr "<i class=\"fa fa-pencil me-2\"/>PROFIL SZERKESZTÉSE"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Badges"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Összes jelvény"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right\"/> All Badges"
msgstr "<i class=\"oi oi-arrow-right\"/> Összes jelvény"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> díjazott felhasználók</i>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Ország...</option>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold me-2\">Current rank:</small>"
msgstr "<small class=\"fw-bold me-2\">Jelenlegi rang:</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Badges</small>"
msgstr "<small class=\"fw-bold\">Jelvények</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Joined</small>"
msgstr "<small class=\"fw-bold\">Csatlakozott</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Biography</span>"
msgstr "<span class=\"fw-bold\">Életrajz</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">City</span>"
msgstr "<span class=\"fw-bold\">Város</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Country</span>"
msgstr "<span class=\"fw-bold\">Ország</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Email</span>"
msgstr "<span class=\"fw-bold\">E-mail</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Name</span>"
msgstr "<span class=\"fw-bold\">Név</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Public Profile</span>"
msgstr "<span class=\"fw-bold\">Nyilvános profil</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Website</span>"
msgstr "<span class=\"fw-bold\">Weboldal</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">Badges</span>"
msgstr "<span class=\"text-muted small fw-bold\">Jelvények</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">XP</span>"
msgstr "<span class=\"text-muted small fw-bold\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">Badges</span>"
msgstr "<span class=\"text-muted\">Jelvények</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">XP</span>"
msgstr "<span class=\"text-muted\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"<span id=\"email_validated_message\">Congratulations! Your email has just "
"been validated.</span>"
msgstr ""
"<span id=\"email_validated_message\">Gratulálunk! Az e-mail címe sikeresen "
"ellenőrizve lett.</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "<strong class=\"mb-3 text-white me-2\">Rank by:</strong>"
msgstr "<strong class=\"mb-3 text-white me-2\">Rangsorolás:</strong>"

#. module: website_profile
#: model:mail.template,body_html:website_profile.validation_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> Profile validation\n"
"                        </span>\n"
"                    </td>\n"
"                    <td t-if=\"not user.company_id.uses_default_logo\" valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hello <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                            You have been invited to validate your email in order to get access to \"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" website.\n"
"                            To validate your email, please click on the following link:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Validate my account\n"
"                                </a>\n"
"                            </div>\n"
"                            Thanks for your participation!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Correct your email address</u>"
msgstr "<u>E-mail cím javítása</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Send Again</u>"
msgstr "<u>Újraküldés</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>here</u>"
msgstr "<u>ide</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "About"
msgstr "Névjegy"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All Users"
msgstr "Minden felhasználó"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All time"
msgstr "Összes idő"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Badges"
msgstr "Jelvények"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "Badges are your collection of achievements. Wear them proudly! <br/>"
msgstr ""
"A jelvények az elért eredményeid gyűjteménye. Viseld őket büszkén! <br/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"                        you receive badges for being especially helpful.<br class=\"d-none d-lg-inline-block\"/>Badges\n"
"                        appear on your profile page, and your posts."
msgstr ""
"A kérdéseiddel és válaszaiddal szerzett hírnév mellett\n"
"                        jelvényeket kapsz, ha különösen segítőkész vagy.<br class=\"d-none d-lg-inline-block\"/>A jelvények\n"
"                        megjelennek a profiloldaladon és a bejegyzéseidnél."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Biography"
msgstr "Életrajz"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__can_publish
msgid "Can Publish"
msgstr "Publikálhat"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Clear"
msgstr "Törlés"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Close"
msgstr "Bezárás"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit"
msgstr "Szerkesztés"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit Profile"
msgstr "Profil szerkesztés"

#. module: website_profile
#: model:mail.template,name:website_profile.validation_email
msgid "Forum: Email Verification"
msgstr "Fórum: E-mail ellenőrzés"

#. module: website_profile
#: model:ir.model,name:website_profile.model_gamification_badge
msgid "Gamification Badge"
msgstr "Játékosítási jelvény"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "Get"
msgstr "Szerezd meg"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Home"
msgstr "Kezdőlap"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I earn badges?"
msgstr "Hogyan szerezhetek jelvényeket?"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I score more points?"
msgstr "Hogyan szerezhetek több pontot?"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__is_published
msgid "Is Published"
msgstr "Közzétett"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Keep learning with"
msgstr "Folytasd a tanulást"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_website__karma_profile_min
msgid "Minimal karma to see other user's profile"
msgstr "Minimális karma más felhasználók profiljának megtekintéséhez"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Mobile sub-nav"
msgstr "Mobil almenü"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "More info"
msgstr "Tivábbi információ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Nav"
msgstr "Navigáció"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Next rank:"
msgstr "Következő rang:"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_main
msgid "No Leaderboard Yet :("
msgstr "Még nincs ranglista :("

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "No badges yet!"
msgstr "Még nincsenek jelvények!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid "No user found for"
msgstr "Nem található felhasználó"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "Not have enough karma to view other users' profile."
msgstr "Nincs elegendő karmád más felhasználók profiljának megtekintéséhez."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr ""
"Kérem adjon meg egy érvényes email címet ahhoz, hogy figyelmeztetéseket "
"kapjon a válaszokról és a hozzászólásokról."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Ranks"
msgstr "Rangok"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_access_denied
msgid "Return to the website."
msgstr "Vissza a weboldalra."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search"
msgstr "Keresés"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search users"
msgstr "Felhasználók keresése"

#. module: website_profile
#: model:mail.template,description:website_profile.validation_email
msgid "Sent to forum visitors to confirm their mail address"
msgstr "Fórum látogatóknak küldve e-mail címük megerősítéséhez"

#. module: website_profile
#: model:ir.model.fields,help:website_profile.field_gamification_badge__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"A teljes elérési út/URL a dokumentum weboldalon keresztüli eléréséhez."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This month"
msgstr "Ebben a hónapban"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "This profile is private!"
msgstr "Ez a profil privát!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This week"
msgstr "Ezen a héten"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "Unpublished"
msgstr "Nem közzétett"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Update"
msgstr "Frissítés"

#. module: website_profile
#: model:ir.model,name:website_profile.model_res_users
msgid "User"
msgstr "Felhasználó"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "User rank"
msgstr "Felhasználói rang"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Users"
msgstr "Felhasználók"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Verification Email sent to"
msgstr "Ellenőrző e-mail elküldve ide"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_published
msgid "Visible on current website"
msgstr "Látható ezen a weboldalon"

#. module: website_profile
#: model:ir.model,name:website_profile.model_website
msgid "Website"
msgstr "Honlap"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_url
msgid "Website URL"
msgstr "Honlap címe"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "When you finish a course or reach milestones, you're awarded badges."
msgstr ""
"Amikor befejezed a kurzust vagy mérföldköveket érsz el, jelvényeket kapsz."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Write a few words about yourself..."
msgstr "Írj néhány szót magadról..."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "XP"
msgstr "XP"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
"You can score more points by answering quizzes at the end of each course "
"content. Points can also be earned on the forum. Follow this link to the "
"guidelines of the forum."
msgstr ""
"Több pontot szerezhetsz, ha kitöltöd a kvízeket minden kurzus tartalom "
"végén. Pontokat szerezhetsz a fórumon is. Kövesd ezt a linket a fórum "
"irányelveihez."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"Your Account has not yet been verified.<br/>\n"
"            Click"
msgstr ""
"Fiókod még nincs ellenőrizve.<br/>\n"
"            Kattints"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Your account does not have an email set up. Please set it up on"
msgstr "Fiókodhoz nincs beállítva e-mail cím. Kérjük, állítsd be itt"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "breadcrumb"
msgstr "navigációs útvonal"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "or"
msgstr "vagy"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "point"
msgstr "pont"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this month"
msgstr "ebben a hónapban"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this week"
msgstr "ez a hét"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "to receive a verification email"
msgstr "hogy ellenőrző e-mailt kapj"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "xp"
msgstr "xp"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"xp\n"
"                        to level up!"
msgstr ""
"xp\n"
"                        a szintlépéshez!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "your account settings"
msgstr "fiókbeállításaid"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"
msgstr ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"

#. module: website_profile
#: model:mail.template,subject:website_profile.validation_email
msgid "{{ object.company_id.name }} Profile validation"
msgstr "{{ object.company_id.name }} Profil érvényesítés"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "└ Users"
msgstr "└ Felhasználók"
