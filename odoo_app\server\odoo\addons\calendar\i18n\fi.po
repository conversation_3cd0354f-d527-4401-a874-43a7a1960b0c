# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# <PERSON><PERSON> <jenni.heik<PERSON><PERSON>@sv-oy.fi>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>rant<PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <kari.l<PERSON>@emsystems.fi>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_count
msgid "# Meetings"
msgstr "Tapaamisten lkm"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"Alkaen %(date_start)s klo %(time_start)s\n"
"Päättyen  %(date_end)s klo %(time_end)s (%(timezone)s)"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "%(day)s klo (%(start)s päättyen %(end)s) (%(timezone)s)"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has accepted the invitation"
msgstr "%s on hyväksynyt kutsun"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has declined the invitation"
msgstr "%s on kieltäytynyt kutsusta"

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br><br>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                The date of your appointment with <t t-out=\"customer.name or ''\">Jesse Brown</t> has been updated.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment has been updated.\n"
"            </t>\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            The date of your appointment with <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> has been updated.\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\"></strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            The date of the meeting has been updated.\n"
"            The meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"\n"
"   <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"   <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"   <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"   <p>\n"
"        Hei <t t-out=\"object.common_name or ''\">Mat Ready</t>,<br><br>\n"
"       <t t-if=\"is_online and target_responsible\">\n"
"           <t t-if=\"customer\">\n"
"               <t t-out=\"customer.name or ''\">Jesse Brownin</t> tapaamispäiväsi on päivitetty.\n"
"           </t>\n"
"           <t t-else=\"\">\n"
"                Ajanvarauksesi on päivitetty.\n"
"           </t>\n"
"            Tapaaminen <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> on nyt ajoitettu seuraavalle päivälle\n"
"           <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 klo (11:00:00 - 11:30:00) (Eurooppa/Bryssel)</t>\n"
"       </t>\n"
"       <t t-elif=\"is_online and target_customer\">\n"
"           <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diazin</t> tapaamisen päivämäärä on päivitetty.\n"
"            Tapaaminen <strong t-out=\"object.event_id.appointment_type_id.name or ''\"></strong> on nyt ajoitettu seuraavalle päivälle\n"
"           <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 klo (11:00:00 To 11:30:00) (Eurooppa/Bryssel)</t>.\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"            Tapaamisen päivämäärä on päivitetty.\n"
"            Kokouksen <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> on nyt tarkoitus olla\n"
"           <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 klo (11:00:00-11:30:00) (Eurooppa/Bryssel)</t>.\n"
"       </t>\n"
"   </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Hyväksy</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Hylkää</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Näytä</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tiistai</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>Toukokuu 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                           <t t-out=\"object.mail_tz or ''\">(Eurooppa/Bryssel</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Tapahtuman yksityiskohdat</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Sijainti: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                       <a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">(Näytä kartta</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>Milloin: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Joka 1 viikko, 3 tapahtumaa</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Kesto: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Osallistujat\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">Sinä</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        Miten liittyä:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Liity Odoo Discussin avulla</t>\n"
"                        <t t-else=\"\"> Liity osoitteessa</t><br>\n"
"                       <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Kuvaus tapahtumasta:\n"
"                   <t t-out=\"object.event_id.description\">Sisäinen kokous, jossa keskustellaan tuotteiden ja palveluiden uudesta hinnoittelusta.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"   <br>\n"
"    Kiitos,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> invited you for the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Your meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> has been booked.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_update
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">Tuesday</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">May 2021</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 AM</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Europe/Brussels</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"></td>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Details of the event</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Description:\n"
"                            <t t-out=\"object.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                How to Join:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                                <t t-else=\"\"> Join at</t><br>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">View Map</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duration:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0H30</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr>\n"
"        <p placeholder=\"Enter your message here\"><br></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">Tiistai</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">Toukokuuta 2021</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 AM</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Eurooppa/Bryssel</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"></td>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Tapahtuman yksityiskohdat</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Kuvaus:\n"
"                           <t t-out=\"object.description\">Sisäinen kokous, jossa keskustellaan tuotteiden ja palveluiden uudesta hinnoittelusta.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                Miten osallistua:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Liity Odoo Discussin kautta</t>\n"
"                                <t t-else=\"\"> Liity osoitteessa</t><br>\n"
"                               <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Sijainti: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                               <a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">(Näytä kartta</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>Milloin: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Joka 1 viikko, 3 tapahtumaa</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Kesto:\n"
"                               <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0H30</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr>\n"
"        <p placeholder=\"Enter your message here\"><br></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br><br>\n"
"        This is a reminder for the below event:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"   <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"   <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"   <p>\n"
"        Hei <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br><br>\n"
"        Tämä on muistutus alla olevasta tapahtumasta:\n"
"   </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Hyväksy</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Hylkää</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Näytä</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tiistai</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>Toukokuu 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                           <t t-out=\"object.mail_tz or ''\">(Eurooppa/Bryssel</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Tapahtuman yksityiskohdat</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Sijainti: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                       <a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">(Näytä kartta</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>Milloin: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Joka 1 viikko, 3 tapahtumaa</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Kesto: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Osallistujat\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">Sinä</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        Miten liittyä:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Liity Odoo Discussin avulla</t>\n"
"                        <t t-else=\"\"> Liity osoitteessa</t><br>\n"
"                       <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Kuvaus tapahtumasta:\n"
"                   <t t-out=\"object.event_id.description\">Sisäinen kokous, jossa keskustellaan tuotteiden ja palveluiden uudesta hinnoittelusta.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"   <br>\n"
"    Kiitos,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-plus\"/> <span>Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/> <span>Odoo-kokous</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span class=\"fa fa-plus\"/><span> Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/><span> Odoo-kokous</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-times\"/><span> Clear meeting</span>"
msgstr "<span class=\"fa fa-times\"/><span> Tyhjennä kokous</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">Repeat on</span>"
msgstr ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">Toista</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Google Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">Google-kalenteri</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Outlook Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">Outlook-kalenteri</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"oi oi-arrow-right\"/><span> Join video call</span>"
msgstr "<span class=\"oi oi-arrow-right\"/><span> Liity videopuheluun</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span invisible=\"allday\" style=\"white-space: pre;\"> or </span>"
msgstr "<span invisible=\"allday\" style=\"white-space: pre;\"> tai </span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> Attendees</span>"
msgstr "<span> Osallistujat</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span> tuntia</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Tallenna</strong> sivu ja palaa takaisin, jotta voit määrittää "
"ominaisuuden."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid ""
"<strong>The following attendees have invalid email addresses and won't "
"receive any email notifications:</strong>"
msgstr ""
"<strong>Seuraavien osallistujien sähköpostiosoitteet ovat virheellisiä, "
"eivätkä he saa sähköposti-ilmoituksia:</strong>"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_filters_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "Käyttäjällä ei voi olla samaa yhteystietoa kahdesti."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Accept"
msgstr "Hyväksy"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__accepted_count
msgid "Accepted Count"
msgstr "Hyväksytty määrä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action"
msgstr "Toiminto"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Toiminnot voivat laukaista tietyn toiminnan, kuten kalenterinäkymän "
"avaamisen tai automaattisesti valmiiksi merkinnän, kun asiakirja on ladattu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__active
msgid "Active"
msgstr "Aktiivinen"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "Toimenpiteet"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "Toimenpide"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Activity Mixin"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "Toimenpidetyyppi"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Toiminta-aikataulun suunnittelun ohjattu toiminto"

#. module: calendar
#: model:onboarding.onboarding.step,button_text:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Add"
msgstr "Lisää"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add attendees..."
msgstr "Lisää osallistujia..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Add description"
msgstr "Lisää kuvaus"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add title"
msgstr "Lisää otsikko"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__body
msgid "Additional Message"
msgstr "Lisäviesti"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__body
msgid ""
"Additional message that would be sent with the notification for the reminder"
msgstr "Lisäviesti, joka lähetettäisiin muistutuksena"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_menu_patch.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "Koko päivä"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "All Day, %(day)s"
msgstr "Koko päivä, %(day)s"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
#, python-format
msgid "All events"
msgstr "Kaikki tapahtumat"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Oletko varma että haluat poistaa tämän tietueen?"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Attendee"
msgstr "Osallistuja"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Attendees"
msgstr "Osallistujat"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendees_count
msgid "Attendees Count"
msgstr "Osallistujien määrä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_status
msgid "Attending?"
msgstr "Osallistuuko?"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Available"
msgstr "Saatavilla"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Available/Busy"
msgstr "Käytettävissä / varattu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__awaiting_count
msgid "Awaiting Count"
msgstr "Odottaa laskua"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "Perustapahtuma"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
#, python-format
msgid "Busy"
msgstr "Varattu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "Päivittäin"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_event_menu
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_users_view_form
msgid "Calendar"
msgstr "Kalenteri"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "Kalenterihälytys"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Osallistujan tiedot kalenterissa"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__record
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "Kalenteritapahtuma"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_filters
msgid "Calendar Filters"
msgstr "Kalenterin suodattimet"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "Kalenterikutsu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "Kalenteritapahtuma"

#. module: calendar
#: model:onboarding.onboarding,name:calendar.onboarding_onboarding_calendar
msgid "Calendar Onboarding"
msgstr "Kalenterin käyttöönotto"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_popover_delete_wizard
msgid "Calendar Popover Delete Wizard"
msgstr "Kalenterin ponnahdusikkuna poistamisen ohjattu toiminto"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_provider_config
msgid "Calendar Provider Configuration Wizard"
msgstr "Ohjattu kalenteripalveluntarjoajan määritys"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Calendar Settings"
msgstr "Kalenteriasetukset"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_changedate
msgid "Calendar: Date Updated"
msgstr "Kalenteri: Päivä päivitetty"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
msgid "Calendar: Event Reminder"
msgstr "Kalenteri: Tapahtuman muistutus"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_update
msgid "Calendar: Event Update"
msgstr "Kalenteri: Tapahtuman päivitys"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_invitation
msgid "Calendar: Meeting Invitation"
msgstr "Kalenteri: Kokouskutsu"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_reminder
msgid "Calendar: Reminder"
msgstr "Kalenteri: Muistutus"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Cancel"
msgstr "Peruuta"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__is_organizer_alone
msgid ""
"Check if the organizer is alone in the event, i.e. if the organizer is the only one that hasn't declined\n"
"        the event (only if the organizer is not the only attendee)"
msgstr ""
"Tarkista, onko järjestäjä yksin tapahtumassa, eli onko järjestäjä ainoa, joka ei ole kieltäytynyt\n"
"        tapahtumasta (vain jos järjestäjä ei ole ainoa osallistuja)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_checked
msgid "Checked"
msgstr "Valittu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__external_calendar_provider
msgid "Choose an external calendar to configure"
msgstr "Valitse ulkoinen kalenteri ja määritä se"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr ""
"Valitse, mitä tehdään muille tapahtumille, jotka ovat toistumisen aikaan. "
"Kaikkien tapahtumien päivittäminen ei ole sallittua, kun päivämääriä tai "
"kellonaikaa muutetaan"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client ID"
msgstr "Asiakkaan tunniste/ID"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client Secret"
msgstr "Client Secret"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__color
msgid "Color"
msgstr "Väri"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "Yleinen nimi"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_menu_config
msgid "Configuration"
msgstr "Asetukset"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
#, python-format
msgid "Confirm"
msgstr "Vahvista"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.xml:0
#: code:addons/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.xml:0
#, python-format
msgid "Connect"
msgstr "Yhdistä"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.js:0
#: model:ir.actions.act_window,name:calendar.action_view_start_calendar_sync
#, python-format
msgid "Connect your Calendar"
msgstr "Yhdistä kalenterisi"

#. module: calendar
#: model:onboarding.onboarding.step,title:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Connect your calendar"
msgstr "Yhdistä kalenterisi"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Contact Attendees"
msgstr "Ole yhteydessä osallistujiin"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "Määrä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "Luotu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_attendee
msgid "Current Attendee"
msgstr "Nykyinen osallistuja"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__custom
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__custom
msgid "Custom"
msgstr "Mukautettu"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__daily
msgid "Daily"
msgstr "Päivittäin"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "Päivämäärä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "Kuukauden päivä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "Päivä"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "Kuukauden päivä"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "Kuukauden päivä"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "Päivää"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Decline"
msgstr "Hylkää"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__declined_count
msgid "Declined Count"
msgstr "Hylättyjen määrä"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__delete
#, python-format
msgid "Delete"
msgstr "Poista"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Delete Event"
msgstr "Poista tapahtuma"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__all
msgid "Delete all the events"
msgstr "Poista kaikki tapahtumat"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__next
msgid "Delete this and following events"
msgstr "Poista tämä ja seuraavat tapahtumat"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__one
msgid "Delete this event"
msgstr "Poista tämä tapahtuma"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Describe your meeting"
msgstr "Kuvaile kokousta"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "Kuvaus"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Details"
msgstr "Tiedot"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__discuss
msgid "Discuss"
msgstr "Viestintä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_channel_id
msgid "Discuss Channel"
msgstr "Keskustelukanava"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_description
msgid "Display Description"
msgstr "Näytettävä kuvaus"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "Dokumentin ID"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "Dokumentin malli"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "Asiakirjamallin nimi"

#. module: calendar
#: model:onboarding.onboarding.step,done_text:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Done!"
msgstr "Valmista!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "Dtstart"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "Kesto"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "Kesto minuuteissa"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "EMAIL"
msgstr "SÄHKÖPOSTI"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
#, python-format
msgid "Edit Recurrent event"
msgstr "Muokkaa toistuvaa tapahtumaa"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "Muokkaa toistuvaa tapahtumaa"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "Sähköposti"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "Sähköposti - 3 tuntia"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "Sähköposti - 6 tuntia"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__mail_template_id
msgid "Email Template"
msgstr "Sähköpostin mallipohja"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_id
msgid "Employee"
msgstr "Työntekijä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "Päättymispäivä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "Lopputyyppi"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "Päättymispäivä"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "Tapahtuman hälytys"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "Hälytysten hallinta"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "Tapahtumakokouksen tyyppi"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Tapahtuman toistuvuussääntö"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "Tapahtuman aika"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Days"
msgstr "Joka %(interval)s päivä"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Days for %(count)s events"
msgstr "Joka %(interval)s päivä %(count)s tapahtumaa varten"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Days until %(until)s"
msgstr "Joka %(interval)s päivä, kunnes %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months day %(day)s"
msgstr "Joka %(interval)s kuukausi päivä %(day)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months day %(day)s for %(count)s events"
msgstr "Joka %(interval)s kuukauden päivä %(day)s %(count)s tapahtuman osalta"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months day %(day)s until %(until)s"
msgstr "Joka %(interval)s kuukausi päivä %(day)s kunnes %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months on the %(position)s %(weekday)s"
msgstr "Joka %(interval)s kuukausi %(position)s %(weekday)s %(weekday)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s for %(count)s "
"events"
msgstr ""
"Joka %(interval)s kuukausi %(position)s %(weekday)s %(count)s -tapahtumissa"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s until %(until)s"
msgstr "Joka %(interval)s kuukausi %(position)s %(weekday)s kunnes %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Weeks on %(days)s"
msgstr "Joka %(interval)s viikko %(days)s:llä]"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Weeks on %(days)s for %(count)s events"
msgstr "Joka %(interval)s viikko %(days)s ja %(count)s tapahtuman välillä"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Weeks on %(days)s until %(until)s"
msgstr "Joka %(interval)s viikko %(days)s, kunnes %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Years"
msgstr "Joka %(interval)s vuosi"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Years for %(count)s events"
msgstr "Joka %(interval)s vuosi %(count)s tapahtumaa varten"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Years until %(until)s"
msgstr "Joka %(interval)s vuosi, kunnes %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/mail_activity.py:0
#, python-format
msgid "Feedback: %(feedback)s"
msgstr "Palaute: %(feedback)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "Ensimmäinen"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "Ensin sinun pitää määritellä kutsulle päivä."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "Seuraa toistumista"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "Ikuisesti"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "Neljäs"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Free"
msgstr "Ilmainen"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fri
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fri
msgid "Fri"
msgstr "Pe"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fri
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fri
msgid "Friday"
msgstr "Perjantai"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__google
msgid "Google"
msgstr "Google"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Google Calendar"
msgstr "Google-kalenteri"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Google Calendar icon"
msgstr "Google-kalenterin kuvake"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_id
msgid "Google Client_id"
msgstr "Google Client_id"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_secret
msgid "Google Client_key"
msgstr "Google Client_key"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "Googlen synkronointi keskeytetty"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "Tunnit"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__id
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__id
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
msgid "ID"
msgstr "ID"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Jos aktiivinen-kentän arvoksi asetetaan false, voit piilottaa "
"tapahtumahälytystiedot poistamatta niitä."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__show_as
msgid ""
"If the time is shown as 'busy', this event will be visible to other people with either the full         information or simply 'busy' written depending on its privacy. Use this option to let other people know         that you are unavailable during that period of time. \n"
" If the event is shown as 'free', other users know         that you are available during that period of time."
msgstr ""
"Jos kellonaika on \"varattu\", tapahtuma näkyy muille henkilöille joko täydellisinä tietoina tai vain \"varattu\" -merkinnällä, riippuen sen yksityisyydestä. Käytä tätä vaihtoehtoa, jos haluat ilmoittaa muille ihmisille, että et ole tavoitettavissa kyseisenä aikana.\n"
" Jos tapahtuma näkyy merkinnällä \"vapaa\", muut käyttäjät tietävät, että olet käytettävissä kyseisenä aikana."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "Aikaväli"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__invalid_email_partner_ids
msgid "Invalid Email Partner"
msgstr "Virheellinen partnerin sähköpostiosoite"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "Kutsu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
#: model:ir.model.fields,field_description:calendar.field_calendar_event__access_token
msgid "Invitation Token"
msgstr "Kutsuavain"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "Kutsun yksityiskohdat"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_invitation
msgid "Invitation email to new attendees"
msgstr "Kutsusähköposti uusille osallistujille"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "Kutsu"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to {{ object.event_id.name }}"
msgstr "Kutsu {{ object.event_id.name }}"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "Kutsut"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "Onko tapahtuma korostettu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_organizer_alone
msgid "Is the Organizer Alone"
msgstr "Onko järjestäjä yksin"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Join Video Call"
msgstr "Liity videopuheluun"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "Viimeinen"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "Peruskalenterin viimeinen ilmoitus on merkitty luetuksi"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type_ui
msgid "Let the event automatically repeat at that interval"
msgstr "Anna tapahtuman toistua säännöllisin väliajoin"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "Sijainti"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "Logo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__mail_tz
msgid "Mail Tz"
msgstr "Sähköpostin aikavyöhyke"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
#, python-format
msgid "Maybe"
msgstr "Ehkä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__user_id
msgid "Me"
msgstr "Minä"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "Tapaaminen"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'"
msgstr ""
"Kokous '%(name)s' alkaa '%(start_datetime)s' ja päättyy '%(end_datetime)s'"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "Tapaamisen aihe"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "Tapaamistyypit"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_location
msgid "Meeting URL"
msgstr "Kokouksen URL-osoite"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "Tapaaminen linkitetty"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
#: model_terms:ir.ui.view,arch_db:calendar.view_partners_form
msgid "Meetings"
msgstr "Tapaamiset"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Microsoft Outlook icon"
msgstr "Microsoft Outlookin kuvake"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "Minuutit"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_name
msgid "Model Description"
msgstr "Mallin kuvaus"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mon
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mon
msgid "Mon"
msgstr "Ma"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mon
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mon
msgid "Monday"
msgstr "Maanantai"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "Kuukausittain"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__monthly
msgid "Monthly"
msgstr "Kuukausittain"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "Kuukaudet"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/calendar_form/calendar_quick_create.xml:0
#, python-format
msgid "More Options"
msgstr "Lisää valintoja"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "Omat tapaamiset"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "Nimi"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
msgid "Needs Action"
msgstr "Vaatii toimia"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
#, python-format
msgid "New"
msgstr "Uusi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_mixin__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_partner__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_users__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Seuraavan toimenpiteen kalenterimerkintä"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
#, python-format
msgid "No"
msgstr "Ei"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "Ei, en osallistu"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "Ei vielä palautetta"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "No meetings found. Let's schedule one!"
msgstr "Kokouksia ei löytynyt. Järjestetään yksi!"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "Ilmoitus"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "Ilmoitus - 1 päivä"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "Ilmoitus - 1 tunti"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "Ilmoitus - 15 minuuttia"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "Ilmoitus - 2 tuntia"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "Ilmoitus - 30 minuuttia"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "Ilmoitus tapaamisesta lähetetty kaikille osanottajille."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Number of Repetitions"
msgstr "Toistojen määrä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "Toistojen määrä"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: calendar
#: model:ir.model,name:calendar.model_onboarding_onboarding
msgid "Onboarding"
msgstr "Perehdytys"

#. module: calendar
#: model:onboarding.onboarding.step,step_image_alt:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Onboarding Calendar Synchronization"
msgstr "Kalenterin synkronoinnin käyttöönotto"

#. module: calendar
#: model:ir.model,name:calendar.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Perehdytyksen askel"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Online Meeting"
msgstr "Online-kokous"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Only Internal Users"
msgstr "Vain sisäiset käyttäjät"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
msgid "Only internal users"
msgstr "Näytä vain sisäisille käyttäjille"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "Avaa kalenteri"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "Valinta"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
msgid "Organizer"
msgstr "Järjestäjä"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__microsoft
msgid "Outlook"
msgstr "Outlook"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "Outlook-kalenteri"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "Outlook asiakasohjelman Id"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "Outlook asiakasohjelman salaisuus"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_sync_paused
msgid "Outlook Synchronization Paused"
msgstr "Outlookin synkronointi keskeytetty"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "Osallistuja"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "Käyttäjän kumppanitiedot"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__privacy
msgid "People to whom this event will be visible."
msgstr "Henkilöt, joille tämä tapahtuma näkyy."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__phone
msgid "Phone"
msgstr "Puhelin"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "Yksityisyys"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Private"
msgstr "Yksityinen"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Public"
msgstr "Julkinen"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Read More"
msgstr "Lue lisää"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "Toistuvuus"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "Toistuvuussääntö"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "Toiston päättö"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "Uusiutumisen päivitys"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Recurrent"
msgstr "Toistuva"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "Toistosääntä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "Muistuta ennen"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:calendar.calendar_submenu_reminders
msgid "Reminders"
msgstr "Muistutukset"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type_ui
msgid "Repeat"
msgstr "Toista"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat On"
msgstr "Toista"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "Toista kunnes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Repeat every"
msgstr "Toista joka"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "Toista joka (päivä/viikko/kuukausi/vuosi)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "Toista x kertaa"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_patch.xml:0
#, python-format
msgid "Reschedule"
msgstr "Ajoita uudelleen"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr "Rrule"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "Säännön tyyppi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sat
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sat
msgid "Sat"
msgstr "La"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sat
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sat
msgid "Saturday"
msgstr "Lauantai"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Scheduled by"
msgstr "Aikatauluttanut"

#. module: calendar
#. odoo-python
#: code:addons/calendar/wizard/mail_activity_schedule.py:0
#, python-format
msgid ""
"Scheduling an activity using the calendar is not possible on more than one "
"record."
msgstr ""
"Toiminnan ajoittaminen kalenterin avulla ei ole mahdollista useammassa kuin "
"yhdessä tietueessa."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "Hae tapaamiset"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "Sekunti"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "Valitse osallistujat..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Email to attendees"
msgstr "Lähetä sähköpostia osallistujille"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Invitations"
msgstr "Lähetä kutsut"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Send Mail"
msgstr "Lähetä sähköposti"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_reminder
msgid "Sent to all attendees if a reminder is set"
msgstr "Lähetetään kaikille osallistujille, jos muistutus on asetettu"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_changedate
msgid "Sent to all attendees if the schedule change"
msgstr "Lähetetään kaikille osallistujille, jos aikataulu muuttuu"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.calendar_settings_action
#: model:ir.ui.menu,name:calendar.menu_calendar_settings
msgid "Settings"
msgstr "Asetukset"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__should_show_status
msgid "Should Show Status"
msgstr "Pitäisi näyttää tila"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show as"
msgstr "Näytä muodossa"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Snooze"
msgstr "Torkuta"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "Aloita"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "Alkupäivä"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr "Tapahtuman alkupäivä, kokopäivän tapahtumille ilman aikaa."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "Tila"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "Tila:"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "Lopeta"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr "Tapahtuman loppupäivä, kokopäivän tapahtumille ilman aikaa."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "Aihe"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Submit"
msgstr "Tallenna"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sun
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sun
msgid "Sun"
msgstr "Su"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sun
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sun
msgid "Sunday"
msgstr "Sunnuntai"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "Synkronoi kalenterisi Google Calendarin kanssa"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "Synkronoi kalenterisi Outlookin kanssa"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists!"
msgstr "Tunniste on jo olemassa!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "Tunnisteet"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__mail_template_id
msgid "Template used to render mail reminder content."
msgstr "Malli, jota käytetään sähköpostimuistutuksen sisällön esittämiseen."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "Alustava"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tentative_count
msgid "Tentative Count"
msgstr "Alustava laskenta"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr "."

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"Kalenteri jaetaan työntekijöiden kesken ja se on täysin integroitu osaksi\n"
"            muihin sovelluksiin, kuten työntekijän lehdet tai yrityksen\n"
"            mahdollisuuksia."

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "Päivän on oltava 1:n ja 31:n välillä"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr ""
"Päättymispäivä ja -aika eivät voi olla aikaisempi kuin alkamispäivä ja "
"-aika."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr "Päättymispäivä ei voi olla aikaisempi kuin alkamispäivä."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The interval cannot be negative."
msgstr "Väli ei voi olla negatiivinen."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The number of repetitions cannot be negative."
msgstr "Toistojen määrä ei voi olla negatiivinen."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "There are no attendees on these events"
msgstr "Näissä tapahtumissa ei ole osallistujia"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "Kolmas"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
#, python-format
msgid "This and following events"
msgstr "Tämä ja seuraavat tapahtumat"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
#, python-format
msgid "This event"
msgstr "Tämä tapahtuma"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__thu
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__thu
msgid "Thu"
msgstr "To"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__thu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__thu
msgid "Thursday"
msgstr "Torstai"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "Aikavyöhyke"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__mail_tz
msgid "Timezone used for displaying time in the mail template"
msgstr "Aikavyöhyke, jota käytetään ajan näyttämiseen sähköpostimallissa"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/res_users.py:0
#, python-format
msgid "Today's Meetings"
msgstr "Tämän päivän kokoukset"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__trigger_id
msgid "Trigger"
msgstr "Liipaisin"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tue
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tue
msgid "Tue"
msgstr "Ti"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tue
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tue
msgid "Tuesday"
msgstr "Tiistai"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "Tyyppi"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Unable to save the recurrence with \"This Event\""
msgstr "Toistuvuutta ei voida tallentaa \"Tämä tapahtuma\" -asetuksella"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Uncertain"
msgstr "Epävarma"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "Yksikkö"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "Kunnes"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_update
msgid "Used to manually notifiy attendees"
msgstr "Käytetään osallistujien manuaaliseen ilmoittamiseen"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "User"
msgstr "Käyttäjä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_can_edit
msgid "User Can Edit"
msgstr "Käyttäjä voi muokata"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "Videopuhelun lähde"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Videocall URL"
msgstr "Videopuhelun URL-osoite"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#, python-format
msgid "View"
msgstr "Näytä"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__wed
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__wed
msgid "Wed"
msgstr "Ke"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__wed
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__wed
msgid "Wednesday"
msgstr "Keskiviikko"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "Arkipäivä"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__weekly
msgid "Weekly"
msgstr "Viikottainen"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "Viikot"

#. module: calendar
#: model:onboarding.onboarding.step,description:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "With Outlook or Google"
msgstr "Outlookin tai Googlen kanssa"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__yearly
msgid "Yearly"
msgstr "Vuosittainen"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "Vuotta"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
#, python-format
msgid "Yes"
msgstr "Kyllä"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "Kyllä, osallistun"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "You can't update a recurrence without base event."
msgstr "Et voi päivittää toistuvuutta ilman perustapahtumaa."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "Et voi kopioida kalenterin mukaista osallistujaa"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "You have to choose at least one day in the week"
msgstr "Sinun on valittava vähintään yksi päivä viikossa"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_renderer.xml:0
#, python-format
msgid "You're alone in this meeting"
msgstr "Olet yksin tässä kokouksessa"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "accepted"
msgstr "hyväksytty"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "attendees"
msgstr "osallistujat"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "awaiting"
msgstr "odottaa"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "declined"
msgstr "hylätty"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "esim. lounastapaaminen"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g: 12/31/2023"
msgstr "esim: 12/31/2023"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "maybe,"
msgstr "ehkä,"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "no,"
msgstr "ei,"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee.xml:0
#, python-format
msgid "props.placeholder"
msgstr "props.placeholder"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "uncertain"
msgstr "epävarma"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "yes,"
msgstr "kyllä,"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "{{ object.event_id.name }} - Reminder"
msgstr "{{ object.event_id.name }} - Muistutus"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "{{ object.event_id.name }}: Date updated"
msgstr "{{ object.event_id.name }}: Päivitetty päivämäärä"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_update
msgid "{{object.name}}: Event update"
msgstr "{{object.name}}: Tapahtuman päivitys"
