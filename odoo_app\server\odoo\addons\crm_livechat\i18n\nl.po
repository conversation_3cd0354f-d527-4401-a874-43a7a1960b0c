# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_livechat
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s's New Lead"
msgstr "%s's <PERSON><PERSON><PERSON> lead"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Chatbot-script"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Chatbot-scriptstap"

#. module: crm_livechat
#: model:ir.model.fields.selection,name:crm_livechat.selection__chatbot_script_step__step_type__create_lead
msgid "Create Lead"
msgstr "Een lead maken"

#. module: crm_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
#: code:addons/crm_livechat/static/src/core/channel_commands.js:0
#, python-format
msgid "Create a new lead (/lead lead title)"
msgstr "Maak een nieuwe lead (/lead lead titel)"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
#, python-format
msgid "Created a new lead: %s"
msgstr "Nieuwe lead aangemaakt: %s"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "Chatkanaal"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script__lead_count
msgid "Generated Lead Count"
msgstr "Aantal gegenereerde leads"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_welcome
msgid "Hi there, what brings you to our website today? 👋"
msgstr "Hallo, wat brengt je naar onze website vandaag? 👋"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "Hu-ho, het lijkt erop dat geen van onze operatoren beschikbaar is 🙁"

#. module: crm_livechat
#: model:chatbot.script,title:crm_livechat.chatbot_script_lead_generation_bot
msgid "Lead Generation Bot"
msgstr "Leadgeneratiebot"

#. module: crm_livechat
#: model_terms:ir.ui.view,arch_db:crm_livechat.chatbot_script_view_form
msgid "Leads"
msgstr "Leads"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__crm_team_id
msgid "Sales Team"
msgstr "Verkoopteam"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Staptype"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_just_looking
msgid "Thank you, you should hear back from us very soon!"
msgstr "Dank je, je hoort snel van ons terug!"

#. module: crm_livechat
#: model:ir.model.fields,help:crm_livechat.field_chatbot_script_step__crm_team_id
msgid ""
"Used in combination with 'create_lead' step type in order to automatically "
"assign the created lead/opportunity to the defined team"
msgstr ""
"Gebruikt in combinatie met het staptype 'create_lead' om de gecreëerde "
"lead/verkoopkans automatisch toe te wijzen aan het gedefinieerde team"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""
"Zou je je e-mailadres willen achterlaten zodat we je terug kunnen bereiken?"
