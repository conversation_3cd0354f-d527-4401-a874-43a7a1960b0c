<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tkinter.messagebox — Tkinter message prompts" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tkinter.messagebox.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tkinter/messagebox.py The tkinter.messagebox module provides a template base class as well as a variety of convenience methods for commonly used configurations. The message boxes a..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tkinter/messagebox.py The tkinter.messagebox module provides a template base class as well as a variety of convenience methods for commonly used configurations. The message boxes a..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tkinter.messagebox — Tkinter message prompts &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tkinter.scrolledtext — Scrolled Text Widget" href="tkinter.scrolledtext.html" />
    <link rel="prev" title="Tkinter Dialogs" href="dialog.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tkinter.messagebox.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="dialog.html"
                          title="previous chapter">Tkinter Dialogs</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.scrolledtext.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.scrolledtext</span></code> — Scrolled Text Widget</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.messagebox.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.scrolledtext.html" title="tkinter.scrolledtext — Scrolled Text Widget"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="dialog.html" title="Tkinter Dialogs"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" accesskey="U">Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.messagebox</span></code> — Tkinter message prompts</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tkinter.messagebox">
<span id="tkinter-messagebox-tkinter-message-prompts"></span><h1><a class="reference internal" href="#module-tkinter.messagebox" title="tkinter.messagebox: Various types of alert dialogs (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.messagebox</span></code></a> — Tkinter message prompts<a class="headerlink" href="#module-tkinter.messagebox" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tkinter/messagebox.py">Lib/tkinter/messagebox.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-tkinter.messagebox" title="tkinter.messagebox: Various types of alert dialogs (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.messagebox</span></code></a> module provides a template base class as well as
a variety of convenience methods for commonly used configurations. The message
boxes are modal and will return a subset of (<code class="docutils literal notranslate"><span class="pre">True</span></code>, <code class="docutils literal notranslate"><span class="pre">False</span></code>, <code class="docutils literal notranslate"><span class="pre">None</span></code>,
<a class="reference internal" href="#tkinter.messagebox.OK" title="tkinter.messagebox.OK"><code class="xref py py-data docutils literal notranslate"><span class="pre">OK</span></code></a>, <a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>, <a class="reference internal" href="#tkinter.messagebox.YES" title="tkinter.messagebox.YES"><code class="xref py py-data docutils literal notranslate"><span class="pre">YES</span></code></a>, <a class="reference internal" href="#tkinter.messagebox.NO" title="tkinter.messagebox.NO"><code class="xref py py-data docutils literal notranslate"><span class="pre">NO</span></code></a>) based on
the user’s selection. Common message box styles and layouts include but are not
limited to:</p>
<figure class="align-default">
<img alt="../_images/tk_msg.png" src="../_images/tk_msg.png" />
</figure>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.messagebox.Message">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">Message</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">master</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.Message" title="Link to this definition">¶</a></dt>
<dd><p>Create a message window with an application-specified message, an icon
and a set of buttons.
Each of the buttons in the message window is identified by a unique symbolic name (see the <em>type</em> options).</p>
<p>The following options are supported:</p>
<blockquote>
<div><dl class="simple">
<dt><em>command</em></dt><dd><p>Specifies the function to invoke when the user closes the dialog.
The name of the button clicked by the user to close the dialog is
passed as argument.
This is only available on macOS.</p>
</dd>
<dt><em>default</em></dt><dd><p>Gives the <a class="reference internal" href="#messagebox-buttons"><span class="std std-ref">symbolic name</span></a> of the default button
for this message window (<a class="reference internal" href="#tkinter.messagebox.OK" title="tkinter.messagebox.OK"><code class="xref py py-data docutils literal notranslate"><span class="pre">OK</span></code></a>, <a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>, and so on).
If this option is not specified, the first button in the dialog will
be made the default.</p>
</dd>
<dt><em>detail</em></dt><dd><p>Specifies an auxiliary message to the main message given by the
<em>message</em> option.
The message detail will be presented beneath the main message and,
where supported by the OS, in a less emphasized font than the main
message.</p>
</dd>
<dt><em>icon</em></dt><dd><p>Specifies an <a class="reference internal" href="#messagebox-icons"><span class="std std-ref">icon</span></a> to display.
If this option is not specified, then the <a class="reference internal" href="#tkinter.messagebox.INFO" title="tkinter.messagebox.INFO"><code class="xref py py-data docutils literal notranslate"><span class="pre">INFO</span></code></a> icon will be
displayed.</p>
</dd>
<dt><em>message</em></dt><dd><p>Specifies the message to display in this message box.
The default value is an empty string.</p>
</dd>
<dt><em>parent</em></dt><dd><p>Makes the specified window the logical parent of the message box.
The message box is displayed on top of its parent window.</p>
</dd>
<dt><em>title</em></dt><dd><p>Specifies a string to display as the title of the message box.
This option is ignored on macOS, where platform guidelines forbid
the use of a title on this kind of dialog.</p>
</dd>
<dt><em>type</em></dt><dd><p>Arranges for a <a class="reference internal" href="#messagebox-types"><span class="std std-ref">predefined set of buttons</span></a>
to be displayed.</p>
</dd>
</dl>
</div></blockquote>
<dl class="py method">
<dt class="sig sig-object py" id="tkinter.messagebox.Message.show">
<span class="sig-name descname"><span class="pre">show</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.Message.show" title="Link to this definition">¶</a></dt>
<dd><p>Display a message window and wait for the user to select one of the buttons. Then return the symbolic name of the selected button.
Keyword arguments can override options specified in the constructor.</p>
</dd></dl>

</dd></dl>

<p><strong>Information message box</strong></p>
<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.showinfo">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">showinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.showinfo" title="Link to this definition">¶</a></dt>
<dd><p>Creates and displays an information message box with the specified title
and message.</p>
</dd></dl>

<p><strong>Warning message boxes</strong></p>
<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.showwarning">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">showwarning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.showwarning" title="Link to this definition">¶</a></dt>
<dd><p>Creates and displays a warning message box with the specified title
and message.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.showerror">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">showerror</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.showerror" title="Link to this definition">¶</a></dt>
<dd><p>Creates and displays an error message box with the specified title
and message.</p>
</dd></dl>

<p><strong>Question message boxes</strong></p>
<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.askquestion">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">askquestion</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">YESNO</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.askquestion" title="Link to this definition">¶</a></dt>
<dd><p>Ask a question. By default shows buttons <a class="reference internal" href="#tkinter.messagebox.YES" title="tkinter.messagebox.YES"><code class="xref py py-data docutils literal notranslate"><span class="pre">YES</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.NO" title="tkinter.messagebox.NO"><code class="xref py py-data docutils literal notranslate"><span class="pre">NO</span></code></a>.
Returns the symbolic name of the selected button.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.askokcancel">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">askokcancel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.askokcancel" title="Link to this definition">¶</a></dt>
<dd><p>Ask if operation should proceed. Shows buttons <a class="reference internal" href="#tkinter.messagebox.OK" title="tkinter.messagebox.OK"><code class="xref py py-data docutils literal notranslate"><span class="pre">OK</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>.
Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the answer is ok and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.askretrycancel">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">askretrycancel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.askretrycancel" title="Link to this definition">¶</a></dt>
<dd><p>Ask if operation should be retried. Shows buttons <a class="reference internal" href="#tkinter.messagebox.RETRY" title="tkinter.messagebox.RETRY"><code class="xref py py-data docutils literal notranslate"><span class="pre">RETRY</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>.
Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the answer is yes and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.askyesno">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">askyesno</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.askyesno" title="Link to this definition">¶</a></dt>
<dd><p>Ask a question. Shows buttons <a class="reference internal" href="#tkinter.messagebox.YES" title="tkinter.messagebox.YES"><code class="xref py py-data docutils literal notranslate"><span class="pre">YES</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.NO" title="tkinter.messagebox.NO"><code class="xref py py-data docutils literal notranslate"><span class="pre">NO</span></code></a>.
Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the answer is yes and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.messagebox.askyesnocancel">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">askyesnocancel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.messagebox.askyesnocancel" title="Link to this definition">¶</a></dt>
<dd><p>Ask a question. Shows buttons <a class="reference internal" href="#tkinter.messagebox.YES" title="tkinter.messagebox.YES"><code class="xref py py-data docutils literal notranslate"><span class="pre">YES</span></code></a>, <a class="reference internal" href="#tkinter.messagebox.NO" title="tkinter.messagebox.NO"><code class="xref py py-data docutils literal notranslate"><span class="pre">NO</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>.
Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the answer is yes, <code class="docutils literal notranslate"><span class="pre">None</span></code> if cancelled, and <code class="docutils literal notranslate"><span class="pre">False</span></code>
otherwise.</p>
</dd></dl>

<p id="messagebox-buttons">Symbolic names of buttons:</p>
<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.ABORT">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">ABORT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'abort'</span></em><a class="headerlink" href="#tkinter.messagebox.ABORT" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.RETRY">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">RETRY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'retry'</span></em><a class="headerlink" href="#tkinter.messagebox.RETRY" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.IGNORE">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">IGNORE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'ignore'</span></em><a class="headerlink" href="#tkinter.messagebox.IGNORE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.OK">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">OK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'ok'</span></em><a class="headerlink" href="#tkinter.messagebox.OK" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.CANCEL">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">CANCEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'cancel'</span></em><a class="headerlink" href="#tkinter.messagebox.CANCEL" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.YES">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">YES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'yes'</span></em><a class="headerlink" href="#tkinter.messagebox.YES" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.NO">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">NO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'no'</span></em><a class="headerlink" href="#tkinter.messagebox.NO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p id="messagebox-types">Predefined sets of buttons:</p>
<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.ABORTRETRYIGNORE">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">ABORTRETRYIGNORE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'abortretryignore'</span></em><a class="headerlink" href="#tkinter.messagebox.ABORTRETRYIGNORE" title="Link to this definition">¶</a></dt>
<dd><p>Displays three buttons whose symbolic names are <a class="reference internal" href="#tkinter.messagebox.ABORT" title="tkinter.messagebox.ABORT"><code class="xref py py-data docutils literal notranslate"><span class="pre">ABORT</span></code></a>,
<a class="reference internal" href="#tkinter.messagebox.RETRY" title="tkinter.messagebox.RETRY"><code class="xref py py-data docutils literal notranslate"><span class="pre">RETRY</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.IGNORE" title="tkinter.messagebox.IGNORE"><code class="xref py py-data docutils literal notranslate"><span class="pre">IGNORE</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">OK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'ok'</span></em></dt>
<dd><p>Displays one button whose symbolic name is <a class="reference internal" href="#tkinter.messagebox.OK" title="tkinter.messagebox.OK"><code class="xref py py-data docutils literal notranslate"><span class="pre">OK</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.OKCANCEL">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">OKCANCEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'okcancel'</span></em><a class="headerlink" href="#tkinter.messagebox.OKCANCEL" title="Link to this definition">¶</a></dt>
<dd><p>Displays two buttons whose symbolic names are <a class="reference internal" href="#tkinter.messagebox.OK" title="tkinter.messagebox.OK"><code class="xref py py-data docutils literal notranslate"><span class="pre">OK</span></code></a> and
<a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.RETRYCANCEL">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">RETRYCANCEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'retrycancel'</span></em><a class="headerlink" href="#tkinter.messagebox.RETRYCANCEL" title="Link to this definition">¶</a></dt>
<dd><p>Displays two buttons whose symbolic names are <a class="reference internal" href="#tkinter.messagebox.RETRY" title="tkinter.messagebox.RETRY"><code class="xref py py-data docutils literal notranslate"><span class="pre">RETRY</span></code></a> and
<a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.YESNO">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">YESNO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'yesno'</span></em><a class="headerlink" href="#tkinter.messagebox.YESNO" title="Link to this definition">¶</a></dt>
<dd><p>Displays two buttons whose symbolic names are <a class="reference internal" href="#tkinter.messagebox.YES" title="tkinter.messagebox.YES"><code class="xref py py-data docutils literal notranslate"><span class="pre">YES</span></code></a> and
<a class="reference internal" href="#tkinter.messagebox.NO" title="tkinter.messagebox.NO"><code class="xref py py-data docutils literal notranslate"><span class="pre">NO</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.YESNOCANCEL">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">YESNOCANCEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'yesnocancel'</span></em><a class="headerlink" href="#tkinter.messagebox.YESNOCANCEL" title="Link to this definition">¶</a></dt>
<dd><p>Displays three buttons whose symbolic names are <a class="reference internal" href="#tkinter.messagebox.YES" title="tkinter.messagebox.YES"><code class="xref py py-data docutils literal notranslate"><span class="pre">YES</span></code></a>,
<a class="reference internal" href="#tkinter.messagebox.NO" title="tkinter.messagebox.NO"><code class="xref py py-data docutils literal notranslate"><span class="pre">NO</span></code></a> and <a class="reference internal" href="#tkinter.messagebox.CANCEL" title="tkinter.messagebox.CANCEL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CANCEL</span></code></a>.</p>
</dd></dl>

<p id="messagebox-icons">Icon images:</p>
<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.ERROR">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'error'</span></em><a class="headerlink" href="#tkinter.messagebox.ERROR" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.INFO">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">INFO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'info'</span></em><a class="headerlink" href="#tkinter.messagebox.INFO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.QUESTION">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">QUESTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'question'</span></em><a class="headerlink" href="#tkinter.messagebox.QUESTION" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.messagebox.WARNING">
<span class="sig-prename descclassname"><span class="pre">tkinter.messagebox.</span></span><span class="sig-name descname"><span class="pre">WARNING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'warning'</span></em><a class="headerlink" href="#tkinter.messagebox.WARNING" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="dialog.html"
                          title="previous chapter">Tkinter Dialogs</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.scrolledtext.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.scrolledtext</span></code> — Scrolled Text Widget</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.messagebox.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.scrolledtext.html" title="tkinter.scrolledtext — Scrolled Text Widget"
             >next</a> |</li>
        <li class="right" >
          <a href="dialog.html" title="Tkinter Dialogs"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" >Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.messagebox</span></code> — Tkinter message prompts</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>