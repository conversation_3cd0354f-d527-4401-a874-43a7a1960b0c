<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Installing Python Modules" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/installing/index.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Email, <EMAIL>,. As a popular open source development project, Python has an active supporting community of contributors and users that also make their software available for other..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Email, <EMAIL>,. As a popular open source development project, Python has an active supporting community of contributors and users that also make their software available for other..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Installing Python Modules &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Python HOWTOs" href="../howto/index.html" />
    <link rel="prev" title="API and ABI Versioning" href="../c-api/apiabiversion.html" />
    <link rel="canonical" href="https://docs.python.org/3/installing/index.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Installing Python Modules</a><ul>
<li><a class="reference internal" href="#key-terms">Key terms</a></li>
<li><a class="reference internal" href="#basic-usage">Basic usage</a></li>
<li><a class="reference internal" href="#how-do-i">How do I …?</a><ul>
<li><a class="reference internal" href="#install-pip-in-versions-of-python-prior-to-python-3-4">… install <code class="docutils literal notranslate"><span class="pre">pip</span></code> in versions of Python prior to Python 3.4?</a></li>
<li><a class="reference internal" href="#install-packages-just-for-the-current-user">… install packages just for the current user?</a></li>
<li><a class="reference internal" href="#install-scientific-python-packages">… install scientific Python packages?</a></li>
<li><a class="reference internal" href="#work-with-multiple-versions-of-python-installed-in-parallel">… work with multiple versions of Python installed in parallel?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#common-installation-issues">Common installation issues</a><ul>
<li><a class="reference internal" href="#installing-into-the-system-python-on-linux">Installing into the system Python on Linux</a></li>
<li><a class="reference internal" href="#pip-not-installed">Pip not installed</a></li>
<li><a class="reference internal" href="#installing-binary-extensions">Installing binary extensions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../c-api/apiabiversion.html"
                          title="previous chapter">API and ABI Versioning</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../howto/index.html"
                          title="next chapter">Python HOWTOs</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/installing/index.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="../howto/index.html" title="Python HOWTOs"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../c-api/apiabiversion.html" title="API and ABI Versioning"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

        <li class="nav-item nav-item-this"><a href="">Installing Python Modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="installing-python-modules">
<span id="installing-index"></span><h1>Installing Python Modules<a class="headerlink" href="#installing-python-modules" title="Link to this heading">¶</a></h1>
<dl class="field-list simple">
<dt class="field-odd">Email<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="mailto:distutils-sig&#37;&#52;&#48;python&#46;org">distutils-sig<span>&#64;</span>python<span>&#46;</span>org</a></p>
</dd>
</dl>
<p>As a popular open source development project, Python has an active
supporting community of contributors and users that also make their software
available for other Python developers to use under open source license terms.</p>
<p>This allows Python users to share and collaborate effectively, benefiting
from the solutions others have already created to common (and sometimes
even rare!) problems, as well as potentially contributing their own
solutions to the common pool.</p>
<p>This guide covers the installation part of the process. For a guide to
creating and sharing your own Python projects, refer to the
<a class="reference external" href="https://packaging.python.org/en/latest/tutorials/packaging-projects/">Python packaging user guide</a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For corporate and other institutional users, be aware that many
organisations have their own policies around using and contributing to
open source software. Please take such policies into account when making
use of the distribution and installation tools provided with Python.</p>
</div>
<section id="key-terms">
<h2>Key terms<a class="headerlink" href="#key-terms" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pip</span></code> is the preferred installer program. Starting with Python 3.4, it
is included by default with the Python binary installers.</p></li>
<li><p>A <em>virtual environment</em> is a semi-isolated Python environment that allows
packages to be installed for use by a particular application, rather than
being installed system wide.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">venv</span></code> is the standard tool for creating virtual environments, and has
been part of Python since Python 3.3. Starting with Python 3.4, it
defaults to installing <code class="docutils literal notranslate"><span class="pre">pip</span></code> into all created virtual environments.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">virtualenv</span></code> is a third party alternative (and predecessor) to
<code class="docutils literal notranslate"><span class="pre">venv</span></code>. It allows virtual environments to be used on versions of
Python prior to 3.4, which either don’t provide <code class="docutils literal notranslate"><span class="pre">venv</span></code> at all, or
aren’t able to automatically install <code class="docutils literal notranslate"><span class="pre">pip</span></code> into created environments.</p></li>
<li><p>The <a class="reference external" href="https://pypi.org">Python Package Index</a> is a public
repository of open source licensed packages made available for use by
other Python users.</p></li>
<li><p>the <a class="reference external" href="https://www.pypa.io/">Python Packaging Authority</a> is the group of
developers and documentation authors responsible for the maintenance and
evolution of the standard packaging tools and the associated metadata and
file format standards. They maintain a variety of tools, documentation,
and issue trackers on <a class="reference external" href="https://github.com/pypa">GitHub</a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">distutils</span></code> is the original build and distribution system first added to
the Python standard library in 1998. While direct use of <code class="docutils literal notranslate"><span class="pre">distutils</span></code> is
being phased out, it still laid the foundation for the current packaging
and distribution infrastructure, and it not only remains part of the
standard library, but its name lives on in other ways (such as the name
of the mailing list used to coordinate Python packaging standards
development).</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The use of <code class="docutils literal notranslate"><span class="pre">venv</span></code> is now recommended for creating virtual environments.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/installing/#creating-virtual-environments">Python Packaging User Guide: Creating and using virtual environments</a></p>
</div>
</section>
<section id="basic-usage">
<h2>Basic usage<a class="headerlink" href="#basic-usage" title="Link to this heading">¶</a></h2>
<p>The standard packaging tools are all designed to be used from the command
line.</p>
<p>The following command will install the latest version of a module and its
dependencies from the Python Package Index:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>python -m pip install SomePackage
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For POSIX users (including macOS and Linux users), the examples in
this guide assume the use of a <a class="reference internal" href="../glossary.html#term-virtual-environment"><span class="xref std std-term">virtual environment</span></a>.</p>
<p>For Windows users, the examples in this guide assume that the option to
adjust the system PATH environment variable was selected when installing
Python.</p>
</div>
<p>It’s also possible to specify an exact or minimum version directly on the
command line. When using comparator operators such as <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;</span></code> or some other
special character which get interpreted by shell, the package name and the
version should be enclosed within double quotes:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>python -m pip install SomePackage==1.0.4    # specific version
python -m pip install &quot;SomePackage&gt;=1.0.4&quot;  # minimum version
</pre></div>
</div>
<p>Normally, if a suitable module is already installed, attempting to install
it again will have no effect. Upgrading existing modules must be requested
explicitly:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>python -m pip install --upgrade SomePackage
</pre></div>
</div>
<p>More information and resources regarding <code class="docutils literal notranslate"><span class="pre">pip</span></code> and its capabilities can be
found in the <a class="reference external" href="https://packaging.python.org">Python Packaging User Guide</a>.</p>
<p>Creation of virtual environments is done through the <a class="reference internal" href="../library/venv.html#module-venv" title="venv: Creation of virtual environments."><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code></a> module.
Installing packages into an active virtual environment uses the commands shown
above.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/installing/">Python Packaging User Guide: Installing Python Distribution Packages</a></p>
</div>
</section>
<section id="how-do-i">
<h2>How do I …?<a class="headerlink" href="#how-do-i" title="Link to this heading">¶</a></h2>
<p>These are quick answers or links for some common tasks.</p>
<section id="install-pip-in-versions-of-python-prior-to-python-3-4">
<h3>… install <code class="docutils literal notranslate"><span class="pre">pip</span></code> in versions of Python prior to Python 3.4?<a class="headerlink" href="#install-pip-in-versions-of-python-prior-to-python-3-4" title="Link to this heading">¶</a></h3>
<p>Python only started bundling <code class="docutils literal notranslate"><span class="pre">pip</span></code> with Python 3.4. For earlier versions,
<code class="docutils literal notranslate"><span class="pre">pip</span></code> needs to be “bootstrapped” as described in the Python Packaging
User Guide.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/installing/#requirements-for-installing-packages">Python Packaging User Guide: Requirements for Installing Packages</a></p>
</div>
</section>
<section id="install-packages-just-for-the-current-user">
<h3>… install packages just for the current user?<a class="headerlink" href="#install-packages-just-for-the-current-user" title="Link to this heading">¶</a></h3>
<p>Passing the <code class="docutils literal notranslate"><span class="pre">--user</span></code> option to <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">pip</span> <span class="pre">install</span></code> will install a
package just for the current user, rather than for all users of the system.</p>
</section>
<section id="install-scientific-python-packages">
<h3>… install scientific Python packages?<a class="headerlink" href="#install-scientific-python-packages" title="Link to this heading">¶</a></h3>
<p>A number of scientific Python packages have complex binary dependencies, and
aren’t currently easy to install using <code class="docutils literal notranslate"><span class="pre">pip</span></code> directly. At this point in
time, it will often be easier for users to install these packages by
<a class="reference external" href="https://packaging.python.org/science/">other means</a>
rather than attempting to install them with <code class="docutils literal notranslate"><span class="pre">pip</span></code>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/science/">Python Packaging User Guide: Installing Scientific Packages</a></p>
</div>
</section>
<section id="work-with-multiple-versions-of-python-installed-in-parallel">
<h3>… work with multiple versions of Python installed in parallel?<a class="headerlink" href="#work-with-multiple-versions-of-python-installed-in-parallel" title="Link to this heading">¶</a></h3>
<p>On Linux, macOS, and other POSIX systems, use the versioned Python commands
in combination with the <code class="docutils literal notranslate"><span class="pre">-m</span></code> switch to run the appropriate copy of
<code class="docutils literal notranslate"><span class="pre">pip</span></code>:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>python2   -m pip install SomePackage  # default Python 2
python2.7 -m pip install SomePackage  # specifically Python 2.7
python3   -m pip install SomePackage  # default Python 3
python3.4 -m pip install SomePackage  # specifically Python 3.4
</pre></div>
</div>
<p>Appropriately versioned <code class="docutils literal notranslate"><span class="pre">pip</span></code> commands may also be available.</p>
<p>On Windows, use the <code class="docutils literal notranslate"><span class="pre">py</span></code> Python launcher in combination with the <code class="docutils literal notranslate"><span class="pre">-m</span></code>
switch:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>py -2   -m pip install SomePackage  # default Python 2
py -2.7 -m pip install SomePackage  # specifically Python 2.7
py -3   -m pip install SomePackage  # default Python 3
py -3.4 -m pip install SomePackage  # specifically Python 3.4
</pre></div>
</div>
</section>
</section>
<section id="common-installation-issues">
<h2>Common installation issues<a class="headerlink" href="#common-installation-issues" title="Link to this heading">¶</a></h2>
<section id="installing-into-the-system-python-on-linux">
<h3>Installing into the system Python on Linux<a class="headerlink" href="#installing-into-the-system-python-on-linux" title="Link to this heading">¶</a></h3>
<p>On Linux systems, a Python installation will typically be included as part
of the distribution. Installing into this Python installation requires
root access to the system, and may interfere with the operation of the
system package manager and other components of the system if a component
is unexpectedly upgraded using <code class="docutils literal notranslate"><span class="pre">pip</span></code>.</p>
<p>On such systems, it is often better to use a virtual environment or a
per-user installation when installing packages with <code class="docutils literal notranslate"><span class="pre">pip</span></code>.</p>
</section>
<section id="pip-not-installed">
<h3>Pip not installed<a class="headerlink" href="#pip-not-installed" title="Link to this heading">¶</a></h3>
<p>It is possible that <code class="docutils literal notranslate"><span class="pre">pip</span></code> does not get installed by default. One potential fix is:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>python -m ensurepip --default-pip
</pre></div>
</div>
<p>There are also additional resources for <a class="reference external" href="https://packaging.python.org/en/latest/tutorials/installing-packages/#ensure-pip-setuptools-and-wheel-are-up-to-date">installing pip.</a></p>
</section>
<section id="installing-binary-extensions">
<h3>Installing binary extensions<a class="headerlink" href="#installing-binary-extensions" title="Link to this heading">¶</a></h3>
<p>Python has typically relied heavily on source based distribution, with end
users being expected to compile extension modules from source as part of
the installation process.</p>
<p>With the introduction of support for the binary <code class="docutils literal notranslate"><span class="pre">wheel</span></code> format, and the
ability to publish wheels for at least Windows and macOS through the
Python Package Index, this problem is expected to diminish over time,
as users are more regularly able to install pre-built extensions rather
than needing to build them themselves.</p>
<p>Some of the solutions for installing <a class="reference external" href="https://packaging.python.org/science/">scientific software</a>
that are not yet available as pre-built <code class="docutils literal notranslate"><span class="pre">wheel</span></code> files may also help with
obtaining other binary extensions without needing to build them locally.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/extensions/">Python Packaging User Guide: Binary Extensions</a></p>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Installing Python Modules</a><ul>
<li><a class="reference internal" href="#key-terms">Key terms</a></li>
<li><a class="reference internal" href="#basic-usage">Basic usage</a></li>
<li><a class="reference internal" href="#how-do-i">How do I …?</a><ul>
<li><a class="reference internal" href="#install-pip-in-versions-of-python-prior-to-python-3-4">… install <code class="docutils literal notranslate"><span class="pre">pip</span></code> in versions of Python prior to Python 3.4?</a></li>
<li><a class="reference internal" href="#install-packages-just-for-the-current-user">… install packages just for the current user?</a></li>
<li><a class="reference internal" href="#install-scientific-python-packages">… install scientific Python packages?</a></li>
<li><a class="reference internal" href="#work-with-multiple-versions-of-python-installed-in-parallel">… work with multiple versions of Python installed in parallel?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#common-installation-issues">Common installation issues</a><ul>
<li><a class="reference internal" href="#installing-into-the-system-python-on-linux">Installing into the system Python on Linux</a></li>
<li><a class="reference internal" href="#pip-not-installed">Pip not installed</a></li>
<li><a class="reference internal" href="#installing-binary-extensions">Installing binary extensions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../c-api/apiabiversion.html"
                          title="previous chapter">API and ABI Versioning</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../howto/index.html"
                          title="next chapter">Python HOWTOs</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/installing/index.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="../howto/index.html" title="Python HOWTOs"
             >next</a> |</li>
        <li class="right" >
          <a href="../c-api/apiabiversion.html" title="API and ABI Versioning"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

        <li class="nav-item nav-item-this"><a href="">Installing Python Modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>