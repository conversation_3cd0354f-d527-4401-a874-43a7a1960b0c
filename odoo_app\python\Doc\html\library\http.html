<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="http — HTTP modules" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/http.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/http/__init__.py http is a package that collects several modules for working with the HyperText Transfer Protocol: http.client is a low-level HTTP protocol client; for high-level U..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/http/__init__.py http is a package that collects several modules for working with the HyperText Transfer Protocol: http.client is a low-level HTTP protocol client; for high-level U..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>http — HTTP modules &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="http.client — HTTP protocol client" href="http.client.html" />
    <link rel="prev" title="urllib.robotparser — Parser for robots.txt" href="urllib.robotparser.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/http.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a><ul>
<li><a class="reference internal" href="#http-status-codes">HTTP status codes</a></li>
<li><a class="reference internal" href="#http-status-category">HTTP status category</a></li>
<li><a class="reference internal" href="#http-methods">HTTP methods</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="urllib.robotparser.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.robotparser</span></code> —  Parser for robots.txt</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="http.client.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="http.client.html" title="http.client — HTTP protocol client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="urllib.robotparser.html" title="urllib.robotparser — Parser for robots.txt"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-http">
<span id="http-http-modules"></span><h1><a class="reference internal" href="#module-http" title="http: HTTP status codes and messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code></a> — HTTP modules<a class="headerlink" href="#module-http" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/http/__init__.py">Lib/http/__init__.py</a></p>
<hr class="docutils" id="index-0" />
<p><a class="reference internal" href="#module-http" title="http: HTTP status codes and messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code></a> is a package that collects several modules for working with the
HyperText Transfer Protocol:</p>
<ul class="simple">
<li><p><a class="reference internal" href="http.client.html#module-http.client" title="http.client: HTTP and HTTPS protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code></a> is a low-level HTTP protocol client; for high-level URL
opening use <a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a></p></li>
<li><p><a class="reference internal" href="http.server.html#module-http.server" title="http.server: HTTP server and request handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code></a> contains basic HTTP server classes based on <a class="reference internal" href="socketserver.html#module-socketserver" title="socketserver: A framework for network servers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code></a></p></li>
<li><p><a class="reference internal" href="http.cookies.html#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a> has utilities for implementing state management with cookies</p></li>
<li><p><a class="reference internal" href="http.cookiejar.html#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> provides persistence of cookies</p></li>
</ul>
<p>The <a class="reference internal" href="#module-http" title="http: HTTP status codes and messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code></a> module also defines the following enums that help you work with http related code:</p>
<dl class="py class">
<dt class="sig sig-object py" id="http.HTTPStatus">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.</span></span><span class="sig-name descname"><span class="pre">HTTPStatus</span></span><a class="headerlink" href="#http.HTTPStatus" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>A subclass of <a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntEnum</span></code></a> that defines a set of HTTP status codes,
reason phrases and long descriptions written in English.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">http</span> <span class="kn">import</span> <span class="n">HTTPStatus</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span>
<span class="go">HTTPStatus.OK</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span> <span class="o">==</span> <span class="mi">200</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span><span class="o">.</span><span class="n">value</span>
<span class="go">200</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span><span class="o">.</span><span class="n">phrase</span>
<span class="go">&#39;OK&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span><span class="o">.</span><span class="n">description</span>
<span class="go">&#39;Request fulfilled, document follows&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">HTTPStatus</span><span class="p">)</span>
<span class="go">[HTTPStatus.CONTINUE, HTTPStatus.SWITCHING_PROTOCOLS, ...]</span>
</pre></div>
</div>
</dd></dl>

<section id="http-status-codes">
<span id="id1"></span><h2>HTTP status codes<a class="headerlink" href="#http-status-codes" title="Link to this heading">¶</a></h2>
<p>Supported,
<a class="reference external" href="https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml">IANA-registered status codes</a>
available in <a class="reference internal" href="#http.HTTPStatus" title="http.HTTPStatus"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.HTTPStatus</span></code></a> are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Code</p></th>
<th class="head"><p>Enum Name</p></th>
<th class="head"><p>Details</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">100</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CONTINUE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.2.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">101</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SWITCHING_PROTOCOLS</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.2.2</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">102</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PROCESSING</span></code></p></td>
<td><p>WebDAV <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2518.html"><strong>RFC 2518</strong></a>, Section 10.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">103</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">EARLY_HINTS</span></code></p></td>
<td><p>An HTTP Status Code for Indicating Hints <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc8297.html"><strong>RFC 8297</strong></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">200</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">OK</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.3.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">201</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CREATED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.3.2</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">202</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ACCEPTED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.3.3</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">203</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NON_AUTHORITATIVE_INFORMATION</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.3.4</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">204</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NO_CONTENT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.3.5</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">205</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">RESET_CONTENT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.3.6</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">206</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PARTIAL_CONTENT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-11"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7233.html"><strong>RFC 7233</strong></a>, Section 4.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">207</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MULTI_STATUS</span></code></p></td>
<td><p>WebDAV <span class="target" id="index-12"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4918.html"><strong>RFC 4918</strong></a>, Section 11.1</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">208</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ALREADY_REPORTED</span></code></p></td>
<td><p>WebDAV Binding Extensions <span class="target" id="index-13"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5842.html"><strong>RFC 5842</strong></a>, Section 7.1 (Experimental)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">226</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">IM_USED</span></code></p></td>
<td><p>Delta Encoding in HTTP <span class="target" id="index-14"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3229.html"><strong>RFC 3229</strong></a>, Section 10.4.1</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">300</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MULTIPLE_CHOICES</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-15"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.4.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">301</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MOVED_PERMANENTLY</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-16"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.4.2</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">302</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FOUND</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-17"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.4.3</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">303</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SEE_OTHER</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-18"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.4.4</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">304</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NOT_MODIFIED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-19"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7232.html"><strong>RFC 7232</strong></a>, Section 4.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">305</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">USE_PROXY</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-20"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.4.5</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">307</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TEMPORARY_REDIRECT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-21"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.4.7</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">308</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PERMANENT_REDIRECT</span></code></p></td>
<td><p>Permanent Redirect <span class="target" id="index-22"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7238.html"><strong>RFC 7238</strong></a>, Section 3 (Experimental)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">400</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BAD_REQUEST</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-23"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">401</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UNAUTHORIZED</span></code></p></td>
<td><p>HTTP/1.1 Authentication <span class="target" id="index-24"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7235.html"><strong>RFC 7235</strong></a>, Section 3.1</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">402</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PAYMENT_REQUIRED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-25"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.2</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">403</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FORBIDDEN</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-26"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.3</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">404</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NOT_FOUND</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-27"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.4</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">405</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">METHOD_NOT_ALLOWED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-28"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.5</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">406</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NOT_ACCEPTABLE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-29"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.6</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">407</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PROXY_AUTHENTICATION_REQUIRED</span></code></p></td>
<td><p>HTTP/1.1 Authentication <span class="target" id="index-30"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7235.html"><strong>RFC 7235</strong></a>, Section 3.2</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">408</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REQUEST_TIMEOUT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-31"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.7</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">409</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CONFLICT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-32"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.8</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">410</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">GONE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-33"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.9</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">411</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LENGTH_REQUIRED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-34"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.10</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">412</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PRECONDITION_FAILED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-35"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7232.html"><strong>RFC 7232</strong></a>, Section 4.2</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">413</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REQUEST_ENTITY_TOO_LARGE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-36"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.11</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">414</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REQUEST_URI_TOO_LONG</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-37"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.12</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">415</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UNSUPPORTED_MEDIA_TYPE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-38"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.13</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">416</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REQUESTED_RANGE_NOT_SATISFIABLE</span></code></p></td>
<td><p>HTTP/1.1 Range Requests <span class="target" id="index-39"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7233.html"><strong>RFC 7233</strong></a>, Section 4.4</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">417</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">EXPECTATION_FAILED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-40"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.14</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">418</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">IM_A_TEAPOT</span></code></p></td>
<td><p>HTCPCP/1.0 <span class="target" id="index-41"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2324.html"><strong>RFC 2324</strong></a>, Section 2.3.2</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">421</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MISDIRECTED_REQUEST</span></code></p></td>
<td><p>HTTP/2 <span class="target" id="index-42"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7540.html"><strong>RFC 7540</strong></a>, Section 9.1.2</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">422</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UNPROCESSABLE_ENTITY</span></code></p></td>
<td><p>WebDAV <span class="target" id="index-43"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4918.html"><strong>RFC 4918</strong></a>, Section 11.2</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">423</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LOCKED</span></code></p></td>
<td><p>WebDAV <span class="target" id="index-44"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4918.html"><strong>RFC 4918</strong></a>, Section 11.3</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">424</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FAILED_DEPENDENCY</span></code></p></td>
<td><p>WebDAV <span class="target" id="index-45"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4918.html"><strong>RFC 4918</strong></a>, Section 11.4</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">425</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TOO_EARLY</span></code></p></td>
<td><p>Using Early Data in HTTP <span class="target" id="index-46"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc8470.html"><strong>RFC 8470</strong></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">426</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UPGRADE_REQUIRED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-47"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.5.15</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">428</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PRECONDITION_REQUIRED</span></code></p></td>
<td><p>Additional HTTP Status Codes <span class="target" id="index-48"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6585.html"><strong>RFC 6585</strong></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">429</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TOO_MANY_REQUESTS</span></code></p></td>
<td><p>Additional HTTP Status Codes <span class="target" id="index-49"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6585.html"><strong>RFC 6585</strong></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">431</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REQUEST_HEADER_FIELDS_TOO_LARGE</span></code></p></td>
<td><p>Additional HTTP Status Codes <span class="target" id="index-50"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6585.html"><strong>RFC 6585</strong></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">451</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UNAVAILABLE_FOR_LEGAL_REASONS</span></code></p></td>
<td><p>An HTTP Status Code to Report Legal Obstacles <span class="target" id="index-51"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7725.html"><strong>RFC 7725</strong></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">500</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTERNAL_SERVER_ERROR</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-52"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.6.1</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">501</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NOT_IMPLEMENTED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-53"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.6.2</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">502</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BAD_GATEWAY</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-54"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.6.3</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">503</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SERVICE_UNAVAILABLE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-55"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.6.4</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">504</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">GATEWAY_TIMEOUT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-56"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.6.5</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">505</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">HTTP_VERSION_NOT_SUPPORTED</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-57"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6.6.6</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">506</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARIANT_ALSO_NEGOTIATES</span></code></p></td>
<td><p>Transparent Content Negotiation in HTTP <span class="target" id="index-58"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2295.html"><strong>RFC 2295</strong></a>, Section 8.1 (Experimental)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">507</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INSUFFICIENT_STORAGE</span></code></p></td>
<td><p>WebDAV <span class="target" id="index-59"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4918.html"><strong>RFC 4918</strong></a>, Section 11.5</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">508</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LOOP_DETECTED</span></code></p></td>
<td><p>WebDAV Binding Extensions <span class="target" id="index-60"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5842.html"><strong>RFC 5842</strong></a>, Section 7.2 (Experimental)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">510</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NOT_EXTENDED</span></code></p></td>
<td><p>An HTTP Extension Framework <span class="target" id="index-61"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2774.html"><strong>RFC 2774</strong></a>, Section 7 (Experimental)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">511</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NETWORK_AUTHENTICATION_REQUIRED</span></code></p></td>
<td><p>Additional HTTP Status Codes <span class="target" id="index-62"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6585.html"><strong>RFC 6585</strong></a>, Section 6</p></td>
</tr>
</tbody>
</table>
<p>In order to preserve backwards compatibility, enum values are also present
in the <a class="reference internal" href="http.client.html#module-http.client" title="http.client: HTTP and HTTPS protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code></a> module in the form of constants. The enum name is
equal to the constant name (i.e. <code class="docutils literal notranslate"><span class="pre">http.HTTPStatus.OK</span></code> is also available as
<code class="docutils literal notranslate"><span class="pre">http.client.OK</span></code>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added <code class="docutils literal notranslate"><span class="pre">421</span> <span class="pre">MISDIRECTED_REQUEST</span></code> status code.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span>Added <code class="docutils literal notranslate"><span class="pre">451</span> <span class="pre">UNAVAILABLE_FOR_LEGAL_REASONS</span></code> status code.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9: </span>Added <code class="docutils literal notranslate"><span class="pre">103</span> <span class="pre">EARLY_HINTS</span></code>, <code class="docutils literal notranslate"><span class="pre">418</span> <span class="pre">IM_A_TEAPOT</span></code> and <code class="docutils literal notranslate"><span class="pre">425</span> <span class="pre">TOO_EARLY</span></code> status codes.</p>
</div>
</section>
<section id="http-status-category">
<h2>HTTP status category<a class="headerlink" href="#http-status-category" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>The enum values have several properties to indicate the HTTP status category:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Indicates that</p></th>
<th class="head"><p>Details</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">is_informational</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">100</span> <span class="pre">&lt;=</span> <span class="pre">status</span> <span class="pre">&lt;=</span> <span class="pre">199</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-63"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">is_success</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">200</span> <span class="pre">&lt;=</span> <span class="pre">status</span> <span class="pre">&lt;=</span> <span class="pre">299</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-64"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">is_redirection</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">300</span> <span class="pre">&lt;=</span> <span class="pre">status</span> <span class="pre">&lt;=</span> <span class="pre">399</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-65"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">is_client_error</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">400</span> <span class="pre">&lt;=</span> <span class="pre">status</span> <span class="pre">&lt;=</span> <span class="pre">499</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-66"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">is_server_error</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">500</span> <span class="pre">&lt;=</span> <span class="pre">status</span> <span class="pre">&lt;=</span> <span class="pre">599</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-67"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 6</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">http</span> <span class="kn">import</span> <span class="n">HTTPStatus</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span><span class="o">.</span><span class="n">is_success</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPStatus</span><span class="o">.</span><span class="n">OK</span><span class="o">.</span><span class="n">is_client_error</span>
<span class="go">False</span>
</pre></div>
</div>
</div></blockquote>
<dl class="py class">
<dt class="sig sig-object py" id="http.HTTPMethod">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.</span></span><span class="sig-name descname"><span class="pre">HTTPMethod</span></span><a class="headerlink" href="#http.HTTPMethod" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<p>A subclass of <a class="reference internal" href="enum.html#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.StrEnum</span></code></a> that defines a set of HTTP methods and descriptions written in English.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">http</span> <span class="kn">import</span> <span class="n">HTTPMethod</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPMethod</span><span class="o">.</span><span class="n">GET</span>
<span class="go">&lt;HTTPMethod.GET&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPMethod</span><span class="o">.</span><span class="n">GET</span> <span class="o">==</span> <span class="s1">&#39;GET&#39;</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPMethod</span><span class="o">.</span><span class="n">GET</span><span class="o">.</span><span class="n">value</span>
<span class="go">&#39;GET&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">HTTPMethod</span><span class="o">.</span><span class="n">GET</span><span class="o">.</span><span class="n">description</span>
<span class="go">&#39;Retrieve the target.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">HTTPMethod</span><span class="p">)</span>
<span class="go">[&lt;HTTPMethod.CONNECT&gt;,</span>
<span class="go"> &lt;HTTPMethod.DELETE&gt;,</span>
<span class="go"> &lt;HTTPMethod.GET&gt;,</span>
<span class="go"> &lt;HTTPMethod.HEAD&gt;,</span>
<span class="go"> &lt;HTTPMethod.OPTIONS&gt;,</span>
<span class="go"> &lt;HTTPMethod.PATCH&gt;,</span>
<span class="go"> &lt;HTTPMethod.POST&gt;,</span>
<span class="go"> &lt;HTTPMethod.PUT&gt;,</span>
<span class="go"> &lt;HTTPMethod.TRACE&gt;]</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="http-methods">
<span id="id2"></span><h2>HTTP methods<a class="headerlink" href="#http-methods" title="Link to this heading">¶</a></h2>
<p>Supported,
<a class="reference external" href="https://www.iana.org/assignments/http-methods/http-methods.xhtml">IANA-registered methods</a>
available in <a class="reference internal" href="#http.HTTPMethod" title="http.HTTPMethod"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.HTTPMethod</span></code></a> are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Method</p></th>
<th class="head"><p>Enum Name</p></th>
<th class="head"><p>Details</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">GET</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">GET</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-68"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.1</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">HEAD</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">HEAD</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-69"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.2</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">POST</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">POST</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-70"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.3</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">PUT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PUT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-71"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.4</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DELETE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DELETE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-72"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.5</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">CONNECT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CONNECT</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-73"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.6</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">OPTIONS</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">OPTIONS</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-74"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.7</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TRACE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRACE</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-75"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7231.html"><strong>RFC 7231</strong></a>, Section 4.3.8</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">PATCH</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PATCH</span></code></p></td>
<td><p>HTTP/1.1 <span class="target" id="index-76"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5789.html"><strong>RFC 5789</strong></a></p></td>
</tr>
</tbody>
</table>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a><ul>
<li><a class="reference internal" href="#http-status-codes">HTTP status codes</a></li>
<li><a class="reference internal" href="#http-status-category">HTTP status category</a></li>
<li><a class="reference internal" href="#http-methods">HTTP methods</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="urllib.robotparser.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.robotparser</span></code> —  Parser for robots.txt</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="http.client.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="http.client.html" title="http.client — HTTP protocol client"
             >next</a> |</li>
        <li class="right" >
          <a href="urllib.robotparser.html" title="urllib.robotparser — Parser for robots.txt"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>