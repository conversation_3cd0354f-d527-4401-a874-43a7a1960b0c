<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="crm.ForecastKanbanRenderer" t-inherit="web.KanbanRenderer" t-inherit-mode="primary">
        <KanbanColumnQuickCreate position="replace">
            <t t-if="isGroupedByForecastField()">
                <ForecastKanbanColumnQuickCreate
                    folded="true"
                    onFoldChange="() => {}"
                    onValidate.bind="addForecastColumn"
                    exampleData="exampleData"
                    groupByField="props.list.groupByField"
                />
            </t>
            <t t-else="">$0</t>
        </KanbanColumnQuickCreate>
    </t>
</templates>
