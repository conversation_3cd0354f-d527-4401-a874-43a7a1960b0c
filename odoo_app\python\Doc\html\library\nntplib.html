<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="nntplib — NNTP protocol client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/nntplib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/nntplib.py This module defines the class NNTP which implements the client side of the Network News Transfer Protocol. It can be used to implement a news reader or poster, or automa..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/nntplib.py This module defines the class NNTP which implements the client side of the Network News Transfer Protocol. It can be used to implement a news reader or poster, or automa..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>nntplib — NNTP protocol client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="optparse — Parser for command line options" href="optparse.html" />
    <link rel="prev" title="nis — Interface to Sun’s NIS (Yellow Pages)" href="nis.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/nntplib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a><ul>
<li><a class="reference internal" href="#nntp-objects">NNTP Objects</a><ul>
<li><a class="reference internal" href="#attributes">Attributes</a></li>
<li><a class="reference internal" href="#methods">Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#utility-functions">Utility functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="nis.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code> — Interface to Sun’s NIS (Yellow Pages)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="optparse.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/nntplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="optparse.html" title="optparse — Parser for command line options"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="nis.html" title="nis — Interface to Sun’s NIS (Yellow Pages)"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-nntplib">
<span id="nntplib-nntp-protocol-client"></span><h1><a class="reference internal" href="#module-nntplib" title="nntplib: NNTP protocol client (requires sockets). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code></a> — NNTP protocol client<a class="headerlink" href="#module-nntplib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/nntplib.py">Lib/nntplib.py</a></p>
<div class="deprecated" id="index-0">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>The <a class="reference internal" href="#module-nntplib" title="nntplib: NNTP protocol client (requires sockets). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code></a> module is deprecated (see <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/"><strong>PEP 594</strong></a> for details).</p>
</div>
<hr class="docutils" />
<p>This module defines the class <a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> which implements the client side of
the Network News Transfer Protocol.  It can be used to implement a news reader
or poster, or automated news processors.  It is compatible with <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3977.html"><strong>RFC 3977</strong></a>
as well as the older <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc977.html"><strong>RFC 977</strong></a> and <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2980.html"><strong>RFC 2980</strong></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>Here are two small examples of how it can be used.  To list some statistics
about a newsgroup and print the subjects of the last 10 articles:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">nntplib</span><span class="o">.</span><span class="n">NNTP</span><span class="p">(</span><span class="s1">&#39;news.gmane.io&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">count</span><span class="p">,</span> <span class="n">first</span><span class="p">,</span> <span class="n">last</span><span class="p">,</span> <span class="n">name</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="s1">&#39;gmane.comp.python.committers&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Group&#39;</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="s1">&#39;has&#39;</span><span class="p">,</span> <span class="n">count</span><span class="p">,</span> <span class="s1">&#39;articles, range&#39;</span><span class="p">,</span> <span class="n">first</span><span class="p">,</span> <span class="s1">&#39;to&#39;</span><span class="p">,</span> <span class="n">last</span><span class="p">)</span>
<span class="go">Group gmane.comp.python.committers has 1096 articles, range 1 to 1096</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">overviews</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">over</span><span class="p">((</span><span class="n">last</span> <span class="o">-</span> <span class="mi">9</span><span class="p">,</span> <span class="n">last</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="nb">id</span><span class="p">,</span> <span class="n">over</span> <span class="ow">in</span> <span class="n">overviews</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="nb">id</span><span class="p">,</span> <span class="n">nntplib</span><span class="o">.</span><span class="n">decode_header</span><span class="p">(</span><span class="n">over</span><span class="p">[</span><span class="s1">&#39;subject&#39;</span><span class="p">]))</span>
<span class="gp">...</span>
<span class="go">1087 Re: Commit privileges for Łukasz Langa</span>
<span class="go">1088 Re: 3.2 alpha 2 freeze</span>
<span class="go">1089 Re: 3.2 alpha 2 freeze</span>
<span class="go">1090 Re: Commit privileges for Łukasz Langa</span>
<span class="go">1091 Re: Commit privileges for Łukasz Langa</span>
<span class="go">1092 Updated ssh key</span>
<span class="go">1093 Re: Updated ssh key</span>
<span class="go">1094 Re: Updated ssh key</span>
<span class="go">1095 Hello fellow committers!</span>
<span class="go">1096 Re: Hello fellow committers!</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">quit</span><span class="p">()</span>
<span class="go">&#39;205 Bye!&#39;</span>
</pre></div>
</div>
<p>To post an article from a binary file (this assumes that the article has valid
headers, and that you have right to post on the particular newsgroup):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">nntplib</span><span class="o">.</span><span class="n">NNTP</span><span class="p">(</span><span class="s1">&#39;news.gmane.io&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">f</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;article.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
<span class="go">&#39;240 Article posted successfully.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">quit</span><span class="p">()</span>
<span class="go">&#39;205 Bye!&#39;</span>
</pre></div>
</div>
<p>The module itself defines the following classes:</p>
<dl class="py class">
<dt class="sig sig-object py" id="nntplib.NNTP">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTP</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=119</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">readermode=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usenetrc=False</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP" title="Link to this definition">¶</a></dt>
<dd><p>Return a new <a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> object, representing a connection
to the NNTP server running on host <em>host</em>, listening at port <em>port</em>.
An optional <em>timeout</em> can be specified for the socket connection.
If the optional <em>user</em> and <em>password</em> are provided, or if suitable
credentials are present in <code class="file docutils literal notranslate"><span class="pre">/.netrc</span></code> and the optional flag <em>usenetrc</em>
is true, the <code class="docutils literal notranslate"><span class="pre">AUTHINFO</span> <span class="pre">USER</span></code> and <code class="docutils literal notranslate"><span class="pre">AUTHINFO</span> <span class="pre">PASS</span></code> commands are used
to identify and authenticate the user to the server.  If the optional
flag <em>readermode</em> is true, then a <code class="docutils literal notranslate"><span class="pre">mode</span> <span class="pre">reader</span></code> command is sent before
authentication is performed.  Reader mode is sometimes necessary if you are
connecting to an NNTP server on the local machine and intend to call
reader-specific commands, such as <code class="docutils literal notranslate"><span class="pre">group</span></code>.  If you get unexpected
<a class="reference internal" href="#nntplib.NNTPPermanentError" title="nntplib.NNTPPermanentError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NNTPPermanentError</span></code></a>s, you might need to set <em>readermode</em>.
The <a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> class supports the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement to
unconditionally consume <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exceptions and to close the NNTP
connection when done, e.g.:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">nntplib</span> <span class="kn">import</span> <span class="n">NNTP</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">NNTP</span><span class="p">(</span><span class="s1">&#39;news.gmane.io&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">n</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">n</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="s1">&#39;gmane.comp.python.committers&#39;</span><span class="p">)</span>
<span class="gp">... </span>
<span class="go">(&#39;211 1755 1 1755 gmane.comp.python.committers&#39;, 1755, 1, 1755, &#39;gmane.comp.python.committers&#39;)</span>
<span class="gp">&gt;&gt;&gt;</span>
</pre></div>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">nntplib.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
<p class="audit-hook"><p>All commands will raise an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">nntplib.putline</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code> and <code class="docutils literal notranslate"><span class="pre">line</span></code>,
where <code class="docutils literal notranslate"><span class="pre">line</span></code> is the bytes about to be sent to the remote host.</p>
</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>usenetrc</em> is now <code class="docutils literal notranslate"><span class="pre">False</span></code> by default.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Support for the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nntplib.NNTP_SSL">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTP_SSL</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=563</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ssl_context=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">readermode=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usenetrc=False</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP_SSL" title="Link to this definition">¶</a></dt>
<dd><p>Return a new <a class="reference internal" href="#nntplib.NNTP_SSL" title="nntplib.NNTP_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP_SSL</span></code></a> object, representing an encrypted
connection to the NNTP server running on host <em>host</em>, listening at
port <em>port</em>.  <a class="reference internal" href="#nntplib.NNTP_SSL" title="nntplib.NNTP_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP_SSL</span></code></a> objects have the same methods as
<a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> objects.  If <em>port</em> is omitted, port 563 (NNTPS) is used.
<em>ssl_context</em> is also optional, and is a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> object.
Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for best practices.
All other parameters behave the same as for <a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a>.</p>
<p>Note that SSL-on-563 is discouraged per <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4642.html"><strong>RFC 4642</strong></a>, in favor of
STARTTLS as described below.  However, some servers only support the
former.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">nntplib.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
<p class="audit-hook"><p>All commands will raise an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">nntplib.putline</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code> and <code class="docutils literal notranslate"><span class="pre">line</span></code>,
where <code class="docutils literal notranslate"><span class="pre">line</span></code> is the bytes about to be sent to the remote host.</p>
</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The class now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-data docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="nntplib.NNTPError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTPError</span></span><a class="headerlink" href="#nntplib.NNTPError" title="Link to this definition">¶</a></dt>
<dd><p>Derived from the standard exception <a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>, this is the base
class for all exceptions raised by the <a class="reference internal" href="#module-nntplib" title="nntplib: NNTP protocol client (requires sockets). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code></a> module.  Instances
of this class have the following attribute:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nntplib.NNTPError.response">
<span class="sig-name descname"><span class="pre">response</span></span><a class="headerlink" href="#nntplib.NNTPError.response" title="Link to this definition">¶</a></dt>
<dd><p>The response of the server if available, as a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> object.</p>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="nntplib.NNTPReplyError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTPReplyError</span></span><a class="headerlink" href="#nntplib.NNTPReplyError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when an unexpected reply is received from the server.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="nntplib.NNTPTemporaryError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTPTemporaryError</span></span><a class="headerlink" href="#nntplib.NNTPTemporaryError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when a response code in the range 400–499 is received.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="nntplib.NNTPPermanentError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTPPermanentError</span></span><a class="headerlink" href="#nntplib.NNTPPermanentError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when a response code in the range 500–599 is received.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="nntplib.NNTPProtocolError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTPProtocolError</span></span><a class="headerlink" href="#nntplib.NNTPProtocolError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when a reply is received from the server that does not begin
with a digit in the range 1–5.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="nntplib.NNTPDataError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">NNTPDataError</span></span><a class="headerlink" href="#nntplib.NNTPDataError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when there is some error in the response data.</p>
</dd></dl>

<section id="nntp-objects">
<span id="id1"></span><h2>NNTP Objects<a class="headerlink" href="#nntp-objects" title="Link to this heading">¶</a></h2>
<p>When connected, <a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> and <a class="reference internal" href="#nntplib.NNTP_SSL" title="nntplib.NNTP_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP_SSL</span></code></a> objects support the
following methods and attributes.</p>
<section id="attributes">
<h3>Attributes<a class="headerlink" href="#attributes" title="Link to this heading">¶</a></h3>
<dl class="py attribute">
<dt class="sig sig-object py" id="nntplib.NNTP.nntp_version">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">nntp_version</span></span><a class="headerlink" href="#nntplib.NNTP.nntp_version" title="Link to this definition">¶</a></dt>
<dd><p>An integer representing the version of the NNTP protocol supported by the
server.  In practice, this should be <code class="docutils literal notranslate"><span class="pre">2</span></code> for servers advertising
<span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3977.html"><strong>RFC 3977</strong></a> compliance and <code class="docutils literal notranslate"><span class="pre">1</span></code> for others.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nntplib.NNTP.nntp_implementation">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">nntp_implementation</span></span><a class="headerlink" href="#nntplib.NNTP.nntp_implementation" title="Link to this definition">¶</a></dt>
<dd><p>A string describing the software name and version of the NNTP server,
or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if not advertised by the server.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
<section id="methods">
<h3>Methods<a class="headerlink" href="#methods" title="Link to this heading">¶</a></h3>
<p>The <em>response</em> that is returned as the first item in the return tuple of almost
all methods is the server’s response: a string beginning with a three-digit
code.  If the server’s response indicates an error, the method raises one of
the above exceptions.</p>
<p>Many of the following methods take an optional keyword-only argument <em>file</em>.
When the <em>file</em> argument is supplied, it must be either a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>
opened for binary writing, or the name of an on-disk file to be written to.
The method will then write any data returned by the server (except for the
response line and the terminating dot) to the file; any list of lines,
tuples or objects that the method normally returns will be empty.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Many of the following methods have been reworked and fixed, which makes
them incompatible with their 3.1 counterparts.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.quit">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.quit" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">QUIT</span></code> command and close the connection.  Once this method has been
called, no other methods of the NNTP object should be called.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.getwelcome">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">getwelcome</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.getwelcome" title="Link to this definition">¶</a></dt>
<dd><p>Return the welcome message sent by the server in reply to the initial
connection.  (This message sometimes contains disclaimers or help information
that may be relevant to the user.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.getcapabilities">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">getcapabilities</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.getcapabilities" title="Link to this definition">¶</a></dt>
<dd><p>Return the <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3977.html"><strong>RFC 3977</strong></a> capabilities advertised by the server, as a
<a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> instance mapping capability names to (possibly empty) lists
of values. On legacy servers which don’t understand the <code class="docutils literal notranslate"><span class="pre">CAPABILITIES</span></code>
command, an empty dictionary is returned instead.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">NNTP</span><span class="p">(</span><span class="s1">&#39;news.gmane.io&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;POST&#39;</span> <span class="ow">in</span> <span class="n">s</span><span class="o">.</span><span class="n">getcapabilities</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.login">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">login</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usenetrc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.login" title="Link to this definition">¶</a></dt>
<dd><p>Send <code class="docutils literal notranslate"><span class="pre">AUTHINFO</span></code> commands with the user name and password.  If <em>user</em>
and <em>password</em> are <code class="docutils literal notranslate"><span class="pre">None</span></code> and <em>usenetrc</em> is true, credentials from
<code class="docutils literal notranslate"><span class="pre">~/.netrc</span></code> will be used if possible.</p>
<p>Unless intentionally delayed, login is normally performed during the
<a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> object initialization and separately calling this function
is unnecessary.  To force authentication to be delayed, you must not set
<em>user</em> or <em>password</em> when creating the object, and must set <em>usenetrc</em> to
False.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.starttls">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">starttls</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.starttls" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">STARTTLS</span></code> command.  This will enable encryption on the NNTP
connection.  The <em>context</em> argument is optional and should be a
<a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> object.  Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for best
practices.</p>
<p>Note that this may not be done after authentication information has
been transmitted, and authentication occurs by default if possible during a
<a class="reference internal" href="#nntplib.NNTP" title="nntplib.NNTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTP</span></code></a> object initialization.  See <a class="reference internal" href="#nntplib.NNTP.login" title="nntplib.NNTP.login"><code class="xref py py-meth docutils literal notranslate"><span class="pre">NNTP.login()</span></code></a> for information
on suppressing this behavior.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The method now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-data docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.newgroups">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">newgroups</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">date</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.newgroups" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">NEWGROUPS</span></code> command.  The <em>date</em> argument should be a
<a class="reference internal" href="datetime.html#datetime.date" title="datetime.date"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.date</span></code></a> or <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a> object.
Return a pair <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">groups)</span></code> where <em>groups</em> is a list representing
the groups that are new since the given <em>date</em>. If <em>file</em> is supplied,
though, then <em>groups</em> will be empty.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">date</span><span class="p">,</span> <span class="n">timedelta</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">groups</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">newgroups</span><span class="p">(</span><span class="n">date</span><span class="o">.</span><span class="n">today</span><span class="p">()</span> <span class="o">-</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">days</span><span class="o">=</span><span class="mi">3</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">groups</span><span class="p">)</span> 
<span class="go">85</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">groups</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> 
<span class="go">GroupInfo(group=&#39;gmane.network.tor.devel&#39;, last=&#39;4&#39;, first=&#39;1&#39;, flag=&#39;m&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.newnews">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">newnews</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">group</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">date</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.newnews" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">NEWNEWS</span></code> command.  Here, <em>group</em> is a group name or <code class="docutils literal notranslate"><span class="pre">'*'</span></code>, and
<em>date</em> has the same meaning as for <a class="reference internal" href="#nntplib.NNTP.newgroups" title="nntplib.NNTP.newgroups"><code class="xref py py-meth docutils literal notranslate"><span class="pre">newgroups()</span></code></a>.  Return a pair
<code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">articles)</span></code> where <em>articles</em> is a list of message ids.</p>
<p>This command is frequently disabled by NNTP server administrators.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.list">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">group_pattern</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.list" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">LIST</span></code> or <code class="docutils literal notranslate"><span class="pre">LIST</span> <span class="pre">ACTIVE</span></code> command.  Return a pair
<code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">list)</span></code> where <em>list</em> is a list of tuples representing all
the groups available from this NNTP server, optionally matching the
pattern string <em>group_pattern</em>.  Each tuple has the form
<code class="docutils literal notranslate"><span class="pre">(group,</span> <span class="pre">last,</span> <span class="pre">first,</span> <span class="pre">flag)</span></code>, where <em>group</em> is a group name, <em>last</em>
and <em>first</em> are the last and first article numbers, and <em>flag</em> usually
takes one of these values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">y</span></code>: Local postings and articles from peers are allowed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">m</span></code>: The group is moderated and all postings must be approved.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">n</span></code>: No local postings are allowed, only articles from peers.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">j</span></code>: Articles from peers are filed in the junk group instead.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">x</span></code>: No local postings, and articles from peers are ignored.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">=foo.bar</span></code>: Articles are filed in the <code class="docutils literal notranslate"><span class="pre">foo.bar</span></code> group instead.</p></li>
</ul>
<p>If <em>flag</em> has another value, then the status of the newsgroup should be
considered unknown.</p>
<p>This command can return very large results, especially if <em>group_pattern</em>
is not specified.  It is best to cache the results offline unless you
really need to refresh them.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>group_pattern</em> was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.descriptions">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">descriptions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">grouppattern</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.descriptions" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">LIST</span> <span class="pre">NEWSGROUPS</span></code> command, where <em>grouppattern</em> is a wildmat string as
specified in <span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3977.html"><strong>RFC 3977</strong></a> (it’s essentially the same as DOS or UNIX shell wildcard
strings).  Return a pair <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">descriptions)</span></code>, where <em>descriptions</em>
is a dictionary mapping group names to textual descriptions.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">descs</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">descriptions</span><span class="p">(</span><span class="s1">&#39;gmane.comp.python.*&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">descs</span><span class="p">)</span> 
<span class="go">295</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">descs</span><span class="o">.</span><span class="n">popitem</span><span class="p">()</span> 
<span class="go">(&#39;gmane.comp.python.bio.general&#39;, &#39;BioPython discussion list (Moderated)&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.description">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">description</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.description" title="Link to this definition">¶</a></dt>
<dd><p>Get a description for a single group <em>group</em>.  If more than one group matches
(if ‘group’ is a real wildmat string), return the first match.   If no group
matches, return an empty string.</p>
<p>This elides the response code from the server.  If the response code is needed,
use <a class="reference internal" href="#nntplib.NNTP.descriptions" title="nntplib.NNTP.descriptions"><code class="xref py py-meth docutils literal notranslate"><span class="pre">descriptions()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.group">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">group</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.group" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">GROUP</span></code> command, where <em>name</em> is the group name.  The group is
selected as the current group, if it exists.  Return a tuple
<code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">count,</span> <span class="pre">first,</span> <span class="pre">last,</span> <span class="pre">name)</span></code> where <em>count</em> is the (estimated)
number of articles in the group, <em>first</em> is the first article number in
the group, <em>last</em> is the last article number in the group, and <em>name</em>
is the group name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.over">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">over</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_spec</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.over" title="Link to this definition">¶</a></dt>
<dd><p>Send an <code class="docutils literal notranslate"><span class="pre">OVER</span></code> command, or an <code class="docutils literal notranslate"><span class="pre">XOVER</span></code> command on legacy servers.
<em>message_spec</em> can be either a string representing a message id, or
a <code class="docutils literal notranslate"><span class="pre">(first,</span> <span class="pre">last)</span></code> tuple of numbers indicating a range of articles in
the current group, or a <code class="docutils literal notranslate"><span class="pre">(first,</span> <span class="pre">None)</span></code> tuple indicating a range of
articles starting from <em>first</em> to the last article in the current group,
or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> to select the current article in the current group.</p>
<p>Return a pair <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">overviews)</span></code>.  <em>overviews</em> is a list of
<code class="docutils literal notranslate"><span class="pre">(article_number,</span> <span class="pre">overview)</span></code> tuples, one for each article selected
by <em>message_spec</em>.  Each <em>overview</em> is a dictionary with the same number
of items, but this number depends on the server.  These items are either
message headers (the key is then the lower-cased header name) or metadata
items (the key is then the metadata name prepended with <code class="docutils literal notranslate"><span class="pre">&quot;:&quot;</span></code>).  The
following items are guaranteed to be present by the NNTP specification:</p>
<ul class="simple">
<li><p>the <code class="docutils literal notranslate"><span class="pre">subject</span></code>, <code class="docutils literal notranslate"><span class="pre">from</span></code>, <code class="docutils literal notranslate"><span class="pre">date</span></code>, <code class="docutils literal notranslate"><span class="pre">message-id</span></code> and <code class="docutils literal notranslate"><span class="pre">references</span></code>
headers</p></li>
<li><p>the <code class="docutils literal notranslate"><span class="pre">:bytes</span></code> metadata: the number of bytes in the entire raw article
(including headers and body)</p></li>
<li><p>the <code class="docutils literal notranslate"><span class="pre">:lines</span></code> metadata: the number of lines in the article body</p></li>
</ul>
<p>The value of each item is either a string, or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if not present.</p>
<p>It is advisable to use the <a class="reference internal" href="#nntplib.decode_header" title="nntplib.decode_header"><code class="xref py py-func docutils literal notranslate"><span class="pre">decode_header()</span></code></a> function on header
values when they may contain non-ASCII characters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">_</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">first</span><span class="p">,</span> <span class="n">last</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="s1">&#39;gmane.comp.python.devel&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">overviews</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">over</span><span class="p">((</span><span class="n">last</span><span class="p">,</span> <span class="n">last</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">art_num</span><span class="p">,</span> <span class="n">over</span> <span class="o">=</span> <span class="n">overviews</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">art_num</span>
<span class="go">117216</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">over</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>
<span class="go">[&#39;xref&#39;, &#39;from&#39;, &#39;:lines&#39;, &#39;:bytes&#39;, &#39;references&#39;, &#39;date&#39;, &#39;message-id&#39;, &#39;subject&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">over</span><span class="p">[</span><span class="s1">&#39;from&#39;</span><span class="p">]</span>
<span class="go">&#39;=?UTF-8?B?Ik1hcnRpbiB2LiBMw7Z3aXMi?= &lt;<EMAIL>&gt;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nntplib</span><span class="o">.</span><span class="n">decode_header</span><span class="p">(</span><span class="n">over</span><span class="p">[</span><span class="s1">&#39;from&#39;</span><span class="p">])</span>
<span class="go">&#39;&quot;Martin v. Löwis&quot; &lt;<EMAIL>&gt;&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.help">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">help</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.help" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">HELP</span></code> command.  Return a pair <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">list)</span></code> where <em>list</em> is a
list of help strings.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.stat">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">stat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_spec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.stat" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">STAT</span></code> command, where <em>message_spec</em> is either a message id
(enclosed in <code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code> and <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code>) or an article number in the current group.
If <em>message_spec</em> is omitted or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the current article in the
current group is considered.  Return a triple <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">number,</span> <span class="pre">id)</span></code>
where <em>number</em> is the article number and <em>id</em> is the message id.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">_</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">first</span><span class="p">,</span> <span class="n">last</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="s1">&#39;gmane.comp.python.devel&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">number</span><span class="p">,</span> <span class="n">message_id</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">first</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">number</span><span class="p">,</span> <span class="n">message_id</span>
<span class="go">(9099, &#39;&lt;<EMAIL>&gt;&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.next">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">next</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.next" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">NEXT</span></code> command.  Return as for <a class="reference internal" href="#nntplib.NNTP.stat" title="nntplib.NNTP.stat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stat()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.last">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">last</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.last" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">LAST</span></code> command.  Return as for <a class="reference internal" href="#nntplib.NNTP.stat" title="nntplib.NNTP.stat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stat()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.article">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">article</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_spec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.article" title="Link to this definition">¶</a></dt>
<dd><p>Send an <code class="docutils literal notranslate"><span class="pre">ARTICLE</span></code> command, where <em>message_spec</em> has the same meaning as
for <a class="reference internal" href="#nntplib.NNTP.stat" title="nntplib.NNTP.stat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stat()</span></code></a>.  Return a tuple <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">info)</span></code> where <em>info</em>
is a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">namedtuple</span></code></a> with three attributes <em>number</em>,
<em>message_id</em> and <em>lines</em> (in that order).  <em>number</em> is the article number
in the group (or 0 if the information is not available), <em>message_id</em> the
message id as a string, and <em>lines</em> a list of lines (without terminating
newlines) comprising the raw message including headers and body.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">resp</span><span class="p">,</span> <span class="n">info</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">article</span><span class="p">(</span><span class="s1">&#39;&lt;<EMAIL>&gt;&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span><span class="o">.</span><span class="n">number</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span><span class="o">.</span><span class="n">message_id</span>
<span class="go">&#39;&lt;<EMAIL>&gt;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">info</span><span class="o">.</span><span class="n">lines</span><span class="p">)</span>
<span class="go">65</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span><span class="o">.</span><span class="n">lines</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">b&#39;Path: main.gmane.org!not-for-mail&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span><span class="o">.</span><span class="n">lines</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
<span class="go">b&#39;From: Neal Norwitz &lt;<EMAIL>&gt;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span><span class="o">.</span><span class="n">lines</span><span class="p">[</span><span class="o">-</span><span class="mi">3</span><span class="p">:]</span>
<span class="go">[b&#39;There is a patch for 2.3 as well as 2.2.&#39;, b&#39;&#39;, b&#39;Neal&#39;]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.head">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">head</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_spec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.head" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#nntplib.NNTP.article" title="nntplib.NNTP.article"><code class="xref py py-meth docutils literal notranslate"><span class="pre">article()</span></code></a>, but sends a <code class="docutils literal notranslate"><span class="pre">HEAD</span></code> command.  The <em>lines</em>
returned (or written to <em>file</em>) will only contain the message headers, not
the body.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.body">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">body</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_spec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.body" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#nntplib.NNTP.article" title="nntplib.NNTP.article"><code class="xref py py-meth docutils literal notranslate"><span class="pre">article()</span></code></a>, but sends a <code class="docutils literal notranslate"><span class="pre">BODY</span></code> command.  The <em>lines</em>
returned (or written to <em>file</em>) will only contain the message body, not the
headers.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.post">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">post</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.post" title="Link to this definition">¶</a></dt>
<dd><p>Post an article using the <code class="docutils literal notranslate"><span class="pre">POST</span></code> command.  The <em>data</em> argument is either
a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> opened for binary reading, or any iterable of bytes
objects (representing raw lines of the article to be posted).  It should
represent a well-formed news article, including the required headers.  The
<a class="reference internal" href="#nntplib.NNTP.post" title="nntplib.NNTP.post"><code class="xref py py-meth docutils literal notranslate"><span class="pre">post()</span></code></a> method automatically escapes lines beginning with <code class="docutils literal notranslate"><span class="pre">.</span></code> and
appends the termination line.</p>
<p>If the method succeeds, the server’s response is returned.  If the server
refuses posting, a <a class="reference internal" href="#nntplib.NNTPReplyError" title="nntplib.NNTPReplyError"><code class="xref py py-class docutils literal notranslate"><span class="pre">NNTPReplyError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.ihave">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">ihave</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.ihave" title="Link to this definition">¶</a></dt>
<dd><p>Send an <code class="docutils literal notranslate"><span class="pre">IHAVE</span></code> command. <em>message_id</em> is the id of the message to send
to the server (enclosed in  <code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code> and <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code>).  The <em>data</em> parameter
and the return value are the same as for <a class="reference internal" href="#nntplib.NNTP.post" title="nntplib.NNTP.post"><code class="xref py py-meth docutils literal notranslate"><span class="pre">post()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.date">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">date</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.date" title="Link to this definition">¶</a></dt>
<dd><p>Return a pair <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">date)</span></code>.  <em>date</em> is a <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime</span></code></a>
object containing the current date and time of the server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.slave">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">slave</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.slave" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">SLAVE</span></code> command.  Return the server’s <em>response</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.set_debuglevel">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">set_debuglevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.set_debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>Set the instance’s debugging level.  This controls the amount of debugging
output printed.  The default, <code class="docutils literal notranslate"><span class="pre">0</span></code>, produces no debugging output.  A value of
<code class="docutils literal notranslate"><span class="pre">1</span></code> produces a moderate amount of debugging output, generally a single line
per request or response.  A value of <code class="docutils literal notranslate"><span class="pre">2</span></code> or higher produces the maximum amount
of debugging output, logging each line sent and received on the connection
(including message text).</p>
</dd></dl>

<p>The following are optional NNTP extensions defined in <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2980.html"><strong>RFC 2980</strong></a>.  Some of
them have been superseded by newer commands in <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3977.html"><strong>RFC 3977</strong></a>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.xhdr">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">xhdr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hdr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.xhdr" title="Link to this definition">¶</a></dt>
<dd><p>Send an <code class="docutils literal notranslate"><span class="pre">XHDR</span></code> command.  The <em>hdr</em> argument is a header keyword, e.g.
<code class="docutils literal notranslate"><span class="pre">'subject'</span></code>.  The <em>str</em> argument should have the form <code class="docutils literal notranslate"><span class="pre">'first-last'</span></code>
where <em>first</em> and <em>last</em> are the first and last article numbers to search.
Return a pair <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">list)</span></code>, where <em>list</em> is a list of pairs <code class="docutils literal notranslate"><span class="pre">(id,</span>
<span class="pre">text)</span></code>, where <em>id</em> is an article number (as a string) and <em>text</em> is the text of
the requested header for that article. If the <em>file</em> parameter is supplied, then
the output of the  <code class="docutils literal notranslate"><span class="pre">XHDR</span></code> command is stored in a file.  If <em>file</em> is a string,
then the method will open a file with that name, write to it  then close it.
If <em>file</em> is a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>, then it will start calling <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> on
it to store the lines of the command output. If <em>file</em> is supplied, then the
returned <em>list</em> is an empty list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nntplib.NNTP.xover">
<span class="sig-prename descclassname"><span class="pre">NNTP.</span></span><span class="sig-name descname"><span class="pre">xover</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.NNTP.xover" title="Link to this definition">¶</a></dt>
<dd><p>Send an <code class="docutils literal notranslate"><span class="pre">XOVER</span></code> command.  <em>start</em> and <em>end</em> are article numbers
delimiting the range of articles to select.  The return value is the
same of for <a class="reference internal" href="#nntplib.NNTP.over" title="nntplib.NNTP.over"><code class="xref py py-meth docutils literal notranslate"><span class="pre">over()</span></code></a>.  It is recommended to use <a class="reference internal" href="#nntplib.NNTP.over" title="nntplib.NNTP.over"><code class="xref py py-meth docutils literal notranslate"><span class="pre">over()</span></code></a>
instead, since it will automatically use the newer <code class="docutils literal notranslate"><span class="pre">OVER</span></code> command
if available.</p>
</dd></dl>

</section>
</section>
<section id="utility-functions">
<h2>Utility functions<a class="headerlink" href="#utility-functions" title="Link to this heading">¶</a></h2>
<p>The module also defines the following utility function:</p>
<dl class="py function">
<dt class="sig sig-object py" id="nntplib.decode_header">
<span class="sig-prename descclassname"><span class="pre">nntplib.</span></span><span class="sig-name descname"><span class="pre">decode_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header_str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nntplib.decode_header" title="Link to this definition">¶</a></dt>
<dd><p>Decode a header value, un-escaping any escaped non-ASCII characters.
<em>header_str</em> must be a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> object.  The unescaped value is
returned.  Using this function is recommended to display some headers
in a human readable form:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">decode_header</span><span class="p">(</span><span class="s2">&quot;Some subject&quot;</span><span class="p">)</span>
<span class="go">&#39;Some subject&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">decode_header</span><span class="p">(</span><span class="s2">&quot;=?ISO-8859-15?Q?D=E9buter_en_Python?=&quot;</span><span class="p">)</span>
<span class="go">&#39;Débuter en Python&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">decode_header</span><span class="p">(</span><span class="s2">&quot;Re: =?UTF-8?B?cHJvYmzDqG1lIGRlIG1hdHJpY2U=?=&quot;</span><span class="p">)</span>
<span class="go">&#39;Re: problème de matrice&#39;</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a><ul>
<li><a class="reference internal" href="#nntp-objects">NNTP Objects</a><ul>
<li><a class="reference internal" href="#attributes">Attributes</a></li>
<li><a class="reference internal" href="#methods">Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#utility-functions">Utility functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="nis.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code> — Interface to Sun’s NIS (Yellow Pages)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="optparse.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/nntplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="optparse.html" title="optparse — Parser for command line options"
             >next</a> |</li>
        <li class="right" >
          <a href="nis.html" title="nis — Interface to Sun’s NIS (Yellow Pages)"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>