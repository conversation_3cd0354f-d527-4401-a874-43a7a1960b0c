<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_update_tax_tags_wizard_form" model="ir.ui.view">
        <field name="name">account.update.tax.tags.wizard.form</field>
        <field name="model">account.update.tax.tags.wizard</field>
        <field name="arch" type="xml">
            <form>
                <field name="company_id" invisible="1"/>
                <field name="display_lock_date_warning" invisible="1"/>
                <div class="alert alert-warning" role="alert">
                    Updating tax tags on existing Journal Entries is an <b>irreversible</b> action that will impact
                    your reports.<br/>
                    It is highly recommended to backup your database beforehand.<br/>
                    The update will change tax tags on your accounting history, starting from and including selected date,
                    so that it matches with the current configuration of your taxes.<br/>
                </div>
                <div class="alert alert-warning" role="alert" attrs="{'invisible': [('display_lock_date_warning', '=', False)]}">
                    The date you chose is violating the tax lock date, do this at your own risk.
                </div>
                <group>
                    <field name="date_from"/>
                </group>
                <footer>
                    <button string="Update" class="btn-primary" name="update_amls_tax_tags" type="object" data-hotkey="v"/>
                    <button string="Discard" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
