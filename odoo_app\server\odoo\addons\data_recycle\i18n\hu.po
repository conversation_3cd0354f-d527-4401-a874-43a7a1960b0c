# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_recycle
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# krnkris, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: krnkris, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid ""
"' recycling rule.<br/>\n"
"You can validate those changes"
msgstr ""

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
#, python-format
msgid "**Record Deleted**"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__active
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__active
msgid "Active"
msgstr "Aktív"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__archive
msgid "Archive"
msgstr "Archiválás"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__automatic
msgid "Automatic"
msgstr "Automatikus"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__company_id
msgid "Company"
msgstr "Vállalat"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config
msgid "Configuration"
msgstr "Konfiguráció"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "Configure rules to identify records to clean"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr ""

#. module: data_recycle
#: model:ir.actions.server,name:data_recycle.ir_cron_clean_records_ir_actions_server
msgid "Data Recycle: Clean Records"
msgstr ""

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
#, python-format
msgid "Data to Recycle"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__days
msgid "Days"
msgstr "nap"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__unlink
msgid "Delete"
msgstr "Törlés"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta
msgid "Delta"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta_unit
msgid "Delta Unit"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Discard"
msgstr "Elvetés"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Discarded"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__display_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record_notification
msgid "Field Recycle Records"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__domain
msgid "Filter"
msgstr "Szűrő"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__id
msgid "ID"
msgstr "Azonosító"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__include_archived
msgid "Include Archived"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__last_notification
msgid "Last Notification"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: data_recycle
#: model:ir.model.fields,help:data_recycle.field_data_recycle_model__notify_user_ids
msgid "List of users to notify when there are new records to recycle"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__manual
msgid "Manual"
msgstr "Manuális"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_id
msgid "Model"
msgstr "Modell"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_name
msgid "Model Name"
msgstr "Modell név"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__months
msgid "Months"
msgstr "Hónap"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__name
msgid "Name"
msgstr "Név"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "No cleaning suggestions"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency
msgid "Notify"
msgstr "Értesítés"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_user_ids
msgid "Notify Users"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_id
msgid "Record ID"
msgstr "Bejegyzés azonosító"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__name
msgid "Record Name"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Records"
msgstr "Bejegyzések"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__records_to_recycle_count
msgid "Records To Recycle"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_action
msgid "Recycle Action"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_mode
msgid "Recycle Mode"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__recycle_model_id
msgid "Recycle Model"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_record_ids
msgid "Recycle Record"
msgstr ""

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_recycle_record
msgid "Recycle Records"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Recycle Rule"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Recycle Rules"
msgstr ""

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_model
msgid "Recycling Model"
msgstr ""

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_record
msgid "Recycling Record"
msgstr ""

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_config
msgid "Recyle Records Rules"
msgstr ""

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "Szabályok"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Run Now"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Select a model to configure recycling actions"
msgstr ""

#. module: data_recycle
#: model:ir.model.constraint,message:data_recycle.constraint_data_recycle_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr ""

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
#, python-format
msgid "This model doesn't manage archived records. Only deletion is possible."
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_id
msgid "Time Field"
msgstr ""

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
#, python-format
msgid "Undefined Name"
msgstr ""

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#, python-format
msgid "Unselect"
msgstr ""

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
#, python-format
msgid "Validate"
msgstr "Jóváhagyás"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "We've identified"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__weeks
msgid "Weeks"
msgstr "hét"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__years
msgid "Years"
msgstr "Év"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "here"
msgstr "itt"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "records to clean with the '"
msgstr ""
