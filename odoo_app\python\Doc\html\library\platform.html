<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="platform —  Access to underlying platform’s identifying data" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/platform.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/platform.py Cross Platform: Java Platform: Windows Platform: macOS Platform: Unix Platforms: Linux Platforms:" />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/platform.py Cross Platform: Java Platform: Windows Platform: macOS Platform: Unix Platforms: Linux Platforms:" />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>platform — Access to underlying platform’s identifying data &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="errno — Standard errno system symbols" href="errno.html" />
    <link rel="prev" title="curses.panel — A panel stack extension for curses" href="curses.panel.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/platform.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a><ul>
<li><a class="reference internal" href="#cross-platform">Cross Platform</a></li>
<li><a class="reference internal" href="#java-platform">Java Platform</a></li>
<li><a class="reference internal" href="#windows-platform">Windows Platform</a></li>
<li><a class="reference internal" href="#macos-platform">macOS Platform</a></li>
<li><a class="reference internal" href="#unix-platforms">Unix Platforms</a></li>
<li><a class="reference internal" href="#linux-platforms">Linux Platforms</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="curses.panel.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses.panel</span></code> — A panel stack extension for curses</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="errno.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code> — Standard errno system symbols</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/platform.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="errno.html" title="errno — Standard errno system symbols"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="curses.panel.html" title="curses.panel — A panel stack extension for curses"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" accesskey="U">Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-platform">
<span id="platform-access-to-underlying-platform-s-identifying-data"></span><h1><a class="reference internal" href="#module-platform" title="platform: Retrieves as much platform identifying data as possible."><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code></a> —  Access to underlying platform’s identifying data<a class="headerlink" href="#module-platform" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/platform.py">Lib/platform.py</a></p>
<hr class="docutils" />
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Specific platforms listed alphabetically, with Linux included in the Unix
section.</p>
</div>
<section id="cross-platform">
<h2>Cross Platform<a class="headerlink" href="#cross-platform" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="platform.architecture">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">architecture</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">executable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">sys.executable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bits</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">linkage</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.architecture" title="Link to this definition">¶</a></dt>
<dd><p>Queries the given executable (defaults to the Python interpreter binary) for
various architecture information.</p>
<p>Returns a tuple <code class="docutils literal notranslate"><span class="pre">(bits,</span> <span class="pre">linkage)</span></code> which contain information about the bit
architecture and the linkage format used for the executable. Both values are
returned as strings.</p>
<p>Values that cannot be determined are returned as given by the parameter presets.
If bits is given as <code class="docutils literal notranslate"><span class="pre">''</span></code>, the <code class="docutils literal notranslate"><span class="pre">sizeof(pointer)</span></code> (or
<code class="docutils literal notranslate"><span class="pre">sizeof(long)</span></code> on Python version &lt; 1.5.2) is used as indicator for the
supported pointer size.</p>
<p>The function relies on the system’s <code class="file docutils literal notranslate"><span class="pre">file</span></code> command to do the actual work.
This is available on most if not all Unix  platforms and some non-Unix platforms
and then only if the executable points to the Python interpreter.  Reasonable
defaults are used when the above needs are not met.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On macOS (and perhaps other platforms), executable files may be
universal files containing multiple architectures.</p>
<p>To get at the “64-bitness” of the current interpreter, it is more
reliable to query the <a class="reference internal" href="sys.html#sys.maxsize" title="sys.maxsize"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.maxsize</span></code></a> attribute:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">is_64bits</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">maxsize</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="o">**</span><span class="mi">32</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.machine">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">machine</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.machine" title="Link to this definition">¶</a></dt>
<dd><p>Returns the machine type, e.g. <code class="docutils literal notranslate"><span class="pre">'AMD64'</span></code>. An empty string is returned if the
value cannot be determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.node">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">node</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.node" title="Link to this definition">¶</a></dt>
<dd><p>Returns the computer’s network name (may not be fully qualified!). An empty
string is returned if the value cannot be determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.platform">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">platform</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">aliased</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">terse</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.platform" title="Link to this definition">¶</a></dt>
<dd><p>Returns a single string identifying the underlying platform with as much useful
information as possible.</p>
<p>The output is intended to be <em>human readable</em> rather than machine parseable. It
may look different on different platforms and this is intended.</p>
<p>If <em>aliased</em> is true, the function will use aliases for various platforms that
report system names which differ from their common names, for example SunOS will
be reported as Solaris.  The <a class="reference internal" href="#platform.system_alias" title="platform.system_alias"><code class="xref py py-func docutils literal notranslate"><span class="pre">system_alias()</span></code></a> function is used to implement
this.</p>
<p>Setting <em>terse</em> to true causes the function to return only the absolute minimum
information needed to identify the platform.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>On macOS, the function now uses <a class="reference internal" href="#platform.mac_ver" title="platform.mac_ver"><code class="xref py py-func docutils literal notranslate"><span class="pre">mac_ver()</span></code></a>, if it returns a
non-empty release string, to get the macOS version rather than the darwin
version.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.processor">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">processor</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.processor" title="Link to this definition">¶</a></dt>
<dd><p>Returns the (real) processor name, e.g. <code class="docutils literal notranslate"><span class="pre">'amdk6'</span></code>.</p>
<p>An empty string is returned if the value cannot be determined. Note that many
platforms do not provide this information or simply return the same value as for
<a class="reference internal" href="#platform.machine" title="platform.machine"><code class="xref py py-func docutils literal notranslate"><span class="pre">machine()</span></code></a>.  NetBSD does this.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_build">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_build</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_build" title="Link to this definition">¶</a></dt>
<dd><p>Returns a tuple <code class="docutils literal notranslate"><span class="pre">(buildno,</span> <span class="pre">builddate)</span></code> stating the Python build number and
date as strings.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_compiler">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_compiler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_compiler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string identifying the compiler used for compiling Python.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_branch">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_branch</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_branch" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string identifying the Python implementation SCM branch.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_implementation">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_implementation</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_implementation" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string identifying the Python implementation. Possible return values
are: ‘CPython’, ‘IronPython’, ‘Jython’, ‘PyPy’.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_revision">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_revision</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_revision" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string identifying the Python implementation SCM revision.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_version">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_version" title="Link to this definition">¶</a></dt>
<dd><p>Returns the Python version as string <code class="docutils literal notranslate"><span class="pre">'major.minor.patchlevel'</span></code>.</p>
<p>Note that unlike the Python <code class="docutils literal notranslate"><span class="pre">sys.version</span></code>, the returned value will always
include the patchlevel (it defaults to 0).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.python_version_tuple">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">python_version_tuple</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.python_version_tuple" title="Link to this definition">¶</a></dt>
<dd><p>Returns the Python version as tuple <code class="docutils literal notranslate"><span class="pre">(major,</span> <span class="pre">minor,</span> <span class="pre">patchlevel)</span></code> of strings.</p>
<p>Note that unlike the Python <code class="docutils literal notranslate"><span class="pre">sys.version</span></code>, the returned value will always
include the patchlevel (it defaults to <code class="docutils literal notranslate"><span class="pre">'0'</span></code>).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.release">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">release</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.release" title="Link to this definition">¶</a></dt>
<dd><p>Returns the system’s release, e.g. <code class="docutils literal notranslate"><span class="pre">'2.2.0'</span></code> or <code class="docutils literal notranslate"><span class="pre">'NT'</span></code>. An empty string is
returned if the value cannot be determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.system">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">system</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.system" title="Link to this definition">¶</a></dt>
<dd><p>Returns the system/OS name, such as <code class="docutils literal notranslate"><span class="pre">'Linux'</span></code>, <code class="docutils literal notranslate"><span class="pre">'Darwin'</span></code>, <code class="docutils literal notranslate"><span class="pre">'Java'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'Windows'</span></code>. An empty string is returned if the value cannot be determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.system_alias">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">system_alias</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">system</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">release</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.system_alias" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">(system,</span> <span class="pre">release,</span> <span class="pre">version)</span></code> aliased to common marketing names used
for some systems.  It also does some reordering of the information in some cases
where it would otherwise cause confusion.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.version">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.version" title="Link to this definition">¶</a></dt>
<dd><p>Returns the system’s release version, e.g. <code class="docutils literal notranslate"><span class="pre">'#3</span> <span class="pre">on</span> <span class="pre">degas'</span></code>. An empty string is
returned if the value cannot be determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.uname">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">uname</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.uname" title="Link to this definition">¶</a></dt>
<dd><p>Fairly portable uname interface. Returns a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a>
containing six attributes: <a class="reference internal" href="#platform.system" title="platform.system"><code class="xref py py-attr docutils literal notranslate"><span class="pre">system</span></code></a>, <a class="reference internal" href="#platform.node" title="platform.node"><code class="xref py py-attr docutils literal notranslate"><span class="pre">node</span></code></a>, <a class="reference internal" href="#platform.release" title="platform.release"><code class="xref py py-attr docutils literal notranslate"><span class="pre">release</span></code></a>,
<a class="reference internal" href="#platform.version" title="platform.version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">version</span></code></a>, <a class="reference internal" href="#platform.machine" title="platform.machine"><code class="xref py py-attr docutils literal notranslate"><span class="pre">machine</span></code></a>, and <a class="reference internal" href="#platform.processor" title="platform.processor"><code class="xref py py-attr docutils literal notranslate"><span class="pre">processor</span></code></a>.</p>
<p><a class="reference internal" href="#platform.processor" title="platform.processor"><code class="xref py py-attr docutils literal notranslate"><span class="pre">processor</span></code></a> is resolved late, on demand.</p>
<p>Note: the first two attribute names differ from the names presented by
<a class="reference internal" href="os.html#os.uname" title="os.uname"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.uname()</span></code></a>, where they are named <code class="xref py py-attr docutils literal notranslate"><span class="pre">sysname</span></code> and
<code class="xref py py-attr docutils literal notranslate"><span class="pre">nodename</span></code>.</p>
<p>Entries which cannot be determined are set to <code class="docutils literal notranslate"><span class="pre">''</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Result changed from a tuple to a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span><a class="reference internal" href="#platform.processor" title="platform.processor"><code class="xref py py-attr docutils literal notranslate"><span class="pre">processor</span></code></a> is resolved late instead of immediately.</p>
</div>
</dd></dl>

</section>
<section id="java-platform">
<h2>Java Platform<a class="headerlink" href="#java-platform" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="platform.java_ver">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">java_ver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">release</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">vendor</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">vminfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('',</span> <span class="pre">'',</span> <span class="pre">'')</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">osinfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('',</span> <span class="pre">'',</span> <span class="pre">'')</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.java_ver" title="Link to this definition">¶</a></dt>
<dd><p>Version interface for Jython.</p>
<p>Returns a tuple <code class="docutils literal notranslate"><span class="pre">(release,</span> <span class="pre">vendor,</span> <span class="pre">vminfo,</span> <span class="pre">osinfo)</span></code> with <em>vminfo</em> being a
tuple <code class="docutils literal notranslate"><span class="pre">(vm_name,</span> <span class="pre">vm_release,</span> <span class="pre">vm_vendor)</span></code> and <em>osinfo</em> being a tuple
<code class="docutils literal notranslate"><span class="pre">(os_name,</span> <span class="pre">os_version,</span> <span class="pre">os_arch)</span></code>. Values which cannot be determined are set to
the defaults given as parameters (which all default to <code class="docutils literal notranslate"><span class="pre">''</span></code>).</p>
</dd></dl>

</section>
<section id="windows-platform">
<h2>Windows Platform<a class="headerlink" href="#windows-platform" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="platform.win32_ver">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">win32_ver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">release</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">csd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ptype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.win32_ver" title="Link to this definition">¶</a></dt>
<dd><p>Get additional version information from the Windows Registry and return a tuple
<code class="docutils literal notranslate"><span class="pre">(release,</span> <span class="pre">version,</span> <span class="pre">csd,</span> <span class="pre">ptype)</span></code> referring to OS release, version number,
CSD level (service pack) and OS type (multi/single processor). Values which
cannot be determined are set to the defaults given as parameters (which all
default to an empty string).</p>
<p>As a hint: <em>ptype</em> is <code class="docutils literal notranslate"><span class="pre">'Uniprocessor</span> <span class="pre">Free'</span></code> on single processor NT machines
and <code class="docutils literal notranslate"><span class="pre">'Multiprocessor</span> <span class="pre">Free'</span></code> on multi processor machines. The <em>‘Free’</em> refers
to the OS version being free of debugging code. It could also state <em>‘Checked’</em>
which means the OS version uses debugging code, i.e. code that checks arguments,
ranges, etc.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.win32_edition">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">win32_edition</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.win32_edition" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string representing the current Windows edition, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the
value cannot be determined.  Possible values include but are not limited to
<code class="docutils literal notranslate"><span class="pre">'Enterprise'</span></code>, <code class="docutils literal notranslate"><span class="pre">'IoTUAP'</span></code>, <code class="docutils literal notranslate"><span class="pre">'ServerStandard'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'nanoserver'</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="platform.win32_is_iot">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">win32_is_iot</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.win32_is_iot" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the Windows edition returned by <a class="reference internal" href="#platform.win32_edition" title="platform.win32_edition"><code class="xref py py-func docutils literal notranslate"><span class="pre">win32_edition()</span></code></a>
is recognized as an IoT edition.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</section>
<section id="macos-platform">
<h2>macOS Platform<a class="headerlink" href="#macos-platform" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="platform.mac_ver">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">mac_ver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">release</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">versioninfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('',</span> <span class="pre">'',</span> <span class="pre">'')</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">machine</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.mac_ver" title="Link to this definition">¶</a></dt>
<dd><p>Get macOS version information and return it as tuple <code class="docutils literal notranslate"><span class="pre">(release,</span> <span class="pre">versioninfo,</span>
<span class="pre">machine)</span></code> with <em>versioninfo</em> being a tuple <code class="docutils literal notranslate"><span class="pre">(version,</span> <span class="pre">dev_stage,</span>
<span class="pre">non_release_version)</span></code>.</p>
<p>Entries which cannot be determined are set to <code class="docutils literal notranslate"><span class="pre">''</span></code>.  All tuple entries are
strings.</p>
</dd></dl>

</section>
<section id="unix-platforms">
<h2>Unix Platforms<a class="headerlink" href="#unix-platforms" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="platform.libc_ver">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">libc_ver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">executable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">sys.executable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lib</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chunksize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">16384</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#platform.libc_ver" title="Link to this definition">¶</a></dt>
<dd><p>Tries to determine the libc version against which the file executable (defaults
to the Python interpreter) is linked.  Returns a tuple of strings <code class="docutils literal notranslate"><span class="pre">(lib,</span>
<span class="pre">version)</span></code> which default to the given parameters in case the lookup fails.</p>
<p>Note that this function has intimate knowledge of how different libc versions
add symbols to the executable is probably only usable for executables compiled
using <strong class="program">gcc</strong>.</p>
<p>The file is read and scanned in chunks of <em>chunksize</em> bytes.</p>
</dd></dl>

</section>
<section id="linux-platforms">
<h2>Linux Platforms<a class="headerlink" href="#linux-platforms" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="platform.freedesktop_os_release">
<span class="sig-prename descclassname"><span class="pre">platform.</span></span><span class="sig-name descname"><span class="pre">freedesktop_os_release</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#platform.freedesktop_os_release" title="Link to this definition">¶</a></dt>
<dd><p>Get operating system identification from <code class="docutils literal notranslate"><span class="pre">os-release</span></code> file and return
it as a dict. The <code class="docutils literal notranslate"><span class="pre">os-release</span></code> file is a <a class="reference external" href="https://www.freedesktop.org/software/systemd/man/os-release.html">freedesktop.org standard</a> and
is available in most Linux distributions. A noticeable exception is
Android and Android-based distributions.</p>
<p>Raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> or subclass when neither <code class="docutils literal notranslate"><span class="pre">/etc/os-release</span></code> nor
<code class="docutils literal notranslate"><span class="pre">/usr/lib/os-release</span></code> can be read.</p>
<p>On success, the function returns a dictionary where keys and values are
strings. Values have their special characters like <code class="docutils literal notranslate"><span class="pre">&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">$</span></code>
unquoted. The fields <code class="docutils literal notranslate"><span class="pre">NAME</span></code>, <code class="docutils literal notranslate"><span class="pre">ID</span></code>, and <code class="docutils literal notranslate"><span class="pre">PRETTY_NAME</span></code> are always
defined according to the standard. All other fields are optional. Vendors
may include additional fields.</p>
<p>Note that fields like <code class="docutils literal notranslate"><span class="pre">NAME</span></code>, <code class="docutils literal notranslate"><span class="pre">VERSION</span></code>, and <code class="docutils literal notranslate"><span class="pre">VARIANT</span></code> are strings
suitable for presentation to users. Programs should use fields like
<code class="docutils literal notranslate"><span class="pre">ID</span></code>, <code class="docutils literal notranslate"><span class="pre">ID_LIKE</span></code>, <code class="docutils literal notranslate"><span class="pre">VERSION_ID</span></code>, or <code class="docutils literal notranslate"><span class="pre">VARIANT_ID</span></code> to identify
Linux distributions.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">get_like_distro</span><span class="p">():</span>
    <span class="n">info</span> <span class="o">=</span> <span class="n">platform</span><span class="o">.</span><span class="n">freedesktop_os_release</span><span class="p">()</span>
    <span class="n">ids</span> <span class="o">=</span> <span class="p">[</span><span class="n">info</span><span class="p">[</span><span class="s2">&quot;ID&quot;</span><span class="p">]]</span>
    <span class="k">if</span> <span class="s2">&quot;ID_LIKE&quot;</span> <span class="ow">in</span> <span class="n">info</span><span class="p">:</span>
        <span class="c1"># ids are space separated and ordered by precedence</span>
        <span class="n">ids</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">info</span><span class="p">[</span><span class="s2">&quot;ID_LIKE&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">split</span><span class="p">())</span>
    <span class="k">return</span> <span class="n">ids</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a><ul>
<li><a class="reference internal" href="#cross-platform">Cross Platform</a></li>
<li><a class="reference internal" href="#java-platform">Java Platform</a></li>
<li><a class="reference internal" href="#windows-platform">Windows Platform</a></li>
<li><a class="reference internal" href="#macos-platform">macOS Platform</a></li>
<li><a class="reference internal" href="#unix-platforms">Unix Platforms</a></li>
<li><a class="reference internal" href="#linux-platforms">Linux Platforms</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="curses.panel.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses.panel</span></code> — A panel stack extension for curses</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="errno.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code> — Standard errno system symbols</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/platform.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="errno.html" title="errno — Standard errno system symbols"
             >next</a> |</li>
        <li class="right" >
          <a href="curses.panel.html" title="curses.panel — A panel stack extension for curses"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" >Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>