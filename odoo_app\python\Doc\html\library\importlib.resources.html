<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="importlib.resources – Package resource reading, opening and access" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/importlib.resources.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/importlib/resources/__init__.py This module leverages Python’s import system to provide access to resources within packages. “Resources” are file-like resources associated with a m..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/importlib/resources/__init__.py This module leverages Python’s import system to provide access to resources within packages. “Resources” are file-like resources associated with a m..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>importlib.resources – Package resource reading, opening and access &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="importlib.resources.abc – Abstract base classes for resources" href="importlib.resources.abc.html" />
    <link rel="prev" title="importlib — The implementation of import" href="importlib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/importlib.resources.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a><ul>
<li><a class="reference internal" href="#deprecated-functions">Deprecated functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="importlib.resources.abc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code> – Abstract base classes for resources</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.resources.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="importlib.resources.abc.html" title="importlib.resources.abc – Abstract base classes for resources"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="importlib.html" title="importlib — The implementation of import"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-importlib.resources">
<span id="importlib-resources-package-resource-reading-opening-and-access"></span><h1><a class="reference internal" href="#module-importlib.resources" title="importlib.resources: Package resource reading, opening, and access"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code></a> – Package resource reading, opening and access<a class="headerlink" href="#module-importlib.resources" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/resources/__init__.py">Lib/importlib/resources/__init__.py</a></p>
<hr class="docutils" />
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>This module leverages Python’s import system to provide access to <em>resources</em>
within <em>packages</em>.</p>
<p>“Resources” are file-like resources associated with a module or package in
Python. The resources may be contained directly in a package, within a
subdirectory contained in that package, or adjacent to modules outside a
package. Resources may be text or binary. As a result, Python module sources
(.py) of a package and compilation artifacts (pycache) are technically
de-facto resources of that package. In practice, however, resources are
primarily those non-Python artifacts exposed specifically by the package
author.</p>
<p>Resources can be opened or read in either binary or text mode.</p>
<p>Resources are roughly akin to files inside directories, though it’s important
to keep in mind that this is just a metaphor.  Resources and packages <strong>do
not</strong> have to exist as physical files and directories on the file system:
for example, a package and its resources can be imported from a zip file using
<a class="reference internal" href="zipimport.html#module-zipimport" title="zipimport: Support for importing Python modules from ZIP archives."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipimport</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This module provides functionality similar to <a class="reference external" href="https://setuptools.readthedocs.io/en/latest/pkg_resources.html">pkg_resources</a> <a class="reference external" href="https://setuptools.readthedocs.io/en/latest/pkg_resources.html#basic-resource-access">Basic
Resource Access</a>
without the performance overhead of that package.  This makes reading
resources included in packages easier, with more stable and consistent
semantics.</p>
<p>The standalone backport of this module provides more information
on <a class="reference external" href="https://importlib-resources.readthedocs.io/en/latest/using.html">using importlib.resources</a> and
<a class="reference external" href="https://importlib-resources.readthedocs.io/en/latest/migration.html">migrating from pkg_resources to importlib.resources</a>.</p>
</div>
<p><a class="reference internal" href="importlib.html#importlib.abc.Loader" title="importlib.abc.Loader"><code class="xref py py-class docutils literal notranslate"><span class="pre">Loaders</span></code></a> that wish to support resource reading should implement a
<code class="docutils literal notranslate"><span class="pre">get_resource_reader(fullname)</span></code> method as specified by
<a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.ResourceReader" title="importlib.resources.abc.ResourceReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.ResourceReader</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="importlib.resources.Anchor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">Anchor</span></span><a class="headerlink" href="#importlib.resources.Anchor" title="Link to this definition">¶</a></dt>
<dd><p>Represents an anchor for resources, either a <a class="reference internal" href="types.html#types.ModuleType" title="types.ModuleType"><code class="xref py py-class docutils literal notranslate"><span class="pre">module</span> <span class="pre">object</span></code></a> or a module name as a string. Defined as
<code class="docutils literal notranslate"><span class="pre">Union[str,</span> <span class="pre">ModuleType]</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.files">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">files</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">anchor</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#importlib.resources.Anchor" title="importlib.resources.Anchor"><span class="pre">Anchor</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.files" title="Link to this definition">¶</a></dt>
<dd><p>Returns a <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.Traversable" title="importlib.resources.abc.Traversable"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traversable</span></code></a> object
representing the resource container (think directory) and its resources
(think files). A Traversable may contain other containers (think
subdirectories).</p>
<p><em>anchor</em> is an optional <a class="reference internal" href="#importlib.resources.Anchor" title="importlib.resources.Anchor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Anchor</span></code></a>. If the anchor is a
package, resources are resolved from that package. If a module,
resources are resolved adjacent to that module (in the same package
or the package root). If the anchor is omitted, the caller’s module
is used.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><em>package</em> parameter was renamed to <em>anchor</em>. <em>anchor</em> can now
be a non-package module and if omitted will default to the caller’s
module. <em>package</em> is still accepted for compatibility but will raise
a <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a>. Consider passing the anchor positionally or
using <code class="docutils literal notranslate"><span class="pre">importlib_resources</span> <span class="pre">&gt;=</span> <span class="pre">5.10</span></code> for a compatible interface
on older Pythons.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.as_file">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">as_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">traversable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.as_file" title="Link to this definition">¶</a></dt>
<dd><p>Given a <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.Traversable" title="importlib.resources.abc.Traversable"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traversable</span></code></a> object representing
a file or directory, typically from <a class="reference internal" href="#importlib.resources.files" title="importlib.resources.files"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.resources.files()</span></code></a>,
return a context manager for use in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.
The context manager provides a <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a> object.</p>
<p>Exiting the context manager cleans up any temporary file or directory
created when the resource was extracted from e.g. a zip file.</p>
<p>Use <code class="docutils literal notranslate"><span class="pre">as_file</span></code> when the Traversable methods
(<code class="docutils literal notranslate"><span class="pre">read_text</span></code>, etc) are insufficient and an actual file or directory on
the file system is required.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added support for <em>traversable</em> representing a directory.</p>
</div>
</dd></dl>

<section id="deprecated-functions">
<h2>Deprecated functions<a class="headerlink" href="#deprecated-functions" title="Link to this heading">¶</a></h2>
<p>An older, deprecated set of functions is still available, but is
scheduled for removal in a future version of Python.
The main drawback of these functions is that they do not support
directories: they assume all resources are located directly within a <em>package</em>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="importlib.resources.Package">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">Package</span></span><a class="headerlink" href="#importlib.resources.Package" title="Link to this definition">¶</a></dt>
<dd><blockquote>
<div><p>Whenever a function accepts a <code class="docutils literal notranslate"><span class="pre">Package</span></code> argument, you can pass in
either a <a class="reference internal" href="types.html#types.ModuleType" title="types.ModuleType"><code class="xref py py-class docutils literal notranslate"><span class="pre">module</span> <span class="pre">object</span></code></a> or a module name
as a string.  You can only pass module objects whose
<code class="docutils literal notranslate"><span class="pre">__spec__.submodule_search_locations</span></code> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">Package</span></code> type is defined as <code class="docutils literal notranslate"><span class="pre">Union[str,</span> <span class="pre">ModuleType]</span></code>.</p>
</div></blockquote>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="importlib.resources.Resource">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">Resource</span></span><a class="headerlink" href="#importlib.resources.Resource" title="Link to this definition">¶</a></dt>
<dd><p>For <em>resource</em> arguments of the functions below, you can pass in
the name of a resource as a string or
a <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">path-like</span> <span class="pre">object</span></code></a>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">Resource</span></code> type is defined as <code class="docutils literal notranslate"><span class="pre">Union[str,</span> <span class="pre">os.PathLike]</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.open_binary">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">open_binary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.open_binary" title="Link to this definition">¶</a></dt>
<dd><p>Open for binary reading the <em>resource</em> within <em>package</em>.</p>
<p><em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.  <em>resource</em> is the name of the resource to open
within <em>package</em>; it may not contain path separators and it may not have
sub-resources (i.e. it cannot be a directory).  This function returns a
<code class="docutils literal notranslate"><span class="pre">typing.BinaryIO</span></code> instance, a binary I/O stream open for reading.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced by:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">resource</span><span class="p">)</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;rb&#39;</span><span class="p">)</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.open_text">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">open_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'utf-8'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'strict'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.open_text" title="Link to this definition">¶</a></dt>
<dd><p>Open for text reading the <em>resource</em> within <em>package</em>.  By default, the
resource is opened for reading as UTF-8.</p>
<p><em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.  <em>resource</em> is the name of the resource to open
within <em>package</em>; it may not contain path separators and it may not have
sub-resources (i.e. it cannot be a directory).  <em>encoding</em> and <em>errors</em>
have the same meaning as with built-in <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.</p>
<p>This function returns a <code class="docutils literal notranslate"><span class="pre">typing.TextIO</span></code> instance, a text I/O stream open
for reading.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced by:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">resource</span><span class="p">)</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="n">encoding</span><span class="p">)</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.read_binary">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">read_binary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.read_binary" title="Link to this definition">¶</a></dt>
<dd><p>Read and return the contents of the <em>resource</em> within <em>package</em> as
<code class="docutils literal notranslate"><span class="pre">bytes</span></code>.</p>
<p><em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.  <em>resource</em> is the name of the resource to open
within <em>package</em>; it may not contain path separators and it may not have
sub-resources (i.e. it cannot be a directory).  This function returns the
contents of the resource as <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced by:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">resource</span><span class="p">)</span><span class="o">.</span><span class="n">read_bytes</span><span class="p">()</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.read_text">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">read_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'utf-8'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'strict'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.read_text" title="Link to this definition">¶</a></dt>
<dd><p>Read and return the contents of <em>resource</em> within <em>package</em> as a <code class="docutils literal notranslate"><span class="pre">str</span></code>.
By default, the contents are read as strict UTF-8.</p>
<p><em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.  <em>resource</em> is the name of the resource to open
within <em>package</em>; it may not contain path separators and it may not have
sub-resources (i.e. it cannot be a directory).  <em>encoding</em> and <em>errors</em>
have the same meaning as with built-in <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.  This function
returns the contents of the resource as <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced by:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">resource</span><span class="p">)</span><span class="o">.</span><span class="n">read_text</span><span class="p">(</span><span class="n">encoding</span><span class="o">=</span><span class="n">encoding</span><span class="p">)</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.path">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.path" title="Link to this definition">¶</a></dt>
<dd><p>Return the path to the <em>resource</em> as an actual file system path.  This
function returns a context manager for use in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.
The context manager provides a <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a> object.</p>
<p>Exiting the context manager cleans up any temporary file created when the
resource needs to be extracted from e.g. a zip file.</p>
<p><em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.  <em>resource</em> is the name of the resource to open
within <em>package</em>; it may not contain path separators and it may not have
sub-resources (i.e. it cannot be a directory).</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced using <a class="reference internal" href="#importlib.resources.as_file" title="importlib.resources.as_file"><code class="xref py py-func docutils literal notranslate"><span class="pre">as_file()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">as_file</span><span class="p">(</span><span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">resource</span><span class="p">))</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.is_resource">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">is_resource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.is_resource" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if there is a resource named <em>name</em> in the package,
otherwise <code class="docutils literal notranslate"><span class="pre">False</span></code>.
This function does not consider directories to be resources.
<em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced by:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">resource</span><span class="p">)</span><span class="o">.</span><span class="n">is_file</span><span class="p">()</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.resources.contents">
<span class="sig-prename descclassname"><span class="pre">importlib.resources.</span></span><span class="sig-name descname"><span class="pre">contents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.contents" title="Link to this definition">¶</a></dt>
<dd><p>Return an iterable over the named items within the package.  The iterable
returns <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> resources (e.g. files) and non-resources
(e.g. directories).  The iterable does not recurse into subdirectories.</p>
<p><em>package</em> is either a name or a module object which conforms to the
<code class="docutils literal notranslate"><span class="pre">Package</span></code> requirements.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Calls to this function can be replaced by:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">resource</span><span class="o">.</span><span class="n">name</span> <span class="k">for</span> <span class="n">resource</span> <span class="ow">in</span> <span class="n">files</span><span class="p">(</span><span class="n">package</span><span class="p">)</span><span class="o">.</span><span class="n">iterdir</span><span class="p">()</span> <span class="k">if</span> <span class="n">resource</span><span class="o">.</span><span class="n">is_file</span><span class="p">())</span>
</pre></div>
</div>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a><ul>
<li><a class="reference internal" href="#deprecated-functions">Deprecated functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="importlib.resources.abc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code> – Abstract base classes for resources</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.resources.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="importlib.resources.abc.html" title="importlib.resources.abc – Abstract base classes for resources"
             >next</a> |</li>
        <li class="right" >
          <a href="importlib.html" title="importlib — The implementation of import"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>