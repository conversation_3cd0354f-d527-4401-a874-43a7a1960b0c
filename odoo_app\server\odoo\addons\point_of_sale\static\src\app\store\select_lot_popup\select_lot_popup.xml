<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.EditListPopup">
        <div class="popup popup-text edit-list-popup" t-ref="root">
            <div class="modal-header">
                <h4 class="modal-title title">
                    <t t-esc="props.title" />
                </h4>
                <span class="sub-title" t-esc="props.name"/>
            </div>
            <main class="modal-body">
                <t t-foreach="state.array" t-as="item" t-key="item._id">
                    <EditListInput item="item" createNewItem.bind="createNewItem" removeItem="() => this.removeItem(item._id)"
                                    deletable="_hasMoreThanOneItem()" onInputChange.bind="onInputChange" />
                </t>
            </main>
            <footer class="footer footer-flex modal-footer">
                <div class="button confirm highlight btn btn-lg btn-primary" t-on-click="confirm">
                    Ok
                </div>
                <div class="button cancel btn btn-lg btn-secondary" t-on-click="cancel">
                    Cancel
                </div>
            </footer>
        </div>
    </t>

</templates>
