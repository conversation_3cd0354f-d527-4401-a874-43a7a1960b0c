<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="zoneinfo — IANA time zone support" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/zoneinfo.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/zoneinfo The zoneinfo module provides a concrete time zone implementation to support the IANA time zone database as originally specified in PEP 615. By default, zoneinfo uses the s..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/zoneinfo The zoneinfo module provides a concrete time zone implementation to support the IANA time zone database as originally specified in PEP 615. By default, zoneinfo uses the s..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>zoneinfo — IANA time zone support &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="calendar — General calendar-related functions" href="calendar.html" />
    <link rel="prev" title="datetime — Basic date and time types" href="datetime.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/zoneinfo.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code> — IANA time zone support</a><ul>
<li><a class="reference internal" href="#using-zoneinfo">Using <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a></li>
<li><a class="reference internal" href="#data-sources">Data sources</a><ul>
<li><a class="reference internal" href="#configuring-the-data-sources">Configuring the data sources</a><ul>
<li><a class="reference internal" href="#compile-time-configuration">Compile-time configuration</a></li>
<li><a class="reference internal" href="#environment-configuration">Environment configuration</a></li>
<li><a class="reference internal" href="#runtime-configuration">Runtime configuration</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#the-zoneinfo-class">The <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> class</a><ul>
<li><a class="reference internal" href="#string-representations">String representations</a></li>
<li><a class="reference internal" href="#pickle-serialization">Pickle serialization</a></li>
</ul>
</li>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#globals">Globals</a></li>
<li><a class="reference internal" href="#exceptions-and-warnings">Exceptions and warnings</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="datetime.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code> — Basic date and time types</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="calendar.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">calendar</span></code> — General calendar-related functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/zoneinfo.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="calendar.html" title="calendar — General calendar-related functions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="datetime.html" title="datetime — Basic date and time types"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" accesskey="U">Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code> — IANA time zone support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-zoneinfo">
<span id="zoneinfo-iana-time-zone-support"></span><h1><a class="reference internal" href="#module-zoneinfo" title="zoneinfo: IANA time zone support"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code></a> — IANA time zone support<a class="headerlink" href="#module-zoneinfo" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/zoneinfo">Lib/zoneinfo</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-zoneinfo" title="zoneinfo: IANA time zone support"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code></a> module provides a concrete time zone implementation to
support the IANA time zone database as originally specified in <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0615/"><strong>PEP 615</strong></a>. By
default, <a class="reference internal" href="#module-zoneinfo" title="zoneinfo: IANA time zone support"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code></a> uses the system’s time zone data if available; if no
system time zone data is available, the library will fall back to using the
first-party <a class="reference external" href="https://pypi.org/project/tzdata/">tzdata</a> package available on PyPI.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module: <a class="reference internal" href="datetime.html#module-datetime" title="datetime: Basic date and time types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code></a></dt><dd><p>Provides the <a class="reference internal" href="datetime.html#datetime.time" title="datetime.time"><code class="xref py py-class docutils literal notranslate"><span class="pre">time</span></code></a> and <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime</span></code></a>
types with which the <a class="reference internal" href="#zoneinfo.ZoneInfo" title="zoneinfo.ZoneInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a> class is designed to be used.</p>
</dd>
<dt>Package <a class="reference external" href="https://pypi.org/project/tzdata/">tzdata</a></dt><dd><p>First-party package maintained by the CPython core developers to supply
time zone data via PyPI.</p>
</dd>
</dl>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<section id="using-zoneinfo">
<h2>Using <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code><a class="headerlink" href="#using-zoneinfo" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#zoneinfo.ZoneInfo" title="zoneinfo.ZoneInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a> is a concrete implementation of the <a class="reference internal" href="datetime.html#datetime.tzinfo" title="datetime.tzinfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.tzinfo</span></code></a>
abstract base class, and is intended to be attached to <code class="docutils literal notranslate"><span class="pre">tzinfo</span></code>, either via
the constructor, the <a class="reference internal" href="datetime.html#datetime.datetime.replace" title="datetime.datetime.replace"><code class="xref py py-meth docutils literal notranslate"><span class="pre">datetime.replace</span></code></a>
method or <a class="reference internal" href="datetime.html#datetime.datetime.astimezone" title="datetime.datetime.astimezone"><code class="xref py py-meth docutils literal notranslate"><span class="pre">datetime.astimezone</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">zoneinfo</span> <span class="kn">import</span> <span class="n">ZoneInfo</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="p">(</span><span class="mi">2020</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">31</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="n">tzinfo</span><span class="o">=</span><span class="n">ZoneInfo</span><span class="p">(</span><span class="s2">&quot;America/Los_Angeles&quot;</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">dt</span><span class="p">)</span>
<span class="go">2020-10-31 12:00:00-07:00</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">dt</span><span class="o">.</span><span class="n">tzname</span><span class="p">()</span>
<span class="go">&#39;PDT&#39;</span>
</pre></div>
</div>
<p>Datetimes constructed in this way are compatible with datetime arithmetic and
handle daylight saving time transitions with no further intervention:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">dt_add</span> <span class="o">=</span> <span class="n">dt</span> <span class="o">+</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">days</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">dt_add</span><span class="p">)</span>
<span class="go">2020-11-01 12:00:00-08:00</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">dt_add</span><span class="o">.</span><span class="n">tzname</span><span class="p">()</span>
<span class="go">&#39;PST&#39;</span>
</pre></div>
</div>
<p>These time zones also support the <a class="reference internal" href="datetime.html#datetime.datetime.fold" title="datetime.datetime.fold"><code class="xref py py-attr docutils literal notranslate"><span class="pre">fold</span></code></a> attribute
introduced in <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0495/"><strong>PEP 495</strong></a>.  During offset transitions which induce ambiguous
times (such as a daylight saving time to standard time transition), the offset
from <em>before</em> the transition is used when <code class="docutils literal notranslate"><span class="pre">fold=0</span></code>, and the offset <em>after</em>
the transition is used when <code class="docutils literal notranslate"><span class="pre">fold=1</span></code>, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="p">(</span><span class="mi">2020</span><span class="p">,</span> <span class="mi">11</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">tzinfo</span><span class="o">=</span><span class="n">ZoneInfo</span><span class="p">(</span><span class="s2">&quot;America/Los_Angeles&quot;</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">dt</span><span class="p">)</span>
<span class="go">2020-11-01 01:00:00-07:00</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">dt</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="n">fold</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span>
<span class="go">2020-11-01 01:00:00-08:00</span>
</pre></div>
</div>
<p>When converting from another time zone, the fold will be set to the correct
value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">timezone</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">LOS_ANGELES</span> <span class="o">=</span> <span class="n">ZoneInfo</span><span class="p">(</span><span class="s2">&quot;America/Los_Angeles&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dt_utc</span> <span class="o">=</span> <span class="n">datetime</span><span class="p">(</span><span class="mi">2020</span><span class="p">,</span> <span class="mi">11</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">tzinfo</span><span class="o">=</span><span class="n">timezone</span><span class="o">.</span><span class="n">utc</span><span class="p">)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Before the PDT -&gt; PST transition</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">dt_utc</span><span class="o">.</span><span class="n">astimezone</span><span class="p">(</span><span class="n">LOS_ANGELES</span><span class="p">))</span>
<span class="go">2020-11-01 01:00:00-07:00</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># After the PDT -&gt; PST transition</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">((</span><span class="n">dt_utc</span> <span class="o">+</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">hours</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span><span class="o">.</span><span class="n">astimezone</span><span class="p">(</span><span class="n">LOS_ANGELES</span><span class="p">))</span>
<span class="go">2020-11-01 01:00:00-08:00</span>
</pre></div>
</div>
</section>
<section id="data-sources">
<h2>Data sources<a class="headerlink" href="#data-sources" title="Link to this heading">¶</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">zoneinfo</span></code> module does not directly provide time zone data, and instead
pulls time zone information from the system time zone database or the
first-party PyPI package <a class="reference external" href="https://pypi.org/project/tzdata/">tzdata</a>, if available. Some systems, including
notably Windows systems, do not have an IANA database available, and so for
projects targeting cross-platform compatibility that require time zone data, it
is recommended to declare a dependency on tzdata. If neither system data nor
tzdata are available, all calls to <a class="reference internal" href="#zoneinfo.ZoneInfo" title="zoneinfo.ZoneInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a> will raise
<a class="reference internal" href="#zoneinfo.ZoneInfoNotFoundError" title="zoneinfo.ZoneInfoNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ZoneInfoNotFoundError</span></code></a>.</p>
<section id="configuring-the-data-sources">
<span id="zoneinfo-data-configuration"></span><h3>Configuring the data sources<a class="headerlink" href="#configuring-the-data-sources" title="Link to this heading">¶</a></h3>
<p>When <code class="docutils literal notranslate"><span class="pre">ZoneInfo(key)</span></code> is called, the constructor first searches the
directories specified in <a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a> for a file matching <code class="docutils literal notranslate"><span class="pre">key</span></code>, and on
failure looks for a match in the tzdata package. This behavior can be
configured in three ways:</p>
<ol class="arabic simple">
<li><p>The default <a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a> when not otherwise specified can be configured at
<a class="reference internal" href="#zoneinfo-data-compile-time-config"><span class="std std-ref">compile time</span></a>.</p></li>
<li><p><a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a> can be configured using <a class="reference internal" href="#zoneinfo-data-environment-var"><span class="std std-ref">an environment variable</span></a>.</p></li>
<li><p>At <a class="reference internal" href="#zoneinfo-data-runtime-config"><span class="std std-ref">runtime</span></a>, the search path can be
manipulated using the <a class="reference internal" href="#zoneinfo.reset_tzpath" title="zoneinfo.reset_tzpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_tzpath()</span></code></a> function.</p></li>
</ol>
<section id="compile-time-configuration">
<span id="zoneinfo-data-compile-time-config"></span><h4>Compile-time configuration<a class="headerlink" href="#compile-time-configuration" title="Link to this heading">¶</a></h4>
<p>The default <a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a> includes several common deployment locations for the
time zone database (except on Windows, where there are no “well-known”
locations for time zone data). On POSIX systems, downstream distributors and
those building Python from source who know where their system
time zone data is deployed may change the default time zone path by specifying
the compile-time option <code class="docutils literal notranslate"><span class="pre">TZPATH</span></code> (or, more likely, the <a class="reference internal" href="../using/configure.html#cmdoption-with-tzpath"><code class="xref std std-option docutils literal notranslate"><span class="pre">configure</span>
<span class="pre">flag</span> <span class="pre">--with-tzpath</span></code></a>), which should be a string delimited by
<a class="reference internal" href="os.html#os.pathsep" title="os.pathsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.pathsep</span></code></a>.</p>
<p>On all platforms, the configured value is available as the <code class="docutils literal notranslate"><span class="pre">TZPATH</span></code> key in
<a class="reference internal" href="sysconfig.html#sysconfig.get_config_var" title="sysconfig.get_config_var"><code class="xref py py-func docutils literal notranslate"><span class="pre">sysconfig.get_config_var()</span></code></a>.</p>
</section>
<section id="environment-configuration">
<span id="zoneinfo-data-environment-var"></span><h4>Environment configuration<a class="headerlink" href="#environment-configuration" title="Link to this heading">¶</a></h4>
<p>When initializing <a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a> (either at import time or whenever
<a class="reference internal" href="#zoneinfo.reset_tzpath" title="zoneinfo.reset_tzpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_tzpath()</span></code></a> is called with no arguments), the <code class="docutils literal notranslate"><span class="pre">zoneinfo</span></code> module will
use the environment variable <code class="docutils literal notranslate"><span class="pre">PYTHONTZPATH</span></code>, if it exists, to set the search
path.</p>
<dl class="std envvar">
<dt class="sig sig-object std" id="envvar-PYTHONTZPATH">
<span class="sig-name descname"><span class="pre">PYTHONTZPATH</span></span><a class="headerlink" href="#envvar-PYTHONTZPATH" title="Link to this definition">¶</a></dt>
<dd><p>This is an <a class="reference internal" href="os.html#os.pathsep" title="os.pathsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.pathsep</span></code></a>-separated string containing the time zone
search path to use. It must consist of only absolute rather than relative
paths. Relative components specified in <code class="docutils literal notranslate"><span class="pre">PYTHONTZPATH</span></code> will not be used,
but otherwise the behavior when a relative path is specified is
implementation-defined; CPython will raise <a class="reference internal" href="#zoneinfo.InvalidTZPathWarning" title="zoneinfo.InvalidTZPathWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InvalidTZPathWarning</span></code></a>, but
other implementations are free to silently ignore the erroneous component
or raise an exception.</p>
</dd></dl>

<p>To set the system to ignore the system data and use the tzdata package
instead, set <code class="docutils literal notranslate"><span class="pre">PYTHONTZPATH=&quot;&quot;</span></code>.</p>
</section>
<section id="runtime-configuration">
<span id="zoneinfo-data-runtime-config"></span><h4>Runtime configuration<a class="headerlink" href="#runtime-configuration" title="Link to this heading">¶</a></h4>
<p>The TZ search path can also be configured at runtime using the
<a class="reference internal" href="#zoneinfo.reset_tzpath" title="zoneinfo.reset_tzpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_tzpath()</span></code></a> function. This is generally not an advisable operation,
though it is reasonable to use it in test functions that require the use of a
specific time zone path (or require disabling access to the system time zones).</p>
</section>
</section>
</section>
<section id="the-zoneinfo-class">
<h2>The <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> class<a class="headerlink" href="#the-zoneinfo-class" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="zoneinfo.ZoneInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zoneinfo.</span></span><span class="sig-name descname"><span class="pre">ZoneInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zoneinfo.ZoneInfo" title="Link to this definition">¶</a></dt>
<dd><p>A concrete <a class="reference internal" href="datetime.html#datetime.tzinfo" title="datetime.tzinfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.tzinfo</span></code></a> subclass that represents an IANA time
zone specified by the string <code class="docutils literal notranslate"><span class="pre">key</span></code>. Calls to the primary constructor will
always return objects that compare identically; put another way, barring
cache invalidation via <a class="reference internal" href="#zoneinfo.ZoneInfo.clear_cache" title="zoneinfo.ZoneInfo.clear_cache"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ZoneInfo.clear_cache()</span></code></a>, for all values of
<code class="docutils literal notranslate"><span class="pre">key</span></code>, the following assertion will always be true:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">a</span> <span class="o">=</span> <span class="n">ZoneInfo</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
<span class="n">b</span> <span class="o">=</span> <span class="n">ZoneInfo</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">a</span> <span class="ow">is</span> <span class="n">b</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">key</span></code> must be in the form of a relative, normalized POSIX path, with no
up-level references. The constructor will raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if a
non-conforming key is passed.</p>
<p>If no file matching <code class="docutils literal notranslate"><span class="pre">key</span></code> is found, the constructor will raise
<a class="reference internal" href="#zoneinfo.ZoneInfoNotFoundError" title="zoneinfo.ZoneInfoNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ZoneInfoNotFoundError</span></code></a>.</p>
</dd></dl>

<p>The <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> class has two alternate constructors:</p>
<dl class="py method">
<dt class="sig sig-object py" id="zoneinfo.ZoneInfo.from_file">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ZoneInfo.</span></span><span class="sig-name descname"><span class="pre">from_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fobj</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zoneinfo.ZoneInfo.from_file" title="Link to this definition">¶</a></dt>
<dd><p>Constructs a <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> object from a file-like object returning bytes
(e.g. a file opened in binary mode or an <a class="reference internal" href="io.html#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BytesIO</span></code></a> object).
Unlike the primary constructor, this always constructs a new object.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">key</span></code> parameter sets the name of the zone for the purposes of
<a class="reference internal" href="../reference/datamodel.html#object.__str__" title="object.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__repr__" title="object.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a>.</p>
<p>Objects created via this constructor cannot be pickled (see <a class="reference internal" href="#pickling">pickling</a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zoneinfo.ZoneInfo.no_cache">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ZoneInfo.</span></span><span class="sig-name descname"><span class="pre">no_cache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zoneinfo.ZoneInfo.no_cache" title="Link to this definition">¶</a></dt>
<dd><p>An alternate constructor that bypasses the constructor’s cache. It is
identical to the primary constructor, but returns a new object on each
call. This is most likely to be useful for testing or demonstration
purposes, but it can also be used to create a system with a different cache
invalidation strategy.</p>
<p>Objects created via this constructor will also bypass the cache of a
deserializing process when unpickled.</p>
<div class="admonition caution">
<p class="admonition-title">Caution</p>
<p>Using this constructor may change the semantics of your datetimes in
surprising ways, only use it if you know that you need to.</p>
</div>
</dd></dl>

<p>The following class methods are also available:</p>
<dl class="py method">
<dt class="sig sig-object py" id="zoneinfo.ZoneInfo.clear_cache">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ZoneInfo.</span></span><span class="sig-name descname"><span class="pre">clear_cache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">only_keys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zoneinfo.ZoneInfo.clear_cache" title="Link to this definition">¶</a></dt>
<dd><p>A method for invalidating the cache on the <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> class. If no
arguments are passed, all caches are invalidated and the next call to
the primary constructor for each key will return a new instance.</p>
<p>If an iterable of key names is passed to the <code class="docutils literal notranslate"><span class="pre">only_keys</span></code> parameter, only
the specified keys will be removed from the cache. Keys passed to
<code class="docutils literal notranslate"><span class="pre">only_keys</span></code> but not found in the cache are ignored.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Invoking this function may change the semantics of datetimes using
<code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> in surprising ways; this modifies module state
and thus may have wide-ranging effects. Only use it if you know that you
need to.</p>
</div>
</dd></dl>

<p>The class has one attribute:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="zoneinfo.ZoneInfo.key">
<span class="sig-prename descclassname"><span class="pre">ZoneInfo.</span></span><span class="sig-name descname"><span class="pre">key</span></span><a class="headerlink" href="#zoneinfo.ZoneInfo.key" title="Link to this definition">¶</a></dt>
<dd><p>This is a read-only <a class="reference internal" href="../glossary.html#term-attribute"><span class="xref std std-term">attribute</span></a> that returns the value of <code class="docutils literal notranslate"><span class="pre">key</span></code>
passed to the constructor, which should be a lookup key in the IANA time
zone database (e.g. <code class="docutils literal notranslate"><span class="pre">America/New_York</span></code>, <code class="docutils literal notranslate"><span class="pre">Europe/Paris</span></code> or
<code class="docutils literal notranslate"><span class="pre">Asia/Tokyo</span></code>).</p>
<p>For zones constructed from file without specifying a <code class="docutils literal notranslate"><span class="pre">key</span></code> parameter,
this will be set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Although it is a somewhat common practice to expose these to end users,
these values are designed to be primary keys for representing the
relevant zones and not necessarily user-facing elements.  Projects like
CLDR (the Unicode Common Locale Data Repository) can be used to get
more user-friendly strings from these keys.</p>
</div>
</dd></dl>

<section id="string-representations">
<h3>String representations<a class="headerlink" href="#string-representations" title="Link to this heading">¶</a></h3>
<p>The string representation returned when calling <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> on a
<a class="reference internal" href="#zoneinfo.ZoneInfo" title="zoneinfo.ZoneInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a> object defaults to using the <a class="reference internal" href="#zoneinfo.ZoneInfo.key" title="zoneinfo.ZoneInfo.key"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ZoneInfo.key</span></code></a> attribute (see
the note on usage in the attribute documentation):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">zone</span> <span class="o">=</span> <span class="n">ZoneInfo</span><span class="p">(</span><span class="s2">&quot;Pacific/Kwajalein&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">zone</span><span class="p">)</span>
<span class="go">&#39;Pacific/Kwajalein&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="p">(</span><span class="mi">2020</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="n">tzinfo</span><span class="o">=</span><span class="n">zone</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">dt</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span><span class="si">}</span><span class="s2"> [</span><span class="si">{</span><span class="n">dt</span><span class="o">.</span><span class="n">tzinfo</span><span class="si">}</span><span class="s2">]&quot;</span>
<span class="go">&#39;2020-04-01T03:15:00+12:00 [Pacific/Kwajalein]&#39;</span>
</pre></div>
</div>
<p>For objects constructed from a file without specifying a <code class="docutils literal notranslate"><span class="pre">key</span></code> parameter,
<code class="docutils literal notranslate"><span class="pre">str</span></code> falls back to calling <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>. <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code>’s <code class="docutils literal notranslate"><span class="pre">repr</span></code> is
implementation-defined and not necessarily stable between versions, but it is
guaranteed not to be a valid <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> key.</p>
</section>
<section id="pickle-serialization">
<span id="pickling"></span><h3>Pickle serialization<a class="headerlink" href="#pickle-serialization" title="Link to this heading">¶</a></h3>
<p>Rather than serializing all transition data, <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> objects are
serialized by key, and <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> objects constructed from files (even those
with a value for <code class="docutils literal notranslate"><span class="pre">key</span></code> specified) cannot be pickled.</p>
<p>The behavior of a <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> file depends on how it was constructed:</p>
<ol class="arabic">
<li><p><code class="docutils literal notranslate"><span class="pre">ZoneInfo(key)</span></code>: When constructed with the primary constructor, a
<code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> object is serialized by key, and when deserialized, the
deserializing process uses the primary and thus it is expected that these
are expected to be the same object as other references to the same time
zone.  For example, if <code class="docutils literal notranslate"><span class="pre">europe_berlin_pkl</span></code> is a string containing a pickle
constructed from <code class="docutils literal notranslate"><span class="pre">ZoneInfo(&quot;Europe/Berlin&quot;)</span></code>, one would expect the
following behavior:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">ZoneInfo</span><span class="p">(</span><span class="s2">&quot;Europe/Berlin&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">pickle</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">europe_berlin_pkl</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="n">b</span>
<span class="go">True</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">ZoneInfo.no_cache(key)</span></code>: When constructed from the cache-bypassing
constructor, the <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> object is also serialized by key, but when
deserialized, the deserializing process uses the cache bypassing
constructor. If <code class="docutils literal notranslate"><span class="pre">europe_berlin_pkl_nc</span></code> is a string containing a pickle
constructed from <code class="docutils literal notranslate"><span class="pre">ZoneInfo.no_cache(&quot;Europe/Berlin&quot;)</span></code>, one would expect
the following behavior:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">ZoneInfo</span><span class="p">(</span><span class="s2">&quot;Europe/Berlin&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">pickle</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">europe_berlin_pkl_nc</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="n">b</span>
<span class="go">False</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">ZoneInfo.from_file(fobj,</span> <span class="pre">/,</span> <span class="pre">key=None)</span></code>: When constructed from a file, the
<code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> object raises an exception on pickling. If an end user wants to
pickle a <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> constructed from a file, it is recommended that they
use a wrapper type or a custom serialization function: either serializing by
key or storing the contents of the file object and serializing that.</p></li>
</ol>
<p>This method of serialization requires that the time zone data for the required
key be available on both the serializing and deserializing side, similar to the
way that references to classes and functions are expected to exist in both the
serializing and deserializing environments. It also means that no guarantees
are made about the consistency of results when unpickling a <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code>
pickled in an environment with a different version of the time zone data.</p>
</section>
</section>
<section id="functions">
<h2>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="zoneinfo.available_timezones">
<span class="sig-prename descclassname"><span class="pre">zoneinfo.</span></span><span class="sig-name descname"><span class="pre">available_timezones</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zoneinfo.available_timezones" title="Link to this definition">¶</a></dt>
<dd><p>Get a set containing all the valid keys for IANA time zones available
anywhere on the time zone path. This is recalculated on every call to the
function.</p>
<p>This function only includes canonical zone names and does not include
“special” zones such as those under the <code class="docutils literal notranslate"><span class="pre">posix/</span></code> and <code class="docutils literal notranslate"><span class="pre">right/</span></code>
directories, or the <code class="docutils literal notranslate"><span class="pre">posixrules</span></code> zone.</p>
<div class="admonition caution">
<p class="admonition-title">Caution</p>
<p>This function may open a large number of files, as the best way to
determine if a file on the time zone path is a valid time zone is to
read the “magic string” at the beginning.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These values are not designed to be exposed to end-users; for user
facing elements, applications should use something like CLDR (the
Unicode Common Locale Data Repository) to get more user-friendly
strings. See also the cautionary note on <a class="reference internal" href="#zoneinfo.ZoneInfo.key" title="zoneinfo.ZoneInfo.key"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ZoneInfo.key</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zoneinfo.reset_tzpath">
<span class="sig-prename descclassname"><span class="pre">zoneinfo.</span></span><span class="sig-name descname"><span class="pre">reset_tzpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">to</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zoneinfo.reset_tzpath" title="Link to this definition">¶</a></dt>
<dd><p>Sets or resets the time zone search path (<a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a>) for the module.
When called with no arguments, <a class="reference internal" href="#zoneinfo.TZPATH" title="zoneinfo.TZPATH"><code class="xref py py-data docutils literal notranslate"><span class="pre">TZPATH</span></code></a> is set to the default value.</p>
<p>Calling <code class="docutils literal notranslate"><span class="pre">reset_tzpath</span></code> will not invalidate the <a class="reference internal" href="#zoneinfo.ZoneInfo" title="zoneinfo.ZoneInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a> cache,
and so calls to the primary <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> constructor will only use the new
<code class="docutils literal notranslate"><span class="pre">TZPATH</span></code> in the case of a cache miss.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">to</span></code> parameter must be a <a class="reference internal" href="../glossary.html#term-sequence"><span class="xref std std-term">sequence</span></a> of strings or
<a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a> and not a string, all of which must be absolute paths.
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be raised if something other than an absolute path
is passed.</p>
</dd></dl>

</section>
<section id="globals">
<h2>Globals<a class="headerlink" href="#globals" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="zoneinfo.TZPATH">
<span class="sig-prename descclassname"><span class="pre">zoneinfo.</span></span><span class="sig-name descname"><span class="pre">TZPATH</span></span><a class="headerlink" href="#zoneinfo.TZPATH" title="Link to this definition">¶</a></dt>
<dd><p>A read-only sequence representing the time zone search path – when
constructing a <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> from a key, the key is joined to each entry in
the <code class="docutils literal notranslate"><span class="pre">TZPATH</span></code>, and the first file found is used.</p>
<p><code class="docutils literal notranslate"><span class="pre">TZPATH</span></code> may contain only absolute paths, never relative paths,
regardless of how it is configured.</p>
<p>The object that <code class="docutils literal notranslate"><span class="pre">zoneinfo.TZPATH</span></code> points to may change in response to a
call to <a class="reference internal" href="#zoneinfo.reset_tzpath" title="zoneinfo.reset_tzpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_tzpath()</span></code></a>, so it is recommended to use
<code class="docutils literal notranslate"><span class="pre">zoneinfo.TZPATH</span></code> rather than importing <code class="docutils literal notranslate"><span class="pre">TZPATH</span></code> from <code class="docutils literal notranslate"><span class="pre">zoneinfo</span></code> or
assigning a long-lived variable to <code class="docutils literal notranslate"><span class="pre">zoneinfo.TZPATH</span></code>.</p>
<p>For more information on configuring the time zone search path, see
<a class="reference internal" href="#zoneinfo-data-configuration"><span class="std std-ref">Configuring the data sources</span></a>.</p>
</dd></dl>

</section>
<section id="exceptions-and-warnings">
<h2>Exceptions and warnings<a class="headerlink" href="#exceptions-and-warnings" title="Link to this heading">¶</a></h2>
<dl class="py exception">
<dt class="sig sig-object py" id="zoneinfo.ZoneInfoNotFoundError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zoneinfo.</span></span><span class="sig-name descname"><span class="pre">ZoneInfoNotFoundError</span></span><a class="headerlink" href="#zoneinfo.ZoneInfoNotFoundError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when construction of a <a class="reference internal" href="#zoneinfo.ZoneInfo" title="zoneinfo.ZoneInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a> object fails because the
specified key could not be found on the system. This is a subclass of
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="zoneinfo.InvalidTZPathWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zoneinfo.</span></span><span class="sig-name descname"><span class="pre">InvalidTZPathWarning</span></span><a class="headerlink" href="#zoneinfo.InvalidTZPathWarning" title="Link to this definition">¶</a></dt>
<dd><p>Raised when <span class="target" id="index-2"></span><a class="reference internal" href="#envvar-PYTHONTZPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONTZPATH</span></code></a> contains an invalid component that will
be filtered out, such as a relative path.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code> — IANA time zone support</a><ul>
<li><a class="reference internal" href="#using-zoneinfo">Using <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code></a></li>
<li><a class="reference internal" href="#data-sources">Data sources</a><ul>
<li><a class="reference internal" href="#configuring-the-data-sources">Configuring the data sources</a><ul>
<li><a class="reference internal" href="#compile-time-configuration">Compile-time configuration</a></li>
<li><a class="reference internal" href="#environment-configuration">Environment configuration</a></li>
<li><a class="reference internal" href="#runtime-configuration">Runtime configuration</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#the-zoneinfo-class">The <code class="docutils literal notranslate"><span class="pre">ZoneInfo</span></code> class</a><ul>
<li><a class="reference internal" href="#string-representations">String representations</a></li>
<li><a class="reference internal" href="#pickle-serialization">Pickle serialization</a></li>
</ul>
</li>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#globals">Globals</a></li>
<li><a class="reference internal" href="#exceptions-and-warnings">Exceptions and warnings</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="datetime.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code> — Basic date and time types</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="calendar.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">calendar</span></code> — General calendar-related functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/zoneinfo.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="calendar.html" title="calendar — General calendar-related functions"
             >next</a> |</li>
        <li class="right" >
          <a href="datetime.html" title="datetime — Basic date and time types"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" >Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">zoneinfo</span></code> — IANA time zone support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>