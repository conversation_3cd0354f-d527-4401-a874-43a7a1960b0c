<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xml.sax.handler — Base classes for SAX handlers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xml.sax.handler.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xml/sax/handler.py The SAX API defines five kinds of handlers: content handlers, DTD handlers, error handlers, entity resolvers and lexical handlers. Applications normally only nee..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xml/sax/handler.py The SAX API defines five kinds of handlers: content handlers, DTD handlers, error handlers, entity resolvers and lexical handlers. Applications normally only nee..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xml.sax.handler — Base classes for SAX handlers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xml.sax.saxutils — SAX Utilities" href="xml.sax.utils.html" />
    <link rel="prev" title="xml.sax — Support for SAX2 parsers" href="xml.sax.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xml.sax.handler.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code> — Base classes for SAX handlers</a><ul>
<li><a class="reference internal" href="#contenthandler-objects">ContentHandler Objects</a></li>
<li><a class="reference internal" href="#dtdhandler-objects">DTDHandler Objects</a></li>
<li><a class="reference internal" href="#entityresolver-objects">EntityResolver Objects</a></li>
<li><a class="reference internal" href="#errorhandler-objects">ErrorHandler Objects</a></li>
<li><a class="reference internal" href="#lexicalhandler-objects">LexicalHandler Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.sax.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax</span></code> — Support for SAX2 parsers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.sax.utils.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.saxutils</span></code> — SAX Utilities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.sax.handler.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.sax.utils.html" title="xml.sax.saxutils — SAX Utilities"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xml.sax.html" title="xml.sax — Support for SAX2 parsers"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code> — Base classes for SAX handlers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml.sax.handler">
<span id="xml-sax-handler-base-classes-for-sax-handlers"></span><h1><a class="reference internal" href="#module-xml.sax.handler" title="xml.sax.handler: Base classes for SAX event handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code></a> — Base classes for SAX handlers<a class="headerlink" href="#module-xml.sax.handler" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xml/sax/handler.py">Lib/xml/sax/handler.py</a></p>
<hr class="docutils" />
<p>The SAX API defines five kinds of handlers: content handlers, DTD handlers,
error handlers, entity resolvers and lexical handlers. Applications normally
only need to implement those interfaces whose events they are interested in;
they can implement the interfaces in a single object or in multiple objects.
Handler implementations should inherit from the base classes provided in the
module <a class="reference internal" href="#module-xml.sax.handler" title="xml.sax.handler: Base classes for SAX event handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code></a>, so that all methods get default implementations.</p>
<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">ContentHandler</span></span><a class="headerlink" href="#xml.sax.handler.ContentHandler" title="Link to this definition">¶</a></dt>
<dd><p>This is the main callback interface in SAX, and the one most important to
applications. The order of events in this interface mirrors the order of the
information in the document.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.handler.DTDHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">DTDHandler</span></span><a class="headerlink" href="#xml.sax.handler.DTDHandler" title="Link to this definition">¶</a></dt>
<dd><p>Handle DTD events.</p>
<p>This interface specifies only those DTD events required for basic parsing
(unparsed entities and attributes).</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.handler.EntityResolver">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">EntityResolver</span></span><a class="headerlink" href="#xml.sax.handler.EntityResolver" title="Link to this definition">¶</a></dt>
<dd><p>Basic interface for resolving entities. If you create an object implementing
this interface, then register the object with your Parser, the parser will call
the method in your object to resolve all external entities.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.handler.ErrorHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">ErrorHandler</span></span><a class="headerlink" href="#xml.sax.handler.ErrorHandler" title="Link to this definition">¶</a></dt>
<dd><p>Interface used by the parser to present error and warning messages to the
application.  The methods of this object control whether errors are immediately
converted to exceptions or are handled in some other way.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.handler.LexicalHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">LexicalHandler</span></span><a class="headerlink" href="#xml.sax.handler.LexicalHandler" title="Link to this definition">¶</a></dt>
<dd><p>Interface used by the parser to represent low frequency events which may not
be of interest to many applications.</p>
</dd></dl>

<p>In addition to these classes, <a class="reference internal" href="#module-xml.sax.handler" title="xml.sax.handler: Base classes for SAX event handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code></a> provides symbolic constants
for the feature and property names.</p>
<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.feature_namespaces">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">feature_namespaces</span></span><a class="headerlink" href="#xml.sax.handler.feature_namespaces" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/features/namespaces&quot;</span></code></div>
<div class="line">true: Perform Namespace processing.</div>
<div class="line">false: Optionally do not perform Namespace processing (implies
namespace-prefixes; default).</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.feature_namespace_prefixes">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">feature_namespace_prefixes</span></span><a class="headerlink" href="#xml.sax.handler.feature_namespace_prefixes" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/features/namespace-prefixes&quot;</span></code></div>
<div class="line">true: Report the original prefixed names and attributes used for Namespace
declarations.</div>
<div class="line">false: Do not report attributes used for Namespace declarations, and
optionally do not report original prefixed names (default).</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.feature_string_interning">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">feature_string_interning</span></span><a class="headerlink" href="#xml.sax.handler.feature_string_interning" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/features/string-interning&quot;</span></code></div>
<div class="line">true: All element names, prefixes, attribute names, Namespace URIs, and
local names are interned using the built-in intern function.</div>
<div class="line">false: Names are not necessarily interned, although they may be (default).</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.feature_validation">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">feature_validation</span></span><a class="headerlink" href="#xml.sax.handler.feature_validation" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/features/validation&quot;</span></code></div>
<div class="line">true: Report all validation errors (implies external-general-entities and
external-parameter-entities).</div>
<div class="line">false: Do not report validation errors.</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.feature_external_ges">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">feature_external_ges</span></span><a class="headerlink" href="#xml.sax.handler.feature_external_ges" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/features/external-general-entities&quot;</span></code></div>
<div class="line">true: Include all external general (text) entities.</div>
<div class="line">false: Do not include external general entities.</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.feature_external_pes">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">feature_external_pes</span></span><a class="headerlink" href="#xml.sax.handler.feature_external_pes" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/features/external-parameter-entities&quot;</span></code></div>
<div class="line">true: Include all external parameter entities, including the external DTD
subset.</div>
<div class="line">false: Do not include any external parameter entities, even the external
DTD subset.</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.all_features">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">all_features</span></span><a class="headerlink" href="#xml.sax.handler.all_features" title="Link to this definition">¶</a></dt>
<dd><p>List of all features.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.property_lexical_handler">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">property_lexical_handler</span></span><a class="headerlink" href="#xml.sax.handler.property_lexical_handler" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/properties/lexical-handler&quot;</span></code></div>
<div class="line">data type: xml.sax.handler.LexicalHandler (not supported in Python 2)</div>
<div class="line">description: An optional extension handler for lexical events like
comments.</div>
<div class="line">access: read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.property_declaration_handler">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">property_declaration_handler</span></span><a class="headerlink" href="#xml.sax.handler.property_declaration_handler" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/properties/declaration-handler&quot;</span></code></div>
<div class="line">data type: xml.sax.sax2lib.DeclHandler (not supported in Python 2)</div>
<div class="line">description: An optional extension handler for DTD-related events other
than notations and unparsed entities.</div>
<div class="line">access: read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.property_dom_node">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">property_dom_node</span></span><a class="headerlink" href="#xml.sax.handler.property_dom_node" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/properties/dom-node&quot;</span></code></div>
<div class="line">data type: org.w3c.dom.Node (not supported in Python 2)</div>
<div class="line">description: When parsing, the current DOM node being visited if this is
a DOM iterator; when not parsing, the root DOM node for iteration.</div>
<div class="line">access: (parsing) read-only; (not parsing) read/write</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.property_xml_string">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">property_xml_string</span></span><a class="headerlink" href="#xml.sax.handler.property_xml_string" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line">value: <code class="docutils literal notranslate"><span class="pre">&quot;http://xml.org/sax/properties/xml-string&quot;</span></code></div>
<div class="line">data type: Bytes</div>
<div class="line">description: The literal string of characters that was the source for the
current event.</div>
<div class="line">access: read-only</div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.sax.handler.all_properties">
<span class="sig-prename descclassname"><span class="pre">xml.sax.handler.</span></span><span class="sig-name descname"><span class="pre">all_properties</span></span><a class="headerlink" href="#xml.sax.handler.all_properties" title="Link to this definition">¶</a></dt>
<dd><p>List of all known property names.</p>
</dd></dl>

<section id="contenthandler-objects">
<span id="content-handler-objects"></span><h2>ContentHandler Objects<a class="headerlink" href="#contenthandler-objects" title="Link to this heading">¶</a></h2>
<p>Users are expected to subclass <a class="reference internal" href="#xml.sax.handler.ContentHandler" title="xml.sax.handler.ContentHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContentHandler</span></code></a> to support their
application.  The following methods are called by the parser on the appropriate
events in the input document:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.setDocumentLocator">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">setDocumentLocator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">locator</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.setDocumentLocator" title="Link to this definition">¶</a></dt>
<dd><p>Called by the parser to give the application a locator for locating the origin
of document events.</p>
<p>SAX parsers are strongly encouraged (though not absolutely required) to supply a
locator: if it does so, it must supply the locator to the application by
invoking this method before invoking any of the other methods in the
DocumentHandler interface.</p>
<p>The locator allows the application to determine the end position of any
document-related event, even if the parser is not reporting an error. Typically,
the application will use this information for reporting its own errors (such as
character content that does not match an application’s business rules). The
information returned by the locator is probably not sufficient for use with a
search engine.</p>
<p>Note that the locator will return correct information only during the invocation
of the events in this interface. The application should not attempt to use it at
any other time.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.startDocument">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">startDocument</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.startDocument" title="Link to this definition">¶</a></dt>
<dd><p>Receive notification of the beginning of a document.</p>
<p>The SAX parser will invoke this method only once, before any other methods in
this interface or in DTDHandler (except for <a class="reference internal" href="#xml.sax.handler.ContentHandler.setDocumentLocator" title="xml.sax.handler.ContentHandler.setDocumentLocator"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setDocumentLocator()</span></code></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.endDocument">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">endDocument</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.endDocument" title="Link to this definition">¶</a></dt>
<dd><p>Receive notification of the end of a document.</p>
<p>The SAX parser will invoke this method only once, and it will be the last method
invoked during the parse. The parser shall not invoke this method until it has
either abandoned parsing (because of an unrecoverable error) or reached the end
of input.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.startPrefixMapping">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">startPrefixMapping</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.startPrefixMapping" title="Link to this definition">¶</a></dt>
<dd><p>Begin the scope of a prefix-URI Namespace mapping.</p>
<p>The information from this event is not necessary for normal Namespace
processing: the SAX XML reader will automatically replace prefixes for element
and attribute names when the <code class="docutils literal notranslate"><span class="pre">feature_namespaces</span></code> feature is enabled (the
default).</p>
<p>There are cases, however, when applications need to use prefixes in character
data or in attribute values, where they cannot safely be expanded automatically;
the <a class="reference internal" href="#xml.sax.handler.ContentHandler.startPrefixMapping" title="xml.sax.handler.ContentHandler.startPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startPrefixMapping()</span></code></a> and <a class="reference internal" href="#xml.sax.handler.ContentHandler.endPrefixMapping" title="xml.sax.handler.ContentHandler.endPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endPrefixMapping()</span></code></a> events supply the
information to the application to expand prefixes in those contexts itself, if
necessary.</p>
<p>Note that <a class="reference internal" href="#xml.sax.handler.ContentHandler.startPrefixMapping" title="xml.sax.handler.ContentHandler.startPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startPrefixMapping()</span></code></a> and <a class="reference internal" href="#xml.sax.handler.ContentHandler.endPrefixMapping" title="xml.sax.handler.ContentHandler.endPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endPrefixMapping()</span></code></a> events are not
guaranteed to be properly nested relative to each-other: all
<a class="reference internal" href="#xml.sax.handler.ContentHandler.startPrefixMapping" title="xml.sax.handler.ContentHandler.startPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startPrefixMapping()</span></code></a> events will occur before the corresponding
<a class="reference internal" href="#xml.sax.handler.ContentHandler.startElement" title="xml.sax.handler.ContentHandler.startElement"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startElement()</span></code></a> event, and all <a class="reference internal" href="#xml.sax.handler.ContentHandler.endPrefixMapping" title="xml.sax.handler.ContentHandler.endPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endPrefixMapping()</span></code></a> events will occur
after the corresponding <a class="reference internal" href="#xml.sax.handler.ContentHandler.endElement" title="xml.sax.handler.ContentHandler.endElement"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endElement()</span></code></a> event, but their order is not
guaranteed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.endPrefixMapping">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">endPrefixMapping</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.endPrefixMapping" title="Link to this definition">¶</a></dt>
<dd><p>End the scope of a prefix-URI mapping.</p>
<p>See <a class="reference internal" href="#xml.sax.handler.ContentHandler.startPrefixMapping" title="xml.sax.handler.ContentHandler.startPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startPrefixMapping()</span></code></a> for details. This event will always occur after
the corresponding <a class="reference internal" href="#xml.sax.handler.ContentHandler.endElement" title="xml.sax.handler.ContentHandler.endElement"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endElement()</span></code></a> event, but the order of
<a class="reference internal" href="#xml.sax.handler.ContentHandler.endPrefixMapping" title="xml.sax.handler.ContentHandler.endPrefixMapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endPrefixMapping()</span></code></a> events is not otherwise guaranteed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.startElement">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">startElement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.startElement" title="Link to this definition">¶</a></dt>
<dd><p>Signals the start of an element in non-namespace mode.</p>
<p>The <em>name</em> parameter contains the raw XML 1.0 name of the element type as a
string and the <em>attrs</em> parameter holds an object of the
<code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code>
interface (see <a class="reference internal" href="xml.sax.reader.html#attributes-objects"><span class="std std-ref">The Attributes Interface</span></a>) containing the attributes of
the element.  The object passed as <em>attrs</em> may be re-used by the parser; holding
on to a reference to it is not a reliable way to keep a copy of the attributes.
To keep a copy of the attributes, use the <a class="reference internal" href="copy.html#module-copy" title="copy: Shallow and deep copy operations."><code class="xref py py-meth docutils literal notranslate"><span class="pre">copy()</span></code></a> method of the <em>attrs</em>
object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.endElement">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">endElement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.endElement" title="Link to this definition">¶</a></dt>
<dd><p>Signals the end of an element in non-namespace mode.</p>
<p>The <em>name</em> parameter contains the name of the element type, just as with the
<a class="reference internal" href="#xml.sax.handler.ContentHandler.startElement" title="xml.sax.handler.ContentHandler.startElement"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startElement()</span></code></a> event.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.startElementNS">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">startElementNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.startElementNS" title="Link to this definition">¶</a></dt>
<dd><p>Signals the start of an element in namespace mode.</p>
<p>The <em>name</em> parameter contains the name of the element type as a <code class="docutils literal notranslate"><span class="pre">(uri,</span>
<span class="pre">localname)</span></code> tuple, the <em>qname</em> parameter contains the raw XML 1.0 name used in
the source document, and the <em>attrs</em> parameter holds an instance of the
<code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> interface (see
<a class="reference internal" href="xml.sax.reader.html#attributes-ns-objects"><span class="std std-ref">The AttributesNS Interface</span></a>)
containing the attributes of the element.  If no namespace is associated with
the element, the <em>uri</em> component of <em>name</em> will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.  The object passed
as <em>attrs</em> may be re-used by the parser; holding on to a reference to it is not
a reliable way to keep a copy of the attributes.  To keep a copy of the
attributes, use the <a class="reference internal" href="copy.html#module-copy" title="copy: Shallow and deep copy operations."><code class="xref py py-meth docutils literal notranslate"><span class="pre">copy()</span></code></a> method of the <em>attrs</em> object.</p>
<p>Parsers may set the <em>qname</em> parameter to <code class="docutils literal notranslate"><span class="pre">None</span></code>, unless the
<code class="docutils literal notranslate"><span class="pre">feature_namespace_prefixes</span></code> feature is activated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.endElementNS">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">endElementNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.endElementNS" title="Link to this definition">¶</a></dt>
<dd><p>Signals the end of an element in namespace mode.</p>
<p>The <em>name</em> parameter contains the name of the element type, just as with the
<a class="reference internal" href="#xml.sax.handler.ContentHandler.startElementNS" title="xml.sax.handler.ContentHandler.startElementNS"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startElementNS()</span></code></a> method, likewise the <em>qname</em> parameter.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.characters">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">characters</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">content</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.characters" title="Link to this definition">¶</a></dt>
<dd><p>Receive notification of character data.</p>
<p>The Parser will call this method to report each chunk of character data. SAX
parsers may return all contiguous character data in a single chunk, or they may
split it into several chunks; however, all of the characters in any single event
must come from the same external entity so that the Locator provides useful
information.</p>
<p><em>content</em> may be a string or bytes instance; the <code class="docutils literal notranslate"><span class="pre">expat</span></code> reader module
always produces strings.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The earlier SAX 1 interface provided by the Python XML Special Interest Group
used a more Java-like interface for this method.  Since most parsers used from
Python did not take advantage of the older interface, the simpler signature was
chosen to replace it.  To convert old code to the new interface, use <em>content</em>
instead of slicing content with the old <em>offset</em> and <em>length</em> parameters.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.ignorableWhitespace">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">ignorableWhitespace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">whitespace</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.ignorableWhitespace" title="Link to this definition">¶</a></dt>
<dd><p>Receive notification of ignorable whitespace in element content.</p>
<p>Validating Parsers must use this method to report each chunk of ignorable
whitespace (see the W3C XML 1.0 recommendation, section 2.10): non-validating
parsers may also use this method if they are capable of parsing and using
content models.</p>
<p>SAX parsers may return all contiguous whitespace in a single chunk, or they may
split it into several chunks; however, all of the characters in any single event
must come from the same external entity, so that the Locator provides useful
information.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.processingInstruction">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">processingInstruction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.processingInstruction" title="Link to this definition">¶</a></dt>
<dd><p>Receive notification of a processing instruction.</p>
<p>The Parser will invoke this method once for each processing instruction found:
note that processing instructions may occur before or after the main document
element.</p>
<p>A SAX parser should never report an XML declaration (XML 1.0, section 2.8) or a
text declaration (XML 1.0, section 4.3.1) using this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ContentHandler.skippedEntity">
<span class="sig-prename descclassname"><span class="pre">ContentHandler.</span></span><span class="sig-name descname"><span class="pre">skippedEntity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ContentHandler.skippedEntity" title="Link to this definition">¶</a></dt>
<dd><p>Receive notification of a skipped entity.</p>
<p>The Parser will invoke this method once for each entity skipped. Non-validating
processors may skip entities if they have not seen the declarations (because,
for example, the entity was declared in an external DTD subset). All processors
may skip external entities, depending on the values of the
<code class="docutils literal notranslate"><span class="pre">feature_external_ges</span></code> and the <code class="docutils literal notranslate"><span class="pre">feature_external_pes</span></code> properties.</p>
</dd></dl>

</section>
<section id="dtdhandler-objects">
<span id="dtd-handler-objects"></span><h2>DTDHandler Objects<a class="headerlink" href="#dtdhandler-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#xml.sax.handler.DTDHandler" title="xml.sax.handler.DTDHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DTDHandler</span></code></a> instances provide the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.DTDHandler.notationDecl">
<span class="sig-prename descclassname"><span class="pre">DTDHandler.</span></span><span class="sig-name descname"><span class="pre">notationDecl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.DTDHandler.notationDecl" title="Link to this definition">¶</a></dt>
<dd><p>Handle a notation declaration event.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.DTDHandler.unparsedEntityDecl">
<span class="sig-prename descclassname"><span class="pre">DTDHandler.</span></span><span class="sig-name descname"><span class="pre">unparsedEntityDecl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ndata</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.DTDHandler.unparsedEntityDecl" title="Link to this definition">¶</a></dt>
<dd><p>Handle an unparsed entity declaration event.</p>
</dd></dl>

</section>
<section id="entityresolver-objects">
<span id="entity-resolver-objects"></span><h2>EntityResolver Objects<a class="headerlink" href="#entityresolver-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.EntityResolver.resolveEntity">
<span class="sig-prename descclassname"><span class="pre">EntityResolver.</span></span><span class="sig-name descname"><span class="pre">resolveEntity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.EntityResolver.resolveEntity" title="Link to this definition">¶</a></dt>
<dd><p>Resolve the system identifier of an entity and return either the system
identifier to read from as a string, or an InputSource to read from. The default
implementation returns <em>systemId</em>.</p>
</dd></dl>

</section>
<section id="errorhandler-objects">
<span id="sax-error-handler"></span><h2>ErrorHandler Objects<a class="headerlink" href="#errorhandler-objects" title="Link to this heading">¶</a></h2>
<p>Objects with this interface are used to receive error and warning information
from the <a class="reference internal" href="xml.sax.reader.html#xml.sax.xmlreader.XMLReader" title="xml.sax.xmlreader.XMLReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLReader</span></code></a>.  If you create an object that
implements this interface, then register the object with your
<a class="reference internal" href="xml.sax.reader.html#xml.sax.xmlreader.XMLReader" title="xml.sax.xmlreader.XMLReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLReader</span></code></a>, the parser
will call the methods in your object to report all warnings and errors. There
are three levels of errors available: warnings, (possibly) recoverable errors,
and unrecoverable errors.  All methods take a <a class="reference internal" href="xml.sax.html#xml.sax.SAXParseException" title="xml.sax.SAXParseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SAXParseException</span></code></a> as the
only parameter.  Errors and warnings may be converted to an exception by raising
the passed-in exception object.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ErrorHandler.error">
<span class="sig-prename descclassname"><span class="pre">ErrorHandler.</span></span><span class="sig-name descname"><span class="pre">error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ErrorHandler.error" title="Link to this definition">¶</a></dt>
<dd><p>Called when the parser encounters a recoverable error.  If this method does not
raise an exception, parsing may continue, but further document information
should not be expected by the application.  Allowing the parser to continue may
allow additional errors to be discovered in the input document.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ErrorHandler.fatalError">
<span class="sig-prename descclassname"><span class="pre">ErrorHandler.</span></span><span class="sig-name descname"><span class="pre">fatalError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ErrorHandler.fatalError" title="Link to this definition">¶</a></dt>
<dd><p>Called when the parser encounters an error it cannot recover from; parsing is
expected to terminate when this method returns.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.ErrorHandler.warning">
<span class="sig-prename descclassname"><span class="pre">ErrorHandler.</span></span><span class="sig-name descname"><span class="pre">warning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.ErrorHandler.warning" title="Link to this definition">¶</a></dt>
<dd><p>Called when the parser presents minor warning information to the application.
Parsing is expected to continue when this method returns, and document
information will continue to be passed to the application. Raising an exception
in this method will cause parsing to end.</p>
</dd></dl>

</section>
<section id="lexicalhandler-objects">
<span id="lexical-handler-objects"></span><h2>LexicalHandler Objects<a class="headerlink" href="#lexicalhandler-objects" title="Link to this heading">¶</a></h2>
<p>Optional SAX2 handler for lexical events.</p>
<p>This handler is used to obtain lexical information about an XML
document. Lexical information includes information describing the
document encoding used and XML comments embedded in the document, as
well as section boundaries for the DTD and for any CDATA sections.
The lexical handlers are used in the same manner as content handlers.</p>
<p>Set the LexicalHandler of an XMLReader by using the setProperty method
with the property identifier
<code class="docutils literal notranslate"><span class="pre">'http://xml.org/sax/properties/lexical-handler'</span></code>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.LexicalHandler.comment">
<span class="sig-prename descclassname"><span class="pre">LexicalHandler.</span></span><span class="sig-name descname"><span class="pre">comment</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">content</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.LexicalHandler.comment" title="Link to this definition">¶</a></dt>
<dd><p>Reports a comment anywhere in the document (including the DTD and
outside the document element).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.LexicalHandler.startDTD">
<span class="sig-prename descclassname"><span class="pre">LexicalHandler.</span></span><span class="sig-name descname"><span class="pre">startDTD</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">public_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">system_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.LexicalHandler.startDTD" title="Link to this definition">¶</a></dt>
<dd><p>Reports the start of the DTD declarations if the document has an
associated DTD.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.LexicalHandler.endDTD">
<span class="sig-prename descclassname"><span class="pre">LexicalHandler.</span></span><span class="sig-name descname"><span class="pre">endDTD</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.LexicalHandler.endDTD" title="Link to this definition">¶</a></dt>
<dd><p>Reports the end of DTD declaration.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.LexicalHandler.startCDATA">
<span class="sig-prename descclassname"><span class="pre">LexicalHandler.</span></span><span class="sig-name descname"><span class="pre">startCDATA</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.LexicalHandler.startCDATA" title="Link to this definition">¶</a></dt>
<dd><p>Reports the start of a CDATA marked section.</p>
<p>The contents of the CDATA marked section will be reported through
the characters handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.handler.LexicalHandler.endCDATA">
<span class="sig-prename descclassname"><span class="pre">LexicalHandler.</span></span><span class="sig-name descname"><span class="pre">endCDATA</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.handler.LexicalHandler.endCDATA" title="Link to this definition">¶</a></dt>
<dd><p>Reports the end of a CDATA marked section.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code> — Base classes for SAX handlers</a><ul>
<li><a class="reference internal" href="#contenthandler-objects">ContentHandler Objects</a></li>
<li><a class="reference internal" href="#dtdhandler-objects">DTDHandler Objects</a></li>
<li><a class="reference internal" href="#entityresolver-objects">EntityResolver Objects</a></li>
<li><a class="reference internal" href="#errorhandler-objects">ErrorHandler Objects</a></li>
<li><a class="reference internal" href="#lexicalhandler-objects">LexicalHandler Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.sax.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax</span></code> — Support for SAX2 parsers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.sax.utils.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.saxutils</span></code> — SAX Utilities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.sax.handler.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.sax.utils.html" title="xml.sax.saxutils — SAX Utilities"
             >next</a> |</li>
        <li class="right" >
          <a href="xml.sax.html" title="xml.sax — Support for SAX2 parsers"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code> — Base classes for SAX handlers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>