<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
  <t t-name="point_of_sale.Numpad">
    <div t-attf-class="{{props.class}} numpad row row-cols-{{props.buttons.length / 4}} gx-0">
      <button t-foreach="props.buttons" t-as="button" t-key="button.value"
        t-attf-class="col btn btn-light py-3 border fw-bolder rounded-0 {{ button.class  or '' }}"
        t-on-click="() => this.onClick(button.value)"
        t-att-disabled="button.disabled"
        t-esc="button.text or button.value" />
    </div>
  </t>
</templates>
