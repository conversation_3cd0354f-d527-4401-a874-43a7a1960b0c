# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# krnkris, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: krnkris, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__code
msgid "Code"
msgstr "Kód"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_version
msgid "Interface Version"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_merchant_id
msgid "Merchant ID"
msgstr "Kereskedelmi azonosító"

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_provider
msgid "Payment Provider"
msgstr "Fizetési szolgáltató"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Fizetési tranzakció"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_prod_url
msgid "Production URL"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_secret
msgid "SIPS Secret Key"
msgstr ""

#. module: payment_sips
#: model_terms:ir.ui.view,arch_db:payment_sips.payment_provider_form
msgid "Secret Key"
msgstr "Titkos kulcs"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_key_version
msgid "Secret Key Version"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_provider__code__sips
msgid "Sips"
msgstr "Sips fizetés"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_test_url
msgid "Test URL"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__sips_merchant_id
msgid "The ID solely used to identify the merchant account with Sips"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Unrecognized response received from the payment provider."
msgstr ""
