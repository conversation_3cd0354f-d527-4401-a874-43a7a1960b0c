<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="socket — Low-level networking interface" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/socket.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/socket.py This module provides access to the BSD socket interface. It is available on all modern Unix systems, Windows, MacOS, and probably additional platforms. Availability: not ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/socket.py This module provides access to the BSD socket interface. It is available on all modern Unix systems, Windows, MacOS, and probably additional platforms. Availability: not ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>socket — Low-level networking interface &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="ssl — TLS/SSL wrapper for socket objects" href="ssl.html" />
    <link rel="prev" title="Developing with asyncio" href="asyncio-dev.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/socket.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code> — Low-level networking interface</a><ul>
<li><a class="reference internal" href="#socket-families">Socket families</a></li>
<li><a class="reference internal" href="#module-contents">Module contents</a><ul>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
<li><a class="reference internal" href="#functions">Functions</a><ul>
<li><a class="reference internal" href="#creating-sockets">Creating sockets</a></li>
<li><a class="reference internal" href="#other-functions">Other functions</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#socket-objects">Socket Objects</a></li>
<li><a class="reference internal" href="#notes-on-socket-timeouts">Notes on socket timeouts</a><ul>
<li><a class="reference internal" href="#timeouts-and-the-connect-method">Timeouts and the <code class="docutils literal notranslate"><span class="pre">connect</span></code> method</a></li>
<li><a class="reference internal" href="#timeouts-and-the-accept-method">Timeouts and the <code class="docutils literal notranslate"><span class="pre">accept</span></code> method</a></li>
</ul>
</li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="asyncio-dev.html"
                          title="previous chapter">Developing with asyncio</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ssl.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code> — TLS/SSL wrapper for socket objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/socket.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ssl.html" title="ssl — TLS/SSL wrapper for socket objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="asyncio-dev.html" title="Developing with asyncio"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" accesskey="U">Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code> — Low-level networking interface</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-socket">
<span id="socket-low-level-networking-interface"></span><h1><a class="reference internal" href="#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> — Low-level networking interface<a class="headerlink" href="#module-socket" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/socket.py">Lib/socket.py</a></p>
<hr class="docutils" />
<p>This module provides access to the BSD <em>socket</em> interface. It is available on
all modern Unix systems, Windows, MacOS, and probably additional platforms.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Some behavior may be platform dependent, since calls are made to the operating
system socket APIs.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p id="index-0">The Python interface is a straightforward transliteration of the Unix system
call and library interface for sockets to Python’s object-oriented style: the
<a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a> function returns a <em class="dfn">socket object</em> whose methods implement
the various socket system calls.  Parameter types are somewhat higher-level than
in the C interface: as with <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> operations on Python
files, buffer allocation on receive operations is automatic, and buffer length
is implicit on send operations.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="socketserver.html#module-socketserver" title="socketserver: A framework for network servers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code></a></dt><dd><p>Classes that simplify writing network servers.</p>
</dd>
<dt>Module <a class="reference internal" href="ssl.html#module-ssl" title="ssl: TLS/SSL wrapper for socket objects"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code></a></dt><dd><p>A TLS/SSL wrapper for socket objects.</p>
</dd>
</dl>
</div>
<section id="socket-families">
<h2>Socket families<a class="headerlink" href="#socket-families" title="Link to this heading">¶</a></h2>
<p>Depending on the system and the build options, various socket families
are supported by this module.</p>
<p>The address format required by a particular socket object is automatically
selected based on the address family specified when the socket object was
created.  Socket addresses are represented as follows:</p>
<ul>
<li><p>The address of an <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a> socket bound to a file system node
is represented as a string, using the file system encoding and the
<code class="docutils literal notranslate"><span class="pre">'surrogateescape'</span></code> error handler (see <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0383/"><strong>PEP 383</strong></a>).  An address in
Linux’s abstract namespace is returned as a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> with
an initial null byte; note that sockets in this namespace can
communicate with normal file system sockets, so programs intended to
run on Linux may need to deal with both types of address.  A string or
bytes-like object can be used for either type of address when
passing it as an argument.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Previously, <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a> socket paths were assumed to use UTF-8
encoding.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</li>
</ul>
<ul id="host-port">
<li><p>A pair <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> is used for the <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a> address family,
where <em>host</em> is a string representing either a hostname in internet domain
notation like <code class="docutils literal notranslate"><span class="pre">'daring.cwi.nl'</span></code> or an IPv4 address like <code class="docutils literal notranslate"><span class="pre">'************'</span></code>,
and <em>port</em> is an integer.</p>
<ul class="simple">
<li><p>For IPv4 addresses, two special forms are accepted instead of a host
address: <code class="docutils literal notranslate"><span class="pre">''</span></code> represents <code class="xref py py-const docutils literal notranslate"><span class="pre">INADDR_ANY</span></code>, which is used to bind to all
interfaces, and the string <code class="docutils literal notranslate"><span class="pre">'&lt;broadcast&gt;'</span></code> represents
<code class="xref py py-const docutils literal notranslate"><span class="pre">INADDR_BROADCAST</span></code>.  This behavior is not compatible with IPv6,
therefore, you may want to avoid these if you intend to support IPv6 with your
Python programs.</p></li>
</ul>
</li>
<li><p>For <a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET6</span></code></a> address family, a four-tuple <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port,</span> <span class="pre">flowinfo,</span>
<span class="pre">scope_id)</span></code> is used, where <em>flowinfo</em> and <em>scope_id</em> represent the <code class="docutils literal notranslate"><span class="pre">sin6_flowinfo</span></code>
and <code class="docutils literal notranslate"><span class="pre">sin6_scope_id</span></code> members in <code class="xref py py-const docutils literal notranslate"><span class="pre">struct</span> <span class="pre">sockaddr_in6</span></code> in C.  For
<a class="reference internal" href="#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> module methods, <em>flowinfo</em> and <em>scope_id</em> can be omitted just for
backward compatibility.  Note, however, omission of <em>scope_id</em> can cause problems
in manipulating scoped IPv6 addresses.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>For multicast addresses (with <em>scope_id</em> meaningful) <em>address</em> may not contain
<code class="docutils literal notranslate"><span class="pre">%scope_id</span></code> (or <code class="docutils literal notranslate"><span class="pre">zone</span> <span class="pre">id</span></code>) part. This information is superfluous and may
be safely omitted (recommended).</p>
</div>
</li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_NETLINK</span></code> sockets are represented as pairs <code class="docutils literal notranslate"><span class="pre">(pid,</span> <span class="pre">groups)</span></code>.</p></li>
<li><p>Linux-only support for TIPC is available using the <code class="xref py py-const docutils literal notranslate"><span class="pre">AF_TIPC</span></code>
address family.  TIPC is an open, non-IP based networked protocol designed
for use in clustered computer environments.  Addresses are represented by a
tuple, and the fields depend on the address type. The general tuple form is
<code class="docutils literal notranslate"><span class="pre">(addr_type,</span> <span class="pre">v1,</span> <span class="pre">v2,</span> <span class="pre">v3</span> <span class="pre">[,</span> <span class="pre">scope])</span></code>, where:</p>
<ul>
<li><p><em>addr_type</em> is one of <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ADDR_NAMESEQ</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ADDR_NAME</span></code>,
or <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ADDR_ID</span></code>.</p></li>
<li><p><em>scope</em> is one of <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ZONE_SCOPE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_CLUSTER_SCOPE</span></code>, and
<code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_NODE_SCOPE</span></code>.</p></li>
<li><p>If <em>addr_type</em> is <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ADDR_NAME</span></code>, then <em>v1</em> is the server type, <em>v2</em> is
the port identifier, and <em>v3</em> should be 0.</p>
<p>If <em>addr_type</em> is <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ADDR_NAMESEQ</span></code>, then <em>v1</em> is the server type, <em>v2</em>
is the lower port number, and <em>v3</em> is the upper port number.</p>
<p>If <em>addr_type</em> is <code class="xref py py-const docutils literal notranslate"><span class="pre">TIPC_ADDR_ID</span></code>, then <em>v1</em> is the node, <em>v2</em> is the
reference, and <em>v3</em> should be set to 0.</p>
</li>
</ul>
</li>
<li><p>A tuple <code class="docutils literal notranslate"><span class="pre">(interface,</span> <span class="pre">)</span></code> is used for the <a class="reference internal" href="#socket.AF_CAN" title="socket.AF_CAN"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_CAN</span></code></a> address family,
where <em>interface</em> is a string representing a network interface name like
<code class="docutils literal notranslate"><span class="pre">'can0'</span></code>. The network interface name <code class="docutils literal notranslate"><span class="pre">''</span></code> can be used to receive packets
from all network interfaces of this family.</p>
<ul class="simple">
<li><p><a class="reference internal" href="#socket.CAN_ISOTP" title="socket.CAN_ISOTP"><code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_ISOTP</span></code></a> protocol require a tuple <code class="docutils literal notranslate"><span class="pre">(interface,</span> <span class="pre">rx_addr,</span> <span class="pre">tx_addr)</span></code>
where both additional parameters are unsigned long integer that represent a
CAN identifier (standard or extended).</p></li>
<li><p><a class="reference internal" href="#socket.CAN_J1939" title="socket.CAN_J1939"><code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_J1939</span></code></a> protocol require a tuple <code class="docutils literal notranslate"><span class="pre">(interface,</span> <span class="pre">name,</span> <span class="pre">pgn,</span> <span class="pre">addr)</span></code>
where additional parameters are 64-bit unsigned integer representing the
ECU name, a 32-bit unsigned integer representing the Parameter Group Number
(PGN), and an 8-bit integer representing the address.</p></li>
</ul>
</li>
<li><p>A string or a tuple <code class="docutils literal notranslate"><span class="pre">(id,</span> <span class="pre">unit)</span></code> is used for the <code class="xref py py-const docutils literal notranslate"><span class="pre">SYSPROTO_CONTROL</span></code>
protocol of the <code class="xref py py-const docutils literal notranslate"><span class="pre">PF_SYSTEM</span></code> family. The string is the name of a
kernel control using a dynamically assigned ID. The tuple can be used if ID
and unit number of the kernel control are known or if a registered ID is
used.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_BLUETOOTH</span></code> supports the following protocols and address
formats:</p>
<ul>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">BTPROTO_L2CAP</span></code> accepts <code class="docutils literal notranslate"><span class="pre">(bdaddr,</span> <span class="pre">psm)</span></code> where <code class="docutils literal notranslate"><span class="pre">bdaddr</span></code> is
the Bluetooth address as a string and <code class="docutils literal notranslate"><span class="pre">psm</span></code> is an integer.</p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">BTPROTO_RFCOMM</span></code> accepts <code class="docutils literal notranslate"><span class="pre">(bdaddr,</span> <span class="pre">channel)</span></code> where <code class="docutils literal notranslate"><span class="pre">bdaddr</span></code>
is the Bluetooth address as a string and <code class="docutils literal notranslate"><span class="pre">channel</span></code> is an integer.</p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">BTPROTO_HCI</span></code> accepts <code class="docutils literal notranslate"><span class="pre">(device_id,)</span></code> where <code class="docutils literal notranslate"><span class="pre">device_id</span></code> is
either an integer or a string with the Bluetooth address of the
interface. (This depends on your OS; NetBSD and DragonFlyBSD expect
a Bluetooth address while everything else expects an integer.)</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>NetBSD and DragonFlyBSD support added.</p>
</div>
</li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">BTPROTO_SCO</span></code> accepts <code class="docutils literal notranslate"><span class="pre">bdaddr</span></code> where <code class="docutils literal notranslate"><span class="pre">bdaddr</span></code> is a
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object containing the Bluetooth address in a
string format. (ex. <code class="docutils literal notranslate"><span class="pre">b'12:23:34:45:56:67'</span></code>) This protocol is not
supported under FreeBSD.</p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#socket.AF_ALG" title="socket.AF_ALG"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_ALG</span></code></a> is a Linux-only socket based interface to Kernel
cryptography. An algorithm socket is configured with a tuple of two to four
elements <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">name</span> <span class="pre">[,</span> <span class="pre">feat</span> <span class="pre">[,</span> <span class="pre">mask]])</span></code>, where:</p>
<ul class="simple">
<li><p><em>type</em> is the algorithm type as string, e.g. <code class="docutils literal notranslate"><span class="pre">aead</span></code>, <code class="docutils literal notranslate"><span class="pre">hash</span></code>,
<code class="docutils literal notranslate"><span class="pre">skcipher</span></code> or <code class="docutils literal notranslate"><span class="pre">rng</span></code>.</p></li>
<li><p><em>name</em> is the algorithm name and operation mode as string, e.g.
<code class="docutils literal notranslate"><span class="pre">sha256</span></code>, <code class="docutils literal notranslate"><span class="pre">hmac(sha256)</span></code>, <code class="docutils literal notranslate"><span class="pre">cbc(aes)</span></code> or <code class="docutils literal notranslate"><span class="pre">drbg_nopr_ctr_aes256</span></code>.</p></li>
<li><p><em>feat</em> and <em>mask</em> are unsigned 32bit integers.</p></li>
</ul>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.38.</p>
<p>Some algorithm types require more recent Kernels.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</li>
<li><p><a class="reference internal" href="#socket.AF_VSOCK" title="socket.AF_VSOCK"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_VSOCK</span></code></a> allows communication between virtual machines and
their hosts. The sockets are represented as a <code class="docutils literal notranslate"><span class="pre">(CID,</span> <span class="pre">port)</span></code> tuple
where the context ID or CID and port are integers.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 3.9</p>
<p>See <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/vsock(7)">vsock(7)</a></em></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</li>
<li><p><a class="reference internal" href="#socket.AF_PACKET" title="socket.AF_PACKET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_PACKET</span></code></a> is a low-level interface directly to network devices.
The addresses are represented by the tuple
<code class="docutils literal notranslate"><span class="pre">(ifname,</span> <span class="pre">proto[,</span> <span class="pre">pkttype[,</span> <span class="pre">hatype[,</span> <span class="pre">addr]]])</span></code> where:</p>
<ul class="simple">
<li><p><em>ifname</em> - String specifying the device name.</p></li>
<li><p><em>proto</em> - The Ethernet protocol number.
May be <a class="reference internal" href="#socket.ETH_P_ALL" title="socket.ETH_P_ALL"><code class="xref py py-data docutils literal notranslate"><span class="pre">ETH_P_ALL</span></code></a> to capture all protocols,
one of the <a class="reference internal" href="#socket-ethernet-types"><span class="std std-ref">ETHERTYPE_* constants</span></a>
or any other Ethernet protocol number.</p></li>
<li><p><em>pkttype</em> - Optional integer specifying the packet type:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">PACKET_HOST</span></code> (the default) - Packet addressed to the local host.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PACKET_BROADCAST</span></code> - Physical-layer broadcast packet.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PACKET_MULTICAST</span></code> - Packet sent to a physical-layer multicast address.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PACKET_OTHERHOST</span></code> - Packet to some other host that has been caught by
a device driver in promiscuous mode.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PACKET_OUTGOING</span></code> - Packet originating from the local host that is
looped back to a packet socket.</p></li>
</ul>
</li>
<li><p><em>hatype</em> - Optional integer specifying the ARP hardware address type.</p></li>
<li><p><em>addr</em> - Optional bytes-like object specifying the hardware physical
address, whose interpretation depends on the device.</p></li>
</ul>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.2.</p>
</div>
</li>
<li><p><a class="reference internal" href="#socket.AF_QIPCRTR" title="socket.AF_QIPCRTR"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_QIPCRTR</span></code></a> is a Linux-only socket based interface for communicating
with services running on co-processors in Qualcomm platforms. The address
family is represented as a <code class="docutils literal notranslate"><span class="pre">(node,</span> <span class="pre">port)</span></code> tuple where the <em>node</em> and <em>port</em>
are non-negative integers.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 4.7.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">IPPROTO_UDPLITE</span></code> is a variant of UDP which allows you to specify
what portion of a packet is covered with the checksum. It adds two socket
options that you can change.
<code class="docutils literal notranslate"><span class="pre">self.setsockopt(IPPROTO_UDPLITE,</span> <span class="pre">UDPLITE_SEND_CSCOV,</span> <span class="pre">length)</span></code> will
change what portion of outgoing packets are covered by the checksum and
<code class="docutils literal notranslate"><span class="pre">self.setsockopt(IPPROTO_UDPLITE,</span> <span class="pre">UDPLITE_RECV_CSCOV,</span> <span class="pre">length)</span></code> will
filter out packets which cover too little of their data. In both cases
<code class="docutils literal notranslate"><span class="pre">length</span></code> should be in <code class="docutils literal notranslate"><span class="pre">range(8,</span> <span class="pre">2**16,</span> <span class="pre">8)</span></code>.</p>
<p>Such a socket should be constructed with
<code class="docutils literal notranslate"><span class="pre">socket(AF_INET,</span> <span class="pre">SOCK_DGRAM,</span> <span class="pre">IPPROTO_UDPLITE)</span></code> for IPv4 or
<code class="docutils literal notranslate"><span class="pre">socket(AF_INET6,</span> <span class="pre">SOCK_DGRAM,</span> <span class="pre">IPPROTO_UDPLITE)</span></code> for IPv6.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.20, FreeBSD &gt;= 10.1</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</li>
<li><p><a class="reference internal" href="#socket.AF_HYPERV" title="socket.AF_HYPERV"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_HYPERV</span></code></a> is a Windows-only socket based interface for communicating
with Hyper-V hosts and guests. The address family is represented as a
<code class="docutils literal notranslate"><span class="pre">(vm_id,</span> <span class="pre">service_id)</span></code> tuple where the <code class="docutils literal notranslate"><span class="pre">vm_id</span></code> and <code class="docutils literal notranslate"><span class="pre">service_id</span></code> are
UUID strings.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">vm_id</span></code> is the virtual machine identifier or a set of known VMID values
if the target is not a specific virtual machine. Known VMID constants
defined on <code class="docutils literal notranslate"><span class="pre">socket</span></code> are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">HV_GUID_ZERO</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HV_GUID_BROADCAST</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HV_GUID_WILDCARD</span></code> - Used to bind on itself and accept connections from
all partitions.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HV_GUID_CHILDREN</span></code> - Used to bind on itself and accept connection from
child partitions.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HV_GUID_LOOPBACK</span></code> - Used as a target to itself.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HV_GUID_PARENT</span></code> - When used as a bind accepts connection from the parent
partition. When used as an address target it will connect to the parent partition.</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">service_id</span></code> is the service identifier of the registered service.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</li>
</ul>
<p>If you use a hostname in the <em>host</em> portion of IPv4/v6 socket address, the
program may show a nondeterministic behavior, as Python uses the first address
returned from the DNS resolution.  The socket address will be resolved
differently into an actual IPv4/v6 address, depending on the results from DNS
resolution and/or the host configuration.  For deterministic behavior use a
numeric address in <em>host</em> portion.</p>
<p>All errors raise exceptions.  The normal exceptions for invalid argument types
and out-of-memory conditions can be raised. Errors
related to socket or address semantics raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> or one of its
subclasses.</p>
<p>Non-blocking mode is supported through <a class="reference internal" href="#socket.socket.setblocking" title="socket.socket.setblocking"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setblocking()</span></code></a>.  A
generalization of this based on timeouts is supported through
<a class="reference internal" href="#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a>.</p>
</section>
<section id="module-contents">
<h2>Module contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<p>The module <a class="reference internal" href="#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> exports the following elements.</p>
<section id="exceptions">
<h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<dl class="py exception">
<dt class="sig sig-object py" id="socket.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#socket.error" title="Link to this definition">¶</a></dt>
<dd><p>A deprecated alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Following <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-3151/"><strong>PEP 3151</strong></a>, this class was made an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="socket.herror">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">herror</span></span><a class="headerlink" href="#socket.herror" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, this exception is raised for
address-related errors, i.e. for functions that use <em>h_errno</em> in the POSIX
C API, including <a class="reference internal" href="#socket.gethostbyname_ex" title="socket.gethostbyname_ex"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyname_ex()</span></code></a> and <a class="reference internal" href="#socket.gethostbyaddr" title="socket.gethostbyaddr"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyaddr()</span></code></a>.
The accompanying value is a pair <code class="docutils literal notranslate"><span class="pre">(h_errno,</span> <span class="pre">string)</span></code> representing an
error returned by a library call.  <em>h_errno</em> is a numeric value, while
<em>string</em> represents the description of <em>h_errno</em>, as returned by the
<code class="xref c c-func docutils literal notranslate"><span class="pre">hstrerror()</span></code> C function.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This class was made a subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="socket.gaierror">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">gaierror</span></span><a class="headerlink" href="#socket.gaierror" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, this exception is raised for
address-related errors by <a class="reference internal" href="#socket.getaddrinfo" title="socket.getaddrinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">getaddrinfo()</span></code></a> and <a class="reference internal" href="#socket.getnameinfo" title="socket.getnameinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">getnameinfo()</span></code></a>.
The accompanying value is a pair <code class="docutils literal notranslate"><span class="pre">(error,</span> <span class="pre">string)</span></code> representing an error
returned by a library call.  <em>string</em> represents the description of
<em>error</em>, as returned by the <code class="xref c c-func docutils literal notranslate"><span class="pre">gai_strerror()</span></code> C function.  The
numeric <em>error</em> value will match one of the <code class="xref py py-const docutils literal notranslate"><span class="pre">EAI_*</span></code> constants
defined in this module.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This class was made a subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="socket.timeout">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">timeout</span></span><a class="headerlink" href="#socket.timeout" title="Link to this definition">¶</a></dt>
<dd><p>A deprecated alias of <a class="reference internal" href="exceptions.html#TimeoutError" title="TimeoutError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutError</span></code></a>.</p>
<p>A subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, this exception is raised when a timeout
occurs on a socket which has had timeouts enabled via a prior call to
<a class="reference internal" href="#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a> (or implicitly through
<a class="reference internal" href="#socket.setdefaulttimeout" title="socket.setdefaulttimeout"><code class="xref py py-func docutils literal notranslate"><span class="pre">setdefaulttimeout()</span></code></a>).  The accompanying value is a string
whose value is currently always “timed out”.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This class was made a subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>This class was made an alias of <a class="reference internal" href="exceptions.html#TimeoutError" title="TimeoutError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutError</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="constants">
<h3>Constants<a class="headerlink" href="#constants" title="Link to this heading">¶</a></h3>
<blockquote>
<div><p>The AF_* and SOCK_* constants are now <code class="xref py py-class docutils literal notranslate"><span class="pre">AddressFamily</span></code> and
<code class="xref py py-class docutils literal notranslate"><span class="pre">SocketKind</span></code> <a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> collections.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</div></blockquote>
<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_UNIX">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_UNIX</span></span><a class="headerlink" href="#socket.AF_UNIX" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.AF_INET">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_INET</span></span><a class="headerlink" href="#socket.AF_INET" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.AF_INET6">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_INET6</span></span><a class="headerlink" href="#socket.AF_INET6" title="Link to this definition">¶</a></dt>
<dd><p>These constants represent the address (and protocol) families, used for the
first argument to <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a>.  If the <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a> constant is not
defined then this protocol is unsupported.  More constants may be available
depending on the system.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_UNSPEC">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_UNSPEC</span></span><a class="headerlink" href="#socket.AF_UNSPEC" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="#socket.AF_UNSPEC" title="socket.AF_UNSPEC"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNSPEC</span></code></a> means that
<a class="reference internal" href="#socket.getaddrinfo" title="socket.getaddrinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">getaddrinfo()</span></code></a> should return socket addresses for any
address family (either IPv4, IPv6, or any other) that can be used.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.SOCK_STREAM">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_STREAM</span></span><a class="headerlink" href="#socket.SOCK_STREAM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOCK_DGRAM">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_DGRAM</span></span><a class="headerlink" href="#socket.SOCK_DGRAM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOCK_RAW">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_RAW</span></span><a class="headerlink" href="#socket.SOCK_RAW" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOCK_RDM">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_RDM</span></span><a class="headerlink" href="#socket.SOCK_RDM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOCK_SEQPACKET">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_SEQPACKET</span></span><a class="headerlink" href="#socket.SOCK_SEQPACKET" title="Link to this definition">¶</a></dt>
<dd><p>These constants represent the socket types, used for the second argument to
<a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a>.  More constants may be available depending on the system.
(Only <a class="reference internal" href="#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a> and <a class="reference internal" href="#socket.SOCK_DGRAM" title="socket.SOCK_DGRAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_DGRAM</span></code></a> appear to be generally
useful.)</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.SOCK_CLOEXEC">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_CLOEXEC</span></span><a class="headerlink" href="#socket.SOCK_CLOEXEC" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOCK_NONBLOCK">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOCK_NONBLOCK</span></span><a class="headerlink" href="#socket.SOCK_NONBLOCK" title="Link to this definition">¶</a></dt>
<dd><p>These two constants, if defined, can be combined with the socket types and
allow you to set some flags atomically (thus avoiding possible race
conditions and the need for separate calls).</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://udrepper.livejournal.com/20407.html">Secure File Descriptor Handling</a>
for a more thorough explanation.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.27.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data" id="socket-unix-constants">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">SO_*</span></span></dt>
<dt class="sig sig-object py" id="socket.SOMAXCONN">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOMAXCONN</span></span><a class="headerlink" href="#socket.SOMAXCONN" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">MSG_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">SOL_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">SCM_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">IPPROTO_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">IPPORT_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">INADDR_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">IP_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">IPV6_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">EAI_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">AI_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">NI_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">TCP_*</span></span></dt>
<dd><p>Many constants of these forms, documented in the Unix documentation on sockets
and/or the IP protocol, are also defined in the socket module. They are
generally used in arguments to the <a class="reference internal" href="#socket.socket.setsockopt" title="socket.socket.setsockopt"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setsockopt()</span></code></a> and <a class="reference internal" href="#socket.socket.getsockopt" title="socket.socket.getsockopt"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getsockopt()</span></code></a>
methods of socket objects.  In most cases, only those symbols that are defined
in the Unix header files are defined; for a few symbols, default values are
provided.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><code class="docutils literal notranslate"><span class="pre">SO_DOMAIN</span></code>, <code class="docutils literal notranslate"><span class="pre">SO_PROTOCOL</span></code>, <code class="docutils literal notranslate"><span class="pre">SO_PEERSEC</span></code>, <code class="docutils literal notranslate"><span class="pre">SO_PASSSEC</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_USER_TIMEOUT</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_CONGESTION</span></code> were added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.5: </span>On Windows, <code class="docutils literal notranslate"><span class="pre">TCP_FASTOPEN</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_KEEPCNT</span></code> appear if run-time Windows
supports.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><code class="docutils literal notranslate"><span class="pre">TCP_NOTSENT_LOWAT</span></code> was added.</p>
<p>On Windows, <code class="docutils literal notranslate"><span class="pre">TCP_KEEPIDLE</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_KEEPINTVL</span></code> appear if run-time Windows
supports.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">IP_RECVTOS</span></code> was added.
 Added <code class="docutils literal notranslate"><span class="pre">TCP_KEEPALIVE</span></code>. On MacOS this constant can be used in the same
 way that <code class="docutils literal notranslate"><span class="pre">TCP_KEEPIDLE</span></code> is used on Linux.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added <code class="docutils literal notranslate"><span class="pre">TCP_CONNECTION_INFO</span></code>. On MacOS this constant can be used in the
same way that <code class="docutils literal notranslate"><span class="pre">TCP_INFO</span></code> is used on Linux and BSD.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added <code class="docutils literal notranslate"><span class="pre">SO_RTABLE</span></code> and <code class="docutils literal notranslate"><span class="pre">SO_USER_COOKIE</span></code>. On OpenBSD
and FreeBSD respectively those constants can be used in the same way that
<code class="docutils literal notranslate"><span class="pre">SO_MARK</span></code> is used on Linux. Also added missing TCP socket options from
Linux: <code class="docutils literal notranslate"><span class="pre">TCP_MD5SIG</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_THIN_LINEAR_TIMEOUTS</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_THIN_DUPACK</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_REPAIR</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_REPAIR_QUEUE</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_QUEUE_SEQ</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_REPAIR_OPTIONS</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_TIMESTAMP</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_CC_INFO</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_SAVE_SYN</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_SAVED_SYN</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_REPAIR_WINDOW</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_FASTOPEN_CONNECT</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_ULP</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_MD5SIG_EXT</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_FASTOPEN_KEY</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_FASTOPEN_NO_COOKIE</span></code>,
<code class="docutils literal notranslate"><span class="pre">TCP_ZEROCOPY_RECEIVE</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_INQ</span></code>, <code class="docutils literal notranslate"><span class="pre">TCP_TX_DELAY</span></code>.
Added <code class="docutils literal notranslate"><span class="pre">IP_PKTINFO</span></code>, <code class="docutils literal notranslate"><span class="pre">IP_UNBLOCK_SOURCE</span></code>, <code class="docutils literal notranslate"><span class="pre">IP_BLOCK_SOURCE</span></code>,
<code class="docutils literal notranslate"><span class="pre">IP_ADD_SOURCE_MEMBERSHIP</span></code>, <code class="docutils literal notranslate"><span class="pre">IP_DROP_SOURCE_MEMBERSHIP</span></code>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_CAN">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_CAN</span></span><a class="headerlink" href="#socket.AF_CAN" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.PF_CAN">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">PF_CAN</span></span><a class="headerlink" href="#socket.PF_CAN" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">SOL_CAN_*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">CAN_*</span></span></dt>
<dd><p>Many constants of these forms, documented in the Linux documentation, are
also defined in the socket module.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.25, NetBSD &gt;= 8.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>NetBSD support was added.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.CAN_BCM">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CAN_BCM</span></span><a class="headerlink" href="#socket.CAN_BCM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">CAN_BCM_*</span></span></dt>
<dd><p>CAN_BCM, in the CAN protocol family, is the broadcast manager (BCM) protocol.
Broadcast manager constants, documented in the Linux documentation, are also
defined in the socket module.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.25.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="xref py py-data docutils literal notranslate"><span class="pre">CAN_BCM_CAN_FD_FRAME</span></code> flag is only available on Linux &gt;= 4.8.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.CAN_RAW_FD_FRAMES">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CAN_RAW_FD_FRAMES</span></span><a class="headerlink" href="#socket.CAN_RAW_FD_FRAMES" title="Link to this definition">¶</a></dt>
<dd><p>Enables CAN FD support in a CAN_RAW socket. This is disabled by default.
This allows your application to send both CAN and CAN FD frames; however,
you must accept both CAN and CAN FD frames when reading from the socket.</p>
<p>This constant is documented in the Linux documentation.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 3.6.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.CAN_RAW_JOIN_FILTERS">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CAN_RAW_JOIN_FILTERS</span></span><a class="headerlink" href="#socket.CAN_RAW_JOIN_FILTERS" title="Link to this definition">¶</a></dt>
<dd><p>Joins the applied CAN filters such that only CAN frames that match all
given CAN filters are passed to user space.</p>
<p>This constant is documented in the Linux documentation.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 4.1.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.CAN_ISOTP">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CAN_ISOTP</span></span><a class="headerlink" href="#socket.CAN_ISOTP" title="Link to this definition">¶</a></dt>
<dd><p>CAN_ISOTP, in the CAN protocol family, is the ISO-TP (ISO 15765-2) protocol.
ISO-TP constants, documented in the Linux documentation.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.25.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.CAN_J1939">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CAN_J1939</span></span><a class="headerlink" href="#socket.CAN_J1939" title="Link to this definition">¶</a></dt>
<dd><p>CAN_J1939, in the CAN protocol family, is the SAE J1939 protocol.
J1939 constants, documented in the Linux documentation.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 5.4.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_DIVERT">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_DIVERT</span></span><a class="headerlink" href="#socket.AF_DIVERT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.PF_DIVERT">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">PF_DIVERT</span></span><a class="headerlink" href="#socket.PF_DIVERT" title="Link to this definition">¶</a></dt>
<dd><p>These two constants, documented in the FreeBSD divert(4) manual page, are
also defined in the socket module.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD &gt;= 14.0.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_PACKET">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_PACKET</span></span><a class="headerlink" href="#socket.AF_PACKET" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.PF_PACKET">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">PF_PACKET</span></span><a class="headerlink" href="#socket.PF_PACKET" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">PACKET_*</span></span></dt>
<dd><p>Many constants of these forms, documented in the Linux documentation, are
also defined in the socket module.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.2.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.ETH_P_ALL">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ETH_P_ALL</span></span><a class="headerlink" href="#socket.ETH_P_ALL" title="Link to this definition">¶</a></dt>
<dd><p><code class="xref py py-data docutils literal notranslate"><span class="pre">ETH_P_ALL</span></code> can be used in the <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket</span></code></a>
constructor as <em>proto</em> for the <a class="reference internal" href="#socket.AF_PACKET" title="socket.AF_PACKET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_PACKET</span></code></a> family in order to
capture every packet, regardless of protocol.</p>
<p>For more information, see the <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/packet(7)">packet(7)</a></em> manpage.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_RDS">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_RDS</span></span><a class="headerlink" href="#socket.AF_RDS" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.PF_RDS">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">PF_RDS</span></span><a class="headerlink" href="#socket.PF_RDS" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOL_RDS">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOL_RDS</span></span><a class="headerlink" href="#socket.SOL_RDS" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">RDS_*</span></span></dt>
<dd><p>Many constants of these forms, documented in the Linux documentation, are
also defined in the socket module.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.30.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.SIO_RCVALL">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SIO_RCVALL</span></span><a class="headerlink" href="#socket.SIO_RCVALL" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SIO_KEEPALIVE_VALS">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SIO_KEEPALIVE_VALS</span></span><a class="headerlink" href="#socket.SIO_KEEPALIVE_VALS" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SIO_LOOPBACK_FAST_PATH">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SIO_LOOPBACK_FAST_PATH</span></span><a class="headerlink" href="#socket.SIO_LOOPBACK_FAST_PATH" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">RCVALL_*</span></span></dt>
<dd><p>Constants for Windows’ WSAIoctl(). The constants are used as arguments to the
<a class="reference internal" href="#socket.socket.ioctl" title="socket.socket.ioctl"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ioctl()</span></code></a> method of socket objects.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><code class="docutils literal notranslate"><span class="pre">SIO_LOOPBACK_FAST_PATH</span></code> was added.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">TIPC_*</span></span></dt>
<dd><p>TIPC related constants, matching the ones exported by the C socket API. See
the TIPC documentation for more information.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_ALG">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_ALG</span></span><a class="headerlink" href="#socket.AF_ALG" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.SOL_ALG">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SOL_ALG</span></span><a class="headerlink" href="#socket.SOL_ALG" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">ALG_*</span></span></dt>
<dd><p>Constants for Linux Kernel cryptography.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.38.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_VSOCK">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_VSOCK</span></span><a class="headerlink" href="#socket.AF_VSOCK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.IOCTL_VM_SOCKETS_GET_LOCAL_CID">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">IOCTL_VM_SOCKETS_GET_LOCAL_CID</span></span><a class="headerlink" href="#socket.IOCTL_VM_SOCKETS_GET_LOCAL_CID" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">VMADDR*</span></span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">SO_VM*</span></span></dt>
<dd><p>Constants for Linux host/guest communication.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 4.8.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_LINK">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_LINK</span></span><a class="headerlink" href="#socket.AF_LINK" title="Link to this definition">¶</a></dt>
<dd><div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: BSD, macOS.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.has_ipv6">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">has_ipv6</span></span><a class="headerlink" href="#socket.has_ipv6" title="Link to this definition">¶</a></dt>
<dd><p>This constant contains a boolean value which indicates if IPv6 is supported on
this platform.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.BDADDR_ANY">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">BDADDR_ANY</span></span><a class="headerlink" href="#socket.BDADDR_ANY" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.BDADDR_LOCAL">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">BDADDR_LOCAL</span></span><a class="headerlink" href="#socket.BDADDR_LOCAL" title="Link to this definition">¶</a></dt>
<dd><p>These are string constants containing Bluetooth addresses with special
meanings. For example, <a class="reference internal" href="#socket.BDADDR_ANY" title="socket.BDADDR_ANY"><code class="xref py py-const docutils literal notranslate"><span class="pre">BDADDR_ANY</span></code></a> can be used to indicate
any address when specifying the binding socket with
<code class="xref py py-const docutils literal notranslate"><span class="pre">BTPROTO_RFCOMM</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.HCI_FILTER">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HCI_FILTER</span></span><a class="headerlink" href="#socket.HCI_FILTER" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HCI_TIME_STAMP">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HCI_TIME_STAMP</span></span><a class="headerlink" href="#socket.HCI_TIME_STAMP" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HCI_DATA_DIR">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HCI_DATA_DIR</span></span><a class="headerlink" href="#socket.HCI_DATA_DIR" title="Link to this definition">¶</a></dt>
<dd><p>For use with <code class="xref py py-const docutils literal notranslate"><span class="pre">BTPROTO_HCI</span></code>. <a class="reference internal" href="#socket.HCI_FILTER" title="socket.HCI_FILTER"><code class="xref py py-const docutils literal notranslate"><span class="pre">HCI_FILTER</span></code></a> is not
available for NetBSD or DragonFlyBSD. <a class="reference internal" href="#socket.HCI_TIME_STAMP" title="socket.HCI_TIME_STAMP"><code class="xref py py-const docutils literal notranslate"><span class="pre">HCI_TIME_STAMP</span></code></a> and
<a class="reference internal" href="#socket.HCI_DATA_DIR" title="socket.HCI_DATA_DIR"><code class="xref py py-const docutils literal notranslate"><span class="pre">HCI_DATA_DIR</span></code></a> are not available for FreeBSD, NetBSD, or
DragonFlyBSD.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_QIPCRTR">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_QIPCRTR</span></span><a class="headerlink" href="#socket.AF_QIPCRTR" title="Link to this definition">¶</a></dt>
<dd><p>Constant for Qualcomm’s IPC router protocol, used to communicate with
service providing remote processors.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 4.7.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.SCM_CREDS2">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SCM_CREDS2</span></span><a class="headerlink" href="#socket.SCM_CREDS2" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.LOCAL_CREDS">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">LOCAL_CREDS</span></span><a class="headerlink" href="#socket.LOCAL_CREDS" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.LOCAL_CREDS_PERSISTENT">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">LOCAL_CREDS_PERSISTENT</span></span><a class="headerlink" href="#socket.LOCAL_CREDS_PERSISTENT" title="Link to this definition">¶</a></dt>
<dd><p>LOCAL_CREDS and LOCAL_CREDS_PERSISTENT can be used
with SOCK_DGRAM, SOCK_STREAM sockets, equivalent to
Linux/DragonFlyBSD SO_PASSCRED, while LOCAL_CREDS
sends the credentials at first read, LOCAL_CREDS_PERSISTENT
sends for each read, SCM_CREDS2 must be then used for
the latter for the message type.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.SO_INCOMING_CPU">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SO_INCOMING_CPU</span></span><a class="headerlink" href="#socket.SO_INCOMING_CPU" title="Link to this definition">¶</a></dt>
<dd><blockquote>
<div><p>Constant to optimize CPU locality, to be used in conjunction with
<code class="xref py py-data docutils literal notranslate"><span class="pre">SO_REUSEPORT</span></code>.</p>
</div></blockquote>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 3.9</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.AF_HYPERV">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">AF_HYPERV</span></span><a class="headerlink" href="#socket.AF_HYPERV" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_PROTOCOL_RAW">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_PROTOCOL_RAW</span></span><a class="headerlink" href="#socket.HV_PROTOCOL_RAW" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HVSOCKET_CONNECT_TIMEOUT">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HVSOCKET_CONNECT_TIMEOUT</span></span><a class="headerlink" href="#socket.HVSOCKET_CONNECT_TIMEOUT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HVSOCKET_CONNECT_TIMEOUT_MAX">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HVSOCKET_CONNECT_TIMEOUT_MAX</span></span><a class="headerlink" href="#socket.HVSOCKET_CONNECT_TIMEOUT_MAX" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HVSOCKET_CONNECTED_SUSPEND">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HVSOCKET_CONNECTED_SUSPEND</span></span><a class="headerlink" href="#socket.HVSOCKET_CONNECTED_SUSPEND" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HVSOCKET_ADDRESS_FLAG_PASSTHRU">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HVSOCKET_ADDRESS_FLAG_PASSTHRU</span></span><a class="headerlink" href="#socket.HVSOCKET_ADDRESS_FLAG_PASSTHRU" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_GUID_ZERO">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_GUID_ZERO</span></span><a class="headerlink" href="#socket.HV_GUID_ZERO" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_GUID_WILDCARD">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_GUID_WILDCARD</span></span><a class="headerlink" href="#socket.HV_GUID_WILDCARD" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_GUID_BROADCAST">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_GUID_BROADCAST</span></span><a class="headerlink" href="#socket.HV_GUID_BROADCAST" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_GUID_CHILDREN">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_GUID_CHILDREN</span></span><a class="headerlink" href="#socket.HV_GUID_CHILDREN" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_GUID_LOOPBACK">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_GUID_LOOPBACK</span></span><a class="headerlink" href="#socket.HV_GUID_LOOPBACK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.HV_GUID_PARENT">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">HV_GUID_PARENT</span></span><a class="headerlink" href="#socket.HV_GUID_PARENT" title="Link to this definition">¶</a></dt>
<dd><p>Constants for Windows Hyper-V sockets for host/guest communications.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data" id="socket-ethernet-types">
<dt class="sig sig-object py" id="socket.ETHERTYPE_ARP">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ETHERTYPE_ARP</span></span><a class="headerlink" href="#socket.ETHERTYPE_ARP" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.ETHERTYPE_IP">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ETHERTYPE_IP</span></span><a class="headerlink" href="#socket.ETHERTYPE_IP" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.ETHERTYPE_IPV6">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ETHERTYPE_IPV6</span></span><a class="headerlink" href="#socket.ETHERTYPE_IPV6" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="socket.ETHERTYPE_VLAN">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ETHERTYPE_VLAN</span></span><a class="headerlink" href="#socket.ETHERTYPE_VLAN" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference external" href="https://www.iana.org/assignments/ieee-802-numbers/ieee-802-numbers.txt">IEEE 802.3 protocol number</a>.
constants.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux, FreeBSD, macOS.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</section>
<section id="functions">
<h3>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h3>
<section id="creating-sockets">
<h4>Creating sockets<a class="headerlink" href="#creating-sockets" title="Link to this heading">¶</a></h4>
<p>The following functions all create <a class="reference internal" href="#socket-objects"><span class="std std-ref">socket objects</span></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="socket.socket">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">socket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">family</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">AF_INET</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">SOCK_STREAM</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proto</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fileno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket" title="Link to this definition">¶</a></dt>
<dd><p>Create a new socket using the given address family, socket type and protocol
number.  The address family should be <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a> (the default),
<a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET6</span></code></a>, <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a>, <a class="reference internal" href="#socket.AF_CAN" title="socket.AF_CAN"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_CAN</span></code></a>, <a class="reference internal" href="#socket.AF_PACKET" title="socket.AF_PACKET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_PACKET</span></code></a>,
or <a class="reference internal" href="#socket.AF_RDS" title="socket.AF_RDS"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_RDS</span></code></a>. The socket type should be <a class="reference internal" href="#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a> (the
default), <a class="reference internal" href="#socket.SOCK_DGRAM" title="socket.SOCK_DGRAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_DGRAM</span></code></a>, <a class="reference internal" href="#socket.SOCK_RAW" title="socket.SOCK_RAW"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_RAW</span></code></a> or perhaps one of the other
<code class="docutils literal notranslate"><span class="pre">SOCK_</span></code> constants. The protocol number is usually zero and may be omitted
or in the case where the address family is <a class="reference internal" href="#socket.AF_CAN" title="socket.AF_CAN"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_CAN</span></code></a> the protocol
should be one of <code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_RAW</span></code>, <a class="reference internal" href="#socket.CAN_BCM" title="socket.CAN_BCM"><code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_BCM</span></code></a>, <a class="reference internal" href="#socket.CAN_ISOTP" title="socket.CAN_ISOTP"><code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_ISOTP</span></code></a> or
<a class="reference internal" href="#socket.CAN_J1939" title="socket.CAN_J1939"><code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_J1939</span></code></a>.</p>
<p>If <em>fileno</em> is specified, the values for <em>family</em>, <em>type</em>, and <em>proto</em> are
auto-detected from the specified file descriptor.  Auto-detection can be
overruled by calling the function with explicit <em>family</em>, <em>type</em>, or <em>proto</em>
arguments.  This only affects how Python represents e.g. the return value
of <a class="reference internal" href="#socket.socket.getpeername" title="socket.socket.getpeername"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.getpeername()</span></code></a> but not the actual OS resource.  Unlike
<a class="reference internal" href="#socket.fromfd" title="socket.fromfd"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket.fromfd()</span></code></a>, <em>fileno</em> will return the same socket and not a
duplicate. This may help close a detached socket using
<a class="reference internal" href="#socket.close" title="socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.close()</span></code></a>.</p>
<p>The newly created socket is <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.__new__</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">family</span></code>, <code class="docutils literal notranslate"><span class="pre">type</span></code>, <code class="docutils literal notranslate"><span class="pre">protocol</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The AF_CAN family was added.
The AF_RDS family was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The CAN_BCM protocol was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The returned socket is now non-inheritable.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The CAN_ISOTP protocol was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>When <a class="reference internal" href="#socket.SOCK_NONBLOCK" title="socket.SOCK_NONBLOCK"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_NONBLOCK</span></code></a> or <a class="reference internal" href="#socket.SOCK_CLOEXEC" title="socket.SOCK_CLOEXEC"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_CLOEXEC</span></code></a>
bit flags are applied to <em>type</em> they are cleared, and
<a class="reference internal" href="#socket.socket.type" title="socket.socket.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">socket.type</span></code></a> will not reflect them.  They are still passed
to the underlying system <code class="docutils literal notranslate"><span class="pre">socket()</span></code> call.  Therefore,</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">sock</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span>
    <span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span>
    <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span> <span class="o">|</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_NONBLOCK</span><span class="p">)</span>
</pre></div>
</div>
<p>will still create a non-blocking socket on OSes that support
<code class="docutils literal notranslate"><span class="pre">SOCK_NONBLOCK</span></code>, but <code class="docutils literal notranslate"><span class="pre">sock.type</span></code> will be set to
<code class="docutils literal notranslate"><span class="pre">socket.SOCK_STREAM</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The CAN_J1939 protocol was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The IPPROTO_MPTCP protocol was added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.socketpair">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">socketpair</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">family</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">proto</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socketpair" title="Link to this definition">¶</a></dt>
<dd><p>Build a pair of connected socket objects using the given address family, socket
type, and protocol number.  Address family, socket type, and protocol number are
as for the <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a> function above. The default family is <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a>
if defined on the platform; otherwise, the default is <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a>.</p>
<p>The newly created sockets are <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The returned socket objects now support the whole socket API, rather
than a subset.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The returned sockets are now non-inheritable.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Windows support added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.create_connection">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">create_connection</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">GLOBAL_DEFAULT</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_address</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">all_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.create_connection" title="Link to this definition">¶</a></dt>
<dd><p>Connect to a TCP service listening on the internet <em>address</em> (a 2-tuple
<code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code>), and return the socket object.  This is a higher-level
function than <a class="reference internal" href="#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.connect()</span></code></a>: if <em>host</em> is a non-numeric hostname,
it will try to resolve it for both <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-data docutils literal notranslate"><span class="pre">AF_INET</span></code></a> and <a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-data docutils literal notranslate"><span class="pre">AF_INET6</span></code></a>,
and then try to connect to all possible addresses in turn until a
connection succeeds.  This makes it easy to write clients that are
compatible to both IPv4 and IPv6.</p>
<p>Passing the optional <em>timeout</em> parameter will set the timeout on the
socket instance before attempting to connect.  If no <em>timeout</em> is
supplied, the global default timeout setting returned by
<a class="reference internal" href="#socket.getdefaulttimeout" title="socket.getdefaulttimeout"><code class="xref py py-func docutils literal notranslate"><span class="pre">getdefaulttimeout()</span></code></a> is used.</p>
<p>If supplied, <em>source_address</em> must be a 2-tuple <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> for the
socket to bind to as its source address before connecting.  If host or port
are ‘’ or 0 respectively the OS default behavior will be used.</p>
<p>When a connection cannot be created, an exception is raised. By default,
it is the exception from the last address in the list. If <em>all_errors</em>
is <code class="docutils literal notranslate"><span class="pre">True</span></code>, it is an <a class="reference internal" href="exceptions.html#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a> containing the errors of all
attempts.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>source_address</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><em>all_errors</em> was added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.create_server">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">create_server</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">family</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">AF_INET</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backlog</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reuse_port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dualstack_ipv6</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.create_server" title="Link to this definition">¶</a></dt>
<dd><p>Convenience function which creates a TCP socket bound to <em>address</em> (a 2-tuple
<code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code>) and returns the socket object.</p>
<p><em>family</em> should be either <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-data docutils literal notranslate"><span class="pre">AF_INET</span></code></a> or <a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-data docutils literal notranslate"><span class="pre">AF_INET6</span></code></a>.
<em>backlog</em> is the queue size passed to <a class="reference internal" href="#socket.socket.listen" title="socket.socket.listen"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.listen()</span></code></a>; if not specified
, a default reasonable value is chosen.
<em>reuse_port</em> dictates whether to set the <code class="xref py py-data docutils literal notranslate"><span class="pre">SO_REUSEPORT</span></code> socket option.</p>
<p>If <em>dualstack_ipv6</em> is true and the platform supports it the socket will
be able to accept both IPv4 and IPv6 connections, else it will raise
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>. Most POSIX platforms and Windows are supposed to support
this functionality.
When this functionality is enabled the address returned by
<a class="reference internal" href="#socket.socket.getpeername" title="socket.socket.getpeername"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.getpeername()</span></code></a> when an IPv4 connection occurs will be an IPv6
address represented as an IPv4-mapped IPv6 address.
If <em>dualstack_ipv6</em> is false it will explicitly disable this functionality
on platforms that enable it by default (e.g. Linux).
This parameter can be used in conjunction with <a class="reference internal" href="#socket.has_dualstack_ipv6" title="socket.has_dualstack_ipv6"><code class="xref py py-func docutils literal notranslate"><span class="pre">has_dualstack_ipv6()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span>

<span class="n">addr</span> <span class="o">=</span> <span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="mi">8080</span><span class="p">)</span>  <span class="c1"># all interfaces, port 8080</span>
<span class="k">if</span> <span class="n">socket</span><span class="o">.</span><span class="n">has_dualstack_ipv6</span><span class="p">():</span>
    <span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">create_server</span><span class="p">(</span><span class="n">addr</span><span class="p">,</span> <span class="n">family</span><span class="o">=</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET6</span><span class="p">,</span> <span class="n">dualstack_ipv6</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">create_server</span><span class="p">(</span><span class="n">addr</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On POSIX platforms the <code class="xref py py-data docutils literal notranslate"><span class="pre">SO_REUSEADDR</span></code> socket option is set in order to
immediately reuse previous sockets which were bound on the same <em>address</em>
and remained in TIME_WAIT state.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.has_dualstack_ipv6">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">has_dualstack_ipv6</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.has_dualstack_ipv6" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the platform supports creating a TCP socket which can
handle both IPv4 and IPv6 connections.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.fromfd">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">fromfd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">family</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proto</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.fromfd" title="Link to this definition">¶</a></dt>
<dd><p>Duplicate the file descriptor <em>fd</em> (an integer as returned by a file object’s
<a class="reference internal" href="io.html#io.IOBase.fileno" title="io.IOBase.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a> method) and build a socket object from the result.  Address
family, socket type and protocol number are as for the <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a> function
above. The file descriptor should refer to a socket, but this is not checked —
subsequent operations on the object may fail if the file descriptor is invalid.
This function is rarely needed, but can be used to get or set socket options on
a socket passed to a program as standard input or output (such as a server
started by the Unix inet daemon).  The socket is assumed to be in blocking mode.</p>
<p>The newly created socket is <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The returned socket is now non-inheritable.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.fromshare">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">fromshare</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.fromshare" title="Link to this definition">¶</a></dt>
<dd><p>Instantiate a socket from data obtained from the <a class="reference internal" href="#socket.socket.share" title="socket.socket.share"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.share()</span></code></a>
method.  The socket is assumed to be in blocking mode.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="socket.SocketType">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">SocketType</span></span><a class="headerlink" href="#socket.SocketType" title="Link to this definition">¶</a></dt>
<dd><p>This is a Python type object that represents the socket object type. It is the
same as <code class="docutils literal notranslate"><span class="pre">type(socket(...))</span></code>.</p>
</dd></dl>

</section>
<section id="other-functions">
<h4>Other functions<a class="headerlink" href="#other-functions" title="Link to this heading">¶</a></h4>
<p>The <a class="reference internal" href="#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> module also offers various network-related services:</p>
<dl class="py function">
<dt class="sig sig-object py" id="socket.close">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.close" title="Link to this definition">¶</a></dt>
<dd><p>Close a socket file descriptor. This is like <a class="reference internal" href="os.html#os.close" title="os.close"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.close()</span></code></a>, but for
sockets. On some platforms (most noticeable Windows) <a class="reference internal" href="os.html#os.close" title="os.close"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.close()</span></code></a>
does not work for socket file descriptors.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getaddrinfo">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getaddrinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">family</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proto</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.getaddrinfo" title="Link to this definition">¶</a></dt>
<dd><p>Translate the <em>host</em>/<em>port</em> argument into a sequence of 5-tuples that contain
all the necessary arguments for creating a socket connected to that service.
<em>host</em> is a domain name, a string representation of an IPv4/v6 address
or <code class="docutils literal notranslate"><span class="pre">None</span></code>. <em>port</em> is a string service name such as <code class="docutils literal notranslate"><span class="pre">'http'</span></code>, a numeric
port number or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  By passing <code class="docutils literal notranslate"><span class="pre">None</span></code> as the value of <em>host</em>
and <em>port</em>, you can pass <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to the underlying C API.</p>
<p>The <em>family</em>, <em>type</em> and <em>proto</em> arguments can be optionally specified
in order to narrow the list of addresses returned.  Passing zero as a
value for each of these arguments selects the full range of results.
The <em>flags</em> argument can be one or several of the <code class="docutils literal notranslate"><span class="pre">AI_*</span></code> constants,
and will influence how results are computed and returned.
For example, <code class="xref py py-const docutils literal notranslate"><span class="pre">AI_NUMERICHOST</span></code> will disable domain name resolution
and will raise an error if <em>host</em> is a domain name.</p>
<p>The function returns a list of 5-tuples with the following structure:</p>
<p><code class="docutils literal notranslate"><span class="pre">(family,</span> <span class="pre">type,</span> <span class="pre">proto,</span> <span class="pre">canonname,</span> <span class="pre">sockaddr)</span></code></p>
<p>In these tuples, <em>family</em>, <em>type</em>, <em>proto</em> are all integers and are
meant to be passed to the <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a> function.  <em>canonname</em> will be
a string representing the canonical name of the <em>host</em> if
<code class="xref py py-const docutils literal notranslate"><span class="pre">AI_CANONNAME</span></code> is part of the <em>flags</em> argument; else <em>canonname</em>
will be empty.  <em>sockaddr</em> is a tuple describing a socket address, whose
format depends on the returned <em>family</em> (a <code class="docutils literal notranslate"><span class="pre">(address,</span> <span class="pre">port)</span></code> 2-tuple for
<a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a>, a <code class="docutils literal notranslate"><span class="pre">(address,</span> <span class="pre">port,</span> <span class="pre">flowinfo,</span> <span class="pre">scope_id)</span></code> 4-tuple for
<a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET6</span></code></a>), and is meant to be passed to the <a class="reference internal" href="#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.connect()</span></code></a>
method.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.getaddrinfo</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>, <code class="docutils literal notranslate"><span class="pre">family</span></code>, <code class="docutils literal notranslate"><span class="pre">type</span></code>, <code class="docutils literal notranslate"><span class="pre">protocol</span></code>.</p>
<p>The following example fetches address information for a hypothetical TCP
connection to <code class="docutils literal notranslate"><span class="pre">example.org</span></code> on port 80 (results may differ on your
system if IPv6 isn’t enabled):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">socket</span><span class="o">.</span><span class="n">getaddrinfo</span><span class="p">(</span><span class="s2">&quot;example.org&quot;</span><span class="p">,</span> <span class="mi">80</span><span class="p">,</span> <span class="n">proto</span><span class="o">=</span><span class="n">socket</span><span class="o">.</span><span class="n">IPPROTO_TCP</span><span class="p">)</span>
<span class="go">[(socket.AF_INET6, socket.SOCK_STREAM,</span>
<span class="go"> 6, &#39;&#39;, (&#39;2606:2800:220:1:248:1893:25c8:1946&#39;, 80, 0, 0)),</span>
<span class="go"> (socket.AF_INET, socket.SOCK_STREAM,</span>
<span class="go"> 6, &#39;&#39;, (&#39;*************&#39;, 80))]</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>parameters can now be passed using keyword arguments.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>for IPv6 multicast addresses, string representing an address will not
contain <code class="docutils literal notranslate"><span class="pre">%scope_id</span></code> part.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getfqdn">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getfqdn</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.getfqdn" title="Link to this definition">¶</a></dt>
<dd><p>Return a fully qualified domain name for <em>name</em>. If <em>name</em> is omitted or empty,
it is interpreted as the local host.  To find the fully qualified name, the
hostname returned by <a class="reference internal" href="#socket.gethostbyaddr" title="socket.gethostbyaddr"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyaddr()</span></code></a> is checked, followed by aliases for the
host, if available.  The first name which includes a period is selected.  In
case no fully qualified domain name is available and <em>name</em> was provided,
it is returned unchanged.  If <em>name</em> was empty or equal to <code class="docutils literal notranslate"><span class="pre">'0.0.0.0'</span></code>,
the hostname from <a class="reference internal" href="#socket.gethostname" title="socket.gethostname"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostname()</span></code></a> is returned.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.gethostbyname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">gethostbyname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hostname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.gethostbyname" title="Link to this definition">¶</a></dt>
<dd><p>Translate a host name to IPv4 address format.  The IPv4 address is returned as a
string, such as  <code class="docutils literal notranslate"><span class="pre">'************'</span></code>.  If the host name is an IPv4 address itself
it is returned unchanged.  See <a class="reference internal" href="#socket.gethostbyname_ex" title="socket.gethostbyname_ex"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyname_ex()</span></code></a> for a more complete
interface. <a class="reference internal" href="#socket.gethostbyname" title="socket.gethostbyname"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyname()</span></code></a> does not support IPv6 name resolution, and
<a class="reference internal" href="#socket.getaddrinfo" title="socket.getaddrinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">getaddrinfo()</span></code></a> should be used instead for IPv4/v6 dual stack support.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.gethostbyname</span></code> with argument <code class="docutils literal notranslate"><span class="pre">hostname</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.gethostbyname_ex">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">gethostbyname_ex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hostname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.gethostbyname_ex" title="Link to this definition">¶</a></dt>
<dd><p>Translate a host name to IPv4 address format, extended interface. Return a
3-tuple <code class="docutils literal notranslate"><span class="pre">(hostname,</span> <span class="pre">aliaslist,</span> <span class="pre">ipaddrlist)</span></code> where <em>hostname</em> is the host’s
primary host name, <em>aliaslist</em> is a (possibly
empty) list of alternative host names for the same address, and <em>ipaddrlist</em> is
a list of IPv4 addresses for the same interface on the same host (often but not
always a single address). <a class="reference internal" href="#socket.gethostbyname_ex" title="socket.gethostbyname_ex"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyname_ex()</span></code></a> does not support IPv6 name
resolution, and <a class="reference internal" href="#socket.getaddrinfo" title="socket.getaddrinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">getaddrinfo()</span></code></a> should be used instead for IPv4/v6 dual
stack support.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.gethostbyname</span></code> with argument <code class="docutils literal notranslate"><span class="pre">hostname</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.gethostname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">gethostname</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.gethostname" title="Link to this definition">¶</a></dt>
<dd><p>Return a string containing the hostname of the machine where  the Python
interpreter is currently executing.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.gethostname</span></code> with no arguments.</p>
<p>Note: <a class="reference internal" href="#socket.gethostname" title="socket.gethostname"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostname()</span></code></a> doesn’t always return the fully qualified domain
name; use <a class="reference internal" href="#socket.getfqdn" title="socket.getfqdn"><code class="xref py py-func docutils literal notranslate"><span class="pre">getfqdn()</span></code></a> for that.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.gethostbyaddr">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">gethostbyaddr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.gethostbyaddr" title="Link to this definition">¶</a></dt>
<dd><p>Return a 3-tuple <code class="docutils literal notranslate"><span class="pre">(hostname,</span> <span class="pre">aliaslist,</span> <span class="pre">ipaddrlist)</span></code> where <em>hostname</em> is the
primary host name responding to the given <em>ip_address</em>, <em>aliaslist</em> is a
(possibly empty) list of alternative host names for the same address, and
<em>ipaddrlist</em> is a list of IPv4/v6 addresses for the same interface on the same
host (most likely containing only a single address). To find the fully qualified
domain name, use the function <a class="reference internal" href="#socket.getfqdn" title="socket.getfqdn"><code class="xref py py-func docutils literal notranslate"><span class="pre">getfqdn()</span></code></a>. <a class="reference internal" href="#socket.gethostbyaddr" title="socket.gethostbyaddr"><code class="xref py py-func docutils literal notranslate"><span class="pre">gethostbyaddr()</span></code></a> supports
both IPv4 and IPv6.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.gethostbyaddr</span></code> with argument <code class="docutils literal notranslate"><span class="pre">ip_address</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getnameinfo">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getnameinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sockaddr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.getnameinfo" title="Link to this definition">¶</a></dt>
<dd><p>Translate a socket address <em>sockaddr</em> into a 2-tuple <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code>. Depending
on the settings of <em>flags</em>, the result can contain a fully qualified domain name
or numeric address representation in <em>host</em>.  Similarly, <em>port</em> can contain a
string port name or a numeric port number.</p>
<p>For IPv6 addresses, <code class="docutils literal notranslate"><span class="pre">%scope_id</span></code> is appended to the host part if <em>sockaddr</em>
contains meaningful <em>scope_id</em>. Usually this happens for multicast addresses.</p>
<p>For more information about <em>flags</em> you can consult <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/getnameinfo(3)">getnameinfo(3)</a></em>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.getnameinfo</span></code> with argument <code class="docutils literal notranslate"><span class="pre">sockaddr</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getprotobyname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getprotobyname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">protocolname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.getprotobyname" title="Link to this definition">¶</a></dt>
<dd><p>Translate an internet protocol name (for example, <code class="docutils literal notranslate"><span class="pre">'icmp'</span></code>) to a constant
suitable for passing as the (optional) third argument to the <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a>
function.  This is usually only needed for sockets opened in “raw” mode
(<a class="reference internal" href="#socket.SOCK_RAW" title="socket.SOCK_RAW"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_RAW</span></code></a>); for the normal socket modes, the correct protocol is chosen
automatically if the protocol is omitted or zero.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getservbyname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getservbyname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">servicename</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">protocolname</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.getservbyname" title="Link to this definition">¶</a></dt>
<dd><p>Translate an internet service name and protocol name to a port number for that
service.  The optional protocol name, if given, should be <code class="docutils literal notranslate"><span class="pre">'tcp'</span></code> or
<code class="docutils literal notranslate"><span class="pre">'udp'</span></code>, otherwise any protocol will match.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.getservbyname</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">servicename</span></code>, <code class="docutils literal notranslate"><span class="pre">protocolname</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getservbyport">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getservbyport</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">port</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">protocolname</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.getservbyport" title="Link to this definition">¶</a></dt>
<dd><p>Translate an internet port number and protocol name to a service name for that
service.  The optional protocol name, if given, should be <code class="docutils literal notranslate"><span class="pre">'tcp'</span></code> or
<code class="docutils literal notranslate"><span class="pre">'udp'</span></code>, otherwise any protocol will match.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.getservbyport</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">port</span></code>, <code class="docutils literal notranslate"><span class="pre">protocolname</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.ntohl">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ntohl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.ntohl" title="Link to this definition">¶</a></dt>
<dd><p>Convert 32-bit positive integers from network to host byte order.  On machines
where the host byte order is the same as network byte order, this is a no-op;
otherwise, it performs a 4-byte swap operation.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.ntohs">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ntohs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.ntohs" title="Link to this definition">¶</a></dt>
<dd><p>Convert 16-bit positive integers from network to host byte order.  On machines
where the host byte order is the same as network byte order, this is a no-op;
otherwise, it performs a 2-byte swap operation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Raises <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> if <em>x</em> does not fit in a 16-bit unsigned
integer.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.htonl">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">htonl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.htonl" title="Link to this definition">¶</a></dt>
<dd><p>Convert 32-bit positive integers from host to network byte order.  On machines
where the host byte order is the same as network byte order, this is a no-op;
otherwise, it performs a 4-byte swap operation.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.htons">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">htons</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.htons" title="Link to this definition">¶</a></dt>
<dd><p>Convert 16-bit positive integers from host to network byte order.  On machines
where the host byte order is the same as network byte order, this is a no-op;
otherwise, it performs a 2-byte swap operation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Raises <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> if <em>x</em> does not fit in a 16-bit unsigned
integer.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.inet_aton">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">inet_aton</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.inet_aton" title="Link to this definition">¶</a></dt>
<dd><p>Convert an IPv4 address from dotted-quad string format (for example,
‘************’) to 32-bit packed binary format, as a bytes object four characters in
length.  This is useful when conversing with a program that uses the standard C
library and needs objects of type <code class="xref c c-struct docutils literal notranslate"><span class="pre">in_addr</span></code>, which is the C type
for the 32-bit packed binary this function returns.</p>
<p><a class="reference internal" href="#socket.inet_aton" title="socket.inet_aton"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_aton()</span></code></a> also accepts strings with less than three dots; see the
Unix manual page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/inet(3)">inet(3)</a></em> for details.</p>
<p>If the IPv4 address string passed to this function is invalid,
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> will be raised. Note that exactly what is valid depends on
the underlying C implementation of <code class="xref c c-func docutils literal notranslate"><span class="pre">inet_aton()</span></code>.</p>
<p><a class="reference internal" href="#socket.inet_aton" title="socket.inet_aton"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_aton()</span></code></a> does not support IPv6, and <a class="reference internal" href="#socket.inet_pton" title="socket.inet_pton"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_pton()</span></code></a> should be used
instead for IPv4/v6 dual stack support.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.inet_ntoa">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">inet_ntoa</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">packed_ip</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.inet_ntoa" title="Link to this definition">¶</a></dt>
<dd><p>Convert a 32-bit packed IPv4 address (a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> four
bytes in length) to its standard dotted-quad string representation (for example,
‘************’).  This is useful when conversing with a program that uses the
standard C library and needs objects of type <code class="xref c c-struct docutils literal notranslate"><span class="pre">in_addr</span></code>, which
is the C type for the 32-bit packed binary data this function takes as an
argument.</p>
<p>If the byte sequence passed to this function is not exactly 4 bytes in
length, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> will be raised. <a class="reference internal" href="#socket.inet_ntoa" title="socket.inet_ntoa"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_ntoa()</span></code></a> does not
support IPv6, and <a class="reference internal" href="#socket.inet_ntop" title="socket.inet_ntop"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_ntop()</span></code></a> should be used instead for IPv4/v6 dual
stack support.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.inet_pton">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">inet_pton</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address_family</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ip_string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.inet_pton" title="Link to this definition">¶</a></dt>
<dd><p>Convert an IP address from its family-specific string format to a packed,
binary format. <a class="reference internal" href="#socket.inet_pton" title="socket.inet_pton"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_pton()</span></code></a> is useful when a library or network protocol
calls for an object of type <code class="xref c c-struct docutils literal notranslate"><span class="pre">in_addr</span></code> (similar to
<a class="reference internal" href="#socket.inet_aton" title="socket.inet_aton"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_aton()</span></code></a>) or <code class="xref c c-struct docutils literal notranslate"><span class="pre">in6_addr</span></code>.</p>
<p>Supported values for <em>address_family</em> are currently <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a> and
<a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET6</span></code></a>. If the IP address string <em>ip_string</em> is invalid,
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> will be raised. Note that exactly what is valid depends on
both the value of <em>address_family</em> and the underlying implementation of
<code class="xref c c-func docutils literal notranslate"><span class="pre">inet_pton()</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Windows support added</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.inet_ntop">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">inet_ntop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address_family</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">packed_ip</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.inet_ntop" title="Link to this definition">¶</a></dt>
<dd><p>Convert a packed IP address (a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> of some number of
bytes) to its standard, family-specific string representation (for
example, <code class="docutils literal notranslate"><span class="pre">'********'</span></code> or <code class="docutils literal notranslate"><span class="pre">'5aef:2b::8'</span></code>).
<a class="reference internal" href="#socket.inet_ntop" title="socket.inet_ntop"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_ntop()</span></code></a> is useful when a library or network protocol returns an
object of type <code class="xref c c-struct docutils literal notranslate"><span class="pre">in_addr</span></code> (similar to <a class="reference internal" href="#socket.inet_ntoa" title="socket.inet_ntoa"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_ntoa()</span></code></a>) or
<code class="xref c c-struct docutils literal notranslate"><span class="pre">in6_addr</span></code>.</p>
<p>Supported values for <em>address_family</em> are currently <a class="reference internal" href="#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a> and
<a class="reference internal" href="#socket.AF_INET6" title="socket.AF_INET6"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET6</span></code></a>. If the bytes object <em>packed_ip</em> is not the correct
length for the specified address family, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be raised.
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised for errors from the call to <a class="reference internal" href="#socket.inet_ntop" title="socket.inet_ntop"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_ntop()</span></code></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Windows support added</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.CMSG_LEN">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CMSG_LEN</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.CMSG_LEN" title="Link to this definition">¶</a></dt>
<dd><p>Return the total length, without trailing padding, of an ancillary
data item with associated data of the given <em>length</em>.  This value
can often be used as the buffer size for <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> to
receive a single item of ancillary data, but <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3542.html"><strong>RFC 3542</strong></a> requires
portable applications to use <a class="reference internal" href="#socket.CMSG_SPACE" title="socket.CMSG_SPACE"><code class="xref py py-func docutils literal notranslate"><span class="pre">CMSG_SPACE()</span></code></a> and thus include
space for padding, even when the item will be the last in the
buffer.  Raises <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> if <em>length</em> is outside the
permissible range of values.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, not Emscripten, not WASI.</p>
<p>Most Unix platforms.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.CMSG_SPACE">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">CMSG_SPACE</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.CMSG_SPACE" title="Link to this definition">¶</a></dt>
<dd><p>Return the buffer size needed for <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> to
receive an ancillary data item with associated data of the given
<em>length</em>, along with any trailing padding.  The buffer space needed
to receive multiple items is the sum of the <a class="reference internal" href="#socket.CMSG_SPACE" title="socket.CMSG_SPACE"><code class="xref py py-func docutils literal notranslate"><span class="pre">CMSG_SPACE()</span></code></a>
values for their associated data lengths.  Raises
<a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> if <em>length</em> is outside the permissible range
of values.</p>
<p>Note that some systems might support ancillary data without
providing this function.  Also note that setting the buffer size
using the results of this function may not precisely limit the
amount of ancillary data that can be received, since additional
data may be able to fit into the padding area.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, not Emscripten, not WASI.</p>
<p>most Unix platforms.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.getdefaulttimeout">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getdefaulttimeout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.getdefaulttimeout" title="Link to this definition">¶</a></dt>
<dd><p>Return the default timeout in seconds (float) for new socket objects. A value
of <code class="docutils literal notranslate"><span class="pre">None</span></code> indicates that new socket objects have no timeout. When the socket
module is first imported, the default is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.setdefaulttimeout">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">setdefaulttimeout</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.setdefaulttimeout" title="Link to this definition">¶</a></dt>
<dd><p>Set the default timeout in seconds (float) for new socket objects.  When
the socket module is first imported, the default is <code class="docutils literal notranslate"><span class="pre">None</span></code>.  See
<a class="reference internal" href="#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a> for possible values and their respective
meanings.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.sethostname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sethostname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.sethostname" title="Link to this definition">¶</a></dt>
<dd><p>Set the machine’s hostname to <em>name</em>.  This will raise an
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if you don’t have enough rights.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.sethostname</span></code> with argument <code class="docutils literal notranslate"><span class="pre">name</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.if_nameindex">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">if_nameindex</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.if_nameindex" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of network interface information
(index int, name string) tuples.
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the system call fails.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows, not Emscripten, not WASI.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Windows support was added.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Windows network interfaces have different names in different contexts
(all names are examples):</p>
<ul class="simple">
<li><p>UUID: <code class="docutils literal notranslate"><span class="pre">{FB605B73-AAC2-49A6-9A2F-25416AEA0573}</span></code></p></li>
<li><p>name: <code class="docutils literal notranslate"><span class="pre">ethernet_32770</span></code></p></li>
<li><p>friendly name: <code class="docutils literal notranslate"><span class="pre">vEthernet</span> <span class="pre">(nat)</span></code></p></li>
<li><p>description: <code class="docutils literal notranslate"><span class="pre">Hyper-V</span> <span class="pre">Virtual</span> <span class="pre">Ethernet</span> <span class="pre">Adapter</span></code></p></li>
</ul>
<p>This function returns names of the second form from the list, <code class="docutils literal notranslate"><span class="pre">ethernet_32770</span></code>
in this example case.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.if_nametoindex">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">if_nametoindex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">if_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.if_nametoindex" title="Link to this definition">¶</a></dt>
<dd><p>Return a network interface index number corresponding to an
interface name.
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if no interface with the given name exists.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows, not Emscripten, not WASI.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Windows support was added.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>“Interface name” is a name as documented in <a class="reference internal" href="#socket.if_nameindex" title="socket.if_nameindex"><code class="xref py py-func docutils literal notranslate"><span class="pre">if_nameindex()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.if_indextoname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">if_indextoname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">if_index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.if_indextoname" title="Link to this definition">¶</a></dt>
<dd><p>Return a network interface name corresponding to an
interface index number.
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if no interface with the given index exists.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows, not Emscripten, not WASI.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Windows support was added.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>“Interface name” is a name as documented in <a class="reference internal" href="#socket.if_nameindex" title="socket.if_nameindex"><code class="xref py py-func docutils literal notranslate"><span class="pre">if_nameindex()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.send_fds">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">send_fds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sock</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffers</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fds</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.send_fds" title="Link to this definition">¶</a></dt>
<dd><p>Send the list of file descriptors <em>fds</em> over an <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a> socket <em>sock</em>.
The <em>fds</em> parameter is a sequence of file descriptors.
Consult <a class="reference internal" href="#socket.socket.sendmsg" title="socket.socket.sendmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmsg()</span></code></a> for the documentation of these parameters.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows, not Emscripten, not WASI.</p>
<p>Unix platforms supporting <a class="reference internal" href="#socket.socket.sendmsg" title="socket.socket.sendmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmsg()</span></code></a>
and <code class="xref py py-const docutils literal notranslate"><span class="pre">SCM_RIGHTS</span></code> mechanism.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="socket.recv_fds">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recv_fds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sock</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bufsize</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxfds</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.recv_fds" title="Link to this definition">¶</a></dt>
<dd><p>Receive up to <em>maxfds</em> file descriptors from an <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a> socket <em>sock</em>.
Return <code class="docutils literal notranslate"><span class="pre">(msg,</span> <span class="pre">list(fds),</span> <span class="pre">flags,</span> <span class="pre">addr)</span></code>.
Consult <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> for the documentation of these parameters.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows, not Emscripten, not WASI.</p>
<p>Unix platforms supporting <a class="reference internal" href="#socket.socket.sendmsg" title="socket.socket.sendmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmsg()</span></code></a>
and <code class="xref py py-const docutils literal notranslate"><span class="pre">SCM_RIGHTS</span></code> mechanism.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Any truncated integers at the end of the list of file descriptors.</p>
</div>
</dd></dl>

</section>
</section>
</section>
<section id="socket-objects">
<span id="id1"></span><h2>Socket Objects<a class="headerlink" href="#socket-objects" title="Link to this heading">¶</a></h2>
<p>Socket objects have the following methods.  Except for
<a class="reference internal" href="#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makefile()</span></code></a>, these correspond to Unix system calls applicable
to sockets.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Support for the <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> protocol was added.  Exiting the
context manager is equivalent to calling <a class="reference internal" href="#socket.close" title="socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.accept">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">accept</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.accept" title="Link to this definition">¶</a></dt>
<dd><p>Accept a connection. The socket must be bound to an address and listening for
connections. The return value is a pair <code class="docutils literal notranslate"><span class="pre">(conn,</span> <span class="pre">address)</span></code> where <em>conn</em> is a
<em>new</em> socket object usable to send and receive data on the connection, and
<em>address</em> is the address bound to the socket on the other end of the connection.</p>
<p>The newly created socket is <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The socket is now non-inheritable.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.bind">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">bind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.bind" title="Link to this definition">¶</a></dt>
<dd><p>Bind the socket to <em>address</em>.  The socket must not already be bound. (The format
of <em>address</em> depends on the address family — see above.)</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.bind</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">address</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.close">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.close" title="Link to this definition">¶</a></dt>
<dd><p>Mark the socket closed.  The underlying system resource (e.g. a file
descriptor) is also closed when all file objects from <a class="reference internal" href="#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makefile()</span></code></a>
are closed.  Once that happens, all future operations on the socket
object will fail. The remote end will receive no more data (after
queued data is flushed).</p>
<p>Sockets are automatically closed when they are garbage-collected, but
it is recommended to <a class="reference internal" href="#socket.close" title="socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> them explicitly, or to use a
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement around them.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is now raised if an error occurs when the underlying
<code class="xref c c-func docutils literal notranslate"><span class="pre">close()</span></code> call is made.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#socket.close" title="socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> releases the resource associated with a connection but
does not necessarily close the connection immediately.  If you want
to close the connection in a timely fashion, call <a class="reference internal" href="#socket.socket.shutdown" title="socket.socket.shutdown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shutdown()</span></code></a>
before <a class="reference internal" href="#socket.close" title="socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.connect">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.connect" title="Link to this definition">¶</a></dt>
<dd><p>Connect to a remote socket at <em>address</em>. (The format of <em>address</em> depends on the
address family — see above.)</p>
<p>If the connection is interrupted by a signal, the method waits until the
connection completes, or raise a <a class="reference internal" href="exceptions.html#TimeoutError" title="TimeoutError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutError</span></code></a> on timeout, if the
signal handler doesn’t raise an exception and the socket is blocking or has
a timeout. For non-blocking sockets, the method raises an
<a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception if the connection is interrupted by a
signal (or the exception raised by the signal handler).</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">address</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The method now waits until the connection completes instead of raising an
<a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception if the connection is interrupted by a
signal, the signal handler doesn’t raise an exception and the socket is
blocking or has a timeout (see the <span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.connect_ex">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">connect_ex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.connect_ex" title="Link to this definition">¶</a></dt>
<dd><p>Like <code class="docutils literal notranslate"><span class="pre">connect(address)</span></code>, but return an error indicator instead of raising an
exception for errors returned by the C-level <code class="xref c c-func docutils literal notranslate"><span class="pre">connect()</span></code> call (other
problems, such as “host not found,” can still raise exceptions).  The error
indicator is <code class="docutils literal notranslate"><span class="pre">0</span></code> if the operation succeeded, otherwise the value of the
<code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> variable.  This is useful to support, for example, asynchronous
connects.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">address</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.detach">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">detach</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.detach" title="Link to this definition">¶</a></dt>
<dd><p>Put the socket object into closed state without actually closing the
underlying file descriptor.  The file descriptor is returned, and can
be reused for other purposes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.dup">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">dup</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.dup" title="Link to this definition">¶</a></dt>
<dd><p>Duplicate the socket.</p>
<p>The newly created socket is <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The socket is now non-inheritable.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.fileno">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Return the socket’s file descriptor (a small integer), or -1 on failure. This
is useful with <a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.select()</span></code></a>.</p>
<p>Under Windows the small integer returned by this method cannot be used where a
file descriptor can be used (such as <a class="reference internal" href="os.html#os.fdopen" title="os.fdopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fdopen()</span></code></a>).  Unix does not have
this limitation.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.get_inheritable">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">get_inheritable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.get_inheritable" title="Link to this definition">¶</a></dt>
<dd><p>Get the <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">inheritable flag</span></a> of the socket’s file
descriptor or socket’s handle: <code class="docutils literal notranslate"><span class="pre">True</span></code> if the socket can be inherited in
child processes, <code class="docutils literal notranslate"><span class="pre">False</span></code> if it cannot.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.getpeername">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getpeername</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.getpeername" title="Link to this definition">¶</a></dt>
<dd><p>Return the remote address to which the socket is connected.  This is useful to
find out the port number of a remote IPv4/v6 socket, for instance. (The format
of the address returned depends on the address family — see above.)  On some
systems this function is not supported.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.getsockname">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getsockname</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.getsockname" title="Link to this definition">¶</a></dt>
<dd><p>Return the socket’s own address.  This is useful to find out the port number of
an IPv4/v6 socket, for instance. (The format of the address returned depends on
the address family — see above.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.getsockopt">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getsockopt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optname</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">buflen</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.getsockopt" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of the given socket option (see the Unix man page
<em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/getsockopt(2)">getsockopt(2)</a></em>).  The needed symbolic constants (<a class="reference internal" href="#socket-unix-constants"><span class="std std-ref">SO_* etc.</span></a>)
are defined in this module.  If <em>buflen</em> is absent, an integer option is assumed
and its integer value is returned by the function.  If <em>buflen</em> is present, it
specifies the maximum length of the buffer used to receive the option in, and
this buffer is returned as a bytes object.  It is up to the caller to decode the
contents of the buffer (see the optional built-in module <a class="reference internal" href="struct.html#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> for a way
to decode C structures encoded as byte strings).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.getblocking">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">getblocking</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.getblocking" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if socket is in blocking mode, <code class="docutils literal notranslate"><span class="pre">False</span></code> if in
non-blocking.</p>
<p>This is equivalent to checking <code class="docutils literal notranslate"><span class="pre">socket.gettimeout()</span> <span class="pre">!=</span> <span class="pre">0</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.gettimeout">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">gettimeout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.gettimeout" title="Link to this definition">¶</a></dt>
<dd><p>Return the timeout in seconds (float) associated with socket operations,
or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no timeout is set.  This reflects the last call to
<a class="reference internal" href="#socket.socket.setblocking" title="socket.socket.setblocking"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setblocking()</span></code></a> or <a class="reference internal" href="#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.ioctl">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">ioctl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">control</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.ioctl" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Platform<span class="colon">:</span></dt>
<dd class="field-odd"><p>Windows</p>
</dd>
</dl>
<p>The <a class="reference internal" href="#socket.socket.ioctl" title="socket.socket.ioctl"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ioctl()</span></code></a> method is a limited interface to the WSAIoctl system
interface.  Please refer to the <a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms741621%28VS.85%29.aspx">Win32 documentation</a> for more
information.</p>
<p>On other platforms, the generic <a class="reference internal" href="fcntl.html#fcntl.fcntl" title="fcntl.fcntl"><code class="xref py py-func docutils literal notranslate"><span class="pre">fcntl.fcntl()</span></code></a> and <a class="reference internal" href="fcntl.html#fcntl.ioctl" title="fcntl.ioctl"><code class="xref py py-func docutils literal notranslate"><span class="pre">fcntl.ioctl()</span></code></a>
functions may be used; they accept a socket object as their first argument.</p>
<p>Currently only the following control codes are supported:
<code class="docutils literal notranslate"><span class="pre">SIO_RCVALL</span></code>, <code class="docutils literal notranslate"><span class="pre">SIO_KEEPALIVE_VALS</span></code>, and <code class="docutils literal notranslate"><span class="pre">SIO_LOOPBACK_FAST_PATH</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><code class="docutils literal notranslate"><span class="pre">SIO_LOOPBACK_FAST_PATH</span></code> was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.listen">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">listen</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">backlog</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.listen" title="Link to this definition">¶</a></dt>
<dd><p>Enable a server to accept connections.  If <em>backlog</em> is specified, it must
be at least 0 (if it is lower, it is set to 0); it specifies the number of
unaccepted connections that the system will allow before refusing new
connections. If not specified, a default reasonable value is chosen.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>backlog</em> parameter is now optional.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.makefile">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">makefile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffering</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.makefile" title="Link to this definition">¶</a></dt>
<dd><p id="index-6">Return a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> associated with the socket.  The exact returned
type depends on the arguments given to <a class="reference internal" href="#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makefile()</span></code></a>.  These arguments are
interpreted the same way as by the built-in <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function, except
the only supported <em>mode</em> values are <code class="docutils literal notranslate"><span class="pre">'r'</span></code> (default), <code class="docutils literal notranslate"><span class="pre">'w'</span></code> and <code class="docutils literal notranslate"><span class="pre">'b'</span></code>.</p>
<p>The socket must be in blocking mode; it can have a timeout, but the file
object’s internal buffer may end up in an inconsistent state if a timeout
occurs.</p>
<p>Closing the file object returned by <a class="reference internal" href="#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makefile()</span></code></a> won’t close the
original socket unless all other file objects have been closed and
<a class="reference internal" href="#socket.close" title="socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.close()</span></code></a> has been called on the socket object.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Windows, the file-like object created by <a class="reference internal" href="#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makefile()</span></code></a> cannot be
used where a file object with a file descriptor is expected, such as the
stream arguments of <a class="reference internal" href="subprocess.html#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-meth docutils literal notranslate"><span class="pre">subprocess.Popen()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.recv">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recv</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bufsize</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.recv" title="Link to this definition">¶</a></dt>
<dd><p>Receive data from the socket.  The return value is a bytes object representing the
data received.  The maximum amount of data to be received at once is specified
by <em>bufsize</em>. A returned empty bytes object indicates that the client has disconnected.
See the Unix manual page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/recv(2)">recv(2)</a></em> for the meaning of the optional argument
<em>flags</em>; it defaults to zero.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For best match with hardware and network realities, the value of  <em>bufsize</em>
should be a relatively small power of 2, for example, 4096.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.recvfrom">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recvfrom</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bufsize</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.recvfrom" title="Link to this definition">¶</a></dt>
<dd><p>Receive data from the socket.  The return value is a pair <code class="docutils literal notranslate"><span class="pre">(bytes,</span> <span class="pre">address)</span></code>
where <em>bytes</em> is a bytes object representing the data received and <em>address</em> is the
address of the socket sending the data.  See the Unix manual page
<em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/recv(2)">recv(2)</a></em> for the meaning of the optional argument <em>flags</em>; it defaults
to zero. (The format of <em>address</em> depends on the address family — see above.)</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>For multicast IPv6 address, first item of <em>address</em> does not contain
<code class="docutils literal notranslate"><span class="pre">%scope_id</span></code> part anymore. In order to get full IPv6 address use
<a class="reference internal" href="#socket.getnameinfo" title="socket.getnameinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">getnameinfo()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.recvmsg">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recvmsg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bufsize</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">ancbufsize</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.recvmsg" title="Link to this definition">¶</a></dt>
<dd><p>Receive normal data (up to <em>bufsize</em> bytes) and ancillary data from
the socket.  The <em>ancbufsize</em> argument sets the size in bytes of
the internal buffer used to receive the ancillary data; it defaults
to 0, meaning that no ancillary data will be received.  Appropriate
buffer sizes for ancillary data can be calculated using
<a class="reference internal" href="#socket.CMSG_SPACE" title="socket.CMSG_SPACE"><code class="xref py py-func docutils literal notranslate"><span class="pre">CMSG_SPACE()</span></code></a> or <a class="reference internal" href="#socket.CMSG_LEN" title="socket.CMSG_LEN"><code class="xref py py-func docutils literal notranslate"><span class="pre">CMSG_LEN()</span></code></a>, and items which do not fit
into the buffer might be truncated or discarded.  The <em>flags</em>
argument defaults to 0 and has the same meaning as for
<a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a>.</p>
<p>The return value is a 4-tuple: <code class="docutils literal notranslate"><span class="pre">(data,</span> <span class="pre">ancdata,</span> <span class="pre">msg_flags,</span>
<span class="pre">address)</span></code>.  The <em>data</em> item is a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object holding the
non-ancillary data received.  The <em>ancdata</em> item is a list of zero
or more tuples <code class="docutils literal notranslate"><span class="pre">(cmsg_level,</span> <span class="pre">cmsg_type,</span> <span class="pre">cmsg_data)</span></code> representing
the ancillary data (control messages) received: <em>cmsg_level</em> and
<em>cmsg_type</em> are integers specifying the protocol level and
protocol-specific type respectively, and <em>cmsg_data</em> is a
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object holding the associated data.  The <em>msg_flags</em>
item is the bitwise OR of various flags indicating conditions on
the received message; see your system documentation for details.
If the receiving socket is unconnected, <em>address</em> is the address of
the sending socket, if available; otherwise, its value is
unspecified.</p>
<p>On some systems, <a class="reference internal" href="#socket.socket.sendmsg" title="socket.socket.sendmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmsg()</span></code></a> and <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> can be used to
pass file descriptors between processes over an <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a>
socket.  When this facility is used (it is often restricted to
<a class="reference internal" href="#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a> sockets), <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> will return, in its
ancillary data, items of the form <code class="docutils literal notranslate"><span class="pre">(socket.SOL_SOCKET,</span>
<span class="pre">socket.SCM_RIGHTS,</span> <span class="pre">fds)</span></code>, where <em>fds</em> is a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object
representing the new file descriptors as a binary array of the
native C <span class="c-expr sig sig-inline c"><span class="kt">int</span></span> type.  If <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> raises an
exception after the system call returns, it will first attempt to
close any file descriptors received via this mechanism.</p>
<p>Some systems do not indicate the truncated length of ancillary data
items which have been only partially received.  If an item appears
to extend beyond the end of the buffer, <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> will issue
a <a class="reference internal" href="exceptions.html#RuntimeWarning" title="RuntimeWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeWarning</span></code></a>, and will return the part of it which is
inside the buffer provided it has not been truncated before the
start of its associated data.</p>
<p>On systems which support the <code class="xref py py-const docutils literal notranslate"><span class="pre">SCM_RIGHTS</span></code> mechanism, the
following function will receive up to <em>maxfds</em> file descriptors,
returning the message data and a list containing the descriptors
(while ignoring unexpected conditions such as unrelated control
messages being received).  See also <a class="reference internal" href="#socket.socket.sendmsg" title="socket.socket.sendmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmsg()</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span><span class="o">,</span> <span class="nn">array</span>

<span class="k">def</span> <span class="nf">recv_fds</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">msglen</span><span class="p">,</span> <span class="n">maxfds</span><span class="p">):</span>
    <span class="n">fds</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="s2">&quot;i&quot;</span><span class="p">)</span>   <span class="c1"># Array of ints</span>
    <span class="n">msg</span><span class="p">,</span> <span class="n">ancdata</span><span class="p">,</span> <span class="n">flags</span><span class="p">,</span> <span class="n">addr</span> <span class="o">=</span> <span class="n">sock</span><span class="o">.</span><span class="n">recvmsg</span><span class="p">(</span><span class="n">msglen</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">CMSG_LEN</span><span class="p">(</span><span class="n">maxfds</span> <span class="o">*</span> <span class="n">fds</span><span class="o">.</span><span class="n">itemsize</span><span class="p">))</span>
    <span class="k">for</span> <span class="n">cmsg_level</span><span class="p">,</span> <span class="n">cmsg_type</span><span class="p">,</span> <span class="n">cmsg_data</span> <span class="ow">in</span> <span class="n">ancdata</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">cmsg_level</span> <span class="o">==</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOL_SOCKET</span> <span class="ow">and</span> <span class="n">cmsg_type</span> <span class="o">==</span> <span class="n">socket</span><span class="o">.</span><span class="n">SCM_RIGHTS</span><span class="p">:</span>
            <span class="c1"># Append data, ignoring any truncated integers at the end.</span>
            <span class="n">fds</span><span class="o">.</span><span class="n">frombytes</span><span class="p">(</span><span class="n">cmsg_data</span><span class="p">[:</span><span class="nb">len</span><span class="p">(</span><span class="n">cmsg_data</span><span class="p">)</span> <span class="o">-</span> <span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">cmsg_data</span><span class="p">)</span> <span class="o">%</span> <span class="n">fds</span><span class="o">.</span><span class="n">itemsize</span><span class="p">)])</span>
    <span class="k">return</span> <span class="n">msg</span><span class="p">,</span> <span class="nb">list</span><span class="p">(</span><span class="n">fds</span><span class="p">)</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
<p>Most Unix platforms.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.recvmsg_into">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recvmsg_into</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffers</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">ancbufsize</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.recvmsg_into" title="Link to this definition">¶</a></dt>
<dd><p>Receive normal data and ancillary data from the socket, behaving as
<a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a> would, but scatter the non-ancillary data into a
series of buffers instead of returning a new bytes object.  The
<em>buffers</em> argument must be an iterable of objects that export
writable buffers (e.g. <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> objects); these will be
filled with successive chunks of the non-ancillary data until it
has all been written or there are no more buffers.  The operating
system may set a limit (<a class="reference internal" href="os.html#os.sysconf" title="os.sysconf"><code class="xref py py-func docutils literal notranslate"><span class="pre">sysconf()</span></code></a> value <code class="docutils literal notranslate"><span class="pre">SC_IOV_MAX</span></code>)
on the number of buffers that can be used.  The <em>ancbufsize</em> and
<em>flags</em> arguments have the same meaning as for <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a>.</p>
<p>The return value is a 4-tuple: <code class="docutils literal notranslate"><span class="pre">(nbytes,</span> <span class="pre">ancdata,</span> <span class="pre">msg_flags,</span>
<span class="pre">address)</span></code>, where <em>nbytes</em> is the total number of bytes of
non-ancillary data written into the buffers, and <em>ancdata</em>,
<em>msg_flags</em> and <em>address</em> are the same as for <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a>.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">socket</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s1</span><span class="p">,</span> <span class="n">s2</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socketpair</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b1</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;----&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b2</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;0123456789&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b3</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;--------------&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s1</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Mary had a little lamb&#39;</span><span class="p">)</span>
<span class="go">22</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s2</span><span class="o">.</span><span class="n">recvmsg_into</span><span class="p">([</span><span class="n">b1</span><span class="p">,</span> <span class="nb">memoryview</span><span class="p">(</span><span class="n">b2</span><span class="p">)[</span><span class="mi">2</span><span class="p">:</span><span class="mi">9</span><span class="p">],</span> <span class="n">b3</span><span class="p">])</span>
<span class="go">(22, [], 0, None)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">b1</span><span class="p">,</span> <span class="n">b2</span><span class="p">,</span> <span class="n">b3</span><span class="p">]</span>
<span class="go">[bytearray(b&#39;Mary&#39;), bytearray(b&#39;01 had a 9&#39;), bytearray(b&#39;little lamb---&#39;)]</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
<p>Most Unix platforms.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.recvfrom_into">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recvfrom_into</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">nbytes</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.recvfrom_into" title="Link to this definition">¶</a></dt>
<dd><p>Receive data from the socket, writing it into <em>buffer</em> instead of creating a
new bytestring.  The return value is a pair <code class="docutils literal notranslate"><span class="pre">(nbytes,</span> <span class="pre">address)</span></code> where <em>nbytes</em> is
the number of bytes received and <em>address</em> is the address of the socket sending
the data.  See the Unix manual page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/recv(2)">recv(2)</a></em> for the meaning of the
optional argument <em>flags</em>; it defaults to zero.  (The format of <em>address</em>
depends on the address family — see above.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.recv_into">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">recv_into</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">nbytes</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.recv_into" title="Link to this definition">¶</a></dt>
<dd><p>Receive up to <em>nbytes</em> bytes from the socket, storing the data into a buffer
rather than creating a new bytestring.  If <em>nbytes</em> is not specified (or 0),
receive up to the size available in the given buffer.  Returns the number of
bytes received.  See the Unix manual page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/recv(2)">recv(2)</a></em> for the meaning
of the optional argument <em>flags</em>; it defaults to zero.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.send">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">send</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.send" title="Link to this definition">¶</a></dt>
<dd><p>Send data to the socket.  The socket must be connected to a remote socket.  The
optional <em>flags</em> argument has the same meaning as for <a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> above.
Returns the number of bytes sent. Applications are responsible for checking that
all data has been sent; if only some of the data was transmitted, the
application needs to attempt delivery of the remaining data. For further
information on this topic, consult the <a class="reference internal" href="../howto/sockets.html#socket-howto"><span class="std std-ref">Socket Programming HOWTO</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.sendall">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sendall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.sendall" title="Link to this definition">¶</a></dt>
<dd><p>Send data to the socket.  The socket must be connected to a remote socket.  The
optional <em>flags</em> argument has the same meaning as for <a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> above.
Unlike <a class="reference internal" href="#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a>, this method continues to send data from <em>bytes</em> until
either all data has been sent or an error occurs.  <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned on
success.  On error, an exception is raised, and there is no way to determine how
much data, if any, was successfully sent.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The socket timeout is no longer reset each time data is sent successfully.
The socket timeout is now the maximum total duration to send all data.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.sendto">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sendto</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.sendto" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sendto</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Send data to the socket.  The socket should not be connected to a remote socket,
since the destination socket is specified by <em>address</em>.  The optional <em>flags</em>
argument has the same meaning as for <a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> above.  Return the number of
bytes sent. (The format of <em>address</em> depends on the address family — see
above.)</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.sendto</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">address</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.sendmsg">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sendmsg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffers</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">ancdata</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.sendmsg" title="Link to this definition">¶</a></dt>
<dd><p>Send normal and ancillary data to the socket, gathering the
non-ancillary data from a series of buffers and concatenating it
into a single message.  The <em>buffers</em> argument specifies the
non-ancillary data as an iterable of
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like objects</span></a>
(e.g. <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects); the operating system may set a limit
(<a class="reference internal" href="os.html#os.sysconf" title="os.sysconf"><code class="xref py py-func docutils literal notranslate"><span class="pre">sysconf()</span></code></a> value <code class="docutils literal notranslate"><span class="pre">SC_IOV_MAX</span></code>) on the number of buffers
that can be used.  The <em>ancdata</em> argument specifies the ancillary
data (control messages) as an iterable of zero or more tuples
<code class="docutils literal notranslate"><span class="pre">(cmsg_level,</span> <span class="pre">cmsg_type,</span> <span class="pre">cmsg_data)</span></code>, where <em>cmsg_level</em> and
<em>cmsg_type</em> are integers specifying the protocol level and
protocol-specific type respectively, and <em>cmsg_data</em> is a
bytes-like object holding the associated data.  Note that
some systems (in particular, systems without <a class="reference internal" href="#socket.CMSG_SPACE" title="socket.CMSG_SPACE"><code class="xref py py-func docutils literal notranslate"><span class="pre">CMSG_SPACE()</span></code></a>)
might support sending only one control message per call.  The
<em>flags</em> argument defaults to 0 and has the same meaning as for
<a class="reference internal" href="#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a>.  If <em>address</em> is supplied and not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it sets a
destination address for the message.  The return value is the
number of bytes of non-ancillary data sent.</p>
<p>The following function sends the list of file descriptors <em>fds</em>
over an <a class="reference internal" href="#socket.AF_UNIX" title="socket.AF_UNIX"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_UNIX</span></code></a> socket, on systems which support the
<code class="xref py py-const docutils literal notranslate"><span class="pre">SCM_RIGHTS</span></code> mechanism.  See also <a class="reference internal" href="#socket.socket.recvmsg" title="socket.socket.recvmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvmsg()</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span><span class="o">,</span> <span class="nn">array</span>

<span class="k">def</span> <span class="nf">send_fds</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">msg</span><span class="p">,</span> <span class="n">fds</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">sock</span><span class="o">.</span><span class="n">sendmsg</span><span class="p">([</span><span class="n">msg</span><span class="p">],</span> <span class="p">[(</span><span class="n">socket</span><span class="o">.</span><span class="n">SOL_SOCKET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SCM_RIGHTS</span><span class="p">,</span> <span class="n">array</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="s2">&quot;i&quot;</span><span class="p">,</span> <span class="n">fds</span><span class="p">))])</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, not WASI.</p>
<p>Most Unix platforms.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">socket.sendmsg</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">address</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the system call is interrupted and the signal handler does not raise
an exception, the method now retries the system call instead of raising
an <a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-13"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.sendmsg_afalg">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sendmsg_afalg</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">op</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">iv</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">assoclen</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.sendmsg_afalg" title="Link to this definition">¶</a></dt>
<dd><p>Specialized version of <a class="reference internal" href="#socket.socket.sendmsg" title="socket.socket.sendmsg"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmsg()</span></code></a> for <a class="reference internal" href="#socket.AF_ALG" title="socket.AF_ALG"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_ALG</span></code></a> socket.
Set mode, IV, AEAD associated data length and flags for <a class="reference internal" href="#socket.AF_ALG" title="socket.AF_ALG"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_ALG</span></code></a> socket.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.38.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.sendfile">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">sendfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.sendfile" title="Link to this definition">¶</a></dt>
<dd><p>Send a file until EOF is reached by using high-performance
<a class="reference internal" href="os.html#os.sendfile" title="os.sendfile"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.sendfile</span></code></a> and return the total number of bytes which were sent.
<em>file</em> must be a regular file object opened in binary mode. If
<a class="reference internal" href="os.html#os.sendfile" title="os.sendfile"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.sendfile</span></code></a> is not available (e.g. Windows) or <em>file</em> is not a
regular file <a class="reference internal" href="#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a> will be used instead. <em>offset</em> tells from where to
start reading the file. If specified, <em>count</em> is the total number of bytes
to transmit as opposed to sending the file until EOF is reached. File
position is updated on return or also in case of error in which case
<a class="reference internal" href="io.html#io.IOBase.tell" title="io.IOBase.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">file.tell()</span></code></a> can be used to figure out the number of
bytes which were sent. The socket must be of <a class="reference internal" href="#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a> type.
Non-blocking sockets are not supported.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.set_inheritable">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">set_inheritable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">inheritable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.set_inheritable" title="Link to this definition">¶</a></dt>
<dd><p>Set the <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">inheritable flag</span></a> of the socket’s file
descriptor or socket’s handle.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.setblocking">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">setblocking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.setblocking" title="Link to this definition">¶</a></dt>
<dd><p>Set blocking or non-blocking mode of the socket: if <em>flag</em> is false, the
socket is set to non-blocking, else to blocking mode.</p>
<p>This method is a shorthand for certain <a class="reference internal" href="#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a> calls:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">sock.setblocking(True)</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">sock.settimeout(None)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sock.setblocking(False)</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">sock.settimeout(0.0)</span></code></p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The method no longer applies <a class="reference internal" href="#socket.SOCK_NONBLOCK" title="socket.SOCK_NONBLOCK"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_NONBLOCK</span></code></a> flag on
<a class="reference internal" href="#socket.socket.type" title="socket.socket.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">socket.type</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.settimeout">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">settimeout</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.settimeout" title="Link to this definition">¶</a></dt>
<dd><p>Set a timeout on blocking socket operations.  The <em>value</em> argument can be a
nonnegative floating point number expressing seconds, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.
If a non-zero value is given, subsequent socket operations will raise a
<a class="reference internal" href="#socket.timeout" title="socket.timeout"><code class="xref py py-exc docutils literal notranslate"><span class="pre">timeout</span></code></a> exception if the timeout period <em>value</em> has elapsed before
the operation has completed.  If zero is given, the socket is put in
non-blocking mode. If <code class="docutils literal notranslate"><span class="pre">None</span></code> is given, the socket is put in blocking mode.</p>
<p>For further information, please consult the <a class="reference internal" href="#socket-timeouts"><span class="std std-ref">notes on socket timeouts</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The method no longer toggles <a class="reference internal" href="#socket.SOCK_NONBLOCK" title="socket.SOCK_NONBLOCK"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_NONBLOCK</span></code></a> flag on
<a class="reference internal" href="#socket.socket.type" title="socket.socket.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">socket.type</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.setsockopt">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">setsockopt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.setsockopt" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">setsockopt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">buffer</span></span></em><span class="sig-paren">)</span></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">setsockopt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optlen:</span> <span class="pre">int</span></span></em><span class="sig-paren">)</span></dt>
<dd><p id="index-14">Set the value of the given socket option (see the Unix manual page
<em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/setsockopt(2)">setsockopt(2)</a></em>).  The needed symbolic constants are defined in this
module (<span class="xref std std-ref">SO_* etc. &lt;socket-unix-constants&gt;</span>).  The value can be an integer,
<code class="docutils literal notranslate"><span class="pre">None</span></code> or a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> representing a buffer. In the later
case it is up to the caller to ensure that the bytestring contains the
proper bits (see the optional built-in module <a class="reference internal" href="struct.html#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> for a way to
encode C structures as bytestrings). When <em>value</em> is set to <code class="docutils literal notranslate"><span class="pre">None</span></code>,
<em>optlen</em> argument is required. It’s equivalent to call <code class="xref c c-func docutils literal notranslate"><span class="pre">setsockopt()</span></code> C
function with <code class="docutils literal notranslate"><span class="pre">optval=NULL</span></code> and <code class="docutils literal notranslate"><span class="pre">optlen=optlen</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>setsockopt(level, optname, None, optlen: int) form added.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.shutdown">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">shutdown</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">how</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.shutdown" title="Link to this definition">¶</a></dt>
<dd><p>Shut down one or both halves of the connection.  If <em>how</em> is <code class="xref py py-const docutils literal notranslate"><span class="pre">SHUT_RD</span></code>,
further receives are disallowed.  If <em>how</em> is <code class="xref py py-const docutils literal notranslate"><span class="pre">SHUT_WR</span></code>, further sends
are disallowed.  If <em>how</em> is <code class="xref py py-const docutils literal notranslate"><span class="pre">SHUT_RDWR</span></code>, further sends and receives are
disallowed.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not WASI.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="socket.socket.share">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">share</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">process_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#socket.socket.share" title="Link to this definition">¶</a></dt>
<dd><p>Duplicate a socket and prepare it for sharing with a target process.  The
target process must be provided with <em>process_id</em>.  The resulting bytes object
can then be passed to the target process using some form of interprocess
communication and the socket can be recreated there using <a class="reference internal" href="#socket.fromshare" title="socket.fromshare"><code class="xref py py-func docutils literal notranslate"><span class="pre">fromshare()</span></code></a>.
Once this method has been called, it is safe to close the socket since
the operating system has already duplicated it for the target process.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<p>Note that there are no methods <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code>; use
<a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> and <a class="reference internal" href="#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a> without <em>flags</em> argument instead.</p>
<p>Socket objects also have these (read-only) attributes that correspond to the
values given to the <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket</span></code></a> constructor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="socket.socket.family">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">family</span></span><a class="headerlink" href="#socket.socket.family" title="Link to this definition">¶</a></dt>
<dd><p>The socket family.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="socket.socket.type">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">type</span></span><a class="headerlink" href="#socket.socket.type" title="Link to this definition">¶</a></dt>
<dd><p>The socket type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="socket.socket.proto">
<span class="sig-prename descclassname"><span class="pre">socket.</span></span><span class="sig-name descname"><span class="pre">proto</span></span><a class="headerlink" href="#socket.socket.proto" title="Link to this definition">¶</a></dt>
<dd><p>The socket protocol.</p>
</dd></dl>

</section>
<section id="notes-on-socket-timeouts">
<span id="socket-timeouts"></span><h2>Notes on socket timeouts<a class="headerlink" href="#notes-on-socket-timeouts" title="Link to this heading">¶</a></h2>
<p>A socket object can be in one of three modes: blocking, non-blocking, or
timeout.  Sockets are by default always created in blocking mode, but this
can be changed by calling <a class="reference internal" href="#socket.setdefaulttimeout" title="socket.setdefaulttimeout"><code class="xref py py-func docutils literal notranslate"><span class="pre">setdefaulttimeout()</span></code></a>.</p>
<ul class="simple">
<li><p>In <em>blocking mode</em>, operations block until complete or the system returns
an error (such as connection timed out).</p></li>
<li><p>In <em>non-blocking mode</em>, operations fail (with an error that is unfortunately
system-dependent) if they cannot be completed immediately: functions from the
<a class="reference internal" href="select.html#module-select" title="select: Wait for I/O completion on multiple streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code></a> module can be used to know when and whether a socket is available
for reading or writing.</p></li>
<li><p>In <em>timeout mode</em>, operations fail if they cannot be completed within the
timeout specified for the socket (they raise a <a class="reference internal" href="#socket.timeout" title="socket.timeout"><code class="xref py py-exc docutils literal notranslate"><span class="pre">timeout</span></code></a> exception)
or if the system returns an error.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>At the operating system level, sockets in <em>timeout mode</em> are internally set
in non-blocking mode.  Also, the blocking and timeout modes are shared between
file descriptors and socket objects that refer to the same network endpoint.
This implementation detail can have visible consequences if e.g. you decide
to use the <a class="reference internal" href="#socket.socket.fileno" title="socket.socket.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a> of a socket.</p>
</div>
<section id="timeouts-and-the-connect-method">
<h3>Timeouts and the <code class="docutils literal notranslate"><span class="pre">connect</span></code> method<a class="headerlink" href="#timeouts-and-the-connect-method" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a> operation is also subject to the timeout
setting, and in general it is recommended to call <a class="reference internal" href="#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a>
before calling <a class="reference internal" href="#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a> or pass a timeout parameter to
<a class="reference internal" href="#socket.create_connection" title="socket.create_connection"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_connection()</span></code></a>.  However, the system network stack may also
return a connection timeout error of its own regardless of any Python socket
timeout setting.</p>
</section>
<section id="timeouts-and-the-accept-method">
<h3>Timeouts and the <code class="docutils literal notranslate"><span class="pre">accept</span></code> method<a class="headerlink" href="#timeouts-and-the-accept-method" title="Link to this heading">¶</a></h3>
<p>If <a class="reference internal" href="#socket.getdefaulttimeout" title="socket.getdefaulttimeout"><code class="xref py py-func docutils literal notranslate"><span class="pre">getdefaulttimeout()</span></code></a> is not <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, sockets returned by
the <a class="reference internal" href="#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a> method inherit that timeout.  Otherwise, the
behaviour depends on settings of the listening socket:</p>
<ul class="simple">
<li><p>if the listening socket is in <em>blocking mode</em> or in <em>timeout mode</em>,
the socket returned by <a class="reference internal" href="#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a> is in <em>blocking mode</em>;</p></li>
<li><p>if the listening socket is in <em>non-blocking mode</em>, whether the socket
returned by <a class="reference internal" href="#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a> is in blocking or non-blocking mode
is operating system-dependent.  If you want to ensure cross-platform
behaviour, it is recommended you manually override this setting.</p></li>
</ul>
</section>
</section>
<section id="example">
<span id="socket-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>Here are four minimal example programs using the TCP/IP protocol: a server that
echoes all data that it receives back (servicing only one client), and a client
using it.  Note that a server must perform the sequence <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a>,
<a class="reference internal" href="#socket.socket.bind" title="socket.socket.bind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">bind()</span></code></a>, <a class="reference internal" href="#socket.socket.listen" title="socket.socket.listen"><code class="xref py py-meth docutils literal notranslate"><span class="pre">listen()</span></code></a>, <a class="reference internal" href="#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a> (possibly
repeating the <a class="reference internal" href="#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a> to service more than one client), while a
client only needs the sequence <a class="reference internal" href="#socket.socket" title="socket.socket"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket()</span></code></a>, <a class="reference internal" href="#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a>.  Also
note that the server does not <a class="reference internal" href="#socket.socket.sendall" title="socket.socket.sendall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendall()</span></code></a>/<a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> on
the socket it is listening on but on the new socket returned by
<a class="reference internal" href="#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a>.</p>
<p>The first two examples support IPv4 only.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Echo server program</span>
<span class="kn">import</span> <span class="nn">socket</span>

<span class="n">HOST</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span>                 <span class="c1"># Symbolic name meaning all available interfaces</span>
<span class="n">PORT</span> <span class="o">=</span> <span class="mi">50007</span>              <span class="c1"># Arbitrary non-privileged port</span>
<span class="k">with</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">)</span> <span class="k">as</span> <span class="n">s</span><span class="p">:</span>
    <span class="n">s</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="n">HOST</span><span class="p">,</span> <span class="n">PORT</span><span class="p">))</span>
    <span class="n">s</span><span class="o">.</span><span class="n">listen</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">conn</span><span class="p">,</span> <span class="n">addr</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">accept</span><span class="p">()</span>
    <span class="k">with</span> <span class="n">conn</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Connected by&#39;</span><span class="p">,</span> <span class="n">addr</span><span class="p">)</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="n">data</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">data</span><span class="p">:</span> <span class="k">break</span>
            <span class="n">conn</span><span class="o">.</span><span class="n">sendall</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Echo client program</span>
<span class="kn">import</span> <span class="nn">socket</span>

<span class="n">HOST</span> <span class="o">=</span> <span class="s1">&#39;daring.cwi.nl&#39;</span>    <span class="c1"># The remote host</span>
<span class="n">PORT</span> <span class="o">=</span> <span class="mi">50007</span>              <span class="c1"># The same port as used by the server</span>
<span class="k">with</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">)</span> <span class="k">as</span> <span class="n">s</span><span class="p">:</span>
    <span class="n">s</span><span class="o">.</span><span class="n">connect</span><span class="p">((</span><span class="n">HOST</span><span class="p">,</span> <span class="n">PORT</span><span class="p">))</span>
    <span class="n">s</span><span class="o">.</span><span class="n">sendall</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Hello, world&#39;</span><span class="p">)</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Received&#39;</span><span class="p">,</span> <span class="nb">repr</span><span class="p">(</span><span class="n">data</span><span class="p">))</span>
</pre></div>
</div>
<p>The next two examples are identical to the above two, but support both IPv4 and
IPv6. The server side will listen to the first address family available (it
should listen to both instead). On most of IPv6-ready systems, IPv6 will take
precedence and the server may not accept IPv4 traffic. The client side will try
to connect to all the addresses returned as a result of the name resolution, and
sends traffic to the first one connected successfully.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Echo server program</span>
<span class="kn">import</span> <span class="nn">socket</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="n">HOST</span> <span class="o">=</span> <span class="kc">None</span>               <span class="c1"># Symbolic name meaning all available interfaces</span>
<span class="n">PORT</span> <span class="o">=</span> <span class="mi">50007</span>              <span class="c1"># Arbitrary non-privileged port</span>
<span class="n">s</span> <span class="o">=</span> <span class="kc">None</span>
<span class="k">for</span> <span class="n">res</span> <span class="ow">in</span> <span class="n">socket</span><span class="o">.</span><span class="n">getaddrinfo</span><span class="p">(</span><span class="n">HOST</span><span class="p">,</span> <span class="n">PORT</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">AF_UNSPEC</span><span class="p">,</span>
                              <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">AI_PASSIVE</span><span class="p">):</span>
    <span class="n">af</span><span class="p">,</span> <span class="n">socktype</span><span class="p">,</span> <span class="n">proto</span><span class="p">,</span> <span class="n">canonname</span><span class="p">,</span> <span class="n">sa</span> <span class="o">=</span> <span class="n">res</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">af</span><span class="p">,</span> <span class="n">socktype</span><span class="p">,</span> <span class="n">proto</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">msg</span><span class="p">:</span>
        <span class="n">s</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">continue</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">s</span><span class="o">.</span><span class="n">bind</span><span class="p">(</span><span class="n">sa</span><span class="p">)</span>
        <span class="n">s</span><span class="o">.</span><span class="n">listen</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">msg</span><span class="p">:</span>
        <span class="n">s</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
        <span class="n">s</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">continue</span>
    <span class="k">break</span>
<span class="k">if</span> <span class="n">s</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;could not open socket&#39;</span><span class="p">)</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">conn</span><span class="p">,</span> <span class="n">addr</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">accept</span><span class="p">()</span>
<span class="k">with</span> <span class="n">conn</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Connected by&#39;</span><span class="p">,</span> <span class="n">addr</span><span class="p">)</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">data</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">data</span><span class="p">:</span> <span class="k">break</span>
        <span class="n">conn</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Echo client program</span>
<span class="kn">import</span> <span class="nn">socket</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="n">HOST</span> <span class="o">=</span> <span class="s1">&#39;daring.cwi.nl&#39;</span>    <span class="c1"># The remote host</span>
<span class="n">PORT</span> <span class="o">=</span> <span class="mi">50007</span>              <span class="c1"># The same port as used by the server</span>
<span class="n">s</span> <span class="o">=</span> <span class="kc">None</span>
<span class="k">for</span> <span class="n">res</span> <span class="ow">in</span> <span class="n">socket</span><span class="o">.</span><span class="n">getaddrinfo</span><span class="p">(</span><span class="n">HOST</span><span class="p">,</span> <span class="n">PORT</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">AF_UNSPEC</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">):</span>
    <span class="n">af</span><span class="p">,</span> <span class="n">socktype</span><span class="p">,</span> <span class="n">proto</span><span class="p">,</span> <span class="n">canonname</span><span class="p">,</span> <span class="n">sa</span> <span class="o">=</span> <span class="n">res</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">af</span><span class="p">,</span> <span class="n">socktype</span><span class="p">,</span> <span class="n">proto</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">msg</span><span class="p">:</span>
        <span class="n">s</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">continue</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">s</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="n">sa</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">msg</span><span class="p">:</span>
        <span class="n">s</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
        <span class="n">s</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">continue</span>
    <span class="k">break</span>
<span class="k">if</span> <span class="n">s</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;could not open socket&#39;</span><span class="p">)</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="k">with</span> <span class="n">s</span><span class="p">:</span>
    <span class="n">s</span><span class="o">.</span><span class="n">sendall</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Hello, world&#39;</span><span class="p">)</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Received&#39;</span><span class="p">,</span> <span class="nb">repr</span><span class="p">(</span><span class="n">data</span><span class="p">))</span>
</pre></div>
</div>
<p>The next example shows how to write a very simple network sniffer with raw
sockets on Windows. The example requires administrator privileges to modify
the interface:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span>

<span class="c1"># the public network interface</span>
<span class="n">HOST</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">gethostbyname</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">gethostname</span><span class="p">())</span>

<span class="c1"># create a raw socket and bind it to the public interface</span>
<span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_RAW</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">IPPROTO_IP</span><span class="p">)</span>
<span class="n">s</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="n">HOST</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>

<span class="c1"># Include IP headers</span>
<span class="n">s</span><span class="o">.</span><span class="n">setsockopt</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">IPPROTO_IP</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">IP_HDRINCL</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>

<span class="c1"># receive all packets</span>
<span class="n">s</span><span class="o">.</span><span class="n">ioctl</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">SIO_RCVALL</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">RCVALL_ON</span><span class="p">)</span>

<span class="c1"># receive a packet</span>
<span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">recvfrom</span><span class="p">(</span><span class="mi">65565</span><span class="p">))</span>

<span class="c1"># disabled promiscuous mode</span>
<span class="n">s</span><span class="o">.</span><span class="n">ioctl</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">SIO_RCVALL</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">RCVALL_OFF</span><span class="p">)</span>
</pre></div>
</div>
<p>The next example shows how to use the socket interface to communicate to a CAN
network using the raw socket protocol. To use CAN with the broadcast
manager protocol instead, open a socket with:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_CAN</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_DGRAM</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">CAN_BCM</span><span class="p">)</span>
</pre></div>
</div>
<p>After binding (<code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_RAW</span></code>) or connecting (<a class="reference internal" href="#socket.CAN_BCM" title="socket.CAN_BCM"><code class="xref py py-const docutils literal notranslate"><span class="pre">CAN_BCM</span></code></a>) the socket, you
can use the <a class="reference internal" href="#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.send()</span></code></a> and <a class="reference internal" href="#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.recv()</span></code></a> operations (and
their counterparts) on the socket object as usual.</p>
<p>This last example might require special privileges:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span>
<span class="kn">import</span> <span class="nn">struct</span>


<span class="c1"># CAN frame packing/unpacking (see &#39;struct can_frame&#39; in &lt;linux/can.h&gt;)</span>

<span class="n">can_frame_fmt</span> <span class="o">=</span> <span class="s2">&quot;=IB3x8s&quot;</span>
<span class="n">can_frame_size</span> <span class="o">=</span> <span class="n">struct</span><span class="o">.</span><span class="n">calcsize</span><span class="p">(</span><span class="n">can_frame_fmt</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">build_can_frame</span><span class="p">(</span><span class="n">can_id</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
    <span class="n">can_dlc</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">ljust</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x00</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">struct</span><span class="o">.</span><span class="n">pack</span><span class="p">(</span><span class="n">can_frame_fmt</span><span class="p">,</span> <span class="n">can_id</span><span class="p">,</span> <span class="n">can_dlc</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">dissect_can_frame</span><span class="p">(</span><span class="n">frame</span><span class="p">):</span>
    <span class="n">can_id</span><span class="p">,</span> <span class="n">can_dlc</span><span class="p">,</span> <span class="n">data</span> <span class="o">=</span> <span class="n">struct</span><span class="o">.</span><span class="n">unpack</span><span class="p">(</span><span class="n">can_frame_fmt</span><span class="p">,</span> <span class="n">frame</span><span class="p">)</span>
    <span class="k">return</span> <span class="p">(</span><span class="n">can_id</span><span class="p">,</span> <span class="n">can_dlc</span><span class="p">,</span> <span class="n">data</span><span class="p">[:</span><span class="n">can_dlc</span><span class="p">])</span>


<span class="c1"># create a raw socket and bind it to the &#39;vcan0&#39; interface</span>
<span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_CAN</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_RAW</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">CAN_RAW</span><span class="p">)</span>
<span class="n">s</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="s1">&#39;vcan0&#39;</span><span class="p">,))</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">cf</span><span class="p">,</span> <span class="n">addr</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">recvfrom</span><span class="p">(</span><span class="n">can_frame_size</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Received: can_id=</span><span class="si">%x</span><span class="s1">, can_dlc=</span><span class="si">%x</span><span class="s1">, data=</span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">dissect_can_frame</span><span class="p">(</span><span class="n">cf</span><span class="p">))</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="n">s</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cf</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">OSError</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Error sending CAN frame&#39;</span><span class="p">)</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="n">s</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">build_can_frame</span><span class="p">(</span><span class="mh">0x01</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x01\x02\x03</span><span class="s1">&#39;</span><span class="p">))</span>
    <span class="k">except</span> <span class="ne">OSError</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Error sending CAN frame&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Running an example several times with too small delay between executions, could
lead to this error:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="ne">OSError</span><span class="p">:</span> <span class="p">[</span><span class="n">Errno</span> <span class="mi">98</span><span class="p">]</span> <span class="n">Address</span> <span class="n">already</span> <span class="ow">in</span> <span class="n">use</span>
</pre></div>
</div>
<p>This is because the previous execution has left the socket in a <code class="docutils literal notranslate"><span class="pre">TIME_WAIT</span></code>
state, and can’t be immediately reused.</p>
<p>There is a <a class="reference internal" href="#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> flag to set, in order to prevent this,
<code class="xref py py-const docutils literal notranslate"><span class="pre">socket.SO_REUSEADDR</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">)</span>
<span class="n">s</span><span class="o">.</span><span class="n">setsockopt</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">SOL_SOCKET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SO_REUSEADDR</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">s</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="n">HOST</span><span class="p">,</span> <span class="n">PORT</span><span class="p">))</span>
</pre></div>
</div>
<p>the <code class="xref py py-data docutils literal notranslate"><span class="pre">SO_REUSEADDR</span></code> flag tells the kernel to reuse a local socket in
<code class="docutils literal notranslate"><span class="pre">TIME_WAIT</span></code> state, without waiting for its natural timeout to expire.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>For an introduction to socket programming (in C), see the following papers:</p>
<ul class="simple">
<li><p><em>An Introductory 4.3BSD Interprocess Communication Tutorial</em>, by Stuart Sechrest</p></li>
<li><p><em>An Advanced 4.3BSD Interprocess Communication Tutorial</em>, by Samuel J.  Leffler et
al,</p></li>
</ul>
<p>both in the UNIX Programmer’s Manual, Supplementary Documents 1 (sections
PS1:7 and PS1:8).  The platform-specific reference material for the various
socket-related system calls are also a valuable source of information on the
details of socket semantics.  For Unix, refer to the manual pages; for Windows,
see the WinSock (or Winsock 2) specification.  For IPv6-ready APIs, readers may
want to refer to <span class="target" id="index-15"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3493.html"><strong>RFC 3493</strong></a> titled Basic Socket Interface Extensions for IPv6.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code> — Low-level networking interface</a><ul>
<li><a class="reference internal" href="#socket-families">Socket families</a></li>
<li><a class="reference internal" href="#module-contents">Module contents</a><ul>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
<li><a class="reference internal" href="#functions">Functions</a><ul>
<li><a class="reference internal" href="#creating-sockets">Creating sockets</a></li>
<li><a class="reference internal" href="#other-functions">Other functions</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#socket-objects">Socket Objects</a></li>
<li><a class="reference internal" href="#notes-on-socket-timeouts">Notes on socket timeouts</a><ul>
<li><a class="reference internal" href="#timeouts-and-the-connect-method">Timeouts and the <code class="docutils literal notranslate"><span class="pre">connect</span></code> method</a></li>
<li><a class="reference internal" href="#timeouts-and-the-accept-method">Timeouts and the <code class="docutils literal notranslate"><span class="pre">accept</span></code> method</a></li>
</ul>
</li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="asyncio-dev.html"
                          title="previous chapter">Developing with asyncio</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ssl.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code> — TLS/SSL wrapper for socket objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/socket.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ssl.html" title="ssl — TLS/SSL wrapper for socket objects"
             >next</a> |</li>
        <li class="right" >
          <a href="asyncio-dev.html" title="Developing with asyncio"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" >Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code> — Low-level networking interface</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>