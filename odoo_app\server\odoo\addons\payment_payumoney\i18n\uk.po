# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# <PERSON>, 2023
# <PERSON>il <PERSON>, 2023
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__code
msgid "Code"
msgstr "Код"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__payumoney_merchant_key
msgid "Merchant Key"
msgstr "Ключ Merchant"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr "Секретний ключ Merchant"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Не знайдено жодної транзакції, що відповідає референсу %s."

#. module: payment_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_provider__code__payumoney
#: model:payment.provider,name:payment_payumoney.payment_provider_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_provider
msgid "Payment Provider"
msgstr "Провайдер платежу"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%s)"
msgstr "Отримані дані з відсутнім референсом (%s)"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_provider__payumoney_merchant_key
msgid "The key solely used to identify the account with PayU money"
msgstr ""
"Ключ, який використовується виключно для ідентифікації рахунку з грошима "
"PayU"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "The payment encountered an error with code %s"
msgstr "Під час платежу сталася помилка з кодом %s"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Технічний код цього провайдера платежу."

#. module: payment_payumoney
#: model_terms:ir.ui.view,arch_db:payment_payumoney.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Razorpay</strong>."
msgstr ""
"Цей провайдер не обслуговується.\n"
"                    Ви можете вимкнути його та перейти на <strong>Razorpay</strong>."

#. module: payment_payumoney
#: model_terms:payment.provider,auth_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been authorized."
msgstr "Вашу оплату було авторизовано."

#. module: payment_payumoney
#: model_terms:payment.provider,cancel_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been cancelled."
msgstr "Ваш платіж скасовано."

#. module: payment_payumoney
#: model_terms:payment.provider,pending_msg:payment_payumoney.payment_provider_payumoney
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Ваш платіж успішно оброблено, але очікує на затвердження."

#. module: payment_payumoney
#: model_terms:payment.provider,done_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been successfully processed."
msgstr "Ваш платіж успішно оброблено."
