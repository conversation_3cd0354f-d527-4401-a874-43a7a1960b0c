<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pty — Pseudo-terminal utilities" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pty.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pty.py The pty module defines operations for handling the pseudo-terminal concept: starting another process and being able to write to and read from its controlling terminal progra..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pty.py The pty module defines operations for handling the pseudo-terminal concept: starting another process and being able to write to and read from its controlling terminal progra..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pty — Pseudo-terminal utilities &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="fcntl — The fcntl and ioctl system calls" href="fcntl.html" />
    <link rel="prev" title="tty — Terminal control functions" href="tty.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pty.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code> — Pseudo-terminal utilities</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tty.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="fcntl.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fcntl</span></code> — The <code class="docutils literal notranslate"><span class="pre">fcntl</span></code> and <code class="docutils literal notranslate"><span class="pre">ioctl</span></code> system calls</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pty.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fcntl.html" title="fcntl — The fcntl and ioctl system calls"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tty.html" title="tty — Terminal control functions"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" accesskey="U">Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code> — Pseudo-terminal utilities</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pty">
<span id="pty-pseudo-terminal-utilities"></span><h1><a class="reference internal" href="#module-pty" title="pty: Pseudo-Terminal Handling for Unix. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code></a> — Pseudo-terminal utilities<a class="headerlink" href="#module-pty" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pty.py">Lib/pty.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-pty" title="pty: Pseudo-Terminal Handling for Unix. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code></a> module defines operations for handling the pseudo-terminal
concept: starting another process and being able to write to and read from its
controlling terminal programmatically.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<p>Pseudo-terminal handling is highly platform dependent. This code is mainly
tested on Linux, FreeBSD, and macOS (it is supposed to work on other POSIX
platforms but it’s not been thoroughly tested).</p>
<p>The <a class="reference internal" href="#module-pty" title="pty: Pseudo-Terminal Handling for Unix. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="pty.fork">
<span class="sig-prename descclassname"><span class="pre">pty.</span></span><span class="sig-name descname"><span class="pre">fork</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pty.fork" title="Link to this definition">¶</a></dt>
<dd><p>Fork. Connect the child’s controlling terminal to a pseudo-terminal. Return
value is <code class="docutils literal notranslate"><span class="pre">(pid,</span> <span class="pre">fd)</span></code>. Note that the child  gets <em>pid</em> 0, and the <em>fd</em> is
<em>invalid</em>. The parent’s return value is the <em>pid</em> of the child, and <em>fd</em> is a
file descriptor connected to the child’s controlling terminal (and also to the
child’s standard input and output).</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>On macOS the use of this function is unsafe when mixed with using
higher-level system APIs, and that includes using <a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pty.openpty">
<span class="sig-prename descclassname"><span class="pre">pty.</span></span><span class="sig-name descname"><span class="pre">openpty</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pty.openpty" title="Link to this definition">¶</a></dt>
<dd><p>Open a new pseudo-terminal pair, using <a class="reference internal" href="os.html#os.openpty" title="os.openpty"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.openpty()</span></code></a> if possible, or
emulation code for generic Unix systems. Return a pair of file descriptors
<code class="docutils literal notranslate"><span class="pre">(master,</span> <span class="pre">slave)</span></code>, for the master and the slave end, respectively.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pty.spawn">
<span class="sig-prename descclassname"><span class="pre">pty.</span></span><span class="sig-name descname"><span class="pre">spawn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">argv</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">master_read</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">stdin_read</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#pty.spawn" title="Link to this definition">¶</a></dt>
<dd><p>Spawn a process, and connect its controlling terminal with the current
process’s standard io. This is often used to baffle programs which insist on
reading from the controlling terminal. It is expected that the process
spawned behind the pty will eventually terminate, and when it does <em>spawn</em>
will return.</p>
<p>A loop copies STDIN of the current process to the child and data received
from the child to STDOUT of the current process. It is not signaled to the
child if STDIN of the current process closes down.</p>
<p>The functions <em>master_read</em> and <em>stdin_read</em> are passed a file descriptor
which they should read from, and they should always return a byte string. In
order to force spawn to return before the child process exits an
empty byte array should be returned to signal end of file.</p>
<p>The default implementation for both functions will read and return up to 1024
bytes each time the function is called. The <em>master_read</em> callback is passed
the pseudoterminal’s master file descriptor to read output from the child
process, and <em>stdin_read</em> is passed file descriptor 0, to read from the
parent process’s standard input.</p>
<p>Returning an empty byte string from either callback is interpreted as an
end-of-file (EOF) condition, and that callback will not be called after
that. If <em>stdin_read</em> signals EOF the controlling terminal can no longer
communicate with the parent process OR the child process. Unless the child
process will quit without any input, <em>spawn</em> will then loop forever. If
<em>master_read</em> signals EOF the same behavior results (on linux at least).</p>
<p>Return the exit status value from <a class="reference internal" href="os.html#os.waitpid" title="os.waitpid"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.waitpid()</span></code></a> on the child process.</p>
<p><a class="reference internal" href="os.html#os.waitstatus_to_exitcode" title="os.waitstatus_to_exitcode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.waitstatus_to_exitcode()</span></code></a> can be used to convert the exit status into
an exit code.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">pty.spawn</span></code> with argument <code class="docutils literal notranslate"><span class="pre">argv</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><a class="reference internal" href="#pty.spawn" title="pty.spawn"><code class="xref py py-func docutils literal notranslate"><span class="pre">spawn()</span></code></a> now returns the status value from <a class="reference internal" href="os.html#os.waitpid" title="os.waitpid"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.waitpid()</span></code></a>
on the child process.</p>
</div>
</dd></dl>

<section id="example">
<h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>The following program acts like the Unix command <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/script(1)">script(1)</a></em>, using a
pseudo-terminal to record all input and output of a terminal session in a
“typescript”.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">argparse</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">pty</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">import</span> <span class="nn">time</span>

<span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">()</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;-a&#39;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;append&#39;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;-p&#39;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;use_python&#39;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;filename&#39;</span><span class="p">,</span> <span class="n">nargs</span><span class="o">=</span><span class="s1">&#39;?&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s1">&#39;typescript&#39;</span><span class="p">)</span>
<span class="n">options</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>

<span class="n">shell</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">executable</span> <span class="k">if</span> <span class="n">options</span><span class="o">.</span><span class="n">use_python</span> <span class="k">else</span> <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;SHELL&#39;</span><span class="p">,</span> <span class="s1">&#39;sh&#39;</span><span class="p">)</span>
<span class="n">filename</span> <span class="o">=</span> <span class="n">options</span><span class="o">.</span><span class="n">filename</span>
<span class="n">mode</span> <span class="o">=</span> <span class="s1">&#39;ab&#39;</span> <span class="k">if</span> <span class="n">options</span><span class="o">.</span><span class="n">append</span> <span class="k">else</span> <span class="s1">&#39;wb&#39;</span>

<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="n">mode</span><span class="p">)</span> <span class="k">as</span> <span class="n">script</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">read</span><span class="p">(</span><span class="n">fd</span><span class="p">):</span>
        <span class="n">data</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="n">fd</span><span class="p">,</span> <span class="mi">1024</span><span class="p">)</span>
        <span class="n">script</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">data</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Script started, file is&#39;</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
    <span class="n">script</span><span class="o">.</span><span class="n">write</span><span class="p">((</span><span class="s1">&#39;Script started on </span><span class="si">%s</span><span class="se">\n</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">time</span><span class="o">.</span><span class="n">asctime</span><span class="p">())</span><span class="o">.</span><span class="n">encode</span><span class="p">())</span>

    <span class="n">pty</span><span class="o">.</span><span class="n">spawn</span><span class="p">(</span><span class="n">shell</span><span class="p">,</span> <span class="n">read</span><span class="p">)</span>

    <span class="n">script</span><span class="o">.</span><span class="n">write</span><span class="p">((</span><span class="s1">&#39;Script done on </span><span class="si">%s</span><span class="se">\n</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">time</span><span class="o">.</span><span class="n">asctime</span><span class="p">())</span><span class="o">.</span><span class="n">encode</span><span class="p">())</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Script done, file is&#39;</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code> — Pseudo-terminal utilities</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tty.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="fcntl.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fcntl</span></code> — The <code class="docutils literal notranslate"><span class="pre">fcntl</span></code> and <code class="docutils literal notranslate"><span class="pre">ioctl</span></code> system calls</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pty.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fcntl.html" title="fcntl — The fcntl and ioctl system calls"
             >next</a> |</li>
        <li class="right" >
          <a href="tty.html" title="tty — Terminal control functions"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" >Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code> — Pseudo-terminal utilities</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>