.o_mg_subscribe_btn {
    width: 120px;
}

.o_mg_page_description {
    background-image: url(/mail_group/static/src/img/mail_group_portal.jpg);
}

.o_mg_ribbon {
    right: 0;
    width: 120px;
    height: 120px;
    overflow: hidden;
    position: absolute;

    span {
        left: -20px;
        top: 20px;
        transform: rotate(45deg);
        z-index: 1;
        position: absolute;
        width: 220px;
        height: 28px;
        line-height: 28px;
        padding: 0 60px;
        text-align: center;
        overflow: hidden;
        user-select: none;
    }
}

.o_mg_message *[data-o-mail-quote]:not(.visible)  {
    // Hide the quotation of the parent email in the body
    // The "visible" class with be toggled in JS
    display: none!important;
}

.o_mg_attachment {
    width: 120px;
}

.o_mg_message {
    min-width: 250px;
}
