<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xmlrpc.client — XML-RPC client access" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xmlrpc.client.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xmlrpc/client.py XML-RPC is a Remote Procedure Call method that uses XML passed via HTTP(S) as a transport. With it, a client can call methods with parameters on a remote server (t..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xmlrpc/client.py XML-RPC is a Remote Procedure Call method that uses XML passed via HTTP(S) as a transport. With it, a client can call methods with parameters on a remote server (t..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xmlrpc.client — XML-RPC client access &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xmlrpc.server — Basic XML-RPC servers" href="xmlrpc.server.html" />
    <link rel="prev" title="xmlrpc — XMLRPC server and client modules" href="xmlrpc.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xmlrpc.client.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a><ul>
<li><a class="reference internal" href="#serverproxy-objects">ServerProxy Objects</a></li>
<li><a class="reference internal" href="#datetime-objects">DateTime Objects</a></li>
<li><a class="reference internal" href="#binary-objects">Binary Objects</a></li>
<li><a class="reference internal" href="#fault-objects">Fault Objects</a></li>
<li><a class="reference internal" href="#protocolerror-objects">ProtocolError Objects</a></li>
<li><a class="reference internal" href="#multicall-objects">MultiCall Objects</a></li>
<li><a class="reference internal" href="#convenience-functions">Convenience Functions</a></li>
<li><a class="reference internal" href="#example-of-client-usage">Example of Client Usage</a></li>
<li><a class="reference internal" href="#example-of-client-and-server-usage">Example of Client and Server Usage</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xmlrpc.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc</span></code> — XMLRPC server and client modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xmlrpc.server.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xmlrpc.client.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xmlrpc.server.html" title="xmlrpc.server — Basic XML-RPC servers"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xmlrpc.html" title="xmlrpc — XMLRPC server and client modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xmlrpc.client">
<span id="xmlrpc-client-xml-rpc-client-access"></span><h1><a class="reference internal" href="#module-xmlrpc.client" title="xmlrpc.client: XML-RPC client access."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code></a> — XML-RPC client access<a class="headerlink" href="#module-xmlrpc.client" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xmlrpc/client.py">Lib/xmlrpc/client.py</a></p>
<hr class="docutils" />
<p>XML-RPC is a Remote Procedure Call method that uses XML passed via HTTP(S) as a
transport.  With it, a client can call methods with parameters on a remote
server (the server is named by a URI) and get back structured data.  This module
supports writing XML-RPC client code; it handles all the details of translating
between conformable Python objects and XML on the wire.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The <a class="reference internal" href="#module-xmlrpc.client" title="xmlrpc.client: XML-RPC client access."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code></a> module is not secure against maliciously
constructed data.  If you need to parse untrusted or unauthenticated data see
<a class="reference internal" href="xml.html#xml-vulnerabilities"><span class="std std-ref">XML vulnerabilities</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>For HTTPS URIs, <a class="reference internal" href="#module-xmlrpc.client" title="xmlrpc.client: XML-RPC client access."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code></a> now performs all the necessary
certificate and hostname checks by default.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.client.ServerProxy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">ServerProxy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">uri</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">transport</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbose</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_none</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_datetime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_builtin_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.ServerProxy" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#xmlrpc.client.ServerProxy" title="xmlrpc.client.ServerProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">ServerProxy</span></code></a> instance is an object that manages communication with a
remote XML-RPC server.  The required first argument is a URI (Uniform Resource
Indicator), and will normally be the URL of the server.  The optional second
argument is a transport factory instance; by default it is an internal
<code class="xref py py-class docutils literal notranslate"><span class="pre">SafeTransport</span></code> instance for https: URLs and an internal HTTP
<code class="xref py py-class docutils literal notranslate"><span class="pre">Transport</span></code> instance otherwise.  The optional third argument is an
encoding, by default UTF-8. The optional fourth argument is a debugging flag.</p>
<p>The following parameters govern the use of the returned proxy instance.
If <em>allow_none</em> is true,  the Python constant <code class="docutils literal notranslate"><span class="pre">None</span></code> will be translated into
XML; the default behaviour is for <code class="docutils literal notranslate"><span class="pre">None</span></code> to raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>. This is
a commonly used extension to the XML-RPC specification, but isn’t supported by
all clients and servers; see <a class="reference external" href="https://web.archive.org/web/20130120074804/http://ontosys.com/xml-rpc/extensions.php">http://ontosys.com/xml-rpc/extensions.php</a>
for a description.
The <em>use_builtin_types</em> flag can be used to cause date/time values
to be presented as <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a> objects and binary data to be
presented as <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects; this flag is false by default.
<a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a>, <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> objects
may be passed to calls.
The <em>headers</em> parameter is an optional sequence of HTTP headers to send with
each request, expressed as a sequence of 2-tuples representing the header
name and value. (e.g. <code class="docutils literal notranslate"><span class="pre">[('Header-Name',</span> <span class="pre">'value')]</span></code>).
The obsolete <em>use_datetime</em> flag is similar to <em>use_builtin_types</em> but it
applies only to date/time values.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>use_builtin_types</em> flag was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>headers</em> parameter was added.</p>
<p>Both the HTTP and HTTPS transports support the URL syntax extension for HTTP
Basic Authentication: <code class="docutils literal notranslate"><span class="pre">http://user:pass&#64;host:port/path</span></code>.  The  <code class="docutils literal notranslate"><span class="pre">user:pass</span></code>
portion will be base64-encoded as an HTTP ‘Authorization’ header, and sent to
the remote server as part of the connection process when invoking an XML-RPC
method.  You only need to use this if the remote server requires a Basic
Authentication user and password. If an HTTPS URL is provided, <em>context</em> may
be <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> and configures the SSL settings of the underlying
HTTPS connection.</p>
<p>The returned instance is a proxy object with methods that can be used to invoke
corresponding RPC calls on the remote server.  If the remote server supports the
introspection API, the proxy can also be used to query the remote server for the
methods it supports (service discovery) and fetch other server-associated
metadata.</p>
<p>Types that are conformable (e.g. that can be marshalled through XML),
include the following (and except where noted, they are unmarshalled
as the same Python type):</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>XML-RPC type</p></th>
<th class="head"><p>Python type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">boolean</span></code></p></td>
<td><p><a class="reference internal" href="functions.html#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">int</span></code>, <code class="docutils literal notranslate"><span class="pre">i1</span></code>,
<code class="docutils literal notranslate"><span class="pre">i2</span></code>,  <code class="docutils literal notranslate"><span class="pre">i4</span></code>,
<code class="docutils literal notranslate"><span class="pre">i8</span></code> or
<code class="docutils literal notranslate"><span class="pre">biginteger</span></code></p></td>
<td><p><a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> in range from -2147483648 to 2147483647.
Values get the <code class="docutils literal notranslate"><span class="pre">&lt;int&gt;</span></code> tag.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">double</span></code> or
<code class="docutils literal notranslate"><span class="pre">float</span></code></p></td>
<td><p><a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>.  Values get the <code class="docutils literal notranslate"><span class="pre">&lt;double&gt;</span></code> tag.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">string</span></code></p></td>
<td><p><a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">array</span></code></p></td>
<td><p><a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> or <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> containing
conformable elements.  Arrays are returned as
<a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">lists</span></code></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">struct</span></code></p></td>
<td><p><a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.  Keys must be strings, values may be
any conformable type.  Objects of user-defined
classes can be passed in; only their
<a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute is transmitted.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">dateTime.iso8601</span></code></p></td>
<td><p><a class="reference internal" href="#xmlrpc.client.DateTime" title="xmlrpc.client.DateTime"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateTime</span></code></a> or <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a>.
Returned type depends on values of
<em>use_builtin_types</em> and <em>use_datetime</em> flags.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">base64</span></code></p></td>
<td><p><a class="reference internal" href="#xmlrpc.client.Binary" title="xmlrpc.client.Binary"><code class="xref py py-class docutils literal notranslate"><span class="pre">Binary</span></code></a>, <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or
<a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>.  Returned type depends on the
value of the <em>use_builtin_types</em> flag.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">nil</span></code></p></td>
<td><p>The <code class="docutils literal notranslate"><span class="pre">None</span></code> constant.  Passing is allowed only if
<em>allow_none</em> is true.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigdecimal</span></code></p></td>
<td><p><a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a>.  Returned type only.</p></td>
</tr>
</tbody>
</table>
<p>This is the full set of data types supported by XML-RPC.  Method calls may also
raise a special <a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Fault</span></code></a> instance, used to signal XML-RPC server errors, or
<a class="reference internal" href="#xmlrpc.client.ProtocolError" title="xmlrpc.client.ProtocolError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProtocolError</span></code></a> used to signal an error in the HTTP/HTTPS transport layer.
Both <a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Fault</span></code></a> and <a class="reference internal" href="#xmlrpc.client.ProtocolError" title="xmlrpc.client.ProtocolError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProtocolError</span></code></a> derive from a base class called
<code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code>.  Note that the xmlrpc client module currently does not marshal
instances of subclasses of built-in types.</p>
<p>When passing strings, characters special to XML such as <code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&amp;</span></code>
will be automatically escaped.  However, it’s the caller’s responsibility to
ensure that the string is free of characters that aren’t allowed in XML, such as
the control characters with ASCII values between 0 and 31 (except, of course,
tab, newline and carriage return); failing to do this will result in an XML-RPC
request that isn’t well-formed XML.  If you have to pass arbitrary bytes
via XML-RPC, use <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> classes or the
<a class="reference internal" href="#xmlrpc.client.Binary" title="xmlrpc.client.Binary"><code class="xref py py-class docutils literal notranslate"><span class="pre">Binary</span></code></a> wrapper class described below.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Server</span></code> is retained as an alias for <a class="reference internal" href="#xmlrpc.client.ServerProxy" title="xmlrpc.client.ServerProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">ServerProxy</span></code></a> for backwards
compatibility.  New code should use <a class="reference internal" href="#xmlrpc.client.ServerProxy" title="xmlrpc.client.ServerProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">ServerProxy</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>context</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added support of type tags with prefixes (e.g. <code class="docutils literal notranslate"><span class="pre">ex:nil</span></code>).
Added support of unmarshalling additional types used by Apache XML-RPC
implementation for numerics: <code class="docutils literal notranslate"><span class="pre">i1</span></code>, <code class="docutils literal notranslate"><span class="pre">i2</span></code>, <code class="docutils literal notranslate"><span class="pre">i8</span></code>, <code class="docutils literal notranslate"><span class="pre">biginteger</span></code>,
<code class="docutils literal notranslate"><span class="pre">float</span></code> and <code class="docutils literal notranslate"><span class="pre">bigdecimal</span></code>.
See <a class="reference external" href="https://ws.apache.org/xmlrpc/types.html">https://ws.apache.org/xmlrpc/types.html</a> for a description.</p>
</div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://tldp.org/HOWTO/XML-RPC-HOWTO/index.html">XML-RPC HOWTO</a></dt><dd><p>A good description of XML-RPC operation and client software in several languages.
Contains pretty much everything an XML-RPC client developer needs to know.</p>
</dd>
<dt><a class="reference external" href="https://xmlrpc-c.sourceforge.net/introspection.html">XML-RPC Introspection</a></dt><dd><p>Describes the XML-RPC protocol extension for introspection.</p>
</dd>
<dt><a class="reference external" href="http://xmlrpc.scripting.com/spec.html">XML-RPC Specification</a></dt><dd><p>The official specification.</p>
</dd>
</dl>
</div>
<section id="serverproxy-objects">
<span id="id1"></span><h2>ServerProxy Objects<a class="headerlink" href="#serverproxy-objects" title="Link to this heading">¶</a></h2>
<p>A <a class="reference internal" href="#xmlrpc.client.ServerProxy" title="xmlrpc.client.ServerProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">ServerProxy</span></code></a> instance has a method corresponding to each remote
procedure call accepted by the XML-RPC server.  Calling the method performs an
RPC, dispatched by both name and argument signature (e.g. the same method name
can be overloaded with multiple argument signatures).  The RPC finishes by
returning a value, which may be either returned data in a conformant type or a
<a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fault</span></code></a> or <a class="reference internal" href="#xmlrpc.client.ProtocolError" title="xmlrpc.client.ProtocolError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProtocolError</span></code></a> object indicating an error.</p>
<p>Servers that support the XML introspection API support some common methods
grouped under the reserved <code class="xref py py-attr docutils literal notranslate"><span class="pre">system</span></code> attribute:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.ServerProxy.system.listMethods">
<span class="sig-prename descclassname"><span class="pre">ServerProxy.system.</span></span><span class="sig-name descname"><span class="pre">listMethods</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.ServerProxy.system.listMethods" title="Link to this definition">¶</a></dt>
<dd><p>This method returns a list of strings, one for each (non-system) method
supported by the XML-RPC server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.ServerProxy.system.methodSignature">
<span class="sig-prename descclassname"><span class="pre">ServerProxy.system.</span></span><span class="sig-name descname"><span class="pre">methodSignature</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.ServerProxy.system.methodSignature" title="Link to this definition">¶</a></dt>
<dd><p>This method takes one parameter, the name of a method implemented by the XML-RPC
server. It returns an array of possible signatures for this method. A signature
is an array of types. The first of these types is the return type of the method,
the rest are parameters.</p>
<p>Because multiple signatures (ie. overloading) is permitted, this method returns
a list of signatures rather than a singleton.</p>
<p>Signatures themselves are restricted to the top level parameters expected by a
method. For instance if a method expects one array of structs as a parameter,
and it returns a string, its signature is simply “string, array”. If it expects
three integers and returns a string, its signature is “string, int, int, int”.</p>
<p>If no signature is defined for the method, a non-array value is returned. In
Python this means that the type of the returned  value will be something other
than list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.ServerProxy.system.methodHelp">
<span class="sig-prename descclassname"><span class="pre">ServerProxy.system.</span></span><span class="sig-name descname"><span class="pre">methodHelp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.ServerProxy.system.methodHelp" title="Link to this definition">¶</a></dt>
<dd><p>This method takes one parameter, the name of a method implemented by the XML-RPC
server.  It returns a documentation string describing the use of that method. If
no such string is available, an empty string is returned. The documentation
string may contain HTML markup.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Instances of <a class="reference internal" href="#xmlrpc.client.ServerProxy" title="xmlrpc.client.ServerProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">ServerProxy</span></code></a> support the <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> protocol
for closing the underlying transport.</p>
</div>
<p>A working example follows. The server code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>

<span class="k">def</span> <span class="nf">is_even</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">n</span> <span class="o">%</span> <span class="mi">2</span> <span class="o">==</span> <span class="mi">0</span>

<span class="n">server</span> <span class="o">=</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Listening on port 8000...&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">is_even</span><span class="p">,</span> <span class="s2">&quot;is_even&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The client code for the preceding server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="k">with</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://localhost:8000/&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">proxy</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;3 is even: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">proxy</span><span class="o">.</span><span class="n">is_even</span><span class="p">(</span><span class="mi">3</span><span class="p">)))</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;100 is even: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">proxy</span><span class="o">.</span><span class="n">is_even</span><span class="p">(</span><span class="mi">100</span><span class="p">)))</span>
</pre></div>
</div>
</section>
<section id="datetime-objects">
<span id="id2"></span><h2>DateTime Objects<a class="headerlink" href="#datetime-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.client.DateTime">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">DateTime</span></span><a class="headerlink" href="#xmlrpc.client.DateTime" title="Link to this definition">¶</a></dt>
<dd><p>This class may be initialized with seconds since the epoch, a time
tuple, an ISO 8601 time/date string, or a <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a>
instance.  It has the following methods, supported mainly for internal
use by the marshalling/unmarshalling code:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.DateTime.decode">
<span class="sig-name descname"><span class="pre">decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.DateTime.decode" title="Link to this definition">¶</a></dt>
<dd><p>Accept a string as the instance’s new time value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.DateTime.encode">
<span class="sig-name descname"><span class="pre">encode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">out</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.DateTime.encode" title="Link to this definition">¶</a></dt>
<dd><p>Write the XML-RPC encoding of this <a class="reference internal" href="#xmlrpc.client.DateTime" title="xmlrpc.client.DateTime"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateTime</span></code></a> item to the <em>out</em> stream
object.</p>
</dd></dl>

<p>It also supports certain of Python’s built-in operators through
<a class="reference internal" href="../reference/datamodel.html#object.__lt__" title="object.__lt__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rich</span> <span class="pre">comparison</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__repr__" title="object.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a>
methods.</p>
</dd></dl>

<p>A working example follows. The server code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">datetime</span>
<span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>
<span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="k">def</span> <span class="nf">today</span><span class="p">():</span>
    <span class="n">today</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">today</span><span class="p">()</span>
    <span class="k">return</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">DateTime</span><span class="p">(</span><span class="n">today</span><span class="p">)</span>

<span class="n">server</span> <span class="o">=</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Listening on port 8000...&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">today</span><span class="p">,</span> <span class="s2">&quot;today&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The client code for the preceding server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>
<span class="kn">import</span> <span class="nn">datetime</span>

<span class="n">proxy</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://localhost:8000/&quot;</span><span class="p">)</span>

<span class="n">today</span> <span class="o">=</span> <span class="n">proxy</span><span class="o">.</span><span class="n">today</span><span class="p">()</span>
<span class="c1"># convert the ISO8601 string to a datetime object</span>
<span class="n">converted</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">strptime</span><span class="p">(</span><span class="n">today</span><span class="o">.</span><span class="n">value</span><span class="p">,</span> <span class="s2">&quot;%Y%m</span><span class="si">%d</span><span class="s2">T%H:%M:%S&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Today: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">converted</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%d</span><span class="s2">.%m.%Y, %H:%M&quot;</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="binary-objects">
<span id="id3"></span><h2>Binary Objects<a class="headerlink" href="#binary-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.client.Binary">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">Binary</span></span><a class="headerlink" href="#xmlrpc.client.Binary" title="Link to this definition">¶</a></dt>
<dd><p>This class may be initialized from bytes data (which may include NULs). The
primary access to the content of a <a class="reference internal" href="#xmlrpc.client.Binary" title="xmlrpc.client.Binary"><code class="xref py py-class docutils literal notranslate"><span class="pre">Binary</span></code></a> object is provided by an
attribute:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.Binary.data">
<span class="sig-name descname"><span class="pre">data</span></span><a class="headerlink" href="#xmlrpc.client.Binary.data" title="Link to this definition">¶</a></dt>
<dd><p>The binary data encapsulated by the <a class="reference internal" href="#xmlrpc.client.Binary" title="xmlrpc.client.Binary"><code class="xref py py-class docutils literal notranslate"><span class="pre">Binary</span></code></a> instance.  The data is
provided as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
</dd></dl>

<p><a class="reference internal" href="#xmlrpc.client.Binary" title="xmlrpc.client.Binary"><code class="xref py py-class docutils literal notranslate"><span class="pre">Binary</span></code></a> objects have the following methods, supported mainly for
internal use by the marshalling/unmarshalling code:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.Binary.decode">
<span class="sig-name descname"><span class="pre">decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.Binary.decode" title="Link to this definition">¶</a></dt>
<dd><p>Accept a base64 <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object and decode it as the instance’s new data.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.client.Binary.encode">
<span class="sig-name descname"><span class="pre">encode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">out</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.Binary.encode" title="Link to this definition">¶</a></dt>
<dd><p>Write the XML-RPC base 64 encoding of this binary item to the <em>out</em> stream object.</p>
<p>The encoded data will have newlines every 76 characters as per
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2045.html#section-6.8"><strong>RFC 2045 section 6.8</strong></a>,
which was the de facto standard base64 specification when the
XML-RPC spec was written.</p>
</dd></dl>

<p>It also supports certain of Python’s built-in operators through
<a class="reference internal" href="../reference/datamodel.html#object.__eq__" title="object.__eq__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__eq__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__ne__" title="object.__ne__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__ne__()</span></code></a> methods.</p>
</dd></dl>

<p>Example usage of the binary objects.  We’re going to transfer an image over
XMLRPC:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>
<span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="k">def</span> <span class="nf">python_logo</span><span class="p">():</span>
    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;python_logo.jpg&quot;</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">handle</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">Binary</span><span class="p">(</span><span class="n">handle</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>

<span class="n">server</span> <span class="o">=</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Listening on port 8000...&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">python_logo</span><span class="p">,</span> <span class="s1">&#39;python_logo&#39;</span><span class="p">)</span>

<span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The client gets the image and saves it to a file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="n">proxy</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://localhost:8000/&quot;</span><span class="p">)</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;fetched_python_logo.jpg&quot;</span><span class="p">,</span> <span class="s2">&quot;wb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">handle</span><span class="p">:</span>
    <span class="n">handle</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">proxy</span><span class="o">.</span><span class="n">python_logo</span><span class="p">()</span><span class="o">.</span><span class="n">data</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="fault-objects">
<span id="id4"></span><h2>Fault Objects<a class="headerlink" href="#fault-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.client.Fault">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">Fault</span></span><a class="headerlink" href="#xmlrpc.client.Fault" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fault</span></code></a> object encapsulates the content of an XML-RPC fault tag. Fault
objects have the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.Fault.faultCode">
<span class="sig-name descname"><span class="pre">faultCode</span></span><a class="headerlink" href="#xmlrpc.client.Fault.faultCode" title="Link to this definition">¶</a></dt>
<dd><p>An int indicating the fault type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.Fault.faultString">
<span class="sig-name descname"><span class="pre">faultString</span></span><a class="headerlink" href="#xmlrpc.client.Fault.faultString" title="Link to this definition">¶</a></dt>
<dd><p>A string containing a diagnostic message associated with the fault.</p>
</dd></dl>

</dd></dl>

<p>In the following example we’re going to intentionally cause a <a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Fault</span></code></a> by
returning a complex type object.  The server code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>

<span class="c1"># A marshalling error is going to occur because we&#39;re returning a</span>
<span class="c1"># complex number</span>
<span class="k">def</span> <span class="nf">add</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">x</span><span class="o">+</span><span class="n">y</span><span class="o">+</span><span class="mi">0</span><span class="n">j</span>

<span class="n">server</span> <span class="o">=</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Listening on port 8000...&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">add</span><span class="p">,</span> <span class="s1">&#39;add&#39;</span><span class="p">)</span>

<span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The client code for the preceding server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="n">proxy</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://localhost:8000/&quot;</span><span class="p">)</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">proxy</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
<span class="k">except</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">Fault</span> <span class="k">as</span> <span class="n">err</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;A fault occurred&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Fault code: </span><span class="si">%d</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">err</span><span class="o">.</span><span class="n">faultCode</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Fault string: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">err</span><span class="o">.</span><span class="n">faultString</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="protocolerror-objects">
<span id="protocol-error-objects"></span><h2>ProtocolError Objects<a class="headerlink" href="#protocolerror-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.client.ProtocolError">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">ProtocolError</span></span><a class="headerlink" href="#xmlrpc.client.ProtocolError" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#xmlrpc.client.ProtocolError" title="xmlrpc.client.ProtocolError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProtocolError</span></code></a> object describes a protocol error in the underlying
transport layer (such as a 404 ‘not found’ error if the server named by the URI
does not exist).  It has the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.ProtocolError.url">
<span class="sig-name descname"><span class="pre">url</span></span><a class="headerlink" href="#xmlrpc.client.ProtocolError.url" title="Link to this definition">¶</a></dt>
<dd><p>The URI or URL that triggered the error.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.ProtocolError.errcode">
<span class="sig-name descname"><span class="pre">errcode</span></span><a class="headerlink" href="#xmlrpc.client.ProtocolError.errcode" title="Link to this definition">¶</a></dt>
<dd><p>The error code.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.ProtocolError.errmsg">
<span class="sig-name descname"><span class="pre">errmsg</span></span><a class="headerlink" href="#xmlrpc.client.ProtocolError.errmsg" title="Link to this definition">¶</a></dt>
<dd><p>The error message or diagnostic string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.client.ProtocolError.headers">
<span class="sig-name descname"><span class="pre">headers</span></span><a class="headerlink" href="#xmlrpc.client.ProtocolError.headers" title="Link to this definition">¶</a></dt>
<dd><p>A dict containing the headers of the HTTP/HTTPS request that triggered the
error.</p>
</dd></dl>

</dd></dl>

<p>In the following example we’re going to intentionally cause a <a class="reference internal" href="#xmlrpc.client.ProtocolError" title="xmlrpc.client.ProtocolError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProtocolError</span></code></a>
by providing an invalid URI:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="c1"># create a ServerProxy with a URI that doesn&#39;t respond to XMLRPC requests</span>
<span class="n">proxy</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://google.com/&quot;</span><span class="p">)</span>

<span class="k">try</span><span class="p">:</span>
    <span class="n">proxy</span><span class="o">.</span><span class="n">some_method</span><span class="p">()</span>
<span class="k">except</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ProtocolError</span> <span class="k">as</span> <span class="n">err</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;A protocol error occurred&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;URL: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">err</span><span class="o">.</span><span class="n">url</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;HTTP/HTTPS headers: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">err</span><span class="o">.</span><span class="n">headers</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Error code: </span><span class="si">%d</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">err</span><span class="o">.</span><span class="n">errcode</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Error message: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">err</span><span class="o">.</span><span class="n">errmsg</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="multicall-objects">
<h2>MultiCall Objects<a class="headerlink" href="#multicall-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xmlrpc.client.MultiCall" title="xmlrpc.client.MultiCall"><code class="xref py py-class docutils literal notranslate"><span class="pre">MultiCall</span></code></a> object provides a way to encapsulate multiple calls to a
remote server into a single request <a class="footnote-reference brackets" href="#id6" id="id5" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.client.MultiCall">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">MultiCall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.MultiCall" title="Link to this definition">¶</a></dt>
<dd><p>Create an object used to boxcar method calls. <em>server</em> is the eventual target of
the call. Calls can be made to the result object, but they will immediately
return <code class="docutils literal notranslate"><span class="pre">None</span></code>, and only store the call name and parameters in the
<a class="reference internal" href="#xmlrpc.client.MultiCall" title="xmlrpc.client.MultiCall"><code class="xref py py-class docutils literal notranslate"><span class="pre">MultiCall</span></code></a> object. Calling the object itself causes all stored calls to
be transmitted as a single <code class="docutils literal notranslate"><span class="pre">system.multicall</span></code> request. The result of this call
is a <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generator</span></a>; iterating over this generator yields the individual
results.</p>
</dd></dl>

<p>A usage example of this class follows.  The server code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>

<span class="k">def</span> <span class="nf">add</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>

<span class="k">def</span> <span class="nf">subtract</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">-</span> <span class="n">y</span>

<span class="k">def</span> <span class="nf">multiply</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">*</span> <span class="n">y</span>

<span class="k">def</span> <span class="nf">divide</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">//</span> <span class="n">y</span>

<span class="c1"># A simple server with simple arithmetic functions</span>
<span class="n">server</span> <span class="o">=</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Listening on port 8000...&quot;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_multicall_functions</span><span class="p">()</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">add</span><span class="p">,</span> <span class="s1">&#39;add&#39;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">subtract</span><span class="p">,</span> <span class="s1">&#39;subtract&#39;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">multiply</span><span class="p">,</span> <span class="s1">&#39;multiply&#39;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">divide</span><span class="p">,</span> <span class="s1">&#39;divide&#39;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The client code for the preceding server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="n">proxy</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://localhost:8000/&quot;</span><span class="p">)</span>
<span class="n">multicall</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">MultiCall</span><span class="p">(</span><span class="n">proxy</span><span class="p">)</span>
<span class="n">multicall</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">multicall</span><span class="o">.</span><span class="n">subtract</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">multicall</span><span class="o">.</span><span class="n">multiply</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">multicall</span><span class="o">.</span><span class="n">divide</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">result</span> <span class="o">=</span> <span class="n">multicall</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;7+3=</span><span class="si">%d</span><span class="s2">, 7-3=</span><span class="si">%d</span><span class="s2">, 7*3=</span><span class="si">%d</span><span class="s2">, 7//3=</span><span class="si">%d</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">result</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="convenience-functions">
<h2>Convenience Functions<a class="headerlink" href="#convenience-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="xmlrpc.client.dumps">
<span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">dumps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">params</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">methodname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">methodresponse</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_none</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.dumps" title="Link to this definition">¶</a></dt>
<dd><p>Convert <em>params</em> into an XML-RPC request. or into a response if <em>methodresponse</em>
is true. <em>params</em> can be either a tuple of arguments or an instance of the
<a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Fault</span></code></a> exception class.  If <em>methodresponse</em> is true, only a single value
can be returned, meaning that <em>params</em> must be of length 1. <em>encoding</em>, if
supplied, is the encoding to use in the generated XML; the default is UTF-8.
Python’s <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> value cannot be used in standard XML-RPC; to allow using
it via an extension,  provide a true value for <em>allow_none</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xmlrpc.client.loads">
<span class="sig-prename descclassname"><span class="pre">xmlrpc.client.</span></span><span class="sig-name descname"><span class="pre">loads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_datetime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_builtin_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.client.loads" title="Link to this definition">¶</a></dt>
<dd><p>Convert an XML-RPC request or response into Python objects, a <code class="docutils literal notranslate"><span class="pre">(params,</span>
<span class="pre">methodname)</span></code>.  <em>params</em> is a tuple of argument; <em>methodname</em> is a string, or
<code class="docutils literal notranslate"><span class="pre">None</span></code> if no method name is present in the packet. If the XML-RPC packet
represents a fault condition, this function will raise a <a class="reference internal" href="#xmlrpc.client.Fault" title="xmlrpc.client.Fault"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Fault</span></code></a> exception.
The <em>use_builtin_types</em> flag can be used to cause date/time values to be
presented as <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a> objects and binary data to be
presented as <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects; this flag is false by default.</p>
<p>The obsolete <em>use_datetime</em> flag is similar to <em>use_builtin_types</em> but it
applies only to date/time values.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>use_builtin_types</em> flag was added.</p>
</div>
</dd></dl>

</section>
<section id="example-of-client-usage">
<span id="xmlrpc-client-example"></span><h2>Example of Client Usage<a class="headerlink" href="#example-of-client-usage" title="Link to this heading">¶</a></h2>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># simple test program (from the XML-RPC specification)</span>
<span class="kn">from</span> <span class="nn">xmlrpc.client</span> <span class="kn">import</span> <span class="n">ServerProxy</span><span class="p">,</span> <span class="n">Error</span>

<span class="c1"># server = ServerProxy(&quot;http://localhost:8000&quot;) # local server</span>
<span class="k">with</span> <span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://betty.userland.com&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">proxy</span><span class="p">:</span>

    <span class="nb">print</span><span class="p">(</span><span class="n">proxy</span><span class="p">)</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">proxy</span><span class="o">.</span><span class="n">examples</span><span class="o">.</span><span class="n">getStateName</span><span class="p">(</span><span class="mi">41</span><span class="p">))</span>
    <span class="k">except</span> <span class="n">Error</span> <span class="k">as</span> <span class="n">v</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ERROR&quot;</span><span class="p">,</span> <span class="n">v</span><span class="p">)</span>
</pre></div>
</div>
<p>To access an XML-RPC server through a HTTP proxy, you need to define a custom
transport.  The following example shows how:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">http.client</span>
<span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="k">class</span> <span class="nc">ProxiedTransport</span><span class="p">(</span><span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">Transport</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">set_proxy</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">host</span><span class="p">,</span> <span class="n">port</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">proxy</span> <span class="o">=</span> <span class="n">host</span><span class="p">,</span> <span class="n">port</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">proxy_headers</span> <span class="o">=</span> <span class="n">headers</span>

    <span class="k">def</span> <span class="nf">make_connection</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">host</span><span class="p">):</span>
        <span class="n">connection</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="o">*</span><span class="bp">self</span><span class="o">.</span><span class="n">proxy</span><span class="p">)</span>
        <span class="n">connection</span><span class="o">.</span><span class="n">set_tunnel</span><span class="p">(</span><span class="n">host</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">proxy_headers</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_connection</span> <span class="o">=</span> <span class="n">host</span><span class="p">,</span> <span class="n">connection</span>
        <span class="k">return</span> <span class="n">connection</span>

<span class="n">transport</span> <span class="o">=</span> <span class="n">ProxiedTransport</span><span class="p">()</span>
<span class="n">transport</span><span class="o">.</span><span class="n">set_proxy</span><span class="p">(</span><span class="s1">&#39;proxy-server&#39;</span><span class="p">,</span> <span class="mi">8080</span><span class="p">)</span>
<span class="n">server</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s1">&#39;http://betty.userland.com&#39;</span><span class="p">,</span> <span class="n">transport</span><span class="o">=</span><span class="n">transport</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">server</span><span class="o">.</span><span class="n">examples</span><span class="o">.</span><span class="n">getStateName</span><span class="p">(</span><span class="mi">41</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="example-of-client-and-server-usage">
<h2>Example of Client and Server Usage<a class="headerlink" href="#example-of-client-and-server-usage" title="Link to this heading">¶</a></h2>
<p>See <a class="reference internal" href="xmlrpc.server.html#simplexmlrpcserver-example"><span class="std std-ref">SimpleXMLRPCServer Example</span></a>.</p>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id6" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id5">1</a><span class="fn-bracket">]</span></span>
<p>This approach has been first presented in <a class="reference external" href="https://web.archive.org/web/20060624230303/http://www.xmlrpc.com/discuss/msgReader$1208?mode=topic">a discussion on xmlrpc.com</a>.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a><ul>
<li><a class="reference internal" href="#serverproxy-objects">ServerProxy Objects</a></li>
<li><a class="reference internal" href="#datetime-objects">DateTime Objects</a></li>
<li><a class="reference internal" href="#binary-objects">Binary Objects</a></li>
<li><a class="reference internal" href="#fault-objects">Fault Objects</a></li>
<li><a class="reference internal" href="#protocolerror-objects">ProtocolError Objects</a></li>
<li><a class="reference internal" href="#multicall-objects">MultiCall Objects</a></li>
<li><a class="reference internal" href="#convenience-functions">Convenience Functions</a></li>
<li><a class="reference internal" href="#example-of-client-usage">Example of Client Usage</a></li>
<li><a class="reference internal" href="#example-of-client-and-server-usage">Example of Client and Server Usage</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xmlrpc.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc</span></code> — XMLRPC server and client modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xmlrpc.server.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xmlrpc.client.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xmlrpc.server.html" title="xmlrpc.server — Basic XML-RPC servers"
             >next</a> |</li>
        <li class="right" >
          <a href="xmlrpc.html" title="xmlrpc — XMLRPC server and client modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>