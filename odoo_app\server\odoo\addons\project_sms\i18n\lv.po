# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_sms
# 
# Translators:
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_project_stage__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the project reaches this stage."
msgstr ""

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_task_type__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the task reaches this stage."
msgstr ""

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project
msgid "Project"
msgstr "Projekti"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project_stage
msgid "Project Stage"
msgstr ""

#. module: project_sms
#: model:ir.model.fields,field_description:project_sms.field_project_project_stage__sms_template_id
#: model:ir.model.fields,field_description:project_sms.field_project_task_type__sms_template_id
msgid "SMS Template"
msgstr ""

#. module: project_sms
#: model:ir.actions.act_window,name:project_sms.project_project_act_window_sms_composer
#: model:ir.actions.act_window,name:project_sms.project_task_act_window_sms_composer
msgid "Send SMS Text Message"
msgstr ""

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task
msgid "Task"
msgstr "Uzdevums"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task_type
msgid "Task Stage"
msgstr "Uzdevuma posms"
