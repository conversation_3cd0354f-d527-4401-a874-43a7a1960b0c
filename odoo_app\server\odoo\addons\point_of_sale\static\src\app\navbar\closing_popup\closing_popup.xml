<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.ClosePosPopup">
        <div class="popup close-pos-popup">
            <div class="modal-header">
                <h4 class="modal-title">Closing Session</h4>
                <div class="total-orders fw-bolder">
                    Total <t t-esc="props.orders_details.quantity"/> orders:
                    <span class="amount" t-esc="env.utils.formatCurrency(props.orders_details.amount)"/>
                </div>
            </div>
            <main class="modal-body">
                <div class="payment-methods-overview overflow-auto">
                    <table class="text-start">
                        <thead>
                            <tr class="text-dark">
                                <th>Payment Method</th>
                                <th>Expected</th>
                                <th style="width:20%">Counted</th>
                                <th>Difference</th>
                            </tr>
                        </thead>
                        <t t-if="pos.config.cash_control">
                            <tbody>
                                <tr>
                                    <td t-esc="props.default_cash_details.name"/>
                                    <td t-esc="env.utils.formatCurrency(props.default_cash_details.amount)"/>
                                    <td class="d-flex">
                                        <Input tModel="[state.payments[props.default_cash_details.id], 'counted']"
                                            callback.bind="(value) =>  this.setManualCashInput(value)"
                                            isValid.bind="env.utils.isValidFloat"/>
                                        <div class="button icon ClosePosPopup btn btn-secondary" t-on-click="openDetailsPopup">
                                            <i class="fa fa-money fa-2x" role="img" title="Open the money details popup"/>
                                        </div>
                                    </td>
                                    <td t-esc="env.utils.formatCurrency(getDifference(props.default_cash_details.id))"
                                        t-att-class="{'warning text-danger fw-bolder': !env.utils.floatIsZero(getDifference(props.default_cash_details.id))}"/>
                                </tr>
                            </tbody>
                            <tbody class="cash-overview border-start small text-muted">
                                <tr>
                                    <td>Opening</td>
                                    <td class="align-top" t-esc="env.utils.formatCurrency(props.default_cash_details.opening)"/>
                                </tr>
                                <tr t-foreach="props.default_cash_details.moves" t-as="move" t-key="move_index">
                                    <td>
                                        <div class="flex d-flex flex-row">
                                            <div class="cash-sign me-1" t-esc="move.amount &lt; 0 ? '-' : '+'"/>
                                            <t t-esc="move.name"/>
                                        </div>
                                    </td>
                                    <td class="align-top" t-esc="env.utils.formatCurrency(Math.abs(move.amount))"/>
                                </tr>
                                <tr t-if="props.default_cash_details.payment_amount">
                                    <td>
                                        <div class="flex d-flex flex-row">
                                            <div class="cash-sign me-1" t-esc="props.default_cash_details.payment_amount &lt; 0 ? '-' : '+'"/>
                                            Payments in <t t-esc="props.default_cash_details.name"/>
                                        </div>
                                    </td>
                                    <td class="align-top" t-esc="env.utils.formatCurrency(Math.abs(props.default_cash_details.payment_amount))"/>
                                </tr>
                            </tbody>
                        </t>
                        <tbody t-if="props.other_payment_methods.length > 0">
                            <tr t-foreach="props.other_payment_methods" t-as="pm" t-key="pm.id">
                                <td t-esc="pm.name"/>
                                <td t-esc="env.utils.formatCurrency(pm.amount)"/>
                                <t t-set="_showDiff" t-value="pm.type === 'bank' and pm.number !== 0" />
                                <td t-if="_showDiff">
                                    <Input tModel="[state.payments[pm.id], 'counted']"
                                        isValid.bind="env.utils.isValidFloat" />
                                </td>
                                <td t-if="_showDiff" t-esc="env.utils.formatCurrency(getDifference(pm.id))"
                                    t-att-class="{'warning text-danger fw-bolder': getDifference(pm.id)}"/>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="notes-container d-flex flex-column flex-sm-row gap-3 border-top mt-3 pt-3">
                    <div class="opening-notes-container d-flex flex-column flex-grow-1 align-items-start" t-if="props.opening_notes" >
                        <label class="form-label" for="props.opening_notes">Opening note</label>
                        <textarea class="opening-notes form-control" id="props.opening_notes">
                            <t t-esc="props.opening_notes"/>
                        </textarea>
                    </div>
                    <div class="closing-notes-container d-flex flex-column flex-grow-1 align-items-start">
                        <label class="form-label" for="closingNotes">Closing note</label>
                        <textarea class="closing-notes form-control" id="closingNotes" placeholder="Add a closing note..." t-model="state.notes"/>
                    </div>
                </div>
            </main>
            <footer class="footer modal-footer justify-content-between">
                <div class="modal-footer-left d-flex gap-2">
                    <button class="button highlight btn btn-lg btn-primary" t-att-disabled="!canConfirm()" t-on-click="confirm">Close Session</button>
                    <button class="button btn btn-lg btn-secondary" t-att-disabled="!canCancel()" t-on-click="cancel">Discard</button>
                </div>
                <!-- Download Sale Details -->
                <div class="modal-footer-right d-flex gap-2">
                    <button class="button icon btn btn-lg btn-secondary"
                        t-on-click="downloadSalesReport"
                        title="Download a report with all the sales of the current PoS Session">
                            Daily Sale
                            <i class="fa fa-download" role="img"/>
                    </button>
                    <!-- Print Sale Details -->
                    <button t-if="hardwareProxy.printer" class="button icon btn btn-lg btn-secondary">
                        <SaleDetailsButton/>
                    </button>
                </div>
            </footer>
        </div>
    </t>

</templates>
