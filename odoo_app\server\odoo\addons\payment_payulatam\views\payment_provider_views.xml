<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">PayU latam Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='provider_creation_warning']" position="after">
                <div class="alert alert-danger"
                     role="alert"
                     invisible="code != 'payulatam'">
                    This provider is deprecated.
                    Consider disabling it and moving to <strong>Mercado Pago</strong>.
                </div>
            </xpath>
            <group name="provider_credentials" position="inside">
                <group invisible="code != 'payulatam'">
                    <field name="payulatam_merchant_id"
                           required="code == 'payulatam' and state != 'disabled'"/>
                    <field name="payulatam_account_id"
                           required="code == 'payulatam' and state != 'disabled'"/>
                    <field name="payulatam_api_key"
                           required="code == 'payulatam' and state != 'disabled'"
                           password="True"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
