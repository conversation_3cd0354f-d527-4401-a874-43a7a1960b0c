<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="re — Regular expression operations" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/re.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/re/ This module provides regular expression matching operations similar to those found in Perl. Both patterns and strings to be searched can be Unicode strings ( str) as well as 8-..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/re/ This module provides regular expression matching operations similar to those found in Perl. Both patterns and strings to be searched can be Unicode strings ( str) as well as 8-..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>re — Regular expression operations &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="difflib — Helpers for computing deltas" href="difflib.html" />
    <link rel="prev" title="string — Common string operations" href="string.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/re.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code> — Regular expression operations</a><ul>
<li><a class="reference internal" href="#regular-expression-syntax">Regular Expression Syntax</a></li>
<li><a class="reference internal" href="#module-contents">Module Contents</a><ul>
<li><a class="reference internal" href="#flags">Flags</a></li>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#regular-expression-objects">Regular Expression Objects</a></li>
<li><a class="reference internal" href="#match-objects">Match Objects</a></li>
<li><a class="reference internal" href="#regular-expression-examples">Regular Expression Examples</a><ul>
<li><a class="reference internal" href="#checking-for-a-pair">Checking for a Pair</a></li>
<li><a class="reference internal" href="#simulating-scanf">Simulating scanf()</a></li>
<li><a class="reference internal" href="#search-vs-match">search() vs. match()</a></li>
<li><a class="reference internal" href="#making-a-phonebook">Making a Phonebook</a></li>
<li><a class="reference internal" href="#text-munging">Text Munging</a></li>
<li><a class="reference internal" href="#finding-all-adverbs">Finding all Adverbs</a></li>
<li><a class="reference internal" href="#finding-all-adverbs-and-their-positions">Finding all Adverbs and their Positions</a></li>
<li><a class="reference internal" href="#raw-string-notation">Raw String Notation</a></li>
<li><a class="reference internal" href="#writing-a-tokenizer">Writing a Tokenizer</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="string.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code> — Common string operations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="difflib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">difflib</span></code> — Helpers for computing deltas</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/re.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="difflib.html" title="difflib — Helpers for computing deltas"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="string.html" title="string — Common string operations"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" accesskey="U">Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code> — Regular expression operations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-re">
<span id="re-regular-expression-operations"></span><h1><a class="reference internal" href="#module-re" title="re: Regular expression operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code></a> — Regular expression operations<a class="headerlink" href="#module-re" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/re/">Lib/re/</a></p>
<hr class="docutils" />
<p>This module provides regular expression matching operations similar to
those found in Perl.</p>
<p>Both patterns and strings to be searched can be Unicode strings (<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>)
as well as 8-bit strings (<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>).
However, Unicode strings and 8-bit strings cannot be mixed:
that is, you cannot match a Unicode string with a bytes pattern or
vice-versa; similarly, when asking for a substitution, the replacement
string must be of the same type as both the pattern and the search string.</p>
<p>Regular expressions use the backslash character (<code class="docutils literal notranslate"><span class="pre">'\'</span></code>) to indicate
special forms or to allow special characters to be used without invoking
their special meaning.  This collides with Python’s usage of the same
character for the same purpose in string literals; for example, to match
a literal backslash, one might have to write <code class="docutils literal notranslate"><span class="pre">'\\\\'</span></code> as the pattern
string, because the regular expression must be <code class="docutils literal notranslate"><span class="pre">\\</span></code>, and each
backslash must be expressed as <code class="docutils literal notranslate"><span class="pre">\\</span></code> inside a regular Python string
literal. Also, please note that any invalid escape sequences in Python’s
usage of the backslash in string literals now generate a <a class="reference internal" href="exceptions.html#SyntaxWarning" title="SyntaxWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxWarning</span></code></a>
and in the future this will become a <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a>. This behaviour
will happen even if it is a valid escape sequence for a regular expression.</p>
<p>The solution is to use Python’s raw string notation for regular expression
patterns; backslashes are not handled in any special way in a string literal
prefixed with <code class="docutils literal notranslate"><span class="pre">'r'</span></code>.  So <code class="docutils literal notranslate"><span class="pre">r&quot;\n&quot;</span></code> is a two-character string containing
<code class="docutils literal notranslate"><span class="pre">'\'</span></code> and <code class="docutils literal notranslate"><span class="pre">'n'</span></code>, while <code class="docutils literal notranslate"><span class="pre">&quot;\n&quot;</span></code> is a one-character string containing a
newline.  Usually patterns will be expressed in Python code using this raw
string notation.</p>
<p>It is important to note that most regular expression operations are available as
module-level functions and methods on
<a class="reference internal" href="#re-objects"><span class="std std-ref">compiled regular expressions</span></a>.  The functions are shortcuts
that don’t require you to compile a regex object first, but miss some
fine-tuning parameters.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The third-party <a class="reference external" href="https://pypi.org/project/regex/">regex</a> module,
which has an API compatible with the standard library <a class="reference internal" href="#module-re" title="re: Regular expression operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code></a> module,
but offers additional functionality and a more thorough Unicode support.</p>
</div>
<section id="regular-expression-syntax">
<span id="re-syntax"></span><h2>Regular Expression Syntax<a class="headerlink" href="#regular-expression-syntax" title="Link to this heading">¶</a></h2>
<p>A regular expression (or RE) specifies a set of strings that matches it; the
functions in this module let you check if a particular string matches a given
regular expression (or if a given regular expression matches a particular
string, which comes down to the same thing).</p>
<p>Regular expressions can be concatenated to form new regular expressions; if <em>A</em>
and <em>B</em> are both regular expressions, then <em>AB</em> is also a regular expression.
In general, if a string <em>p</em> matches <em>A</em> and another string <em>q</em> matches <em>B</em>, the
string <em>pq</em> will match AB.  This holds unless <em>A</em> or <em>B</em> contain low precedence
operations; boundary conditions between <em>A</em> and <em>B</em>; or have numbered group
references.  Thus, complex expressions can easily be constructed from simpler
primitive expressions like the ones described here.  For details of the theory
and implementation of regular expressions, consult the Friedl book <a class="reference internal" href="#frie09" id="id1"><span>[Frie09]</span></a>,
or almost any textbook about compiler construction.</p>
<p>A brief explanation of the format of regular expressions follows.  For further
information and a gentler presentation, consult the <a class="reference internal" href="../howto/regex.html#regex-howto"><span class="std std-ref">Regular Expression HOWTO</span></a>.</p>
<p>Regular expressions can contain both special and ordinary characters. Most
ordinary characters, like <code class="docutils literal notranslate"><span class="pre">'A'</span></code>, <code class="docutils literal notranslate"><span class="pre">'a'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'0'</span></code>, are the simplest regular
expressions; they simply match themselves.  You can concatenate ordinary
characters, so <code class="docutils literal notranslate"><span class="pre">last</span></code> matches the string <code class="docutils literal notranslate"><span class="pre">'last'</span></code>.  (In the rest of this
section, we’ll write RE’s in <code class="docutils literal notranslate"><span class="pre">this</span> <span class="pre">special</span> <span class="pre">style</span></code>, usually without quotes, and
strings to be matched <code class="docutils literal notranslate"><span class="pre">'in</span> <span class="pre">single</span> <span class="pre">quotes'</span></code>.)</p>
<p>Some characters, like <code class="docutils literal notranslate"><span class="pre">'|'</span></code> or <code class="docutils literal notranslate"><span class="pre">'('</span></code>, are special. Special
characters either stand for classes of ordinary characters, or affect
how the regular expressions around them are interpreted.</p>
<p>Repetition operators or quantifiers (<code class="docutils literal notranslate"><span class="pre">*</span></code>, <code class="docutils literal notranslate"><span class="pre">+</span></code>, <code class="docutils literal notranslate"><span class="pre">?</span></code>, <code class="docutils literal notranslate"><span class="pre">{m,n}</span></code>, etc) cannot be
directly nested. This avoids ambiguity with the non-greedy modifier suffix
<code class="docutils literal notranslate"><span class="pre">?</span></code>, and with other modifiers in other implementations. To apply a second
repetition to an inner repetition, parentheses may be used. For example,
the expression <code class="docutils literal notranslate"><span class="pre">(?:a{6})*</span></code> matches any multiple of six <code class="docutils literal notranslate"><span class="pre">'a'</span></code> characters.</p>
<p>The special characters are:</p>
<dl class="simple" id="index-0">
<dt><code class="docutils literal notranslate"><span class="pre">.</span></code></dt><dd><p>(Dot.)  In the default mode, this matches any character except a newline.  If
the <a class="reference internal" href="#re.DOTALL" title="re.DOTALL"><code class="xref py py-const docutils literal notranslate"><span class="pre">DOTALL</span></code></a> flag has been specified, this matches any character
including a newline.</p>
</dd>
</dl>
<dl class="simple" id="index-1">
<dt><code class="docutils literal notranslate"><span class="pre">^</span></code></dt><dd><p>(Caret.)  Matches the start of the string, and in <a class="reference internal" href="#re.MULTILINE" title="re.MULTILINE"><code class="xref py py-const docutils literal notranslate"><span class="pre">MULTILINE</span></code></a> mode also
matches immediately after each newline.</p>
</dd>
</dl>
<dl class="simple" id="index-2">
<dt><code class="docutils literal notranslate"><span class="pre">$</span></code></dt><dd><p>Matches the end of the string or just before the newline at the end of the
string, and in <a class="reference internal" href="#re.MULTILINE" title="re.MULTILINE"><code class="xref py py-const docutils literal notranslate"><span class="pre">MULTILINE</span></code></a> mode also matches before a newline.  <code class="docutils literal notranslate"><span class="pre">foo</span></code>
matches both ‘foo’ and ‘foobar’, while the regular expression <code class="docutils literal notranslate"><span class="pre">foo$</span></code> matches
only ‘foo’.  More interestingly, searching for <code class="docutils literal notranslate"><span class="pre">foo.$</span></code> in <code class="docutils literal notranslate"><span class="pre">'foo1\nfoo2\n'</span></code>
matches ‘foo2’ normally, but ‘foo1’ in <a class="reference internal" href="#re.MULTILINE" title="re.MULTILINE"><code class="xref py py-const docutils literal notranslate"><span class="pre">MULTILINE</span></code></a> mode; searching for
a single <code class="docutils literal notranslate"><span class="pre">$</span></code> in <code class="docutils literal notranslate"><span class="pre">'foo\n'</span></code> will find two (empty) matches: one just before
the newline, and one at the end of the string.</p>
</dd>
</dl>
<dl class="simple" id="index-3">
<dt><code class="docutils literal notranslate"><span class="pre">*</span></code></dt><dd><p>Causes the resulting RE to match 0 or more repetitions of the preceding RE, as
many repetitions as are possible.  <code class="docutils literal notranslate"><span class="pre">ab*</span></code> will match ‘a’, ‘ab’, or ‘a’ followed
by any number of ‘b’s.</p>
</dd>
</dl>
<dl class="simple" id="index-4">
<dt><code class="docutils literal notranslate"><span class="pre">+</span></code></dt><dd><p>Causes the resulting RE to match 1 or more repetitions of the preceding RE.
<code class="docutils literal notranslate"><span class="pre">ab+</span></code> will match ‘a’ followed by any non-zero number of ‘b’s; it will not
match just ‘a’.</p>
</dd>
</dl>
<dl class="simple" id="index-5">
<dt><code class="docutils literal notranslate"><span class="pre">?</span></code></dt><dd><p>Causes the resulting RE to match 0 or 1 repetitions of the preceding RE.
<code class="docutils literal notranslate"><span class="pre">ab?</span></code> will match either ‘a’ or ‘ab’.</p>
</dd>
</dl>
<dl class="simple" id="index-6">
<dt><code class="docutils literal notranslate"><span class="pre">*?</span></code>, <code class="docutils literal notranslate"><span class="pre">+?</span></code>, <code class="docutils literal notranslate"><span class="pre">??</span></code></dt><dd><p>The <code class="docutils literal notranslate"><span class="pre">'*'</span></code>, <code class="docutils literal notranslate"><span class="pre">'+'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'?'</span></code> quantifiers are all <em class="dfn">greedy</em>; they match
as much text as possible.  Sometimes this behaviour isn’t desired; if the RE
<code class="docutils literal notranslate"><span class="pre">&lt;.*&gt;</span></code> is matched against <code class="docutils literal notranslate"><span class="pre">'&lt;a&gt;</span> <span class="pre">b</span> <span class="pre">&lt;c&gt;'</span></code>, it will match the entire
string, and not just <code class="docutils literal notranslate"><span class="pre">'&lt;a&gt;'</span></code>.  Adding <code class="docutils literal notranslate"><span class="pre">?</span></code> after the quantifier makes it
perform the match in <em class="dfn">non-greedy</em> or <em class="dfn">minimal</em> fashion; as <em>few</em>
characters as possible will be matched.  Using the RE <code class="docutils literal notranslate"><span class="pre">&lt;.*?&gt;</span></code> will match
only <code class="docutils literal notranslate"><span class="pre">'&lt;a&gt;'</span></code>.</p>
</dd>
</dl>
<dl id="index-7">
<dt><code class="docutils literal notranslate"><span class="pre">*+</span></code>, <code class="docutils literal notranslate"><span class="pre">++</span></code>, <code class="docutils literal notranslate"><span class="pre">?+</span></code></dt><dd><p>Like the <code class="docutils literal notranslate"><span class="pre">'*'</span></code>, <code class="docutils literal notranslate"><span class="pre">'+'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'?'</span></code> quantifiers, those where <code class="docutils literal notranslate"><span class="pre">'+'</span></code> is
appended also match as many times as possible.
However, unlike the true greedy quantifiers, these do not allow
back-tracking when the expression following it fails to match.
These are known as <em class="dfn">possessive</em> quantifiers.
For example, <code class="docutils literal notranslate"><span class="pre">a*a</span></code> will match <code class="docutils literal notranslate"><span class="pre">'aaaa'</span></code> because the <code class="docutils literal notranslate"><span class="pre">a*</span></code> will match
all 4 <code class="docutils literal notranslate"><span class="pre">'a'</span></code>s, but, when the final <code class="docutils literal notranslate"><span class="pre">'a'</span></code> is encountered, the
expression is backtracked so that in the end the <code class="docutils literal notranslate"><span class="pre">a*</span></code> ends up matching
3 <code class="docutils literal notranslate"><span class="pre">'a'</span></code>s total, and the fourth <code class="docutils literal notranslate"><span class="pre">'a'</span></code> is matched by the final <code class="docutils literal notranslate"><span class="pre">'a'</span></code>.
However, when <code class="docutils literal notranslate"><span class="pre">a*+a</span></code> is used to match <code class="docutils literal notranslate"><span class="pre">'aaaa'</span></code>, the <code class="docutils literal notranslate"><span class="pre">a*+</span></code> will
match all 4 <code class="docutils literal notranslate"><span class="pre">'a'</span></code>, but when the final <code class="docutils literal notranslate"><span class="pre">'a'</span></code> fails to find any more
characters to match, the expression cannot be backtracked and will thus
fail to match.
<code class="docutils literal notranslate"><span class="pre">x*+</span></code>, <code class="docutils literal notranslate"><span class="pre">x++</span></code> and <code class="docutils literal notranslate"><span class="pre">x?+</span></code> are equivalent to <code class="docutils literal notranslate"><span class="pre">(?&gt;x*)</span></code>, <code class="docutils literal notranslate"><span class="pre">(?&gt;x+)</span></code>
and <code class="docutils literal notranslate"><span class="pre">(?&gt;x?)</span></code> correspondingly.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd>
</dl>
<dl id="index-8">
<dt><code class="docutils literal notranslate"><span class="pre">{m}</span></code></dt><dd><p>Specifies that exactly <em>m</em> copies of the previous RE should be matched; fewer
matches cause the entire RE not to match.  For example, <code class="docutils literal notranslate"><span class="pre">a{6}</span></code> will match
exactly six <code class="docutils literal notranslate"><span class="pre">'a'</span></code> characters, but not five.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">{m,n}</span></code></dt><dd><p>Causes the resulting RE to match from <em>m</em> to <em>n</em> repetitions of the preceding
RE, attempting to match as many repetitions as possible.  For example,
<code class="docutils literal notranslate"><span class="pre">a{3,5}</span></code> will match from 3 to 5 <code class="docutils literal notranslate"><span class="pre">'a'</span></code> characters.  Omitting <em>m</em> specifies a
lower bound of zero,  and omitting <em>n</em> specifies an infinite upper bound.  As an
example, <code class="docutils literal notranslate"><span class="pre">a{4,}b</span></code> will match <code class="docutils literal notranslate"><span class="pre">'aaaab'</span></code> or a thousand <code class="docutils literal notranslate"><span class="pre">'a'</span></code> characters
followed by a <code class="docutils literal notranslate"><span class="pre">'b'</span></code>, but not <code class="docutils literal notranslate"><span class="pre">'aaab'</span></code>. The comma may not be omitted or the
modifier would be confused with the previously described form.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">{m,n}?</span></code></dt><dd><p>Causes the resulting RE to match from <em>m</em> to <em>n</em> repetitions of the preceding
RE, attempting to match as <em>few</em> repetitions as possible.  This is the
non-greedy version of the previous quantifier.  For example, on the
6-character string <code class="docutils literal notranslate"><span class="pre">'aaaaaa'</span></code>, <code class="docutils literal notranslate"><span class="pre">a{3,5}</span></code> will match 5 <code class="docutils literal notranslate"><span class="pre">'a'</span></code> characters,
while <code class="docutils literal notranslate"><span class="pre">a{3,5}?</span></code> will only match 3 characters.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">{m,n}+</span></code></dt><dd><p>Causes the resulting RE to match from <em>m</em> to <em>n</em> repetitions of the
preceding RE, attempting to match as many repetitions as possible
<em>without</em> establishing any backtracking points.
This is the possessive version of the quantifier above.
For example, on the 6-character string <code class="docutils literal notranslate"><span class="pre">'aaaaaa'</span></code>, <code class="docutils literal notranslate"><span class="pre">a{3,5}+aa</span></code>
attempt to match 5 <code class="docutils literal notranslate"><span class="pre">'a'</span></code> characters, then, requiring 2 more <code class="docutils literal notranslate"><span class="pre">'a'</span></code>s,
will need more characters than available and thus fail, while
<code class="docutils literal notranslate"><span class="pre">a{3,5}aa</span></code> will match with <code class="docutils literal notranslate"><span class="pre">a{3,5}</span></code> capturing 5, then 4 <code class="docutils literal notranslate"><span class="pre">'a'</span></code>s
by backtracking and then the final 2 <code class="docutils literal notranslate"><span class="pre">'a'</span></code>s are matched by the final
<code class="docutils literal notranslate"><span class="pre">aa</span></code> in the pattern.
<code class="docutils literal notranslate"><span class="pre">x{m,n}+</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">(?&gt;x{m,n})</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd>
</dl>
<dl id="index-9">
<dt><code class="docutils literal notranslate"><span class="pre">\</span></code></dt><dd><p>Either escapes special characters (permitting you to match characters like
<code class="docutils literal notranslate"><span class="pre">'*'</span></code>, <code class="docutils literal notranslate"><span class="pre">'?'</span></code>, and so forth), or signals a special sequence; special
sequences are discussed below.</p>
<p>If you’re not using a raw string to express the pattern, remember that Python
also uses the backslash as an escape sequence in string literals; if the escape
sequence isn’t recognized by Python’s parser, the backslash and subsequent
character are included in the resulting string.  However, if Python would
recognize the resulting sequence, the backslash should be repeated twice.  This
is complicated and hard to understand, so it’s highly recommended that you use
raw strings for all but the simplest expressions.</p>
</dd>
</dl>
<dl id="index-10">
<dt><code class="docutils literal notranslate"><span class="pre">[]</span></code></dt><dd><p>Used to indicate a set of characters.  In a set:</p>
<ul class="simple">
<li><p>Characters can be listed individually, e.g. <code class="docutils literal notranslate"><span class="pre">[amk]</span></code> will match <code class="docutils literal notranslate"><span class="pre">'a'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'m'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'k'</span></code>.</p></li>
</ul>
<ul class="simple" id="index-11">
<li><p>Ranges of characters can be indicated by giving two characters and separating
them by a <code class="docutils literal notranslate"><span class="pre">'-'</span></code>, for example <code class="docutils literal notranslate"><span class="pre">[a-z]</span></code> will match any lowercase ASCII letter,
<code class="docutils literal notranslate"><span class="pre">[0-5][0-9]</span></code> will match all the two-digits numbers from <code class="docutils literal notranslate"><span class="pre">00</span></code> to <code class="docutils literal notranslate"><span class="pre">59</span></code>, and
<code class="docutils literal notranslate"><span class="pre">[0-9A-Fa-f]</span></code> will match any hexadecimal digit.  If <code class="docutils literal notranslate"><span class="pre">-</span></code> is escaped (e.g.
<code class="docutils literal notranslate"><span class="pre">[a\-z]</span></code>) or if it’s placed as the first or last character
(e.g. <code class="docutils literal notranslate"><span class="pre">[-a]</span></code> or <code class="docutils literal notranslate"><span class="pre">[a-]</span></code>), it will match a literal <code class="docutils literal notranslate"><span class="pre">'-'</span></code>.</p></li>
<li><p>Special characters lose their special meaning inside sets.  For example,
<code class="docutils literal notranslate"><span class="pre">[(+*)]</span></code> will match any of the literal characters <code class="docutils literal notranslate"><span class="pre">'('</span></code>, <code class="docutils literal notranslate"><span class="pre">'+'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'*'</span></code>, or <code class="docutils literal notranslate"><span class="pre">')'</span></code>.</p></li>
</ul>
<ul class="simple" id="index-12">
<li><p>Character classes such as <code class="docutils literal notranslate"><span class="pre">\w</span></code> or <code class="docutils literal notranslate"><span class="pre">\S</span></code> (defined below) are also accepted
inside a set, although the characters they match depend on the <a class="reference internal" href="#flags">flags</a> used.</p></li>
</ul>
<ul class="simple" id="index-13">
<li><p>Characters that are not within a range can be matched by <em class="dfn">complementing</em>
the set.  If the first character of the set is <code class="docutils literal notranslate"><span class="pre">'^'</span></code>, all the characters
that are <em>not</em> in the set will be matched.  For example, <code class="docutils literal notranslate"><span class="pre">[^5]</span></code> will match
any character except <code class="docutils literal notranslate"><span class="pre">'5'</span></code>, and <code class="docutils literal notranslate"><span class="pre">[^^]</span></code> will match any character except
<code class="docutils literal notranslate"><span class="pre">'^'</span></code>.  <code class="docutils literal notranslate"><span class="pre">^</span></code> has no special meaning if it’s not the first character in
the set.</p></li>
<li><p>To match a literal <code class="docutils literal notranslate"><span class="pre">']'</span></code> inside a set, precede it with a backslash, or
place it at the beginning of the set.  For example, both <code class="docutils literal notranslate"><span class="pre">[()[\]{}]</span></code> and
<code class="docutils literal notranslate"><span class="pre">[]()[{}]</span></code> will match a right bracket, as well as left bracket, braces,
and parentheses.</p></li>
</ul>
<ul class="simple">
<li><p>Support of nested sets and set operations as in <a class="reference external" href="https://unicode.org/reports/tr18/">Unicode Technical
Standard #18</a> might be added in the future.  This would change the
syntax, so to facilitate this change a <a class="reference internal" href="exceptions.html#FutureWarning" title="FutureWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FutureWarning</span></code></a> will be raised
in ambiguous cases for the time being.
That includes sets starting with a literal <code class="docutils literal notranslate"><span class="pre">'['</span></code> or containing literal
character sequences <code class="docutils literal notranslate"><span class="pre">'--'</span></code>, <code class="docutils literal notranslate"><span class="pre">'&amp;&amp;'</span></code>, <code class="docutils literal notranslate"><span class="pre">'~~'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'||'</span></code>.  To
avoid a warning escape them with a backslash.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="exceptions.html#FutureWarning" title="FutureWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FutureWarning</span></code></a> is raised if a character set contains constructs
that will change semantically in the future.</p>
</div>
</dd>
</dl>
<dl class="simple" id="index-14">
<dt><code class="docutils literal notranslate"><span class="pre">|</span></code></dt><dd><p><code class="docutils literal notranslate"><span class="pre">A|B</span></code>, where <em>A</em> and <em>B</em> can be arbitrary REs, creates a regular expression that
will match either <em>A</em> or <em>B</em>.  An arbitrary number of REs can be separated by the
<code class="docutils literal notranslate"><span class="pre">'|'</span></code> in this way.  This can be used inside groups (see below) as well.  As
the target string is scanned, REs separated by <code class="docutils literal notranslate"><span class="pre">'|'</span></code> are tried from left to
right. When one pattern completely matches, that branch is accepted. This means
that once <em>A</em> matches, <em>B</em> will not be tested further, even if it would
produce a longer overall match.  In other words, the <code class="docutils literal notranslate"><span class="pre">'|'</span></code> operator is never
greedy.  To match a literal <code class="docutils literal notranslate"><span class="pre">'|'</span></code>, use <code class="docutils literal notranslate"><span class="pre">\|</span></code>, or enclose it inside a
character class, as in <code class="docutils literal notranslate"><span class="pre">[|]</span></code>.</p>
</dd>
</dl>
<dl class="simple" id="index-15">
<dt><code class="docutils literal notranslate"><span class="pre">(...)</span></code></dt><dd><p>Matches whatever regular expression is inside the parentheses, and indicates the
start and end of a group; the contents of a group can be retrieved after a match
has been performed, and can be matched later in the string with the <code class="docutils literal notranslate"><span class="pre">\number</span></code>
special sequence, described below.  To match the literals <code class="docutils literal notranslate"><span class="pre">'('</span></code> or <code class="docutils literal notranslate"><span class="pre">')'</span></code>,
use <code class="docutils literal notranslate"><span class="pre">\(</span></code> or <code class="docutils literal notranslate"><span class="pre">\)</span></code>, or enclose them inside a character class: <code class="docutils literal notranslate"><span class="pre">[(]</span></code>, <code class="docutils literal notranslate"><span class="pre">[)]</span></code>.</p>
</dd>
</dl>
<dl id="index-16">
<dt><code class="docutils literal notranslate"><span class="pre">(?...)</span></code></dt><dd><p>This is an extension notation (a <code class="docutils literal notranslate"><span class="pre">'?'</span></code> following a <code class="docutils literal notranslate"><span class="pre">'('</span></code> is not meaningful
otherwise).  The first character after the <code class="docutils literal notranslate"><span class="pre">'?'</span></code> determines what the meaning
and further syntax of the construct is. Extensions usually do not create a new
group; <code class="docutils literal notranslate"><span class="pre">(?P&lt;name&gt;...)</span></code> is the only exception to this rule. Following are the
currently supported extensions.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">(?aiLmsux)</span></code></dt><dd><p>(One or more letters from the set
<code class="docutils literal notranslate"><span class="pre">'a'</span></code>, <code class="docutils literal notranslate"><span class="pre">'i'</span></code>, <code class="docutils literal notranslate"><span class="pre">'L'</span></code>, <code class="docutils literal notranslate"><span class="pre">'m'</span></code>, <code class="docutils literal notranslate"><span class="pre">'s'</span></code>, <code class="docutils literal notranslate"><span class="pre">'u'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code>.)
The group matches the empty string;
the letters set the corresponding flags for the entire regular expression:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#re.A" title="re.A"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.A</span></code></a> (ASCII-only matching)</p></li>
<li><p><a class="reference internal" href="#re.I" title="re.I"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.I</span></code></a> (ignore case)</p></li>
<li><p><a class="reference internal" href="#re.L" title="re.L"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.L</span></code></a> (locale dependent)</p></li>
<li><p><a class="reference internal" href="#re.M" title="re.M"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.M</span></code></a> (multi-line)</p></li>
<li><p><a class="reference internal" href="#re.S" title="re.S"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.S</span></code></a> (dot matches all)</p></li>
<li><p><a class="reference internal" href="#re.U" title="re.U"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.U</span></code></a> (Unicode matching)</p></li>
<li><p><a class="reference internal" href="#re.X" title="re.X"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.X</span></code></a> (verbose)</p></li>
</ul>
<p>(The flags are described in <a class="reference internal" href="#contents-of-module-re"><span class="std std-ref">Module Contents</span></a>.)
This is useful if you wish to include the flags as part of the
regular expression, instead of passing a <em>flag</em> argument to the
<a class="reference internal" href="#re.compile" title="re.compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.compile()</span></code></a> function.
Flags should be used first in the expression string.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>This construction can only be used at the start of the expression.</p>
</div>
</dd>
</dl>
<dl id="index-17">
<dt><code class="docutils literal notranslate"><span class="pre">(?:...)</span></code></dt><dd><p>A non-capturing version of regular parentheses.  Matches whatever regular
expression is inside the parentheses, but the substring matched by the group
<em>cannot</em> be retrieved after performing a match or referenced later in the
pattern.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">(?aiLmsux-imsx:...)</span></code></dt><dd><p>(Zero or more letters from the set
<code class="docutils literal notranslate"><span class="pre">'a'</span></code>, <code class="docutils literal notranslate"><span class="pre">'i'</span></code>, <code class="docutils literal notranslate"><span class="pre">'L'</span></code>, <code class="docutils literal notranslate"><span class="pre">'m'</span></code>, <code class="docutils literal notranslate"><span class="pre">'s'</span></code>, <code class="docutils literal notranslate"><span class="pre">'u'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code>,
optionally followed by <code class="docutils literal notranslate"><span class="pre">'-'</span></code> followed by
one or more letters from the <code class="docutils literal notranslate"><span class="pre">'i'</span></code>, <code class="docutils literal notranslate"><span class="pre">'m'</span></code>, <code class="docutils literal notranslate"><span class="pre">'s'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code>.)
The letters set or remove the corresponding flags for the part of the expression:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#re.A" title="re.A"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.A</span></code></a> (ASCII-only matching)</p></li>
<li><p><a class="reference internal" href="#re.I" title="re.I"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.I</span></code></a> (ignore case)</p></li>
<li><p><a class="reference internal" href="#re.L" title="re.L"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.L</span></code></a> (locale dependent)</p></li>
<li><p><a class="reference internal" href="#re.M" title="re.M"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.M</span></code></a> (multi-line)</p></li>
<li><p><a class="reference internal" href="#re.S" title="re.S"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.S</span></code></a> (dot matches all)</p></li>
<li><p><a class="reference internal" href="#re.U" title="re.U"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.U</span></code></a> (Unicode matching)</p></li>
<li><p><a class="reference internal" href="#re.X" title="re.X"><code class="xref py py-const docutils literal notranslate"><span class="pre">re.X</span></code></a> (verbose)</p></li>
</ul>
<p>(The flags are described in <a class="reference internal" href="#contents-of-module-re"><span class="std std-ref">Module Contents</span></a>.)</p>
<p>The letters <code class="docutils literal notranslate"><span class="pre">'a'</span></code>, <code class="docutils literal notranslate"><span class="pre">'L'</span></code> and <code class="docutils literal notranslate"><span class="pre">'u'</span></code> are mutually exclusive when used
as inline flags, so they can’t be combined or follow <code class="docutils literal notranslate"><span class="pre">'-'</span></code>.  Instead,
when one of them appears in an inline group, it overrides the matching mode
in the enclosing group.  In Unicode patterns <code class="docutils literal notranslate"><span class="pre">(?a:...)</span></code> switches to
ASCII-only matching, and <code class="docutils literal notranslate"><span class="pre">(?u:...)</span></code> switches to Unicode matching
(default).  In bytes patterns <code class="docutils literal notranslate"><span class="pre">(?L:...)</span></code> switches to locale dependent
matching, and <code class="docutils literal notranslate"><span class="pre">(?a:...)</span></code> switches to ASCII-only matching (default).
This override is only in effect for the narrow inline group, and the
original matching mode is restored outside of the group.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The letters <code class="docutils literal notranslate"><span class="pre">'a'</span></code>, <code class="docutils literal notranslate"><span class="pre">'L'</span></code> and <code class="docutils literal notranslate"><span class="pre">'u'</span></code> also can be used in a group.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">(?&gt;...)</span></code></dt><dd><p>Attempts to match <code class="docutils literal notranslate"><span class="pre">...</span></code> as if it was a separate regular expression, and
if successful, continues to match the rest of the pattern following it.
If the subsequent pattern fails to match, the stack can only be unwound
to a point <em>before</em> the <code class="docutils literal notranslate"><span class="pre">(?&gt;...)</span></code> because once exited, the expression,
known as an <em class="dfn">atomic group</em>, has thrown away all stack points within
itself.
Thus, <code class="docutils literal notranslate"><span class="pre">(?&gt;.*).</span></code> would never match anything because first the <code class="docutils literal notranslate"><span class="pre">.*</span></code>
would match all characters possible, then, having nothing left to match,
the final <code class="docutils literal notranslate"><span class="pre">.</span></code> would fail to match.
Since there are no stack points saved in the Atomic Group, and there is
no stack point before it, the entire expression would thus fail to match.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd>
</dl>
<dl id="index-18">
<dt><code class="docutils literal notranslate"><span class="pre">(?P&lt;name&gt;...)</span></code></dt><dd><p>Similar to regular parentheses, but the substring matched by the group is
accessible via the symbolic group name <em>name</em>.  Group names must be valid
Python identifiers, and in <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> patterns they can only contain
bytes in the ASCII range.  Each group name must be defined only once within
a regular expression.  A symbolic group is also a numbered group, just as if
the group were not named.</p>
<p>Named groups can be referenced in three contexts.  If the pattern is
<code class="docutils literal notranslate"><span class="pre">(?P&lt;quote&gt;['&quot;]).*?(?P=quote)</span></code> (i.e. matching a string quoted with either
single or double quotes):</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Context of reference to group “quote”</p></th>
<th class="head"><p>Ways to reference it</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>in the same pattern itself</p></td>
<td><ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">(?P=quote)</span></code> (as shown)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">\1</span></code></p></li>
</ul>
</td>
</tr>
<tr class="row-odd"><td><p>when processing match object <em>m</em></p></td>
<td><ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">m.group('quote')</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">m.end('quote')</span></code> (etc.)</p></li>
</ul>
</td>
</tr>
<tr class="row-even"><td><p>in a string passed to the <em>repl</em>
argument of <code class="docutils literal notranslate"><span class="pre">re.sub()</span></code></p></td>
<td><ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">\g&lt;quote&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">\g&lt;1&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">\1</span></code></p></li>
</ul>
</td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>In <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> patterns, group <em>name</em> can only contain bytes
in the ASCII range (<code class="docutils literal notranslate"><span class="pre">b'\x00'</span></code>-<code class="docutils literal notranslate"><span class="pre">b'\x7f'</span></code>).</p>
</div>
</dd>
</dl>
<dl class="simple" id="index-19">
<dt><code class="docutils literal notranslate"><span class="pre">(?P=name)</span></code></dt><dd><p>A backreference to a named group; it matches whatever text was matched by the
earlier group named <em>name</em>.</p>
</dd>
</dl>
<dl class="simple" id="index-20">
<dt><code class="docutils literal notranslate"><span class="pre">(?#...)</span></code></dt><dd><p>A comment; the contents of the parentheses are simply ignored.</p>
</dd>
</dl>
<dl class="simple" id="index-21">
<dt><code class="docutils literal notranslate"><span class="pre">(?=...)</span></code></dt><dd><p>Matches if <code class="docutils literal notranslate"><span class="pre">...</span></code> matches next, but doesn’t consume any of the string.  This is
called a <em class="dfn">lookahead assertion</em>.  For example, <code class="docutils literal notranslate"><span class="pre">Isaac</span> <span class="pre">(?=Asimov)</span></code> will match
<code class="docutils literal notranslate"><span class="pre">'Isaac</span> <span class="pre">'</span></code> only if it’s followed by <code class="docutils literal notranslate"><span class="pre">'Asimov'</span></code>.</p>
</dd>
</dl>
<dl class="simple" id="index-22">
<dt><code class="docutils literal notranslate"><span class="pre">(?!...)</span></code></dt><dd><p>Matches if <code class="docutils literal notranslate"><span class="pre">...</span></code> doesn’t match next.  This is a <em class="dfn">negative lookahead assertion</em>.
For example, <code class="docutils literal notranslate"><span class="pre">Isaac</span> <span class="pre">(?!Asimov)</span></code> will match <code class="docutils literal notranslate"><span class="pre">'Isaac</span> <span class="pre">'</span></code> only if it’s <em>not</em>
followed by <code class="docutils literal notranslate"><span class="pre">'Asimov'</span></code>.</p>
</dd>
</dl>
<dl id="index-23">
<dt><code class="docutils literal notranslate"><span class="pre">(?&lt;=...)</span></code></dt><dd><p>Matches if the current position in the string is preceded by a match for <code class="docutils literal notranslate"><span class="pre">...</span></code>
that ends at the current position.  This is called a <em class="dfn">positive lookbehind
assertion</em>. <code class="docutils literal notranslate"><span class="pre">(?&lt;=abc)def</span></code> will find a match in <code class="docutils literal notranslate"><span class="pre">'abcdef'</span></code>, since the
lookbehind will back up 3 characters and check if the contained pattern matches.
The contained pattern must only match strings of some fixed length, meaning that
<code class="docutils literal notranslate"><span class="pre">abc</span></code> or <code class="docutils literal notranslate"><span class="pre">a|b</span></code> are allowed, but <code class="docutils literal notranslate"><span class="pre">a*</span></code> and <code class="docutils literal notranslate"><span class="pre">a{3,4}</span></code> are not.  Note that
patterns which start with positive lookbehind assertions will not match at the
beginning of the string being searched; you will most likely want to use the
<a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">search()</span></code></a> function rather than the <a class="reference internal" href="#re.match" title="re.match"><code class="xref py py-func docutils literal notranslate"><span class="pre">match()</span></code></a> function:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">re</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s1">&#39;(?&lt;=abc)def&#39;</span><span class="p">,</span> <span class="s1">&#39;abcdef&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="go">&#39;def&#39;</span>
</pre></div>
</div>
<p>This example looks for a word following a hyphen:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;(?&lt;=-)\w+&#39;</span><span class="p">,</span> <span class="s1">&#39;spam-egg&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="go">&#39;egg&#39;</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added support for group references of fixed length.</p>
</div>
</dd>
</dl>
<dl class="simple" id="index-24">
<dt><code class="docutils literal notranslate"><span class="pre">(?&lt;!...)</span></code></dt><dd><p>Matches if the current position in the string is not preceded by a match for
<code class="docutils literal notranslate"><span class="pre">...</span></code>.  This is called a <em class="dfn">negative lookbehind assertion</em>.  Similar to
positive lookbehind assertions, the contained pattern must only match strings of
some fixed length.  Patterns which start with negative lookbehind assertions may
match at the beginning of the string being searched.</p>
</dd>
</dl>
<span id="re-conditional-expression"></span><dl id="index-25">
<dt><code class="docutils literal notranslate"><span class="pre">(?(id/name)yes-pattern|no-pattern)</span></code></dt><dd><p>Will try to match with <code class="docutils literal notranslate"><span class="pre">yes-pattern</span></code> if the group with given <em>id</em> or
<em>name</em> exists, and with <code class="docutils literal notranslate"><span class="pre">no-pattern</span></code> if it doesn’t. <code class="docutils literal notranslate"><span class="pre">no-pattern</span></code> is
optional and can be omitted. For example,
<code class="docutils literal notranslate"><span class="pre">(&lt;)?(\w+&#64;\w+(?:\.\w+)+)(?(1)&gt;|$)</span></code> is a poor email matching pattern, which
will match with <code class="docutils literal notranslate"><span class="pre">'&lt;user&#64;host.com&gt;'</span></code> as well as <code class="docutils literal notranslate"><span class="pre">'user&#64;host.com'</span></code>, but
not with <code class="docutils literal notranslate"><span class="pre">'&lt;user&#64;host.com'</span></code> nor <code class="docutils literal notranslate"><span class="pre">'user&#64;host.com&gt;'</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Group <em>id</em> can only contain ASCII digits.
In <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> patterns, group <em>name</em> can only contain bytes
in the ASCII range (<code class="docutils literal notranslate"><span class="pre">b'\x00'</span></code>-<code class="docutils literal notranslate"><span class="pre">b'\x7f'</span></code>).</p>
</div>
</dd>
</dl>
<p id="re-special-sequences">The special sequences consist of <code class="docutils literal notranslate"><span class="pre">'\'</span></code> and a character from the list below.
If the ordinary character is not an ASCII digit or an ASCII letter, then the
resulting RE will match the second character.  For example, <code class="docutils literal notranslate"><span class="pre">\$</span></code> matches the
character <code class="docutils literal notranslate"><span class="pre">'$'</span></code>.</p>
<dl class="simple" id="index-26">
<dt><code class="docutils literal notranslate"><span class="pre">\number</span></code></dt><dd><p>Matches the contents of the group of the same number.  Groups are numbered
starting from 1.  For example, <code class="docutils literal notranslate"><span class="pre">(.+)</span> <span class="pre">\1</span></code> matches <code class="docutils literal notranslate"><span class="pre">'the</span> <span class="pre">the'</span></code> or <code class="docutils literal notranslate"><span class="pre">'55</span> <span class="pre">55'</span></code>,
but not <code class="docutils literal notranslate"><span class="pre">'thethe'</span></code> (note the space after the group).  This special sequence
can only be used to match one of the first 99 groups.  If the first digit of
<em>number</em> is 0, or <em>number</em> is 3 octal digits long, it will not be interpreted as
a group match, but as the character with octal value <em>number</em>. Inside the
<code class="docutils literal notranslate"><span class="pre">'['</span></code> and <code class="docutils literal notranslate"><span class="pre">']'</span></code> of a character class, all numeric escapes are treated as
characters.</p>
</dd>
</dl>
<dl class="simple" id="index-27">
<dt><code class="docutils literal notranslate"><span class="pre">\A</span></code></dt><dd><p>Matches only at the start of the string.</p>
</dd>
</dl>
<dl id="index-28">
<dt><code class="docutils literal notranslate"><span class="pre">\b</span></code></dt><dd><p>Matches the empty string, but only at the beginning or end of a word.
A word is defined as a sequence of word characters.
Note that formally, <code class="docutils literal notranslate"><span class="pre">\b</span></code> is defined as the boundary
between a <code class="docutils literal notranslate"><span class="pre">\w</span></code> and a <code class="docutils literal notranslate"><span class="pre">\W</span></code> character (or vice versa),
or between <code class="docutils literal notranslate"><span class="pre">\w</span></code> and the beginning or end of the string.
This means that <code class="docutils literal notranslate"><span class="pre">r'\bat\b'</span></code> matches <code class="docutils literal notranslate"><span class="pre">'at'</span></code>, <code class="docutils literal notranslate"><span class="pre">'at.'</span></code>, <code class="docutils literal notranslate"><span class="pre">'(at)'</span></code>,
and <code class="docutils literal notranslate"><span class="pre">'as</span> <span class="pre">at</span> <span class="pre">ay'</span></code> but not <code class="docutils literal notranslate"><span class="pre">'attempt'</span></code> or <code class="docutils literal notranslate"><span class="pre">'atlas'</span></code>.</p>
<p>The default word characters in Unicode (str) patterns
are Unicode alphanumerics and the underscore,
but this can be changed by using the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag.
Word boundaries are determined by the current locale
if the <a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> flag is used.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Inside a character range, <code class="docutils literal notranslate"><span class="pre">\b</span></code> represents the backspace character,
for compatibility with Python’s string literals.</p>
</div>
</dd>
</dl>
<dl class="simple" id="index-29">
<dt><code class="docutils literal notranslate"><span class="pre">\B</span></code></dt><dd><p>Matches the empty string,
but only when it is <em>not</em> at the beginning or end of a word.
This means that <code class="docutils literal notranslate"><span class="pre">r'at\B'</span></code> matches <code class="docutils literal notranslate"><span class="pre">'athens'</span></code>, <code class="docutils literal notranslate"><span class="pre">'atom'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'attorney'</span></code>, but not <code class="docutils literal notranslate"><span class="pre">'at'</span></code>, <code class="docutils literal notranslate"><span class="pre">'at.'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'at!'</span></code>.
<code class="docutils literal notranslate"><span class="pre">\B</span></code> is the opposite of <code class="docutils literal notranslate"><span class="pre">\b</span></code>,
so word characters in Unicode (str) patterns
are Unicode alphanumerics or the underscore,
although this can be changed by using the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag.
Word boundaries are determined by the current locale
if the <a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> flag is used.</p>
</dd>
</dl>
<dl id="index-30">
<dt><code class="docutils literal notranslate"><span class="pre">\d</span></code></dt><dd><dl>
<dt>For Unicode (str) patterns:</dt><dd><p>Matches any Unicode decimal digit
(that is, any character in Unicode character category <a class="reference external" href="https://www.unicode.org/versions/Unicode15.0.0/ch04.pdf#G134153">[Nd]</a>).
This includes <code class="docutils literal notranslate"><span class="pre">[0-9]</span></code>, and also many other digit characters.</p>
<p>Matches <code class="docutils literal notranslate"><span class="pre">[0-9]</span></code> if the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used.</p>
</dd>
<dt>For 8-bit (bytes) patterns:</dt><dd><p>Matches any decimal digit in the ASCII character set;
this is equivalent to <code class="docutils literal notranslate"><span class="pre">[0-9]</span></code>.</p>
</dd>
</dl>
</dd>
</dl>
<dl id="index-31">
<dt><code class="docutils literal notranslate"><span class="pre">\D</span></code></dt><dd><p>Matches any character which is not a decimal digit.
This is the opposite of <code class="docutils literal notranslate"><span class="pre">\d</span></code>.</p>
<p>Matches <code class="docutils literal notranslate"><span class="pre">[^0-9]</span></code> if the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used.</p>
</dd>
</dl>
<dl id="index-32">
<dt><code class="docutils literal notranslate"><span class="pre">\s</span></code></dt><dd><dl>
<dt>For Unicode (str) patterns:</dt><dd><p>Matches Unicode whitespace characters (which includes
<code class="docutils literal notranslate"><span class="pre">[</span> <span class="pre">\t\n\r\f\v]</span></code>, and also many other characters, for example the
non-breaking spaces mandated by typography rules in many
languages).</p>
<p>Matches <code class="docutils literal notranslate"><span class="pre">[</span> <span class="pre">\t\n\r\f\v]</span></code> if the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used.</p>
</dd>
<dt>For 8-bit (bytes) patterns:</dt><dd><p>Matches characters considered whitespace in the ASCII character set;
this is equivalent to <code class="docutils literal notranslate"><span class="pre">[</span> <span class="pre">\t\n\r\f\v]</span></code>.</p>
</dd>
</dl>
</dd>
</dl>
<dl id="index-33">
<dt><code class="docutils literal notranslate"><span class="pre">\S</span></code></dt><dd><p>Matches any character which is not a whitespace character. This is
the opposite of <code class="docutils literal notranslate"><span class="pre">\s</span></code>.</p>
<p>Matches <code class="docutils literal notranslate"><span class="pre">[^</span> <span class="pre">\t\n\r\f\v]</span></code> if the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used.</p>
</dd>
</dl>
<dl id="index-34">
<dt><code class="docutils literal notranslate"><span class="pre">\w</span></code></dt><dd><dl>
<dt>For Unicode (str) patterns:</dt><dd><p>Matches Unicode word characters;
this includes all Unicode alphanumeric characters
(as defined by <a class="reference internal" href="stdtypes.html#str.isalnum" title="str.isalnum"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.isalnum()</span></code></a>),
as well as the underscore (<code class="docutils literal notranslate"><span class="pre">_</span></code>).</p>
<p>Matches <code class="docutils literal notranslate"><span class="pre">[a-zA-Z0-9_]</span></code> if the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used.</p>
</dd>
<dt>For 8-bit (bytes) patterns:</dt><dd><p>Matches characters considered alphanumeric in the ASCII character set;
this is equivalent to <code class="docutils literal notranslate"><span class="pre">[a-zA-Z0-9_]</span></code>.
If the <a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> flag is used,
matches characters considered alphanumeric in the current locale and the underscore.</p>
</dd>
</dl>
</dd>
</dl>
<dl id="index-35">
<dt><code class="docutils literal notranslate"><span class="pre">\W</span></code></dt><dd><p>Matches any character which is not a word character.
This is the opposite of <code class="docutils literal notranslate"><span class="pre">\w</span></code>.
By default, matches non-underscore (<code class="docutils literal notranslate"><span class="pre">_</span></code>) characters
for which <a class="reference internal" href="stdtypes.html#str.isalnum" title="str.isalnum"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.isalnum()</span></code></a> returns <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<p>Matches <code class="docutils literal notranslate"><span class="pre">[^a-zA-Z0-9_]</span></code> if the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used.</p>
<p>If the <a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> flag is used,
matches characters which are neither alphanumeric in the current locale
nor the underscore.</p>
</dd>
</dl>
<dl class="simple" id="index-36">
<dt><code class="docutils literal notranslate"><span class="pre">\Z</span></code></dt><dd><p>Matches only at the end of the string.</p>
</dd>
</dl>
<p id="index-37">Most of the <a class="reference internal" href="../reference/lexical_analysis.html#escape-sequences"><span class="std std-ref">escape sequences</span></a> supported by Python
string literals are also accepted by the regular expression parser:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span>\<span class="n">a</span>      \<span class="n">b</span>      \<span class="n">f</span>      \<span class="n">n</span>
\<span class="n">N</span>      \<span class="n">r</span>      \<span class="n">t</span>      \<span class="n">u</span>
\<span class="n">U</span>      \<span class="n">v</span>      \<span class="n">x</span>      \\
</pre></div>
</div>
<p>(Note that <code class="docutils literal notranslate"><span class="pre">\b</span></code> is used to represent word boundaries, and means “backspace”
only inside character classes.)</p>
<p><code class="docutils literal notranslate"><span class="pre">'\u'</span></code>, <code class="docutils literal notranslate"><span class="pre">'\U'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'\N'</span></code> escape sequences are
only recognized in Unicode (str) patterns.
In bytes patterns they are errors.
Unknown escapes of ASCII letters are reserved
for future use and treated as errors.</p>
<p>Octal escapes are included in a limited form.  If the first digit is a 0, or if
there are three octal digits, it is considered an octal escape. Otherwise, it is
a group reference.  As for string literals, octal escapes are always at most
three digits in length.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <code class="docutils literal notranslate"><span class="pre">'\u'</span></code> and <code class="docutils literal notranslate"><span class="pre">'\U'</span></code> escape sequences have been added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Unknown escapes consisting of <code class="docutils literal notranslate"><span class="pre">'\'</span></code> and an ASCII letter now are errors.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <code class="samp docutils literal notranslate"><span class="pre">'\N{</span><em><span class="pre">name</span></em><span class="pre">}'</span></code> escape sequence has been added. As in string literals,
it expands to the named Unicode character (e.g. <code class="docutils literal notranslate"><span class="pre">'\N{EM</span> <span class="pre">DASH}'</span></code>).</p>
</div>
</section>
<section id="module-contents">
<span id="contents-of-module-re"></span><h2>Module Contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<p>The module defines several functions, constants, and an exception. Some of the
functions are simplified versions of the full featured methods for compiled
regular expressions.  Most non-trivial applications always use the compiled
form.</p>
<section id="flags">
<h3>Flags<a class="headerlink" href="#flags" title="Link to this heading">¶</a></h3>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Flag constants are now instances of <a class="reference internal" href="#re.RegexFlag" title="re.RegexFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">RegexFlag</span></code></a>, which is a subclass of
<a class="reference internal" href="enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntFlag</span></code></a>.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="re.RegexFlag">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">RegexFlag</span></span><a class="headerlink" href="#re.RegexFlag" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntFlag</span></code></a> class containing the regex options listed below.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>- added to <code class="docutils literal notranslate"><span class="pre">__all__</span></code></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.A">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">A</span></span><a class="headerlink" href="#re.A" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.ASCII">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">ASCII</span></span><a class="headerlink" href="#re.ASCII" title="Link to this definition">¶</a></dt>
<dd><p>Make <code class="docutils literal notranslate"><span class="pre">\w</span></code>, <code class="docutils literal notranslate"><span class="pre">\W</span></code>, <code class="docutils literal notranslate"><span class="pre">\b</span></code>, <code class="docutils literal notranslate"><span class="pre">\B</span></code>, <code class="docutils literal notranslate"><span class="pre">\d</span></code>, <code class="docutils literal notranslate"><span class="pre">\D</span></code>, <code class="docutils literal notranslate"><span class="pre">\s</span></code> and <code class="docutils literal notranslate"><span class="pre">\S</span></code>
perform ASCII-only matching instead of full Unicode matching.  This is only
meaningful for Unicode (str) patterns, and is ignored for bytes patterns.</p>
<p>Corresponds to the inline flag <code class="docutils literal notranslate"><span class="pre">(?a)</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#re.U" title="re.U"><code class="xref py py-const docutils literal notranslate"><span class="pre">U</span></code></a> flag still exists for backward compatibility,
but is redundant in Python 3 since
matches are Unicode by default for <code class="docutils literal notranslate"><span class="pre">str</span></code> patterns,
and Unicode matching isn’t allowed for bytes patterns.
<a class="reference internal" href="#re.UNICODE" title="re.UNICODE"><code class="xref py py-const docutils literal notranslate"><span class="pre">UNICODE</span></code></a> and the inline flag <code class="docutils literal notranslate"><span class="pre">(?u)</span></code> are similarly redundant.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.DEBUG">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">DEBUG</span></span><a class="headerlink" href="#re.DEBUG" title="Link to this definition">¶</a></dt>
<dd><p>Display debug information about compiled expression.</p>
<p>No corresponding inline flag.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.I">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">I</span></span><a class="headerlink" href="#re.I" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.IGNORECASE">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">IGNORECASE</span></span><a class="headerlink" href="#re.IGNORECASE" title="Link to this definition">¶</a></dt>
<dd><p>Perform case-insensitive matching;
expressions like <code class="docutils literal notranslate"><span class="pre">[A-Z]</span></code> will also  match lowercase letters.
Full Unicode matching (such as <code class="docutils literal notranslate"><span class="pre">Ü</span></code> matching <code class="docutils literal notranslate"><span class="pre">ü</span></code>)
also works unless the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag
is used to disable non-ASCII matches.
The current locale does not change the effect of this flag
unless the <a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> flag is also used.</p>
<p>Corresponds to the inline flag <code class="docutils literal notranslate"><span class="pre">(?i)</span></code>.</p>
<p>Note that when the Unicode patterns <code class="docutils literal notranslate"><span class="pre">[a-z]</span></code> or <code class="docutils literal notranslate"><span class="pre">[A-Z]</span></code> are used in
combination with the <a class="reference internal" href="#re.IGNORECASE" title="re.IGNORECASE"><code class="xref py py-const docutils literal notranslate"><span class="pre">IGNORECASE</span></code></a> flag, they will match the 52 ASCII
letters and 4 additional non-ASCII letters: ‘İ’ (U+0130, Latin capital
letter I with dot above), ‘ı’ (U+0131, Latin small letter dotless i),
‘ſ’ (U+017F, Latin small letter long s) and ‘K’ (U+212A, Kelvin sign).
If the <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> flag is used, only letters ‘a’ to ‘z’
and ‘A’ to ‘Z’ are matched.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.L">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">L</span></span><a class="headerlink" href="#re.L" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.LOCALE">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">LOCALE</span></span><a class="headerlink" href="#re.LOCALE" title="Link to this definition">¶</a></dt>
<dd><p>Make <code class="docutils literal notranslate"><span class="pre">\w</span></code>, <code class="docutils literal notranslate"><span class="pre">\W</span></code>, <code class="docutils literal notranslate"><span class="pre">\b</span></code>, <code class="docutils literal notranslate"><span class="pre">\B</span></code> and case-insensitive matching
dependent on the current locale.
This flag can be used only with bytes patterns.</p>
<p>Corresponds to the inline flag <code class="docutils literal notranslate"><span class="pre">(?L)</span></code>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This flag is discouraged; consider Unicode matching instead.
The locale mechanism is very unreliable
as it only handles one “culture” at a time
and only works with 8-bit locales.
Unicode matching is enabled by default for Unicode (str) patterns
and it is able to handle different locales and languages.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> can be used only with bytes patterns
and is not compatible with <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Compiled regular expression objects with the <a class="reference internal" href="#re.LOCALE" title="re.LOCALE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LOCALE</span></code></a> flag
no longer depend on the locale at compile time.
Only the locale at matching time affects the result of matching.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.M">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">M</span></span><a class="headerlink" href="#re.M" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.MULTILINE">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">MULTILINE</span></span><a class="headerlink" href="#re.MULTILINE" title="Link to this definition">¶</a></dt>
<dd><p>When specified, the pattern character <code class="docutils literal notranslate"><span class="pre">'^'</span></code> matches at the beginning of the
string and at the beginning of each line (immediately following each newline);
and the pattern character <code class="docutils literal notranslate"><span class="pre">'$'</span></code> matches at the end of the string and at the
end of each line (immediately preceding each newline).  By default, <code class="docutils literal notranslate"><span class="pre">'^'</span></code>
matches only at the beginning of the string, and <code class="docutils literal notranslate"><span class="pre">'$'</span></code> only at the end of the
string and immediately before the newline (if any) at the end of the string.</p>
<p>Corresponds to the inline flag <code class="docutils literal notranslate"><span class="pre">(?m)</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.NOFLAG">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">NOFLAG</span></span><a class="headerlink" href="#re.NOFLAG" title="Link to this definition">¶</a></dt>
<dd><p>Indicates no flag being applied, the value is <code class="docutils literal notranslate"><span class="pre">0</span></code>.  This flag may be used
as a default value for a function keyword argument or as a base value that
will be conditionally ORed with other flags.  Example of use as a default
value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">myfunc</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="n">flag</span><span class="o">=</span><span class="n">re</span><span class="o">.</span><span class="n">NOFLAG</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="n">flag</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.S">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">S</span></span><a class="headerlink" href="#re.S" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.DOTALL">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">DOTALL</span></span><a class="headerlink" href="#re.DOTALL" title="Link to this definition">¶</a></dt>
<dd><p>Make the <code class="docutils literal notranslate"><span class="pre">'.'</span></code> special character match any character at all, including a
newline; without this flag, <code class="docutils literal notranslate"><span class="pre">'.'</span></code> will match anything <em>except</em> a newline.</p>
<p>Corresponds to the inline flag <code class="docutils literal notranslate"><span class="pre">(?s)</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.U">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">U</span></span><a class="headerlink" href="#re.U" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.UNICODE">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">UNICODE</span></span><a class="headerlink" href="#re.UNICODE" title="Link to this definition">¶</a></dt>
<dd><p>In Python 3, Unicode characters are matched by default
for <code class="docutils literal notranslate"><span class="pre">str</span></code> patterns.
This flag is therefore redundant with <strong>no effect</strong>
and is only kept for backward compatibility.</p>
<p>See <a class="reference internal" href="#re.ASCII" title="re.ASCII"><code class="xref py py-const docutils literal notranslate"><span class="pre">ASCII</span></code></a> to restrict matching to ASCII characters instead.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="re.X">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">X</span></span><a class="headerlink" href="#re.X" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.VERBOSE">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">VERBOSE</span></span><a class="headerlink" href="#re.VERBOSE" title="Link to this definition">¶</a></dt>
<dd><p id="index-38">This flag allows you to write regular expressions that look nicer and are
more readable by allowing you to visually separate logical sections of the
pattern and add comments. Whitespace within the pattern is ignored, except
when in a character class, or when preceded by an unescaped backslash,
or within tokens like <code class="docutils literal notranslate"><span class="pre">*?</span></code>, <code class="docutils literal notranslate"><span class="pre">(?:</span></code> or <code class="docutils literal notranslate"><span class="pre">(?P&lt;...&gt;</span></code>. For example, <code class="docutils literal notranslate"><span class="pre">(?</span> <span class="pre">:</span></code>
and <code class="docutils literal notranslate"><span class="pre">*</span> <span class="pre">?</span></code> are not allowed.
When a line contains a <code class="docutils literal notranslate"><span class="pre">#</span></code> that is not in a character class and is not
preceded by an unescaped backslash, all characters from the leftmost such
<code class="docutils literal notranslate"><span class="pre">#</span></code> through the end of the line are ignored.</p>
<p>This means that the two following regular expression objects that match a
decimal number are functionally equal:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">a</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;&quot;&quot;\d +  # the integral part</span>
<span class="s2">                   \.    # the decimal point</span>
<span class="s2">                   \d *  # some fractional digits&quot;&quot;&quot;</span><span class="p">,</span> <span class="n">re</span><span class="o">.</span><span class="n">X</span><span class="p">)</span>
<span class="n">b</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;\d+\.\d*&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Corresponds to the inline flag <code class="docutils literal notranslate"><span class="pre">(?x)</span></code>.</p>
</dd></dl>

</section>
<section id="functions">
<h3>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="re.compile">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">compile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.compile" title="Link to this definition">¶</a></dt>
<dd><p>Compile a regular expression pattern into a <a class="reference internal" href="#re-objects"><span class="std std-ref">regular expression object</span></a>, which can be used for matching using its
<a class="reference internal" href="#re.Pattern.match" title="re.Pattern.match"><code class="xref py py-func docutils literal notranslate"><span class="pre">match()</span></code></a>, <a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">search()</span></code></a> and other methods, described
below.</p>
<p>The expression’s behaviour can be modified by specifying a <em>flags</em> value.
Values can be any of the <a class="reference internal" href="#flags">flags</a> variables, combined using bitwise OR
(the <code class="docutils literal notranslate"><span class="pre">|</span></code> operator).</p>
<p>The sequence</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">prog</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="n">pattern</span><span class="p">)</span>
<span class="n">result</span> <span class="o">=</span> <span class="n">prog</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="n">string</span><span class="p">)</span>
</pre></div>
</div>
<p>is equivalent to</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">result</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="n">pattern</span><span class="p">,</span> <span class="n">string</span><span class="p">)</span>
</pre></div>
</div>
<p>but using <a class="reference internal" href="#re.compile" title="re.compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.compile()</span></code></a> and saving the resulting regular expression
object for reuse is more efficient when the expression will be used several
times in a single program.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The compiled versions of the most recent patterns passed to
<a class="reference internal" href="#re.compile" title="re.compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.compile()</span></code></a> and the module-level matching functions are cached, so
programs that use only a few regular expressions at a time needn’t worry
about compiling regular expressions.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.search">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.search" title="Link to this definition">¶</a></dt>
<dd><p>Scan through <em>string</em> looking for the first location where the regular expression
<em>pattern</em> produces a match, and return a corresponding <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a>. Return
<code class="docutils literal notranslate"><span class="pre">None</span></code> if no position in the string matches the pattern; note that this is
different from finding a zero-length match at some point in the string.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.match">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">match</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.match" title="Link to this definition">¶</a></dt>
<dd><p>If zero or more characters at the beginning of <em>string</em> match the regular
expression <em>pattern</em>, return a corresponding <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a>.  Return
<code class="docutils literal notranslate"><span class="pre">None</span></code> if the string does not match the pattern; note that this is
different from a zero-length match.</p>
<p>Note that even in <a class="reference internal" href="#re.MULTILINE" title="re.MULTILINE"><code class="xref py py-const docutils literal notranslate"><span class="pre">MULTILINE</span></code></a> mode, <a class="reference internal" href="#re.match" title="re.match"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.match()</span></code></a> will only match
at the beginning of the string and not at the beginning of each line.</p>
<p>If you want to locate a match anywhere in <em>string</em>, use <a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">search()</span></code></a>
instead (see also <a class="reference internal" href="#search-vs-match"><span class="std std-ref">search() vs. match()</span></a>).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.fullmatch">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">fullmatch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.fullmatch" title="Link to this definition">¶</a></dt>
<dd><p>If the whole <em>string</em> matches the regular expression <em>pattern</em>, return a
corresponding <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a>.  Return <code class="docutils literal notranslate"><span class="pre">None</span></code> if the string does not match
the pattern; note that this is different from a zero-length match.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.split">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxsplit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.split" title="Link to this definition">¶</a></dt>
<dd><p>Split <em>string</em> by the occurrences of <em>pattern</em>.  If capturing parentheses are
used in <em>pattern</em>, then the text of all groups in the pattern are also returned
as part of the resulting list. If <em>maxsplit</em> is nonzero, at most <em>maxsplit</em>
splits occur, and the remainder of the string is returned as the final element
of the list.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;\W+&#39;</span><span class="p">,</span> <span class="s1">&#39;Words, words, words.&#39;</span><span class="p">)</span>
<span class="go">[&#39;Words&#39;, &#39;words&#39;, &#39;words&#39;, &#39;&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;(\W+)&#39;</span><span class="p">,</span> <span class="s1">&#39;Words, words, words.&#39;</span><span class="p">)</span>
<span class="go">[&#39;Words&#39;, &#39;, &#39;, &#39;words&#39;, &#39;, &#39;, &#39;words&#39;, &#39;.&#39;, &#39;&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;\W+&#39;</span><span class="p">,</span> <span class="s1">&#39;Words, words, words.&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="go">[&#39;Words&#39;, &#39;words, words.&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;[a-f]+&#39;</span><span class="p">,</span> <span class="s1">&#39;0a3B9&#39;</span><span class="p">,</span> <span class="n">flags</span><span class="o">=</span><span class="n">re</span><span class="o">.</span><span class="n">IGNORECASE</span><span class="p">)</span>
<span class="go">[&#39;0&#39;, &#39;3&#39;, &#39;9&#39;]</span>
</pre></div>
</div>
<p>If there are capturing groups in the separator and it matches at the start of
the string, the result will start with an empty string.  The same holds for
the end of the string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;(\W+)&#39;</span><span class="p">,</span> <span class="s1">&#39;...words, words...&#39;</span><span class="p">)</span>
<span class="go">[&#39;&#39;, &#39;...&#39;, &#39;words&#39;, &#39;, &#39;, &#39;words&#39;, &#39;...&#39;, &#39;&#39;]</span>
</pre></div>
</div>
<p>That way, separator components are always found at the same relative
indices within the result list.</p>
<p>Empty matches for the pattern split the string only when not adjacent
to a previous empty match.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;\b&#39;</span><span class="p">,</span> <span class="s1">&#39;Words, words, words.&#39;</span><span class="p">)</span>
<span class="go">[&#39;&#39;, &#39;Words&#39;, &#39;, &#39;, &#39;words&#39;, &#39;, &#39;, &#39;words&#39;, &#39;.&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;\W*&#39;</span><span class="p">,</span> <span class="s1">&#39;...words...&#39;</span><span class="p">)</span>
<span class="go">[&#39;&#39;, &#39;&#39;, &#39;w&#39;, &#39;o&#39;, &#39;r&#39;, &#39;d&#39;, &#39;s&#39;, &#39;&#39;, &#39;&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;(\W*)&#39;</span><span class="p">,</span> <span class="s1">&#39;...words...&#39;</span><span class="p">)</span>
<span class="go">[&#39;&#39;, &#39;...&#39;, &#39;&#39;, &#39;&#39;, &#39;w&#39;, &#39;&#39;, &#39;o&#39;, &#39;&#39;, &#39;r&#39;, &#39;&#39;, &#39;d&#39;, &#39;&#39;, &#39;s&#39;, &#39;...&#39;, &#39;&#39;, &#39;&#39;, &#39;&#39;]</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the optional flags argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added support of splitting on a pattern that could match an empty string.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.findall">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">findall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.findall" title="Link to this definition">¶</a></dt>
<dd><p>Return all non-overlapping matches of <em>pattern</em> in <em>string</em>, as a list of
strings or tuples.  The <em>string</em> is scanned left-to-right, and matches
are returned in the order found.  Empty matches are included in the result.</p>
<p>The result depends on the number of capturing groups in the pattern.
If there are no groups, return a list of strings matching the whole
pattern.  If there is exactly one group, return a list of strings
matching that group.  If multiple groups are present, return a list
of tuples of strings matching the groups.  Non-capturing groups do not
affect the form of the result.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;\bf[a-z]*&#39;</span><span class="p">,</span> <span class="s1">&#39;which foot or hand fell fastest&#39;</span><span class="p">)</span>
<span class="go">[&#39;foot&#39;, &#39;fell&#39;, &#39;fastest&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;(\w+)=(\d+)&#39;</span><span class="p">,</span> <span class="s1">&#39;set width=20 and height=10&#39;</span><span class="p">)</span>
<span class="go">[(&#39;width&#39;, &#39;20&#39;), (&#39;height&#39;, &#39;10&#39;)]</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Non-empty matches can now start just after a previous empty match.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.finditer">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">finditer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.finditer" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> yielding <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a> objects over
all non-overlapping matches for the RE <em>pattern</em> in <em>string</em>.  The <em>string</em>
is scanned left-to-right, and matches are returned in the order found.  Empty
matches are included in the result.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Non-empty matches can now start just after a previous empty match.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.sub">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">sub</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">repl</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.sub" title="Link to this definition">¶</a></dt>
<dd><p>Return the string obtained by replacing the leftmost non-overlapping occurrences
of <em>pattern</em> in <em>string</em> by the replacement <em>repl</em>.  If the pattern isn’t found,
<em>string</em> is returned unchanged.  <em>repl</em> can be a string or a function; if it is
a string, any backslash escapes in it are processed.  That is, <code class="docutils literal notranslate"><span class="pre">\n</span></code> is
converted to a single newline character, <code class="docutils literal notranslate"><span class="pre">\r</span></code> is converted to a carriage return, and
so forth.  Unknown escapes of ASCII letters are reserved for future use and
treated as errors.  Other unknown escapes such as <code class="docutils literal notranslate"><span class="pre">\&amp;</span></code> are left alone.
Backreferences, such
as <code class="docutils literal notranslate"><span class="pre">\6</span></code>, are replaced with the substring matched by group 6 in the pattern.
For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">sub</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;def\s+([a-zA-Z_][a-zA-Z_0-9]*)\s*\(\s*\):&#39;</span><span class="p">,</span>
<span class="gp">... </span>       <span class="sa">r</span><span class="s1">&#39;static PyObject*\npy_\1(void)\n{&#39;</span><span class="p">,</span>
<span class="gp">... </span>       <span class="s1">&#39;def myfunc():&#39;</span><span class="p">)</span>
<span class="go">&#39;static PyObject*\npy_myfunc(void)\n{&#39;</span>
</pre></div>
</div>
<p>If <em>repl</em> is a function, it is called for every non-overlapping occurrence of
<em>pattern</em>.  The function takes a single <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a> argument, and returns
the replacement string.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">dashrepl</span><span class="p">(</span><span class="n">matchobj</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">if</span> <span class="n">matchobj</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span> <span class="o">==</span> <span class="s1">&#39;-&#39;</span><span class="p">:</span> <span class="k">return</span> <span class="s1">&#39; &#39;</span>
<span class="gp">... </span>    <span class="k">else</span><span class="p">:</span> <span class="k">return</span> <span class="s1">&#39;-&#39;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">sub</span><span class="p">(</span><span class="s1">&#39;-{1,2}&#39;</span><span class="p">,</span> <span class="n">dashrepl</span><span class="p">,</span> <span class="s1">&#39;pro----gram-files&#39;</span><span class="p">)</span>
<span class="go">&#39;pro--gram files&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">sub</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;\sAND\s&#39;</span><span class="p">,</span> <span class="s1">&#39; &amp; &#39;</span><span class="p">,</span> <span class="s1">&#39;Baked Beans And Spam&#39;</span><span class="p">,</span> <span class="n">flags</span><span class="o">=</span><span class="n">re</span><span class="o">.</span><span class="n">IGNORECASE</span><span class="p">)</span>
<span class="go">&#39;Baked Beans &amp; Spam&#39;</span>
</pre></div>
</div>
<p>The pattern may be a string or a <a class="reference internal" href="#re.Pattern" title="re.Pattern"><code class="xref py py-class docutils literal notranslate"><span class="pre">Pattern</span></code></a>.</p>
<p>The optional argument <em>count</em> is the maximum number of pattern occurrences to be
replaced; <em>count</em> must be a non-negative integer.  If omitted or zero, all
occurrences will be replaced. Empty matches for the pattern are replaced only
when not adjacent to a previous empty match, so <code class="docutils literal notranslate"><span class="pre">sub('x*',</span> <span class="pre">'-',</span> <span class="pre">'abxd')</span></code> returns
<code class="docutils literal notranslate"><span class="pre">'-a-b--d-'</span></code>.</p>
<p id="index-39">In string-type <em>repl</em> arguments, in addition to the character escapes and
backreferences described above,
<code class="docutils literal notranslate"><span class="pre">\g&lt;name&gt;</span></code> will use the substring matched by the group named <code class="docutils literal notranslate"><span class="pre">name</span></code>, as
defined by the <code class="docutils literal notranslate"><span class="pre">(?P&lt;name&gt;...)</span></code> syntax. <code class="docutils literal notranslate"><span class="pre">\g&lt;number&gt;</span></code> uses the corresponding
group number; <code class="docutils literal notranslate"><span class="pre">\g&lt;2&gt;</span></code> is therefore equivalent to <code class="docutils literal notranslate"><span class="pre">\2</span></code>, but isn’t ambiguous
in a replacement such as <code class="docutils literal notranslate"><span class="pre">\g&lt;2&gt;0</span></code>.  <code class="docutils literal notranslate"><span class="pre">\20</span></code> would be interpreted as a
reference to group 20, not a reference to group 2 followed by the literal
character <code class="docutils literal notranslate"><span class="pre">'0'</span></code>.  The backreference <code class="docutils literal notranslate"><span class="pre">\g&lt;0&gt;</span></code> substitutes in the entire
substring matched by the RE.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the optional flags argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Unmatched groups are replaced with an empty string.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Unknown escapes in <em>pattern</em> consisting of <code class="docutils literal notranslate"><span class="pre">'\'</span></code> and an ASCII letter
now are errors.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Unknown escapes in <em>repl</em> consisting of <code class="docutils literal notranslate"><span class="pre">'\'</span></code> and an ASCII letter
now are errors.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Empty matches for the pattern are replaced when adjacent to a previous
non-empty match.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Group <em>id</em> can only contain ASCII digits.
In <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> replacement strings, group <em>name</em> can only contain bytes
in the ASCII range (<code class="docutils literal notranslate"><span class="pre">b'\x00'</span></code>-<code class="docutils literal notranslate"><span class="pre">b'\x7f'</span></code>).</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.subn">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">subn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">repl</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.subn" title="Link to this definition">¶</a></dt>
<dd><p>Perform the same operation as <a class="reference internal" href="#re.sub" title="re.sub"><code class="xref py py-func docutils literal notranslate"><span class="pre">sub()</span></code></a>, but return a tuple <code class="docutils literal notranslate"><span class="pre">(new_string,</span>
<span class="pre">number_of_subs_made)</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the optional flags argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Unmatched groups are replaced with an empty string.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.escape">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">escape</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.escape" title="Link to this definition">¶</a></dt>
<dd><p>Escape special characters in <em>pattern</em>.
This is useful if you want to match an arbitrary literal string that may
have regular expression metacharacters in it.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">re</span><span class="o">.</span><span class="n">escape</span><span class="p">(</span><span class="s1">&#39;https://www.python.org&#39;</span><span class="p">))</span>
<span class="go">https://www\.python\.org</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">legal_chars</span> <span class="o">=</span> <span class="n">string</span><span class="o">.</span><span class="n">ascii_lowercase</span> <span class="o">+</span> <span class="n">string</span><span class="o">.</span><span class="n">digits</span> <span class="o">+</span> <span class="s2">&quot;!#$%&amp;&#39;*+-.^_`|~:&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="s1">&#39;[</span><span class="si">%s</span><span class="s1">]+&#39;</span> <span class="o">%</span> <span class="n">re</span><span class="o">.</span><span class="n">escape</span><span class="p">(</span><span class="n">legal_chars</span><span class="p">))</span>
<span class="go">[abcdefghijklmnopqrstuvwxyz0123456789!\#\$%\&amp;&#39;\*\+\-\.\^_`\|\~:]+</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">operators</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;+&#39;</span><span class="p">,</span> <span class="s1">&#39;-&#39;</span><span class="p">,</span> <span class="s1">&#39;*&#39;</span><span class="p">,</span> <span class="s1">&#39;/&#39;</span><span class="p">,</span> <span class="s1">&#39;**&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="s1">&#39;|&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="n">re</span><span class="o">.</span><span class="n">escape</span><span class="p">,</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">operators</span><span class="p">,</span> <span class="n">reverse</span><span class="o">=</span><span class="kc">True</span><span class="p">))))</span>
<span class="go">/|\-|\+|\*\*|\*</span>
</pre></div>
</div>
<p>This function must not be used for the replacement string in <a class="reference internal" href="#re.sub" title="re.sub"><code class="xref py py-func docutils literal notranslate"><span class="pre">sub()</span></code></a>
and <a class="reference internal" href="#re.subn" title="re.subn"><code class="xref py py-func docutils literal notranslate"><span class="pre">subn()</span></code></a>, only backslashes should be escaped.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">digits_re</span> <span class="o">=</span> <span class="sa">r</span><span class="s1">&#39;\d+&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sample</span> <span class="o">=</span> <span class="s1">&#39;/usr/sbin/sendmail - 0 errors, 12 warnings&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">re</span><span class="o">.</span><span class="n">sub</span><span class="p">(</span><span class="n">digits_re</span><span class="p">,</span> <span class="n">digits_re</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\\</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">r</span><span class="s1">&#39;</span><span class="se">\\</span><span class="s1">&#39;</span><span class="p">),</span> <span class="n">sample</span><span class="p">))</span>
<span class="go">/usr/sbin/sendmail - \d+ errors, \d+ warnings</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <code class="docutils literal notranslate"><span class="pre">'_'</span></code> character is no longer escaped.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Only characters that can have special meaning in a regular expression
are escaped. As a result, <code class="docutils literal notranslate"><span class="pre">'!'</span></code>, <code class="docutils literal notranslate"><span class="pre">'&quot;'</span></code>, <code class="docutils literal notranslate"><span class="pre">'%'</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;'&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">','</span></code>,
<code class="docutils literal notranslate"><span class="pre">'/'</span></code>, <code class="docutils literal notranslate"><span class="pre">':'</span></code>, <code class="docutils literal notranslate"><span class="pre">';'</span></code>, <code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code>, <code class="docutils literal notranslate"><span class="pre">'='</span></code>, <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code>, <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code>, and
<code class="docutils literal notranslate"><span class="pre">&quot;`&quot;</span></code> are no longer escaped.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="re.purge">
<span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">purge</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#re.purge" title="Link to this definition">¶</a></dt>
<dd><p>Clear the regular expression cache.</p>
</dd></dl>

</section>
<section id="exceptions">
<h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<dl class="py exception">
<dt class="sig sig-object py" id="re.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.error" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when a string passed to one of the functions here is not a
valid regular expression (for example, it might contain unmatched parentheses)
or when some other error occurs during compilation or matching.  It is never an
error if a string contains no match for a pattern.  The error instance has
the following additional attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="re.error.msg">
<span class="sig-name descname"><span class="pre">msg</span></span><a class="headerlink" href="#re.error.msg" title="Link to this definition">¶</a></dt>
<dd><p>The unformatted error message.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.error.pattern">
<span class="sig-name descname"><span class="pre">pattern</span></span><a class="headerlink" href="#re.error.pattern" title="Link to this definition">¶</a></dt>
<dd><p>The regular expression pattern.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.error.pos">
<span class="sig-name descname"><span class="pre">pos</span></span><a class="headerlink" href="#re.error.pos" title="Link to this definition">¶</a></dt>
<dd><p>The index in <em>pattern</em> where compilation failed (may be <code class="docutils literal notranslate"><span class="pre">None</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.error.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#re.error.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line corresponding to <em>pos</em> (may be <code class="docutils literal notranslate"><span class="pre">None</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.error.colno">
<span class="sig-name descname"><span class="pre">colno</span></span><a class="headerlink" href="#re.error.colno" title="Link to this definition">¶</a></dt>
<dd><p>The column corresponding to <em>pos</em> (may be <code class="docutils literal notranslate"><span class="pre">None</span></code>).</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added additional attributes.</p>
</div>
</dd></dl>

</section>
</section>
<section id="regular-expression-objects">
<span id="re-objects"></span><h2>Regular Expression Objects<a class="headerlink" href="#regular-expression-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="re.Pattern">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">Pattern</span></span><a class="headerlink" href="#re.Pattern" title="Link to this definition">¶</a></dt>
<dd><p>Compiled regular expression object returned by <a class="reference internal" href="#re.compile" title="re.compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.compile()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span><a class="reference internal" href="#re.Pattern" title="re.Pattern"><code class="xref py py-class docutils literal notranslate"><span class="pre">re.Pattern</span></code></a> supports <code class="docutils literal notranslate"><span class="pre">[]</span></code> to indicate a Unicode (str) or bytes pattern.
See <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.search">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">endpos</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.search" title="Link to this definition">¶</a></dt>
<dd><p>Scan through <em>string</em> looking for the first location where this regular
expression produces a match, and return a corresponding <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a>.
Return <code class="docutils literal notranslate"><span class="pre">None</span></code> if no position in the string matches the pattern; note that
this is different from finding a zero-length match at some point in the string.</p>
<p>The optional second parameter <em>pos</em> gives an index in the string where the
search is to start; it defaults to <code class="docutils literal notranslate"><span class="pre">0</span></code>.  This is not completely equivalent to
slicing the string; the <code class="docutils literal notranslate"><span class="pre">'^'</span></code> pattern character matches at the real beginning
of the string and at positions just after a newline, but not necessarily at the
index where the search is to start.</p>
<p>The optional parameter <em>endpos</em> limits how far the string will be searched; it
will be as if the string is <em>endpos</em> characters long, so only the characters
from <em>pos</em> to <code class="docutils literal notranslate"><span class="pre">endpos</span> <span class="pre">-</span> <span class="pre">1</span></code> will be searched for a match.  If <em>endpos</em> is less
than <em>pos</em>, no match will be found; otherwise, if <em>rx</em> is a compiled regular
expression object, <code class="docutils literal notranslate"><span class="pre">rx.search(string,</span> <span class="pre">0,</span> <span class="pre">50)</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">rx.search(string[:50],</span> <span class="pre">0)</span></code>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="s2">&quot;d&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;dog&quot;</span><span class="p">)</span>     <span class="c1"># Match at index 0</span>
<span class="go">&lt;re.Match object; span=(0, 1), match=&#39;d&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;dog&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>  <span class="c1"># No match; search doesn&#39;t include the &quot;d&quot;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.match">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">match</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">endpos</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.match" title="Link to this definition">¶</a></dt>
<dd><p>If zero or more characters at the <em>beginning</em> of <em>string</em> match this regular
expression, return a corresponding <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a>. Return <code class="docutils literal notranslate"><span class="pre">None</span></code> if the
string does not match the pattern; note that this is different from a
zero-length match.</p>
<p>The optional <em>pos</em> and <em>endpos</em> parameters have the same meaning as for the
<a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> method.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="s2">&quot;o&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;dog&quot;</span><span class="p">)</span>      <span class="c1"># No match as &quot;o&quot; is not at the start of &quot;dog&quot;.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;dog&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>   <span class="c1"># Match as &quot;o&quot; is the 2nd character of &quot;dog&quot;.</span>
<span class="go">&lt;re.Match object; span=(1, 2), match=&#39;o&#39;&gt;</span>
</pre></div>
</div>
<p>If you want to locate a match anywhere in <em>string</em>, use
<a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> instead (see also <a class="reference internal" href="#search-vs-match"><span class="std std-ref">search() vs. match()</span></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.fullmatch">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">fullmatch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">endpos</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.fullmatch" title="Link to this definition">¶</a></dt>
<dd><p>If the whole <em>string</em> matches this regular expression, return a corresponding
<a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a>.  Return <code class="docutils literal notranslate"><span class="pre">None</span></code> if the string does not match the pattern;
note that this is different from a zero-length match.</p>
<p>The optional <em>pos</em> and <em>endpos</em> parameters have the same meaning as for the
<a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> method.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="s2">&quot;o[gh]&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">fullmatch</span><span class="p">(</span><span class="s2">&quot;dog&quot;</span><span class="p">)</span>      <span class="c1"># No match as &quot;o&quot; is not at the start of &quot;dog&quot;.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">fullmatch</span><span class="p">(</span><span class="s2">&quot;ogre&quot;</span><span class="p">)</span>     <span class="c1"># No match as not the full string matches.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span><span class="o">.</span><span class="n">fullmatch</span><span class="p">(</span><span class="s2">&quot;doggie&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>   <span class="c1"># Matches within given limits.</span>
<span class="go">&lt;re.Match object; span=(1, 3), match=&#39;og&#39;&gt;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.split">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxsplit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.split" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#re.split" title="re.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a> function, using the compiled pattern.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.findall">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">findall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">endpos</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.findall" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#re.findall" title="re.findall"><code class="xref py py-func docutils literal notranslate"><span class="pre">findall()</span></code></a> function, using the compiled pattern, but
also accepts optional <em>pos</em> and <em>endpos</em> parameters that limit the search
region like for <a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.finditer">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">finditer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">endpos</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.finditer" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#re.finditer" title="re.finditer"><code class="xref py py-func docutils literal notranslate"><span class="pre">finditer()</span></code></a> function, using the compiled pattern, but
also accepts optional <em>pos</em> and <em>endpos</em> parameters that limit the search
region like for <a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.sub">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">sub</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">repl</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.sub" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#re.sub" title="re.sub"><code class="xref py py-func docutils literal notranslate"><span class="pre">sub()</span></code></a> function, using the compiled pattern.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Pattern.subn">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">subn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">repl</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Pattern.subn" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#re.subn" title="re.subn"><code class="xref py py-func docutils literal notranslate"><span class="pre">subn()</span></code></a> function, using the compiled pattern.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Pattern.flags">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">flags</span></span><a class="headerlink" href="#re.Pattern.flags" title="Link to this definition">¶</a></dt>
<dd><p>The regex matching flags.  This is a combination of the flags given to
<a class="reference internal" href="#re.compile" title="re.compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>, any <code class="docutils literal notranslate"><span class="pre">(?...)</span></code> inline flags in the pattern, and implicit
flags such as <a class="reference internal" href="#re.UNICODE" title="re.UNICODE"><code class="xref py py-const docutils literal notranslate"><span class="pre">UNICODE</span></code></a> if the pattern is a Unicode string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Pattern.groups">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">groups</span></span><a class="headerlink" href="#re.Pattern.groups" title="Link to this definition">¶</a></dt>
<dd><p>The number of capturing groups in the pattern.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Pattern.groupindex">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">groupindex</span></span><a class="headerlink" href="#re.Pattern.groupindex" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping any symbolic group names defined by <code class="docutils literal notranslate"><span class="pre">(?P&lt;id&gt;)</span></code> to group
numbers.  The dictionary is empty if no symbolic groups were used in the
pattern.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Pattern.pattern">
<span class="sig-prename descclassname"><span class="pre">Pattern.</span></span><span class="sig-name descname"><span class="pre">pattern</span></span><a class="headerlink" href="#re.Pattern.pattern" title="Link to this definition">¶</a></dt>
<dd><p>The pattern string from which the pattern object was compiled.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added support of <a class="reference internal" href="copy.html#copy.copy" title="copy.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> and <a class="reference internal" href="copy.html#copy.deepcopy" title="copy.deepcopy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.deepcopy()</span></code></a>.  Compiled
regular expression objects are considered atomic.</p>
</div>
</section>
<section id="match-objects">
<span id="id3"></span><h2>Match Objects<a class="headerlink" href="#match-objects" title="Link to this heading">¶</a></h2>
<p>Match objects always have a boolean value of <code class="docutils literal notranslate"><span class="pre">True</span></code>.
Since <a class="reference internal" href="#re.Pattern.match" title="re.Pattern.match"><code class="xref py py-meth docutils literal notranslate"><span class="pre">match()</span></code></a> and <a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> return <code class="docutils literal notranslate"><span class="pre">None</span></code>
when there is no match, you can test whether there was a match with a simple
<code class="docutils literal notranslate"><span class="pre">if</span></code> statement:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">match</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">pattern</span><span class="p">,</span> <span class="n">string</span><span class="p">)</span>
<span class="k">if</span> <span class="n">match</span><span class="p">:</span>
    <span class="n">process</span><span class="p">(</span><span class="n">match</span><span class="p">)</span>
</pre></div>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="re.Match">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">re.</span></span><span class="sig-name descname"><span class="pre">Match</span></span><a class="headerlink" href="#re.Match" title="Link to this definition">¶</a></dt>
<dd><p>Match object returned by successful <code class="docutils literal notranslate"><span class="pre">match</span></code>es and <code class="docutils literal notranslate"><span class="pre">search</span></code>es.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span><a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">re.Match</span></code></a> supports <code class="docutils literal notranslate"><span class="pre">[]</span></code> to indicate a Unicode (str) or bytes match.
See <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.expand">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">expand</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">template</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.expand" title="Link to this definition">¶</a></dt>
<dd><p>Return the string obtained by doing backslash substitution on the template
string <em>template</em>, as done by the <a class="reference internal" href="#re.Pattern.sub" title="re.Pattern.sub"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sub()</span></code></a> method.
Escapes such as <code class="docutils literal notranslate"><span class="pre">\n</span></code> are converted to the appropriate characters,
and numeric backreferences (<code class="docutils literal notranslate"><span class="pre">\1</span></code>, <code class="docutils literal notranslate"><span class="pre">\2</span></code>) and named backreferences
(<code class="docutils literal notranslate"><span class="pre">\g&lt;1&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">\g&lt;name&gt;</span></code>) are replaced by the contents of the
corresponding group. The backreference <code class="docutils literal notranslate"><span class="pre">\g&lt;0&gt;</span></code> will be
replaced by the entire match.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Unmatched groups are replaced with an empty string.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.group">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">group</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">group1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.group" title="Link to this definition">¶</a></dt>
<dd><p>Returns one or more subgroups of the match.  If there is a single argument, the
result is a single string; if there are multiple arguments, the result is a
tuple with one item per argument. Without arguments, <em>group1</em> defaults to zero
(the whole match is returned). If a <em>groupN</em> argument is zero, the corresponding
return value is the entire matching string; if it is in the inclusive range
[1..99], it is the string matching the corresponding parenthesized group.  If a
group number is negative or larger than the number of groups defined in the
pattern, an <a class="reference internal" href="exceptions.html#IndexError" title="IndexError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexError</span></code></a> exception is raised. If a group is contained in a
part of the pattern that did not match, the corresponding result is <code class="docutils literal notranslate"><span class="pre">None</span></code>.
If a group is contained in a part of the pattern that matched multiple times,
the last match is returned.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(\w+) (\w+)&quot;</span><span class="p">,</span> <span class="s2">&quot;Isaac Newton, physicist&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>       <span class="c1"># The entire match</span>
<span class="go">&#39;Isaac Newton&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>       <span class="c1"># The first parenthesized subgroup.</span>
<span class="go">&#39;Isaac&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>       <span class="c1"># The second parenthesized subgroup.</span>
<span class="go">&#39;Newton&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>    <span class="c1"># Multiple arguments give us a tuple.</span>
<span class="go">(&#39;Isaac&#39;, &#39;Newton&#39;)</span>
</pre></div>
</div>
<p>If the regular expression uses the <code class="docutils literal notranslate"><span class="pre">(?P&lt;name&gt;...)</span></code> syntax, the <em>groupN</em>
arguments may also be strings identifying groups by their group name.  If a
string argument is not used as a group name in the pattern, an <a class="reference internal" href="exceptions.html#IndexError" title="IndexError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexError</span></code></a>
exception is raised.</p>
<p>A moderately complicated example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(?P&lt;first_name&gt;\w+) (?P&lt;last_name&gt;\w+)&quot;</span><span class="p">,</span> <span class="s2">&quot;Malcolm Reynolds&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="s1">&#39;first_name&#39;</span><span class="p">)</span>
<span class="go">&#39;Malcolm&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="s1">&#39;last_name&#39;</span><span class="p">)</span>
<span class="go">&#39;Reynolds&#39;</span>
</pre></div>
</div>
<p>Named groups can also be referred to by their index:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="go">&#39;Malcolm&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="go">&#39;Reynolds&#39;</span>
</pre></div>
</div>
<p>If a group matches multiple times, only the last match is accessible:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(..)+&quot;</span><span class="p">,</span> <span class="s2">&quot;a1b2c3&quot;</span><span class="p">)</span>  <span class="c1"># Matches 3 times.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>                        <span class="c1"># Returns only the last match.</span>
<span class="go">&#39;c3&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.__getitem__">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">__getitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">g</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.__getitem__" title="Link to this definition">¶</a></dt>
<dd><p>This is identical to <code class="docutils literal notranslate"><span class="pre">m.group(g)</span></code>.  This allows easier access to
an individual group from a match:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(\w+) (\w+)&quot;</span><span class="p">,</span> <span class="s2">&quot;Isaac Newton, physicist&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>       <span class="c1"># The entire match</span>
<span class="go">&#39;Isaac Newton&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>       <span class="c1"># The first parenthesized subgroup.</span>
<span class="go">&#39;Isaac&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>       <span class="c1"># The second parenthesized subgroup.</span>
<span class="go">&#39;Newton&#39;</span>
</pre></div>
</div>
<p>Named groups are supported as well:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(?P&lt;first_name&gt;\w+) (?P&lt;last_name&gt;\w+)&quot;</span><span class="p">,</span> <span class="s2">&quot;Isaac Newton&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="p">[</span><span class="s1">&#39;first_name&#39;</span><span class="p">]</span>
<span class="go">&#39;Isaac&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="p">[</span><span class="s1">&#39;last_name&#39;</span><span class="p">]</span>
<span class="go">&#39;Newton&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.groups">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">groups</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.groups" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing all the subgroups of the match, from 1 up to however
many groups are in the pattern.  The <em>default</em> argument is used for groups that
did not participate in the match; it defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(\d+)\.(\d+)&quot;</span><span class="p">,</span> <span class="s2">&quot;24.1632&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">groups</span><span class="p">()</span>
<span class="go">(&#39;24&#39;, &#39;1632&#39;)</span>
</pre></div>
</div>
<p>If we make the decimal place and everything after it optional, not all groups
might participate in the match.  These groups will default to <code class="docutils literal notranslate"><span class="pre">None</span></code> unless
the <em>default</em> argument is given:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(\d+)\.?(\d+)?&quot;</span><span class="p">,</span> <span class="s2">&quot;24&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">groups</span><span class="p">()</span>      <span class="c1"># Second group defaults to None.</span>
<span class="go">(&#39;24&#39;, None)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">groups</span><span class="p">(</span><span class="s1">&#39;0&#39;</span><span class="p">)</span>   <span class="c1"># Now, the second group defaults to &#39;0&#39;.</span>
<span class="go">(&#39;24&#39;, &#39;0&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.groupdict">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">groupdict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.groupdict" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary containing all the <em>named</em> subgroups of the match, keyed by
the subgroup name.  The <em>default</em> argument is used for groups that did not
participate in the match; it defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(?P&lt;first_name&gt;\w+) (?P&lt;last_name&gt;\w+)&quot;</span><span class="p">,</span> <span class="s2">&quot;Malcolm Reynolds&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">groupdict</span><span class="p">()</span>
<span class="go">{&#39;first_name&#39;: &#39;Malcolm&#39;, &#39;last_name&#39;: &#39;Reynolds&#39;}</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.start">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">start</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.start" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="re.Match.end">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">end</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.end" title="Link to this definition">¶</a></dt>
<dd><p>Return the indices of the start and end of the substring matched by <em>group</em>;
<em>group</em> defaults to zero (meaning the whole matched substring). Return <code class="docutils literal notranslate"><span class="pre">-1</span></code> if
<em>group</em> exists but did not contribute to the match.  For a match object <em>m</em>, and
a group <em>g</em> that did contribute to the match, the substring matched by group <em>g</em>
(equivalent to <code class="docutils literal notranslate"><span class="pre">m.group(g)</span></code>) is</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">m</span><span class="o">.</span><span class="n">string</span><span class="p">[</span><span class="n">m</span><span class="o">.</span><span class="n">start</span><span class="p">(</span><span class="n">g</span><span class="p">):</span><span class="n">m</span><span class="o">.</span><span class="n">end</span><span class="p">(</span><span class="n">g</span><span class="p">)]</span>
</pre></div>
</div>
<p>Note that <code class="docutils literal notranslate"><span class="pre">m.start(group)</span></code> will equal <code class="docutils literal notranslate"><span class="pre">m.end(group)</span></code> if <em>group</em> matched a
null string.  For example, after <code class="docutils literal notranslate"><span class="pre">m</span> <span class="pre">=</span> <span class="pre">re.search('b(c?)',</span> <span class="pre">'cba')</span></code>,
<code class="docutils literal notranslate"><span class="pre">m.start(0)</span></code> is 1, <code class="docutils literal notranslate"><span class="pre">m.end(0)</span></code> is 2, <code class="docutils literal notranslate"><span class="pre">m.start(1)</span></code> and <code class="docutils literal notranslate"><span class="pre">m.end(1)</span></code> are both
2, and <code class="docutils literal notranslate"><span class="pre">m.start(2)</span></code> raises an <a class="reference internal" href="exceptions.html#IndexError" title="IndexError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexError</span></code></a> exception.</p>
<p>An example that will remove <em>remove_this</em> from email addresses:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">email</span> <span class="o">=</span> <span class="s2">&quot;tony@tiremove_thisger.net&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;remove_this&quot;</span><span class="p">,</span> <span class="n">email</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">email</span><span class="p">[:</span><span class="n">m</span><span class="o">.</span><span class="n">start</span><span class="p">()]</span> <span class="o">+</span> <span class="n">email</span><span class="p">[</span><span class="n">m</span><span class="o">.</span><span class="n">end</span><span class="p">():]</span>
<span class="go">&#39;<EMAIL>&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="re.Match.span">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">span</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#re.Match.span" title="Link to this definition">¶</a></dt>
<dd><p>For a match <em>m</em>, return the 2-tuple <code class="docutils literal notranslate"><span class="pre">(m.start(group),</span> <span class="pre">m.end(group))</span></code>. Note
that if <em>group</em> did not contribute to the match, this is <code class="docutils literal notranslate"><span class="pre">(-1,</span> <span class="pre">-1)</span></code>.
<em>group</em> defaults to zero, the entire match.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Match.pos">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">pos</span></span><a class="headerlink" href="#re.Match.pos" title="Link to this definition">¶</a></dt>
<dd><p>The value of <em>pos</em> which was passed to the <a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> or
<a class="reference internal" href="#re.Pattern.match" title="re.Pattern.match"><code class="xref py py-meth docutils literal notranslate"><span class="pre">match()</span></code></a> method of a <a class="reference internal" href="#re-objects"><span class="std std-ref">regex object</span></a>.  This is
the index into the string at which the RE engine started looking for a match.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Match.endpos">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">endpos</span></span><a class="headerlink" href="#re.Match.endpos" title="Link to this definition">¶</a></dt>
<dd><p>The value of <em>endpos</em> which was passed to the <a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> or
<a class="reference internal" href="#re.Pattern.match" title="re.Pattern.match"><code class="xref py py-meth docutils literal notranslate"><span class="pre">match()</span></code></a> method of a <a class="reference internal" href="#re-objects"><span class="std std-ref">regex object</span></a>.  This is
the index into the string beyond which the RE engine will not go.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Match.lastindex">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">lastindex</span></span><a class="headerlink" href="#re.Match.lastindex" title="Link to this definition">¶</a></dt>
<dd><p>The integer index of the last matched capturing group, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no group
was matched at all. For example, the expressions <code class="docutils literal notranslate"><span class="pre">(a)b</span></code>, <code class="docutils literal notranslate"><span class="pre">((a)(b))</span></code>, and
<code class="docutils literal notranslate"><span class="pre">((ab))</span></code> will have <code class="docutils literal notranslate"><span class="pre">lastindex</span> <span class="pre">==</span> <span class="pre">1</span></code> if applied to the string <code class="docutils literal notranslate"><span class="pre">'ab'</span></code>, while
the expression <code class="docutils literal notranslate"><span class="pre">(a)(b)</span></code> will have <code class="docutils literal notranslate"><span class="pre">lastindex</span> <span class="pre">==</span> <span class="pre">2</span></code>, if applied to the same
string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Match.lastgroup">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">lastgroup</span></span><a class="headerlink" href="#re.Match.lastgroup" title="Link to this definition">¶</a></dt>
<dd><p>The name of the last matched capturing group, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the group didn’t
have a name, or if no group was matched at all.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Match.re">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">re</span></span><a class="headerlink" href="#re.Match.re" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#re-objects"><span class="std std-ref">regular expression object</span></a> whose <a class="reference internal" href="#re.Pattern.match" title="re.Pattern.match"><code class="xref py py-meth docutils literal notranslate"><span class="pre">match()</span></code></a> or
<a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a> method produced this match instance.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="re.Match.string">
<span class="sig-prename descclassname"><span class="pre">Match.</span></span><span class="sig-name descname"><span class="pre">string</span></span><a class="headerlink" href="#re.Match.string" title="Link to this definition">¶</a></dt>
<dd><p>The string passed to <a class="reference internal" href="#re.Pattern.match" title="re.Pattern.match"><code class="xref py py-meth docutils literal notranslate"><span class="pre">match()</span></code></a> or <a class="reference internal" href="#re.Pattern.search" title="re.Pattern.search"><code class="xref py py-meth docutils literal notranslate"><span class="pre">search()</span></code></a>.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added support of <a class="reference internal" href="copy.html#copy.copy" title="copy.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> and <a class="reference internal" href="copy.html#copy.deepcopy" title="copy.deepcopy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.deepcopy()</span></code></a>.  Match objects
are considered atomic.</p>
</div>
</section>
<section id="regular-expression-examples">
<span id="re-examples"></span><h2>Regular Expression Examples<a class="headerlink" href="#regular-expression-examples" title="Link to this heading">¶</a></h2>
<section id="checking-for-a-pair">
<h3>Checking for a Pair<a class="headerlink" href="#checking-for-a-pair" title="Link to this heading">¶</a></h3>
<p>In this example, we’ll use the following helper function to display match
objects a little more gracefully:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">displaymatch</span><span class="p">(</span><span class="n">match</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">match</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">None</span>
    <span class="k">return</span> <span class="s1">&#39;&lt;Match: </span><span class="si">%r</span><span class="s1">, groups=</span><span class="si">%r</span><span class="s1">&gt;&#39;</span> <span class="o">%</span> <span class="p">(</span><span class="n">match</span><span class="o">.</span><span class="n">group</span><span class="p">(),</span> <span class="n">match</span><span class="o">.</span><span class="n">groups</span><span class="p">())</span>
</pre></div>
</div>
<p>Suppose you are writing a poker program where a player’s hand is represented as
a 5-character string with each character representing a card, “a” for ace, “k”
for king, “q” for queen, “j” for jack, “t” for 10, and “2” through “9”
representing the card with that value.</p>
<p>To see if a given string is a valid hand, one could do the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">valid</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;^[a2-9tjqk]</span><span class="si">{5}</span><span class="s2">$&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">valid</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;akt5q&quot;</span><span class="p">))</span>  <span class="c1"># Valid.</span>
<span class="go">&quot;&lt;Match: &#39;akt5q&#39;, groups=()&gt;&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">valid</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;akt5e&quot;</span><span class="p">))</span>  <span class="c1"># Invalid.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">valid</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;akt&quot;</span><span class="p">))</span>    <span class="c1"># Invalid.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">valid</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;727ak&quot;</span><span class="p">))</span>  <span class="c1"># Valid.</span>
<span class="go">&quot;&lt;Match: &#39;727ak&#39;, groups=()&gt;&quot;</span>
</pre></div>
</div>
<p>That last hand, <code class="docutils literal notranslate"><span class="pre">&quot;727ak&quot;</span></code>, contained a pair, or two of the same valued cards.
To match this with a regular expression, one could use backreferences as such:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pair</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;.*(.).*\1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">pair</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;717ak&quot;</span><span class="p">))</span>     <span class="c1"># Pair of 7s.</span>
<span class="go">&quot;&lt;Match: &#39;717&#39;, groups=(&#39;7&#39;,)&gt;&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">pair</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;718ak&quot;</span><span class="p">))</span>     <span class="c1"># No pairs.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">displaymatch</span><span class="p">(</span><span class="n">pair</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;354aa&quot;</span><span class="p">))</span>     <span class="c1"># Pair of aces.</span>
<span class="go">&quot;&lt;Match: &#39;354aa&#39;, groups=(&#39;a&#39;,)&gt;&quot;</span>
</pre></div>
</div>
<p>To find out what card the pair consists of, one could use the
<a class="reference internal" href="#re.Match.group" title="re.Match.group"><code class="xref py py-meth docutils literal notranslate"><span class="pre">group()</span></code></a> method of the match object in the following manner:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pair</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;.*(.).*\1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pair</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;717ak&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="go">&#39;7&#39;</span>

<span class="go"># Error because re.match() returns None, which doesn&#39;t have a group() method:</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pair</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;718ak&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;pyshell#23&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="w">    </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;.*(.).*\1&quot;</span><span class="p">,</span> <span class="s2">&quot;718ak&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="gr">AttributeError</span>: <span class="n">&#39;NoneType&#39; object has no attribute &#39;group&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">pair</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;354aa&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="go">&#39;a&#39;</span>
</pre></div>
</div>
</section>
<section id="simulating-scanf">
<h3>Simulating scanf()<a class="headerlink" href="#simulating-scanf" title="Link to this heading">¶</a></h3>
<p id="index-40">Python does not currently have an equivalent to <code class="xref c c-func docutils literal notranslate"><span class="pre">scanf()</span></code>.  Regular
expressions are generally more powerful, though also more verbose, than
<code class="xref c c-func docutils literal notranslate"><span class="pre">scanf()</span></code> format strings.  The table below offers some more-or-less
equivalent mappings between <code class="xref c c-func docutils literal notranslate"><span class="pre">scanf()</span></code> format tokens and regular
expressions.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p><code class="xref c c-func docutils literal notranslate"><span class="pre">scanf()</span></code> Token</p></th>
<th class="head"><p>Regular Expression</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%c</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">.</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%5c</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">.{5}</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%d</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[-+]?\d+</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%e</span></code>, <code class="docutils literal notranslate"><span class="pre">%E</span></code>, <code class="docutils literal notranslate"><span class="pre">%f</span></code>, <code class="docutils literal notranslate"><span class="pre">%g</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[-+]?(\d+(\.\d*)?|\.\d+)([eE][-+]?\d+)?</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%i</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[-+]?(0[xX][\dA-Fa-f]+|0[0-7]*|\d+)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%o</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[-+]?[0-7]+</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%s</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">\S+</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%u</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">\d+</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%x</span></code>, <code class="docutils literal notranslate"><span class="pre">%X</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[-+]?(0[xX])?[\dA-Fa-f]+</span></code></p></td>
</tr>
</tbody>
</table>
<p>To extract the filename and numbers from a string like</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">sbin</span><span class="o">/</span><span class="n">sendmail</span> <span class="o">-</span> <span class="mi">0</span> <span class="n">errors</span><span class="p">,</span> <span class="mi">4</span> <span class="n">warnings</span>
</pre></div>
</div>
<p>you would use a <code class="xref c c-func docutils literal notranslate"><span class="pre">scanf()</span></code> format like</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">%</span><span class="n">s</span> <span class="o">-</span> <span class="o">%</span><span class="n">d</span> <span class="n">errors</span><span class="p">,</span> <span class="o">%</span><span class="n">d</span> <span class="n">warnings</span>
</pre></div>
</div>
<p>The equivalent regular expression would be</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span>\<span class="n">S</span><span class="o">+</span><span class="p">)</span> <span class="o">-</span> <span class="p">(</span>\<span class="n">d</span><span class="o">+</span><span class="p">)</span> <span class="n">errors</span><span class="p">,</span> <span class="p">(</span>\<span class="n">d</span><span class="o">+</span><span class="p">)</span> <span class="n">warnings</span>
</pre></div>
</div>
</section>
<section id="search-vs-match">
<span id="id4"></span><h3>search() vs. match()<a class="headerlink" href="#search-vs-match" title="Link to this heading">¶</a></h3>
<p>Python offers different primitive operations based on regular expressions:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#re.match" title="re.match"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.match()</span></code></a> checks for a match only at the beginning of the string</p></li>
<li><p><a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.search()</span></code></a> checks for a match anywhere in the string
(this is what Perl does by default)</p></li>
<li><p><a class="reference internal" href="#re.fullmatch" title="re.fullmatch"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.fullmatch()</span></code></a> checks for entire string to be a match</p></li>
</ul>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;c&quot;</span><span class="p">,</span> <span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>    <span class="c1"># No match</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;c&quot;</span><span class="p">,</span> <span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>   <span class="c1"># Match</span>
<span class="go">&lt;re.Match object; span=(2, 3), match=&#39;c&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">fullmatch</span><span class="p">(</span><span class="s2">&quot;p.*n&quot;</span><span class="p">,</span> <span class="s2">&quot;python&quot;</span><span class="p">)</span> <span class="c1"># Match</span>
<span class="go">&lt;re.Match object; span=(0, 6), match=&#39;python&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">fullmatch</span><span class="p">(</span><span class="s2">&quot;r.*n&quot;</span><span class="p">,</span> <span class="s2">&quot;python&quot;</span><span class="p">)</span> <span class="c1"># No match</span>
</pre></div>
</div>
<p>Regular expressions beginning with <code class="docutils literal notranslate"><span class="pre">'^'</span></code> can be used with <a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">search()</span></code></a> to
restrict the match at the beginning of the string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;c&quot;</span><span class="p">,</span> <span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>    <span class="c1"># No match</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;^c&quot;</span><span class="p">,</span> <span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>  <span class="c1"># No match</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;^a&quot;</span><span class="p">,</span> <span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>  <span class="c1"># Match</span>
<span class="go">&lt;re.Match object; span=(0, 1), match=&#39;a&#39;&gt;</span>
</pre></div>
</div>
<p>Note however that in <a class="reference internal" href="#re.MULTILINE" title="re.MULTILINE"><code class="xref py py-const docutils literal notranslate"><span class="pre">MULTILINE</span></code></a> mode <a class="reference internal" href="#re.match" title="re.match"><code class="xref py py-func docutils literal notranslate"><span class="pre">match()</span></code></a> only matches at the
beginning of the string, whereas using <a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">search()</span></code></a> with a regular expression
beginning with <code class="docutils literal notranslate"><span class="pre">'^'</span></code> will match at the beginning of each line.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;X&quot;</span><span class="p">,</span> <span class="s2">&quot;A</span><span class="se">\n</span><span class="s2">B</span><span class="se">\n</span><span class="s2">X&quot;</span><span class="p">,</span> <span class="n">re</span><span class="o">.</span><span class="n">MULTILINE</span><span class="p">)</span>  <span class="c1"># No match</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;^X&quot;</span><span class="p">,</span> <span class="s2">&quot;A</span><span class="se">\n</span><span class="s2">B</span><span class="se">\n</span><span class="s2">X&quot;</span><span class="p">,</span> <span class="n">re</span><span class="o">.</span><span class="n">MULTILINE</span><span class="p">)</span>  <span class="c1"># Match</span>
<span class="go">&lt;re.Match object; span=(4, 5), match=&#39;X&#39;&gt;</span>
</pre></div>
</div>
</section>
<section id="making-a-phonebook">
<h3>Making a Phonebook<a class="headerlink" href="#making-a-phonebook" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#re.split" title="re.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a> splits a string into a list delimited by the passed pattern.  The
method is invaluable for converting textual data into data structures that can be
easily read and modified by Python as demonstrated in the following example that
creates a phonebook.</p>
<p>First, here is the input.  Normally it may come from a file, here we are using
triple-quoted string syntax</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">text</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;Ross McFluff: ************ 155 Elm Street</span>
<span class="gp">...</span>
<span class="gp">... </span><span class="s2">Ronald Heathmore: ************ 436 Finley Avenue</span>
<span class="gp">... </span><span class="s2">Frank Burger: ************ 662 South Dogwood Way</span>
<span class="gp">...</span>
<span class="gp">...</span>
<span class="gp">... </span><span class="s2">Heather Albrecht: ************ 919 Park Place&quot;&quot;&quot;</span>
</pre></div>
</div>
<p>The entries are separated by one or more newlines. Now we convert the string
into a list with each nonempty line having its own entry:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">entries</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">+&quot;</span><span class="p">,</span> <span class="n">text</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">entries</span>
<span class="go">[&#39;Ross McFluff: ************ 155 Elm Street&#39;,</span>
<span class="go">&#39;Ronald Heathmore: ************ 436 Finley Avenue&#39;,</span>
<span class="go">&#39;Frank Burger: ************ 662 South Dogwood Way&#39;,</span>
<span class="go">&#39;Heather Albrecht: ************ 919 Park Place&#39;]</span>
</pre></div>
</div>
<p>Finally, split each entry into a list with first name, last name, telephone
number, and address.  We use the <code class="docutils literal notranslate"><span class="pre">maxsplit</span></code> parameter of <a class="reference internal" href="#re.split" title="re.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a>
because the address has spaces, our splitting pattern, in it:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;:? &quot;</span><span class="p">,</span> <span class="n">entry</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span> <span class="k">for</span> <span class="n">entry</span> <span class="ow">in</span> <span class="n">entries</span><span class="p">]</span>
<span class="go">[[&#39;Ross&#39;, &#39;McFluff&#39;, &#39;************&#39;, &#39;155 Elm Street&#39;],</span>
<span class="go">[&#39;Ronald&#39;, &#39;Heathmore&#39;, &#39;************&#39;, &#39;436 Finley Avenue&#39;],</span>
<span class="go">[&#39;Frank&#39;, &#39;Burger&#39;, &#39;************&#39;, &#39;662 South Dogwood Way&#39;],</span>
<span class="go">[&#39;Heather&#39;, &#39;Albrecht&#39;, &#39;************&#39;, &#39;919 Park Place&#39;]]</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">:?</span></code> pattern matches the colon after the last name, so that it does not
occur in the result list.  With a <code class="docutils literal notranslate"><span class="pre">maxsplit</span></code> of <code class="docutils literal notranslate"><span class="pre">4</span></code>, we could separate the
house number from the street name:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">re</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;:? &quot;</span><span class="p">,</span> <span class="n">entry</span><span class="p">,</span> <span class="mi">4</span><span class="p">)</span> <span class="k">for</span> <span class="n">entry</span> <span class="ow">in</span> <span class="n">entries</span><span class="p">]</span>
<span class="go">[[&#39;Ross&#39;, &#39;McFluff&#39;, &#39;************&#39;, &#39;155&#39;, &#39;Elm Street&#39;],</span>
<span class="go">[&#39;Ronald&#39;, &#39;Heathmore&#39;, &#39;************&#39;, &#39;436&#39;, &#39;Finley Avenue&#39;],</span>
<span class="go">[&#39;Frank&#39;, &#39;Burger&#39;, &#39;************&#39;, &#39;662&#39;, &#39;South Dogwood Way&#39;],</span>
<span class="go">[&#39;Heather&#39;, &#39;Albrecht&#39;, &#39;************&#39;, &#39;919&#39;, &#39;Park Place&#39;]]</span>
</pre></div>
</div>
</section>
<section id="text-munging">
<h3>Text Munging<a class="headerlink" href="#text-munging" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#re.sub" title="re.sub"><code class="xref py py-func docutils literal notranslate"><span class="pre">sub()</span></code></a> replaces every occurrence of a pattern with a string or the
result of a function.  This example demonstrates using <a class="reference internal" href="#re.sub" title="re.sub"><code class="xref py py-func docutils literal notranslate"><span class="pre">sub()</span></code></a> with
a function to “munge” text, or randomize the order of all the characters
in each word of a sentence except for the first and last characters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">repl</span><span class="p">(</span><span class="n">m</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">inner_word</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">2</span><span class="p">))</span>
<span class="gp">... </span>    <span class="n">random</span><span class="o">.</span><span class="n">shuffle</span><span class="p">(</span><span class="n">inner_word</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span> <span class="o">+</span> <span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">inner_word</span><span class="p">)</span> <span class="o">+</span> <span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">text</span> <span class="o">=</span> <span class="s2">&quot;Professor Abdolmalek, please report your absences promptly.&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">sub</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(\w)(\w+)(\w)&quot;</span><span class="p">,</span> <span class="n">repl</span><span class="p">,</span> <span class="n">text</span><span class="p">)</span>
<span class="go">&#39;Poefsrosr Aealmlobdk, pslaee reorpt your abnseces plmrptoy.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">sub</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;(\w)(\w+)(\w)&quot;</span><span class="p">,</span> <span class="n">repl</span><span class="p">,</span> <span class="n">text</span><span class="p">)</span>
<span class="go">&#39;Pofsroser Aodlambelk, plasee reoprt yuor asnebces potlmrpy.&#39;</span>
</pre></div>
</div>
</section>
<section id="finding-all-adverbs">
<h3>Finding all Adverbs<a class="headerlink" href="#finding-all-adverbs" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#re.findall" title="re.findall"><code class="xref py py-func docutils literal notranslate"><span class="pre">findall()</span></code></a> matches <em>all</em> occurrences of a pattern, not just the first
one as <a class="reference internal" href="#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">search()</span></code></a> does.  For example, if a writer wanted to
find all of the adverbs in some text, they might use <a class="reference internal" href="#re.findall" title="re.findall"><code class="xref py py-func docutils literal notranslate"><span class="pre">findall()</span></code></a> in
the following manner:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">text</span> <span class="o">=</span> <span class="s2">&quot;He was carefully disguised but captured quickly by police.&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;\w+ly\b&quot;</span><span class="p">,</span> <span class="n">text</span><span class="p">)</span>
<span class="go">[&#39;carefully&#39;, &#39;quickly&#39;]</span>
</pre></div>
</div>
</section>
<section id="finding-all-adverbs-and-their-positions">
<h3>Finding all Adverbs and their Positions<a class="headerlink" href="#finding-all-adverbs-and-their-positions" title="Link to this heading">¶</a></h3>
<p>If one wants more information about all matches of a pattern than the matched
text, <a class="reference internal" href="#re.finditer" title="re.finditer"><code class="xref py py-func docutils literal notranslate"><span class="pre">finditer()</span></code></a> is useful as it provides <a class="reference internal" href="#re.Match" title="re.Match"><code class="xref py py-class docutils literal notranslate"><span class="pre">Match</span></code></a> objects
instead of strings.  Continuing with the previous example, if a writer wanted
to find all of the adverbs <em>and their positions</em> in some text, they would use
<a class="reference internal" href="#re.finditer" title="re.finditer"><code class="xref py py-func docutils literal notranslate"><span class="pre">finditer()</span></code></a> in the following manner:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">text</span> <span class="o">=</span> <span class="s2">&quot;He was carefully disguised but captured quickly by police.&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">re</span><span class="o">.</span><span class="n">finditer</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;\w+ly\b&quot;</span><span class="p">,</span> <span class="n">text</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;</span><span class="si">%02d</span><span class="s1">-</span><span class="si">%02d</span><span class="s1">: </span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">start</span><span class="p">(),</span> <span class="n">m</span><span class="o">.</span><span class="n">end</span><span class="p">(),</span> <span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">0</span><span class="p">)))</span>
<span class="go">07-16: carefully</span>
<span class="go">40-47: quickly</span>
</pre></div>
</div>
</section>
<section id="raw-string-notation">
<h3>Raw String Notation<a class="headerlink" href="#raw-string-notation" title="Link to this heading">¶</a></h3>
<p>Raw string notation (<code class="docutils literal notranslate"><span class="pre">r&quot;text&quot;</span></code>) keeps regular expressions sane.  Without it,
every backslash (<code class="docutils literal notranslate"><span class="pre">'\'</span></code>) in a regular expression would have to be prefixed with
another one to escape it.  For example, the two following lines of code are
functionally identical:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;\W(.)\1\W&quot;</span><span class="p">,</span> <span class="s2">&quot; ff &quot;</span><span class="p">)</span>
<span class="go">&lt;re.Match object; span=(0, 4), match=&#39; ff &#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\\</span><span class="s2">W(.)</span><span class="se">\\</span><span class="s2">1</span><span class="se">\\</span><span class="s2">W&quot;</span><span class="p">,</span> <span class="s2">&quot; ff &quot;</span><span class="p">)</span>
<span class="go">&lt;re.Match object; span=(0, 4), match=&#39; ff &#39;&gt;</span>
</pre></div>
</div>
<p>When one wants to match a literal backslash, it must be escaped in the regular
expression.  With raw string notation, this means <code class="docutils literal notranslate"><span class="pre">r&quot;\\&quot;</span></code>.  Without raw string
notation, one must use <code class="docutils literal notranslate"><span class="pre">&quot;\\\\&quot;</span></code>, making the following lines of code
functionally identical:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="sa">r</span><span class="s2">&quot;</span><span class="se">\\</span><span class="s2">&quot;</span><span class="p">,</span> <span class="sa">r</span><span class="s2">&quot;</span><span class="se">\\</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">&lt;re.Match object; span=(0, 1), match=&#39;\\&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">re</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\\\\</span><span class="s2">&quot;</span><span class="p">,</span> <span class="sa">r</span><span class="s2">&quot;</span><span class="se">\\</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">&lt;re.Match object; span=(0, 1), match=&#39;\\&#39;&gt;</span>
</pre></div>
</div>
</section>
<section id="writing-a-tokenizer">
<h3>Writing a Tokenizer<a class="headerlink" href="#writing-a-tokenizer" title="Link to this heading">¶</a></h3>
<p>A <a class="reference external" href="https://en.wikipedia.org/wiki/Lexical_analysis">tokenizer or scanner</a>
analyzes a string to categorize groups of characters.  This is a useful first
step in writing a compiler or interpreter.</p>
<p>The text categories are specified with regular expressions.  The technique is
to combine those into a single master regular expression and to loop over
successive matches:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">NamedTuple</span>
<span class="kn">import</span> <span class="nn">re</span>

<span class="k">class</span> <span class="nc">Token</span><span class="p">(</span><span class="n">NamedTuple</span><span class="p">):</span>
    <span class="nb">type</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">line</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">column</span><span class="p">:</span> <span class="nb">int</span>

<span class="k">def</span> <span class="nf">tokenize</span><span class="p">(</span><span class="n">code</span><span class="p">):</span>
    <span class="n">keywords</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;IF&#39;</span><span class="p">,</span> <span class="s1">&#39;THEN&#39;</span><span class="p">,</span> <span class="s1">&#39;ENDIF&#39;</span><span class="p">,</span> <span class="s1">&#39;FOR&#39;</span><span class="p">,</span> <span class="s1">&#39;NEXT&#39;</span><span class="p">,</span> <span class="s1">&#39;GOSUB&#39;</span><span class="p">,</span> <span class="s1">&#39;RETURN&#39;</span><span class="p">}</span>
    <span class="n">token_specification</span> <span class="o">=</span> <span class="p">[</span>
        <span class="p">(</span><span class="s1">&#39;NUMBER&#39;</span><span class="p">,</span>   <span class="sa">r</span><span class="s1">&#39;\d+(\.\d*)?&#39;</span><span class="p">),</span>  <span class="c1"># Integer or decimal number</span>
        <span class="p">(</span><span class="s1">&#39;ASSIGN&#39;</span><span class="p">,</span>   <span class="sa">r</span><span class="s1">&#39;:=&#39;</span><span class="p">),</span>           <span class="c1"># Assignment operator</span>
        <span class="p">(</span><span class="s1">&#39;END&#39;</span><span class="p">,</span>      <span class="sa">r</span><span class="s1">&#39;;&#39;</span><span class="p">),</span>            <span class="c1"># Statement terminator</span>
        <span class="p">(</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span>       <span class="sa">r</span><span class="s1">&#39;[A-Za-z]+&#39;</span><span class="p">),</span>    <span class="c1"># Identifiers</span>
        <span class="p">(</span><span class="s1">&#39;OP&#39;</span><span class="p">,</span>       <span class="sa">r</span><span class="s1">&#39;[+\-*/]&#39;</span><span class="p">),</span>      <span class="c1"># Arithmetic operators</span>
        <span class="p">(</span><span class="s1">&#39;NEWLINE&#39;</span><span class="p">,</span>  <span class="sa">r</span><span class="s1">&#39;\n&#39;</span><span class="p">),</span>           <span class="c1"># Line endings</span>
        <span class="p">(</span><span class="s1">&#39;SKIP&#39;</span><span class="p">,</span>     <span class="sa">r</span><span class="s1">&#39;[ \t]+&#39;</span><span class="p">),</span>       <span class="c1"># Skip over spaces and tabs</span>
        <span class="p">(</span><span class="s1">&#39;MISMATCH&#39;</span><span class="p">,</span> <span class="sa">r</span><span class="s1">&#39;.&#39;</span><span class="p">),</span>            <span class="c1"># Any other character</span>
    <span class="p">]</span>
    <span class="n">tok_regex</span> <span class="o">=</span> <span class="s1">&#39;|&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="s1">&#39;(?P&lt;</span><span class="si">%s</span><span class="s1">&gt;</span><span class="si">%s</span><span class="s1">)&#39;</span> <span class="o">%</span> <span class="n">pair</span> <span class="k">for</span> <span class="n">pair</span> <span class="ow">in</span> <span class="n">token_specification</span><span class="p">)</span>
    <span class="n">line_num</span> <span class="o">=</span> <span class="mi">1</span>
    <span class="n">line_start</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="k">for</span> <span class="n">mo</span> <span class="ow">in</span> <span class="n">re</span><span class="o">.</span><span class="n">finditer</span><span class="p">(</span><span class="n">tok_regex</span><span class="p">,</span> <span class="n">code</span><span class="p">):</span>
        <span class="n">kind</span> <span class="o">=</span> <span class="n">mo</span><span class="o">.</span><span class="n">lastgroup</span>
        <span class="n">value</span> <span class="o">=</span> <span class="n">mo</span><span class="o">.</span><span class="n">group</span><span class="p">()</span>
        <span class="n">column</span> <span class="o">=</span> <span class="n">mo</span><span class="o">.</span><span class="n">start</span><span class="p">()</span> <span class="o">-</span> <span class="n">line_start</span>
        <span class="k">if</span> <span class="n">kind</span> <span class="o">==</span> <span class="s1">&#39;NUMBER&#39;</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="nb">float</span><span class="p">(</span><span class="n">value</span><span class="p">)</span> <span class="k">if</span> <span class="s1">&#39;.&#39;</span> <span class="ow">in</span> <span class="n">value</span> <span class="k">else</span> <span class="nb">int</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">kind</span> <span class="o">==</span> <span class="s1">&#39;ID&#39;</span> <span class="ow">and</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">keywords</span><span class="p">:</span>
            <span class="n">kind</span> <span class="o">=</span> <span class="n">value</span>
        <span class="k">elif</span> <span class="n">kind</span> <span class="o">==</span> <span class="s1">&#39;NEWLINE&#39;</span><span class="p">:</span>
            <span class="n">line_start</span> <span class="o">=</span> <span class="n">mo</span><span class="o">.</span><span class="n">end</span><span class="p">()</span>
            <span class="n">line_num</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">continue</span>
        <span class="k">elif</span> <span class="n">kind</span> <span class="o">==</span> <span class="s1">&#39;SKIP&#39;</span><span class="p">:</span>
            <span class="k">continue</span>
        <span class="k">elif</span> <span class="n">kind</span> <span class="o">==</span> <span class="s1">&#39;MISMATCH&#39;</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">value</span><span class="si">!r}</span><span class="s1"> unexpected on line </span><span class="si">{</span><span class="n">line_num</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
        <span class="k">yield</span> <span class="n">Token</span><span class="p">(</span><span class="n">kind</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">line_num</span><span class="p">,</span> <span class="n">column</span><span class="p">)</span>

<span class="n">statements</span> <span class="o">=</span> <span class="s1">&#39;&#39;&#39;</span>
<span class="s1">    IF quantity THEN</span>
<span class="s1">        total := total + price * quantity;</span>
<span class="s1">        tax := price * 0.05;</span>
<span class="s1">    ENDIF;</span>
<span class="s1">&#39;&#39;&#39;</span>

<span class="k">for</span> <span class="n">token</span> <span class="ow">in</span> <span class="n">tokenize</span><span class="p">(</span><span class="n">statements</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">token</span><span class="p">)</span>
</pre></div>
</div>
<p>The tokenizer produces the following output:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;IF&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;IF&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;quantity&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">7</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;THEN&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;THEN&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">16</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;total&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">8</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ASSIGN&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;:=&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">14</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;total&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">17</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;OP&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;+&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">23</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;price&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">25</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;OP&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;*&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">31</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;quantity&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">33</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;END&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;;&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">41</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;tax&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">8</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ASSIGN&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;:=&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">12</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ID&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;price&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">15</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;OP&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;*&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">21</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;NUMBER&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="mf">0.05</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">23</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;END&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;;&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">27</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ENDIF&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;ENDIF&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
<span class="n">Token</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;END&#39;</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="s1">&#39;;&#39;</span><span class="p">,</span> <span class="n">line</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">column</span><span class="o">=</span><span class="mi">9</span><span class="p">)</span>
</pre></div>
</div>
<div role="list" class="citation-list">
<div class="citation" id="frie09" role="doc-biblioentry">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">Frie09</a><span class="fn-bracket">]</span></span>
<p>Friedl, Jeffrey. Mastering Regular Expressions. 3rd ed., O’Reilly
Media, 2009. The third edition of the book no longer covers Python at all,
but the first edition covered writing good regular expression patterns in
great detail.</p>
</div>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code> — Regular expression operations</a><ul>
<li><a class="reference internal" href="#regular-expression-syntax">Regular Expression Syntax</a></li>
<li><a class="reference internal" href="#module-contents">Module Contents</a><ul>
<li><a class="reference internal" href="#flags">Flags</a></li>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#regular-expression-objects">Regular Expression Objects</a></li>
<li><a class="reference internal" href="#match-objects">Match Objects</a></li>
<li><a class="reference internal" href="#regular-expression-examples">Regular Expression Examples</a><ul>
<li><a class="reference internal" href="#checking-for-a-pair">Checking for a Pair</a></li>
<li><a class="reference internal" href="#simulating-scanf">Simulating scanf()</a></li>
<li><a class="reference internal" href="#search-vs-match">search() vs. match()</a></li>
<li><a class="reference internal" href="#making-a-phonebook">Making a Phonebook</a></li>
<li><a class="reference internal" href="#text-munging">Text Munging</a></li>
<li><a class="reference internal" href="#finding-all-adverbs">Finding all Adverbs</a></li>
<li><a class="reference internal" href="#finding-all-adverbs-and-their-positions">Finding all Adverbs and their Positions</a></li>
<li><a class="reference internal" href="#raw-string-notation">Raw String Notation</a></li>
<li><a class="reference internal" href="#writing-a-tokenizer">Writing a Tokenizer</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="string.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code> — Common string operations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="difflib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">difflib</span></code> — Helpers for computing deltas</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/re.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="difflib.html" title="difflib — Helpers for computing deltas"
             >next</a> |</li>
        <li class="right" >
          <a href="string.html" title="string — Common string operations"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" >Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code> — Regular expression operations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>