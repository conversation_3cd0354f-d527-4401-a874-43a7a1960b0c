# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_stripe
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Complete the Stripe onboarding for company %s."
msgstr ""

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Do not have access to fetch token from Stripe"
msgstr "Nie ma dostępu do pobrania tokena ze Stripe"

#. module: pos_stripe
#: model_terms:ir.ui.view,arch_db:pos_stripe.pos_payment_method_view_form_inherit_pos_stripe
msgid ""
"Don't forget to complete Stripe connect before using this payment method."
msgstr ""
"Nie zapomnij aby ukończyć połączenie Stripe przed użyciem tej metody "
"płatności."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Failed to discover: %s"
msgstr "Nie udało się odkryć:%s"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Failed to load resource: net::ERR_INTERNET_DISCONNECTED."
msgstr "Nie udało się załadować zasobu: net::ERR_INTERNET_DISCONNECTED."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "No available Stripe readers."
msgstr "Nie ma dostępnych czytników Stripe"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Payment canceled because not reader connected"
msgstr "Płatność anulowana ponieważ nie ma podłączonego czytnika"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Metody płatności punktu sprzedaży"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesja punktu sprzedaży"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Reader disconnected"
msgstr "Czytnik rozłączył się"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe"
msgstr "Stripe"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Stripe Error"
msgstr "Błąd Stripe"

#. module: pos_stripe
#: model:ir.model.fields,field_description:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "Stripe Serial Number"
msgstr "Numer seryjny Stripe"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe payment provider for company %s is missing"
msgstr ""

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Stripe readers %s not listed in your account"
msgstr "Czytniki Stripe %s nie wymienione w Twoim koncie"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr "Terminal %s jest już używany do metody płatności %s."

#. module: pos_stripe
#: model:ir.model.fields,help:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "[Serial number of the stripe terminal], for example: WSC513105011295"
msgstr "[Numer seryjny terminala Stripe], na przykład: WSC513105011295"
