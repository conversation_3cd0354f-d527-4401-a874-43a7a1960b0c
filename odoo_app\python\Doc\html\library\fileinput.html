<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="fileinput — Iterate over lines from multiple input streams" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/fileinput.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/fileinput.py This module implements a helper class and functions to quickly write a loop over standard input or a list of files. If you just want to read or write one file see open..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/fileinput.py This module implements a helper class and functions to quickly write a loop over standard input or a list of files. If you just want to read or write one file see open..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>fileinput — Iterate over lines from multiple input streams &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="stat — Interpreting stat() results" href="stat.html" />
    <link rel="prev" title="os.path — Common pathname manipulations" href="os.path.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/fileinput.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="os.path.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code> — Common pathname manipulations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="stat.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code> — Interpreting <code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code> results</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/fileinput.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="stat.html" title="stat — Interpreting stat() results"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="os.path.html" title="os.path — Common pathname manipulations"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" accesskey="U">File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code> — Iterate over lines from multiple input streams</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-fileinput">
<span id="fileinput-iterate-over-lines-from-multiple-input-streams"></span><h1><a class="reference internal" href="#module-fileinput" title="fileinput: Loop over standard input or a list of files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code></a> — Iterate over lines from multiple input streams<a class="headerlink" href="#module-fileinput" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/fileinput.py">Lib/fileinput.py</a></p>
<hr class="docutils" />
<p>This module implements a helper class and functions to quickly write a
loop over standard input or a list of files. If you just want to read or
write one file see <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.</p>
<p>The typical use is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">fileinput</span>
<span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">fileinput</span><span class="o">.</span><span class="n">input</span><span class="p">(</span><span class="n">encoding</span><span class="o">=</span><span class="s2">&quot;utf-8&quot;</span><span class="p">):</span>
    <span class="n">process</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</pre></div>
</div>
<p>This iterates over the lines of all files listed in <code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>, defaulting
to <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code> if the list is empty.  If a filename is <code class="docutils literal notranslate"><span class="pre">'-'</span></code>, it is also
replaced by <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code> and the optional arguments <em>mode</em> and <em>openhook</em>
are ignored.  To specify an alternative list of filenames, pass it as the
first argument to <a class="reference internal" href="#fileinput.input" title="fileinput.input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a>.  A single file name is also allowed.</p>
<p>All files are opened in text mode by default, but you can override this by
specifying the <em>mode</em> parameter in the call to <a class="reference internal" href="#fileinput.input" title="fileinput.input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a> or
<a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a>.  If an I/O error occurs during opening or reading a file,
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> used to be raised; it is now an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code> is used more than once, the second and further use will return
no lines, except perhaps for interactive use, or if it has been explicitly reset
(e.g. using <code class="docutils literal notranslate"><span class="pre">sys.stdin.seek(0)</span></code>).</p>
<p>Empty files are opened and immediately closed; the only time their presence in
the list of filenames is noticeable at all is when the last file opened is
empty.</p>
<p>Lines are returned with any newlines intact, which means that the last line in
a file may not have one.</p>
<p>You can control how files are opened by providing an opening hook via the
<em>openhook</em> parameter to <a class="reference internal" href="#fileinput.input" title="fileinput.input"><code class="xref py py-func docutils literal notranslate"><span class="pre">fileinput.input()</span></code></a> or <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput()</span></code></a>. The
hook must be a function that takes two arguments, <em>filename</em> and <em>mode</em>, and
returns an accordingly opened file-like object. If <em>encoding</em> and/or <em>errors</em>
are specified, they will be passed to the hook as additional keyword arguments.
This module provides a <a class="reference internal" href="#fileinput.hook_compressed" title="fileinput.hook_compressed"><code class="xref py py-func docutils literal notranslate"><span class="pre">hook_compressed()</span></code></a> to support compressed files.</p>
<p>The following function is the primary interface of this module:</p>
<dl class="py function">
<dt class="sig sig-object py" id="fileinput.input">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">files</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inplace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backup</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">openhook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.input" title="Link to this definition">¶</a></dt>
<dd><p>Create an instance of the <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a> class.  The instance will be used
as global state for the functions of this module, and is also returned to use
during iteration.  The parameters to this function will be passed along to the
constructor of the <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a> class.</p>
<p>The <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a> instance can be used as a context manager in the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  In this example, <em>input</em> is closed after the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement is exited, even if an exception occurs:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">fileinput</span><span class="o">.</span><span class="n">input</span><span class="p">(</span><span class="n">files</span><span class="o">=</span><span class="p">(</span><span class="s1">&#39;spam.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;eggs.txt&#39;</span><span class="p">),</span> <span class="n">encoding</span><span class="o">=</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">f</span><span class="p">:</span>
        <span class="n">process</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Can be used as a context manager.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The keyword parameters <em>mode</em> and <em>openhook</em> are now keyword-only.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The keyword-only parameter <em>encoding</em> and <em>errors</em> are added.</p>
</div>
</dd></dl>

<p>The following functions use the global state created by <a class="reference internal" href="#fileinput.input" title="fileinput.input"><code class="xref py py-func docutils literal notranslate"><span class="pre">fileinput.input()</span></code></a>;
if there is no active state, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised.</p>
<dl class="py function">
<dt class="sig sig-object py" id="fileinput.filename">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">filename</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.filename" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the file currently being read.  Before the first line has
been read, returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.fileno">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Return the integer “file descriptor” for the current file. When no file is
opened (before the first line and between files), returns <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.lineno">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">lineno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.lineno" title="Link to this definition">¶</a></dt>
<dd><p>Return the cumulative line number of the line that has just been read.  Before
the first line has been read, returns <code class="docutils literal notranslate"><span class="pre">0</span></code>.  After the last line of the last
file has been read, returns the line number of that line.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.filelineno">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">filelineno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.filelineno" title="Link to this definition">¶</a></dt>
<dd><p>Return the line number in the current file.  Before the first line has been
read, returns <code class="docutils literal notranslate"><span class="pre">0</span></code>.  After the last line of the last file has been read,
returns the line number of that line within the file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.isfirstline">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">isfirstline</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.isfirstline" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the line just read is the first line of its file, otherwise
return <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.isstdin">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">isstdin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.isstdin" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the last line was read from <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code>, otherwise return
<code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.nextfile">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">nextfile</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.nextfile" title="Link to this definition">¶</a></dt>
<dd><p>Close the current file so that the next iteration will read the first line from
the next file (if any); lines not read from the file will not count towards the
cumulative line count.  The filename is not changed until after the first line
of the next file has been read.  Before the first line has been read, this
function has no effect; it cannot be used to skip the first file.  After the
last line of the last file has been read, this function has no effect.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.close">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the sequence.</p>
</dd></dl>

<p>The class which implements the sequence behavior provided by the module is
available for subclassing as well:</p>
<dl class="py class">
<dt class="sig sig-object py" id="fileinput.FileInput">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">FileInput</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">files</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inplace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backup</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">openhook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.FileInput" title="Link to this definition">¶</a></dt>
<dd><p>Class <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a> is the implementation; its methods <a class="reference internal" href="#fileinput.filename" title="fileinput.filename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filename()</span></code></a>,
<a class="reference internal" href="#fileinput.fileno" title="fileinput.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a>, <a class="reference internal" href="#fileinput.lineno" title="fileinput.lineno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">lineno()</span></code></a>, <a class="reference internal" href="#fileinput.filelineno" title="fileinput.filelineno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filelineno()</span></code></a>, <a class="reference internal" href="#fileinput.isfirstline" title="fileinput.isfirstline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isfirstline()</span></code></a>,
<a class="reference internal" href="#fileinput.isstdin" title="fileinput.isstdin"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isstdin()</span></code></a>, <a class="reference internal" href="#fileinput.nextfile" title="fileinput.nextfile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">nextfile()</span></code></a> and <a class="reference internal" href="#fileinput.close" title="fileinput.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> correspond to the
functions of the same name in the module. In addition it is <a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a>
and has a <a class="reference internal" href="io.html#io.TextIOBase.readline" title="io.TextIOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> method which returns the next
input line. The sequence must be accessed in strictly sequential order;
random access and <a class="reference internal" href="io.html#io.TextIOBase.readline" title="io.TextIOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> cannot be mixed.</p>
<p>With <em>mode</em> you can specify which file mode will be passed to <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>. It
must be one of <code class="docutils literal notranslate"><span class="pre">'r'</span></code> and <code class="docutils literal notranslate"><span class="pre">'rb'</span></code>.</p>
<p>The <em>openhook</em>, when given, must be a function that takes two arguments,
<em>filename</em> and <em>mode</em>, and returns an accordingly opened file-like object. You
cannot use <em>inplace</em> and <em>openhook</em> together.</p>
<p>You can specify <em>encoding</em> and <em>errors</em> that is passed to <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> or <em>openhook</em>.</p>
<p>A <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a> instance can be used as a context manager in the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  In this example, <em>input</em> is closed after the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement is exited, even if an exception occurs:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">FileInput</span><span class="p">(</span><span class="n">files</span><span class="o">=</span><span class="p">(</span><span class="s1">&#39;spam.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;eggs.txt&#39;</span><span class="p">))</span> <span class="k">as</span> <span class="nb">input</span><span class="p">:</span>
    <span class="n">process</span><span class="p">(</span><span class="nb">input</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Can be used as a context manager.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The keyword parameter <em>mode</em> and <em>openhook</em> are now keyword-only.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The keyword-only parameter <em>encoding</em> and <em>errors</em> are added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <code class="docutils literal notranslate"><span class="pre">'rU'</span></code> and <code class="docutils literal notranslate"><span class="pre">'U'</span></code> modes and the <code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code> method have
been removed.</p>
</div>
</dd></dl>

<p><strong>Optional in-place filtering:</strong> if the keyword argument <code class="docutils literal notranslate"><span class="pre">inplace=True</span></code> is
passed to <a class="reference internal" href="#fileinput.input" title="fileinput.input"><code class="xref py py-func docutils literal notranslate"><span class="pre">fileinput.input()</span></code></a> or to the <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a> constructor, the
file is moved to a backup file and standard output is directed to the input file
(if a file of the same name as the backup file already exists, it will be
replaced silently).  This makes it possible to write a filter that rewrites its
input file in place.  If the <em>backup</em> parameter is given (typically as
<code class="docutils literal notranslate"><span class="pre">backup='.&lt;some</span> <span class="pre">extension&gt;'</span></code>), it specifies the extension for the backup file,
and the backup file remains around; by default, the extension is <code class="docutils literal notranslate"><span class="pre">'.bak'</span></code> and
it is deleted when the output file is closed.  In-place filtering is disabled
when standard input is read.</p>
<p>The two following opening hooks are provided by this module:</p>
<dl class="py function">
<dt class="sig sig-object py" id="fileinput.hook_compressed">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">hook_compressed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.hook_compressed" title="Link to this definition">¶</a></dt>
<dd><p>Transparently opens files compressed with gzip and bzip2 (recognized by the
extensions <code class="docutils literal notranslate"><span class="pre">'.gz'</span></code> and <code class="docutils literal notranslate"><span class="pre">'.bz2'</span></code>) using the <a class="reference internal" href="gzip.html#module-gzip" title="gzip: Interfaces for gzip compression and decompression using file objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code></a> and <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a>
modules.  If the filename extension is not <code class="docutils literal notranslate"><span class="pre">'.gz'</span></code> or <code class="docutils literal notranslate"><span class="pre">'.bz2'</span></code>, the file is
opened normally (ie, using <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> without any decompression).</p>
<p>The <em>encoding</em> and <em>errors</em> values are passed to <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a>
for compressed files and open for normal files.</p>
<p>Usage example:  <code class="docutils literal notranslate"><span class="pre">fi</span> <span class="pre">=</span> <span class="pre">fileinput.FileInput(openhook=fileinput.hook_compressed,</span> <span class="pre">encoding=&quot;utf-8&quot;)</span></code></p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The keyword-only parameter <em>encoding</em> and <em>errors</em> are added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="fileinput.hook_encoded">
<span class="sig-prename descclassname"><span class="pre">fileinput.</span></span><span class="sig-name descname"><span class="pre">hook_encoded</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fileinput.hook_encoded" title="Link to this definition">¶</a></dt>
<dd><p>Returns a hook which opens each file with <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>, using the given
<em>encoding</em> and <em>errors</em> to read the file.</p>
<p>Usage example: <code class="docutils literal notranslate"><span class="pre">fi</span> <span class="pre">=</span>
<span class="pre">fileinput.FileInput(openhook=fileinput.hook_encoded(&quot;utf-8&quot;,</span>
<span class="pre">&quot;surrogateescape&quot;))</span></code></p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the optional <em>errors</em> parameter.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.10: </span>This function is deprecated since <a class="reference internal" href="#fileinput.input" title="fileinput.input"><code class="xref py py-func docutils literal notranslate"><span class="pre">fileinput.input()</span></code></a> and <a class="reference internal" href="#fileinput.FileInput" title="fileinput.FileInput"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileInput</span></code></a>
now have <em>encoding</em> and <em>errors</em> parameters.</p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="os.path.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code> — Common pathname manipulations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="stat.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code> — Interpreting <code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code> results</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/fileinput.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="stat.html" title="stat — Interpreting stat() results"
             >next</a> |</li>
        <li class="right" >
          <a href="os.path.html" title="os.path — Common pathname manipulations"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" >File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code> — Iterate over lines from multiple input streams</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>