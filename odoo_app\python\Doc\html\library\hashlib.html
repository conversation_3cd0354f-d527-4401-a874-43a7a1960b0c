<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="hashlib — Secure hashes and message digests" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/hashlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/hashlib.py This module implements a common interface to many different secure hash and message digest algorithms. Included are the FIPS secure hash algorithms SHA1, SHA224, SHA256,..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/hashlib.py This module implements a common interface to many different secure hash and message digest algorithms. Included are the FIPS secure hash algorithms SHA1, SHA224, SHA256,..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>hashlib — Secure hashes and message digests &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="hmac — Keyed-Hashing for Message Authentication" href="hmac.html" />
    <link rel="prev" title="Cryptographic Services" href="crypto.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/hashlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code> — Secure hashes and message digests</a><ul>
<li><a class="reference internal" href="#hash-algorithms">Hash algorithms</a></li>
<li><a class="reference internal" href="#usage">Usage</a></li>
<li><a class="reference internal" href="#constructors">Constructors</a></li>
<li><a class="reference internal" href="#attributes">Attributes</a></li>
<li><a class="reference internal" href="#hash-objects">Hash Objects</a></li>
<li><a class="reference internal" href="#shake-variable-length-digests">SHAKE variable length digests</a></li>
<li><a class="reference internal" href="#file-hashing">File hashing</a></li>
<li><a class="reference internal" href="#key-derivation">Key derivation</a></li>
<li><a class="reference internal" href="#blake2">BLAKE2</a><ul>
<li><a class="reference internal" href="#creating-hash-objects">Creating hash objects</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#simple-hashing">Simple hashing</a></li>
<li><a class="reference internal" href="#using-different-digest-sizes">Using different digest sizes</a></li>
<li><a class="reference internal" href="#keyed-hashing">Keyed hashing</a></li>
<li><a class="reference internal" href="#randomized-hashing">Randomized hashing</a></li>
<li><a class="reference internal" href="#personalization">Personalization</a></li>
<li><a class="reference internal" href="#tree-mode">Tree mode</a></li>
</ul>
</li>
<li><a class="reference internal" href="#credits">Credits</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="crypto.html"
                          title="previous chapter">Cryptographic Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="hmac.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code> — Keyed-Hashing for Message Authentication</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/hashlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="hmac.html" title="hmac — Keyed-Hashing for Message Authentication"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="crypto.html" title="Cryptographic Services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="crypto.html" accesskey="U">Cryptographic Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code> — Secure hashes and message digests</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-hashlib">
<span id="hashlib-secure-hashes-and-message-digests"></span><h1><a class="reference internal" href="#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a> — Secure hashes and message digests<a class="headerlink" href="#module-hashlib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/hashlib.py">Lib/hashlib.py</a></p>
<span class="target" id="index-0"></span><hr class="docutils" />
<p>This module implements a common interface to many different secure hash and
message digest algorithms.  Included are the FIPS secure hash algorithms SHA1,
SHA224, SHA256, SHA384, SHA512, (defined in <a class="reference external" href="https://csrc.nist.gov/publications/detail/fips/180/4/final">the FIPS 180-4 standard</a>),
the SHA-3 series (defined in <a class="reference external" href="https://csrc.nist.gov/publications/detail/fips/202/final">the FIPS 202 standard</a>) as well as RSA’s MD5
algorithm (defined in internet <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1321.html"><strong>RFC 1321</strong></a>).  The terms “secure hash” and
“message digest” are interchangeable.  Older algorithms were called message
digests.  The modern term is secure hash.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you want the adler32 or crc32 hash functions, they are available in
the <a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module.</p>
</div>
<section id="hash-algorithms">
<span id="id1"></span><h2>Hash algorithms<a class="headerlink" href="#hash-algorithms" title="Link to this heading">¶</a></h2>
<p>There is one constructor method named for each type of <em class="dfn">hash</em>.  All return
a hash object with the same simple interface. For example: use <a class="reference internal" href="#hashlib.sha256" title="hashlib.sha256"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha256()</span></code></a>
to create a SHA-256 hash object. You can now feed this object with
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like objects</span></a> (normally <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>) using
the <a class="reference internal" href="#hashlib.hash.update" title="hashlib.hash.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">update</span></code></a> method.  At any point you can ask it for the
<em class="dfn">digest</em> of the concatenation of the data fed to it so far using the
<a class="reference internal" href="#hashlib.hash.digest" title="hashlib.hash.digest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">digest()</span></code></a> or <a class="reference internal" href="#hashlib.hash.hexdigest" title="hashlib.hash.hexdigest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hexdigest()</span></code></a> methods.</p>
<p>To allow multithreading, the Python <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a> is released while computing a
hash supplied more than 2047 bytes of data at once in its constructor or
<a class="reference internal" href="#hashlib.hash.update" title="hashlib.hash.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">.update</span></code></a> method.</p>
<p id="index-2">Constructors for hash algorithms that are always present in this module are
<a class="reference internal" href="#hashlib.sha1" title="hashlib.sha1"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha1()</span></code></a>, <a class="reference internal" href="#hashlib.sha224" title="hashlib.sha224"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha224()</span></code></a>, <a class="reference internal" href="#hashlib.sha256" title="hashlib.sha256"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha256()</span></code></a>, <a class="reference internal" href="#hashlib.sha384" title="hashlib.sha384"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha384()</span></code></a>, <a class="reference internal" href="#hashlib.sha512" title="hashlib.sha512"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha512()</span></code></a>,
<a class="reference internal" href="#hashlib.sha3_224" title="hashlib.sha3_224"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_224()</span></code></a>, <a class="reference internal" href="#hashlib.sha3_256" title="hashlib.sha3_256"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_256()</span></code></a>, <a class="reference internal" href="#hashlib.sha3_384" title="hashlib.sha3_384"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_384()</span></code></a>, <a class="reference internal" href="#hashlib.sha3_512" title="hashlib.sha3_512"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_512()</span></code></a>,
<a class="reference internal" href="#hashlib.shake_128" title="hashlib.shake_128"><code class="xref py py-func docutils literal notranslate"><span class="pre">shake_128()</span></code></a>, <a class="reference internal" href="#hashlib.shake_256" title="hashlib.shake_256"><code class="xref py py-func docutils literal notranslate"><span class="pre">shake_256()</span></code></a>, <a class="reference internal" href="#hashlib.blake2b" title="hashlib.blake2b"><code class="xref py py-func docutils literal notranslate"><span class="pre">blake2b()</span></code></a>, and <a class="reference internal" href="#hashlib.blake2s" title="hashlib.blake2s"><code class="xref py py-func docutils literal notranslate"><span class="pre">blake2s()</span></code></a>.
<a class="reference internal" href="#hashlib.md5" title="hashlib.md5"><code class="xref py py-func docutils literal notranslate"><span class="pre">md5()</span></code></a> is normally available as well, though it may be missing or blocked
if you are using a rare “FIPS compliant” build of Python.
These correspond to <a class="reference internal" href="#hashlib.algorithms_guaranteed" title="hashlib.algorithms_guaranteed"><code class="xref py py-data docutils literal notranslate"><span class="pre">algorithms_guaranteed</span></code></a>.</p>
<p>Additional algorithms may also be available if your Python distribution’s
<a class="reference internal" href="#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a> was linked against a build of OpenSSL that provides others.
Others <em>are not guaranteed available</em> on all installations and will only be
accessible by name via <a class="reference internal" href="#hashlib.new" title="hashlib.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">new()</span></code></a>.  See <a class="reference internal" href="#hashlib.algorithms_available" title="hashlib.algorithms_available"><code class="xref py py-data docutils literal notranslate"><span class="pre">algorithms_available</span></code></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Some algorithms have known hash collision weaknesses (including MD5 and
SHA1). Refer to <a class="reference external" href="https://en.wikipedia.org/wiki/Cryptographic_hash_function#Attacks_on_cryptographic_hash_algorithms">Attacks on cryptographic hash algorithms</a> and the
<a class="reference internal" href="#hashlib-seealso">hashlib-seealso</a> section at the end of this document.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>SHA3 (Keccak) and SHAKE constructors <a class="reference internal" href="#hashlib.sha3_224" title="hashlib.sha3_224"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_224()</span></code></a>, <a class="reference internal" href="#hashlib.sha3_256" title="hashlib.sha3_256"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_256()</span></code></a>,
<a class="reference internal" href="#hashlib.sha3_384" title="hashlib.sha3_384"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_384()</span></code></a>, <a class="reference internal" href="#hashlib.sha3_512" title="hashlib.sha3_512"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha3_512()</span></code></a>, <a class="reference internal" href="#hashlib.shake_128" title="hashlib.shake_128"><code class="xref py py-func docutils literal notranslate"><span class="pre">shake_128()</span></code></a>, <a class="reference internal" href="#hashlib.shake_256" title="hashlib.shake_256"><code class="xref py py-func docutils literal notranslate"><span class="pre">shake_256()</span></code></a>
were added.
<a class="reference internal" href="#hashlib.blake2b" title="hashlib.blake2b"><code class="xref py py-func docutils literal notranslate"><span class="pre">blake2b()</span></code></a> and <a class="reference internal" href="#hashlib.blake2s" title="hashlib.blake2s"><code class="xref py py-func docutils literal notranslate"><span class="pre">blake2s()</span></code></a> were added.</p>
</div>
<div class="versionchanged" id="hashlib-usedforsecurity">
<p><span class="versionmodified changed">Changed in version 3.9: </span>All hashlib constructors take a keyword-only argument <em>usedforsecurity</em>
with default value <code class="docutils literal notranslate"><span class="pre">True</span></code>. A false value allows the use of insecure and
blocked hashing algorithms in restricted environments. <code class="docutils literal notranslate"><span class="pre">False</span></code> indicates
that the hashing algorithm is not used in a security context, e.g. as a
non-cryptographic one-way compression function.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Hashlib now uses SHA3 and SHAKE from OpenSSL if it provides it.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>For any of the MD5, SHA1, SHA2, or SHA3 algorithms that the linked
OpenSSL does not provide we fall back to a verified implementation from
the <a class="reference external" href="https://github.com/hacl-star/hacl-star">HACL* project</a>.</p>
</div>
</section>
<section id="usage">
<h2>Usage<a class="headerlink" href="#usage" title="Link to this heading">¶</a></h2>
<p>To obtain the digest of the byte string <code class="docutils literal notranslate"><span class="pre">b&quot;Nobody</span> <span class="pre">inspects</span> <span class="pre">the</span> <span class="pre">spammish</span>
<span class="pre">repetition&quot;</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">hashlib</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Nobody inspects&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot; the spammish repetition&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span>
<span class="go">b&#39;\x03\x1e\xdd}Ae\x15\x93\xc5\xfe\\\x00o\xa5u+7\xfd\xdf\xf7\xbcN\x84:\xa6\xaf\x0c\x95\x0fK\x94\x06&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;031edd7d41651593c5fe5c006fa5752b37fddff7bc4e843aa6af0c950f4b9406&#39;</span>
</pre></div>
</div>
<p>More condensed:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Nobody inspects the spammish repetition&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;031edd7d41651593c5fe5c006fa5752b37fddff7bc4e843aa6af0c950f4b9406&#39;</span>
</pre></div>
</div>
</section>
<section id="constructors">
<h2>Constructors<a class="headerlink" href="#constructors" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="hashlib.new">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">new</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.new" title="Link to this definition">¶</a></dt>
<dd><p>Is a generic constructor that takes the string <em>name</em> of the desired
algorithm as its first parameter.  It also exists to allow access to the
above listed hashes as well as any other algorithms that your OpenSSL
library may offer.</p>
</dd></dl>

<p>Using <a class="reference internal" href="#hashlib.new" title="hashlib.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">new()</span></code></a> with an algorithm name:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">new</span><span class="p">(</span><span class="s1">&#39;sha256&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Nobody inspects the spammish repetition&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;031edd7d41651593c5fe5c006fa5752b37fddff7bc4e843aa6af0c950f4b9406&#39;</span>
</pre></div>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="hashlib.md5">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">md5</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.md5" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha1">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha1</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha1" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha224">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha224</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha224" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha256">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha256</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha256" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha384">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha384</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha384" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha512">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha512</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha512" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha3_224">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha3_224</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha3_224" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha3_256">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha3_256</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha3_256" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha3_384">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha3_384</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha3_384" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.sha3_512">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">sha3_512</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.sha3_512" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>Named constructors such as these are faster than passing an algorithm name to
<a class="reference internal" href="#hashlib.new" title="hashlib.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">new()</span></code></a>.</p>
</section>
<section id="attributes">
<h2>Attributes<a class="headerlink" href="#attributes" title="Link to this heading">¶</a></h2>
<p>Hashlib provides the following constant module attributes:</p>
<dl class="py data">
<dt class="sig sig-object py" id="hashlib.algorithms_guaranteed">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">algorithms_guaranteed</span></span><a class="headerlink" href="#hashlib.algorithms_guaranteed" title="Link to this definition">¶</a></dt>
<dd><p>A set containing the names of the hash algorithms guaranteed to be supported
by this module on all platforms.  Note that ‘md5’ is in this list despite
some upstream vendors offering an odd “FIPS compliant” Python build that
excludes it.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="hashlib.algorithms_available">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">algorithms_available</span></span><a class="headerlink" href="#hashlib.algorithms_available" title="Link to this definition">¶</a></dt>
<dd><p>A set containing the names of the hash algorithms that are available in the
running Python interpreter.  These names will be recognized when passed to
<a class="reference internal" href="#hashlib.new" title="hashlib.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">new()</span></code></a>.  <a class="reference internal" href="#hashlib.algorithms_guaranteed" title="hashlib.algorithms_guaranteed"><code class="xref py py-attr docutils literal notranslate"><span class="pre">algorithms_guaranteed</span></code></a> will always be a subset.  The
same algorithm may appear multiple times in this set under different names
(thanks to OpenSSL).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
<section id="hash-objects">
<h2>Hash Objects<a class="headerlink" href="#hash-objects" title="Link to this heading">¶</a></h2>
<p>The following values are provided as constant attributes of the hash objects
returned by the constructors:</p>
<dl class="py data">
<dt class="sig sig-object py" id="hashlib.hash.digest_size">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">digest_size</span></span><a class="headerlink" href="#hashlib.hash.digest_size" title="Link to this definition">¶</a></dt>
<dd><p>The size of the resulting hash in bytes.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="hashlib.hash.block_size">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">block_size</span></span><a class="headerlink" href="#hashlib.hash.block_size" title="Link to this definition">¶</a></dt>
<dd><p>The internal block size of the hash algorithm in bytes.</p>
</dd></dl>

<p>A hash object has the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="hashlib.hash.name">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#hashlib.hash.name" title="Link to this definition">¶</a></dt>
<dd><p>The canonical name of this hash, always lowercase and always suitable as a
parameter to <a class="reference internal" href="#hashlib.new" title="hashlib.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">new()</span></code></a> to create another hash of this type.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The name attribute has been present in CPython since its inception, but
until Python 3.4 was not formally specified, so may not exist on some
platforms.</p>
</div>
</dd></dl>

<p>A hash object has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="hashlib.hash.update">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.hash.update" title="Link to this definition">¶</a></dt>
<dd><p>Update the hash object with the <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>.
Repeated calls are equivalent to a single call with the
concatenation of all the arguments: <code class="docutils literal notranslate"><span class="pre">m.update(a);</span> <span class="pre">m.update(b)</span></code> is
equivalent to <code class="docutils literal notranslate"><span class="pre">m.update(a+b)</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hashlib.hash.digest">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">digest</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.hash.digest" title="Link to this definition">¶</a></dt>
<dd><p>Return the digest of the data passed to the <a class="reference internal" href="#hashlib.hash.update" title="hashlib.hash.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">update()</span></code></a> method so far.
This is a bytes object of size <a class="reference internal" href="#hashlib.hash.digest_size" title="hashlib.hash.digest_size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">digest_size</span></code></a> which may contain bytes in
the whole range from 0 to 255.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hashlib.hash.hexdigest">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">hexdigest</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.hash.hexdigest" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#hashlib.hash.digest" title="hashlib.hash.digest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">digest()</span></code></a> except the digest is returned as a string object of
double length, containing only hexadecimal digits.  This may be used to
exchange the value safely in email or other non-binary environments.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hashlib.hash.copy">
<span class="sig-prename descclassname"><span class="pre">hash.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.hash.copy" title="Link to this definition">¶</a></dt>
<dd><p>Return a copy (“clone”) of the hash object.  This can be used to efficiently
compute the digests of data sharing a common initial substring.</p>
</dd></dl>

</section>
<section id="shake-variable-length-digests">
<h2>SHAKE variable length digests<a class="headerlink" href="#shake-variable-length-digests" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="hashlib.shake_128">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">shake_128</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.shake_128" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.shake_256">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">shake_256</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.shake_256" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>The <a class="reference internal" href="#hashlib.shake_128" title="hashlib.shake_128"><code class="xref py py-func docutils literal notranslate"><span class="pre">shake_128()</span></code></a> and <a class="reference internal" href="#hashlib.shake_256" title="hashlib.shake_256"><code class="xref py py-func docutils literal notranslate"><span class="pre">shake_256()</span></code></a> algorithms provide variable
length digests with length_in_bits//2 up to 128 or 256 bits of security.
As such, their digest methods require a length. Maximum length is not limited
by the SHAKE algorithm.</p>
<dl class="py method">
<dt class="sig sig-object py" id="hashlib.shake.digest">
<span class="sig-prename descclassname"><span class="pre">shake.</span></span><span class="sig-name descname"><span class="pre">digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.shake.digest" title="Link to this definition">¶</a></dt>
<dd><p>Return the digest of the data passed to the <a class="reference internal" href="#hashlib.hash.update" title="hashlib.hash.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">update()</span></code></a> method so far.
This is a bytes object of size <em>length</em> which may contain bytes in
the whole range from 0 to 255.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hashlib.shake.hexdigest">
<span class="sig-prename descclassname"><span class="pre">shake.</span></span><span class="sig-name descname"><span class="pre">hexdigest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.shake.hexdigest" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#hashlib.shake.digest" title="hashlib.shake.digest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">digest()</span></code></a> except the digest is returned as a string object of
double length, containing only hexadecimal digits.  This may be used to
exchange the value in email or other non-binary environments.</p>
</dd></dl>

<p>Example use:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">shake_256</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Nobody inspects the spammish repetition&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
<span class="go">&#39;44709d6fcb83d92a76dcb0b668c98e1b1d3dafe7&#39;</span>
</pre></div>
</div>
</section>
<section id="file-hashing">
<h2>File hashing<a class="headerlink" href="#file-hashing" title="Link to this heading">¶</a></h2>
<p>The hashlib module provides a helper function for efficient hashing of
a file or file-like object.</p>
<dl class="py function">
<dt class="sig sig-object py" id="hashlib.file_digest">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">file_digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileobj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">digest</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.file_digest" title="Link to this definition">¶</a></dt>
<dd><p>Return a digest object that has been updated with contents of file object.</p>
<p><em>fileobj</em> must be a file-like object opened for reading in binary mode.
It accepts file objects from  builtin <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>, <a class="reference internal" href="io.html#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a>
instances, SocketIO objects from <a class="reference internal" href="socket.html#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.socket.makefile()</span></code></a>, and
similar. The function may bypass Python’s I/O and use the file descriptor
from <a class="reference internal" href="io.html#io.IOBase.fileno" title="io.IOBase.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a> directly. <em>fileobj</em> must be assumed to be
in an unknown state after this function returns or raises. It is up to
the caller to close <em>fileobj</em>.</p>
<p><em>digest</em> must either be a hash algorithm name as a <em>str</em>, a hash
constructor, or a callable that returns a hash object.</p>
<p>Example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">io</span><span class="o">,</span> <span class="nn">hashlib</span><span class="o">,</span> <span class="nn">hmac</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">hashlib</span><span class="o">.</span><span class="vm">__file__</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">digest</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">file_digest</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="s2">&quot;sha256&quot;</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">digest</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>  
<span class="go">&#39;...&#39;</span>
</pre></div>
</div>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">buf</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">BytesIO</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;somedata&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mac1</span> <span class="o">=</span> <span class="n">hmac</span><span class="o">.</span><span class="n">HMAC</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;key&quot;</span><span class="p">,</span> <span class="n">digestmod</span><span class="o">=</span><span class="n">hashlib</span><span class="o">.</span><span class="n">sha512</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">digest</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">file_digest</span><span class="p">(</span><span class="n">buf</span><span class="p">,</span> <span class="k">lambda</span><span class="p">:</span> <span class="n">mac1</span><span class="p">)</span>
</pre></div>
</div>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">digest</span> <span class="ow">is</span> <span class="n">mac1</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mac2</span> <span class="o">=</span> <span class="n">hmac</span><span class="o">.</span><span class="n">HMAC</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;key&quot;</span><span class="p">,</span> <span class="sa">b</span><span class="s2">&quot;somedata&quot;</span><span class="p">,</span> <span class="n">digestmod</span><span class="o">=</span><span class="n">hashlib</span><span class="o">.</span><span class="n">sha512</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mac1</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span> <span class="o">==</span> <span class="n">mac2</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</section>
<section id="key-derivation">
<h2>Key derivation<a class="headerlink" href="#key-derivation" title="Link to this heading">¶</a></h2>
<p>Key derivation and key stretching algorithms are designed for secure password
hashing. Naive algorithms such as <code class="docutils literal notranslate"><span class="pre">sha1(password)</span></code> are not resistant against
brute-force attacks. A good password hashing function must be tunable, slow, and
include a <a class="reference external" href="https://en.wikipedia.org/wiki/Salt_%28cryptography%29">salt</a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="hashlib.pbkdf2_hmac">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">pbkdf2_hmac</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hash_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">salt</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterations</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dklen</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.pbkdf2_hmac" title="Link to this definition">¶</a></dt>
<dd><p>The function provides PKCS#5 password-based key derivation function 2. It
uses HMAC as pseudorandom function.</p>
<p>The string <em>hash_name</em> is the desired name of the hash digest algorithm for
HMAC, e.g. ‘sha1’ or ‘sha256’. <em>password</em> and <em>salt</em> are interpreted as
buffers of bytes. Applications and libraries should limit <em>password</em> to
a sensible length (e.g. 1024). <em>salt</em> should be about 16 or more bytes from
a proper source, e.g. <a class="reference internal" href="os.html#os.urandom" title="os.urandom"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.urandom()</span></code></a>.</p>
<p>The number of <em>iterations</em> should be chosen based on the hash algorithm and
computing power. As of 2022, hundreds of thousands of iterations of SHA-256
are suggested. For rationale as to why and how to choose what is best for
your application, read <em>Appendix A.2.2</em> of <a class="reference external" href="https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf">NIST-SP-800-132</a>. The answers
on the <a class="reference external" href="https://security.stackexchange.com/questions/3959/recommended-of-iterations-when-using-pbkdf2-sha256/">stackexchange pbkdf2 iterations question</a> explain in detail.</p>
<p><em>dklen</em> is the length of the derived key. If <em>dklen</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> then the
digest size of the hash algorithm <em>hash_name</em> is used, e.g. 64 for SHA-512.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">pbkdf2_hmac</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">our_app_iters</span> <span class="o">=</span> <span class="mi">500_000</span>  <span class="c1"># Application specific, read above.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dk</span> <span class="o">=</span> <span class="n">pbkdf2_hmac</span><span class="p">(</span><span class="s1">&#39;sha256&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;password&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;bad salt&#39;</span> <span class="o">*</span> <span class="mi">2</span><span class="p">,</span> <span class="n">our_app_iters</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dk</span><span class="o">.</span><span class="n">hex</span><span class="p">()</span>
<span class="go">&#39;15530bba69924174860db778f2c6f8104d3aaf9d26241840c8c4a641c8d000a9&#39;</span>
</pre></div>
</div>
<p>Function only available when Python is compiled with OpenSSL.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Function now only available when Python is built with OpenSSL. The slow
pure Python implementation has been removed.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.scrypt">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">scrypt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">salt</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">r</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">p</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxmem</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dklen</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">64</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.scrypt" title="Link to this definition">¶</a></dt>
<dd><p>The function provides scrypt password-based key derivation function as
defined in <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7914.html"><strong>RFC 7914</strong></a>.</p>
<p><em>password</em> and <em>salt</em> must be <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like objects</span></a>.  Applications and libraries should limit <em>password</em>
to a sensible length (e.g. 1024).  <em>salt</em> should be about 16 or more
bytes from a proper source, e.g. <a class="reference internal" href="os.html#os.urandom" title="os.urandom"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.urandom()</span></code></a>.</p>
<p><em>n</em> is the CPU/Memory cost factor, <em>r</em> the block size, <em>p</em> parallelization
factor and <em>maxmem</em> limits memory (OpenSSL 1.1.0 defaults to 32 MiB).
<em>dklen</em> is the length of the derived key.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

</section>
<section id="blake2">
<h2>BLAKE2<a class="headerlink" href="#blake2" title="Link to this heading">¶</a></h2>
<p id="index-4"><a class="reference external" href="https://www.blake2.net">BLAKE2</a> is a cryptographic hash function defined in <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7693.html"><strong>RFC 7693</strong></a> that comes in two
flavors:</p>
<ul class="simple">
<li><p><strong>BLAKE2b</strong>, optimized for 64-bit platforms and produces digests of any size
between 1 and 64 bytes,</p></li>
<li><p><strong>BLAKE2s</strong>, optimized for 8- to 32-bit platforms and produces digests of any
size between 1 and 32 bytes.</p></li>
</ul>
<p>BLAKE2 supports <strong>keyed mode</strong> (a faster and simpler replacement for <a class="reference external" href="https://en.wikipedia.org/wiki/Hash-based_message_authentication_code">HMAC</a>),
<strong>salted hashing</strong>, <strong>personalization</strong>, and <strong>tree hashing</strong>.</p>
<p>Hash objects from this module follow the API of standard library’s
<a class="reference internal" href="#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a> objects.</p>
<section id="creating-hash-objects">
<h3>Creating hash objects<a class="headerlink" href="#creating-hash-objects" title="Link to this heading">¶</a></h3>
<p>New hash objects are created by calling constructor functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="hashlib.blake2b">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">blake2b</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">digest_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">64</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">salt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">person</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fanout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">leaf_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inner_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">last_node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.blake2b" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hashlib.blake2s">
<span class="sig-prename descclassname"><span class="pre">hashlib.</span></span><span class="sig-name descname"><span class="pre">blake2s</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">digest_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">32</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">salt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">person</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fanout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">leaf_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inner_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">last_node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usedforsecurity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hashlib.blake2s" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>These functions return the corresponding hash objects for calculating
BLAKE2b or BLAKE2s. They optionally take these general parameters:</p>
<ul class="simple">
<li><p><em>data</em>: initial chunk of data to hash, which must be
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>.  It can be passed only as positional argument.</p></li>
<li><p><em>digest_size</em>: size of output digest in bytes.</p></li>
<li><p><em>key</em>: key for keyed hashing (up to 64 bytes for BLAKE2b, up to 32 bytes for
BLAKE2s).</p></li>
<li><p><em>salt</em>: salt for randomized hashing (up to 16 bytes for BLAKE2b, up to 8
bytes for BLAKE2s).</p></li>
<li><p><em>person</em>: personalization string (up to 16 bytes for BLAKE2b, up to 8 bytes
for BLAKE2s).</p></li>
</ul>
<p>The following table shows limits for general parameters (in bytes):</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Hash</p></th>
<th class="head"><p>digest_size</p></th>
<th class="head"><p>len(key)</p></th>
<th class="head"><p>len(salt)</p></th>
<th class="head"><p>len(person)</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>BLAKE2b</p></td>
<td><p>64</p></td>
<td><p>64</p></td>
<td><p>16</p></td>
<td><p>16</p></td>
</tr>
<tr class="row-odd"><td><p>BLAKE2s</p></td>
<td><p>32</p></td>
<td><p>32</p></td>
<td><p>8</p></td>
<td><p>8</p></td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>BLAKE2 specification defines constant lengths for salt and personalization
parameters, however, for convenience, this implementation accepts byte
strings of any size up to the specified length. If the length of the
parameter is less than specified, it is padded with zeros, thus, for
example, <code class="docutils literal notranslate"><span class="pre">b'salt'</span></code> and <code class="docutils literal notranslate"><span class="pre">b'salt\x00'</span></code> is the same value. (This is not
the case for <em>key</em>.)</p>
</div>
<p>These sizes are available as module <a class="reference internal" href="#constants">constants</a> described below.</p>
<p>Constructor functions also accept the following tree hashing parameters:</p>
<ul class="simple">
<li><p><em>fanout</em>: fanout (0 to 255, 0 if unlimited, 1 in sequential mode).</p></li>
<li><p><em>depth</em>: maximal depth of tree (1 to 255, 255 if unlimited, 1 in
sequential mode).</p></li>
<li><p><em>leaf_size</em>: maximal byte length of leaf (0 to <code class="docutils literal notranslate"><span class="pre">2**32-1</span></code>, 0 if unlimited or in
sequential mode).</p></li>
<li><p><em>node_offset</em>: node offset (0 to <code class="docutils literal notranslate"><span class="pre">2**64-1</span></code> for BLAKE2b, 0 to <code class="docutils literal notranslate"><span class="pre">2**48-1</span></code> for
BLAKE2s, 0 for the first, leftmost, leaf, or in sequential mode).</p></li>
<li><p><em>node_depth</em>: node depth (0 to 255, 0 for leaves, or in sequential mode).</p></li>
<li><p><em>inner_size</em>: inner digest size (0 to 64 for BLAKE2b, 0 to 32 for
BLAKE2s, 0 in sequential mode).</p></li>
<li><p><em>last_node</em>: boolean indicating whether the processed node is the last
one (<code class="docutils literal notranslate"><span class="pre">False</span></code> for sequential mode).</p></li>
</ul>
<figure class="align-default">
<img alt="Explanation of tree mode parameters." class="invert-in-dark-mode" src="../_images/hashlib-blake2-tree.png" />
</figure>
<p>See section 2.10 in <a class="reference external" href="https://www.blake2.net/blake2_20130129.pdf">BLAKE2 specification</a> for comprehensive review of tree
hashing.</p>
</section>
<section id="constants">
<h3>Constants<a class="headerlink" href="#constants" title="Link to this heading">¶</a></h3>
<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2b.SALT_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2b.</span></span><span class="sig-name descname"><span class="pre">SALT_SIZE</span></span><a class="headerlink" href="#hashlib.blake2b.SALT_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2s.SALT_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2s.</span></span><span class="sig-name descname"><span class="pre">SALT_SIZE</span></span><a class="headerlink" href="#hashlib.blake2s.SALT_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>Salt length (maximum length accepted by constructors).</p>
<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2b.PERSON_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2b.</span></span><span class="sig-name descname"><span class="pre">PERSON_SIZE</span></span><a class="headerlink" href="#hashlib.blake2b.PERSON_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2s.PERSON_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2s.</span></span><span class="sig-name descname"><span class="pre">PERSON_SIZE</span></span><a class="headerlink" href="#hashlib.blake2s.PERSON_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>Personalization string length (maximum length accepted by constructors).</p>
<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2b.MAX_KEY_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2b.</span></span><span class="sig-name descname"><span class="pre">MAX_KEY_SIZE</span></span><a class="headerlink" href="#hashlib.blake2b.MAX_KEY_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2s.MAX_KEY_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2s.</span></span><span class="sig-name descname"><span class="pre">MAX_KEY_SIZE</span></span><a class="headerlink" href="#hashlib.blake2s.MAX_KEY_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>Maximum key size.</p>
<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2b.MAX_DIGEST_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2b.</span></span><span class="sig-name descname"><span class="pre">MAX_DIGEST_SIZE</span></span><a class="headerlink" href="#hashlib.blake2b.MAX_DIGEST_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="hashlib.blake2s.MAX_DIGEST_SIZE">
<span class="sig-prename descclassname"><span class="pre">blake2s.</span></span><span class="sig-name descname"><span class="pre">MAX_DIGEST_SIZE</span></span><a class="headerlink" href="#hashlib.blake2s.MAX_DIGEST_SIZE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>Maximum digest size that the hash function can output.</p>
</section>
<section id="examples">
<h3>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h3>
<section id="simple-hashing">
<h4>Simple hashing<a class="headerlink" href="#simple-hashing" title="Link to this heading">¶</a></h4>
<p>To calculate hash of some data, you should first construct a hash object by
calling the appropriate constructor function (<a class="reference internal" href="#hashlib.blake2b" title="hashlib.blake2b"><code class="xref py py-func docutils literal notranslate"><span class="pre">blake2b()</span></code></a> or
<a class="reference internal" href="#hashlib.blake2s" title="hashlib.blake2s"><code class="xref py py-func docutils literal notranslate"><span class="pre">blake2s()</span></code></a>), then update it with the data by calling <a class="reference internal" href="#hashlib.hash.update" title="hashlib.hash.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">update()</span></code></a> on the
object, and, finally, get the digest out of the object by calling
<a class="reference internal" href="#hashlib.hash.digest" title="hashlib.hash.digest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">digest()</span></code></a> (or <a class="reference internal" href="#hashlib.hash.hexdigest" title="hashlib.hash.hexdigest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hexdigest()</span></code></a> for hex-encoded string).</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Hello world&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;6ff843ba685842aa82031d3f53c48b66326df7639a63d128974c5c14f31a0f33343a8c65551134ed1ae0f2b0dd2bb495dc81039e3eeb0aa1bb0388bbeac29183&#39;</span>
</pre></div>
</div>
<p>As a shortcut, you can pass the first chunk of data to update directly to the
constructor as the positional argument:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">blake2b</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Hello world&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;6ff843ba685842aa82031d3f53c48b66326df7639a63d128974c5c14f31a0f33343a8c65551134ed1ae0f2b0dd2bb495dc81039e3eeb0aa1bb0388bbeac29183&#39;</span>
</pre></div>
</div>
<p>You can call <a class="reference internal" href="#hashlib.hash.update" title="hashlib.hash.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hash.update()</span></code></a> as many times as you need to iteratively
update the hash:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">items</span> <span class="o">=</span> <span class="p">[</span><span class="sa">b</span><span class="s1">&#39;Hello&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39; &#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;world&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">items</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">item</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;6ff843ba685842aa82031d3f53c48b66326df7639a63d128974c5c14f31a0f33343a8c65551134ed1ae0f2b0dd2bb495dc81039e3eeb0aa1bb0388bbeac29183&#39;</span>
</pre></div>
</div>
</section>
<section id="using-different-digest-sizes">
<h4>Using different digest sizes<a class="headerlink" href="#using-different-digest-sizes" title="Link to this heading">¶</a></h4>
<p>BLAKE2 has configurable size of digests up to 64 bytes for BLAKE2b and up to 32
bytes for BLAKE2s. For example, to replace SHA-1 with BLAKE2b without changing
the size of output, we can tell BLAKE2b to produce 20-byte digests:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">20</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Replacing SHA1 with the more secure function&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;d24f26cf8de66472d58d4e1b1774b4c9158b1f4c&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">digest_size</span>
<span class="go">20</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">h</span><span class="o">.</span><span class="n">digest</span><span class="p">())</span>
<span class="go">20</span>
</pre></div>
</div>
<p>Hash objects with different digest sizes have completely different outputs
(shorter hashes are <em>not</em> prefixes of longer hashes); BLAKE2b and BLAKE2s
produce different outputs even if the output length is the same:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span><span class="p">,</span> <span class="n">blake2s</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;6fa1d8fcfd719046d762&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">11</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;eb6ec15daf9546254f0809&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">blake2s</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;1bf21a98c78a1c376ae9&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">blake2s</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">11</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;567004bf96e4a25773ebf4&#39;</span>
</pre></div>
</div>
</section>
<section id="keyed-hashing">
<h4>Keyed hashing<a class="headerlink" href="#keyed-hashing" title="Link to this heading">¶</a></h4>
<p>Keyed hashing can be used for authentication as a faster and simpler
replacement for <a class="reference external" href="https://en.wikipedia.org/wiki/HMAC">Hash-based message authentication code</a> (HMAC).
BLAKE2 can be securely used in prefix-MAC mode thanks to the
indifferentiability property inherited from BLAKE.</p>
<p>This example shows how to get a (hex-encoded) 128-bit authentication code for
message <code class="docutils literal notranslate"><span class="pre">b'message</span> <span class="pre">data'</span></code> with key <code class="docutils literal notranslate"><span class="pre">b'pseudorandom</span> <span class="pre">key'</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="sa">b</span><span class="s1">&#39;pseudorandom key&#39;</span><span class="p">,</span> <span class="n">digest_size</span><span class="o">=</span><span class="mi">16</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;message data&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;3d363ff7401e02026f4a4687d4863ced&#39;</span>
</pre></div>
</div>
<p>As a practical example, a web application can symmetrically sign cookies sent
to users and later verify them to make sure they weren’t tampered with:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hmac</span> <span class="kn">import</span> <span class="n">compare_digest</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">SECRET_KEY</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;pseudorandomly generated server secret key&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">AUTH_SIZE</span> <span class="o">=</span> <span class="mi">16</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">sign</span><span class="p">(</span><span class="n">cookie</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="n">AUTH_SIZE</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="n">SECRET_KEY</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">cookie</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">verify</span><span class="p">(</span><span class="n">cookie</span><span class="p">,</span> <span class="n">sig</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">good_sig</span> <span class="o">=</span> <span class="n">sign</span><span class="p">(</span><span class="n">cookie</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">compare_digest</span><span class="p">(</span><span class="n">good_sig</span><span class="p">,</span> <span class="n">sig</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cookie</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;user-alice&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sig</span> <span class="o">=</span> <span class="n">sign</span><span class="p">(</span><span class="n">cookie</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">{0}</span><span class="s2">,</span><span class="si">{1}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">cookie</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">),</span> <span class="n">sig</span><span class="p">))</span>
<span class="go">user-alice,b&#39;43b3c982cf697e0c5ab22172d1ca7421&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">verify</span><span class="p">(</span><span class="n">cookie</span><span class="p">,</span> <span class="n">sig</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">verify</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;user-bob&#39;</span><span class="p">,</span> <span class="n">sig</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">verify</span><span class="p">(</span><span class="n">cookie</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;0102030405060708090a0b0c0d0e0f00&#39;</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>Even though there’s a native keyed hashing mode, BLAKE2 can, of course, be used
in HMAC construction with <a class="reference internal" href="hmac.html#module-hmac" title="hmac: Keyed-Hashing for Message Authentication (HMAC) implementation"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code></a> module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">hmac</span><span class="o">,</span> <span class="nn">hashlib</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">hmac</span><span class="o">.</span><span class="n">new</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;secret key&#39;</span><span class="p">,</span> <span class="n">digestmod</span><span class="o">=</span><span class="n">hashlib</span><span class="o">.</span><span class="n">blake2s</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;message&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;e3c8102868d28b5ff85fc35dda07329970d1a01e273c37481326fe0c861c8142&#39;</span>
</pre></div>
</div>
</section>
<section id="randomized-hashing">
<h4>Randomized hashing<a class="headerlink" href="#randomized-hashing" title="Link to this heading">¶</a></h4>
<p>By setting <em>salt</em> parameter users can introduce randomization to the hash
function. Randomized hashing is useful for protecting against collision attacks
on the hash function used in digital signatures.</p>
<blockquote>
<div><p>Randomized hashing is designed for situations where one party, the message
preparer, generates all or part of a message to be signed by a second
party, the message signer. If the message preparer is able to find
cryptographic hash function collisions (i.e., two messages producing the
same hash value), then they might prepare meaningful versions of the message
that would produce the same hash value and digital signature, but with
different results (e.g., transferring $1,000,000 to an account, rather than
$10). Cryptographic hash functions have been designed with collision
resistance as a major goal, but the current concentration on attacking
cryptographic hash functions may result in a given cryptographic hash
function providing less collision resistance than expected. Randomized
hashing offers the signer additional protection by reducing the likelihood
that a preparer can generate two or more messages that ultimately yield the
same hash value during the digital signature generation process — even if
it is practical to find collisions for the hash function. However, the use
of randomized hashing may reduce the amount of security provided by a
digital signature when all portions of the message are prepared
by the signer.</p>
<p>(<a class="reference external" href="https://csrc.nist.gov/publications/detail/sp/800-106/archive/2009-02-25">NIST SP-800-106 “Randomized Hashing for Digital Signatures”</a>)</p>
</div></blockquote>
<p>In BLAKE2 the salt is processed as a one-time input to the hash function during
initialization, rather than as an input to each compression function.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><em>Salted hashing</em> (or just hashing) with BLAKE2 or any other general-purpose
cryptographic hash function, such as SHA-256, is not suitable for hashing
passwords.  See <a class="reference external" href="https://www.blake2.net/#qa">BLAKE2 FAQ</a> for more
information.</p>
</div>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">msg</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;some message&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Calculate the first hash with a random salt.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">salt1</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">urandom</span><span class="p">(</span><span class="n">blake2b</span><span class="o">.</span><span class="n">SALT_SIZE</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h1</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">salt</span><span class="o">=</span><span class="n">salt1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h1</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Calculate the second hash with a different random salt.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">salt2</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">urandom</span><span class="p">(</span><span class="n">blake2b</span><span class="o">.</span><span class="n">SALT_SIZE</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h2</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">salt</span><span class="o">=</span><span class="n">salt2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h2</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># The digests are different.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h1</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span> <span class="o">!=</span> <span class="n">h2</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
</section>
<section id="personalization">
<h4>Personalization<a class="headerlink" href="#personalization" title="Link to this heading">¶</a></h4>
<p>Sometimes it is useful to force hash function to produce different digests for
the same input for different purposes. Quoting the authors of the Skein hash
function:</p>
<blockquote>
<div><p>We recommend that all application designers seriously consider doing this;
we have seen many protocols where a hash that is computed in one part of
the protocol can be used in an entirely different part because two hash
computations were done on similar or related data, and the attacker can
force the application to make the hash inputs the same. Personalizing each
hash function used in the protocol summarily stops this type of attack.</p>
<p>(<a class="reference external" href="https://www.schneier.com/wp-content/uploads/2016/02/skein.pdf">The Skein Hash Function Family</a>,
p. 21)</p>
</div></blockquote>
<p>BLAKE2 can be personalized by passing bytes to the <em>person</em> argument:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">FILES_HASH_PERSON</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;MyApp Files Hash&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">BLOCK_HASH_PERSON</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;MyApp Block Hash&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">32</span><span class="p">,</span> <span class="n">person</span><span class="o">=</span><span class="n">FILES_HASH_PERSON</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;the same content&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;20d9cd024d4fb086aae819a1432dd2466de12947831b75c5a30cf2676095d3b4&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">32</span><span class="p">,</span> <span class="n">person</span><span class="o">=</span><span class="n">BLOCK_HASH_PERSON</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;the same content&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;cf68fb5761b9c44e7878bfb2c4c9aea52264a80b75005e65619778de59f383a3&#39;</span>
</pre></div>
</div>
<p>Personalization together with the keyed mode can also be used to derive different
keys from a single one.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2s</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">base64</span> <span class="kn">import</span> <span class="n">b64decode</span><span class="p">,</span> <span class="n">b64encode</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">orig_key</span> <span class="o">=</span> <span class="n">b64decode</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Rm5EPJai72qcK3RGBpW3vPNfZy5OZothY+kHY6h21KM=&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">enc_key</span> <span class="o">=</span> <span class="n">blake2s</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="n">orig_key</span><span class="p">,</span> <span class="n">person</span><span class="o">=</span><span class="sa">b</span><span class="s1">&#39;kEncrypt&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mac_key</span> <span class="o">=</span> <span class="n">blake2s</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="n">orig_key</span><span class="p">,</span> <span class="n">person</span><span class="o">=</span><span class="sa">b</span><span class="s1">&#39;kMAC&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">b64encode</span><span class="p">(</span><span class="n">enc_key</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="go">rbPb15S/Z9t+agffno5wuhB77VbRi6F9Iv2qIxU7WHw=</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">b64encode</span><span class="p">(</span><span class="n">mac_key</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="go">G9GtHFE1YluXY1zWPlYk1e/nWfu0WSEb0KRcjhDeP/o=</span>
</pre></div>
</div>
</section>
<section id="tree-mode">
<h4>Tree mode<a class="headerlink" href="#tree-mode" title="Link to this heading">¶</a></h4>
<p>Here’s an example of hashing a minimal tree with two leaf nodes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span>  <span class="mi">10</span>
 <span class="o">/</span>  \
<span class="mi">00</span>  <span class="mi">01</span>
</pre></div>
</div>
<p>This example uses 64-byte internal digests, and returns the 32-byte final
digest:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">blake2b</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">FANOUT</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">DEPTH</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">LEAF_SIZE</span> <span class="o">=</span> <span class="mi">4096</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">INNER_SIZE</span> <span class="o">=</span> <span class="mi">64</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">buf</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="mi">6000</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Left leaf</span>
<span class="gp">... </span><span class="n">h00</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">buf</span><span class="p">[</span><span class="mi">0</span><span class="p">:</span><span class="n">LEAF_SIZE</span><span class="p">],</span> <span class="n">fanout</span><span class="o">=</span><span class="n">FANOUT</span><span class="p">,</span> <span class="n">depth</span><span class="o">=</span><span class="n">DEPTH</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">leaf_size</span><span class="o">=</span><span class="n">LEAF_SIZE</span><span class="p">,</span> <span class="n">inner_size</span><span class="o">=</span><span class="n">INNER_SIZE</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">node_offset</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">node_depth</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">last_node</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Right leaf</span>
<span class="gp">... </span><span class="n">h01</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">buf</span><span class="p">[</span><span class="n">LEAF_SIZE</span><span class="p">:],</span> <span class="n">fanout</span><span class="o">=</span><span class="n">FANOUT</span><span class="p">,</span> <span class="n">depth</span><span class="o">=</span><span class="n">DEPTH</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">leaf_size</span><span class="o">=</span><span class="n">LEAF_SIZE</span><span class="p">,</span> <span class="n">inner_size</span><span class="o">=</span><span class="n">INNER_SIZE</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">node_offset</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">node_depth</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">last_node</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Root node</span>
<span class="gp">... </span><span class="n">h10</span> <span class="o">=</span> <span class="n">blake2b</span><span class="p">(</span><span class="n">digest_size</span><span class="o">=</span><span class="mi">32</span><span class="p">,</span> <span class="n">fanout</span><span class="o">=</span><span class="n">FANOUT</span><span class="p">,</span> <span class="n">depth</span><span class="o">=</span><span class="n">DEPTH</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">leaf_size</span><span class="o">=</span><span class="n">LEAF_SIZE</span><span class="p">,</span> <span class="n">inner_size</span><span class="o">=</span><span class="n">INNER_SIZE</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">node_offset</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">node_depth</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">last_node</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h10</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">h00</span><span class="o">.</span><span class="n">digest</span><span class="p">())</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h10</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">h01</span><span class="o">.</span><span class="n">digest</span><span class="p">())</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h10</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="go">&#39;3ad2a9b37c6070e374c7a8c508fe20ca86b6ed54e286e93a0318e95e881db5aa&#39;</span>
</pre></div>
</div>
</section>
</section>
<section id="credits">
<h3>Credits<a class="headerlink" href="#credits" title="Link to this heading">¶</a></h3>
<p><a class="reference external" href="https://www.blake2.net">BLAKE2</a> was designed by <em>Jean-Philippe Aumasson</em>, <em>Samuel Neves</em>, <em>Zooko
Wilcox-O’Hearn</em>, and <em>Christian Winnerlein</em> based on <a class="reference external" href="https://en.wikipedia.org/wiki/Secure_Hash_Algorithms">SHA-3</a> finalist <a class="reference external" href="https://web.archive.org/web/20200918190133/https://131002.net/blake/">BLAKE</a>
created by <em>Jean-Philippe Aumasson</em>, <em>Luca Henzen</em>, <em>Willi Meier</em>, and
<em>Raphael C.-W. Phan</em>.</p>
<p>It uses core algorithm from <a class="reference external" href="https://cr.yp.to/chacha.html">ChaCha</a> cipher designed by <em>Daniel J.  Bernstein</em>.</p>
<p>The stdlib implementation is based on <a class="reference external" href="https://pythonhosted.org/pyblake2/">pyblake2</a> module. It was written by
<em>Dmitry Chestnykh</em> based on C implementation written by <em>Samuel Neves</em>. The
documentation was copied from <a class="reference external" href="https://pythonhosted.org/pyblake2/">pyblake2</a> and written by <em>Dmitry Chestnykh</em>.</p>
<p>The C code was partly rewritten for Python by <em>Christian Heimes</em>.</p>
<p>The following public domain dedication applies for both C hash function
implementation, extension code, and this documentation:</p>
<blockquote>
<div><p>To the extent possible under law, the author(s) have dedicated all copyright
and related and neighboring rights to this software to the public domain
worldwide. This software is distributed without any warranty.</p>
<p>You should have received a copy of the CC0 Public Domain Dedication along
with this software. If not, see
<a class="reference external" href="https://creativecommons.org/publicdomain/zero/1.0/">https://creativecommons.org/publicdomain/zero/1.0/</a>.</p>
</div></blockquote>
<p>The following people have helped with development or contributed their changes
to the project and the public domain according to the Creative Commons Public
Domain Dedication 1.0 Universal:</p>
<ul class="simple">
<li><p><em>Alexandr Sokolovskiy</em></p></li>
</ul>
<div class="admonition seealso" id="hashlib-seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="hmac.html#module-hmac" title="hmac: Keyed-Hashing for Message Authentication (HMAC) implementation"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code></a></dt><dd><p>A module to generate message authentication codes using hashes.</p>
</dd>
<dt>Module <a class="reference internal" href="base64.html#module-base64" title="base64: RFC 4648: Base16, Base32, Base64 Data Encodings; Base85 and Ascii85"><code class="xref py py-mod docutils literal notranslate"><span class="pre">base64</span></code></a></dt><dd><p>Another way to encode binary hashes for non-binary environments.</p>
</dd>
<dt><a class="reference external" href="https://nvlpubs.nist.gov/nistpubs/fips/nist.fips.180-4.pdf">https://nvlpubs.nist.gov/nistpubs/fips/nist.fips.180-4.pdf</a></dt><dd><p>The FIPS 180-4 publication on Secure Hash Algorithms.</p>
</dd>
<dt><a class="reference external" href="https://csrc.nist.gov/publications/detail/fips/202/final">https://csrc.nist.gov/publications/detail/fips/202/final</a></dt><dd><p>The FIPS 202 publication on the SHA-3 Standard.</p>
</dd>
<dt><a class="reference external" href="https://www.blake2.net/">https://www.blake2.net/</a></dt><dd><p>Official BLAKE2 website.</p>
</dd>
<dt><a class="reference external" href="https://en.wikipedia.org/wiki/Cryptographic_hash_function">https://en.wikipedia.org/wiki/Cryptographic_hash_function</a></dt><dd><p>Wikipedia article with information on which algorithms have known issues
and what that means regarding their use.</p>
</dd>
<dt><a class="reference external" href="https://www.ietf.org/rfc/rfc8018.txt">https://www.ietf.org/rfc/rfc8018.txt</a></dt><dd><p>PKCS #5: Password-Based Cryptography Specification Version 2.1</p>
</dd>
<dt><a class="reference external" href="https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf">https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf</a></dt><dd><p>NIST Recommendation for Password-Based Key Derivation.</p>
</dd>
</dl>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code> — Secure hashes and message digests</a><ul>
<li><a class="reference internal" href="#hash-algorithms">Hash algorithms</a></li>
<li><a class="reference internal" href="#usage">Usage</a></li>
<li><a class="reference internal" href="#constructors">Constructors</a></li>
<li><a class="reference internal" href="#attributes">Attributes</a></li>
<li><a class="reference internal" href="#hash-objects">Hash Objects</a></li>
<li><a class="reference internal" href="#shake-variable-length-digests">SHAKE variable length digests</a></li>
<li><a class="reference internal" href="#file-hashing">File hashing</a></li>
<li><a class="reference internal" href="#key-derivation">Key derivation</a></li>
<li><a class="reference internal" href="#blake2">BLAKE2</a><ul>
<li><a class="reference internal" href="#creating-hash-objects">Creating hash objects</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#simple-hashing">Simple hashing</a></li>
<li><a class="reference internal" href="#using-different-digest-sizes">Using different digest sizes</a></li>
<li><a class="reference internal" href="#keyed-hashing">Keyed hashing</a></li>
<li><a class="reference internal" href="#randomized-hashing">Randomized hashing</a></li>
<li><a class="reference internal" href="#personalization">Personalization</a></li>
<li><a class="reference internal" href="#tree-mode">Tree mode</a></li>
</ul>
</li>
<li><a class="reference internal" href="#credits">Credits</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="crypto.html"
                          title="previous chapter">Cryptographic Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="hmac.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code> — Keyed-Hashing for Message Authentication</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/hashlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="hmac.html" title="hmac — Keyed-Hashing for Message Authentication"
             >next</a> |</li>
        <li class="right" >
          <a href="crypto.html" title="Cryptographic Services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="crypto.html" >Cryptographic Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code> — Secure hashes and message digests</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>