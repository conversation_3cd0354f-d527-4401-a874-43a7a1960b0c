<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.OdooLogo">
        <div t-attf-class="{{ props.class }}" t-attf-style="{{ props.style }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 919 495">
                <path d="M695,346a75,75,0,1,1,75-75A75,75,0,0,1,695,346Zm0-31a44,44,0,1,0-44-44A44,44,0,0,0,695,315ZM538,346a75,75,0,1,1,75-75A75,75,0,0,1,538,346Zm0-31a44,44,0,1,0-44-44A44,44,0,0,0,538,315Zm-82-45c0,41.9-33.6,76-75,76s-75-34-75-75.9S336.5,196,381,196c16.4,0,31.6,3.5,44,12.6V165.1c0-8.3,7.3-15.1,15.5-15.1s15.5,6.8,15.5,15.1Zm-75,45a44,44,0,1,0-44-44A44,44,0,0,0,381,315Z"
                    t-attf-style="fill:{{ props.monochrome ? '#FFF' : '#8f8f8f' }}"/>
                <path d="M224,346a75,75,0,1,1,75-75A75,75,0,0,1,224,346Zm0-31a44,44,0,1,0-44-44A44,44,0,0,0,224,315Z"
                    t-attf-style="fill:{{ props.monochrome ? '#FFF' : '#714b67' }}"/>
            </svg>
        </div>
    </t>
</templates>
