<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="os.path — Common pathname manipulations" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/os.path.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/posixpath.py(for POSIX) and Lib/ntpath.py(for Windows). This module implements some useful functions on pathnames. To read or write files see open(), and for accessing the filesyst..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/posixpath.py(for POSIX) and Lib/ntpath.py(for Windows). This module implements some useful functions on pathnames. To read or write files see open(), and for accessing the filesyst..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>os.path — Common pathname manipulations &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="fileinput — Iterate over lines from multiple input streams" href="fileinput.html" />
    <link rel="prev" title="pathlib — Object-oriented filesystem paths" href="pathlib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/os.path.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pathlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code> — Object-oriented filesystem paths</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="fileinput.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code> — Iterate over lines from multiple input streams</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/os.path.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fileinput.html" title="fileinput — Iterate over lines from multiple input streams"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pathlib.html" title="pathlib — Object-oriented filesystem paths"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" accesskey="U">File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code> — Common pathname manipulations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-os.path">
<span id="os-path-common-pathname-manipulations"></span><h1><a class="reference internal" href="#module-os.path" title="os.path: Operations on pathnames."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code></a> — Common pathname manipulations<a class="headerlink" href="#module-os.path" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/posixpath.py">Lib/posixpath.py</a> (for POSIX) and
<a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/ntpath.py">Lib/ntpath.py</a> (for Windows).</p>
<hr class="docutils" id="index-0" />
<p>This module implements some useful functions on pathnames. To read or write
files see <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>, and for accessing the filesystem see the <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a>
module. The path parameters can be passed as strings, or bytes, or any object
implementing the <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a> protocol.</p>
<p>Unlike a Unix shell, Python does not do any <em>automatic</em> path expansions.
Functions such as <a class="reference internal" href="#os.path.expanduser" title="os.path.expanduser"><code class="xref py py-func docutils literal notranslate"><span class="pre">expanduser()</span></code></a> and <a class="reference internal" href="#os.path.expandvars" title="os.path.expandvars"><code class="xref py py-func docutils literal notranslate"><span class="pre">expandvars()</span></code></a> can be invoked
explicitly when an application desires shell-like path expansion.  (See also
the <a class="reference internal" href="glob.html#module-glob" title="glob: Unix shell style pathname pattern expansion."><code class="xref py py-mod docutils literal notranslate"><span class="pre">glob</span></code></a> module.)</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The <a class="reference internal" href="pathlib.html#module-pathlib" title="pathlib: Object-oriented filesystem paths"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code></a> module offers high-level path objects.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>All of these functions accept either only bytes or only string objects as
their parameters.  The result is an object of the same type, if a path or
file name is returned.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Since different operating systems have different path name conventions, there
are several versions of this module in the standard library.  The
<a class="reference internal" href="#module-os.path" title="os.path: Operations on pathnames."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code></a> module is always the path module suitable for the operating
system Python is running on, and therefore usable for local paths.  However,
you can also import and use the individual modules if you want to manipulate
a path that is <em>always</em> in one of the different formats.  They all have the
same interface:</p>
<ul class="simple">
<li><p><code class="xref py py-mod docutils literal notranslate"><span class="pre">posixpath</span></code> for UNIX-style paths</p></li>
<li><p><code class="xref py py-mod docutils literal notranslate"><span class="pre">ntpath</span></code> for Windows paths</p></li>
</ul>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span><a class="reference internal" href="#os.path.exists" title="os.path.exists"><code class="xref py py-func docutils literal notranslate"><span class="pre">exists()</span></code></a>, <a class="reference internal" href="#os.path.lexists" title="os.path.lexists"><code class="xref py py-func docutils literal notranslate"><span class="pre">lexists()</span></code></a>, <a class="reference internal" href="#os.path.isdir" title="os.path.isdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">isdir()</span></code></a>, <a class="reference internal" href="#os.path.isfile" title="os.path.isfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">isfile()</span></code></a>,
<a class="reference internal" href="#os.path.islink" title="os.path.islink"><code class="xref py py-func docutils literal notranslate"><span class="pre">islink()</span></code></a>, and <a class="reference internal" href="#os.path.ismount" title="os.path.ismount"><code class="xref py py-func docutils literal notranslate"><span class="pre">ismount()</span></code></a> now return <code class="docutils literal notranslate"><span class="pre">False</span></code> instead of
raising an exception for paths that contain characters or bytes
unrepresentable at the OS level.</p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="os.path.abspath">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">abspath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.abspath" title="Link to this definition">¶</a></dt>
<dd><p>Return a normalized absolutized version of the pathname <em>path</em>. On most
platforms, this is equivalent to calling the function <a class="reference internal" href="#os.path.normpath" title="os.path.normpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">normpath()</span></code></a> as
follows: <code class="docutils literal notranslate"><span class="pre">normpath(join(os.getcwd(),</span> <span class="pre">path))</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.basename">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">basename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.basename" title="Link to this definition">¶</a></dt>
<dd><p>Return the base name of pathname <em>path</em>.  This is the second element of the
pair returned by passing <em>path</em> to the function <a class="reference internal" href="#os.path.split" title="os.path.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a>.  Note that
the result of this function is different
from the Unix <strong class="program">basename</strong> program; where <strong class="program">basename</strong> for
<code class="docutils literal notranslate"><span class="pre">'/foo/bar/'</span></code> returns <code class="docutils literal notranslate"><span class="pre">'bar'</span></code>, the <a class="reference internal" href="#os.path.basename" title="os.path.basename"><code class="xref py py-func docutils literal notranslate"><span class="pre">basename()</span></code></a> function returns an
empty string (<code class="docutils literal notranslate"><span class="pre">''</span></code>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.commonpath">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">commonpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">paths</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.commonpath" title="Link to this definition">¶</a></dt>
<dd><p>Return the longest common sub-path of each pathname in the sequence
<em>paths</em>.  Raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>paths</em> contain both absolute
and relative pathnames, the <em>paths</em> are on the different drives or
if <em>paths</em> is empty.  Unlike <a class="reference internal" href="#os.path.commonprefix" title="os.path.commonprefix"><code class="xref py py-func docutils literal notranslate"><span class="pre">commonprefix()</span></code></a>, this returns a
valid path.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a sequence of <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like objects</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.commonprefix">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">commonprefix</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.commonprefix" title="Link to this definition">¶</a></dt>
<dd><p>Return the longest path prefix (taken character-by-character) that is a
prefix of all paths in  <em>list</em>.  If <em>list</em> is empty, return the empty string
(<code class="docutils literal notranslate"><span class="pre">''</span></code>).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function may return invalid paths because it works a
character at a time.  To obtain a valid path, see
<a class="reference internal" href="#os.path.commonpath" title="os.path.commonpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">commonpath()</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">commonprefix</span><span class="p">([</span><span class="s1">&#39;/usr/lib&#39;</span><span class="p">,</span> <span class="s1">&#39;/usr/local/lib&#39;</span><span class="p">])</span>
<span class="go">&#39;/usr/l&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">commonpath</span><span class="p">([</span><span class="s1">&#39;/usr/lib&#39;</span><span class="p">,</span> <span class="s1">&#39;/usr/local/lib&#39;</span><span class="p">])</span>
<span class="go">&#39;/usr&#39;</span>
</pre></div>
</div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.dirname">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">dirname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.dirname" title="Link to this definition">¶</a></dt>
<dd><p>Return the directory name of pathname <em>path</em>.  This is the first element of
the pair returned by passing <em>path</em> to the function <a class="reference internal" href="#os.path.split" title="os.path.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.exists">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">exists</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.exists" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> refers to an existing path or an open
file descriptor.  Returns <code class="docutils literal notranslate"><span class="pre">False</span></code> for broken symbolic links.  On
some platforms, this function may return <code class="docutils literal notranslate"><span class="pre">False</span></code> if permission is
not granted to execute <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a> on the requested file, even
if the <em>path</em> physically exists.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>path</em> can now be an integer: <code class="docutils literal notranslate"><span class="pre">True</span></code> is returned if it is an
 open file descriptor, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.lexists">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">lexists</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.lexists" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> refers to an existing path. Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> for
broken symbolic links.   Equivalent to <a class="reference internal" href="#os.path.exists" title="os.path.exists"><code class="xref py py-func docutils literal notranslate"><span class="pre">exists()</span></code></a> on platforms lacking
<a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function" id="index-1">
<dt class="sig sig-object py" id="os.path.expanduser">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">expanduser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.expanduser" title="Link to this definition">¶</a></dt>
<dd><p>On Unix and Windows, return the argument with an initial component of <code class="docutils literal notranslate"><span class="pre">~</span></code> or
<code class="docutils literal notranslate"><span class="pre">~user</span></code> replaced by that <em>user</em>’s home directory.</p>
<p id="index-2">On Unix, an initial <code class="docutils literal notranslate"><span class="pre">~</span></code> is replaced by the environment variable <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">HOME</span></code>
if it is set; otherwise the current user’s home directory is looked up in the
password directory through the built-in module <a class="reference internal" href="pwd.html#module-pwd" title="pwd: The password database (getpwnam() and friends). (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pwd</span></code></a>. An initial <code class="docutils literal notranslate"><span class="pre">~user</span></code>
is looked up directly in the password directory.</p>
<p>On Windows, <span class="target" id="index-4"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">USERPROFILE</span></code> will be used if set, otherwise a combination
of <span class="target" id="index-5"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">HOMEPATH</span></code> and <span class="target" id="index-6"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">HOMEDRIVE</span></code> will be used.  An initial
<code class="docutils literal notranslate"><span class="pre">~user</span></code> is handled by checking that the last directory component of the current
user’s home directory matches <span class="target" id="index-7"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">USERNAME</span></code>, and replacing it if so.</p>
<p>If the expansion fails or if the path does not begin with a tilde, the path is
returned unchanged.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>No longer uses <span class="target" id="index-8"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">HOME</span></code> on Windows.</p>
</div>
</dd></dl>

<dl class="py function" id="index-9">
<dt class="sig sig-object py" id="os.path.expandvars">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">expandvars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.expandvars" title="Link to this definition">¶</a></dt>
<dd><p>Return the argument with environment variables expanded.  Substrings of the form
<code class="docutils literal notranslate"><span class="pre">$name</span></code> or <code class="docutils literal notranslate"><span class="pre">${name}</span></code> are replaced by the value of environment variable
<em>name</em>.  Malformed variable names and references to non-existing variables are
left unchanged.</p>
<p>On Windows, <code class="docutils literal notranslate"><span class="pre">%name%</span></code> expansions are supported in addition to <code class="docutils literal notranslate"><span class="pre">$name</span></code> and
<code class="docutils literal notranslate"><span class="pre">${name}</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.getatime">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">getatime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.getatime" title="Link to this definition">¶</a></dt>
<dd><p>Return the time of last access of <em>path</em>.  The return value is a floating point number giving
the number of seconds since the epoch (see the  <a class="reference internal" href="time.html#module-time" title="time: Time access and conversions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code></a> module).  Raise
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the file does not exist or is inaccessible.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.getmtime">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">getmtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.getmtime" title="Link to this definition">¶</a></dt>
<dd><p>Return the time of last modification of <em>path</em>.  The return value is a floating point number
giving the number of seconds since the epoch (see the  <a class="reference internal" href="time.html#module-time" title="time: Time access and conversions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code></a> module).
Raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the file does not exist or is inaccessible.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.getctime">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">getctime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.getctime" title="Link to this definition">¶</a></dt>
<dd><p>Return the system’s ctime which, on some systems (like Unix) is the time of the
last metadata change, and, on others (like Windows), is the creation time for <em>path</em>.
The return value is a number giving the number of seconds since the epoch (see
the  <a class="reference internal" href="time.html#module-time" title="time: Time access and conversions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code></a> module).  Raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the file does not exist or
is inaccessible.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.getsize">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">getsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.getsize" title="Link to this definition">¶</a></dt>
<dd><p>Return the size, in bytes, of <em>path</em>.  Raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the file does
not exist or is inaccessible.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.isabs">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">isabs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.isabs" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> is an absolute pathname.  On Unix, that means it
begins with a slash, on Windows that it begins with a (back)slash after chopping
off a potential drive letter.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.isfile">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">isfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.isfile" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> is an <a class="reference internal" href="#os.path.exists" title="os.path.exists"><code class="xref py py-func docutils literal notranslate"><span class="pre">existing</span></code></a> regular file.
This follows symbolic links, so both <a class="reference internal" href="#os.path.islink" title="os.path.islink"><code class="xref py py-func docutils literal notranslate"><span class="pre">islink()</span></code></a> and <a class="reference internal" href="#os.path.isfile" title="os.path.isfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">isfile()</span></code></a> can
be true for the same path.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.isdir">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">isdir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.isdir" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> is an <a class="reference internal" href="#os.path.exists" title="os.path.exists"><code class="xref py py-func docutils literal notranslate"><span class="pre">existing</span></code></a> directory.  This
follows symbolic links, so both <a class="reference internal" href="#os.path.islink" title="os.path.islink"><code class="xref py py-func docutils literal notranslate"><span class="pre">islink()</span></code></a> and <a class="reference internal" href="#os.path.isdir" title="os.path.isdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">isdir()</span></code></a> can be true
for the same path.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.isjunction">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">isjunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.isjunction" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> refers to an <a class="reference internal" href="#os.path.lexists" title="os.path.lexists"><code class="xref py py-func docutils literal notranslate"><span class="pre">existing</span></code></a> directory
entry that is a junction.  Always return <code class="docutils literal notranslate"><span class="pre">False</span></code> if junctions are not
supported on the current platform.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.islink">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">islink</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.islink" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>path</em> refers to an <a class="reference internal" href="#os.path.exists" title="os.path.exists"><code class="xref py py-func docutils literal notranslate"><span class="pre">existing</span></code></a> directory
entry that is a symbolic link.  Always <code class="docutils literal notranslate"><span class="pre">False</span></code> if symbolic links are not
supported by the Python runtime.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.ismount">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">ismount</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.ismount" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if pathname <em>path</em> is a <em class="dfn">mount point</em>: a point in a
file system where a different file system has been mounted.  On POSIX, the
function checks whether <em>path</em>’s parent, <code class="file docutils literal notranslate"><em><span class="pre">path</span></em><span class="pre">/..</span></code>, is on a different
device than <em>path</em>, or whether <code class="file docutils literal notranslate"><em><span class="pre">path</span></em><span class="pre">/..</span></code> and <em>path</em> point to the same
i-node on the same device — this should detect mount points for all Unix
and POSIX variants.  It is not able to reliably detect bind mounts on the
same filesystem.  On Windows, a drive letter root and a share UNC are
always mount points, and for any other path <code class="docutils literal notranslate"><span class="pre">GetVolumePathName</span></code> is called
to see if it is different from the input path.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>Support for detecting non-root mount points on Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.isdevdrive">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">isdevdrive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.isdevdrive" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if pathname <em>path</em> is located on a Windows Dev Drive.
A Dev Drive is optimized for developer scenarios, and offers faster
performance for reading and writing files. It is recommended for use for
source code, temporary build directories, package caches, and other
IO-intensive operations.</p>
<p>May raise an error for an invalid path, for example, one without a
recognizable drive, but returns <code class="docutils literal notranslate"><span class="pre">False</span></code> on platforms that do not support
Dev Drives. See <a class="reference external" href="https://learn.microsoft.com/windows/dev-drive/">the Windows documentation</a>
for information on enabling and creating Dev Drives.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.join">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">join</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">paths</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.join" title="Link to this definition">¶</a></dt>
<dd><p>Join one or more path segments intelligently.  The return value is the
concatenation of <em>path</em> and all members of <em>*paths</em>, with exactly one
directory separator following each non-empty part, except the last. That is,
the result will only end in a separator if the last part is either empty or
ends in a separator. If a segment is an absolute path (which on Windows
requires both a drive and a root), then all previous segments are ignored and
joining continues from the absolute path segment.</p>
<p>On Windows, the drive is not reset when a rooted path segment (e.g.,
<code class="docutils literal notranslate"><span class="pre">r'\foo'</span></code>) is encountered. If a segment is on a different drive or is an
absolute path, all previous segments are ignored and the drive is reset. Note
that since there is a current directory for each drive,
<code class="docutils literal notranslate"><span class="pre">os.path.join(&quot;c:&quot;,</span> <span class="pre">&quot;foo&quot;)</span></code> represents a path relative to the current
directory on drive <code class="file docutils literal notranslate"><span class="pre">C:</span></code> (<code class="file docutils literal notranslate"><span class="pre">c:foo</span></code>), not <code class="file docutils literal notranslate"><span class="pre">c:\foo</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> for <em>path</em> and <em>paths</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.normcase">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">normcase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.normcase" title="Link to this definition">¶</a></dt>
<dd><p>Normalize the case of a pathname.  On Windows, convert all characters in the
pathname to lowercase, and also convert forward slashes to backward slashes.
On other operating systems, return the path unchanged.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.normpath">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">normpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.normpath" title="Link to this definition">¶</a></dt>
<dd><blockquote>
<div><p>Normalize a pathname by collapsing redundant separators and up-level
references so that <code class="docutils literal notranslate"><span class="pre">A//B</span></code>, <code class="docutils literal notranslate"><span class="pre">A/B/</span></code>, <code class="docutils literal notranslate"><span class="pre">A/./B</span></code> and <code class="docutils literal notranslate"><span class="pre">A/foo/../B</span></code> all
become <code class="docutils literal notranslate"><span class="pre">A/B</span></code>.  This string manipulation may change the meaning of a path
that contains symbolic links.  On Windows, it converts forward slashes to
backward slashes. To normalize case, use <a class="reference internal" href="#os.path.normcase" title="os.path.normcase"><code class="xref py py-func docutils literal notranslate"><span class="pre">normcase()</span></code></a>.</p>
</div></blockquote>
<div class="admonition note">
<p class="admonition-title">Note</p>
<blockquote>
<div><p>On POSIX systems, in accordance with <a class="reference external" href="https://pubs.opengroup.org/onlinepubs/9699919799/basedefs/V1_chap04.html#tag_04_13">IEEE Std 1003.1 2013 Edition; 4.13
Pathname Resolution</a>,
if a pathname begins with exactly two slashes, the first component
following the leading characters may be interpreted in an implementation-defined
manner, although more than two leading characters shall be treated as a
single character.</p>
</div></blockquote>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.realpath">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">realpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.realpath" title="Link to this definition">¶</a></dt>
<dd><p>Return the canonical path of the specified filename, eliminating any symbolic
links encountered in the path (if they are supported by the operating
system).</p>
<p>If a path doesn’t exist or a symlink loop is encountered, and <em>strict</em> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised. If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the path is
resolved as far as possible and any remainder is appended without checking
whether it exists.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function emulates the operating system’s procedure for making a path
canonical, which differs slightly between Windows and UNIX with respect
to how links and subsequent path components interact.</p>
<p>Operating system APIs make paths canonical as needed, so it’s not
normally necessary to call this function.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Symbolic links and junctions are now resolved on Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>strict</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.relpath">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">relpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">os.curdir</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.relpath" title="Link to this definition">¶</a></dt>
<dd><p>Return a relative filepath to <em>path</em> either from the current directory or
from an optional <em>start</em> directory.  This is a path computation:  the
filesystem is not accessed to confirm the existence or nature of <em>path</em> or
<em>start</em>.  On Windows, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised when <em>path</em> and <em>start</em>
are on different drives.</p>
<p><em>start</em> defaults to <a class="reference internal" href="os.html#os.curdir" title="os.curdir"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.curdir</span></code></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.samefile">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">samefile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.samefile" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if both pathname arguments refer to the same file or directory.
This is determined by the device number and i-node number and raises an
exception if an <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a> call on either pathname fails.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added Windows support.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Windows now uses the same implementation as all other platforms.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.sameopenfile">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">sameopenfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.sameopenfile" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the file descriptors <em>fp1</em> and <em>fp2</em> refer to the same file.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added Windows support.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.samestat">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">samestat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stat1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stat2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.samestat" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the stat tuples <em>stat1</em> and <em>stat2</em> refer to the same file.
These structures may have been returned by <a class="reference internal" href="os.html#os.fstat" title="os.fstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fstat()</span></code></a>,
<a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a>, or <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>.  This function implements the
underlying comparison used by <a class="reference internal" href="#os.path.samefile" title="os.path.samefile"><code class="xref py py-func docutils literal notranslate"><span class="pre">samefile()</span></code></a> and <a class="reference internal" href="#os.path.sameopenfile" title="os.path.sameopenfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">sameopenfile()</span></code></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added Windows support.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.split">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.split" title="Link to this definition">¶</a></dt>
<dd><p>Split the pathname <em>path</em> into a pair, <code class="docutils literal notranslate"><span class="pre">(head,</span> <span class="pre">tail)</span></code> where <em>tail</em> is the
last pathname component and <em>head</em> is everything leading up to that.  The
<em>tail</em> part will never contain a slash; if <em>path</em> ends in a slash, <em>tail</em>
will be empty.  If there is no slash in <em>path</em>, <em>head</em> will be empty.  If
<em>path</em> is empty, both <em>head</em> and <em>tail</em> are empty.  Trailing slashes are
stripped from <em>head</em> unless it is the root (one or more slashes only).  In
all cases, <code class="docutils literal notranslate"><span class="pre">join(head,</span> <span class="pre">tail)</span></code> returns a path to the same location as <em>path</em>
(but the strings may differ).  Also see the functions <a class="reference internal" href="#os.path.dirname" title="os.path.dirname"><code class="xref py py-func docutils literal notranslate"><span class="pre">dirname()</span></code></a> and
<a class="reference internal" href="#os.path.basename" title="os.path.basename"><code class="xref py py-func docutils literal notranslate"><span class="pre">basename()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.splitdrive">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">splitdrive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.splitdrive" title="Link to this definition">¶</a></dt>
<dd><p>Split the pathname <em>path</em> into a pair <code class="docutils literal notranslate"><span class="pre">(drive,</span> <span class="pre">tail)</span></code> where <em>drive</em> is either
a mount point or the empty string.  On systems which do not use drive
specifications, <em>drive</em> will always be the empty string.  In all cases, <code class="docutils literal notranslate"><span class="pre">drive</span>
<span class="pre">+</span> <span class="pre">tail</span></code> will be the same as <em>path</em>.</p>
<p>On Windows, splits a pathname into drive/UNC sharepoint and relative path.</p>
<p>If the path contains a drive letter, drive will contain everything
up to and including the colon:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitdrive</span><span class="p">(</span><span class="s2">&quot;c:/dir&quot;</span><span class="p">)</span>
<span class="go">(&quot;c:&quot;, &quot;/dir&quot;)</span>
</pre></div>
</div>
<p>If the path contains a UNC path, drive will contain the host name
and share:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitdrive</span><span class="p">(</span><span class="s2">&quot;//host/computer/dir&quot;</span><span class="p">)</span>
<span class="go">(&quot;//host/computer&quot;, &quot;/dir&quot;)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.splitroot">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">splitroot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.splitroot" title="Link to this definition">¶</a></dt>
<dd><p>Split the pathname <em>path</em> into a 3-item tuple <code class="docutils literal notranslate"><span class="pre">(drive,</span> <span class="pre">root,</span> <span class="pre">tail)</span></code> where
<em>drive</em> is a device name or mount point, <em>root</em> is a string of separators
after the drive, and <em>tail</em> is everything after the root. Any of these
items may be the empty string. In all cases, <code class="docutils literal notranslate"><span class="pre">drive</span> <span class="pre">+</span> <span class="pre">root</span> <span class="pre">+</span> <span class="pre">tail</span></code> will
be the same as <em>path</em>.</p>
<p>On POSIX systems, <em>drive</em> is always empty. The <em>root</em> may be empty (if <em>path</em> is
relative), a single forward slash (if <em>path</em> is absolute), or two forward slashes
(implementation-defined per <a class="reference external" href="https://pubs.opengroup.org/onlinepubs/9699919799/basedefs/V1_chap04.html#tag_04_13">IEEE Std 1003.1-2017; 4.13 Pathname Resolution</a>.)
For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitroot</span><span class="p">(</span><span class="s1">&#39;/home/<USER>/span><span class="p">)</span>
<span class="go">(&#39;&#39;, &#39;/&#39;, &#39;home/sam&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">splitroot</span><span class="p">(</span><span class="s1">&#39;//home/<USER>/span><span class="p">)</span>
<span class="go">(&#39;&#39;, &#39;//&#39;, &#39;home/sam&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">splitroot</span><span class="p">(</span><span class="s1">&#39;///home/<USER>/span><span class="p">)</span>
<span class="go">(&#39;&#39;, &#39;/&#39;, &#39;//home/<USER>/span>
</pre></div>
</div>
<p>On Windows, <em>drive</em> may be empty, a drive-letter name, a UNC share, or a device
name. The <em>root</em> may be empty, a forward slash, or a backward slash. For
example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitroot</span><span class="p">(</span><span class="s1">&#39;C:/Users/<USER>/span><span class="p">)</span>
<span class="go">(&#39;C:&#39;, &#39;/&#39;, &#39;Users/Sam&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">splitroot</span><span class="p">(</span><span class="s1">&#39;//Server/Share/Users/<USER>/span><span class="p">)</span>
<span class="go">(&#39;//Server/Share&#39;, &#39;/&#39;, &#39;Users/Sam&#39;)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="os.path.splitext">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">splitext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#os.path.splitext" title="Link to this definition">¶</a></dt>
<dd><p>Split the pathname <em>path</em> into a pair <code class="docutils literal notranslate"><span class="pre">(root,</span> <span class="pre">ext)</span></code>  such that <code class="docutils literal notranslate"><span class="pre">root</span> <span class="pre">+</span> <span class="pre">ext</span> <span class="pre">==</span>
<span class="pre">path</span></code>, and the extension, <em>ext</em>, is empty or begins with a period and contains at
most one period.</p>
<p>If the path contains no extension, <em>ext</em> will be <code class="docutils literal notranslate"><span class="pre">''</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitext</span><span class="p">(</span><span class="s1">&#39;bar&#39;</span><span class="p">)</span>
<span class="go">(&#39;bar&#39;, &#39;&#39;)</span>
</pre></div>
</div>
<p>If the path contains an extension, then <em>ext</em> will be set to this extension,
including the leading period. Note that previous periods will be ignored:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitext</span><span class="p">(</span><span class="s1">&#39;foo.bar.exe&#39;</span><span class="p">)</span>
<span class="go">(&#39;foo.bar&#39;, &#39;.exe&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">splitext</span><span class="p">(</span><span class="s1">&#39;/foo/bar.exe&#39;</span><span class="p">)</span>
<span class="go">(&#39;/foo/bar&#39;, &#39;.exe&#39;)</span>
</pre></div>
</div>
<p>Leading periods of the last component of the path are considered to
be part of the root:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">splitext</span><span class="p">(</span><span class="s1">&#39;.cshrc&#39;</span><span class="p">)</span>
<span class="go">(&#39;.cshrc&#39;, &#39;&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">splitext</span><span class="p">(</span><span class="s1">&#39;/foo/....jpg&#39;</span><span class="p">)</span>
<span class="go">(&#39;/foo/....jpg&#39;, &#39;&#39;)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="os.path.supports_unicode_filenames">
<span class="sig-prename descclassname"><span class="pre">os.path.</span></span><span class="sig-name descname"><span class="pre">supports_unicode_filenames</span></span><a class="headerlink" href="#os.path.supports_unicode_filenames" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if arbitrary Unicode strings can be used as file names (within limitations
imposed by the file system).</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pathlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code> — Object-oriented filesystem paths</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="fileinput.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code> — Iterate over lines from multiple input streams</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/os.path.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fileinput.html" title="fileinput — Iterate over lines from multiple input streams"
             >next</a> |</li>
        <li class="right" >
          <a href="pathlib.html" title="pathlib — Object-oriented filesystem paths"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" >File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code> — Common pathname manipulations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>