# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:27+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
msgid "# Events"
msgstr "# Événements"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_count_done
msgid "# Sent"
msgstr "# Envoyés"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%(event_name)s (%(count)s seats remaining)"
msgstr "%(event_name)s (%(count)s places restantes)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%(event_name)s (Sold out)"
msgstr "%(event_name)s (Complet)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "%(ticket_name)s (%(count)s seats remaining)"
msgstr "%(ticket_name)s (%(count)s places restantes)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "%(ticket_name)s (Sold out)"
msgstr "%(ticket_name)s (Épuisés)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_foldable_badge
msgid ""
"'Foldable Badge - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), (object.name or '').replace('/',''))"
msgstr ""
"'Badge pliable - %s - %s' % ((object.event_id.name or "
"'Événement').replace('/',''), (object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_foldable_badge
msgid "'Foldable Badge - %s' % (object.name or 'Event').replace('/','')"
msgstr "'Badge pliable - %s' % (object.name or 'Événement').replace('/','')"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_full_page_ticket
msgid ""
"'Full Page Ticket - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), (object.name or '').replace('/',''))"
msgstr ""
"'Ticket pleine page - %s - %s' % ((object.event_id.name or "
"'Événement').replace('/',''), (object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_full_page_ticket
msgid "'Full Page Ticket - %s' % (object.name or 'Event').replace('/','')"
msgstr ""
" 'Ticket pleine page - %s' % (object.name or 'Événement').replace('/','')"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "- \"%(event_name)s\": Missing %(nb_too_many)i seats."
msgstr "- \"%(event_name)s\":  %(nb_too_many)i places manquantes."

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"- the ticket \"%(ticket_name)s\" (%(event_name)s): Missing %(nb_too_many)i "
"seats."
msgstr ""
"- le ticket \"%(ticket_name)s\" (%(event_name)s):  %(nb_too_many)i places "
"manquantes."

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_2
msgid "10-14"
msgstr "10-14"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_3
msgid "15-18"
msgstr "15-18"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_4
msgid "18+"
msgstr "18+"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_1
msgid "5-10"
msgstr "5-10"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""
"<b>Salle Business</b> - Pour discuter des méthodologies de mise en œuvre, "
"des meilleures pratiques de vente, etc."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr ""
"<b>Salles techniques</b> - une dédiée aux développeurs Odoo avancés, une aux"
" nouveaux développeurs."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr ""
"<b>La Foire du Design est précédée par 2 jours de Sessions de Formation pour"
" les experts !</b><br> Nous proposons 3 sessions de différentes formations, "
"qui se déroulent chacune sur 2 jours."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr ""
"<b>Les séances plénières du matin seront plus courtes</b> et nous donnerons "
"plus de temps pour des réunions thématiques, des conférences, des ateliers "
"et des séances de tutorat l'après-midi."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""
"<b>L'ensemble de l'événement est ouvert à tout public !</b><br> Nous "
"demandons des frais de participation de 49,50 € pour le coût des 3 jours "
"(pauses café, restauration, boissons et une soirée concert et bières). "
"<br>Pour ceux qui ne veulent pas contribuer, il y existe un ticket gratuit, "
"mais la restauration et l'accès aux soirées ne sont pas inclus."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr "<b>Salle de séminaire</b>- essentiellement pour les développeurs."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                            <span class=\"text-muted\">Air your tracks online through a Youtube integration</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"text-muted\">Diffuser vos sessions en ligne via une intégration avec Youtube</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                            <span class=\"text-muted\">Share a quiz to your attendees once a track is over</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"text-muted\">Partager un quiz avec vos participants à l'issue d'une session</span>"

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br>\n"
"    Thank you for your inquiry.<br>\n"
"    Here is your badge for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.<br>\n"
"    If you have any questions, please let us know.\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    Cher <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br>\n"
"    Merci pour votre demande.<br>\n"
"    Voici votre badge pour l'événement <t t-out=\"object.event_id.name or ''\">Révélation en ligne de la collection OpenWood</t>.<br>\n"
"    N'hésitez pas à nous contacter si vous avez des questions.\n"
"    <br><br>\n"
"    Merci,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>If you wish to make a presentation, please send your topic proposal as "
"soon as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany "
"(dot) com. The presentations should be, for example, a presentation of a "
"community module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English.</em>"
msgstr ""
"<em>Si vous souhaitez effectuer une présentation, envoyez vos suggestions de"
" sujets dès que possible à M. Famke Jenssens à l'adresse ngh (a) yourcompany"
" (dot) com. Les présentations doivent par exemple porter sur un module de la"
" communauté, une étude de cas, des commentaires de méthodologie, des "
"techniques, etc. Chaque présentation doit être effectuée en anglais.</em>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-arrow-right me-2 o_event_fontsize_09\" title=\"End date\"/>"
msgstr ""
"<i class=\"fa fa-arrow-right me-2 o_event_fontsize_09\" title=\"Date de "
"fin\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirmer l'inscription\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirmer l'inscription\"/>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle me-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr ""
"<i class=\"fa fa-info-circle me-2\"></i>Cet événement et toutes les "
"conférences se déroulent en <b>anglais</b>!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-level-up fa-rotate-90\" title=\"Confirmed\"/>"
msgstr "<i class=\"fa fa-level-up fa-rotate-90\" title=\"Confirmé\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-map-marker mt-1 me-1\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker mt-1 me-1\" title=\"Lieu\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-user-plus fa-3x\" role=\"img\" aria-label=\"Attended "
"button\" title=\"Confirm Attendance\"/>"
msgstr ""
"<i class=\"fa fa-user-plus fa-3x\" role=\"img\" aria-label=\"Attended "
"button\" title=\"Confirmer la participation\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-user-plus\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Confirm Attendance\"/>"
msgstr ""
"<i class=\"fa fa-user-plus\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Confirmer la participation\"/>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-"
"end\">SPEAKER</span>"
msgstr ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-"
"end\">INTERVENANT</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<span class=\"bg-danger\">Archived</span>"
msgstr "<span class=\"bg-danger\">Archivé</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Les valeurs définies ici sont"
" propres à l'entreprise.\" groups=\"base.group_multi_company\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Registration statistics\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Statistiques d'inscription\n"
"                            </span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<span class=\"text-muted\" states=\"done\">Attended</span>\n"
"                                    <span class=\"text-muted\" states=\"cancel\">Canceled</span>"
msgstr ""
"<span class=\"text-muted\" states=\"done\">Présent</span>\n"
"                                    <span class=\"text-muted\" states=\"cancel\">Annulé</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span>John Doe</span>"
msgstr "<span>John Doe</span>"

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Oscar Morgan</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-attf-href=\"{{ object.event_id.website_url }}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Event\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br>\n"
"                        We are excited to remind you that the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        is starting <strong t-out=\"object.get_date_range_str() or ''\">today</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\"></t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Get the best mobile experience.</strong>\n"
"                        <a href=\"/event\">Install our mobile app</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"></i>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                See location on Google Maps\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br>\n"
"            Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Votre inscription</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Oscar Morgan</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-attf-href=\"{{ object.event_id.website_url }}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            Voir l'événement\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Bonjour <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br>\n"
"                        Nous avons le plaisir de vous rappeler que l'événement\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">Révélation en ligne de la collection OpenWood</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">Révélation en ligne de la collection OpenWood</strong>\n"
"                        </t>\n"
"                        commence <strong t-out=\"object.get_date_range_str() or ''\">aujourd'hui</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Ajouter cet événement à votre calendrier</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        Nous confirmons votre inscription et espérons vous y voir,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            L'équipe <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>Du</strong> <t t-out=\"object.event_id.date_begin_located or ''\">4 mai 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>Au</strong> <t t-out=\"object.event_id.date_end_located or ''\">6 mai 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Europe/Bruxelles</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Des questions ?</span>\n"
"                            <div>Veuillez contacter l'organisateur :</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Adresse e-mail : <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Téléphone : <t t-out=\"event_organizer.phone or ''\"></t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Bénéficiez de la meilleure expérience mobile.</strong>\n"
"                        <a href=\"/event\">Installez notre application mobile</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"></i>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                               Voir le lieu sur Google Maps\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Envoyé par <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br>\n"
"            Découvrez <a href=\"/event\" style=\"color:#875A7B;\">tous nos événements</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Oscar Morgan</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-att-href=\"object.event_id.website_url\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Event\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br>\n"
"                        We are happy to confirm your registration to the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        for attendee <t t-out=\"object.name or ''\">Oscar Morgan</t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"></i>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    See location on Google Maps\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Votre inscription</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Oscar Morgan</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-att-href=\"object.event_id.website_url\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            Voir l'événement\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Bonjour <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br>\n"
"                        Nous avons le plaisir de confirmer votre inscription à l'événement\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">Révélation en ligne de la collection OpenWood</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">Révélation en ligne de la collection OpenWood</strong>\n"
"                        </t>\n"
"                        pour le participant <t t-out=\"object.name or ''\">Oscar Morgan</t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Ajouter cet événement à votre calendrier</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        À bientôt,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            L'équipe <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>Du</strong> <t t-out=\"object.event_id.date_begin_located or ''\">4 mai 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>Au</strong> <t t-out=\"object.event_id.date_end_located or ''\">6 mai 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Bruxelles</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Des questions ?</span>\n"
"                            <div>Veuillez contacter l'organisateur :</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Adresse e-mail : <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Téléphone : <t t-out=\"event_organizer.phone or ''\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Bénéficiez de la meilleure expérience mobile.</strong>\n"
"                            <a href=\"/event\">Installez notre application mobile</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"></i>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    Voir le lieu sur Google Maps\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Envoyé par <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br>\n"
"                Découvrez <a href=\"/event\" style=\"color:#875A7B;\">tous nos événements</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__description
#: model:ir.model.fields,help:event.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "Description du ticket à communiquer à vos clients."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
#: model:ir.model.fields,field_description:event.field_event_registration__active
msgid "Active"
msgstr "Actif"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_2
msgid "Activity"
msgstr "Activité"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "Status de l'activité"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Add a description..."
msgstr "Ajouter une description…"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""
"Ajouter un menu de navigation aux pages Web de votre événement avec un "
"calendrier, des sessions, un formulaire de proposition de sessions, etc."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Add some internal notes (to do lists, contact info, ...)"
msgstr "Ajoutez quelques notes internes (listes de tâches, coordonnées, ...)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_search
msgid "Address"
msgstr "Adresse"

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_exhibitor
msgid "Advanced Sponsors"
msgstr "Sponsors avancés"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "Après chaque inscription"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "Après l'événement"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_1
msgid "Age"
msgstr "Âge"

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr "Un évènement non publié"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"And this time, we go fully ONLINE! Meet us in our live streams from the comfort of your house.<br>\n"
"        Special discount codes will be handed out during the various streams, make sure to be there on time."
msgstr ""
"Et cette fois-ci, cela se passe entièrement EN LIGNE ! Rejoignez-nous nos live streams depuis le confort de votre maison.<br>\n"
"        Des codes de réductions spéciaux seront distribués pendant les différents streams, veillez à être à l'heure."

#. module: event
#: model:event.stage,name:event.event_stage_announced
msgid "Announced"
msgstr "Annoncé"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Apply change."
msgstr "Appliquer la modification."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Archived"
msgstr "Archivé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_launched
msgid "Are sales launched"
msgstr "Les ventes sont lancées"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Around one hundred ballons will simultaneously take flight and turn the sky "
"into a beautiful canvas of colours."
msgstr ""
"Une centaine de ballons prendront simultanément leur envol et transformeront"
" le ciel en une belle toile de couleurs."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "As a team, we are happy to contribute to this event."
msgstr "En équipe, nous sommes heureux de contribuer à cet événement."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"À seulement 13 ans, John DOE commençait déjà à développer ses premières "
"applications commerciales pour des clients. Après une maîtrise en génie "
"civil, il a fondé TinyERP. Ce fut la première phase d'OpenERP qui deviendra "
"plus tard Odoo, le logiciel d'entreprise open source le plus installé au "
"monde."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "Présences"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "Présent"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "Date de présence"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Participant"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
msgid "Attendee Name"
msgstr "Nom du participant"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.actions.act_window,name:event.event_registration_action
#: model:ir.actions.act_window,name:event.event_registration_action_kanban
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Attendees"
msgstr "Participants"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__auto_confirm
msgid ""
"Autoconfirm Registrations. Registrations will automatically be confirmed "
"upon creation."
msgstr ""
"Confirmation automatique des inscriptions. Les inscriptions seront "
"automatiquement confirmées lors de la création."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__auto_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Autoconfirmation"
msgstr "Confirmation automatique"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__auto_confirm
msgid "Automatically Confirm Registrations"
msgstr "Confirmer automatiquement les inscriptions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr "Places disponibles"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Bands like Bar Fighters, Led Slippers and Link Floyd will offer you the show"
" of the century during our three day event."
msgstr ""
"Des groupes comme Bar Fighters, Led Slippers et Link Floyd vous offriront le"
" spectacle du siècle lors de notre événement de trois jours."

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_barcode
msgid "Barcode"
msgstr "Code-barres"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "Avant l'événement"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_blocked:event.event_stage_announced
#: model:event.stage,legend_blocked:event.event_stage_booked
#: model:event.stage,legend_blocked:event.event_stage_cancelled
#: model:event.stage,legend_blocked:event.event_stage_done
#: model:event.stage,legend_blocked:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__blocked
#, python-format
msgid "Blocked"
msgstr "Bloqué"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_1
msgid ""
"Bloem brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Bloem apporte honnêteté et sérieux à l'industrie du bois tout en aidant ses "
"clients à gérer les arbres, les fleurs et les champignons."

#. module: event
#: model:event.stage,name:event.event_stage_booked
msgid "Booked"
msgstr "Réservé"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Booked By"
msgstr "Réservé par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Booked by"
msgstr "Réservé par"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_booth
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Booth Management"
msgstr "Gestion des stands"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr ""
"Faites passer votre saison de hockey sur gazon au niveau supérieur en "
"prenant le terrain lors de ce 9e tournoi annuel de hockey sur gazon."

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr "Ateliers commerciaux"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Campaign"
msgstr "Campagne"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel"
msgstr "Annuler"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Cancel Registration"
msgstr "Annuler l'inscription"

#. module: event
#: model:event.stage,name:event.event_stage_cancelled
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "Annulé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_id
msgid "Category"
msgstr "Catégorie"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_sequence
msgid "Category Sequence"
msgstr "Séquence de la catégorie"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr ""
"Chamber Works se réserve le droit d'annuler, de renommer ou de déplacer "
"l'événement ou de modifier les dates auxquelles il a lieu."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__color
msgid "Color Index"
msgstr "Couleur"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Come see us live, we hope to meet you !"
msgstr "Venez nous voir en direct, nous nous réjouissons de vous rencontrer !"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "Communication"

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__mail_registration_ids
msgid "Communication related to event registrations"
msgstr "Communication liée aux inscriptions aux événements"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Community Chat Rooms"
msgstr "Salles de discussion communautaires"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
#: model_terms:event.event,description:event.event_2
msgid "Company"
msgstr "Société"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Compose Email"
msgstr "Rédiger un e-mail"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_2
#: model:event.type,name:event.event_type_data_conference
msgid "Conference"
msgstr "Conférence"

#. module: event
#: model:event.event,name:event.event_2
#: model_terms:event.event,description:event.event_2
msgid "Conference for Architects"
msgstr "Conférence pour les architectes"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr ""
"Des conférences, ateliers et formations seront organisés dans 6 salles :"

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm"
msgstr "Confirmer"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance"
msgstr "Confirmer la présence"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Registration"
msgstr "Confirmer l'inscription"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Confirmed"
msgstr "Confirmé"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Confirmed Attendees"
msgstr "Participants confirmés"

#. module: event
#: model:ir.model,name:event.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "Pays"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Create Booths and manage their reservations"
msgstr "Créer des stands et gérer leurs réservations"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Create an Event"
msgstr "Créer un événement"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid "Create an Event Stage"
msgstr "Créez une étape d'événement"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Create an Event Tag Category"
msgstr "Créer une catégorie d'étiquette d'événement"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid "Create an Event Template"
msgstr "Créer un modèle d'événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_stage__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_stage__create_date
#: model:ir.model.fields,field_description:event.field_event_tag__create_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_date
msgid "Created on"
msgstr "Créé le"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_1
msgid "Culture"
msgstr "Culture"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Customer"
msgstr "Client"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Customer Email"
msgstr "E-mail client"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "Date"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "Jours"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,help:event.field_event_type_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too many registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""
"Définir le nombre de tickets disponibles. Si vous recevez trop "
"d'inscriptions, il ne vous sera plus possible de vendre des tickets. Laisser"
" à 0 pour ignorer cette limite."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
#: model:ir.model.fields,field_description:event.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event.field_event_type_ticket__description
msgid "Description"
msgstr "Description"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr "Design Fair à Los Angeles"

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr "Découvrez comment développer une entreprise durable avec nos experts."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Discover more"
msgstr "Découvrir plus"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_meet
msgid "Discussion Rooms"
msgstr "Salles de discussion"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_stage__display_name
#: model:ir.model.fields,field_description:event.field_event_tag__display_name
#: model:ir.model.fields,field_description:event.field_event_tag_category__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Display Sponsors and Exhibitors on your event pages"
msgstr ""
"Afficher les sponsors et les exposants sur les pages de votre événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
msgid "Display Timezone"
msgstr "Afficher le fuseau horaire"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "Afficher l'ordre"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__done
msgid "Done"
msgstr "Fait"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""
"Au cours de cette conférence, notre équipe vous donnera un aperçu détaillé "
"de nos applications professionnelles. Vous connaîtrez tous les avantages de "
"leur utilisation."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__email
msgid "Email"
msgstr "E-mail"

#. module: event
#: model:ir.model,name:event.model_mail_template
msgid "Email Templates"
msgstr "Modèles d'e-mail"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "Date de fin"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr "Date de fin située"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__pipe_end
msgid "End Stage"
msgstr "Étape de fin"

#. module: event
#: model:event.stage,name:event.event_stage_done
msgid "Ended"
msgstr "Terminé"

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr ""
"Améliorez votre entreprise architecturale mais aussi vos compétences "
"professionnelles."

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "Événement"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "Envois automatisés pour un événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__event_type_id
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Event Category"
msgstr "Catégorie d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_form
msgid "Event Category Tag"
msgstr "Étiquette de catégorie d'événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "Date de fin d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Event Gamification"
msgstr "Ludification d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "Information de l'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "Planificateur d'emails d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "Planificateurs d'emails d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "Nom de l'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "Organisation de l'événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_organizer_id
msgid "Event Organizer"
msgstr "Organisateur de l'événement"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "Inscription à l'événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_user_id
msgid "Event Responsible"
msgstr "Responsable de l'évenement"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"Event Scheduler for:\n"
"  - Event: %(event_name)s (%(event_id)s)\n"
"  - Scheduled: %(date)s\n"
"  - Template: %(template_name)s (%(template_id)s)\n"
"\n"
"Failed with error:\n"
"  - %(error)s\n"
"\n"
"You receive this email because you are:\n"
"  - the organizer of the event,\n"
"  - or the responsible of the event,\n"
"  - or the last writer of the template.\n"
msgstr ""
"Planificateur d'événements pour :\n"
"  - Événement : %(event_name)s (%(event_id)s)\n"
"  - Programmé : %(date)s\n"
"  - Modèle : %(template_name)s (%(template_id)s)\n"
"\n"
"Échec avec erreur :\n"
"  - %(error)s\n"
"\n"
"Vous recevez cet e-mail parce que vous êtes :\n"
"  - l'organisateur de l'événement,\n"
"  - ou le responsable de l'événement,\n"
"  - ou le dernier auteur du modèle.\n"

#. module: event
#: model:ir.model,name:event.model_event_stage
msgid "Event Stage"
msgstr "Étape d'événement"

#. module: event
#: model:ir.actions.act_window,name:event.event_stage_action
#: model:ir.ui.menu,name:event.event_stage_menu
msgid "Event Stages"
msgstr "Étapes d'événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "Date de début de l'événement"

#. module: event
#: model:ir.model,name:event.model_event_tag
msgid "Event Tag"
msgstr "Étiquettes d'événement"

#. module: event
#: model:ir.model,name:event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Catégorie d'étiquette d'événement"

#. module: event
#: model:ir.actions.act_window,name:event.event_tag_category_action_tree
#: model:ir.ui.menu,name:event.menu_event_category
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_tree
msgid "Event Tags Categories"
msgstr "Catégories d'étiquette d'événement"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Template"
msgstr "Modèle d'événement"

#. module: event
#: model:ir.model,name:event.model_event_type_ticket
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_form_from_type
msgid "Event Template Ticket"
msgstr "Modèle de ticket d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_tree_from_type
msgid "Event Template Tickets"
msgstr "Modèle de tickets d'événement"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Templates"
msgstr "Modèles d'événements"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid ""
"Event Templates combine configurations you use often and are\n"
"                usually based on the types of events you organize (e.g. \"Workshop\",\n"
"                \"Roadshow\", \"Online Webinar\", etc)."
msgstr ""
"Les modèles d'événements combinent les configurations que vous utilisez souvent et sont \n"
"généralement basés sur les types d'événements que vous organisez (par exemple, \"Atelier\",\n"
"\"Roadshow\", \"Webinaire en ligne\", etc.)."

#. module: event
#: model:ir.model,name:event.model_event_event_ticket
#: model:ir.model.fields,field_description:event.field_event_event__event_ticket_ids
#: model:ir.model.fields,field_description:event.field_event_registration__event_ticket_id
msgid "Event Ticket"
msgstr "Ticket d'événement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Event Ticket For"
msgstr "Ticket d'événement pour"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "Type d'événement"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_tree
msgid "Event registrations"
msgstr "Inscriptions à l'événement"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid ""
"Event stages are used to track the progress of an Event from its origin "
"until its conclusion."
msgstr ""
"Les étapes d'un événement sont utilisées pour suivre la progression de ce "
"dernier depuis sa création jusqu'à sa conclusion."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "Event's Ticket"
msgstr "Ticket d'événement"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
#: model:ir.cron,cron_name:event.event_mail_scheduler
msgid "Event: Mail Scheduler"
msgstr "Événement : Planificateur d'email"

#. module: event
#: model:mail.template,name:event.event_registration_mail_template_badge
msgid "Event: Registration Badge"
msgstr "Événement : Badge d'inscription"

#. module: event
#: model:mail.template,name:event.event_subscription
msgid "Event: Registration Confirmation"
msgstr "Événement : Confirmation de l'inscription"

#. module: event
#: model:mail.template,name:event.event_reminder
msgid "Event: Reminder"
msgstr "Événement : Rappel"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "Événements"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "Planificateurs d'emails des événements"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_tree
msgid "Events Stage"
msgstr "Étape des événements"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__auto_confirm
msgid ""
"Events and registrations will automatically be confirmed upon creation, "
"easing the flow for simple events."
msgstr ""
"Les événements et les inscriptions seront automatiquement confirmés lors de "
"leur création, ce qui simplifie le déroulement des événements."

#. module: event
#: model:ir.model.fields,help:event.field_event_stage__pipe_end
msgid ""
"Events will automatically be moved into this stage when they are finished. "
"The event moved into this stage will automatically be set as green."
msgstr ""
"Les événements seront automatiquement déplacés vers cette étape lorsqu'ils "
"seront terminés. L'événement déplacé dans cette étape sera automatiquement "
"défini comme vert."

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"            This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"Chaque année, nous invitons notre communauté, nos partenaires et nos utilisateurs à venir nous rencontrer ! C'est l'événement idéal pour se retrouver et présenter les nouvelles fonctionnalités, la roadmap des futures versions, le logiciel, mais aussi participer à des ateliers, sessions de formation, etc...\n"
"Cet événement est également l'occasion de présenter les études de cas de nos partenaires, la méthodologie ou des développements. Rejoignez-nous et découvrez à la source les fonctionnalités de la nouvelle version !"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""
"Chaque année, nous invitons notre communauté, nos partenaires et nos "
"utilisateurs à venir nous rencontrer ! C'est l'événement idéal pour se "
"réunir et présenter de nouvelles fonctionnalités, les composantes des "
"futures versions, réalisations du logiciel, ateliers, sessions de formation,"
" etc ...."

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Exhibition"
msgstr "Exposition"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr "Attendu"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Expected Attendees"
msgstr "Participants attendus"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr "Participants attendus"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr ""
"Expérimentez de la music live, de la nourriture locale et des boissons."

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_foldable_badge
msgid "Foldable Badge"
msgstr "Badge pliable"

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid ""
"Foldable Badge - {{ (object.event_id.name or 'Event').replace('/','_') }}"
msgstr ""
"Badge pliable - {{ (object.event_id.name or 'Événement').replace('/','_') }}"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_foldable_badge
msgid "Foldable Badge Example"
msgstr "Exemple de badge pliable"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__fold
msgid "Folded in Kanban"
msgstr "Repliée dans la vue Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_type_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
#: model_terms:event.event,description:event.event_7
msgid "For any additional information, please contact us at"
msgstr "Pour toutes informations supplémentaires, veuillez nous contacter au"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""
"Pour chaque événement, vous pouvez définir un nombre maximum de places "
"(nombre de participants), au delà de ce chiffre, les inscriptions ne sont "
"pas acceptées."

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_1
msgid "For only 10, you gain access to catering. Yum yum."
msgstr "Pour seulement 10, vous avez accès à la restauration. Miam miam."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Foster interactions between attendees by creating virtual conference rooms"
msgstr ""
"Favoriser les interactions entre les participants en créant des salles de "
"conférence virtuelles"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_0
msgid "Free"
msgstr "Gratuit"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_0
msgid "Free entrance, no food !"
msgstr "Entrée gratuite, pas de nourriture !"

#. module: event
#: model:event.stage,description:event.event_stage_new
msgid "Freshly created"
msgstr "Fraîchement créé"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid ""
"From this dashboard you can report, analyze and detect trends regarding your"
" event registrations."
msgstr ""
"À partir de ce tableau de bord, vous pouvez faire du reporting, analyser et "
"détecter les tendances concernant les inscriptions à vos événements."

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_full_page_ticket
msgid "Full Page Ticket"
msgstr "Ticket pleine page"

#. module: event
#: model:mail.template,report_name:event.event_subscription
msgid ""
"Full Page Ticket - {{ (object.event_id.name or 'Event').replace('/','') }}"
msgstr ""
"Ticket pleine page - {{ (object.event_id.name or "
"'Événement').replace('/','') }}"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_full_page_ticket
msgid "Full Page Ticket Example"
msgstr "Exemple de ticket pleine page"

#. module: event
#: model:event.stage,description:event.event_stage_done
msgid "Fully ended"
msgstr "Complètement terminé"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr "Organigramme fonctionnel des principales applications ;"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "Activités futures"

#. module: event
#: model:event.event.ticket,name:event.event_4_ticket_0
msgid "General Admission"
msgstr "Admission générale"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr "Inspirez-vous • Connectez-vous • Amusez-vous"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_state
msgid "Global communication Status"
msgstr "Statut de la communication globale"

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr "La Great Reno Balloon Race"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Great! Now all you have to do is wait for your attendees to show up!"
msgstr ""
"Super ! Il ne vous reste plus qu'à attendre que vos participants se "
"présentent !"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_done
msgid "Green Kanban Label"
msgstr "Étiquette Kanban verte"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Étiquette Kanban grise"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Regrouper par"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Happy to be Sponsor"
msgstr "Heureux d'être sponsor"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__has_message
#: model:ir.model.fields,field_description:event.field_event_registration__has_message
msgid "Has Message"
msgstr "A un message"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr ""
"Ayant participé à cette conférence, les participants devraient être capable "
"de :"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Here it is, the 12th edition of our Live Musical Festival !"
msgstr "On y est, la 12ème édition de notre Festival de musique live !"

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr "Tournoi de hockey"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "Heures"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (1)"
msgstr "Comment plier (1)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (2)"
msgstr "Comment plier (2)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (3)"
msgstr "Comment plier (3)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (4)"
msgstr "Comment plier (4)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_stage__id
#: model:ir.model.fields,field_description:event.field_event_tag__id
#: model:ir.model.fields,field_description:event.field_event_tag_category__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__id
msgid "ID"
msgstr "ID"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/icon_selection_field/icon_selection_field.js:0
#, python-format
msgid "Icon Selection"
msgstr "Sélection d'icônes"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__start_sale_datetime
msgid ""
"If ticketing is used, contains the earliest starting sale date of tickets."
msgstr ""
"Si la billetterie est utilisée, contient la première date de début de vente "
"des tickets."

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "If you don't have this ticket, you will <b>not</b> be allowed entry!"
msgstr ""
"Si vous n'avez pas ce ticket, vous ne serez <b>pas</b> autorisé à entrer !"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"If you don't know anything about Hockey, this is a great introduction to this wonderful sport as you will will be able to see some training process and also have some time\n"
"                to chat with experienced players and trainers once the tournament is over !"
msgstr ""
"Si vous ne connaissez rien au hockey, c'est une excellente introduction à ce merveilleux sport, car vous pourrez voir un processus d'entraînement et aussi avoir du temps\n"
"pour discuter avec des joueurs et entraîneurs expérimentés une fois le tournoi terminé !"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "Immédiatement"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Important ticket information"
msgstr "Informations importantes sur les tickets"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_normal:event.event_stage_announced
#: model:event.stage,legend_normal:event.event_stage_booked
#: model:event.stage,legend_normal:event.event_stage_cancelled
#: model:event.stage,legend_normal:event.event_stage_done
#: model:event.stage,legend_normal:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__normal
#, python-format
msgid "In Progress"
msgstr "En cours"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_tz
msgid ""
"Indicates the timezone in which the event dates/times will be displayed on "
"the website."
msgstr ""
"Indique le fuseau horaire dans lequel les dates/horaires des événements "
"seront affichés sur le site web."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "Intervalle"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "Introduction, CRM, Gestion des ventes"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Invalid event / ticket choice"
msgstr "Choix d'événement/ticket invalide"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sale_available
msgid "Is Available"
msgstr "Est disponible"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr "A expiré"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_finished
msgid "Is Finished"
msgstr "Est terminé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr "Est un jour"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Est en cours"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__seats_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""
"Cette valeur par défaut maximum est sélectionnée si vous choisissez cet "
"événement"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr "John DOE"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Join us for the greatest ballon race of all times !"
msgstr ""
"Rejoignez-nous pour la plus grande course de ballons de tous les temps !"

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid "Join us for this 24 hours Event"
msgstr "Rejoignez-nous pour cet événement de 24 heures"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr "Rejoignez-nous pour cet événement de 3 jours"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Explication de kanban bloqué"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Explication de kanban en cours"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state
msgid "Kanban State"
msgstr "État kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state_label
msgid "Kanban State Label"
msgstr "Étiquette d'état Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_done
msgid "Kanban Valid Explanation"
msgstr "Explication de kanban valide"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Last 30 days"
msgstr "Les 30 derniers jours"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event____last_update
#: model:ir.model.fields,field_description:event.field_event_event_ticket____last_update
#: model:ir.model.fields,field_description:event.field_event_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_stage____last_update
#: model:ir.model.fields,field_description:event.field_event_tag____last_update
#: model:ir.model.fields,field_description:event.field_event_tag_category____last_update
#: model:ir.model.fields,field_description:event.field_event_type____last_update
#: model:ir.model.fields,field_description:event.field_event_type_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_type_ticket____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_stage__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_stage__write_date
#: model:ir.model.fields,field_description:event.field_event_tag__write_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "Activités en retard"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Let's create your first <b>event</b>."
msgstr "Créons votre premier <b>événement</b>."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_limited
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_limited
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_limited
msgid "Limit Attendees"
msgstr "Limiter les participants"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Limit Registrations"
msgstr "Limiter les inscriptions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "Places limitées"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Live Broadcast"
msgstr "Diffusion en direct"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_live
msgid "Live Mode"
msgstr "Mode Live"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr "Festival de musique live"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
msgid "Mail"
msgstr "Mail"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "Inscription par email"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "Planification d'email"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "Planificateur d'email"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "Planificateurs d'email"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Planification d'emails en fonction de la catégorie d'événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "Email envoyé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_main_attachment_id
#: model:ir.model.fields,field_description:event.field_event_registration__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage &amp; publish a schedule with tracks"
msgstr "Gérer & publier un programme avec des sessions"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Mark as Attending"
msgstr "Marquer comme présent"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Marketing"
msgstr "Marketing"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
msgid "Maximum"
msgstr "Maximum"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_max
msgid "Maximum Attendees"
msgstr "Nombre maximal de participants"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__seats_max
msgid "Maximum Registrations"
msgstr "Nombre maximal d'inscriptions"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Maximum Seats"
msgstr "Nombre maximal de places"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_medium_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Medium"
msgstr "Medium"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "Messages"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__mobile
msgid "Mobile"
msgstr "Mobile"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "Mois"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_2
msgid "Music"
msgstr "Musique"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__my_activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Mes événements"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event.field_event_tag__name
#: model:ir.model.fields,field_description:event.field_event_tag_category__name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__name
msgid "Name"
msgstr "Nom"

#. module: event
#: model:event.stage,name:event.event_stage_new
msgid "New"
msgstr "Nouveau"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_calendar_event_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
msgid "No Attendees expected yet!"
msgstr "Aucun participant n'est encore attendu !"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid "No Attendees yet!"
msgstr "Pas encore de participants !"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "None"
msgstr "Aucun"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__note
#: model:ir.model.fields,field_description:event.field_event_type__note
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Note"
msgstr "Note"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Notes"
msgstr "Notes"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid "Nothing Scheduled yet!"
msgstr "Rien de prévu pour le moment !"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Now that your event is ready, click here to move it to another stage."
msgstr ""
"Maintenant que votre événement est prêt, cliquez ici pour le déplacer vers "
"une autre étape."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Attendees"
msgstr "Nombre de participants"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_expected
msgid "Number of Expected Attendees"
msgstr "Nombre de participants attendus"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Number of Registrations"
msgstr "Nombre d'inscriptions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "Objectifs"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Once again we assembled the most legendary bands in Rock history."
msgstr ""
"Une fois de plus, nous avons réuni les groupes les plus légendaires de "
"l'histoire du rock."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ongoing Events"
msgstr "Événements en cours"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_1
msgid "Online"
msgstr "En ligne"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Online Exhibitors"
msgstr "Exposants en ligne"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr "Billetterie en ligne"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Open date range picker. Pick a Start date for your event"
msgstr ""
"Ouvrir le sélecteur de page de dates. Choisissez une date de début pour "
"votre événement"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr ""
"OpenElec Applications se réserve le droit d'annuler, de renommer ou de "
"localiser l'événement ou de modifier les dates auxquelles il a lieu."

#. module: event
#: model:event.event,name:event.event_7
msgid "OpenWood Collection Online Reveal"
msgstr "Révélation en ligne de la collection OpenWood"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_2
msgid ""
"OpenWood brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"OpenWood apporte honnêteté et sérieux à l'industrie du bois, tout en aidant "
"ses clients à gérer les arbres, les fleurs et les champignons."

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "Operation not supported."
msgstr "Opération non prise en charge."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Organizer"
msgstr "Organisateur"

#. module: event
#: model:event.event,subtitle:event.event_7
msgid ""
"Our newest collection will be revealed online! Interact with us on our live "
"streams!"
msgstr ""
"Notre nouvelle collection sera révélée en ligne ! Interagissez avec nous "
"lors de nos live streams !"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_blocked
#: model:ir.model.fields,help:event.field_event_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection."
msgstr ""
"Remplacer la valeur par défaut affichée pour l'état 'bloqué' dans la "
"sélection kanban."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_done
#: model:ir.model.fields,help:event.field_event_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection."
msgstr ""
"Remplacer la valeur par défaut affichée pour l'état 'terminé' dans la "
"sélection kanban."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_normal
#: model:ir.model.fields,help:event.field_event_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection."
msgstr ""
"Remplacer la valeur par défaut affichée pour l'état 'normal' dans la "
"sélection kanban."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "Participant"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Partenaire"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__phone
msgid "Phone"
msgstr "Téléphone"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid ""
"Please come <b>at least</b> 30 minutes before the beginning of the event."
msgstr ""
"Veuillez vous présenter <b>au moins</b> 30 minutes avant le début de "
"l'événement."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr "Point de vente (PdV), introduction à la personnalisation de rapports."

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "Programme"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr "Gestion de projets, Ressources humaines, Gestion des contrats."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr "Achats, Gestion des ventes & des achats, Comptabilité financière."

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_quiz
msgid "Quiz on Tracks"
msgstr "Quiz à propos des sessions"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_done:event.event_stage_announced
#: model:event.stage,legend_done:event.event_stage_booked
#: model:event.stage,legend_done:event.event_stage_cancelled
#: model:event.stage,legend_done:event.event_stage_done
#: model:event.stage,legend_done:event.event_stage_new
#, python-format
msgid "Ready for Next Stage"
msgstr "Prêt pour la prochaine étape"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Ready to <b>organize events</b> in a few minutes? Let's get started!"
msgstr ""
"Prêt à <b>organiser des événements</b> en quelques minutes ? Commençons !"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Indicateur Kanban rouge"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#: code:addons/event/models/event_ticket.py:0
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#, python-format
msgid "Registration"
msgstr "Inscription"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "Date d'inscription"

#. module: event
#: model:res.groups,name:event.group_event_registration_desk
msgid "Registration Desk"
msgstr "Bureau d'inscription"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__end_sale_datetime
msgid "Registration End"
msgstr "Fin de l'inscription"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "ID Inscription"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Planificateur d'email d'inscription"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "Mails à l'inscription"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__start_sale_datetime
msgid "Registration Start"
msgstr "Début de l'inscription"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_questions
msgid "Registration Survey"
msgstr "Sondage sur l'inscription"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "Registration for %s"
msgstr "Inscription pour %s"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "Email d'inscription"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_open
msgid "Registration open"
msgstr "Inscription ouverte"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_stats_from_event
msgid "Registration statistics"
msgstr "Statistiques sur les inscriptions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr "Inscriptions"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_open
msgid ""
"Registrations are open if:\n"
"- the event is not ended\n"
"- there are seats available on event\n"
"- the tickets are sellable (if ticketing is used)"
msgstr ""
"Les inscriptions sont ouvertes si :\n"
"- l'événement n'est pas terminé\n"
"- il y a des places disponibles à l'événement\n"
"- les tickets sont vendables (si la billetterie est utilisée)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_started
msgid "Registrations started"
msgstr "Les inscriptions ont commencé"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "Analyse"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr "Places réservées"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Responsible"
msgstr "Responsable"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__running
msgid "Running"
msgstr "En cours"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Scan badges to confirm attendances"
msgstr "Scanner les badges pour confirmer les présences"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "Programme & Sessions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Schedule Date"
msgstr "Date prévue"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events: handle registrations, send automated "
"confirmation emails, sell tickets, etc."
msgstr ""
"Programmez et organisez vos événements : gérez les inscriptions, envoyez des"
" e-mails de confirmation automatisés, vendez des tickets, etc."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__scheduled
msgid "Scheduled"
msgstr "Planifié"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "Heure prévue"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid "Seasoned Hockey Fans and curious people, this tournament is for you !"
msgstr ""
"Amateurs de hockey aguerris et curieux, ce tournoi est fait pour vous !"

#. module: event
#: model:event.type,name:event.event_type_data_ticket
msgid "Sell Online"
msgstr "Vendre en ligne"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "Vendre des tickets sur votre site web"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "Vendre des tickets avec des commandes clients"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "Envoyer"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "Envoyer par e-mail"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_done
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__sent
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Sent"
msgstr "Envoyé"

#. module: event
#: model:mail.template,description:event.event_reminder
msgid ""
"Sent automatically to attendees if there is a reminder defined on the event"
msgstr ""
"Envoyé automatiquement aux participants si un rappel est défini pour "
"l'événement"

#. module: event
#: model:mail.template,description:event.event_registration_mail_template_badge
msgid "Sent automatically to someone after they registered to an event"
msgstr ""
"Envoyé automatiquement à une personne après son inscription à un événement"

#. module: event
#: model:mail.template,description:event.event_subscription
msgid "Sent to attendees after registering to an event"
msgstr "Envoyé aux participants après qu'ils se sont inscrits à un événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__sequence
#: model:ir.model.fields,field_description:event.field_event_tag__sequence
#: model:ir.model.fields,field_description:event.field_event_tag_category__sequence
#: model:ir.model.fields,field_description:event.field_event_type__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr "Marquer comme non confirmé"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "Paramètres"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_4
msgid ""
"Shangai Pterocarpus Furniture brings honesty and seriousness to wood "
"industry while helping customers deal with trees, flowers and fungi."
msgstr ""
"Shangai Pterocarpus Furniture apporte honnêteté et sérieux à industrie du "
"bois tout en aidant les clients à gérer les arbres, les fleurs et les "
"champignons."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez tous les enregistrements pour lesquels la date des prochaines "
"actions est pour aujourd'hui ou avant"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_sold_out
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_sold_out
msgid "Sold Out"
msgstr "Complet"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_source_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Source"
msgstr "Source"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_3
#: model:event.type,name:event.event_type_2
msgid "Sport"
msgstr "Sport"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__stage_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Stage"
msgstr "Étape"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Description des étapes et infobulles"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__name
msgid "Stage Name"
msgstr "Nom de l'étape"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__description
msgid "Stage description"
msgstr "Description de l'étape"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_1
#: model:event.event.ticket,name:event.event_2_ticket_1
#: model:event.event.ticket,name:event.event_3_ticket_0
#: model:event.event.ticket,name:event.event_7_ticket_1
msgid "Standard"
msgstr "Standard"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "Date de début"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr "Date de début située"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__start_sale_datetime
msgid "Start sale date"
msgstr "Date de début de vente"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Statut"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: event
#: model:ir.model.fields,help:event.field_event_tag__color
msgid ""
"Tag color. No color means no display in kanban or front-end, to distinguish "
"internal tags from public categorization tags."
msgstr ""
"Couleur de l'étiquette. Aucune couleur signifie aucun affichage dans le "
"kanban ou le frontend, pour distinguer les étiquettes internes des "
"étiquettes de catégorisation publiques."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__tag_ids
#: model:ir.model.fields,field_description:event.field_event_tag_category__tag_ids
#: model:ir.model.fields,field_description:event.field_event_type__tag_ids
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "Tags"
msgstr "Étiquettes"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Tâche en cours. Cliquez pour bloquer ou marquer comme terminée."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "Tâche bloquée. Cliquez pour débloquer ou marquer comme terminée."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
#: model:ir.model.fields,field_description:event.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_ref
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Template"
msgstr "Modèle"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__template_model_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_model_id
msgid "Template Model"
msgstr "Modèle"

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr ""
"La Great Reno Balloon Race est le plus grand événement de montgolfière "
"gratuit au monde."

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"The best Hockey teams of the country will compete for the national Hockey "
"trophy."
msgstr ""
"Les meilleures équipes de hockey du pays concourront pour le trophée "
"national de hockey."

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"The best aeronauts of the world will gather on this event to offer you the "
"most spectacular show."
msgstr ""
"Les meilleurs aéronautes du monde se réuniront sur cet événement pour vous "
"offrir le spectacle le plus spectaculaire."

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "The closing date cannot be earlier than the beginning date."
msgstr "La date de fin ne peut pas être antérieure à la date de début."

#. module: event
#: model:event.stage,description:event.event_stage_cancelled
msgid "The event has been cancelled"
msgstr "Cet événement a été annulé"

#. module: event
#: model:event.stage,description:event.event_stage_announced
msgid "The event has been publicly announced"
msgstr "Cet événement a été annoncé publiquement"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_sold_out
msgid ""
"The event is sold out if no more seats are available on event. If ticketing "
"is used and all tickets are sold out, the event will be sold out."
msgstr ""
"L'événement est complet s'il n'y a plus de places disponibles sur "
"l'événement. Si la billetterie est utilisée et que tous les tickets sont "
"épuisés, l'événement affichera complet."

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"The finest OpenWood furnitures are coming to your house in a brand new "
"collection"
msgstr ""
"Les meilleurs meubles OpenWood pour votre maison dans une toute nouvelle "
"collection"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"The following tickets cannot be deleted while they have one or more registrations linked to them:\n"
"- %s"
msgstr ""
"Les tickets suivants ne peuvent pas être supprimés tant qu'ils sont associés à une ou plusieurs inscriptions :\n"
"- %s"

#. module: event
#: model:event.stage,description:event.event_stage_booked
msgid "The place has been reserved"
msgstr "Cette place a été réservée"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "The safety of our attendees and our aeronauts comes first !"
msgstr "La sécurité de nos participants et nos aéronautes avant tout !"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"The stop date cannot be earlier than the start date. Please check ticket "
"%(ticket_name)s"
msgstr ""
"La date de fin ne peut pas être antérieure à la date de début. Veuillez "
"vérifier le ticket %(ticket_name)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"The template which is referenced should be coming from %(model_name)s model."
msgstr "Le modèle référencé doit venir du modèle %(model_name)s."

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "There are not enough seats available for:"
msgstr "Il n'y a pas assez de places disponibles pour :"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr ""
"Cet événement est également l'occasion de présenter les études de cas, la "
"méthodologie ou les développements de nos partenaires. Soyez là et "
"découvrez, à la source, les fonctionnalités de la version 12 !"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"This event is fully online and FREE, if you have paid for tickets, you should get a refund.<br>\n"
"        It will require a good Internet connection to get the best video quality."
msgstr ""
"Cet événement est entièrement en ligne et GRATUIT, si vous avez payé des tickets, vous devriez obtenir un remboursement.<br>\n"
"        Il faudra une bonne connexion internet pour obtenir la meilleure qualité vidéo."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__ticket_instructions
#: model:ir.model.fields,help:event.field_event_type__ticket_instructions
msgid "This information will be printed on your tickets."
msgstr "Cette information sera imprimée sur vos tickets."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This is the <b>name</b> your guests will see when registering."
msgstr "C'est le <b>nom</b> que vos invités verront lors de leur inscription."

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"This is the perfect place for spending a nice day with your family, we "
"guarantee you will be leaving with beautiful everlasting memories !"
msgstr ""
"C'est l'endroit idéal pour passer une chouette journée en famille. Nous "
"garantissons que vous partirez avec tout plein de magnifiques souvenirs ! "

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"This is the perfect place for spending a nice time with your friends while "
"listening to some of the most iconic rock songs of all times!"
msgstr ""
"C'est l'endroit idéal pour passer un chouette moment avec vos amis tout en "
"écoutant les chansons rock les plus iconiques de tous les temps !"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "This operator is not supported"
msgstr "Cet opérateur n'est pas pris en charge"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""
"Cette étape est terminée. Cliquez pour bloquer ou marquer comme terminée."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_form_from_event
msgid "Ticket"
msgstr "Ticket"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__ticket_instructions
#: model:ir.model.fields,field_description:event.field_event_type__ticket_instructions
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Ticket Instructions"
msgstr "Instructions sur le ticket"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket Type"
msgstr "Type de tickets"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Ticket types allow you to distinguish your attendees. Let's <b>create</b> a "
"new one."
msgstr ""
"Les types de tickets vous permettent de distinguer vos participants. "
"<b>Créons-en</b> un nouveau."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__event_type_ticket_ids
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Tickets"
msgstr "Tickets"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Tickets can be printed or scanned directly from your phone."
msgstr ""
"Les tickets peuvent être imprimés ou scannés directement depuis votre "
"téléphone."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "Fuseau horaire"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "Activités du jour"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "Total"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Total Registrations for this Event"
msgstr "Total des inscriptions pour cet événement"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "Sessions et Agenda"

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Training"
msgstr "Formation"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_3
msgid ""
"Tree Dealers brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Tree Dealers apporte honnêteté et sérieux à l'industrie du bois tout en "
"aidant les clients à gérer les arbres, les fleurs et les champignons."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "Déclencheur"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr "Déclencheur"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_3
msgid "Type"
msgstr "Type"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "Non confirmé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_unconfirmed
msgid "Unconfirmed Registrations"
msgstr "Inscriptions non confirmées"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_unconfirmed
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Unconfirmed Seats"
msgstr "Places non confirmées"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid ""
"Under this technical menu you will find all scheduled communication related "
"to your events."
msgstr ""
"Sous ce menu technique, vous trouverez toutes les communications programmées"
" liées à vos événements."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "Comprendre les modèles variés ;"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "Unité"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "Événements futurs à partir d'aujourd'hui"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr "À venir/En cours"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Use Event Tag Categories to classify and organize your event tags."
msgstr ""
"Utilisez les catégories d'étiquettes d'événement pour classer et organiser "
"vos étiquettes d'événement."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use the <b>breadcrumbs</b> to go back to your kanban overview."
msgstr "Utilisez le <b>fil d'Ariane</b> pour revenir à l'aperçu Kanban."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_used
msgid "Used Seats"
msgstr "Places utilisées"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Utilisateur"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_2
#: model:event.event.ticket,name:event.event_2_ticket_2
#: model:event.event.ticket,name:event.event_3_ticket_1
#: model:event.event.ticket,name:event.event_7_ticket_2
msgid "VIP"
msgstr "VIP"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "Value should be True or False (not %s)"
msgstr "Les valeurs doivent être vrai ou faux (pas %s)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
msgid "Venue"
msgstr "Lieu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_inline
msgid "Venue (formatted for one line uses)"
msgstr "Lieu (formaté pour une ligne)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr "ATTENTION : Erreur de planificateur d'événement pour l'événement : %s"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
msgid ""
"Wait until Attendees register to your Event or create their registrations "
"manually."
msgstr ""
"Attendez que les participants s'inscrivent à votre événement ou créez leurs "
"inscriptions manuellement."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr "Gestion de l'entrepôt, Fabrication (MRP) &amp; Ventes, Import/Export."

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid ""
"We reserve the right to cancel, re-name or re-locate the event or change the"
" dates on which it is held in case the weather fails us."
msgstr ""
"Nous nous réservons le droit d'annuler, de renommer ou de déplacer "
"l'événement ou de modifier les dates auxquelles il a lieu en cas de mauvais "
"temps."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "Semaines"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr "Quoi de neuf ?"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_begin
#: model:ir.model.fields,help:event.field_event_registration__event_begin_date
msgid ""
"When the event is scheduled to take place (expressed in your local timezone "
"on the form view)."
msgstr ""
"Le moment auquel l'événement est prévu (selon votre fuseau horaire local "
"dans la vue formulaire)."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Si l'événement a commencé"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__sale_available
msgid "Whether it is possible to sell these tickets"
msgstr "S'il est possible de vendre ces tickets"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__is_sold_out
msgid "Whether seats are not available for this ticket."
msgstr "S'il n'y a pas de places disponibles pour ce ticket."

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_2
msgid "You are truly among the best."
msgstr "Vous êtes vraiment parmi les meilleurs."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Vous pouvez aussi ajouter une description pour aider vos coéquipiers à "
"comprendre la signification et le but de cette étape."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels in the kanban view."
msgstr ""
"Vous pouvez définir ici les libellés qui seront affichés pour l'état au lieu\n"
"                        des libellés par défaut dans la vue kanban."

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for {{ object.event_id.name }}"
msgstr "Votre badge pour {{ object.event_id.name }}"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at {{ object.event_id.name }}"
msgstr "Votre inscription à {{ object.event_id.name }}"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. Conference for Architects"
msgstr "par ex. Conférence pour architectes"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. How to get to your event, door closing time, ..."
msgstr ""
"par ex. Comment se rendre à votre événement, heure de fermeture des portes, "
"..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. Online Conferences"
msgstr "par ex. Conférences en ligne"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "e.g. VIP Ticket"
msgstr "par ex. Ticket VIP"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "in %d days"
msgstr "dans %d jours"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "next month"
msgstr "le mois prochain"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "next week"
msgstr "la semaine prochaine"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "on %(date)s"
msgstr "le %(date)s"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_started
msgid ""
"registrations have started if the current datetime is after the earliest "
"starting date of tickets."
msgstr ""
"les inscriptions ont commencé si le datetime actuel est postérieur à la "
"première date de début des tickets."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "reserved +"
msgstr "reservé +"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "to"
msgstr "à"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "today"
msgstr "aujourd'hui"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "tomorrow"
msgstr "demain"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "unconfirmed"
msgstr "non confirmé"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "{{ object.event_id.name }}: {{ object.get_date_range_str() }}"
msgstr "{{ object.event_id.name }}: {{ object.get_date_range_str() }}"
