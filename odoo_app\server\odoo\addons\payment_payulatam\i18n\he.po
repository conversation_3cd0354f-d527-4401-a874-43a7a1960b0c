# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payulatam
# 
# Translators:
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>tem <<EMAIL>>, 2023
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__code
msgid "Code"
msgstr "קוד"

#. module: payment_payulatam
#. odoo-python
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "Invalid payment status."
msgstr ""

#. module: payment_payulatam
#. odoo-python
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "לא נמצאה עסקה המתאימה למספר האסמכתא %s."

#. module: payment_payulatam
#: model:ir.model.fields.selection,name:payment_payulatam.selection__payment_provider__code__payulatam
#: model:payment.provider,name:payment_payulatam.payment_provider_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_api_key
msgid "PayU Latam API Key"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_account_id
msgid "PayU Latam Account ID"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_merchant_id
msgid "PayU Latam Merchant ID"
msgstr ""

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_provider
msgid "Payment Provider"
msgstr "ספק תשלום"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_transaction
msgid "Payment Transaction"
msgstr "עסקת תשלום"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__payulatam_merchant_id
msgid "The ID solely used to identify the account with PayULatam"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__payulatam_account_id
msgid ""
"The ID solely used to identify the country-dependent shop with PayULatam"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "הקוד הטכני של ספק התשלומים הזה."

#. module: payment_payulatam
#: model_terms:ir.ui.view,arch_db:payment_payulatam.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Mercado Pago</strong>."
msgstr ""

#. module: payment_payulatam
#: model_terms:payment.provider,auth_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been authorized."
msgstr "התשלום שלך אושר."

#. module: payment_payulatam
#: model_terms:payment.provider,cancel_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been cancelled."
msgstr "התשלום שלך בוטל."

#. module: payment_payulatam
#: model_terms:payment.provider,pending_msg:payment_payulatam.payment_provider_payulatam
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "התשלום שלך עבר עיבוד בהצלחה אך הוא ממתין לאישור."

#. module: payment_payulatam
#: model_terms:payment.provider,done_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been successfully processed."
msgstr "התשלום שלך עובד בהצלחה."
