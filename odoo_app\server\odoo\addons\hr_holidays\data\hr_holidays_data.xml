<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="icon_1" model="ir.attachment">
            <field name="name">Annual_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Annual_Time_Off.svg</field>
        </record>
        <record id="icon_2" model="ir.attachment">
            <field name="name">Annual_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Annual_Time_Off_2.svg</field>
        </record>
        <record id="icon_3" model="ir.attachment">
            <field name="name">Annual_Time_Off_3.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Annual_Time_Off_3.svg</field>
        </record>
        <record id="icon_4" model="ir.attachment">
            <field name="name">Compensatory_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Compensatory_Time_Off.svg</field>
        </record>
        <record id="icon_5" model="ir.attachment">
            <field name="name">Compensatory_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Compensatory_Time_Off_2.svg</field>
        </record>
        <record id="icon_6" model="ir.attachment">
            <field name="name">Compensatory_Time_Off_3.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Compensatory_Time_Off_3.svg</field>
        </record>
        <record id="icon_7" model="ir.attachment">
            <field name="name">Credit_Time.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Credit_Time.svg</field>
        </record>
        <record id="icon_8" model="ir.attachment">
            <field name="name">Credit_Time_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Credit_Time_2.svg</field>
        </record>
        <record id="icon_9" model="ir.attachment">
            <field name="name">Extra_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Extra_Time_Off.svg</field>
        </record>
        <record id="icon_10" model="ir.attachment">
            <field name="name">Extra_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Extra_Time_Off_2.svg</field>
        </record>
        <record id="icon_11" model="ir.attachment">
            <field name="name">Maternity_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Maternity_Time_Off.svg</field>
        </record>
        <record id="icon_12" model="ir.attachment">
            <field name="name">Maternity_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Maternity_Time_Off_2.svg</field>
        </record>
        <record id="icon_13" model="ir.attachment">
            <field name="name">Maternity_Time_Off_3.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Maternity_Time_Off_3.svg</field>
        </record>
        <record id="icon_14" model="ir.attachment">
            <field name="name">Paid_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Paid_Time_Off.svg</field>
        </record>
        <record id="icon_15" model="ir.attachment">
            <field name="name">Paid_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Paid_Time_Off_2.svg</field>
        </record>
        <record id="icon_16" model="ir.attachment">
            <field name="name">Paid_Time_Off_3.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Paid_Time_Off_3.svg</field>
        </record>
        <record id="icon_17" model="ir.attachment">
            <field name="name">Parental_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Parental_Time_Off.svg</field>
        </record>
        <record id="icon_18" model="ir.attachment">
            <field name="name">Parental_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Parental_Time_Off_2.svg</field>
        </record>
        <record id="icon_19" model="ir.attachment">
            <field name="name">Recovery_Bank_Holiday.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Recovery_Bank_Holiday.svg</field>
        </record>
        <record id="icon_20" model="ir.attachment">
            <field name="name">Recovery_Bank_Holiday_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Recovery_Bank_Holiday_2.svg</field>
        </record>
        <record id="icon_21" model="ir.attachment">
            <field name="name">Sick_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Sick_Time_Off.svg</field>
        </record>
        <record id="icon_22" model="ir.attachment">
            <field name="name">Sick_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Sick_Time_Off_2.svg</field>
        </record>
        <record id="icon_23" model="ir.attachment">
            <field name="name">Small_Unemployement.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Small_Unemployement.svg</field>
        </record>
        <record id="icon_24" model="ir.attachment">
            <field name="name">Small_Unemployement_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Small_Unemployement_2.svg</field>
        </record>
        <record id="icon_25" model="ir.attachment">
            <field name="name">Small_Unemployement_3.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Small_Unemployement_3.svg</field>
        </record>
        <record id="icon_26" model="ir.attachment">
            <field name="name">Training_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Training_Time_Off.svg</field>
        </record>
        <record id="icon_27" model="ir.attachment">
            <field name="name">Training_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Training_Time_Off_2.svg</field>
        </record>
        <record id="icon_28" model="ir.attachment">
            <field name="name">Unpaid_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Unpaid_Time_Off.svg</field>
        </record>
        <record id="icon_29" model="ir.attachment">
            <field name="name">Unpaid_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Unpaid_Time_Off_2.svg</field>
        </record>
        <record id="icon_30" model="ir.attachment">
            <field name="name">Work_Accident_Time_Off.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Work_Accident_Time_Off.svg</field>
        </record>
        <record id="icon_31" model="ir.attachment">
            <field name="name">Work_Accident_Time_Off_2.svg</field>
            <field name="res_model">hr.leave.type</field>
            <field name="res_field">icon_id</field>
            <field name="public" eval="True"/>
            <field name="type">url</field>
            <field name="url">/hr_holidays/static/src/img/icons/Work_Accident_Time_Off_2.svg</field>
        </record>
    </data>
    <data noupdate="1">
        <!-- Casual leave -->
        <record id="holiday_status_cl" model="hr.leave.type">
            <field name="name">Paid Time Off</field>
            <field name="requires_allocation">yes</field>
            <field name="employee_requests">no</field>
            <field name="leave_validation_type">both</field>
            <field name="allocation_validation_type">officer</field>
            <field name="leave_notif_subtype_id" ref="mt_leave"/>
            <field name="allocation_notif_subtype_id" ref="mt_leave_allocation"/>
            <field name="responsible_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="icon_id" ref="hr_holidays.icon_14"/>
            <field name="color">2</field>
            <field name="company_id" eval="False"/> <!-- Explicitely set to False for it to be available to all companies -->
            <field name="sequence">1</field>
        </record>

        <!-- Sick leave -->
        <record id="holiday_status_sl" model="hr.leave.type">
            <field name="name">Sick Time Off</field>
            <field name="requires_allocation">no</field>
            <field name="leave_notif_subtype_id" ref="mt_leave_sick"/>
            <field name="responsible_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="support_document">True</field>
            <field name="icon_id" ref="hr_holidays.icon_22"/>
            <field name="color">3</field>
            <field name="company_id" eval="False"/> <!-- Explicitely set to False for it to be available to all companies -->
            <field name="sequence">2</field>
        </record>

        <!-- Compensatory Days -->
        <record id="holiday_status_comp" model="hr.leave.type">
            <field name="name">Compensatory Days</field>
            <field name="requires_allocation">yes</field>
            <field name="employee_requests">yes</field>
            <field name="leave_validation_type">manager</field>
            <field name="allocation_validation_type">officer</field>
            <field name="request_unit">hour</field>
            <field name="leave_notif_subtype_id" ref="mt_leave"/>
            <field name="responsible_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="icon_id" ref="hr_holidays.icon_4"/>
            <field name="color">4</field>
            <field name="company_id" eval="False"/> <!-- Explicitely set to False for it to be available to all companies -->
            <field name="sequence">4</field>
        </record>

        <!--Unpaid Time Off -->
        <record id="holiday_status_unpaid" model="hr.leave.type">
            <field name="name">Unpaid</field>
            <field name="requires_allocation">no</field>
            <field name="leave_validation_type">both</field>
            <field name="allocation_validation_type">officer</field>
            <field name="request_unit">hour</field>
            <field name="unpaid" eval="True"/>
            <field name="leave_notif_subtype_id" ref="mt_leave_unpaid"/>
            <field name="responsible_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="icon_id" ref="hr_holidays.icon_28"/>
            <field name="color">5</field>
            <field name="company_id" eval="False"/> <!-- Explicitely set to False for it to be available to all companies -->
            <field name="sequence">3</field>
        </record>
    </data>
</odoo>
