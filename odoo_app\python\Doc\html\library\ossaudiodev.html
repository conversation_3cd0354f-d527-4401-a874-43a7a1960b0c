<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="ossaudiodev — Access to OSS-compatible audio devices" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/ossaudiodev.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module allows you to access the OSS (Open Sound System) audio interface. OSS is available for a wide range of open-source and commercial Unices, and is the standard audio interface for Linux a..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module allows you to access the OSS (Open Sound System) audio interface. OSS is available for a wide range of open-source and commercial Unices, and is the standard audio interface for Linux a..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>ossaudiodev — Access to OSS-compatible audio devices &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="pipes — Interface to shell pipelines" href="pipes.html" />
    <link rel="prev" title="optparse — Parser for command line options" href="optparse.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/ossaudiodev.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code> — Access to OSS-compatible audio devices</a><ul>
<li><a class="reference internal" href="#audio-device-objects">Audio Device Objects</a></li>
<li><a class="reference internal" href="#mixer-device-objects">Mixer Device Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="optparse.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pipes.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pipes</span></code> — Interface to shell pipelines</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ossaudiodev.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pipes.html" title="pipes — Interface to shell pipelines"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="optparse.html" title="optparse — Parser for command line options"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code> — Access to OSS-compatible audio devices</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-ossaudiodev">
<span id="ossaudiodev-access-to-oss-compatible-audio-devices"></span><h1><a class="reference internal" href="#module-ossaudiodev" title="ossaudiodev: Access to OSS-compatible audio devices. (deprecated) (Linux, FreeBSD)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code></a> — Access to OSS-compatible audio devices<a class="headerlink" href="#module-ossaudiodev" title="Link to this heading">¶</a></h1>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-ossaudiodev" title="ossaudiodev: Access to OSS-compatible audio devices. (deprecated) (Linux, FreeBSD)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code></a> module is deprecated
(see <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#ossaudiodev"><strong>PEP 594</strong></a> for details).</p>
</div>
<hr class="docutils" />
<p>This module allows you to access the OSS (Open Sound System) audio interface.
OSS is available for a wide range of open-source and commercial Unices, and is
the standard audio interface for Linux and recent versions of FreeBSD.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Operations in this module now raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> where <a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a>
was raised.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="http://www.opensound.com/pguide/oss.pdf">Open Sound System Programmer’s Guide</a></dt><dd><p>the official documentation for the OSS C API</p>
</dd>
</dl>
<p>The module defines a large number of constants supplied by the OSS device
driver; see <code class="docutils literal notranslate"><span class="pre">&lt;sys/soundcard.h&gt;</span></code> on either Linux or FreeBSD for a listing.</p>
</div>
<p><a class="reference internal" href="#module-ossaudiodev" title="ossaudiodev: Access to OSS-compatible audio devices. (deprecated) (Linux, FreeBSD)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code></a> defines the following variables and functions:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="ossaudiodev.OSSAudioError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ossaudiodev.</span></span><span class="sig-name descname"><span class="pre">OSSAudioError</span></span><a class="headerlink" href="#ossaudiodev.OSSAudioError" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised on certain errors.  The argument is a string describing
what went wrong.</p>
<p>(If <a class="reference internal" href="#module-ossaudiodev" title="ossaudiodev: Access to OSS-compatible audio devices. (deprecated) (Linux, FreeBSD)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code></a> receives an error from a system call such as
<code class="xref c c-func docutils literal notranslate"><span class="pre">open()</span></code>, <code class="xref c c-func docutils literal notranslate"><span class="pre">write()</span></code>, or <code class="xref c c-func docutils literal notranslate"><span class="pre">ioctl()</span></code>, it raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.
Errors detected directly by <a class="reference internal" href="#module-ossaudiodev" title="ossaudiodev: Access to OSS-compatible audio devices. (deprecated) (Linux, FreeBSD)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code></a> result in <a class="reference internal" href="#ossaudiodev.OSSAudioError" title="ossaudiodev.OSSAudioError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSSAudioError</span></code></a>.)</p>
<p>(For backwards compatibility, the exception class is also available as
<code class="docutils literal notranslate"><span class="pre">ossaudiodev.error</span></code>.)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ossaudiodev.open">
<span class="sig-prename descclassname"><span class="pre">ossaudiodev.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.open" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">ossaudiodev.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">device</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Open an audio device and return an OSS audio device object.  This object
supports many file-like methods, such as <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code>, and
<code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code> (although there are subtle differences between conventional Unix
read/write semantics and those of OSS audio devices).  It also supports a number
of audio-specific methods; see below for the complete list of methods.</p>
<p><em>device</em> is the audio device filename to use.  If it is not specified, this
module first looks in the environment variable <span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">AUDIODEV</span></code> for a device
to use.  If not found, it falls back to <code class="file docutils literal notranslate"><span class="pre">/dev/dsp</span></code>.</p>
<p><em>mode</em> is one of <code class="docutils literal notranslate"><span class="pre">'r'</span></code> for read-only (record) access, <code class="docutils literal notranslate"><span class="pre">'w'</span></code> for
write-only (playback) access and <code class="docutils literal notranslate"><span class="pre">'rw'</span></code> for both. Since many sound cards
only allow one process to have the recorder or player open at a time, it is a
good idea to open the device only for the activity needed.  Further, some
sound cards are half-duplex: they can be opened for reading or writing, but
not both at once.</p>
<p>Note the unusual calling syntax: the <em>first</em> argument is optional, and the
second is required.  This is a historical artifact for compatibility with the
older <code class="xref py py-mod docutils literal notranslate"><span class="pre">linuxaudiodev</span></code> module which <a class="reference internal" href="#module-ossaudiodev" title="ossaudiodev: Access to OSS-compatible audio devices. (deprecated) (Linux, FreeBSD)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code></a> supersedes.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ossaudiodev.openmixer">
<span class="sig-prename descclassname"><span class="pre">ossaudiodev.</span></span><span class="sig-name descname"><span class="pre">openmixer</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">device</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.openmixer" title="Link to this definition">¶</a></dt>
<dd><p>Open a mixer device and return an OSS mixer device object.   <em>device</em> is the
mixer device filename to use.  If it is not specified, this module first looks
in the environment variable <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">MIXERDEV</span></code> for a device to use.  If not
found, it falls back to <code class="file docutils literal notranslate"><span class="pre">/dev/mixer</span></code>.</p>
</dd></dl>

<section id="audio-device-objects">
<span id="ossaudio-device-objects"></span><h2>Audio Device Objects<a class="headerlink" href="#audio-device-objects" title="Link to this heading">¶</a></h2>
<p>Before you can write to or read from an audio device, you must call three
methods in the correct order:</p>
<ol class="arabic simple">
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">setfmt()</span></code> to set the output format</p></li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">channels()</span></code> to set the number of channels</p></li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">speed()</span></code> to set the sample rate</p></li>
</ol>
<p>Alternately, you can use the <code class="xref py py-meth docutils literal notranslate"><span class="pre">setparameters()</span></code> method to set all three audio
parameters at once.  This is more convenient, but may not be as flexible in all
cases.</p>
<p>The audio device objects returned by <a class="reference internal" href="#ossaudiodev.open" title="ossaudiodev.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> define the following methods
and (read-only) attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.close">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.close" title="Link to this definition">¶</a></dt>
<dd><p>Explicitly close the audio device.  When you are done writing to or reading from
an audio device, you should explicitly close it.  A closed device cannot be used
again.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.fileno">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Return the file descriptor associated with the device.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.read">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.read" title="Link to this definition">¶</a></dt>
<dd><p>Read <em>size</em> bytes from the audio input and return them as a Python string.
Unlike most Unix device drivers, OSS audio devices in blocking mode (the
default) will block <a class="reference internal" href="#ossaudiodev.oss_audio_device.read" title="ossaudiodev.oss_audio_device.read"><code class="xref py py-func docutils literal notranslate"><span class="pre">read()</span></code></a> until the entire requested amount of data is
available.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.write">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.write" title="Link to this definition">¶</a></dt>
<dd><p>Write a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> <em>data</em> to the audio device and return the
number of bytes written.  If the audio device is in blocking mode (the
default), the entire data is always written (again, this is different from
usual Unix device semantics).  If the device is in non-blocking mode, some
data may not be written—see <a class="reference internal" href="#ossaudiodev.oss_audio_device.writeall" title="ossaudiodev.oss_audio_device.writeall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeall()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.writeall">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">writeall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.writeall" title="Link to this definition">¶</a></dt>
<dd><p>Write a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> <em>data</em> to the audio device: waits until
the audio device is able to accept data, writes as much data as it will
accept, and repeats until <em>data</em> has been completely written. If the device
is in blocking mode (the default), this has the same effect as
<a class="reference internal" href="#ossaudiodev.oss_audio_device.write" title="ossaudiodev.oss_audio_device.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a>; <a class="reference internal" href="#ossaudiodev.oss_audio_device.writeall" title="ossaudiodev.oss_audio_device.writeall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeall()</span></code></a> is only useful in non-blocking mode.  Has
no return value, since the amount of data written is always equal to the
amount of data supplied.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Audio device objects also support the context management protocol, i.e. they can
be used in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.</p>
</div>
<p>The following methods each map to exactly one <code class="xref c c-func docutils literal notranslate"><span class="pre">ioctl()</span></code> system call.  The
correspondence is obvious: for example, <code class="xref py py-meth docutils literal notranslate"><span class="pre">setfmt()</span></code> corresponds to the
<code class="docutils literal notranslate"><span class="pre">SNDCTL_DSP_SETFMT</span></code> ioctl, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">sync()</span></code> to <code class="docutils literal notranslate"><span class="pre">SNDCTL_DSP_SYNC</span></code> (this can
be useful when consulting the OSS documentation).  If the underlying
<code class="xref c c-func docutils literal notranslate"><span class="pre">ioctl()</span></code> fails, they all raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.nonblock">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">nonblock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.nonblock" title="Link to this definition">¶</a></dt>
<dd><p>Put the device into non-blocking mode.  Once in non-blocking mode, there is no
way to return it to blocking mode.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.getfmts">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">getfmts</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.getfmts" title="Link to this definition">¶</a></dt>
<dd><p>Return a bitmask of the audio output formats supported by the soundcard.  Some
of the formats supported by OSS are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Format</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_MU_LAW</span></code></p></td>
<td><p>a logarithmic encoding (used by Sun <code class="docutils literal notranslate"><span class="pre">.au</span></code>
files and <code class="file docutils literal notranslate"><span class="pre">/dev/audio</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_A_LAW</span></code></p></td>
<td><p>a logarithmic encoding</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_IMA_ADPCM</span></code></p></td>
<td><p>a 4:1 compressed format defined by the
Interactive Multimedia Association</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_U8</span></code></p></td>
<td><p>Unsigned, 8-bit audio</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_S16_LE</span></code></p></td>
<td><p>Signed, 16-bit audio, little-endian byte
order (as used by Intel processors)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_S16_BE</span></code></p></td>
<td><p>Signed, 16-bit audio, big-endian byte order
(as used by 68k, PowerPC, Sparc)</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_S8</span></code></p></td>
<td><p>Signed, 8 bit audio</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_U16_LE</span></code></p></td>
<td><p>Unsigned, 16-bit little-endian audio</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_U16_BE</span></code></p></td>
<td><p>Unsigned, 16-bit big-endian audio</p></td>
</tr>
</tbody>
</table>
<p>Consult the OSS documentation for a full list of audio formats, and note that
most devices support only a subset of these formats.  Some older devices only
support <code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_U8</span></code>; the most common format used today is
<code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_S16_LE</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.setfmt">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">setfmt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.setfmt" title="Link to this definition">¶</a></dt>
<dd><p>Try to set the current audio format to <em>format</em>—see <a class="reference internal" href="#ossaudiodev.oss_audio_device.getfmts" title="ossaudiodev.oss_audio_device.getfmts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getfmts()</span></code></a> for a
list.  Returns the audio format that the device was set to, which may not be the
requested format.  May also be used to return the current audio format—do this
by passing an “audio format” of <code class="xref py py-const docutils literal notranslate"><span class="pre">AFMT_QUERY</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.channels">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">channels</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nchannels</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.channels" title="Link to this definition">¶</a></dt>
<dd><p>Set the number of output channels to <em>nchannels</em>.  A value of 1 indicates
monophonic sound, 2 stereophonic.  Some devices may have more than 2 channels,
and some high-end devices may not support mono. Returns the number of channels
the device was set to.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.speed">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">speed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">samplerate</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.speed" title="Link to this definition">¶</a></dt>
<dd><p>Try to set the audio sampling rate to <em>samplerate</em> samples per second.  Returns
the rate actually set.  Most sound devices don’t support arbitrary sampling
rates.  Common rates are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Rate</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>8000</p></td>
<td><p>default rate for <code class="file docutils literal notranslate"><span class="pre">/dev/audio</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>11025</p></td>
<td><p>speech recording</p></td>
</tr>
<tr class="row-even"><td><p>22050</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p>44100</p></td>
<td><p>CD quality audio (at 16 bits/sample and 2
channels)</p></td>
</tr>
<tr class="row-even"><td><p>96000</p></td>
<td><p>DVD quality audio (at 24 bits/sample)</p></td>
</tr>
</tbody>
</table>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.sync">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">sync</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.sync" title="Link to this definition">¶</a></dt>
<dd><p>Wait until the sound device has played every byte in its buffer.  (This happens
implicitly when the device is closed.)  The OSS documentation recommends closing
and re-opening the device rather than using <a class="reference internal" href="#ossaudiodev.oss_audio_device.sync" title="ossaudiodev.oss_audio_device.sync"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sync()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.reset">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.reset" title="Link to this definition">¶</a></dt>
<dd><p>Immediately stop playing or recording and return the device to a state where it
can accept commands.  The OSS documentation recommends closing and re-opening
the device after calling <a class="reference internal" href="#ossaudiodev.oss_audio_device.reset" title="ossaudiodev.oss_audio_device.reset"><code class="xref py py-meth docutils literal notranslate"><span class="pre">reset()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.post">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">post</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.post" title="Link to this definition">¶</a></dt>
<dd><p>Tell the driver that there is likely to be a pause in the output, making it
possible for the device to handle the pause more intelligently.  You might use
this after playing a spot sound effect, before waiting for user input, or before
doing disk I/O.</p>
</dd></dl>

<p>The following convenience methods combine several ioctls, or one ioctl and some
simple calculations.</p>
<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.setparameters">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">setparameters</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nchannels</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">samplerate</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">strict=False</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.setparameters" title="Link to this definition">¶</a></dt>
<dd><p>Set the key audio sampling parameters—sample format, number of channels, and
sampling rate—in one method call.  <em>format</em>,  <em>nchannels</em>, and <em>samplerate</em>
should be as specified in the <a class="reference internal" href="#ossaudiodev.oss_audio_device.setfmt" title="ossaudiodev.oss_audio_device.setfmt"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setfmt()</span></code></a>, <a class="reference internal" href="#ossaudiodev.oss_audio_device.channels" title="ossaudiodev.oss_audio_device.channels"><code class="xref py py-meth docutils literal notranslate"><span class="pre">channels()</span></code></a>, and
<a class="reference internal" href="#ossaudiodev.oss_audio_device.speed" title="ossaudiodev.oss_audio_device.speed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">speed()</span></code></a>  methods.  If <em>strict</em> is true, <a class="reference internal" href="#ossaudiodev.oss_audio_device.setparameters" title="ossaudiodev.oss_audio_device.setparameters"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setparameters()</span></code></a> checks to
see if each parameter was actually set to the requested value, and raises
<a class="reference internal" href="#ossaudiodev.OSSAudioError" title="ossaudiodev.OSSAudioError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSSAudioError</span></code></a> if not.  Returns a tuple (<em>format</em>, <em>nchannels</em>,
<em>samplerate</em>) indicating the parameter values that were actually set by the
device driver (i.e., the same as the return values of <a class="reference internal" href="#ossaudiodev.oss_audio_device.setfmt" title="ossaudiodev.oss_audio_device.setfmt"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setfmt()</span></code></a>,
<a class="reference internal" href="#ossaudiodev.oss_audio_device.channels" title="ossaudiodev.oss_audio_device.channels"><code class="xref py py-meth docutils literal notranslate"><span class="pre">channels()</span></code></a>, and <a class="reference internal" href="#ossaudiodev.oss_audio_device.speed" title="ossaudiodev.oss_audio_device.speed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">speed()</span></code></a>).</p>
<p>For example,</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">fmt</span><span class="p">,</span> <span class="n">channels</span><span class="p">,</span> <span class="n">rate</span><span class="p">)</span> <span class="o">=</span> <span class="n">dsp</span><span class="o">.</span><span class="n">setparameters</span><span class="p">(</span><span class="n">fmt</span><span class="p">,</span> <span class="n">channels</span><span class="p">,</span> <span class="n">rate</span><span class="p">)</span>
</pre></div>
</div>
<p>is equivalent to</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">fmt</span> <span class="o">=</span> <span class="n">dsp</span><span class="o">.</span><span class="n">setfmt</span><span class="p">(</span><span class="n">fmt</span><span class="p">)</span>
<span class="n">channels</span> <span class="o">=</span> <span class="n">dsp</span><span class="o">.</span><span class="n">channels</span><span class="p">(</span><span class="n">channels</span><span class="p">)</span>
<span class="n">rate</span> <span class="o">=</span> <span class="n">dsp</span><span class="o">.</span><span class="n">rate</span><span class="p">(</span><span class="n">rate</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.bufsize">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">bufsize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.bufsize" title="Link to this definition">¶</a></dt>
<dd><p>Returns the size of the hardware buffer, in samples.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.obufcount">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">obufcount</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.obufcount" title="Link to this definition">¶</a></dt>
<dd><p>Returns the number of samples that are in the hardware buffer yet to be played.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.obuffree">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">obuffree</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_audio_device.obuffree" title="Link to this definition">¶</a></dt>
<dd><p>Returns the number of samples that could be queued into the hardware buffer to
be played without blocking.</p>
</dd></dl>

<p>Audio device objects also support several read-only attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.closed">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">closed</span></span><a class="headerlink" href="#ossaudiodev.oss_audio_device.closed" title="Link to this definition">¶</a></dt>
<dd><p>Boolean indicating whether the device has been closed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.name">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#ossaudiodev.oss_audio_device.name" title="Link to this definition">¶</a></dt>
<dd><p>String containing the name of the device file.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ossaudiodev.oss_audio_device.mode">
<span class="sig-prename descclassname"><span class="pre">oss_audio_device.</span></span><span class="sig-name descname"><span class="pre">mode</span></span><a class="headerlink" href="#ossaudiodev.oss_audio_device.mode" title="Link to this definition">¶</a></dt>
<dd><p>The I/O mode for the file, either <code class="docutils literal notranslate"><span class="pre">&quot;r&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;rw&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;w&quot;</span></code>.</p>
</dd></dl>

</section>
<section id="mixer-device-objects">
<span id="id1"></span><h2>Mixer Device Objects<a class="headerlink" href="#mixer-device-objects" title="Link to this heading">¶</a></h2>
<p>The mixer object provides two file-like methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.close">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.close" title="Link to this definition">¶</a></dt>
<dd><p>This method closes the open mixer device file.  Any further attempts to use the
mixer after this file is closed will raise an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.fileno">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Returns the file handle number of the open mixer device file.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Mixer objects also support the context management protocol.</p>
</div>
<p>The remaining methods are specific to audio mixing:</p>
<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.controls">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">controls</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.controls" title="Link to this definition">¶</a></dt>
<dd><p>This method returns a bitmask specifying the available mixer controls (“Control”
being a specific mixable “channel”, such as <code class="xref py py-const docutils literal notranslate"><span class="pre">SOUND_MIXER_PCM</span></code> or
<code class="xref py py-const docutils literal notranslate"><span class="pre">SOUND_MIXER_SYNTH</span></code>).  This bitmask indicates a subset of all available
mixer controls—the <code class="xref py py-const docutils literal notranslate"><span class="pre">SOUND_MIXER_*</span></code> constants defined at module level.
To determine if, for example, the current mixer object supports a PCM mixer, use
the following Python code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">mixer</span><span class="o">=</span><span class="n">ossaudiodev</span><span class="o">.</span><span class="n">openmixer</span><span class="p">()</span>
<span class="k">if</span> <span class="n">mixer</span><span class="o">.</span><span class="n">controls</span><span class="p">()</span> <span class="o">&amp;</span> <span class="p">(</span><span class="mi">1</span> <span class="o">&lt;&lt;</span> <span class="n">ossaudiodev</span><span class="o">.</span><span class="n">SOUND_MIXER_PCM</span><span class="p">):</span>
    <span class="c1"># PCM is supported</span>
    <span class="o">...</span> <span class="n">code</span> <span class="o">...</span>
</pre></div>
</div>
<p>For most purposes, the <code class="xref py py-const docutils literal notranslate"><span class="pre">SOUND_MIXER_VOLUME</span></code> (master volume) and
<code class="xref py py-const docutils literal notranslate"><span class="pre">SOUND_MIXER_PCM</span></code> controls should suffice—but code that uses the mixer
should be flexible when it comes to choosing mixer controls.  On the Gravis
Ultrasound, for example, <code class="xref py py-const docutils literal notranslate"><span class="pre">SOUND_MIXER_VOLUME</span></code> does not exist.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.stereocontrols">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">stereocontrols</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.stereocontrols" title="Link to this definition">¶</a></dt>
<dd><p>Returns a bitmask indicating stereo mixer controls.  If a bit is set, the
corresponding control is stereo; if it is unset, the control is either
monophonic or not supported by the mixer (use in combination with
<a class="reference internal" href="#ossaudiodev.oss_mixer_device.controls" title="ossaudiodev.oss_mixer_device.controls"><code class="xref py py-meth docutils literal notranslate"><span class="pre">controls()</span></code></a> to determine which).</p>
<p>See the code example for the <a class="reference internal" href="#ossaudiodev.oss_mixer_device.controls" title="ossaudiodev.oss_mixer_device.controls"><code class="xref py py-meth docutils literal notranslate"><span class="pre">controls()</span></code></a> function for an example of getting
data from a bitmask.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.reccontrols">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">reccontrols</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.reccontrols" title="Link to this definition">¶</a></dt>
<dd><p>Returns a bitmask specifying the mixer controls that may be used to record.  See
the code example for <a class="reference internal" href="#ossaudiodev.oss_mixer_device.controls" title="ossaudiodev.oss_mixer_device.controls"><code class="xref py py-meth docutils literal notranslate"><span class="pre">controls()</span></code></a> for an example of reading from a bitmask.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.get">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">control</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.get" title="Link to this definition">¶</a></dt>
<dd><p>Returns the volume of a given mixer control.  The returned volume is a 2-tuple
<code class="docutils literal notranslate"><span class="pre">(left_volume,right_volume)</span></code>.  Volumes are specified as numbers from 0
(silent) to 100 (full volume).  If the control is monophonic, a 2-tuple is still
returned, but both volumes are the same.</p>
<p>Raises <a class="reference internal" href="#ossaudiodev.OSSAudioError" title="ossaudiodev.OSSAudioError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSSAudioError</span></code></a> if an invalid control is specified, or
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if an unsupported control is specified.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.set">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">control</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">(left</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">right)</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.set" title="Link to this definition">¶</a></dt>
<dd><p>Sets the volume for a given mixer control to <code class="docutils literal notranslate"><span class="pre">(left,right)</span></code>. <code class="docutils literal notranslate"><span class="pre">left</span></code> and
<code class="docutils literal notranslate"><span class="pre">right</span></code> must be ints and between 0 (silent) and 100 (full volume).  On
success, the new volume is returned as a 2-tuple. Note that this may not be
exactly the same as the volume specified, because of the limited resolution of
some soundcard’s mixers.</p>
<p>Raises <a class="reference internal" href="#ossaudiodev.OSSAudioError" title="ossaudiodev.OSSAudioError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSSAudioError</span></code></a> if an invalid mixer control was specified, or if the
specified volumes were out-of-range.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.get_recsrc">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">get_recsrc</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.get_recsrc" title="Link to this definition">¶</a></dt>
<dd><p>This method returns a bitmask indicating which control(s) are currently being
used as a recording source.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ossaudiodev.oss_mixer_device.set_recsrc">
<span class="sig-prename descclassname"><span class="pre">oss_mixer_device.</span></span><span class="sig-name descname"><span class="pre">set_recsrc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bitmask</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ossaudiodev.oss_mixer_device.set_recsrc" title="Link to this definition">¶</a></dt>
<dd><p>Call this function to specify a recording source.  Returns a bitmask indicating
the new recording source (or sources) if successful; raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if an
invalid source was specified.  To set the current recording source to the
microphone input:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">mixer</span><span class="o">.</span><span class="n">setrecsrc</span> <span class="p">(</span><span class="mi">1</span> <span class="o">&lt;&lt;</span> <span class="n">ossaudiodev</span><span class="o">.</span><span class="n">SOUND_MIXER_MIC</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code> — Access to OSS-compatible audio devices</a><ul>
<li><a class="reference internal" href="#audio-device-objects">Audio Device Objects</a></li>
<li><a class="reference internal" href="#mixer-device-objects">Mixer Device Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="optparse.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pipes.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pipes</span></code> — Interface to shell pipelines</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ossaudiodev.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pipes.html" title="pipes — Interface to shell pipelines"
             >next</a> |</li>
        <li class="right" >
          <a href="optparse.html" title="optparse — Parser for command line options"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code> — Access to OSS-compatible audio devices</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>