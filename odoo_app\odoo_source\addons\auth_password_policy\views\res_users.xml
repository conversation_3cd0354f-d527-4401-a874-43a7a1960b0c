<odoo>
    <record id="change_password" model="ir.ui.view">
        <field name="name">Enable password meter on own password wizard</field>
        <field name="inherit_id" ref="base.change_password_own_form"/>
        <field name="model">change.password.own</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='new_password']" position="attributes">
                <attribute name="widget">password_meter</attribute>
            </xpath>
        </field>
    </record>
    <record id="change_password_multi" model="ir.ui.view">
        <field name="name">Enable password meter on multi passwords wizard</field>
        <field name="inherit_id" ref="base.change_password_wizard_user_tree_view"/>
        <field name="model">change.password.user</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='new_passwd']" position="attributes">
                <attribute name="widget">password_meter</attribute>
            </xpath>
        </field>
    </record>
</odoo>
