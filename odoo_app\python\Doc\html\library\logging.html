<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="logging — Logging facility for Python" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/logging.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/logging/__init__.py Important: This page contains the API reference information. For tutorial information and discussion of more advanced topics, see Basic Tutorial, Advanced Tutor..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/logging/__init__.py Important: This page contains the API reference information. For tutorial information and discussion of more advanced topics, see Basic Tutorial, Advanced Tutor..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>logging — Logging facility for Python &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="logging.config — Logging configuration" href="logging.config.html" />
    <link rel="prev" title="getopt — C-style parser for command line options" href="getopt.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/logging.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code> — Logging facility for Python</a><ul>
<li><a class="reference internal" href="#logger-objects">Logger Objects</a></li>
<li><a class="reference internal" href="#logging-levels">Logging Levels</a></li>
<li><a class="reference internal" href="#handler-objects">Handler Objects</a></li>
<li><a class="reference internal" href="#formatter-objects">Formatter Objects</a></li>
<li><a class="reference internal" href="#filter-objects">Filter Objects</a></li>
<li><a class="reference internal" href="#logrecord-objects">LogRecord Objects</a></li>
<li><a class="reference internal" href="#logrecord-attributes">LogRecord attributes</a></li>
<li><a class="reference internal" href="#loggeradapter-objects">LoggerAdapter Objects</a></li>
<li><a class="reference internal" href="#thread-safety">Thread Safety</a></li>
<li><a class="reference internal" href="#module-level-functions">Module-Level Functions</a></li>
<li><a class="reference internal" href="#module-level-attributes">Module-Level Attributes</a></li>
<li><a class="reference internal" href="#integration-with-the-warnings-module">Integration with the warnings module</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="getopt.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">getopt</span></code> — C-style parser for command line options</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="logging.config.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code> — Logging configuration</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/logging.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="logging.config.html" title="logging.config — Logging configuration"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="getopt.html" title="getopt — C-style parser for command line options"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" accesskey="U">Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code> — Logging facility for Python</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-logging">
<span id="logging-logging-facility-for-python"></span><h1><a class="reference internal" href="#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> — Logging facility for Python<a class="headerlink" href="#module-logging" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/logging/__init__.py">Lib/logging/__init__.py</a></p>
<aside class="sidebar" id="index-0">
<p class="sidebar-title">Important</p>
<p>This page contains the API reference information. For tutorial
information and discussion of more advanced topics, see</p>
<ul class="simple">
<li><p><a class="reference internal" href="../howto/logging.html#logging-basic-tutorial"><span class="std std-ref">Basic Tutorial</span></a></p></li>
<li><p><a class="reference internal" href="../howto/logging.html#logging-advanced-tutorial"><span class="std std-ref">Advanced Tutorial</span></a></p></li>
<li><p><a class="reference internal" href="../howto/logging-cookbook.html#logging-cookbook"><span class="std std-ref">Logging Cookbook</span></a></p></li>
</ul>
</aside>
<hr class="docutils" />
<p>This module defines functions and classes which implement a flexible event
logging system for applications and libraries.</p>
<p>The key benefit of having the logging API provided by a standard library module
is that all Python modules can participate in logging, so your application log
can include your own messages integrated with messages from third-party
modules.</p>
<p>Here’s a simple example of idiomatic usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># myapp.py</span>
<span class="kn">import</span> <span class="nn">logging</span>
<span class="kn">import</span> <span class="nn">mylib</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">basicConfig</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="s1">&#39;myapp.log&#39;</span><span class="p">,</span> <span class="n">level</span><span class="o">=</span><span class="n">logging</span><span class="o">.</span><span class="n">INFO</span><span class="p">)</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s1">&#39;Started&#39;</span><span class="p">)</span>
    <span class="n">mylib</span><span class="o">.</span><span class="n">do_something</span><span class="p">()</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s1">&#39;Finished&#39;</span><span class="p">)</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># mylib.py</span>
<span class="kn">import</span> <span class="nn">logging</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">do_something</span><span class="p">():</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s1">&#39;Doing something&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>If you run <em>myapp.py</em>, you should see this in <em>myapp.log</em>:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>INFO:__main__:Started
INFO:mylib:Doing something
INFO:__main__:Finished
</pre></div>
</div>
<p>The key feature of this idiomatic usage is that the majority of code is simply
creating a module level logger with <code class="docutils literal notranslate"><span class="pre">getLogger(__name__)</span></code>, and using that
logger to do any needed logging. This is concise, while allowing downstream
code fine-grained control if needed. Logged messages to the module-level logger
get forwarded to handlers of loggers in higher-level modules, all the way up to
the highest-level logger known as the root logger; this approach is known as
hierarchical logging.</p>
<p>For logging to be useful, it needs to be configured: setting the levels and
destinations for each logger, potentially changing how specific modules log,
often based on command-line arguments or application configuration. In most
cases, like the one above, only the root logger needs to be so configured, since
all the lower level loggers at module level eventually forward their messages to
its handlers.  <a class="reference internal" href="#logging.basicConfig" title="logging.basicConfig"><code class="xref py py-func docutils literal notranslate"><span class="pre">basicConfig()</span></code></a> provides a quick way to configure
the root logger that handles many use cases.</p>
<p>The module provides a lot of functionality and flexibility.  If you are
unfamiliar with logging, the best way to get to grips with it is to view the
tutorials (<strong>see the links above and on the right</strong>).</p>
<p>The basic classes defined by the module, together with their attributes and
methods, are listed in the sections below.</p>
<ul class="simple">
<li><p>Loggers expose the interface that application code directly uses.</p></li>
<li><p>Handlers send the log records (created by loggers) to the appropriate
destination.</p></li>
<li><p>Filters provide a finer grained facility for determining which log records
to output.</p></li>
<li><p>Formatters specify the layout of log records in the final output.</p></li>
</ul>
<section id="logger-objects">
<span id="logger"></span><h2>Logger Objects<a class="headerlink" href="#logger-objects" title="Link to this heading">¶</a></h2>
<p>Loggers have the following attributes and methods.  Note that Loggers should
<em>NEVER</em> be instantiated directly, but always through the module-level function
<code class="docutils literal notranslate"><span class="pre">logging.getLogger(name)</span></code>.  Multiple calls to <a class="reference internal" href="#logging.getLogger" title="logging.getLogger"><code class="xref py py-func docutils literal notranslate"><span class="pre">getLogger()</span></code></a> with the same
name will always return a reference to the same Logger object.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">name</span></code> is potentially a period-separated hierarchical value, like
<code class="docutils literal notranslate"><span class="pre">foo.bar.baz</span></code> (though it could also be just plain <code class="docutils literal notranslate"><span class="pre">foo</span></code>, for example).
Loggers that are further down in the hierarchical list are children of loggers
higher up in the list.  For example, given a logger with a name of <code class="docutils literal notranslate"><span class="pre">foo</span></code>,
loggers with names of <code class="docutils literal notranslate"><span class="pre">foo.bar</span></code>, <code class="docutils literal notranslate"><span class="pre">foo.bar.baz</span></code>, and <code class="docutils literal notranslate"><span class="pre">foo.bam</span></code> are all
descendants of <code class="docutils literal notranslate"><span class="pre">foo</span></code>.  The logger name hierarchy is analogous to the Python
package hierarchy, and identical to it if you organise your loggers on a
per-module basis using the recommended construction
<code class="docutils literal notranslate"><span class="pre">logging.getLogger(__name__)</span></code>.  That’s because in a module, <code class="docutils literal notranslate"><span class="pre">__name__</span></code>
is the module’s name in the Python package namespace.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.Logger">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">Logger</span></span><a class="headerlink" href="#logging.Logger" title="Link to this definition">¶</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="logging.Logger.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#logging.Logger.name" title="Link to this definition">¶</a></dt>
<dd><p>This is the logger’s name, and is the value that was passed to <a class="reference internal" href="#logging.getLogger" title="logging.getLogger"><code class="xref py py-func docutils literal notranslate"><span class="pre">getLogger()</span></code></a>
to obtain the logger.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This attribute should be treated as read-only.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.Logger.level">
<span class="sig-name descname"><span class="pre">level</span></span><a class="headerlink" href="#logging.Logger.level" title="Link to this definition">¶</a></dt>
<dd><p>The threshold of this logger, as set by the <a class="reference internal" href="#logging.Logger.setLevel" title="logging.Logger.setLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setLevel()</span></code></a> method.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Do not set this attribute directly - always use <a class="reference internal" href="#logging.Logger.setLevel" title="logging.Logger.setLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setLevel()</span></code></a>,
which has checks for the level passed to it.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.Logger.parent">
<span class="sig-name descname"><span class="pre">parent</span></span><a class="headerlink" href="#logging.Logger.parent" title="Link to this definition">¶</a></dt>
<dd><p>The parent logger of this logger. It may change based on later instantiation
of loggers which are higher up in the namespace hierarchy.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This value should be treated as read-only.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.Logger.propagate">
<span class="sig-name descname"><span class="pre">propagate</span></span><a class="headerlink" href="#logging.Logger.propagate" title="Link to this definition">¶</a></dt>
<dd><p>If this attribute evaluates to true, events logged to this logger will be
passed to the handlers of higher level (ancestor) loggers, in addition to
any handlers attached to this logger. Messages are passed directly to the
ancestor loggers’ handlers - neither the level nor filters of the ancestor
loggers in question are considered.</p>
<p>If this evaluates to false, logging messages are not passed to the handlers
of ancestor loggers.</p>
<p>Spelling it out with an example: If the propagate attribute of the logger named
<code class="docutils literal notranslate"><span class="pre">A.B.C</span></code> evaluates to true, any event logged to <code class="docutils literal notranslate"><span class="pre">A.B.C</span></code> via a method call such as
<code class="docutils literal notranslate"><span class="pre">logging.getLogger('A.B.C').error(...)</span></code> will [subject to passing that logger’s
level and filter settings] be passed in turn to any handlers attached to loggers
named <code class="docutils literal notranslate"><span class="pre">A.B</span></code>, <code class="docutils literal notranslate"><span class="pre">A</span></code> and the root logger, after first being passed to any handlers
attached to <code class="docutils literal notranslate"><span class="pre">A.B.C</span></code>. If any logger in the chain <code class="docutils literal notranslate"><span class="pre">A.B.C</span></code>, <code class="docutils literal notranslate"><span class="pre">A.B</span></code>, <code class="docutils literal notranslate"><span class="pre">A</span></code> has its
<code class="docutils literal notranslate"><span class="pre">propagate</span></code> attribute set to false, then that is the last logger whose handlers
are offered the event to handle, and propagation stops at that point.</p>
<p>The constructor sets this attribute to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you attach a handler to a logger <em>and</em> one or more of its
ancestors, it may emit the same record multiple times. In general, you
should not need to attach a handler to more than one logger - if you just
attach it to the appropriate logger which is highest in the logger
hierarchy, then it will see all events logged by all descendant loggers,
provided that their propagate setting is left set to <code class="docutils literal notranslate"><span class="pre">True</span></code>. A common
scenario is to attach handlers only to the root logger, and to let
propagation take care of the rest.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.Logger.handlers">
<span class="sig-name descname"><span class="pre">handlers</span></span><a class="headerlink" href="#logging.Logger.handlers" title="Link to this definition">¶</a></dt>
<dd><p>The list of handlers directly attached to this logger instance.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This attribute should be treated as read-only; it is normally changed via
the <a class="reference internal" href="#logging.Logger.addHandler" title="logging.Logger.addHandler"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addHandler()</span></code></a> and <a class="reference internal" href="#logging.Logger.removeHandler" title="logging.Logger.removeHandler"><code class="xref py py-meth docutils literal notranslate"><span class="pre">removeHandler()</span></code></a> methods, which use locks to ensure
thread-safe operation.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.Logger.disabled">
<span class="sig-name descname"><span class="pre">disabled</span></span><a class="headerlink" href="#logging.Logger.disabled" title="Link to this definition">¶</a></dt>
<dd><p>This attribute disables handling of any events. It is set to <code class="docutils literal notranslate"><span class="pre">False</span></code> in the
initializer, and only changed by logging configuration code.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This attribute should be treated as read-only.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.setLevel">
<span class="sig-name descname"><span class="pre">setLevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.setLevel" title="Link to this definition">¶</a></dt>
<dd><p>Sets the threshold for this logger to <em>level</em>. Logging messages which are less
severe than <em>level</em> will be ignored; logging messages which have severity <em>level</em>
or higher will be emitted by whichever handler or handlers service this logger,
unless a handler’s level has been set to a higher severity level than <em>level</em>.</p>
<p>When a logger is created, the level is set to <a class="reference internal" href="#logging.NOTSET" title="logging.NOTSET"><code class="xref py py-const docutils literal notranslate"><span class="pre">NOTSET</span></code></a> (which causes
all messages to be processed when the logger is the root logger, or delegation
to the parent when the logger is a non-root logger). Note that the root logger
is created with level <a class="reference internal" href="#logging.WARNING" title="logging.WARNING"><code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code></a>.</p>
<p>The term ‘delegation to the parent’ means that if a logger has a level of
NOTSET, its chain of ancestor loggers is traversed until either an ancestor with
a level other than NOTSET is found, or the root is reached.</p>
<p>If an ancestor is found with a level other than NOTSET, then that ancestor’s
level is treated as the effective level of the logger where the ancestor search
began, and is used to determine how a logging event is handled.</p>
<p>If the root is reached, and it has a level of NOTSET, then all messages will be
processed. Otherwise, the root’s level will be used as the effective level.</p>
<p>See <a class="reference internal" href="#levels"><span class="std std-ref">Logging Levels</span></a> for a list of levels.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>level</em> parameter now accepts a string representation of the
level such as ‘INFO’ as an alternative to the integer constants
such as <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code></a>. Note, however, that levels are internally stored
as integers, and methods such as e.g. <a class="reference internal" href="#logging.Logger.getEffectiveLevel" title="logging.Logger.getEffectiveLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getEffectiveLevel()</span></code></a> and
<a class="reference internal" href="#logging.Logger.isEnabledFor" title="logging.Logger.isEnabledFor"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isEnabledFor()</span></code></a> will return/expect to be passed integers.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.isEnabledFor">
<span class="sig-name descname"><span class="pre">isEnabledFor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.isEnabledFor" title="Link to this definition">¶</a></dt>
<dd><p>Indicates if a message of severity <em>level</em> would be processed by this logger.
This method checks first the module-level level set by
<code class="docutils literal notranslate"><span class="pre">logging.disable(level)</span></code> and then the logger’s effective level as determined
by <a class="reference internal" href="#logging.Logger.getEffectiveLevel" title="logging.Logger.getEffectiveLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getEffectiveLevel()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.getEffectiveLevel">
<span class="sig-name descname"><span class="pre">getEffectiveLevel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.getEffectiveLevel" title="Link to this definition">¶</a></dt>
<dd><p>Indicates the effective level for this logger. If a value other than
<a class="reference internal" href="#logging.NOTSET" title="logging.NOTSET"><code class="xref py py-const docutils literal notranslate"><span class="pre">NOTSET</span></code></a> has been set using <a class="reference internal" href="#logging.Logger.setLevel" title="logging.Logger.setLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setLevel()</span></code></a>, it is returned. Otherwise,
the hierarchy is traversed towards the root until a value other than
<a class="reference internal" href="#logging.NOTSET" title="logging.NOTSET"><code class="xref py py-const docutils literal notranslate"><span class="pre">NOTSET</span></code></a> is found, and that value is returned. The value returned is
an integer, typically one of <a class="reference internal" href="#logging.DEBUG" title="logging.DEBUG"><code class="xref py py-const docutils literal notranslate"><span class="pre">logging.DEBUG</span></code></a>, <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">logging.INFO</span></code></a>
etc.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.getChild">
<span class="sig-name descname"><span class="pre">getChild</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">suffix</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.getChild" title="Link to this definition">¶</a></dt>
<dd><p>Returns a logger which is a descendant to this logger, as determined by the suffix.
Thus, <code class="docutils literal notranslate"><span class="pre">logging.getLogger('abc').getChild('def.ghi')</span></code> would return the same
logger as would be returned by <code class="docutils literal notranslate"><span class="pre">logging.getLogger('abc.def.ghi')</span></code>. This is a
convenience method, useful when the parent logger is named using e.g. <code class="docutils literal notranslate"><span class="pre">__name__</span></code>
rather than a literal string.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.getChildren">
<span class="sig-name descname"><span class="pre">getChildren</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.getChildren" title="Link to this definition">¶</a></dt>
<dd><p>Returns a set of loggers which are immediate children of this logger. So for
example <code class="docutils literal notranslate"><span class="pre">logging.getLogger().getChildren()</span></code> might return a set containing
loggers named <code class="docutils literal notranslate"><span class="pre">foo</span></code> and <code class="docutils literal notranslate"><span class="pre">bar</span></code>, but a logger named <code class="docutils literal notranslate"><span class="pre">foo.bar</span></code> wouldn’t be
included in the set. Likewise, <code class="docutils literal notranslate"><span class="pre">logging.getLogger('foo').getChildren()</span></code> might
return a set including a logger named <code class="docutils literal notranslate"><span class="pre">foo.bar</span></code>, but it wouldn’t include one
named <code class="docutils literal notranslate"><span class="pre">foo.bar.baz</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.debug">
<span class="sig-name descname"><span class="pre">debug</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.debug" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.DEBUG" title="logging.DEBUG"><code class="xref py py-const docutils literal notranslate"><span class="pre">DEBUG</span></code></a> on this logger. The <em>msg</em> is the
message format string, and the <em>args</em> are the arguments which are merged into
<em>msg</em> using the string formatting operator. (Note that this means that you can
use keywords in the format string, together with a single dictionary argument.)
No % formatting operation is performed on <em>msg</em> when no <em>args</em> are supplied.</p>
<p>There are four keyword arguments in <em>kwargs</em> which are inspected:
<em>exc_info</em>, <em>stack_info</em>, <em>stacklevel</em> and <em>extra</em>.</p>
<p>If <em>exc_info</em> does not evaluate as false, it causes exception information to be
added to the logging message. If an exception tuple (in the format returned by
<a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>) or an exception instance is provided, it is used;
otherwise, <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a> is called to get the exception information.</p>
<p>The second optional keyword argument is <em>stack_info</em>, which defaults to
<code class="docutils literal notranslate"><span class="pre">False</span></code>. If true, stack information is added to the logging
message, including the actual logging call. Note that this is not the same
stack information as that displayed through specifying <em>exc_info</em>: The
former is stack frames from the bottom of the stack up to the logging call
in the current thread, whereas the latter is information about stack frames
which have been unwound, following an exception, while searching for
exception handlers.</p>
<p>You can specify <em>stack_info</em> independently of <em>exc_info</em>, e.g. to just show
how you got to a certain point in your code, even when no exceptions were
raised. The stack frames are printed following a header line which says:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Stack (most recent call last):
</pre></div>
</div>
<p>This mimics the <code class="docutils literal notranslate"><span class="pre">Traceback</span> <span class="pre">(most</span> <span class="pre">recent</span> <span class="pre">call</span> <span class="pre">last):</span></code> which is used when
displaying exception frames.</p>
<p>The third optional keyword argument is <em>stacklevel</em>, which defaults to <code class="docutils literal notranslate"><span class="pre">1</span></code>.
If greater than 1, the corresponding number of stack frames are skipped
when computing the line number and function name set in the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>
created for the logging event. This can be used in logging helpers so that
the function name, filename and line number recorded are not the information
for the helper function/method, but rather its caller. The name of this
parameter mirrors the equivalent one in the <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module.</p>
<p>The fourth keyword argument is <em>extra</em> which can be used to pass a
dictionary which is used to populate the __dict__ of the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>
created for the logging event with user-defined attributes. These custom
attributes can then be used as you like. For example, they could be
incorporated into logged messages. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">FORMAT</span> <span class="o">=</span> <span class="s1">&#39;</span><span class="si">%(asctime)s</span><span class="s1"> </span><span class="si">%(clientip)-15s</span><span class="s1"> </span><span class="si">%(user)-8s</span><span class="s1"> </span><span class="si">%(message)s</span><span class="s1">&#39;</span>
<span class="n">logging</span><span class="o">.</span><span class="n">basicConfig</span><span class="p">(</span><span class="nb">format</span><span class="o">=</span><span class="n">FORMAT</span><span class="p">)</span>
<span class="n">d</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;clientip&#39;</span><span class="p">:</span> <span class="s1">&#39;***********&#39;</span><span class="p">,</span> <span class="s1">&#39;user&#39;</span><span class="p">:</span> <span class="s1">&#39;fbloggs&#39;</span><span class="p">}</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;tcpserver&#39;</span><span class="p">)</span>
<span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s1">&#39;Protocol problem: </span><span class="si">%s</span><span class="s1">&#39;</span><span class="p">,</span> <span class="s1">&#39;connection reset&#39;</span><span class="p">,</span> <span class="n">extra</span><span class="o">=</span><span class="n">d</span><span class="p">)</span>
</pre></div>
</div>
<p>would print something like</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>2006-02-08 22:20:02,165 *********** fbloggs  Protocol problem: connection reset
</pre></div>
</div>
<p>The keys in the dictionary passed in <em>extra</em> should not clash with the keys used
by the logging system. (See the section on <a class="reference internal" href="#logrecord-attributes"><span class="std std-ref">LogRecord attributes</span></a> for more
information on which keys are used by the logging system.)</p>
<p>If you choose to use these attributes in logged messages, you need to exercise
some care. In the above example, for instance, the <a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> has been
set up with a format string which expects ‘clientip’ and ‘user’ in the attribute
dictionary of the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>. If these are missing, the message will
not be logged because a string formatting exception will occur. So in this case,
you always need to pass the <em>extra</em> dictionary with these keys.</p>
<p>While this might be annoying, this feature is intended for use in specialized
circumstances, such as multi-threaded servers where the same code executes in
many contexts, and interesting conditions which arise are dependent on this
context (such as remote client IP address and authenticated user name, in the
above example). In such circumstances, it is likely that specialized
<a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a>s would be used with particular <a class="reference internal" href="#logging.Handler" title="logging.Handler"><code class="xref py py-class docutils literal notranslate"><span class="pre">Handler</span></code></a>s.</p>
<p>If no handler is attached to this logger (or any of its ancestors,
taking into account the relevant <a class="reference internal" href="#logging.Logger.propagate" title="logging.Logger.propagate"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Logger.propagate</span></code></a> attributes),
the message will be sent to the handler set on <a class="reference internal" href="#logging.lastResort" title="logging.lastResort"><code class="xref py py-attr docutils literal notranslate"><span class="pre">lastResort</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>stack_info</em> parameter was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>exc_info</em> parameter can now accept exception instances.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>stacklevel</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.info">
<span class="sig-name descname"><span class="pre">info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.info" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code></a> on this logger. The arguments are
interpreted as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.warning">
<span class="sig-name descname"><span class="pre">warning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.warning" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.WARNING" title="logging.WARNING"><code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code></a> on this logger. The arguments are
interpreted as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>There is an obsolete method <code class="docutils literal notranslate"><span class="pre">warn</span></code> which is functionally
identical to <code class="docutils literal notranslate"><span class="pre">warning</span></code>. As <code class="docutils literal notranslate"><span class="pre">warn</span></code> is deprecated, please do not use
it - use <code class="docutils literal notranslate"><span class="pre">warning</span></code> instead.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.error">
<span class="sig-name descname"><span class="pre">error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.error" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code></a> on this logger. The arguments are
interpreted as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.critical">
<span class="sig-name descname"><span class="pre">critical</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.critical" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.CRITICAL" title="logging.CRITICAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CRITICAL</span></code></a> on this logger. The arguments are
interpreted as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.log">
<span class="sig-name descname"><span class="pre">log</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.log" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with integer level <em>level</em> on this logger. The other arguments are
interpreted as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.exception">
<span class="sig-name descname"><span class="pre">exception</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.exception" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code></a> on this logger. The arguments are
interpreted as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>. Exception info is added to the logging
message. This method should only be called from an exception handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.addFilter">
<span class="sig-name descname"><span class="pre">addFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filter</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.addFilter" title="Link to this definition">¶</a></dt>
<dd><p>Adds the specified filter <em>filter</em> to this logger.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.removeFilter">
<span class="sig-name descname"><span class="pre">removeFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filter</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.removeFilter" title="Link to this definition">¶</a></dt>
<dd><p>Removes the specified filter <em>filter</em> from this logger.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.filter">
<span class="sig-name descname"><span class="pre">filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.filter" title="Link to this definition">¶</a></dt>
<dd><p>Apply this logger’s filters to the record and return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the
record is to be processed. The filters are consulted in turn, until one of
them returns a false value. If none of them return a false value, the record
will be processed (passed to handlers). If one returns a false value, no
further processing of the record occurs.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.addHandler">
<span class="sig-name descname"><span class="pre">addHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hdlr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.addHandler" title="Link to this definition">¶</a></dt>
<dd><p>Adds the specified handler <em>hdlr</em> to this logger.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.removeHandler">
<span class="sig-name descname"><span class="pre">removeHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hdlr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.removeHandler" title="Link to this definition">¶</a></dt>
<dd><p>Removes the specified handler <em>hdlr</em> from this logger.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.findCaller">
<span class="sig-name descname"><span class="pre">findCaller</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stack_info</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stacklevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.findCaller" title="Link to this definition">¶</a></dt>
<dd><p>Finds the caller’s source filename and line number. Returns the filename, line
number, function name and stack information as a 4-element tuple. The stack
information is returned as <code class="docutils literal notranslate"><span class="pre">None</span></code> unless <em>stack_info</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<p>The <em>stacklevel</em> parameter is passed from code calling the <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>
and other APIs. If greater than 1, the excess is used to skip stack frames
before determining the values to be returned. This will generally be useful
when calling logging APIs from helper/wrapper code, so that the information
in the event log refers not to the helper/wrapper code, but to the code that
calls it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.handle">
<span class="sig-name descname"><span class="pre">handle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.handle" title="Link to this definition">¶</a></dt>
<dd><p>Handles a record by passing it to all handlers associated with this logger and
its ancestors (until a false value of <em>propagate</em> is found). This method is used
for unpickled records received from a socket, as well as those created locally.
Logger-level filtering is applied using <a class="reference internal" href="#logging.Logger.filter" title="logging.Logger.filter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filter()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.makeRecord">
<span class="sig-name descname"><span class="pre">makeRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fn</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc_info</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extra</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sinfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.makeRecord" title="Link to this definition">¶</a></dt>
<dd><p>This is a factory method which can be overridden in subclasses to create
specialized <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> instances.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Logger.hasHandlers">
<span class="sig-name descname"><span class="pre">hasHandlers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Logger.hasHandlers" title="Link to this definition">¶</a></dt>
<dd><p>Checks to see if this logger has any handlers configured. This is done by
looking for handlers in this logger and its parents in the logger hierarchy.
Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if a handler was found, else <code class="docutils literal notranslate"><span class="pre">False</span></code>. The method stops searching
up the hierarchy whenever a logger with the ‘propagate’ attribute set to
false is found - that will be the last logger which is checked for the
existence of handlers.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Loggers can now be pickled and unpickled.</p>
</div>
</dd></dl>

</section>
<section id="logging-levels">
<span id="levels"></span><h2>Logging Levels<a class="headerlink" href="#logging-levels" title="Link to this heading">¶</a></h2>
<p>The numeric values of logging levels are given in the following table. These are
primarily of interest if you want to define your own levels, and need them to
have specific values relative to the predefined levels. If you define a level
with the same numeric value, it overwrites the predefined value; the predefined
name is lost.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Level</p></th>
<th class="head"><p>Numeric value</p></th>
<th class="head"><p>What it means / When to use it</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="py data">
<dt class="sig sig-object py" id="logging.NOTSET">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">NOTSET</span></span><a class="headerlink" href="#logging.NOTSET" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>0</p></td>
<td><p>When set on a logger, indicates that
ancestor loggers are to be consulted
to determine the effective level.
If that still resolves to
<code class="xref py py-const docutils literal notranslate"><span class="pre">NOTSET</span></code>, then all events
are logged. When set on a handler,
all events are handled.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py data">
<dt class="sig sig-object py" id="logging.DEBUG">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">DEBUG</span></span><a class="headerlink" href="#logging.DEBUG" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>10</p></td>
<td><p>Detailed information, typically only
of interest to a developer trying to
diagnose a problem.</p></td>
</tr>
<tr class="row-even"><td><dl class="py data">
<dt class="sig sig-object py" id="logging.INFO">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">INFO</span></span><a class="headerlink" href="#logging.INFO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>20</p></td>
<td><p>Confirmation that things are working
as expected.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py data">
<dt class="sig sig-object py" id="logging.WARNING">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">WARNING</span></span><a class="headerlink" href="#logging.WARNING" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>30</p></td>
<td><p>An indication that something
unexpected happened, or that a
problem might occur in the near
future (e.g. ‘disk space low’). The
software is still working as
expected.</p></td>
</tr>
<tr class="row-even"><td><dl class="py data">
<dt class="sig sig-object py" id="logging.ERROR">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">ERROR</span></span><a class="headerlink" href="#logging.ERROR" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>40</p></td>
<td><p>Due to a more serious problem, the
software has not been able to
perform some function.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py data">
<dt class="sig sig-object py" id="logging.CRITICAL">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">CRITICAL</span></span><a class="headerlink" href="#logging.CRITICAL" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>50</p></td>
<td><p>A serious error, indicating that the
program itself may be unable to
continue running.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="handler-objects">
<span id="handler"></span><h2>Handler Objects<a class="headerlink" href="#handler-objects" title="Link to this heading">¶</a></h2>
<p>Handlers have the following attributes and methods. Note that <a class="reference internal" href="#logging.Handler" title="logging.Handler"><code class="xref py py-class docutils literal notranslate"><span class="pre">Handler</span></code></a>
is never instantiated directly; this class acts as a base for more useful
subclasses. However, the <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> method in subclasses needs to call
<a class="reference internal" href="#logging.Handler.__init__" title="logging.Handler.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Handler.__init__()</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.Handler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">Handler</span></span><a class="headerlink" href="#logging.Handler" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">NOTSET</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.__init__" title="Link to this definition">¶</a></dt>
<dd><p>Initializes the <a class="reference internal" href="#logging.Handler" title="logging.Handler"><code class="xref py py-class docutils literal notranslate"><span class="pre">Handler</span></code></a> instance by setting its level, setting the list
of filters to the empty list and creating a lock (using <a class="reference internal" href="#logging.Handler.createLock" title="logging.Handler.createLock"><code class="xref py py-meth docutils literal notranslate"><span class="pre">createLock()</span></code></a>) for
serializing access to an I/O mechanism.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.createLock">
<span class="sig-name descname"><span class="pre">createLock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.createLock" title="Link to this definition">¶</a></dt>
<dd><p>Initializes a thread lock which can be used to serialize access to underlying
I/O functionality which may not be threadsafe.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.acquire">
<span class="sig-name descname"><span class="pre">acquire</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.acquire" title="Link to this definition">¶</a></dt>
<dd><p>Acquires the thread lock created with <a class="reference internal" href="#logging.Handler.createLock" title="logging.Handler.createLock"><code class="xref py py-meth docutils literal notranslate"><span class="pre">createLock()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.release">
<span class="sig-name descname"><span class="pre">release</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.release" title="Link to this definition">¶</a></dt>
<dd><p>Releases the thread lock acquired with <a class="reference internal" href="#logging.Handler.acquire" title="logging.Handler.acquire"><code class="xref py py-meth docutils literal notranslate"><span class="pre">acquire()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.setLevel">
<span class="sig-name descname"><span class="pre">setLevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.setLevel" title="Link to this definition">¶</a></dt>
<dd><p>Sets the threshold for this handler to <em>level</em>. Logging messages which are
less severe than <em>level</em> will be ignored. When a handler is created, the
level is set to <a class="reference internal" href="#logging.NOTSET" title="logging.NOTSET"><code class="xref py py-const docutils literal notranslate"><span class="pre">NOTSET</span></code></a> (which causes all messages to be
processed).</p>
<p>See <a class="reference internal" href="#levels"><span class="std std-ref">Logging Levels</span></a> for a list of levels.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>level</em> parameter now accepts a string representation of the
level such as ‘INFO’ as an alternative to the integer constants
such as <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.setFormatter">
<span class="sig-name descname"><span class="pre">setFormatter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fmt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.setFormatter" title="Link to this definition">¶</a></dt>
<dd><p>Sets the <a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> for this handler to <em>fmt</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.addFilter">
<span class="sig-name descname"><span class="pre">addFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filter</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.addFilter" title="Link to this definition">¶</a></dt>
<dd><p>Adds the specified filter <em>filter</em> to this handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.removeFilter">
<span class="sig-name descname"><span class="pre">removeFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filter</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.removeFilter" title="Link to this definition">¶</a></dt>
<dd><p>Removes the specified filter <em>filter</em> from this handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.filter">
<span class="sig-name descname"><span class="pre">filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.filter" title="Link to this definition">¶</a></dt>
<dd><p>Apply this handler’s filters to the record and return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the
record is to be processed. The filters are consulted in turn, until one of
them returns a false value. If none of them return a false value, the record
will be emitted. If one returns a false value, the handler will not emit the
record.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.flush" title="Link to this definition">¶</a></dt>
<dd><p>Ensure all logging output has been flushed. This version does nothing and is
intended to be implemented by subclasses.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.close" title="Link to this definition">¶</a></dt>
<dd><p>Tidy up any resources used by the handler. This version does no output but
removes the handler from an internal list of handlers which is closed when
<a class="reference internal" href="#logging.shutdown" title="logging.shutdown"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutdown()</span></code></a> is called. Subclasses should ensure that this gets called
from overridden <a class="reference internal" href="#logging.Handler.close" title="logging.Handler.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.handle">
<span class="sig-name descname"><span class="pre">handle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.handle" title="Link to this definition">¶</a></dt>
<dd><p>Conditionally emits the specified logging record, depending on filters which may
have been added to the handler. Wraps the actual emission of the record with
acquisition/release of the I/O thread lock.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.handleError">
<span class="sig-name descname"><span class="pre">handleError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.handleError" title="Link to this definition">¶</a></dt>
<dd><p>This method should be called from handlers when an exception is encountered
during an <a class="reference internal" href="#logging.Handler.emit" title="logging.Handler.emit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code></a> call. If the module-level attribute
<a class="reference internal" href="#logging.raiseExceptions" title="logging.raiseExceptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">raiseExceptions</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, exceptions get silently ignored. This is
what is mostly wanted for a logging system - most users will not care about
errors in the logging system, they are more interested in application
errors. You could, however, replace this with a custom handler if you wish.
The specified record is the one which was being processed when the exception
occurred. (The default value of <a class="reference internal" href="#logging.raiseExceptions" title="logging.raiseExceptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">raiseExceptions</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, as that is
more useful during development).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.format" title="Link to this definition">¶</a></dt>
<dd><p>Do formatting for a record - if a formatter is set, use it. Otherwise, use the
default formatter for the module.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Handler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Handler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Do whatever it takes to actually log the specified logging record. This version
is intended to be implemented by subclasses and so raises a
<a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This method is called after a handler-level lock is acquired, which
is released after this method returns. When you override this method, note
that you should be careful when calling anything that invokes other parts of
the logging API which might do locking, because that might result in a
deadlock. Specifically:</p>
<ul class="simple">
<li><p>Logging configuration APIs acquire the module-level lock, and then
individual handler-level locks as those handlers are configured.</p></li>
<li><p>Many logging APIs lock the module-level lock. If such an API is called
from this method, it could cause a deadlock if a configuration call is
made on another thread, because that thread will try to acquire the
module-level lock <em>before</em> the handler-level lock, whereas this thread
tries to acquire the module-level lock <em>after</em> the handler-level lock
(because in this method, the handler-level lock has already been acquired).</p></li>
</ul>
</div>
</dd></dl>

</dd></dl>

<p>For a list of handlers included as standard, see <a class="reference internal" href="logging.handlers.html#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>.</p>
</section>
<section id="formatter-objects">
<span id="id1"></span><h2>Formatter Objects<a class="headerlink" href="#formatter-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="logging.Formatter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">Formatter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fmt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">datefmt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'%'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">validate</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">defaults</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Formatter" title="Link to this definition">¶</a></dt>
<dd><p>Responsible for converting a <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> to an output string
to be interpreted by a human or external system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fmt</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – A format string in the given <em>style</em> for
the logged output as a whole.
The possible mapping keys are drawn from the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> object’s
<a class="reference internal" href="#logrecord-attributes"><span class="std std-ref">LogRecord attributes</span></a>.
If not specified, <code class="docutils literal notranslate"><span class="pre">'%(message)s'</span></code> is used,
which is just the logged message.</p></li>
<li><p><strong>datefmt</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – A format string in the given <em>style</em> for
the date/time portion of the logged output.
If not specified, the default described in <a class="reference internal" href="#logging.Formatter.formatTime" title="logging.Formatter.formatTime"><code class="xref py py-meth docutils literal notranslate"><span class="pre">formatTime()</span></code></a> is used.</p></li>
<li><p><strong>style</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – Can be one of <code class="docutils literal notranslate"><span class="pre">'%'</span></code>, <code class="docutils literal notranslate"><span class="pre">'{'</span></code> or <code class="docutils literal notranslate"><span class="pre">'$'</span></code> and determines
how the format string will be merged with its data: using one of
<a class="reference internal" href="stdtypes.html#old-string-formatting"><span class="std std-ref">printf-style String Formatting</span></a> (<code class="docutils literal notranslate"><span class="pre">%</span></code>), <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a> (<code class="docutils literal notranslate"><span class="pre">{</span></code>)
or <a class="reference internal" href="string.html#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">string.Template</span></code></a> (<code class="docutils literal notranslate"><span class="pre">$</span></code>). This only applies to
<em>fmt</em> and <em>datefmt</em> (e.g. <code class="docutils literal notranslate"><span class="pre">'%(message)s'</span></code> versus <code class="docutils literal notranslate"><span class="pre">'{message}'</span></code>),
not to the actual log messages passed to the logging methods.
However, there are <a class="reference internal" href="../howto/logging-cookbook.html#formatting-styles"><span class="std std-ref">other ways</span></a>
to use <code class="docutils literal notranslate"><span class="pre">{</span></code>- and <code class="docutils literal notranslate"><span class="pre">$</span></code>-formatting for log messages.</p></li>
<li><p><strong>validate</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – If <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default), incorrect or mismatched
<em>fmt</em> and <em>style</em> will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>; for example,
<code class="docutils literal notranslate"><span class="pre">logging.Formatter('%(asctime)s</span> <span class="pre">-</span> <span class="pre">%(message)s',</span> <span class="pre">style='{')</span></code>.</p></li>
<li><p><strong>defaults</strong> (<a class="reference internal" href="stdtypes.html#dict" title="dict"><em>dict</em></a><em>[</em><a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em>, </em><em>Any</em><em>]</em>) – A dictionary with default values to use in custom fields.
For example,
<code class="docutils literal notranslate"><span class="pre">logging.Formatter('%(ip)s</span> <span class="pre">%(message)s',</span> <span class="pre">defaults={&quot;ip&quot;:</span> <span class="pre">None})</span></code></p></li>
</ul>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>style</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>validate</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <em>defaults</em> parameter.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.Formatter.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Formatter.format" title="Link to this definition">¶</a></dt>
<dd><p>The record’s attribute dictionary is used as the operand to a string
formatting operation. Returns the resulting string. Before formatting the
dictionary, a couple of preparatory steps are carried out. The <em>message</em>
attribute of the record is computed using <em>msg</em> % <em>args</em>. If the
formatting string contains <code class="docutils literal notranslate"><span class="pre">'(asctime)'</span></code>, <a class="reference internal" href="#logging.Formatter.formatTime" title="logging.Formatter.formatTime"><code class="xref py py-meth docutils literal notranslate"><span class="pre">formatTime()</span></code></a> is called
to format the event time. If there is exception information, it is
formatted using <a class="reference internal" href="#logging.Formatter.formatException" title="logging.Formatter.formatException"><code class="xref py py-meth docutils literal notranslate"><span class="pre">formatException()</span></code></a> and appended to the message. Note
that the formatted exception information is cached in attribute
<em>exc_text</em>. This is useful because the exception information can be
pickled and sent across the wire, but you should be careful if you have
more than one <a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> subclass which customizes the formatting
of exception information. In this case, you will have to clear the cached
value (by setting the <em>exc_text</em> attribute to <code class="docutils literal notranslate"><span class="pre">None</span></code>) after a formatter
has done its formatting, so that the next formatter to handle the event
doesn’t use the cached value, but recalculates it afresh.</p>
<p>If stack information is available, it’s appended after the exception
information, using <a class="reference internal" href="#logging.Formatter.formatStack" title="logging.Formatter.formatStack"><code class="xref py py-meth docutils literal notranslate"><span class="pre">formatStack()</span></code></a> to transform it if necessary.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Formatter.formatTime">
<span class="sig-name descname"><span class="pre">formatTime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">datefmt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Formatter.formatTime" title="Link to this definition">¶</a></dt>
<dd><p>This method should be called from <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> by a formatter which
wants to make use of a formatted time. This method can be overridden in
formatters to provide for any specific requirement, but the basic behavior
is as follows: if <em>datefmt</em> (a string) is specified, it is used with
<a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to format the creation time of the
record. Otherwise, the format ‘%Y-%m-%d %H:%M:%S,uuu’ is used, where the
uuu part is a millisecond value and the other letters are as per the
<a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> documentation.  An example time in this format is
<code class="docutils literal notranslate"><span class="pre">2003-01-23</span> <span class="pre">00:29:50,411</span></code>.  The resulting string is returned.</p>
<p>This function uses a user-configurable function to convert the creation
time to a tuple. By default, <a class="reference internal" href="time.html#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.localtime()</span></code></a> is used; to change
this for a particular formatter instance, set the <code class="docutils literal notranslate"><span class="pre">converter</span></code> attribute
to a function with the same signature as <a class="reference internal" href="time.html#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.localtime()</span></code></a> or
<a class="reference internal" href="time.html#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.gmtime()</span></code></a>. To change it for all formatters, for example if you
want all logging times to be shown in GMT, set the <code class="docutils literal notranslate"><span class="pre">converter</span></code>
attribute in the <code class="docutils literal notranslate"><span class="pre">Formatter</span></code> class.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Previously, the default format was hard-coded as in this example:
<code class="docutils literal notranslate"><span class="pre">2010-09-06</span> <span class="pre">22:38:15,292</span></code> where the part before the comma is
handled by a strptime format string (<code class="docutils literal notranslate"><span class="pre">'%Y-%m-%d</span> <span class="pre">%H:%M:%S'</span></code>), and the
part after the comma is a millisecond value. Because strptime does not
have a format placeholder for milliseconds, the millisecond value is
appended using another format string, <code class="docutils literal notranslate"><span class="pre">'%s,%03d'</span></code> — and both of these
format strings have been hardcoded into this method. With the change,
these strings are defined as class-level attributes which can be
overridden at the instance level when desired. The names of the
attributes are <code class="docutils literal notranslate"><span class="pre">default_time_format</span></code> (for the strptime format string)
and <code class="docutils literal notranslate"><span class="pre">default_msec_format</span></code> (for appending the millisecond value).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <code class="docutils literal notranslate"><span class="pre">default_msec_format</span></code> can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Formatter.formatException">
<span class="sig-name descname"><span class="pre">formatException</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc_info</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Formatter.formatException" title="Link to this definition">¶</a></dt>
<dd><p>Formats the specified exception information (a standard exception tuple as
returned by <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>) as a string. This default implementation
just uses <a class="reference internal" href="traceback.html#traceback.print_exception" title="traceback.print_exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">traceback.print_exception()</span></code></a>. The resulting string is
returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.Formatter.formatStack">
<span class="sig-name descname"><span class="pre">formatStack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stack_info</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Formatter.formatStack" title="Link to this definition">¶</a></dt>
<dd><p>Formats the specified stack information (a string as returned by
<a class="reference internal" href="traceback.html#traceback.print_stack" title="traceback.print_stack"><code class="xref py py-func docutils literal notranslate"><span class="pre">traceback.print_stack()</span></code></a>, but with the last newline removed) as a
string. This default implementation just returns the input value.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="logging.BufferingFormatter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">BufferingFormatter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">linefmt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.BufferingFormatter" title="Link to this definition">¶</a></dt>
<dd><p>A base formatter class suitable for subclassing when you want to format a
number of records. You can pass a <a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> instance which you want
to use to format each line (that corresponds to a single record). If not
specified, the default formatter (which just outputs the event message) is
used as the line formatter.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.BufferingFormatter.formatHeader">
<span class="sig-name descname"><span class="pre">formatHeader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">records</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.BufferingFormatter.formatHeader" title="Link to this definition">¶</a></dt>
<dd><p>Return a header for a list of <em>records</em>. The base implementation just
returns the empty string. You will need to override this method if you
want specific behaviour, e.g. to show the count of records, a title or a
separator line.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.BufferingFormatter.formatFooter">
<span class="sig-name descname"><span class="pre">formatFooter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">records</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.BufferingFormatter.formatFooter" title="Link to this definition">¶</a></dt>
<dd><p>Return a footer for a list of <em>records</em>. The base implementation just
returns the empty string. You will need to override this method if you
want specific behaviour, e.g. to show the count of records or a separator
line.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.BufferingFormatter.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">records</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.BufferingFormatter.format" title="Link to this definition">¶</a></dt>
<dd><p>Return formatted text for a list of <em>records</em>. The base implementation
just returns the empty string if there are no records; otherwise, it
returns the concatenation of the header, each record formatted with the
line formatter, and the footer.</p>
</dd></dl>

</dd></dl>

</section>
<section id="filter-objects">
<span id="filter"></span><h2>Filter Objects<a class="headerlink" href="#filter-objects" title="Link to this heading">¶</a></h2>
<p><code class="docutils literal notranslate"><span class="pre">Filters</span></code> can be used by <code class="docutils literal notranslate"><span class="pre">Handlers</span></code> and <code class="docutils literal notranslate"><span class="pre">Loggers</span></code> for more sophisticated
filtering than is provided by levels. The base filter class only allows events
which are below a certain point in the logger hierarchy. For example, a filter
initialized with ‘A.B’ will allow events logged by loggers ‘A.B’, ‘A.B.C’,
‘A.B.C.D’, ‘A.B.D’ etc. but not ‘A.BB’, ‘B.A.B’ etc. If initialized with the
empty string, all events are passed.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.Filter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">Filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Filter" title="Link to this definition">¶</a></dt>
<dd><p>Returns an instance of the <a class="reference internal" href="#logging.Filter" title="logging.Filter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Filter</span></code></a> class. If <em>name</em> is specified, it
names a logger which, together with its children, will have its events allowed
through the filter. If <em>name</em> is the empty string, allows every event.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.Filter.filter">
<span class="sig-name descname"><span class="pre">filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.Filter.filter" title="Link to this definition">¶</a></dt>
<dd><p>Is the specified record to be logged? Returns false for no, true for
yes. Filters can either modify log records in-place or return a completely
different record instance which will replace the original
log record in any future processing of the event.</p>
</dd></dl>

</dd></dl>

<p>Note that filters attached to handlers are consulted before an event is
emitted by the handler, whereas filters attached to loggers are consulted
whenever an event is logged (using <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>, <a class="reference internal" href="#logging.info" title="logging.info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">info()</span></code></a>,
etc.), before sending an event to handlers. This means that events which have
been generated by descendant loggers will not be filtered by a logger’s filter
setting, unless the filter has also been applied to those descendant loggers.</p>
<p>You don’t actually need to subclass <code class="docutils literal notranslate"><span class="pre">Filter</span></code>: you can pass any instance
which has a <code class="docutils literal notranslate"><span class="pre">filter</span></code> method with the same semantics.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>You don’t need to create specialized <code class="docutils literal notranslate"><span class="pre">Filter</span></code> classes, or use other
classes with a <code class="docutils literal notranslate"><span class="pre">filter</span></code> method: you can use a function (or other
callable) as a filter. The filtering logic will check to see if the filter
object has a <code class="docutils literal notranslate"><span class="pre">filter</span></code> attribute: if it does, it’s assumed to be a
<code class="docutils literal notranslate"><span class="pre">Filter</span></code> and its <a class="reference internal" href="#logging.Filter.filter" title="logging.Filter.filter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filter()</span></code></a> method is called. Otherwise, it’s
assumed to be a callable and called with the record as the single
parameter. The returned value should conform to that returned by
<a class="reference internal" href="#logging.Filter.filter" title="logging.Filter.filter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filter()</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>You can now return a <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> instance from filters to replace
the log record rather than modifying it in place. This allows filters attached to
a <a class="reference internal" href="#logging.Handler" title="logging.Handler"><code class="xref py py-class docutils literal notranslate"><span class="pre">Handler</span></code></a> to modify the log record before it is emitted, without
having side effects on other handlers.</p>
</div>
<p>Although filters are used primarily to filter records based on more
sophisticated criteria than levels, they get to see every record which is
processed by the handler or logger they’re attached to: this can be useful if
you want to do things like counting how many records were processed by a
particular logger or handler, or adding, changing or removing attributes in
the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> being processed. Obviously changing the LogRecord needs
to be done with some care, but it does allow the injection of contextual
information into logs (see <a class="reference internal" href="../howto/logging-cookbook.html#filters-contextual"><span class="std std-ref">Using Filters to impart contextual information</span></a>).</p>
</section>
<section id="logrecord-objects">
<span id="log-record"></span><h2>LogRecord Objects<a class="headerlink" href="#logrecord-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> instances are created automatically by the <a class="reference internal" href="#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">Logger</span></code></a>
every time something is logged, and can be created manually via
<a class="reference internal" href="#logging.makeLogRecord" title="logging.makeLogRecord"><code class="xref py py-func docutils literal notranslate"><span class="pre">makeLogRecord()</span></code></a> (for example, from a pickled event received over the
wire).</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.LogRecord">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">LogRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pathname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc_info</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sinfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.LogRecord" title="Link to this definition">¶</a></dt>
<dd><p>Contains all the information pertinent to the event being logged.</p>
<p>The primary information is passed in <em>msg</em> and <em>args</em>,
which are combined using <code class="docutils literal notranslate"><span class="pre">msg</span> <span class="pre">%</span> <span class="pre">args</span></code> to create
the <code class="xref py py-attr docutils literal notranslate"><span class="pre">message</span></code> attribute of the record.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the logger used to log the event
represented by this <code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code>.
Note that the logger name in the <code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code>
will always have this value,
even though it may be emitted by a handler
attached to a different (ancestor) logger.</p></li>
<li><p><strong>level</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The <a class="reference internal" href="#levels"><span class="std std-ref">numeric level</span></a> of the logging event
(such as <code class="docutils literal notranslate"><span class="pre">10</span></code> for <code class="docutils literal notranslate"><span class="pre">DEBUG</span></code>, <code class="docutils literal notranslate"><span class="pre">20</span></code> for <code class="docutils literal notranslate"><span class="pre">INFO</span></code>, etc).
Note that this is converted to <em>two</em> attributes of the LogRecord:
<code class="xref py py-attr docutils literal notranslate"><span class="pre">levelno</span></code> for the numeric value
and <code class="xref py py-attr docutils literal notranslate"><span class="pre">levelname</span></code> for the corresponding level name.</p></li>
<li><p><strong>pathname</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The full string path of the source file
where the logging call was made.</p></li>
<li><p><strong>lineno</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The line number in the source file
where the logging call was made.</p></li>
<li><p><strong>msg</strong> (<a class="reference internal" href="typing.html#typing.Any" title="typing.Any"><em>Any</em></a>) – The event description message,
which can be a %-format string with placeholders for variable data,
or an arbitrary object (see <a class="reference internal" href="../howto/logging.html#arbitrary-object-messages"><span class="std std-ref">Using arbitrary objects as messages</span></a>).</p></li>
<li><p><strong>args</strong> (<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><em>tuple</em></a><em> | </em><a class="reference internal" href="stdtypes.html#dict" title="dict"><em>dict</em></a><em>[</em><a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em>, </em><a class="reference internal" href="typing.html#typing.Any" title="typing.Any"><em>Any</em></a><em>]</em>) – Variable data to merge into the <em>msg</em> argument
to obtain the event description.</p></li>
<li><p><strong>exc_info</strong> (<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><em>tuple</em></a><em>[</em><a class="reference internal" href="functions.html#type" title="type"><em>type</em></a><em>[</em><a class="reference internal" href="exceptions.html#BaseException" title="BaseException"><em>BaseException</em></a><em>]</em><em>, </em><a class="reference internal" href="exceptions.html#BaseException" title="BaseException"><em>BaseException</em></a><em>, </em><a class="reference internal" href="types.html#types.TracebackType" title="types.TracebackType"><em>types.TracebackType</em></a><em>] </em><em>| </em><em>None</em>) – An exception tuple with the current exception information,
as returned by <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>,
or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no exception information is available.</p></li>
<li><p><strong>func</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em> | </em><em>None</em>) – The name of the function or method
from which the logging call was invoked.</p></li>
<li><p><strong>sinfo</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em> | </em><em>None</em>) – A text string representing stack information
from the base of the stack in the current thread,
up to the logging call.</p></li>
</ul>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="logging.LogRecord.getMessage">
<span class="sig-name descname"><span class="pre">getMessage</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.LogRecord.getMessage" title="Link to this definition">¶</a></dt>
<dd><p>Returns the message for this <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> instance after merging any
user-supplied arguments with the message. If the user-supplied message
argument to the logging call is not a string, <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> is called on it to
convert it to a string. This allows use of user-defined classes as
messages, whose <code class="docutils literal notranslate"><span class="pre">__str__</span></code> method can return the actual format string to
be used.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The creation of a <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> has been made more configurable by
providing a factory which is used to create the record. The factory can be
set using <a class="reference internal" href="#logging.getLogRecordFactory" title="logging.getLogRecordFactory"><code class="xref py py-func docutils literal notranslate"><span class="pre">getLogRecordFactory()</span></code></a> and <a class="reference internal" href="#logging.setLogRecordFactory" title="logging.setLogRecordFactory"><code class="xref py py-func docutils literal notranslate"><span class="pre">setLogRecordFactory()</span></code></a>
(see this for the factory’s signature).</p>
</div>
<p>This functionality can be used to inject your own values into a
<a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> at creation time. You can use the following pattern:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">old_factory</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogRecordFactory</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">record_factory</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="n">record</span> <span class="o">=</span> <span class="n">old_factory</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
    <span class="n">record</span><span class="o">.</span><span class="n">custom_attribute</span> <span class="o">=</span> <span class="mh">0xdecafbad</span>
    <span class="k">return</span> <span class="n">record</span>

<span class="n">logging</span><span class="o">.</span><span class="n">setLogRecordFactory</span><span class="p">(</span><span class="n">record_factory</span><span class="p">)</span>
</pre></div>
</div>
<p>With this pattern, multiple factories could be chained, and as long
as they don’t overwrite each other’s attributes or unintentionally
overwrite the standard attributes listed above, there should be no
surprises.</p>
</dd></dl>

</section>
<section id="logrecord-attributes">
<span id="id2"></span><h2>LogRecord attributes<a class="headerlink" href="#logrecord-attributes" title="Link to this heading">¶</a></h2>
<p>The LogRecord has a number of attributes, most of which are derived from the
parameters to the constructor. (Note that the names do not always correspond
exactly between the LogRecord constructor parameters and the LogRecord
attributes.) These attributes can be used to merge data from the record into
the format string. The following table lists (in alphabetical order) the
attribute names, their meanings and the corresponding placeholder in a %-style
format string.</p>
<p>If you are using {}-formatting (<a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-func docutils literal notranslate"><span class="pre">str.format()</span></code></a>), you can use
<code class="docutils literal notranslate"><span class="pre">{attrname}</span></code> as the placeholder in the format string. If you are using
$-formatting (<a class="reference internal" href="string.html#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">string.Template</span></code></a>), use the form <code class="docutils literal notranslate"><span class="pre">${attrname}</span></code>. In
both cases, of course, replace <code class="docutils literal notranslate"><span class="pre">attrname</span></code> with the actual attribute name
you want to use.</p>
<p>In the case of {}-formatting, you can specify formatting flags by placing them
after the attribute name, separated from it with a colon. For example: a
placeholder of <code class="docutils literal notranslate"><span class="pre">{msecs:03.0f}</span></code> would format a millisecond value of <code class="docutils literal notranslate"><span class="pre">4</span></code> as
<code class="docutils literal notranslate"><span class="pre">004</span></code>. Refer to the <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a> documentation for full details on
the options available to you.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Attribute name</p></th>
<th class="head"><p>Format</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>args</p></td>
<td><p>You shouldn’t need to
format this yourself.</p></td>
<td><p>The tuple of arguments merged into <code class="docutils literal notranslate"><span class="pre">msg</span></code> to
produce <code class="docutils literal notranslate"><span class="pre">message</span></code>, or a dict whose values
are used for the merge (when there is only one
argument, and it is a dictionary).</p></td>
</tr>
<tr class="row-odd"><td><p>asctime</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(asctime)s</span></code></p></td>
<td><p>Human-readable time when the
<a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> was created.  By default
this is of the form ‘2003-07-08 16:49:45,896’
(the numbers after the comma are millisecond
portion of the time).</p></td>
</tr>
<tr class="row-even"><td><p>created</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(created)f</span></code></p></td>
<td><p>Time when the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> was created
(as returned by <a class="reference internal" href="time.html#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.time()</span></code></a>).</p></td>
</tr>
<tr class="row-odd"><td><p>exc_info</p></td>
<td><p>You shouldn’t need to
format this yourself.</p></td>
<td><p>Exception tuple (à la <code class="docutils literal notranslate"><span class="pre">sys.exc_info</span></code>) or,
if no exception has occurred, <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p>filename</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(filename)s</span></code></p></td>
<td><p>Filename portion of <code class="docutils literal notranslate"><span class="pre">pathname</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p>funcName</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(funcName)s</span></code></p></td>
<td><p>Name of function containing the logging call.</p></td>
</tr>
<tr class="row-even"><td><p>levelname</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(levelname)s</span></code></p></td>
<td><p>Text logging level for the message
(<code class="docutils literal notranslate"><span class="pre">'DEBUG'</span></code>, <code class="docutils literal notranslate"><span class="pre">'INFO'</span></code>, <code class="docutils literal notranslate"><span class="pre">'WARNING'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'ERROR'</span></code>, <code class="docutils literal notranslate"><span class="pre">'CRITICAL'</span></code>).</p></td>
</tr>
<tr class="row-odd"><td><p>levelno</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(levelno)s</span></code></p></td>
<td><p>Numeric logging level for the message
(<a class="reference internal" href="#logging.DEBUG" title="logging.DEBUG"><code class="xref py py-const docutils literal notranslate"><span class="pre">DEBUG</span></code></a>, <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code></a>,
<a class="reference internal" href="#logging.WARNING" title="logging.WARNING"><code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code></a>, <a class="reference internal" href="#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code></a>,
<a class="reference internal" href="#logging.CRITICAL" title="logging.CRITICAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CRITICAL</span></code></a>).</p></td>
</tr>
<tr class="row-even"><td><p>lineno</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(lineno)d</span></code></p></td>
<td><p>Source line number where the logging call was
issued (if available).</p></td>
</tr>
<tr class="row-odd"><td><p>message</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(message)s</span></code></p></td>
<td><p>The logged message, computed as <code class="docutils literal notranslate"><span class="pre">msg</span> <span class="pre">%</span>
<span class="pre">args</span></code>. This is set when
<a class="reference internal" href="#logging.Formatter.format" title="logging.Formatter.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Formatter.format()</span></code></a> is invoked.</p></td>
</tr>
<tr class="row-even"><td><p>module</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(module)s</span></code></p></td>
<td><p>Module (name portion of <code class="docutils literal notranslate"><span class="pre">filename</span></code>).</p></td>
</tr>
<tr class="row-odd"><td><p>msecs</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(msecs)d</span></code></p></td>
<td><p>Millisecond portion of the time when the
<a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> was created.</p></td>
</tr>
<tr class="row-even"><td><p>msg</p></td>
<td><p>You shouldn’t need to
format this yourself.</p></td>
<td><p>The format string passed in the original
logging call. Merged with <code class="docutils literal notranslate"><span class="pre">args</span></code> to
produce <code class="docutils literal notranslate"><span class="pre">message</span></code>, or an arbitrary object
(see <a class="reference internal" href="../howto/logging.html#arbitrary-object-messages"><span class="std std-ref">Using arbitrary objects as messages</span></a>).</p></td>
</tr>
<tr class="row-odd"><td><p>name</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(name)s</span></code></p></td>
<td><p>Name of the logger used to log the call.</p></td>
</tr>
<tr class="row-even"><td><p>pathname</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(pathname)s</span></code></p></td>
<td><p>Full pathname of the source file where the
logging call was issued (if available).</p></td>
</tr>
<tr class="row-odd"><td><p>process</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(process)d</span></code></p></td>
<td><p>Process ID (if available).</p></td>
</tr>
<tr class="row-even"><td><p>processName</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(processName)s</span></code></p></td>
<td><p>Process name (if available).</p></td>
</tr>
<tr class="row-odd"><td><p>relativeCreated</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(relativeCreated)d</span></code></p></td>
<td><p>Time in milliseconds when the LogRecord was
created, relative to the time the logging
module was loaded.</p></td>
</tr>
<tr class="row-even"><td><p>stack_info</p></td>
<td><p>You shouldn’t need to
format this yourself.</p></td>
<td><p>Stack frame information (where available)
from the bottom of the stack in the current
thread, up to and including the stack frame
of the logging call which resulted in the
creation of this record.</p></td>
</tr>
<tr class="row-odd"><td><p>thread</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(thread)d</span></code></p></td>
<td><p>Thread ID (if available).</p></td>
</tr>
<tr class="row-even"><td><p>threadName</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(threadName)s</span></code></p></td>
<td><p>Thread name (if available).</p></td>
</tr>
<tr class="row-odd"><td><p>taskName</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">%(taskName)s</span></code></p></td>
<td><p><a class="reference internal" href="asyncio-task.html#asyncio.Task" title="asyncio.Task"><code class="xref py py-class docutils literal notranslate"><span class="pre">asyncio.Task</span></code></a> name (if available).</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span><em>processName</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><em>taskName</em> was added.</p>
</div>
</section>
<section id="loggeradapter-objects">
<span id="logger-adapter"></span><h2>LoggerAdapter Objects<a class="headerlink" href="#loggeradapter-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#logging.LoggerAdapter" title="logging.LoggerAdapter"><code class="xref py py-class docutils literal notranslate"><span class="pre">LoggerAdapter</span></code></a> instances are used to conveniently pass contextual
information into logging calls. For a usage example, see the section on
<a class="reference internal" href="../howto/logging-cookbook.html#context-info"><span class="std std-ref">adding contextual information to your logging output</span></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.LoggerAdapter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">LoggerAdapter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">logger</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extra</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.LoggerAdapter" title="Link to this definition">¶</a></dt>
<dd><p>Returns an instance of <a class="reference internal" href="#logging.LoggerAdapter" title="logging.LoggerAdapter"><code class="xref py py-class docutils literal notranslate"><span class="pre">LoggerAdapter</span></code></a> initialized with an
underlying <a class="reference internal" href="#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">Logger</span></code></a> instance and a dict-like object.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.LoggerAdapter.process">
<span class="sig-name descname"><span class="pre">process</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.LoggerAdapter.process" title="Link to this definition">¶</a></dt>
<dd><p>Modifies the message and/or keyword arguments passed to a logging call in
order to insert contextual information. This implementation takes the object
passed as <em>extra</em> to the constructor and adds it to <em>kwargs</em> using key
‘extra’. The return value is a (<em>msg</em>, <em>kwargs</em>) tuple which has the
(possibly modified) versions of the arguments passed in.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.LoggerAdapter.manager">
<span class="sig-name descname"><span class="pre">manager</span></span><a class="headerlink" href="#logging.LoggerAdapter.manager" title="Link to this definition">¶</a></dt>
<dd><p>Delegates to the underlying <code class="xref py py-attr docutils literal notranslate"><span class="pre">manager`</span></code> on <em>logger</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.LoggerAdapter._log">
<span class="sig-name descname"><span class="pre">_log</span></span><a class="headerlink" href="#logging.LoggerAdapter._log" title="Link to this definition">¶</a></dt>
<dd><p>Delegates to the underlying <code class="xref py py-meth docutils literal notranslate"><span class="pre">_log`()</span></code> method on <em>logger</em>.</p>
</dd></dl>

<p>In addition to the above, <a class="reference internal" href="#logging.LoggerAdapter" title="logging.LoggerAdapter"><code class="xref py py-class docutils literal notranslate"><span class="pre">LoggerAdapter</span></code></a> supports the following
methods of <a class="reference internal" href="#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">Logger</span></code></a>: <a class="reference internal" href="#logging.Logger.debug" title="logging.Logger.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">debug()</span></code></a>, <a class="reference internal" href="#logging.Logger.info" title="logging.Logger.info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">info()</span></code></a>,
<a class="reference internal" href="#logging.Logger.warning" title="logging.Logger.warning"><code class="xref py py-meth docutils literal notranslate"><span class="pre">warning()</span></code></a>, <a class="reference internal" href="#logging.Logger.error" title="logging.Logger.error"><code class="xref py py-meth docutils literal notranslate"><span class="pre">error()</span></code></a>, <a class="reference internal" href="#logging.Logger.exception" title="logging.Logger.exception"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exception()</span></code></a>,
<a class="reference internal" href="#logging.Logger.critical" title="logging.Logger.critical"><code class="xref py py-meth docutils literal notranslate"><span class="pre">critical()</span></code></a>, <a class="reference internal" href="#logging.Logger.log" title="logging.Logger.log"><code class="xref py py-meth docutils literal notranslate"><span class="pre">log()</span></code></a>, <a class="reference internal" href="#logging.Logger.isEnabledFor" title="logging.Logger.isEnabledFor"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isEnabledFor()</span></code></a>,
<a class="reference internal" href="#logging.Logger.getEffectiveLevel" title="logging.Logger.getEffectiveLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getEffectiveLevel()</span></code></a>, <a class="reference internal" href="#logging.Logger.setLevel" title="logging.Logger.setLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setLevel()</span></code></a> and
<a class="reference internal" href="#logging.Logger.hasHandlers" title="logging.Logger.hasHandlers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hasHandlers()</span></code></a>. These methods have the same signatures as their
counterparts in <a class="reference internal" href="#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">Logger</span></code></a>, so you can use the two types of instances
interchangeably.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <a class="reference internal" href="#logging.Logger.isEnabledFor" title="logging.Logger.isEnabledFor"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isEnabledFor()</span></code></a>, <a class="reference internal" href="#logging.Logger.getEffectiveLevel" title="logging.Logger.getEffectiveLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getEffectiveLevel()</span></code></a>,
<a class="reference internal" href="#logging.Logger.setLevel" title="logging.Logger.setLevel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setLevel()</span></code></a> and <a class="reference internal" href="#logging.Logger.hasHandlers" title="logging.Logger.hasHandlers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hasHandlers()</span></code></a> methods were added
to <a class="reference internal" href="#logging.LoggerAdapter" title="logging.LoggerAdapter"><code class="xref py py-class docutils literal notranslate"><span class="pre">LoggerAdapter</span></code></a>.  These methods delegate to the underlying logger.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Attribute <code class="xref py py-attr docutils literal notranslate"><span class="pre">manager</span></code> and method <code class="xref py py-meth docutils literal notranslate"><span class="pre">_log()</span></code> were added, which
delegate to the underlying logger and allow adapters to be nested.</p>
</div>
</dd></dl>

</section>
<section id="thread-safety">
<h2>Thread Safety<a class="headerlink" href="#thread-safety" title="Link to this heading">¶</a></h2>
<p>The logging module is intended to be thread-safe without any special work
needing to be done by its clients. It achieves this though using threading
locks; there is one lock to serialize access to the module’s shared data, and
each handler also creates a lock to serialize access to its underlying I/O.</p>
<p>If you are implementing asynchronous signal handlers using the <a class="reference internal" href="signal.html#module-signal" title="signal: Set handlers for asynchronous events."><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code></a>
module, you may not be able to use logging from within such handlers. This is
because lock implementations in the <a class="reference internal" href="threading.html#module-threading" title="threading: Thread-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">threading</span></code></a> module are not always
re-entrant, and so cannot be invoked from such signal handlers.</p>
</section>
<section id="module-level-functions">
<h2>Module-Level Functions<a class="headerlink" href="#module-level-functions" title="Link to this heading">¶</a></h2>
<p>In addition to the classes described above, there are a number of module-level
functions.</p>
<dl class="py function">
<dt class="sig sig-object py" id="logging.getLogger">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getLogger</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.getLogger" title="Link to this definition">¶</a></dt>
<dd><p>Return a logger with the specified name or, if name is <code class="docutils literal notranslate"><span class="pre">None</span></code>, return a
logger which is the root logger of the hierarchy. If specified, the name is
typically a dot-separated hierarchical name like <em>‘a’</em>, <em>‘a.b’</em> or <em>‘a.b.c.d’</em>.
Choice of these names is entirely up to the developer who is using logging.</p>
<p>All calls to this function with a given name return the same logger instance.
This means that logger instances never need to be passed between different parts
of an application.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.getLoggerClass">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getLoggerClass</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.getLoggerClass" title="Link to this definition">¶</a></dt>
<dd><p>Return either the standard <a class="reference internal" href="#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">Logger</span></code></a> class, or the last class passed to
<a class="reference internal" href="#logging.setLoggerClass" title="logging.setLoggerClass"><code class="xref py py-func docutils literal notranslate"><span class="pre">setLoggerClass()</span></code></a>. This function may be called from within a new class
definition, to ensure that installing a customized <a class="reference internal" href="#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">Logger</span></code></a> class will
not undo customizations already applied by other code. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyLogger</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">getLoggerClass</span><span class="p">()):</span>
    <span class="c1"># ... override behaviour here</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.getLogRecordFactory">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getLogRecordFactory</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.getLogRecordFactory" title="Link to this definition">¶</a></dt>
<dd><p>Return a callable which is used to create a <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span>This function has been provided, along with <a class="reference internal" href="#logging.setLogRecordFactory" title="logging.setLogRecordFactory"><code class="xref py py-func docutils literal notranslate"><span class="pre">setLogRecordFactory()</span></code></a>,
to allow developers more control over how the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>
representing a logging event is constructed.</p>
</div>
<p>See <a class="reference internal" href="#logging.setLogRecordFactory" title="logging.setLogRecordFactory"><code class="xref py py-func docutils literal notranslate"><span class="pre">setLogRecordFactory()</span></code></a> for more information about the how the
factory is called.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.debug">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">debug</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.debug" title="Link to this definition">¶</a></dt>
<dd><p>This is a convenience function that calls <a class="reference internal" href="#logging.Logger.debug" title="logging.Logger.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Logger.debug()</span></code></a>, on the root
logger. The handling of the arguments is in every way identical
to what is described in that method.</p>
<p>The only difference is that if the root logger has no handlers, then
<a class="reference internal" href="#logging.basicConfig" title="logging.basicConfig"><code class="xref py py-func docutils literal notranslate"><span class="pre">basicConfig()</span></code></a> is called, prior to calling <code class="docutils literal notranslate"><span class="pre">debug</span></code> on the root logger.</p>
<p>For very short scripts or quick demonstrations of <code class="docutils literal notranslate"><span class="pre">logging</span></code> facilities,
<code class="docutils literal notranslate"><span class="pre">debug</span></code> and the other module-level functions may be convenient. However,
most programs will want to carefully and explicitly control the logging
configuration, and should therefore prefer creating a module-level logger and
calling <a class="reference internal" href="#logging.Logger.debug" title="logging.Logger.debug"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Logger.debug()</span></code></a> (or other level-specific methods) on it, as
described at the beginnning of this documentation.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.info">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.info" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code></a> on the root logger. The arguments and behavior
are otherwise the same as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.warning">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">warning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.warning" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.WARNING" title="logging.WARNING"><code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code></a> on the root logger. The arguments and behavior
are otherwise the same as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>There is an obsolete function <code class="docutils literal notranslate"><span class="pre">warn</span></code> which is functionally
identical to <code class="docutils literal notranslate"><span class="pre">warning</span></code>. As <code class="docutils literal notranslate"><span class="pre">warn</span></code> is deprecated, please do not use
it - use <code class="docutils literal notranslate"><span class="pre">warning</span></code> instead.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.error">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.error" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code></a> on the root logger. The arguments and behavior
are otherwise the same as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.critical">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">critical</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.critical" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.CRITICAL" title="logging.CRITICAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CRITICAL</span></code></a> on the root logger. The arguments and behavior
are otherwise the same as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.exception">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">exception</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.exception" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <a class="reference internal" href="#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code></a> on the root logger. The arguments and behavior
are otherwise the same as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>. Exception info is added to the logging
message. This function should only be called from an exception handler.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.log">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">log</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.log" title="Link to this definition">¶</a></dt>
<dd><p>Logs a message with level <em>level</em> on the root logger. The arguments and behavior
are otherwise the same as for <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.disable">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">CRITICAL</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.disable" title="Link to this definition">¶</a></dt>
<dd><p>Provides an overriding level <em>level</em> for all loggers which takes precedence over
the logger’s own level. When the need arises to temporarily throttle logging
output down across the whole application, this function can be useful. Its
effect is to disable all logging calls of severity <em>level</em> and below, so that
if you call it with a value of INFO, then all INFO and DEBUG events would be
discarded, whereas those of severity WARNING and above would be processed
according to the logger’s effective level. If
<code class="docutils literal notranslate"><span class="pre">logging.disable(logging.NOTSET)</span></code> is called, it effectively removes this
overriding level, so that logging output again depends on the effective
levels of individual loggers.</p>
<p>Note that if you have defined any custom logging level higher than
<code class="docutils literal notranslate"><span class="pre">CRITICAL</span></code> (this is not recommended), you won’t be able to rely on the
default value for the <em>level</em> parameter, but will have to explicitly supply a
suitable value.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The <em>level</em> parameter was defaulted to level <code class="docutils literal notranslate"><span class="pre">CRITICAL</span></code>. See
<a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=28524">bpo-28524</a> for more information about this change.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.addLevelName">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">addLevelName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">levelName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.addLevelName" title="Link to this definition">¶</a></dt>
<dd><p>Associates level <em>level</em> with text <em>levelName</em> in an internal dictionary, which is
used to map numeric levels to a textual representation, for example when a
<a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> formats a message. This function can also be used to define
your own levels. The only constraints are that all levels used must be
registered using this function, levels should be positive integers and they
should increase in increasing order of severity.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you are thinking of defining your own levels, please see the
section on <a class="reference internal" href="../howto/logging.html#custom-levels"><span class="std std-ref">Custom Levels</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.getLevelNamesMapping">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getLevelNamesMapping</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.getLevelNamesMapping" title="Link to this definition">¶</a></dt>
<dd><p>Returns a mapping from level names to their corresponding logging levels. For example, the
string “CRITICAL” maps to <a class="reference internal" href="#logging.CRITICAL" title="logging.CRITICAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CRITICAL</span></code></a>. The returned mapping is copied from an internal
mapping on each call to this function.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.getLevelName">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getLevelName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.getLevelName" title="Link to this definition">¶</a></dt>
<dd><p>Returns the textual or numeric representation of logging level <em>level</em>.</p>
<p>If <em>level</em> is one of the predefined levels <a class="reference internal" href="#logging.CRITICAL" title="logging.CRITICAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CRITICAL</span></code></a>, <a class="reference internal" href="#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code></a>,
<a class="reference internal" href="#logging.WARNING" title="logging.WARNING"><code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code></a>, <a class="reference internal" href="#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code></a> or <a class="reference internal" href="#logging.DEBUG" title="logging.DEBUG"><code class="xref py py-const docutils literal notranslate"><span class="pre">DEBUG</span></code></a> then you get the
corresponding string. If you have associated levels with names using
<a class="reference internal" href="#logging.addLevelName" title="logging.addLevelName"><code class="xref py py-func docutils literal notranslate"><span class="pre">addLevelName()</span></code></a> then the name you have associated with <em>level</em> is
returned. If a numeric value corresponding to one of the defined levels is
passed in, the corresponding string representation is returned.</p>
<p>The <em>level</em> parameter also accepts a string representation of the level such
as ‘INFO’. In such cases, this functions returns the corresponding numeric
value of the level.</p>
<p>If no matching numeric or string value is passed in, the string
‘Level %s’ % level is returned.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Levels are internally integers (as they need to be compared in the
logging logic). This function is used to convert between an integer level
and the level name displayed in the formatted log output by means of the
<code class="docutils literal notranslate"><span class="pre">%(levelname)s</span></code> format specifier (see <a class="reference internal" href="#logrecord-attributes"><span class="std std-ref">LogRecord attributes</span></a>), and
vice versa.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>In Python versions earlier than 3.4, this function could also be passed a
text level, and would return the corresponding numeric value of the level.
This undocumented behaviour was considered a mistake, and was removed in
Python 3.4, but reinstated in 3.4.2 due to retain backward compatibility.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.getHandlerByName">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getHandlerByName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.getHandlerByName" title="Link to this definition">¶</a></dt>
<dd><p>Returns a handler with the specified <em>name</em>, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if there is no handler
with that name.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.getHandlerNames">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">getHandlerNames</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.getHandlerNames" title="Link to this definition">¶</a></dt>
<dd><p>Returns an immutable set of all known handler names.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.makeLogRecord">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">makeLogRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrdict</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.makeLogRecord" title="Link to this definition">¶</a></dt>
<dd><p>Creates and returns a new <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> instance whose attributes are
defined by <em>attrdict</em>. This function is useful for taking a pickled
<a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> attribute dictionary, sent over a socket, and reconstituting
it as a <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> instance at the receiving end.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.basicConfig">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">basicConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.basicConfig" title="Link to this definition">¶</a></dt>
<dd><p>Does basic configuration for the logging system by creating a
<a class="reference internal" href="logging.handlers.html#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a> with a default <a class="reference internal" href="#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> and adding it to the
root logger. The functions <a class="reference internal" href="#logging.debug" title="logging.debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">debug()</span></code></a>, <a class="reference internal" href="#logging.info" title="logging.info"><code class="xref py py-func docutils literal notranslate"><span class="pre">info()</span></code></a>, <a class="reference internal" href="#logging.warning" title="logging.warning"><code class="xref py py-func docutils literal notranslate"><span class="pre">warning()</span></code></a>,
<a class="reference internal" href="#logging.error" title="logging.error"><code class="xref py py-func docutils literal notranslate"><span class="pre">error()</span></code></a> and <a class="reference internal" href="#logging.critical" title="logging.critical"><code class="xref py py-func docutils literal notranslate"><span class="pre">critical()</span></code></a> will call <a class="reference internal" href="#logging.basicConfig" title="logging.basicConfig"><code class="xref py py-func docutils literal notranslate"><span class="pre">basicConfig()</span></code></a> automatically
if no handlers are defined for the root logger.</p>
<p>This function does nothing if the root logger already has handlers
configured, unless the keyword argument <em>force</em> is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function should be called from the main thread
before other threads are started. In versions of Python prior to
2.7.1 and 3.2, if this function is called from multiple threads,
it is possible (in rare circumstances) that a handler will be added
to the root logger more than once, leading to unexpected results
such as messages being duplicated in the log.</p>
</div>
<p>The following keyword arguments are supported.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Format</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>filename</em></p></td>
<td><p>Specifies that a <a class="reference internal" href="logging.handlers.html#logging.FileHandler" title="logging.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a> be
created, using the specified filename,
rather than a <a class="reference internal" href="logging.handlers.html#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><em>filemode</em></p></td>
<td><p>If <em>filename</em> is specified, open the file
in this <a class="reference internal" href="functions.html#filemodes"><span class="std std-ref">mode</span></a>. Defaults
to <code class="docutils literal notranslate"><span class="pre">'a'</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><em>format</em></p></td>
<td><p>Use the specified format string for the
handler. Defaults to attributes
<code class="docutils literal notranslate"><span class="pre">levelname</span></code>, <code class="docutils literal notranslate"><span class="pre">name</span></code> and <code class="docutils literal notranslate"><span class="pre">message</span></code>
separated by colons.</p></td>
</tr>
<tr class="row-odd"><td><p><em>datefmt</em></p></td>
<td><p>Use the specified date/time format, as
accepted by <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a>.</p></td>
</tr>
<tr class="row-even"><td><p><em>style</em></p></td>
<td><p>If <em>format</em> is specified, use this style
for the format string. One of <code class="docutils literal notranslate"><span class="pre">'%'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'{'</span></code> or <code class="docutils literal notranslate"><span class="pre">'$'</span></code> for <a class="reference internal" href="stdtypes.html#old-string-formatting"><span class="std std-ref">printf-style</span></a>,
<a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a> or
<a class="reference internal" href="string.html#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">string.Template</span></code></a> respectively.
Defaults to <code class="docutils literal notranslate"><span class="pre">'%'</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><em>level</em></p></td>
<td><p>Set the root logger level to the specified
<a class="reference internal" href="#levels"><span class="std std-ref">level</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><em>stream</em></p></td>
<td><p>Use the specified stream to initialize the
<a class="reference internal" href="logging.handlers.html#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a>. Note that this
argument is incompatible with <em>filename</em> -
if both are present, a <code class="docutils literal notranslate"><span class="pre">ValueError</span></code> is
raised.</p></td>
</tr>
<tr class="row-odd"><td><p><em>handlers</em></p></td>
<td><p>If specified, this should be an iterable of
already created handlers to add to the root
logger. Any handlers which don’t already
have a formatter set will be assigned the
default formatter created in this function.
Note that this argument is incompatible
with <em>filename</em> or <em>stream</em> - if both
are present, a <code class="docutils literal notranslate"><span class="pre">ValueError</span></code> is raised.</p></td>
</tr>
<tr class="row-even"><td><p><em>force</em></p></td>
<td><p>If this keyword argument is specified as
true, any existing handlers attached to the
root logger are removed and closed, before
carrying out the configuration as specified
by the other arguments.</p></td>
</tr>
<tr class="row-odd"><td><p><em>encoding</em></p></td>
<td><p>If this keyword argument is specified along
with <em>filename</em>, its value is used when the
<a class="reference internal" href="logging.handlers.html#logging.FileHandler" title="logging.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a> is created, and thus
used when opening the output file.</p></td>
</tr>
<tr class="row-even"><td><p><em>errors</em></p></td>
<td><p>If this keyword argument is specified along
with <em>filename</em>, its value is used when the
<a class="reference internal" href="logging.handlers.html#logging.FileHandler" title="logging.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a> is created, and thus
used when opening the output file. If not
specified, the value ‘backslashreplace’ is
used. Note that if <code class="docutils literal notranslate"><span class="pre">None</span></code> is specified,
it will be passed as such to <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>,
which means that it will be treated the
same as passing ‘errors’.</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>style</em> argument was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>handlers</em> argument was added. Additional checks were added to
catch situations where incompatible arguments are specified (e.g.
<em>handlers</em> together with <em>stream</em> or <em>filename</em>, or <em>stream</em>
together with <em>filename</em>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>force</em> argument was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>encoding</em> and <em>errors</em> arguments were added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.shutdown">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">shutdown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.shutdown" title="Link to this definition">¶</a></dt>
<dd><p>Informs the logging system to perform an orderly shutdown by flushing and
closing all handlers. This should be called at application exit and no
further use of the logging system should be made after this call.</p>
<p>When the logging module is imported, it registers this function as an exit
handler (see <a class="reference internal" href="atexit.html#module-atexit" title="atexit: Register and execute cleanup functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">atexit</span></code></a>), so normally there’s no need to do that
manually.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.setLoggerClass">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">setLoggerClass</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">klass</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.setLoggerClass" title="Link to this definition">¶</a></dt>
<dd><p>Tells the logging system to use the class <em>klass</em> when instantiating a logger.
The class should define <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> such that only a name argument is
required, and the <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> should call <code class="xref py py-meth docutils literal notranslate"><span class="pre">Logger.__init__()</span></code>. This
function is typically called before any loggers are instantiated by applications
which need to use custom logger behavior. After this call, as at any other
time, do not instantiate loggers directly using the subclass: continue to use
the <a class="reference internal" href="#logging.getLogger" title="logging.getLogger"><code class="xref py py-func docutils literal notranslate"><span class="pre">logging.getLogger()</span></code></a> API to get your loggers.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="logging.setLogRecordFactory">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">setLogRecordFactory</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">factory</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.setLogRecordFactory" title="Link to this definition">¶</a></dt>
<dd><p>Set a callable which is used to create a <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>factory</strong> – The factory callable to be used to instantiate a log record.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span>This function has been provided, along with <a class="reference internal" href="#logging.getLogRecordFactory" title="logging.getLogRecordFactory"><code class="xref py py-func docutils literal notranslate"><span class="pre">getLogRecordFactory()</span></code></a>, to
allow developers more control over how the <a class="reference internal" href="#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> representing
a logging event is constructed.</p>
</div>
<p>The factory has the following signature:</p>
<p><code class="docutils literal notranslate"><span class="pre">factory(name,</span> <span class="pre">level,</span> <span class="pre">fn,</span> <span class="pre">lno,</span> <span class="pre">msg,</span> <span class="pre">args,</span> <span class="pre">exc_info,</span> <span class="pre">func=None,</span> <span class="pre">sinfo=None,</span> <span class="pre">**kwargs)</span></code></p>
<blockquote>
<div><dl class="field-list simple">
<dt class="field-odd">name<span class="colon">:</span></dt>
<dd class="field-odd"><p>The logger name.</p>
</dd>
<dt class="field-even">level<span class="colon">:</span></dt>
<dd class="field-even"><p>The logging level (numeric).</p>
</dd>
<dt class="field-odd">fn<span class="colon">:</span></dt>
<dd class="field-odd"><p>The full pathname of the file where the logging call was made.</p>
</dd>
<dt class="field-even">lno<span class="colon">:</span></dt>
<dd class="field-even"><p>The line number in the file where the logging call was made.</p>
</dd>
<dt class="field-odd">msg<span class="colon">:</span></dt>
<dd class="field-odd"><p>The logging message.</p>
</dd>
<dt class="field-even">args<span class="colon">:</span></dt>
<dd class="field-even"><p>The arguments for the logging message.</p>
</dd>
<dt class="field-odd">exc_info<span class="colon">:</span></dt>
<dd class="field-odd"><p>An exception tuple, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd>
<dt class="field-even">func<span class="colon">:</span></dt>
<dd class="field-even"><p>The name of the function or method which invoked the logging
call.</p>
</dd>
<dt class="field-odd">sinfo<span class="colon">:</span></dt>
<dd class="field-odd"><p>A stack traceback such as is provided by
<a class="reference internal" href="traceback.html#traceback.print_stack" title="traceback.print_stack"><code class="xref py py-func docutils literal notranslate"><span class="pre">traceback.print_stack()</span></code></a>, showing the call hierarchy.</p>
</dd>
<dt class="field-even">kwargs<span class="colon">:</span></dt>
<dd class="field-even"><p>Additional keyword arguments.</p>
</dd>
</dl>
</div></blockquote>
</dd></dl>

</section>
<section id="module-level-attributes">
<h2>Module-Level Attributes<a class="headerlink" href="#module-level-attributes" title="Link to this heading">¶</a></h2>
<dl class="py attribute">
<dt class="sig sig-object py" id="logging.lastResort">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">lastResort</span></span><a class="headerlink" href="#logging.lastResort" title="Link to this definition">¶</a></dt>
<dd><p>A “handler of last resort” is available through this attribute. This
is a <a class="reference internal" href="logging.handlers.html#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a> writing to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> with a level of
<code class="docutils literal notranslate"><span class="pre">WARNING</span></code>, and is used to handle logging events in the absence of any
logging configuration. The end result is to just print the message to
<code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>. This replaces the earlier error message saying that
“no handlers could be found for logger XYZ”. If you need the earlier
behaviour for some reason, <code class="docutils literal notranslate"><span class="pre">lastResort</span></code> can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.raiseExceptions">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">raiseExceptions</span></span><a class="headerlink" href="#logging.raiseExceptions" title="Link to this definition">¶</a></dt>
<dd><p>Used to see if exceptions during handling should be propagated.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<p>If <a class="reference internal" href="#logging.raiseExceptions" title="logging.raiseExceptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">raiseExceptions</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>,
exceptions get silently ignored. This is what is mostly wanted
for a logging system - most users will not care about errors in
the logging system, they are more interested in application errors.</p>
</dd></dl>

</section>
<section id="integration-with-the-warnings-module">
<h2>Integration with the warnings module<a class="headerlink" href="#integration-with-the-warnings-module" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.captureWarnings" title="logging.captureWarnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">captureWarnings()</span></code></a> function can be used to integrate <a class="reference internal" href="#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a>
with the <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module.</p>
<dl class="py function">
<dt class="sig sig-object py" id="logging.captureWarnings">
<span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">captureWarnings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">capture</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.captureWarnings" title="Link to this definition">¶</a></dt>
<dd><p>This function is used to turn the capture of warnings by logging on and
off.</p>
<p>If <em>capture</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, warnings issued by the <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module will
be redirected to the logging system. Specifically, a warning will be
formatted using <a class="reference internal" href="warnings.html#warnings.formatwarning" title="warnings.formatwarning"><code class="xref py py-func docutils literal notranslate"><span class="pre">warnings.formatwarning()</span></code></a> and the resulting string
logged to a logger named <code class="docutils literal notranslate"><span class="pre">'py.warnings'</span></code> with a severity of <a class="reference internal" href="#logging.WARNING" title="logging.WARNING"><code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code></a>.</p>
<p>If <em>capture</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the redirection of warnings to the logging system
will stop, and warnings will be redirected to their original destinations
(i.e. those in effect before <code class="docutils literal notranslate"><span class="pre">captureWarnings(True)</span></code> was called).</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="logging.config.html#module-logging.config" title="logging.config: Configuration of the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code></a></dt><dd><p>Configuration API for the logging module.</p>
</dd>
<dt>Module <a class="reference internal" href="logging.handlers.html#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a></dt><dd><p>Useful handlers included with the logging module.</p>
</dd>
<dt><span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0282/"><strong>PEP 282</strong></a> - A Logging System</dt><dd><p>The proposal which described this feature for inclusion in the Python standard
library.</p>
</dd>
<dt><a class="reference external" href="https://old.red-dove.com/python_logging.html">Original Python logging package</a></dt><dd><p>This is the original source for the <a class="reference internal" href="#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> package.  The version of the
package available from this site is suitable for use with Python 1.5.2, 2.1.x
and 2.2.x, which do not include the <a class="reference internal" href="#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> package in the standard
library.</p>
</dd>
</dl>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code> — Logging facility for Python</a><ul>
<li><a class="reference internal" href="#logger-objects">Logger Objects</a></li>
<li><a class="reference internal" href="#logging-levels">Logging Levels</a></li>
<li><a class="reference internal" href="#handler-objects">Handler Objects</a></li>
<li><a class="reference internal" href="#formatter-objects">Formatter Objects</a></li>
<li><a class="reference internal" href="#filter-objects">Filter Objects</a></li>
<li><a class="reference internal" href="#logrecord-objects">LogRecord Objects</a></li>
<li><a class="reference internal" href="#logrecord-attributes">LogRecord attributes</a></li>
<li><a class="reference internal" href="#loggeradapter-objects">LoggerAdapter Objects</a></li>
<li><a class="reference internal" href="#thread-safety">Thread Safety</a></li>
<li><a class="reference internal" href="#module-level-functions">Module-Level Functions</a></li>
<li><a class="reference internal" href="#module-level-attributes">Module-Level Attributes</a></li>
<li><a class="reference internal" href="#integration-with-the-warnings-module">Integration with the warnings module</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="getopt.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">getopt</span></code> — C-style parser for command line options</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="logging.config.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code> — Logging configuration</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/logging.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="logging.config.html" title="logging.config — Logging configuration"
             >next</a> |</li>
        <li class="right" >
          <a href="getopt.html" title="getopt — C-style parser for command line options"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" >Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code> — Logging facility for Python</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>