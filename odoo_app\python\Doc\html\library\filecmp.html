<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="filecmp — File and Directory Comparisons" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/filecmp.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/filecmp.py The filecmp module defines functions to compare files and directories, with various optional time/correctness trade-offs. For comparing files, see also the difflib modul..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/filecmp.py The filecmp module defines functions to compare files and directories, with various optional time/correctness trade-offs. For comparing files, see also the difflib modul..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>filecmp — File and Directory Comparisons &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tempfile — Generate temporary files and directories" href="tempfile.html" />
    <link rel="prev" title="stat — Interpreting stat() results" href="stat.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/filecmp.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code> — File and Directory Comparisons</a><ul>
<li><a class="reference internal" href="#the-dircmp-class">The <code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code> class</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stat.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code> — Interpreting <code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code> results</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tempfile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tempfile</span></code> — Generate temporary files and directories</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/filecmp.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tempfile.html" title="tempfile — Generate temporary files and directories"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="stat.html" title="stat — Interpreting stat() results"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" accesskey="U">File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code> — File and Directory Comparisons</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-filecmp">
<span id="filecmp-file-and-directory-comparisons"></span><h1><a class="reference internal" href="#module-filecmp" title="filecmp: Compare files efficiently."><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code></a> — File and Directory Comparisons<a class="headerlink" href="#module-filecmp" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/filecmp.py">Lib/filecmp.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-filecmp" title="filecmp: Compare files efficiently."><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code></a> module defines functions to compare files and directories,
with various optional time/correctness trade-offs. For comparing files,
see also the <a class="reference internal" href="difflib.html#module-difflib" title="difflib: Helpers for computing differences between objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">difflib</span></code></a> module.</p>
<p>The <a class="reference internal" href="#module-filecmp" title="filecmp: Compare files efficiently."><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="filecmp.cmp">
<span class="sig-prename descclassname"><span class="pre">filecmp.</span></span><span class="sig-name descname"><span class="pre">cmp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">f1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">f2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shallow</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.cmp" title="Link to this definition">¶</a></dt>
<dd><p>Compare the files named <em>f1</em> and <em>f2</em>, returning <code class="docutils literal notranslate"><span class="pre">True</span></code> if they seem equal,
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<p>If <em>shallow</em> is true and the <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a> signatures (file type, size, and
modification time) of both files are identical, the files are taken to be
equal.</p>
<p>Otherwise, the files are treated as different if their sizes or contents differ.</p>
<p>Note that no external programs are called from this function, giving it
portability and efficiency.</p>
<p>This function uses a cache for past comparisons and the results,
with cache entries invalidated if the <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a> information for the
file changes.  The entire cache may be cleared using <a class="reference internal" href="#filecmp.clear_cache" title="filecmp.clear_cache"><code class="xref py py-func docutils literal notranslate"><span class="pre">clear_cache()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="filecmp.cmpfiles">
<span class="sig-prename descclassname"><span class="pre">filecmp.</span></span><span class="sig-name descname"><span class="pre">cmpfiles</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dir1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dir2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">common</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shallow</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.cmpfiles" title="Link to this definition">¶</a></dt>
<dd><p>Compare the files in the two directories <em>dir1</em> and <em>dir2</em> whose names are
given by <em>common</em>.</p>
<p>Returns three lists of file names: <em>match</em>, <em>mismatch</em>,
<em>errors</em>.  <em>match</em> contains the list of files that match, <em>mismatch</em> contains
the names of those that don’t, and <em>errors</em> lists the names of files which
could not be compared.  Files are listed in <em>errors</em> if they don’t exist in
one of the directories, the user lacks permission to read them or if the
comparison could not be done for some other reason.</p>
<p>The <em>shallow</em> parameter has the same meaning and default value as for
<a class="reference internal" href="#filecmp.cmp" title="filecmp.cmp"><code class="xref py py-func docutils literal notranslate"><span class="pre">filecmp.cmp()</span></code></a>.</p>
<p>For example, <code class="docutils literal notranslate"><span class="pre">cmpfiles('a',</span> <span class="pre">'b',</span> <span class="pre">['c',</span> <span class="pre">'d/e'])</span></code> will compare <code class="docutils literal notranslate"><span class="pre">a/c</span></code> with
<code class="docutils literal notranslate"><span class="pre">b/c</span></code> and <code class="docutils literal notranslate"><span class="pre">a/d/e</span></code> with <code class="docutils literal notranslate"><span class="pre">b/d/e</span></code>.  <code class="docutils literal notranslate"><span class="pre">'c'</span></code> and <code class="docutils literal notranslate"><span class="pre">'d/e'</span></code> will each be in
one of the three returned lists.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="filecmp.clear_cache">
<span class="sig-prename descclassname"><span class="pre">filecmp.</span></span><span class="sig-name descname"><span class="pre">clear_cache</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.clear_cache" title="Link to this definition">¶</a></dt>
<dd><p>Clear the filecmp cache. This may be useful if a file is compared so quickly
after it is modified that it is within the mtime resolution of
the underlying filesystem.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<section id="the-dircmp-class">
<span id="dircmp-objects"></span><h2>The <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a> class<a class="headerlink" href="#the-dircmp-class" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="filecmp.dircmp">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">filecmp.</span></span><span class="sig-name descname"><span class="pre">dircmp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hide</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.dircmp" title="Link to this definition">¶</a></dt>
<dd><p>Construct a new directory comparison object, to compare the directories <em>a</em>
and <em>b</em>.  <em>ignore</em> is a list of names to ignore, and defaults to
<a class="reference internal" href="#filecmp.DEFAULT_IGNORES" title="filecmp.DEFAULT_IGNORES"><code class="xref py py-const docutils literal notranslate"><span class="pre">filecmp.DEFAULT_IGNORES</span></code></a>.  <em>hide</em> is a list of names to hide, and
defaults to <code class="docutils literal notranslate"><span class="pre">[os.curdir,</span> <span class="pre">os.pardir]</span></code>.</p>
<p>The <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a> class compares files by doing <em>shallow</em> comparisons
as described for <a class="reference internal" href="#filecmp.cmp" title="filecmp.cmp"><code class="xref py py-func docutils literal notranslate"><span class="pre">filecmp.cmp()</span></code></a>.</p>
<p>The <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a> class provides the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="filecmp.dircmp.report">
<span class="sig-name descname"><span class="pre">report</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.dircmp.report" title="Link to this definition">¶</a></dt>
<dd><p>Print (to <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>) a comparison between <em>a</em> and <em>b</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="filecmp.dircmp.report_partial_closure">
<span class="sig-name descname"><span class="pre">report_partial_closure</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.dircmp.report_partial_closure" title="Link to this definition">¶</a></dt>
<dd><p>Print a comparison between <em>a</em> and <em>b</em> and common immediate
subdirectories.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="filecmp.dircmp.report_full_closure">
<span class="sig-name descname"><span class="pre">report_full_closure</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#filecmp.dircmp.report_full_closure" title="Link to this definition">¶</a></dt>
<dd><p>Print a comparison between <em>a</em> and <em>b</em> and common subdirectories
(recursively).</p>
</dd></dl>

<p>The <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a> class offers a number of interesting attributes that may be
used to get various bits of information about the directory trees being
compared.</p>
<p>Note that via <a class="reference internal" href="../reference/datamodel.html#object.__getattr__" title="object.__getattr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattr__()</span></code></a> hooks, all attributes are computed lazily,
so there is no speed penalty if only those attributes which are lightweight
to compute are used.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.left">
<span class="sig-name descname"><span class="pre">left</span></span><a class="headerlink" href="#filecmp.dircmp.left" title="Link to this definition">¶</a></dt>
<dd><p>The directory <em>a</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.right">
<span class="sig-name descname"><span class="pre">right</span></span><a class="headerlink" href="#filecmp.dircmp.right" title="Link to this definition">¶</a></dt>
<dd><p>The directory <em>b</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.left_list">
<span class="sig-name descname"><span class="pre">left_list</span></span><a class="headerlink" href="#filecmp.dircmp.left_list" title="Link to this definition">¶</a></dt>
<dd><p>Files and subdirectories in <em>a</em>, filtered by <em>hide</em> and <em>ignore</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.right_list">
<span class="sig-name descname"><span class="pre">right_list</span></span><a class="headerlink" href="#filecmp.dircmp.right_list" title="Link to this definition">¶</a></dt>
<dd><p>Files and subdirectories in <em>b</em>, filtered by <em>hide</em> and <em>ignore</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.common">
<span class="sig-name descname"><span class="pre">common</span></span><a class="headerlink" href="#filecmp.dircmp.common" title="Link to this definition">¶</a></dt>
<dd><p>Files and subdirectories in both <em>a</em> and <em>b</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.left_only">
<span class="sig-name descname"><span class="pre">left_only</span></span><a class="headerlink" href="#filecmp.dircmp.left_only" title="Link to this definition">¶</a></dt>
<dd><p>Files and subdirectories only in <em>a</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.right_only">
<span class="sig-name descname"><span class="pre">right_only</span></span><a class="headerlink" href="#filecmp.dircmp.right_only" title="Link to this definition">¶</a></dt>
<dd><p>Files and subdirectories only in <em>b</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.common_dirs">
<span class="sig-name descname"><span class="pre">common_dirs</span></span><a class="headerlink" href="#filecmp.dircmp.common_dirs" title="Link to this definition">¶</a></dt>
<dd><p>Subdirectories in both <em>a</em> and <em>b</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.common_files">
<span class="sig-name descname"><span class="pre">common_files</span></span><a class="headerlink" href="#filecmp.dircmp.common_files" title="Link to this definition">¶</a></dt>
<dd><p>Files in both <em>a</em> and <em>b</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.common_funny">
<span class="sig-name descname"><span class="pre">common_funny</span></span><a class="headerlink" href="#filecmp.dircmp.common_funny" title="Link to this definition">¶</a></dt>
<dd><p>Names in both <em>a</em> and <em>b</em>, such that the type differs between the
directories, or names for which <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a> reports an error.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.same_files">
<span class="sig-name descname"><span class="pre">same_files</span></span><a class="headerlink" href="#filecmp.dircmp.same_files" title="Link to this definition">¶</a></dt>
<dd><p>Files which are identical in both <em>a</em> and <em>b</em>, using the class’s
file comparison operator.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.diff_files">
<span class="sig-name descname"><span class="pre">diff_files</span></span><a class="headerlink" href="#filecmp.dircmp.diff_files" title="Link to this definition">¶</a></dt>
<dd><p>Files which are in both <em>a</em> and <em>b</em>, whose contents differ according
to the class’s file comparison operator.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.funny_files">
<span class="sig-name descname"><span class="pre">funny_files</span></span><a class="headerlink" href="#filecmp.dircmp.funny_files" title="Link to this definition">¶</a></dt>
<dd><p>Files which are in both <em>a</em> and <em>b</em>, but could not be compared.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.dircmp.subdirs">
<span class="sig-name descname"><span class="pre">subdirs</span></span><a class="headerlink" href="#filecmp.dircmp.subdirs" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping names in <a class="reference internal" href="#filecmp.dircmp.common_dirs" title="filecmp.dircmp.common_dirs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">common_dirs</span></code></a> to <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a>
instances (or MyDirCmp instances if this instance is of type MyDirCmp, a
subclass of <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Previously entries were always <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a> instances. Now entries
are the same type as <em>self</em>, if <em>self</em> is a subclass of
<a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a>.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="filecmp.DEFAULT_IGNORES">
<span class="sig-prename descclassname"><span class="pre">filecmp.</span></span><span class="sig-name descname"><span class="pre">DEFAULT_IGNORES</span></span><a class="headerlink" href="#filecmp.DEFAULT_IGNORES" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>List of directories ignored by <a class="reference internal" href="#filecmp.dircmp" title="filecmp.dircmp"><code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code></a> by default.</p>
</dd></dl>

<p>Here is a simplified example of using the <code class="docutils literal notranslate"><span class="pre">subdirs</span></code> attribute to search
recursively through two directories to show common different files:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">filecmp</span> <span class="kn">import</span> <span class="n">dircmp</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">print_diff_files</span><span class="p">(</span><span class="n">dcmp</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">dcmp</span><span class="o">.</span><span class="n">diff_files</span><span class="p">:</span>
<span class="gp">... </span>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;diff_file </span><span class="si">%s</span><span class="s2"> found in </span><span class="si">%s</span><span class="s2"> and </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">dcmp</span><span class="o">.</span><span class="n">left</span><span class="p">,</span>
<span class="gp">... </span>              <span class="n">dcmp</span><span class="o">.</span><span class="n">right</span><span class="p">))</span>
<span class="gp">... </span>    <span class="k">for</span> <span class="n">sub_dcmp</span> <span class="ow">in</span> <span class="n">dcmp</span><span class="o">.</span><span class="n">subdirs</span><span class="o">.</span><span class="n">values</span><span class="p">():</span>
<span class="gp">... </span>        <span class="n">print_diff_files</span><span class="p">(</span><span class="n">sub_dcmp</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dcmp</span> <span class="o">=</span> <span class="n">dircmp</span><span class="p">(</span><span class="s1">&#39;dir1&#39;</span><span class="p">,</span> <span class="s1">&#39;dir2&#39;</span><span class="p">)</span> 
<span class="gp">&gt;&gt;&gt; </span><span class="n">print_diff_files</span><span class="p">(</span><span class="n">dcmp</span><span class="p">)</span> 
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code> — File and Directory Comparisons</a><ul>
<li><a class="reference internal" href="#the-dircmp-class">The <code class="xref py py-class docutils literal notranslate"><span class="pre">dircmp</span></code> class</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stat.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code> — Interpreting <code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code> results</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tempfile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tempfile</span></code> — Generate temporary files and directories</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/filecmp.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tempfile.html" title="tempfile — Generate temporary files and directories"
             >next</a> |</li>
        <li class="right" >
          <a href="stat.html" title="stat — Interpreting stat() results"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" >File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code> — File and Directory Comparisons</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>