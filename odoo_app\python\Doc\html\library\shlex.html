<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="shlex — Simple lexical analysis" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/shlex.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/shlex.py The shlex class makes it easy to write lexical analyzers for simple syntaxes resembling that of the Unix shell. This will often be useful for writing minilanguages, (for e..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/shlex.py The shlex class makes it easy to write lexical analyzers for simple syntaxes resembling that of the Unix shell. This will often be useful for writing minilanguages, (for e..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>shlex — Simple lexical analysis &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Graphical User Interfaces with Tk" href="tk.html" />
    <link rel="prev" title="cmd — Support for line-oriented command interpreters" href="cmd.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/shlex.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code> — Simple lexical analysis</a><ul>
<li><a class="reference internal" href="#shlex-objects">shlex Objects</a></li>
<li><a class="reference internal" href="#parsing-rules">Parsing Rules</a></li>
<li><a class="reference internal" href="#improved-compatibility-with-shells">Improved Compatibility with Shells</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmd.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmd</span></code> — Support for line-oriented command interpreters</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tk.html"
                          title="next chapter">Graphical User Interfaces with Tk</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/shlex.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tk.html" title="Graphical User Interfaces with Tk"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmd.html" title="cmd — Support for line-oriented command interpreters"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="frameworks.html" accesskey="U">Program Frameworks</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code> — Simple lexical analysis</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-shlex">
<span id="shlex-simple-lexical-analysis"></span><h1><a class="reference internal" href="#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code></a> — Simple lexical analysis<a class="headerlink" href="#module-shlex" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/shlex.py">Lib/shlex.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> class makes it easy to write lexical analyzers for
simple syntaxes resembling that of the Unix shell.  This will often be useful
for writing minilanguages, (for example, in run control files for Python
applications) or for parsing quoted strings.</p>
<p>The <a class="reference internal" href="#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="shlex.split">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">comments</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">posix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.split" title="Link to this definition">¶</a></dt>
<dd><p>Split the string <em>s</em> using shell-like syntax. If <em>comments</em> is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>
(the default), the parsing of comments in the given string will be disabled
(setting the <a class="reference internal" href="#shlex.shlex.commenters" title="shlex.shlex.commenters"><code class="xref py py-attr docutils literal notranslate"><span class="pre">commenters</span></code></a> attribute of the
<a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> instance to the empty string).  This function operates
in POSIX mode by default, but uses non-POSIX mode if the <em>posix</em> argument is
false.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Passing <code class="docutils literal notranslate"><span class="pre">None</span></code> for <em>s</em> argument now raises an exception, rather than
reading <a class="reference internal" href="sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shlex.join">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">join</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">split_command</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.join" title="Link to this definition">¶</a></dt>
<dd><p>Concatenate the tokens of the list <em>split_command</em> and return a string.
This function is the inverse of <a class="reference internal" href="#shlex.split" title="shlex.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">shlex</span> <span class="kn">import</span> <span class="n">join</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">join</span><span class="p">([</span><span class="s1">&#39;echo&#39;</span><span class="p">,</span> <span class="s1">&#39;-n&#39;</span><span class="p">,</span> <span class="s1">&#39;Multiple words&#39;</span><span class="p">]))</span>
<span class="go">echo -n &#39;Multiple words&#39;</span>
</pre></div>
</div>
<p>The returned value is shell-escaped to protect against injection
vulnerabilities (see <a class="reference internal" href="#shlex.quote" title="shlex.quote"><code class="xref py py-func docutils literal notranslate"><span class="pre">quote()</span></code></a>).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shlex.quote">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">quote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.quote" title="Link to this definition">¶</a></dt>
<dd><p>Return a shell-escaped version of the string <em>s</em>.  The returned value is a
string that can safely be used as one token in a shell command line, for
cases where you cannot use a list.</p>
<div class="admonition warning" id="shlex-quote-warning">
<p class="admonition-title">Warning</p>
<p>The <code class="docutils literal notranslate"><span class="pre">shlex</span></code> module is <strong>only designed for Unix shells</strong>.</p>
<p>The <a class="reference internal" href="#shlex.quote" title="shlex.quote"><code class="xref py py-func docutils literal notranslate"><span class="pre">quote()</span></code></a> function is not guaranteed to be correct on non-POSIX
compliant shells or shells from other operating systems such as Windows.
Executing commands quoted by this module on such shells can open up the
possibility of a command injection vulnerability.</p>
<p>Consider using functions that pass command arguments with lists such as
<a class="reference internal" href="subprocess.html#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">subprocess.run()</span></code></a> with <code class="docutils literal notranslate"><span class="pre">shell=False</span></code>.</p>
</div>
<p>This idiom would be unsafe:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">filename</span> <span class="o">=</span> <span class="s1">&#39;somefile; rm -rf ~&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">command</span> <span class="o">=</span> <span class="s1">&#39;ls -l </span><span class="si">{}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">filename</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">command</span><span class="p">)</span>  <span class="c1"># executed by a shell: boom!</span>
<span class="go">ls -l somefile; rm -rf ~</span>
</pre></div>
</div>
<p><a class="reference internal" href="#shlex.quote" title="shlex.quote"><code class="xref py py-func docutils literal notranslate"><span class="pre">quote()</span></code></a> lets you plug the security hole:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">shlex</span> <span class="kn">import</span> <span class="n">quote</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">command</span> <span class="o">=</span> <span class="s1">&#39;ls -l </span><span class="si">{}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">quote</span><span class="p">(</span><span class="n">filename</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">command</span><span class="p">)</span>
<span class="go">ls -l &#39;somefile; rm -rf ~&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">remote_command</span> <span class="o">=</span> <span class="s1">&#39;ssh home </span><span class="si">{}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">quote</span><span class="p">(</span><span class="n">command</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">remote_command</span><span class="p">)</span>
<span class="go">ssh home &#39;ls -l &#39;&quot;&#39;&quot;&#39;somefile; rm -rf ~&#39;&quot;&#39;&quot;&#39;&#39;</span>
</pre></div>
</div>
<p>The quoting is compatible with UNIX shells and with <a class="reference internal" href="#shlex.split" title="shlex.split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a>:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">shlex</span> <span class="kn">import</span> <span class="n">split</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">remote_command</span> <span class="o">=</span> <span class="n">split</span><span class="p">(</span><span class="n">remote_command</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">remote_command</span>
<span class="go">[&#39;ssh&#39;, &#39;home&#39;, &quot;ls -l &#39;somefile; rm -rf ~&#39;&quot;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">command</span> <span class="o">=</span> <span class="n">split</span><span class="p">(</span><span class="n">remote_command</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">command</span>
<span class="go">[&#39;ls&#39;, &#39;-l&#39;, &#39;somefile; rm -rf ~&#39;]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code></a> module defines the following class:</p>
<dl class="py class">
<dt class="sig sig-object py" id="shlex.shlex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">shlex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">instream</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">infile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">posix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">punctuation_chars</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> instance or subclass instance is a lexical analyzer
object.  The initialization argument, if present, specifies where to read
characters from.  It must be a file-/stream-like object with
<a class="reference internal" href="io.html#io.TextIOBase.read" title="io.TextIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> and <a class="reference internal" href="io.html#io.TextIOBase.readline" title="io.TextIOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> methods, or
a string.  If no argument is given, input will be taken from <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code>.
The second optional argument is a filename string, which sets the initial
value of the <a class="reference internal" href="#shlex.shlex.infile" title="shlex.shlex.infile"><code class="xref py py-attr docutils literal notranslate"><span class="pre">infile</span></code></a> attribute.  If the <em>instream</em>
argument is omitted or equal to <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code>, this second argument
defaults to “stdin”.  The <em>posix</em> argument defines the operational mode:
when <em>posix</em> is not true (default), the <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> instance will
operate in compatibility mode.  When operating in POSIX mode,
<a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> will try to be as close as possible to the POSIX shell
parsing rules.  The <em>punctuation_chars</em> argument provides a way to make the
behaviour even closer to how real shells parse.  This can take a number of
values: the default value, <code class="docutils literal notranslate"><span class="pre">False</span></code>, preserves the behaviour seen under
Python 3.5 and earlier.  If set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, then parsing of the characters
<code class="docutils literal notranslate"><span class="pre">();&lt;&gt;|&amp;</span></code> is changed: any run of these characters (considered punctuation
characters) is returned as a single token.  If set to a non-empty string of
characters, those characters will be used as the punctuation characters.  Any
characters in the <a class="reference internal" href="#shlex.shlex.wordchars" title="shlex.shlex.wordchars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">wordchars</span></code></a> attribute that appear in
<em>punctuation_chars</em> will be removed from <a class="reference internal" href="#shlex.shlex.wordchars" title="shlex.shlex.wordchars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">wordchars</span></code></a>.  See
<a class="reference internal" href="#improved-shell-compatibility"><span class="std std-ref">Improved Compatibility with Shells</span></a> for more information. <em>punctuation_chars</em>
can be set only upon <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> instance creation and can’t be
modified later.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>punctuation_chars</em> parameter was added.</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="configparser.html#module-configparser" title="configparser: Configuration file parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">configparser</span></code></a></dt><dd><p>Parser for configuration files similar to the Windows <code class="file docutils literal notranslate"><span class="pre">.ini</span></code> files.</p>
</dd>
</dl>
</div>
<section id="shlex-objects">
<span id="id1"></span><h2>shlex Objects<a class="headerlink" href="#shlex-objects" title="Link to this heading">¶</a></h2>
<p>A <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> instance has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.get_token">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">get_token</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.get_token" title="Link to this definition">¶</a></dt>
<dd><p>Return a token.  If tokens have been stacked using <a class="reference internal" href="#shlex.shlex.push_token" title="shlex.shlex.push_token"><code class="xref py py-meth docutils literal notranslate"><span class="pre">push_token()</span></code></a>, pop a
token off the stack.  Otherwise, read one from the input stream.  If reading
encounters an immediate end-of-file, <a class="reference internal" href="#shlex.shlex.eof" title="shlex.shlex.eof"><code class="xref py py-attr docutils literal notranslate"><span class="pre">eof</span></code></a> is returned (the empty
string (<code class="docutils literal notranslate"><span class="pre">''</span></code>) in non-POSIX mode, and <code class="docutils literal notranslate"><span class="pre">None</span></code> in POSIX mode).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.push_token">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">push_token</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.push_token" title="Link to this definition">¶</a></dt>
<dd><p>Push the argument onto the token stack.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.read_token">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">read_token</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.read_token" title="Link to this definition">¶</a></dt>
<dd><p>Read a raw token.  Ignore the pushback stack, and do not interpret source
requests.  (This is not ordinarily a useful entry point, and is documented here
only for the sake of completeness.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.sourcehook">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">sourcehook</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.sourcehook" title="Link to this definition">¶</a></dt>
<dd><p>When <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> detects a source request (see <a class="reference internal" href="#shlex.shlex.source" title="shlex.shlex.source"><code class="xref py py-attr docutils literal notranslate"><span class="pre">source</span></code></a>
below) this method is given the following token as argument, and expected
to return a tuple consisting of a filename and an open file-like object.</p>
<p>Normally, this method first strips any quotes off the argument.  If the result
is an absolute pathname, or there was no previous source request in effect, or
the previous source was a stream (such as <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code>), the result is left
alone.  Otherwise, if the result is a relative pathname, the directory part of
the name of the file immediately before it on the source inclusion stack is
prepended (this behavior is like the way the C preprocessor handles <code class="docutils literal notranslate"><span class="pre">#include</span>
<span class="pre">&quot;file.h&quot;</span></code>).</p>
<p>The result of the manipulations is treated as a filename, and returned as the
first component of the tuple, with <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> called on it to yield the second
component. (Note: this is the reverse of the order of arguments in instance
initialization!)</p>
<p>This hook is exposed so that you can use it to implement directory search paths,
addition of file extensions, and other namespace hacks. There is no
corresponding ‘close’ hook, but a shlex instance will call the
<a class="reference internal" href="io.html#io.IOBase.close" title="io.IOBase.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method of the sourced input stream when it returns
EOF.</p>
<p>For more explicit control of source stacking, use the <a class="reference internal" href="#shlex.shlex.push_source" title="shlex.shlex.push_source"><code class="xref py py-meth docutils literal notranslate"><span class="pre">push_source()</span></code></a> and
<a class="reference internal" href="#shlex.shlex.pop_source" title="shlex.shlex.pop_source"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pop_source()</span></code></a> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.push_source">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">push_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newstream</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newfile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.push_source" title="Link to this definition">¶</a></dt>
<dd><p>Push an input source stream onto the input stack.  If the filename argument is
specified it will later be available for use in error messages.  This is the
same method used internally by the <a class="reference internal" href="#shlex.shlex.sourcehook" title="shlex.shlex.sourcehook"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sourcehook()</span></code></a> method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.pop_source">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">pop_source</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.pop_source" title="Link to this definition">¶</a></dt>
<dd><p>Pop the last-pushed input source from the input stack. This is the same method
used internally when the lexer reaches EOF on a stacked input stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="shlex.shlex.error_leader">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">error_leader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">infile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shlex.shlex.error_leader" title="Link to this definition">¶</a></dt>
<dd><p>This method generates an error message leader in the format of a Unix C compiler
error label; the format is <code class="docutils literal notranslate"><span class="pre">'&quot;%s&quot;,</span> <span class="pre">line</span> <span class="pre">%d:</span> <span class="pre">'</span></code>, where the <code class="docutils literal notranslate"><span class="pre">%s</span></code> is replaced
with the name of the current source file and the <code class="docutils literal notranslate"><span class="pre">%d</span></code> with the current input
line number (the optional arguments can be used to override these).</p>
<p>This convenience is provided to encourage <a class="reference internal" href="#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code></a> users to generate error
messages in the standard, parseable format understood by Emacs and other Unix
tools.</p>
</dd></dl>

<p>Instances of <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> subclasses have some public instance
variables which either control lexical analysis or can be used for debugging:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.commenters">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">commenters</span></span><a class="headerlink" href="#shlex.shlex.commenters" title="Link to this definition">¶</a></dt>
<dd><p>The string of characters that are recognized as comment beginners. All
characters from the comment beginner to end of line are ignored. Includes just
<code class="docutils literal notranslate"><span class="pre">'#'</span></code> by default.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.wordchars">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">wordchars</span></span><a class="headerlink" href="#shlex.shlex.wordchars" title="Link to this definition">¶</a></dt>
<dd><p>The string of characters that will accumulate into multi-character tokens.  By
default, includes all ASCII alphanumerics and underscore.  In POSIX mode, the
accented characters in the Latin-1 set are also included.  If
<a class="reference internal" href="#shlex.shlex.punctuation_chars" title="shlex.shlex.punctuation_chars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">punctuation_chars</span></code></a> is not empty, the characters <code class="docutils literal notranslate"><span class="pre">~-./*?=</span></code>, which can
appear in filename specifications and command line parameters, will also be
included in this attribute, and any characters which appear in
<code class="docutils literal notranslate"><span class="pre">punctuation_chars</span></code> will be removed from <code class="docutils literal notranslate"><span class="pre">wordchars</span></code> if they are present
there. If <a class="reference internal" href="#shlex.shlex.whitespace_split" title="shlex.shlex.whitespace_split"><code class="xref py py-attr docutils literal notranslate"><span class="pre">whitespace_split</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, this will have no
effect.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.whitespace">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">whitespace</span></span><a class="headerlink" href="#shlex.shlex.whitespace" title="Link to this definition">¶</a></dt>
<dd><p>Characters that will be considered whitespace and skipped.  Whitespace bounds
tokens.  By default, includes space, tab, linefeed and carriage-return.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.escape">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">escape</span></span><a class="headerlink" href="#shlex.shlex.escape" title="Link to this definition">¶</a></dt>
<dd><p>Characters that will be considered as escape. This will be only used in POSIX
mode, and includes just <code class="docutils literal notranslate"><span class="pre">'\'</span></code> by default.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.quotes">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">quotes</span></span><a class="headerlink" href="#shlex.shlex.quotes" title="Link to this definition">¶</a></dt>
<dd><p>Characters that will be considered string quotes.  The token accumulates until
the same quote is encountered again (thus, different quote types protect each
other as in the shell.)  By default, includes ASCII single and double quotes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.escapedquotes">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">escapedquotes</span></span><a class="headerlink" href="#shlex.shlex.escapedquotes" title="Link to this definition">¶</a></dt>
<dd><p>Characters in <a class="reference internal" href="#shlex.shlex.quotes" title="shlex.shlex.quotes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">quotes</span></code></a> that will interpret escape characters defined in
<a class="reference internal" href="#shlex.shlex.escape" title="shlex.shlex.escape"><code class="xref py py-attr docutils literal notranslate"><span class="pre">escape</span></code></a>.  This is only used in POSIX mode, and includes just <code class="docutils literal notranslate"><span class="pre">'&quot;'</span></code> by
default.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.whitespace_split">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">whitespace_split</span></span><a class="headerlink" href="#shlex.shlex.whitespace_split" title="Link to this definition">¶</a></dt>
<dd><p>If <code class="docutils literal notranslate"><span class="pre">True</span></code>, tokens will only be split in whitespaces.  This is useful, for
example, for parsing command lines with <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a>, getting
tokens in a similar way to shell arguments.  When used in combination with
<a class="reference internal" href="#shlex.shlex.punctuation_chars" title="shlex.shlex.punctuation_chars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">punctuation_chars</span></code></a>, tokens will be split on whitespace in addition to
those characters.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#shlex.shlex.punctuation_chars" title="shlex.shlex.punctuation_chars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">punctuation_chars</span></code></a> attribute was made compatible with the
<a class="reference internal" href="#shlex.shlex.whitespace_split" title="shlex.shlex.whitespace_split"><code class="xref py py-attr docutils literal notranslate"><span class="pre">whitespace_split</span></code></a> attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.infile">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">infile</span></span><a class="headerlink" href="#shlex.shlex.infile" title="Link to this definition">¶</a></dt>
<dd><p>The name of the current input file, as initially set at class instantiation time
or stacked by later source requests.  It may be useful to examine this when
constructing error messages.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.instream">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">instream</span></span><a class="headerlink" href="#shlex.shlex.instream" title="Link to this definition">¶</a></dt>
<dd><p>The input stream from which this <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> instance is reading
characters.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.source">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">source</span></span><a class="headerlink" href="#shlex.shlex.source" title="Link to this definition">¶</a></dt>
<dd><p>This attribute is <code class="docutils literal notranslate"><span class="pre">None</span></code> by default.  If you assign a string to it, that
string will be recognized as a lexical-level inclusion request similar to the
<code class="docutils literal notranslate"><span class="pre">source</span></code> keyword in various shells.  That is, the immediately following token
will be opened as a filename and input will be taken from that stream until
EOF, at which point the <a class="reference internal" href="io.html#io.IOBase.close" title="io.IOBase.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method of that stream will be
called and the input source will again become the original input stream.  Source
requests may be stacked any number of levels deep.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.debug">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">debug</span></span><a class="headerlink" href="#shlex.shlex.debug" title="Link to this definition">¶</a></dt>
<dd><p>If this attribute is numeric and <code class="docutils literal notranslate"><span class="pre">1</span></code> or more, a <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a>
instance will print verbose progress output on its behavior.  If you need
to use this, you can read the module source code to learn the details.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.lineno">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#shlex.shlex.lineno" title="Link to this definition">¶</a></dt>
<dd><p>Source line number (count of newlines seen so far plus one).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.token">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">token</span></span><a class="headerlink" href="#shlex.shlex.token" title="Link to this definition">¶</a></dt>
<dd><p>The token buffer.  It may be useful to examine this when catching exceptions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.eof">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">eof</span></span><a class="headerlink" href="#shlex.shlex.eof" title="Link to this definition">¶</a></dt>
<dd><p>Token used to determine end of file. This will be set to the empty string
(<code class="docutils literal notranslate"><span class="pre">''</span></code>), in non-POSIX mode, and to <code class="docutils literal notranslate"><span class="pre">None</span></code> in POSIX mode.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="shlex.shlex.punctuation_chars">
<span class="sig-prename descclassname"><span class="pre">shlex.</span></span><span class="sig-name descname"><span class="pre">punctuation_chars</span></span><a class="headerlink" href="#shlex.shlex.punctuation_chars" title="Link to this definition">¶</a></dt>
<dd><p>A read-only property. Characters that will be considered punctuation. Runs of
punctuation characters will be returned as a single token. However, note that no
semantic validity checking will be performed: for example, ‘&gt;&gt;&gt;’ could be
returned as a token, even though it may not be recognised as such by shells.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

</section>
<section id="parsing-rules">
<span id="shlex-parsing-rules"></span><h2>Parsing Rules<a class="headerlink" href="#parsing-rules" title="Link to this heading">¶</a></h2>
<p>When operating in non-POSIX mode, <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> will try to obey to the
following rules.</p>
<ul class="simple">
<li><p>Quote characters are not recognized within words (<code class="docutils literal notranslate"><span class="pre">Do&quot;Not&quot;Separate</span></code> is
parsed as the single word <code class="docutils literal notranslate"><span class="pre">Do&quot;Not&quot;Separate</span></code>);</p></li>
<li><p>Escape characters are not recognized;</p></li>
<li><p>Enclosing characters in quotes preserve the literal value of all characters
within the quotes;</p></li>
<li><p>Closing quotes separate words (<code class="docutils literal notranslate"><span class="pre">&quot;Do&quot;Separate</span></code> is parsed as <code class="docutils literal notranslate"><span class="pre">&quot;Do&quot;</span></code> and
<code class="docutils literal notranslate"><span class="pre">Separate</span></code>);</p></li>
<li><p>If <a class="reference internal" href="#shlex.shlex.whitespace_split" title="shlex.shlex.whitespace_split"><code class="xref py py-attr docutils literal notranslate"><span class="pre">whitespace_split</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, any character not
declared to be a word character, whitespace, or a quote will be returned as
a single-character token. If it is <code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> will only
split words in whitespaces;</p></li>
<li><p>EOF is signaled with an empty string (<code class="docutils literal notranslate"><span class="pre">''</span></code>);</p></li>
<li><p>It’s not possible to parse empty strings, even if quoted.</p></li>
</ul>
<p>When operating in POSIX mode, <a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> will try to obey to the
following parsing rules.</p>
<ul class="simple">
<li><p>Quotes are stripped out, and do not separate words (<code class="docutils literal notranslate"><span class="pre">&quot;Do&quot;Not&quot;Separate&quot;</span></code> is
parsed as the single word <code class="docutils literal notranslate"><span class="pre">DoNotSeparate</span></code>);</p></li>
<li><p>Non-quoted escape characters (e.g. <code class="docutils literal notranslate"><span class="pre">'\'</span></code>) preserve the literal value of the
next character that follows;</p></li>
<li><p>Enclosing characters in quotes which are not part of
<a class="reference internal" href="#shlex.shlex.escapedquotes" title="shlex.shlex.escapedquotes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">escapedquotes</span></code></a> (e.g. <code class="docutils literal notranslate"><span class="pre">&quot;'&quot;</span></code>) preserve the literal value
of all characters within the quotes;</p></li>
<li><p>Enclosing characters in quotes which are part of
<a class="reference internal" href="#shlex.shlex.escapedquotes" title="shlex.shlex.escapedquotes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">escapedquotes</span></code></a> (e.g. <code class="docutils literal notranslate"><span class="pre">'&quot;'</span></code>) preserves the literal value
of all characters within the quotes, with the exception of the characters
mentioned in <a class="reference internal" href="#shlex.shlex.escape" title="shlex.shlex.escape"><code class="xref py py-attr docutils literal notranslate"><span class="pre">escape</span></code></a>.  The escape characters retain its
special meaning only when followed by the quote in use, or the escape
character itself. Otherwise the escape character will be considered a
normal character.</p></li>
<li><p>EOF is signaled with a <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> value;</p></li>
<li><p>Quoted empty strings (<code class="docutils literal notranslate"><span class="pre">''</span></code>) are allowed.</p></li>
</ul>
</section>
<section id="improved-compatibility-with-shells">
<span id="improved-shell-compatibility"></span><h2>Improved Compatibility with Shells<a class="headerlink" href="#improved-compatibility-with-shells" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>The <a class="reference internal" href="#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a> class provides compatibility with the parsing performed by
common Unix shells like <code class="docutils literal notranslate"><span class="pre">bash</span></code>, <code class="docutils literal notranslate"><span class="pre">dash</span></code>, and <code class="docutils literal notranslate"><span class="pre">sh</span></code>.  To take advantage of
this compatibility, specify the <code class="docutils literal notranslate"><span class="pre">punctuation_chars</span></code> argument in the
constructor.  This defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>, which preserves pre-3.6 behaviour.
However, if it is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, then parsing of the characters <code class="docutils literal notranslate"><span class="pre">();&lt;&gt;|&amp;</span></code>
is changed: any run of these characters is returned as a single token.  While
this is short of a full parser for shells (which would be out of scope for the
standard library, given the multiplicity of shells out there), it does allow
you to perform processing of command lines more easily than you could
otherwise.  To illustrate, you can see the difference in the following snippet:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="go"> &gt;&gt;&gt; import shlex</span>
<span class="go"> &gt;&gt;&gt; text = &quot;a &amp;&amp; b; c &amp;&amp; d || e; f &gt;&#39;abc&#39;; (def \&quot;ghi\&quot;)&quot;</span>
<span class="go"> &gt;&gt;&gt; s = shlex.shlex(text, posix=True)</span>
<span class="go"> &gt;&gt;&gt; s.whitespace_split = True</span>
<span class="go"> &gt;&gt;&gt; list(s)</span>
<span class="go"> [&#39;a&#39;, &#39;&amp;&amp;&#39;, &#39;b;&#39;, &#39;c&#39;, &#39;&amp;&amp;&#39;, &#39;d&#39;, &#39;||&#39;, &#39;e;&#39;, &#39;f&#39;, &#39;&gt;abc;&#39;, &#39;(def&#39;, &#39;ghi)&#39;]</span>
<span class="go"> &gt;&gt;&gt; s = shlex.shlex(text, posix=True, punctuation_chars=True)</span>
<span class="go"> &gt;&gt;&gt; s.whitespace_split = True</span>
<span class="go"> &gt;&gt;&gt; list(s)</span>
<span class="go"> [&#39;a&#39;, &#39;&amp;&amp;&#39;, &#39;b&#39;, &#39;;&#39;, &#39;c&#39;, &#39;&amp;&amp;&#39;, &#39;d&#39;, &#39;||&#39;, &#39;e&#39;, &#39;;&#39;, &#39;f&#39;, &#39;&gt;&#39;, &#39;abc&#39;, &#39;;&#39;,</span>
<span class="go"> &#39;(&#39;, &#39;def&#39;, &#39;ghi&#39;, &#39;)&#39;]</span>
</pre></div>
</div>
<p>Of course, tokens will be returned which are not valid for shells, and you’ll
need to implement your own error checks on the returned tokens.</p>
<p>Instead of passing <code class="docutils literal notranslate"><span class="pre">True</span></code> as the value for the punctuation_chars parameter,
you can pass a string with specific characters, which will be used to determine
which characters constitute punctuation. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">shlex</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">shlex</span><span class="o">.</span><span class="n">shlex</span><span class="p">(</span><span class="s2">&quot;a &amp;&amp; b || c&quot;</span><span class="p">,</span> <span class="n">punctuation_chars</span><span class="o">=</span><span class="s2">&quot;|&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="go">[&#39;a&#39;, &#39;&amp;&#39;, &#39;&amp;&#39;, &#39;b&#39;, &#39;||&#39;, &#39;c&#39;]</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When <code class="docutils literal notranslate"><span class="pre">punctuation_chars</span></code> is specified, the <a class="reference internal" href="#shlex.shlex.wordchars" title="shlex.shlex.wordchars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">wordchars</span></code></a>
attribute is augmented with the characters <code class="docutils literal notranslate"><span class="pre">~-./*?=</span></code>.  That is because these
characters can appear in file names (including wildcards) and command-line
arguments (e.g. <code class="docutils literal notranslate"><span class="pre">--color=auto</span></code>). Hence:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">shlex</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">shlex</span><span class="o">.</span><span class="n">shlex</span><span class="p">(</span><span class="s1">&#39;~/a &amp;&amp; b-c --color=auto || d *.py?&#39;</span><span class="p">,</span>
<span class="gp">... </span>                <span class="n">punctuation_chars</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="go">[&#39;~/a&#39;, &#39;&amp;&amp;&#39;, &#39;b-c&#39;, &#39;--color=auto&#39;, &#39;||&#39;, &#39;d&#39;, &#39;*.py?&#39;]</span>
</pre></div>
</div>
<p>However, to match the shell as closely as possible, it is recommended to
always use <code class="docutils literal notranslate"><span class="pre">posix</span></code> and <a class="reference internal" href="#shlex.shlex.whitespace_split" title="shlex.shlex.whitespace_split"><code class="xref py py-attr docutils literal notranslate"><span class="pre">whitespace_split</span></code></a> when using
<a class="reference internal" href="#shlex.shlex.punctuation_chars" title="shlex.shlex.punctuation_chars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">punctuation_chars</span></code></a>, which will negate
<a class="reference internal" href="#shlex.shlex.wordchars" title="shlex.shlex.wordchars"><code class="xref py py-attr docutils literal notranslate"><span class="pre">wordchars</span></code></a> entirely.</p>
</div>
<p>For best effect, <code class="docutils literal notranslate"><span class="pre">punctuation_chars</span></code> should be set in conjunction with
<code class="docutils literal notranslate"><span class="pre">posix=True</span></code>. (Note that <code class="docutils literal notranslate"><span class="pre">posix=False</span></code> is the default for
<a class="reference internal" href="#shlex.shlex" title="shlex.shlex"><code class="xref py py-class docutils literal notranslate"><span class="pre">shlex</span></code></a>.)</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code> — Simple lexical analysis</a><ul>
<li><a class="reference internal" href="#shlex-objects">shlex Objects</a></li>
<li><a class="reference internal" href="#parsing-rules">Parsing Rules</a></li>
<li><a class="reference internal" href="#improved-compatibility-with-shells">Improved Compatibility with Shells</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmd.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmd</span></code> — Support for line-oriented command interpreters</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tk.html"
                          title="next chapter">Graphical User Interfaces with Tk</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/shlex.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tk.html" title="Graphical User Interfaces with Tk"
             >next</a> |</li>
        <li class="right" >
          <a href="cmd.html" title="cmd — Support for line-oriented command interpreters"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="frameworks.html" >Program Frameworks</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code> — Simple lexical analysis</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>