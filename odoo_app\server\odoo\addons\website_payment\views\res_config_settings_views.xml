<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.website</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="website.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <block id="website_info_settings" position="after">
                <block title="Shop - Payment" id="div_website_payment">
                    <setting id="website_payment" string="Activate Payments" help="Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple Pay, etc. as well as recurring charges.">
                        <div class="content-group">
                            <div class="row" invisible="providers_state == 'other_than_paypal'">
                                <field name="providers_state" invisible="1"/>
                                <field name="is_stripe_supported_country" invisible="1"/>
                                <div class="oe_inline"
                                     invisible="not is_stripe_supported_country">
                                    <button string="Activate Stripe" name="action_activate_stripe" type="object" class="btn-primary"/>
                                </div>
                                <div class="oe_inline"
                                     title="Stripe Connect is not available in your country, please use another payment provider."
                                     invisible="is_stripe_supported_country">
                                    <button string="Activate Stripe" class="btn-primary" disabled=""/>
                                </div>
                                <button type="action"
                                        name="%(payment.action_payment_provider)d"
                                        string="View Alternatives"
                                        class="btn-link oe_inline"
                                        icon="oi-arrow-right"
                                />
                            </div>
                            <div invisible="providers_state != 'other_than_paypal'">
                                <button name="action_configure_first_provider"
                                        type="object"
                                        class="btn-primary col-auto"
                                >
                                    <field name="first_provider_label" nolabel="1" class="oe_inline"/>
                                </button>
                                <button type="action"
                                        name="%(payment.action_payment_provider)d"
                                        string="View other providers "
                                        class="btn-link col-auto"
                                        icon="oi-arrow-right"
                                />
                            </div>
                        </div>
                    </setting>
                </block>
            </block>
        </field>
    </record>
</odoo>
