<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">

    <!-- Message with scheduled notification -->
    <record id="message_demo_partner_2_0" model="mail.message">
        <field name="author_id" ref="base.partner_demo"/>
        <field name="body" type="html"><p>Hello! Could you send us your address?</p></field>
        <field name="date" eval="(DateTime.today() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:00')"/>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_2"/>
        <field name="message_type">email</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
    </record>
    <record id="message_demo_partner_1_5_notif_0" model="mail.message.schedule">
        <field name="mail_message_id" ref="message_demo_partner_2_0"/>
        <field name="scheduled_datetime" eval="(DateTime.today() + timedelta(days=2)).strftime('%Y-%m-%d %H:%M:00')"/>
    </record>

</data></odoo>
