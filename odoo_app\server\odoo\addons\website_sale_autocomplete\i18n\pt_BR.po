# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_autocomplete
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Create a Google Project and get a key"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Crie um Projeto Google e receba uma chave"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Enable billing on your Google Project"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Habilite o faturamento em seu Projeto Google"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid "API Key"
msgstr "Chave de API"

#. module: website_sale_autocomplete
#: model:ir.model,name:website_sale_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: website_sale_autocomplete
#: model:ir.model.fields,field_description:website_sale_autocomplete.field_res_config_settings__google_places_api_key
#: model:ir.model.fields,field_description:website_sale_autocomplete.field_website__google_places_api_key
msgid "Google Places API Key"
msgstr "Chave da API do Google Maps"

#. module: website_sale_autocomplete
#. odoo-javascript
#: code:addons/website_sale_autocomplete/static/src/xml/autocomplete.xml:0
#, python-format
msgid "Powered by Google"
msgstr "Desenvolvido por Google"

#. module: website_sale_autocomplete
#: model:ir.model,name:website_sale_autocomplete.model_website
msgid "Website"
msgstr "Site"
