# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <alexand<PERSON>@gnugr.org>, 2024
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON>ralamp<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid " %(subject)s (final)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid " %i duplicates have been ignored."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid ""
"\"Damien Roberts\" <<EMAIL>>\n"
"\"Rick Sanchez\" <<EMAIL>>\n"
"<EMAIL>"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_count
msgid "# Favorite Filters"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "$18"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Blacklist"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Bounce"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Opt-out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%(contact_name)s subscribed to the following mailing list(s)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%(contact_name)s unsubscribed from the following mailing list(s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "%Click (Total)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "%i Contacts have been imported."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (αντίγραφο)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_to_list.py:0
#, python-format
msgid "%s Mailing Contacts have been added. "
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "&amp;nbsp;&amp;nbsp;"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
#, python-format
msgid "(scheduled for %s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "100%"
msgstr "100%"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_reports
msgid "24H Stat Mailing Reports"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "25%"
msgstr "25%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "400px"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "50%"
msgstr "50%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "75%"
msgstr "75%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "800px"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid "<b>John DOE</b> • CEO of MyCompany"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">\n"
"                                        <i class=\"fa fa-envelope-o\"/> Contacts\n"
"                                    </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Blacklist</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Bounce</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Mailings</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Opt-Out</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid ""
"<br/>We want to take this opportunity to welcome you to our ever-growing community!\n"
"                <br/>Your platform is ready for work, it will help you reduce the costs of digital signatures, attract new customers and increase sales."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font class=\"text-o-color-1\">25 September 2022 - 4:30 PM</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font class=\"text-o-color-1\">26 September 2022 - 1:30 PM</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"<font class=\"text-o-color-2\"><span style=\"font-"
"weight:bolder;\">-20%</span></font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<font style=\"color: rgb(12 84 96);\">Don't write about products or services"
" here, write about solutions.</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"<font style=\"font-size: 12px;\">Add a caption to enhance the meaning of "
"this image.</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font style=\"font-size: 18px\">Event Two</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font style=\"font-size: 18px;\">Event One</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid "<font style=\"font-size: 48px;\">A Punchy Headline</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
msgid ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" invisible=\"not "
"is_blacklisted\" groups=\"base.group_user\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-bar-chart\"/> Compare Version"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-copy\"/> Create an Alternative"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-envelope\"/> Send this as winner"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<i class=\"fa fa-envelope\"/><span name=\"ab_test_auto\">\n"
"                                                    Send Winner Now\n"
"                                                </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"<i class=\"fa fa-exclamation-triangle text-danger\" aria-hidden=\"true\"/>\n"
"                        The sum of all percentages for this A/B campaign totals"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh me-1\"/> Replace Icon"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<i>Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services.</i>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" invisible=\"reply_to_mode == 'update' or mailing_model_name in ['mailing.contact', 'res.partner', 'mailing.list']\">\n"
"                                                    To track replies, this address must belong to this database.\n"
"                                                </small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-calendar-check-o me-2 small my-auto\" aria-label=\"Sent "
"date\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-half me-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-o me-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                            <span class=\"align-self-baseline\">Next Batch</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "<span class=\"o_stat_text\">Open Recipient</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Blacklist</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Bounce</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Mailings</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Opt-out</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "<span invisible=\"not mailing_on_mailing_list\">Mailing Contact</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"canceled_text\">emails have been canceled and will not be "
"sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"failed_text\">email(s) not sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"failed_text\">emails could not be sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"mailing_schedule_type_now_text\">This mailing will be sent as "
"soon as possible.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"next_departure_text\">This mailing is scheduled for </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"process_text\">emails are being processed.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"refresh_text\">This mailing will be sent as soon as "
"possible.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"scheduled_text\">email(s) scheduled for </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"scheduled_text\">emails are in queue and will be sent "
"soon.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"sent\">emails have been sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.digest_mail_main
msgid "<span style=\"color: #878d97;\">Turn off Mailing Reports</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<span style=\"color: rgb(12 84 96); font-size: 16px; font-weight: "
"bolder;\">Explain the benefits you offer</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-size: 11px\">user / month (billed annually)</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">12</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">45</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">8</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "<span style=\"font-weight: bolder;\">50,000+ companies</span> run Odoo."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight: bolder;\">DEFAULT</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "<span style=\"font-weight: bolder;\">GET $20 OFF</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight: bolder;\">PRO</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight:bolder\">24/7 Support</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span style=\"font-weight:bolder\">Advanced</span>\n"
"                                    features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight:bolder\">Fully customizable</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span style=\"font-weight:bolder\">Total</span>\n"
"                                    management"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Contacts</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<span>Design</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "<span>There wasn't enough recipients left for this mailing</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Valid Email Recipients</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "<span>​</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<strong class=\"d-block\" invisible=\"mailing_type == 'mail' or not ab_testing_enabled or state != 'done' or sent != 0 or failed != 0 or canceled != 0\">\n"
"                                <span name=\"ab_test_text\">There wasn't enough recipients given to this mailing. </span>\n"
"                            </strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<u>Refresh <i class=\"fa fa-refresh ms-1\"/></u>"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A campaign should be set when A/B test is enabled"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "A color block"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr ""

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_subscription_unique_contact_list
msgid ""
"A mailing contact cannot subscribe to the same mailing list multiple times."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "A sample of"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "A short description of this great feature."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "A small explanation of this great feature, in clear words."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "A unique value"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_mailing_id
msgid "A/B Campaign Winner Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "A/B Test"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_mailings_count
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_ab_testing_open_winner_mailing
msgid "A/B Test Winner"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_completed
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_description
msgid "A/B Testing Description"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid "A/B Testing percentage"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#, python-format
msgid "A/B Tests"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "A/B Tests to review"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B test option has not been enabled"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__active
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_mail_server__active_mailing_ids
msgid "Active mailing using this mail server"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_ids
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Δραστηριότητα Εξαίρεσης Διακόσμησης"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Activity Type Icon"
msgstr "Εικονίδιο Τύπου Δραστηριότητας"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#, python-format
msgid "Add"
msgstr "Προσθήκη"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_to_list
msgid "Add Contacts to Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Add Mailing Contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_to_list_action
msgid "Add Selected Contacts to a Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Add a great slogan."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add and Send Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Add to List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Add to Templates"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "Add to favorite filters"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Advanced"
msgstr "Για προχωρημένους"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Alert"
msgstr "Προσοχή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Bottom"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Center"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Left"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Middle"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Right"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Top"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Alignment"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid "All Rights Reserved"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid "Allow A/B Testing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__is_feedback
msgid "Allow Feedback"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Allow recipients to blacklist themselves"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid ""
"Allow the recipient to manage themselves their state in the blacklist via "
"the unsubscription page."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage themselves their state in the blacklist via "
"the unsubscription page. If the option is active, the 'Blacklist Me' button "
"is hidden on the unsubscription page. The 'come Back' button will always be "
"visible in any case to allow leads and partners to re-subscribe."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Image Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Amazing pages"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_feedback.xml:0
#, python-format
msgid "An error occurred. Please retry later or contact us."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "An error occurred. Please retry later."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "Another color block"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Another feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Apply changes"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Archive"
msgstr "Αρχειοθετήθηκαν"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__archive_src_lists
msgid "Archive source mailing lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Are you sure you want to unsubscribe from our mailing list?"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Are you sure you want to unsubscribe from the mailing list "
"\"%(unsubscribed_lists)s\"?"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid ""
"At least one of the mailing list you are trying to archive is used in an "
"ongoing mailing campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr "Επισύναψη αρχείου"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_attachment_count
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_attachment_count
msgid "Attachment Count"
msgstr "Αριθμός Συνημμένων"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__attachment_ids
msgid "Attachments"
msgstr "Συνημμένα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Auto"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Average"
msgstr "Μέσος όρος"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Bounced"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Clicked"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Delivered"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Opened"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Replied"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Background Color"
msgstr "Χρώμα Φόντου"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_on_mailing_list
msgid "Based on Mailing Lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "Basic features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "Basic management"
msgstr "Βασική διαχείριση"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Beautiful snippets"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Big Boxes"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Blacklist (%s)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid "Blacklist Option when Unsubscribing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Blacklisted"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mail_blacklist_mm_menu
msgid "Blacklisted Email Addresses"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Blocklist removal request from portal"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist removal request from portal of mailing %(mailing_link)s (document "
"%(record_link)s)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Blocklist request from portal"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist request from portal of mailing %(mailing_link)s (document "
"%(record_link)s)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist request from unsubscribe link of mailing %(mailing_link)s (direct "
"link usage)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist request from unsubscribe link of mailing %(mailing_link)s "
"(document %(record_link)s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Blockquote"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_arch
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Body"
msgstr "Κυρίως θέμα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Body Width"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_html
msgid "Body converted to be sent by mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Bold"
msgstr "Έντονη"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Books"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Border"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__message_bounce
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bounce
msgid "Bounce"
msgstr "Προώθηση"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Bounce (%)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Bounce happens when a mailing cannot be delivered (fake address, server "
"issues, ...). Check each record to see what went wrong."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__bounced
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__bounce
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Bounced"
msgstr "Προωθήθηκε"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Bounced (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Business Benefits on %(expected)i %(mailing_type)s Sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "Button Label"
msgstr "Ετικέτα Κουμπιού"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "By using the <b>Breadcrumb</b>, you can navigate back to the overview."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__calendar_date
msgid "Calendar Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Call to Action"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Campaign"
msgstr "Εκστρατεία"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
msgid "Campaign Stages"
msgstr "Στάδια Εκστρατείας"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_tag_menu
msgid "Campaign Tags"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_utm_campaigns
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
msgid "Campaigns"
msgstr "Εκστρατείες"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid ""
"Campaigns are the perfect tool to track results across multiple mailings."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Cancel"
msgstr "Ακύρωση"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__canceled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__canceled
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__cancel
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Canceled"
msgstr "Ακυρώθηκε"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing_test__email_to
msgid "Carriage-return-separated list of email addresses."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__preview
msgid ""
"Catchy preview sentence that encourages recipients to open this email.\n"
"In most inboxes, this is displayed next to the subject.\n"
"Keep it empty if you prefer the first characters of your email content to appear instead."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Center"
msgstr "Κέντρο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Centered Logo"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Change Icons"
msgstr "Αλλαγή Εικονιδίων"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Check how well your mailing is doing a day after it has been sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_reports
msgid "Check how well your mailing is doing a day after it has been sent."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our books"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our clothes"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our furniture"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Check the email address and click send."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Choose a date"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Choose this <b>theme</b>."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Choose your mailing subscriptions"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#, python-format
msgid "Click on the ⭐ next to the subject to save this mailing as a"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Click on this button to add this mailing to your templates."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Click on this paragraph to edit it."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__clicked
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Clicked"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Clicked (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Clicked On"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Clicks"
msgstr "Κλικς"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing_mobile_preview.xml:0
#, python-format
msgid "Close"
msgstr "Κλείσιμο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Clothes"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__color
msgid "Color Index"
msgstr "Χρωματισμός Ευρετήριου"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Columns"
msgstr "Στήλες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Come Back"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Come back once your mailing has been sent to track who opened your mailing."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__company_name
msgid "Company Name"
msgstr "Επωνυμία Εταιρίας"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Comparisons"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Configure Outgoing Mail Servers"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Congratulations, I love your first mailing. :)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_partner
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__contact_id
msgid "Contact"
msgstr "Επαφή"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__contact_list
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "Contact List"
msgstr "Λίστα Επαφών"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Contact Name"
msgstr "Όνομα Επαφής"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_import__contact_list
msgid "Contact list that will be imported, one contact per line"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Contact us"
msgstr "Επικοινωνήστε μαζί μας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__contact_ids
msgid "Contacts"
msgstr "Επαφές"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Could not retrieve URL: %s"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Μετρητής του αριθμού προωθήσεων emails για αυτή την επαφή"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__country_id
msgid "Country"
msgstr "Χώρα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Cover"
msgstr "Εξώφυλλο"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Create a Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Create a Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid "Create a mailing campaign"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Create a mailing contact"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Create a new mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Create an Alternative Version"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Creation Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
msgid "Creation Period"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Custom"
msgstr "Προσωποποιημένο"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dashed"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Date"
msgstr "Ημερομηνία"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__calendar_date
msgid "Date at which the mailing was or will be sent."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
msgid "Dedicated Server"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default"
msgstr "Προεπιλογή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default Reversed"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Default Server"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delete"
msgstr "Διαγραφή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Delete Blocks"
msgstr "Διαγραφή Πλαισίων"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__delivered
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__sent
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Delivered"
msgstr "Παραδόθηκαν"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Delivered (%)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delivered to"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/snippets.editor.js:0
#, python-format
msgid "Design Options"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Design a striking email, define recipients and track its results."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Design added to the %s Templates!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Design removed from the %s Templates!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__dest_list_id
msgid "Destination Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Discard"
msgstr "Απόρριψη"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Discount Offer"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Discover"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Discover all the features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Display Inline"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__res_id
msgid "Document ID"
msgstr "Κωδικός εγγράφου"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__model
msgid "Document model"
msgstr "Μοντέλου Εγγράφου"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_domain
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Domain"
msgstr "Τομέας"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
#, python-format
msgid "Domain field"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Don't forget to send your preferred version"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Don't worry, the mailing contact we created is an internal user."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dotted"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Double"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__draft
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__draft
msgid "Draft"
msgstr "Προσχέδιο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Duplicate"
msgstr "Δημιουργία Αντίγραφου"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_dup
msgid "Duplicated Email"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "ENDOFSUMMER20"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Edit Styles"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__email
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__mailing_type__mail
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_type__mail
#, python-format
msgid "Email"
msgstr "Email"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Email Blacklisted"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Email Content"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/res_users.py:0
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
#, python-format
msgid "Email Marketing"
msgstr "Email Marketing"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
#, python-format
msgid ""
"Email Marketing uses it as its default mail server to send mass mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr "Νήμα Email"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#, python-format
msgid "Email added to our blocklist"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Οδηγός Σύνταξης Email"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#, python-format
msgid "Email removed from our blocklist"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Emails"
msgstr "Email"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_trace_ids
msgid "Emails Statistics"
msgstr "Στατιστικά Emails"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Engagement on %(expected)i %(mailing_type)s Sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__error
msgid "Error"
msgstr "Σφάλμα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Event"
msgstr "Συμβάν"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Event heading"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__error
msgid "Exception"
msgstr "Εξαίρεση"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Blacklisted Emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Exclude Me"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Opt Out"
msgstr "Εξαίρεση 'Δεν συμφωνεί'"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__expected
msgid "Expected"
msgstr "Αναμένεται"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Extended Filters..."
msgstr "Εκτεταμένα Φίλτρα..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Facebook"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__failed
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Failed"
msgstr "Αποτυχία"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_reason
msgid "Failure reason"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_type
msgid "Failure type"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__favorite
msgid "Favorite"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__favorite_date
msgid "Favorite Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_id
msgid "Favorite Filter"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_filter_action
#: model:ir.ui.menu,name:mass_mailing.mailing_filter_menu_action
msgid "Favorite Filters"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_domain
msgid "Favorite filter domain"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature One"
msgstr "Χαρακτηριστικό 1"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Three"
msgstr "Χαρακτηριστικό 3"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Two"
msgstr "Χαρακτηριστικό 2"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Features"
msgstr "Χαρακτηριστικά"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Features Grid"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Feedback from %(author_name)s"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_domain
msgid "Filter Domain"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__name
msgid "Filter Name"
msgstr "Όνομα Φίλτρου"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "Filter templates"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "Filters saved by me"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "First Feature"
msgstr "Πρώτο χαρακτηριστικό"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "First feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "First list of Features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Fit content"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_follower_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_partner_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Font Family"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Γραμματοσειρά awesome  π.χ. fa-tasks"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footer Center"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footer Left"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"software, marketing, and customer experience strategies."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__email_from
msgid "From"
msgstr "Από"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Full"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Fullscreen"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Furniture"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
msgid ""
"Get your inside sales (CRM) fully integrated with online sales (eCommerce), "
"in-store sales (Point of Sale) and marketplaces like eBay and Amazon."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Great Value"
msgstr "Μεγάλη Αξία"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Group By..."
msgstr "Ομαδοποίηση κατά..."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__has_message
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__has_message
msgid "Has Message"
msgstr "Έχει Μήνυμα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Headers"
msgstr "Κεφαλίδες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 1"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 2"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 3"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Height"
msgstr "Ύψος"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Here's your coupon code - but hurry! Ends 9/28"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__opened_ratio
msgid "Highest Open Rate"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__replied_ratio
msgid "Highest Reply Rate"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_1
msgid "I changed my mind"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_0
msgid "I never subscribed to this list"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_2
msgid "I receive too many emails from this list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__id
msgid "ID"
msgstr "Κωδικός"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the"
" ID is needed for several action and controllers."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_icon
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Icon"
msgstr "Εικονίδιο"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Εικονίδιο που υποδεικνύει μια δραστηριότητα εξαίρεσης."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid ""
"If checked, recipients will be mailed only once for the whole campaign. This"
" lets you send different mailings to randomly selected recipients and test "
"the effectiveness of the mailings, without causing duplicate messages."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Αν επιλεγεί, κάποια μηνύματα έχουν σφάλμα παράδοσης."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid ""
"If set, a mass mailing will be created so that you can track its results in "
"the Email Marketing app."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Ignored"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Image - Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Image Text Image"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Images"
msgstr "Εικόνες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Import"
msgstr "Εισαγωγή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "Import Contacts"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_import_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#, python-format
msgid "Import Mailing Contacts"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_contact.py:0
#, python-format
msgid "Import Template for Mailing List Contacts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Import contacts in"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__in_queue
msgid "In Queue"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "Inline field"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Inner Content"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Instagram"
msgstr "Instagram"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_ab_test_sent
msgid "Is Ab Test Sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Is Body Empty"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_is_follower
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_is_winner_mailing
msgid "Is the Winner of its Campaign"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Italic"
msgstr "Πλάγια"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Items"
msgstr "Είδη"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Join us and make your company a better place."
msgstr "Έλα μαζί μας και κάντε την εταιρεία σας ένα καλύτερο μέρος."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__kpi_mail_required
msgid "KPI mail required"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__keep_archives
msgid "Keep Archives"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "LOGIN"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__lang
msgid "Language"
msgstr "Γλώσσα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Large"
msgstr "Μεγάλο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Last Feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Last State Update"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Left"
msgstr "Αριστερά"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Left Logo"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Left Text"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Let's try the Email Marketing app."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
#: model:ir.ui.menu,name:mass_mailing.link_tracker_menu_mass_mailing
msgid "Link Tracker"
msgstr "Ιχνηλάτης URL"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "Link Tracker Click"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Link Trackers will measure how many times each link is clicked as well as "
"the proportion of %s who clicked at least once in your mailing."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "LinkedIn"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Links"
msgstr "Σύνδεσμοι"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_ids
msgid "Links click"
msgstr "Κλικ συνδέσμων"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__mailing_list_ids
msgid "Lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "London, United Kingdom"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__mailing_type__mail
msgid "Mail"
msgstr "Αλληλογραφία"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_blacklist
msgid "Mail Blacklist"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr "Κυρίως κείμενο Μηνύματος"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Mail Debug"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Mail ID"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid "Mail ID (tech)"
msgstr "Κωδικός Αλληλογραφίας(τεχνικό)"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_ab_testing_ir_actions_server
msgid "Mail Marketing: A/B Testing"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_queue_ir_actions_server
msgid "Mail Marketing: Process queue"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_mail_server
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_mail_server_id
msgid "Mail Server"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_available
msgid "Mail Server Available"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mailing_trace_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
msgid "Mail Statistics"
msgstr "Στατιστικά Αλληλογραφίας"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Mail Traces"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailing"
msgstr "Αλληλογραφία"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__campaign
msgid "Mailing Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid "Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact
msgid "Mailing Contact"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_import
msgid "Mailing Contact Import"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_model__is_mailing_enabled
msgid "Mailing Enabled"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_filter
msgid "Mailing Favorite Filters"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "Mailing Filters"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__mailing_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__list_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Mailing List"
msgstr "Λίστα Αλληλογραφίας"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Mailing List #"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Mailing List Contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_subscription
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
msgid "Mailing List Subscription"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Mailing List Subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__src_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__contact_list_ids
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_mailing_list_menu
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Mailing Lists"
msgstr "Λίστες Αλληλογραφίας"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid "Mailing Reports Turned Off"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid ""
"Mailing Reports have been turned off for all users. <br/>\n"
"                                If needed, they can be turned back on from the"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace
msgid "Mailing Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_subscription_optout
msgid "Mailing Subscription Reason"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Mailing Subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr "Τεστ Αλληλογραφίας"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_action
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
msgid "Mailing Traces"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type_description
msgid "Mailing Type Description"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Mailing addresses incorrect: %s"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"business contact directory."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Mailing filters"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_action_mail
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mailings"
msgstr "Αλληλογραφία"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings that are assigned to me"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr "Διαχείριση Εκστρατειών Μαζικής Αλληλογραφίας"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Manage Subscriptions"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Manage mass mailing campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__manual
msgid "Manual"
msgstr "Χειροκίνητα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Marketing"
msgstr "Marketing"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Marketing Content"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Masonry"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__name
msgid "Mass Mail"
msgstr "Μαζικό Ταχυδρομείο"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__mass_mailing_id
#: model:ir.ui.menu,name:mass_mailing.mailing_mailing_menu_technical
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Mass Mailing"
msgstr "Ομαδική Αλληλογραφία"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
#, python-format
msgid "Mass Mailing \"%s\""
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_report_action_mail
#: model:ir.ui.menu,name:mass_mailing.mailing_menu_report_mailing
msgid "Mass Mailing Analysis"
msgstr "Ανάλυση Μαζικής Αλληλογραφίας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Mass Mailing Campaign"
msgstr "Εκστρατεία Μαζικής Αλληλογραφίας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Mass Mailing Statistics"
msgstr "Στατιστικά Μαζικής Αλληλογραφίας"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_trace_report_action_mail
msgid ""
"Mass Mailing Statistics allows you to check different mailing related information\n"
"    like number of bounced mails, opened mails, replied mails. You can sort out\n"
"    your analysis by different groups to get accurate grained analysis."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_ids
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Μαζικές Αλληλογραφίες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Media List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Media heading"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__medium_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Medium"
msgstr "Μέσο"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "Membership updated"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_list_merge_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge"
msgstr "Συνένωση"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list_merge
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge Mass Mailing List"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__merge_options
msgid "Merge Option"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__new
msgid "Merge into a new mailing list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__existing
msgid "Merge into an existing mailing list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error
msgid "Message Delivery error"
msgstr "Σφάλμα παράδοσης μηνύματος"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__message_id
msgid "Message-ID"
msgstr "Αναγνωριστικό - Μηνύματος"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid ""
"Michael Fletcher<br/>\n"
"                   <span style=\"font-size: 12px; font-weight: bolder;\">Customer Service</span>"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_missing
msgid "Missing email address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_from_missing
msgid "Missing from address"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "Mobile Preview"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
#, python-format
msgid "Model field"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_model
msgid "Models"
msgstr "Μοντέλα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "More"
msgstr "Περισσότερα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "More Details"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "More Info"
msgstr "Περισσότερες Πληροφορίες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Mosaic"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Η Προθεσμία της Δραστηριότητάς Μου"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "My Company"
msgstr "Η Εταιρία μου"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "My Filters"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "My Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Name"
msgstr "Περιγραφή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Name / Email"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__new_list_name
msgid "New Mailing List Name"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "New contacts imported"
msgstr ""

#. module: mass_mailing
#: model:utm.campaign,title:mass_mailing.mass_mail_campaign_1
msgid "Newsletter"
msgstr "Newsletter"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Επόμενο Γεγονός Δραστηριότητας στο Ημερολόγιο"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure_is_past
msgid "Next Departure Is Past"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s address bounced yet!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s clicked your mailing yet!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s opened your mailing yet!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s received your mailing yet!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s replied to your mailing yet!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid ""
"No contacts were imported. All email addresses are already in the mailing "
"list."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "No customization"
msgstr "Χωρίς προσαρμογή"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mail_mail_statistics_mailing
msgid "No data yet!"
msgstr "Δεν υπάρχουν δεδομένα ακόμη!"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_action_report_optout
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_optout_action
msgid "No data yet."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No mailing campaign has been found"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"No mailing for this A/B testing campaign has been sent yet! Send one first "
"and try again later."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid ""
"No need to import mailing lists, you can send mailings to contacts saved in "
"other Odoo apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_filter_action
msgid "No saved filter yet!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "No support"
msgstr "Χωρίς υποστήριξη"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "No valid email address found."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "None"
msgstr "Κανένα"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__email
msgid "Normalized email address"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Not subscribed"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_blacklisted
msgid "Number of Blacklisted"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicks_ratio
msgid "Number of Clicks"
msgstr "Αριθμός Κλικς"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count
msgid "Number of Contacts"
msgstr "Αριθμός Επαφών"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_email
msgid "Number of Emails"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_count
msgid "Number of Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_opt_out
msgid "Number of Opted-out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Number of Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid "Number of bounced email."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of errors"
msgstr "Αριθμός σφαλμάτων"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Αριθμός μηνυμάτων που απαιτούν ενέργεια"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Αριθμός μηνυμάτων με σφάλμα παράδοσης"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Numbers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "ON YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "OPENED (%i)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
msgid "Omnichannel sales"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Once the best version is identified, we will send the best one to the "
"remaining recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"Once you send these emails, they'll be making a grand entrance in all the "
"inboxes, creating quite the buzz!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Open Date"
msgstr "Ημερ. Ανοιχτή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Open Recipient"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__opened
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__open
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Opened"
msgstr "Ανοιχτό"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Opened (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__open_datetime
msgid "Opened On"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Ανοιχτή Αναλογία "

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__opt_out
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out
msgid "Opt Out"
msgstr "Δεν Συμφωνεί"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__opt_out
msgid ""
"Opt out flag for a specific mailing list. This field should not be used in a"
" view without a unique and active mailing list context."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Opt-out (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_optout
msgid "Opted Out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Opted-out"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mailing_menu_report_subscribe_reason
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Optout"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Optout Reason"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_subscription_optout_action
#: model:ir.ui.menu,name:mass_mailing.mailing_subscription_optout_menu
msgid "Optout Reasons"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_4
msgid "Other"
msgstr "Άλλο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "Our References"
msgstr "Οι αναφορές μας"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__outgoing
msgid "Outgoing"
msgstr "Εξερχόμενα"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr "Εξερχόμενα μηνύματα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Padding ↕"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Padding ⭤"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__pending
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__pending
msgid "Pending"
msgstr "Εκρεμμής"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_blacklisted
msgid "Percentage of Blacklisted"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_bounce
msgid "Percentage of Bouncing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_opt_out
msgid "Percentage of Opted-out"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be chosen "
"randomly."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Pick a dedicated outgoing mail server for your mass mailings"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Pick the <b>email subject</b>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Picture"
msgstr "Εικόνα"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Plain Text"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_portal_subscription_feedback.js:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Please let us know why you updated your subscription."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_portal_subscription_feedback.js:0
#, python-format
msgid "Please let us know why you want to be in our block list."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
#, python-format
msgid "Please provide a name for the filter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Post heading"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to
msgid "Preferred Reply-To Address"
msgstr "Προτεινόμενη Διεύθυνση Απάντησης "

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__preview
msgid "Preview"
msgstr "Προεπισκόπηση"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Preview Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Primary Buttons"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__process
msgid "Process"
msgstr "Διεργασία"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__processing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__process
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Processing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Progress bar"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Promo Code"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating
msgid "Quality"
msgstr "Ποιότητα"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "REPLIED (%i)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Rating"
msgstr "Βαθμολόγηση"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__rating_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__rating_ids
msgid "Ratings"
msgstr "Αξιολογήσεις"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "Read More"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Ready for take-off!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_blacklist__opt_out_reason_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out_reason_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_search
msgid "Reason"
msgstr "Αιτία"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr "Παραλήφθηκε"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__received_ratio
msgid "Received Ratio"
msgstr "Λειφθήσα Αναλογία"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
msgid "Recipient"
msgstr "Παραλήπτης"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Recipient Address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__update
msgid "Recipient Followers"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Recipients"
msgstr "Αποδέκτες"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_model_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Μοντέλο Παραληπτών"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_model_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_name
msgid "Recipients Model Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_real
msgid "Recipients Real Model"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "Redeem Discount!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "References"
msgstr "Παραπομπές"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "Register Now"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Regular"
msgstr "Σύνηθες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Reload a favorite filter"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "Remove from Favorites"
msgstr "Κατάργηση από τα Αγαπημένα"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#, python-format
msgid "Remove from Templates"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__render_model
msgid "Rendering Model"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__replied
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__reply
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Replied"
msgstr "Απαντημένη"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Replied (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__reply_datetime
msgid "Replied On"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Ποσοστό Απαντημένων"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Reply Date"
msgstr "Ημερομηνία Απάντησης"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to
msgid "Reply To"
msgstr "Απάντηση Σε"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to_mode
msgid "Reply-To Mode"
msgstr "Κατάσταση Απάντησης"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reporting"
msgstr "Αναφορές"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__user_id
msgid "Responsible"
msgstr "Υπεύθυνοι"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Restore"
msgstr "Επαναφορά"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr "Επανάληψη"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Right"
msgstr "Δεξιά"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Round Corners"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Σφάλμα παράδοσης SMS"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Sample Icons"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_test
msgid "Sample Mail Wizard"
msgstr "Οδηγός Δείγματος Αλληλογραφίας"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "Save as Favorite Filter"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__create_uid
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Saved by"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Schedule"
msgstr "Προγραμματισμός"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Scheduled"
msgstr "Προγραμματισμένη"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled_date
msgid "Scheduled Date"
msgstr "Προγραμματισμένη Ημερομηνία"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Scheduled On"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Scheduled Period"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure
msgid "Scheduled date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__schedule_date
msgid "Scheduled for"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.next_departure.value}"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.schedule_date.value}"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Score"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_model.py:0
#, python-format
msgid ""
"Searching Mailing Enabled models supports only direct search using '='' or "
"'!='."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Second Feature"
msgstr "Δεύτερο χαρακτηριστικό"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Second feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Second list of Features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Secondary Buttons"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Select mailing lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists..."
msgstr "Επιλέξτε λίστες αλληλογραφίας..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists:"
msgstr "Επιλέξτε λίστες αλληλογραφίας:"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Send"
msgstr "Αποστολή"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__email_from
msgid "Send From"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Send Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_compose_form_mass_mailing
msgid "Send Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr "Αποστολή ενός Δείγματος Αλληλογραφίας "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Send a report to the mailing responsible one day after the mailing has been "
"sent."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a sample mailing for testing purpose to the address below."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__now
msgid "Send now"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Send on"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send test"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Send to all"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__sending
msgid "Sending"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__done
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__pending
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__done
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Sent"
msgstr "Εστάλη"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent By"
msgstr "Αποστολή Από"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent_date
msgid "Sent Date"
msgstr "Ημερ. Αποστολής"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Sent Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__sent_datetime
msgid "Sent On"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Period"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Sent on #{record.sent_date.value}"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_feedback.xml:0
#, python-format
msgid "Sent. Thanks you for your feedback!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Separator"
msgstr "Διαχωριστής"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Separators"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid "Settings Menu."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__is_public
msgid "Show In Preferences"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Showcase"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Signature"
msgstr "Υπογραφή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Since the date and time for this test has not been scheduled, don't forget "
"to manually send your preferred version."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Size"
msgstr "Μέγεθος"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Small"
msgstr "Μικρό"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Solid"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Source"
msgstr "Πηγή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__new
msgid "Specified Email Address"
msgstr "Συγκεκριμένη Διεύθυνση Email"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Start From Scratch"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Start by creating your first <b>Mailing</b>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_trace_ids
msgid "Statistics"
msgstr "Στατιστικά"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__state
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_status
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "Κατάσταση"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Stretch to Equal Height"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__subject
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Subject"
msgstr "Θέμα"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Subscribed"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Subscription Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__subscription_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__subscription_ids
msgid "Subscription Information"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_subscription_action_report_optout
msgid "Subscriptions"
msgstr "Συνδρομές"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Successfully Unsubscribed"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Successfully unsubscribed!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__tag_ids
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Tags"
msgstr "Ετικέτες"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Take Future Schedule Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Team"
msgstr "Ομάδα"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_available
msgid ""
"Technical field used to know if the user has activated the outgoing mail "
"server option in the settings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Tell what's the value for the customer for this feature."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Template"
msgstr "Πρότυπο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Test"
msgstr "Τεστ"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Test Mailing"
msgstr "Δοκιμαστική Αλληλογραφία"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing could not be sent to %s:"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing successfully sent to %s"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Test this mailing by sending a copy to yourself."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__test
msgid "Tested"
msgstr "Ελεγμένο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Text"
msgstr "Κείμενο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Text - Image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Text Highlight"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Text Image Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Thank you for joining us!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid "That way, Odoo evolves much faster than any other solution."
msgstr ""

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_mailing_percentage_valid
msgid "The A/B Testing Percentage needs to be between 0 and 100%"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr "Η επαφή έχει επιλέξει να μην λαμβάνει emails πλέον από αυτήν τη λίστα"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_3
msgid "The content of these emails is not relevant to me"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_filter.py:0
#, python-format
msgid "The filter domain is not valid for this recipients."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_list__is_public
msgid ""
"The mailing list can be accessible by recipients in the subscription "
"management page to allow them to update their preferences."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                    business experts to build hundreds of apps in just a few years."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of "
"developers and business experts to build hundreds of apps in just a few "
"years."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"The saved filter targets different recipients and is incompatible with this "
"mailing."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"The winner has already been sent. Use <b>Compare Version</b> to get an "
"overview of this A/B testing campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Then on"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "There are no recipients selected."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Third Feature"
msgstr "Τρίτο Χαρακτηριστικό"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "This"
msgstr "Αυτό"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"This email from can not be used with this mail server.\n"
"Your emails might be marked as spam on the mail clients."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mail_thread.py:0
#, python-format
msgid ""
"This email has been automatically added in blocklist because of too much "
"bounced."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid ""
"This is useful if your marketing campaigns are composed of several emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "This mailing has no selected design (yet!)."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This tool is advised if your marketing campaign is composed of several "
"emails."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to_mode
msgid ""
"Thread: replies go to target document. Email: replies are routed to a given "
"email."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "TikTok"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__title_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Title"
msgstr "Τίτλος"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the campaign should not have been completed."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the same campaign should be used by the mailings"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To track how many replies this mailing gets, make sure its reply-to address "
"belongs to this database."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__total
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Total"
msgstr "Σύνολο"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "Total <br/>Contacts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Total Bounces"
msgstr ""

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_trace_check_res_id_is_set
msgid "Traces have to be linked to records with a not null res_id."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Tracking"
msgstr "Παρακολούθηση"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Try different variations in the campaign to compare their"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Twitter"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_type
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__mailing_type
msgid "Type"
msgstr "Τύπος"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Τύπος της καταγεγραμμένης δραστηριότητας εξαίρεσης."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__campaign_id
msgid "UTM Campaign"
msgstr "Καμπάνια UTM"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_medium
msgid "UTM Medium"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__medium_id
msgid "UTM Medium: delivery method (email, sms, ...)"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_source
msgid "UTM Source"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Underline"
msgstr "Υπογράμμιση"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__unknown
msgid "Unknown error"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_basic_template
#, python-format
msgid "Unsubscribe"
msgstr "Κατάργηση εγγραφής"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out_datetime
msgid "Unsubscription Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Upload a file"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                    of our fully integrated apps."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Use a specific mail server in priority. Otherwise Odoo relies on the first "
"outgoing mail server available (based on their sequencing) as it does for "
"normal mails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Use alternative versions to be able to select the winner."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Use now"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Useful options"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_users
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "Χρήστης"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Valid Email Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vert. Alignment"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vertical Alignment"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_view
msgid "View Online"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Wait until your mailing has been sent to check how many recipients you "
"managed to reach."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Want to import country, company name and more?"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning Message"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning message displayed in the mailing form view"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available. To welcome you"
" back please accept this 20% discount on you next purchase by clicking the "
"button."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "We are in good company."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Welcome Message"
msgstr "Μήνυμα Καλωσορίσματος"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_schedule_date_action
msgid "When do you want to send your mailing?"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__favorite_date
msgid "When this mailing was added in the favorites"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_ir_model__is_mailing_enabled
msgid ""
"Whether this model supports marketing mailing capabilities (notably email "
"and SMS)."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_filter_action
msgid ""
"While designing the mailing, you can define the rules to filter recipients.\n"
"                To save the same criteria for future use, you can add it to the favorite list\n"
"                by clicking on <i class=\"fa fa-floppy-o text-warning\"></i> icon next to \"Recipients\"."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Width"
msgstr "Πλάτος"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                    It provides top notch usability that scales across all apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"With strong technical foundations, Odoo's framework is unique. It provides "
"<span style=\"font-weight: bolder;\">top notch usability that scales across "
"all apps</span>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid ""
"Write or paste email addresses in the field below.\n"
"                    Each line will be imported as a mailing list contact."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Write what the customer would like to know, not what you want to show."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "You are no longer part of our mailing list(s)."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"You are no longer part of our services and will not be contacted again."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "You are no longer part of the %(mailing_name)s mailing list."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "You are no longer part of the %(mailing_names)s mailing list."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "You are not subscribed to any of our mailing list."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_list_merge.py:0
#, python-format
msgid "You can only apply this action from Mailing Lists."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/utm_medium.py:0
#, python-format
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following mailings in Mass Mailing:\n"
"%(mailing_names)s"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following mailings in Mass Mailing:\n"
"%(mailing_names)s"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"You don't need to import your mailing lists, you can easily\n"
"                send emails<br> to any contact saved in other Odoo apps."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "You have to much emails, please upload a file."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "You may also be interested in"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_contact.py:0
#, python-format
msgid ""
"You should give either list_ids, either subscription_ids to create new "
"contacts."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "You will not hear from us anymore."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid ""
"You will not receive any news from those mailing lists you are a member of:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_title
msgid "Your Title"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "Your email is currently"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Your email is currently <strong>in our block list</strong>."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "e.g. \"VIP Customers\""
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. Check it out before it's too late!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "e.g. Consumer Newsletter"
msgstr "π.χ. Newsletter Καταναλωτή"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "e.g. John Smith"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. New Sale on all T-shirts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"<EMAIL>\n"
"<EMAIL>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "having the"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "in our block list"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"is the winner of the A/B testing campaign and has been sent to all remaining"
" recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"of all potential recipients.<br/>\n"
"                        <b class=\"text-danger\">Some of the mailings will not be sent</b>, as only 1 email will be sent for each unique recipient in this campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "on"
msgstr "στις"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_schedule_date
msgid "schedule a mailing"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#, python-format
msgid "template"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "the"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "to the remaining recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this version."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this version.<br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Active"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Inactive"
msgstr ""
