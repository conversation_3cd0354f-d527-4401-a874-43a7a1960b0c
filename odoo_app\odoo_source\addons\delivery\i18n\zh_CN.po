# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>EN <<EMAIL>>, 2023
# <PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:27+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid " (Estimated Cost: %s )"
msgstr "（预估客户运费:%s )"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "（合计:"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"fa fa-arrow-right me-1\"/>Get rate"
msgstr "<i class=\"fa fa-arrow-right me-1\"/>获取速率"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                    现在购买 Odoo企业版获取更多的服务商。\n"
"                </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_warning_text\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"o_warning_text\">测试</span>\n"
"                                    <span class=\"o_stat_text\">环境</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-danger\">No debug</span>"
msgstr "<span class=\"text-danger\">没有调试</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">调试请求</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"text-success\">生产</span>\n"
"                                    <span class=\"o_stat_text\">环境</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span>‎- 重量（估计）：‎</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span> - ‎重量‎: </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>‎运输重量：‎</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Carrier:</strong>"
msgstr "<strong>承运商:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>HS Code</strong>"
msgstr "<strong>HS编码</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_report_saleorder_document
msgid "<strong>Shipping Description:</strong>"
msgstr "<strong>运输说明：</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
msgid "<strong>Shipping Weight: </strong>"
msgstr "<strong>运输重量：</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                    <br/>"
msgstr ""
"<strong>运输重量:</strong>\n"
"                <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid ""
"<strong>Total Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>‎总重量：‎</strong>\n"
"                       <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Tracking Number:</strong>"
msgstr "<strong>运单/面单号:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
msgid "<strong>Weight: </strong>"
msgstr "<strong>重量</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid ""
"<strong>Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>重量:</strong>\n"
"                <br/>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__carrier_description
msgid ""
"A description of the delivery method that you want to communicate to your "
"customers on the Sales Order and sales confirmation email.E.g. instructions "
"for customers to follow."
msgstr "您希望在销售订单和销售确认电子邮件中与客户沟通的交货方式的描述，例如，客户要遵循的说明。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "确认发货时触发"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "启用"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "添加"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "Add a shipping method"
msgstr "添加送货方式"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "设置物流"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "金额"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr "免费发货的订单金额，按公司本位币计算"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "已归档"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "可用的承运商"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "按设置的规则"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "散货重量"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "可以生成退货"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "取消"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid ""
"Cancelling a delivery may not be undoable. Are you sure you want to "
"continue?"
msgstr "取消配送可能无法撤消。是否确实要继续？"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.stock_move_line_view_search_delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "承运商"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "承运商代码"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__carrier_description
msgid "Carrier Description"
msgstr "运营商描述"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__carrier_name
msgid "Carrier Name"
msgstr "承运商名称"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.stock_move_line_view_search_delivery
msgid "Carrier name"
msgstr "承运商名称"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "公司"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "条件"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "成本"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "国家"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_uid
msgid "Created by"
msgstr "创建人"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_date
msgid "Created on"
msgstr "创建时间"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
msgid "Currency"
msgstr "币种"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "客户"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "调试日志"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "销售订单中使用的默认交货方式。"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "定义新交货方式"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "发货承运商"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "承运商选择向导"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "运输成本"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "发货消息"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
msgid "Delivery Method"
msgstr "交货方式"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "邮包选择向导"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "快递包裹类型"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "显示给客户的运费"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "运费计算规则"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "运费对应的产品"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_rating_success
msgid "Delivery Rating Success"
msgstr "获取运费成功"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "已定物流商"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_zip_prefix
msgid "Delivery Zip Prefix"
msgstr "交货邮编前缀"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "发货成本应重新计算"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid ""
"Delivery zip prefixes are assigned to delivery carriers to restrict\n"
"                which zips it is available to."
msgstr ""
"将递送拉链前缀分配给递送承运人以限制\n"
"                它可用于哪个拉链。"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Description"
msgstr "说明"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.vpicktree_view_tree
msgid "Destination"
msgstr "目的地"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr "目的地有效性"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "目的地国家"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "决定显示顺序"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "丢弃"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "显示运单追踪链接"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"                UPS Express, UPS Standard) with a set of pricing rules attached\n"
"                to each method."
msgstr ""
"每一个承运商(例如UPS)能有几种不同的运输方式(例如UPS快运, UPS 标准)\n"
"并且每种方式都相应的一套定价规则"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Environment"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "Error: this delivery method is not available for this address."
msgstr "错误:此交货方式不适用于这个地址。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of the shipping will be updated on the SO after the delivery."
msgstr ""
"预估客户运费:将向客户开具估计的运费。\n"
"实际客户运费:客户将开具结算单的实际开具结算单，实际客户运费将在交付后的SO上更新。"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "预估运费"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Exception occurred with respect to carrier on the transfer"
msgstr "承运商在转运时发生异常"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Exception:"
msgstr "例外："

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr "如果您打算根据发货的实际数量填写账单的话，就填写这一字段"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr "填写此表，您可以根据您的客户的发货地址进行过滤。"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "固定价格"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid "Free Shipping"
msgstr "免费送货"

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Free delivery charges"
msgstr "免运输费"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "如果订单超过此金额则免费"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "生成退货标签"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "获取费率"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "获取费率并创建运单/面单"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "分组"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:delivery.field_product_template__hs_code
#: model_terms:ir.ui.view,arch_db:delivery.product_template_hs_code
msgid "HS Code"
msgstr "HS编码"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr "如果订单总额（不包括运费）高于或等于此值，客户可享受免费发货服务"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "增加更多物流商"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__shipping_insurance
msgid "Insurance Percentage"
msgstr "保险百分比"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "集成级别"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "开票信息"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "开票策略"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "是退回拣货"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "是交货"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_local_delivery
#: model:product.template,name:delivery.product_product_local_delivery_product_template
msgid "Local Delivery"
msgstr "本地配送"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "记录请求以便于调试"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid "Manage delivery zip prefixes"
msgstr "管理递送邮政编码前缀"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Manual actions might be needed."
msgstr "可能需要手动操作。"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "毛利"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "毛利不得低于 -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "利润率"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "最大值"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "名称"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "New Providers"
msgstr "新服务商"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "无承运商集成"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "No price rule matching this order; delivery cost cannot be computed."
msgstr "没有与此订单匹配的价格规则；运送费用无法计算。"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "操作员"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "订单"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_product__country_of_origin
#: model:ir.model.fields,field_description:delivery.field_product_template__country_of_origin
msgid "Origin of Goods"
msgstr "货物原产地"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "包裹"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Package Details"
msgstr "包裹明细"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid "Package too heavy!"
msgstr "包裹太重！"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr "包裹"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "拣货"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__name
msgid "Prefix"
msgstr "前缀"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_zip_prefix_name_uniq
msgid "Prefix already exists!"
msgstr "前缀已存在！"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__zip_prefix_ids
msgid ""
"Prefixes of zip codes that this carrier applies to. Note that regular "
"expressions can be used to support countries with varying zip code lengths, "
"i.e. '$' can be added to end of prefix to match the exact zip (e.g. '100$' "
"will only match '100' and not '1000')"
msgstr ""
"此承运商适用的邮编前缀。请注意，正则表达式可用于支持不同国家/地区的不同邮编长度，例如，可将“$”添加至邮编前缀的末尾，以匹配具体邮编（例如，“100$”将仅匹配“100”，不匹配“1000”）"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "价格"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "价格规则"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "价格"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "价格规则"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "打印退货标签"

#. module: delivery
#: model:ir.model,name:delivery.model_product_template
msgid "Product"
msgstr "产品"

#. module: delivery
#: model:ir.model,name:delivery.model_product_category
msgid "Product Category"
msgstr "产品类别"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "产品移动(移库明细)"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "产品数量"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "物流商"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "数量"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "实际运费"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "退货标签"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "退货标签可从客户门户访问"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "退回拣货"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_product__country_of_origin
#: model:ir.model.fields,help:delivery.field_product_template__country_of_origin
msgid ""
"Rules of origin determine where goods originate, i.e. not where they have been shipped from, but where they have been produced or manufactured.\n"
"As such, the ‘origin’ is the 'economic nationality' of goods traded in commerce."
msgstr ""
"原产地规则确定货物的来源，即不是货物的运自何处，而是货物的生产或制造地。\n"
"因此，“原产地”是商业贸易商品的“经济国籍”。"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "基础标价"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "实际标价"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "销售订单"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "保存"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "发至物流商"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "序号"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "服务产品"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr "如果您的凭证已通过生产认证，请设置为True。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s<br/>Cost: %(price).2f %(currency)s"
msgstr "运输送至承运商%(carrier_name)s‎跟踪号码‎%(ref)s<br/>‎成本：‎%(price).2f%(currency)s"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "显示的客户运费"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "发货信息"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Shipping Method"
msgstr "送货方式"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.ui.menu,name:delivery.menu_action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "送货方式"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "发货重量"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr "运输重量："

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__shipping_insurance
msgid ""
"Shipping insurance is a service which may reimburse senders whose parcels "
"are lost, stolen, and/or damaged in transit."
msgstr "运输保险是一项可以报销包裹在运输过程中丢失，被盗和/或损坏的发件人的服务。"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Shipping method details to be included at bottom sales orders and their "
"confirmation emails. E.g. Instructions for customers to follow."
msgstr "送货方式详细信息将包含在底部销售订单及其确认电子邮件中。例如，客户要遵循的说明。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_product__hs_code
#: model:ir.model.fields,help:delivery.field_product_template__hs_code
msgid ""
"Standardized code for international shipping and goods declaration. At the "
"moment, only used for FedEx and USPS shipping providers."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "省/州"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move
msgid "Stock Move"
msgstr "库存移动"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_package_type
msgid "Stock package type"
msgstr "库存包裹类型"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__supports_shipping_insurance
msgid "Supports Shipping Insurance"
msgstr "支持航运保险"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight_uom_rounding
msgid "Technical field indicating weight's number of decimal places"
msgstr "指示权重小数位数的技术字段"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight_is_kg
msgid "Technical field indicating whether weight uom is kg or not (i.e. lb)"
msgstr "指示重量是否为公斤（即磅）的技术字段"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO 国家代码使用两个字符。\n"
" 您可以使用此字段进行快速搜索。"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "邮政"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr "无法创建包裹，因为拣货单上产品的重量总计是0.0%s"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr "客户可以从客户门户下载退货标签。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "退货标签在发货时自动生成。"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_shipping_insurance_is_percentage
msgid "The shipping insurance must be a percentage between 0 and 100."
msgstr "运输保险必须是介于 0 和 100 之间的百分比。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "由于订单金额超过 %.2f，提供免费送货。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "The shipping price will be set once the delivery is done."
msgstr "发货完成后将设置客户运费。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr "您的包裹的重量大于这个包裹类型所授权的最大重量。请选择另一个包裹类型。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "There is no matching delivery rule."
msgstr "没有匹配的运费规则。"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"                according to your settings; on the sales order (based on the\n"
"                quotation) or the invoice (based on the delivery orders)."
msgstr ""
"这些运费模板能根据您的设定，自动的计算显示给用户的运输价格；\n"
"显示在销售订单中（基于报价）或者结算单中（基于发货单）"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "这个百分比将被添加到物流价格。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "包裹中包含的所有产品的总重量。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr "‎包裹和产品的总重量不在包裹中。未指定运输重量的包裹将默认为其产品的总重量。这是用于计算运费的权重。‎"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "‎不包括包裹中的产品的总重量。‎"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "包裹的总重量。"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "拣货中产品的总重量。"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "运单/面单的追踪链接"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "追踪"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "追踪参考"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "追踪 URL"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
msgid "Tracking:"
msgstr "追踪:"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_picking
msgid "Transfer"
msgstr "调拨"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "更新"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
#, python-format
msgid "Update shipping cost"
msgstr "更新发货成本"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "可变"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "变量计算器"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "体积"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "重量"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "重量 * 体积"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "物流重量"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "物流重量"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "重量单位标签"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr "重量："

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"已结算的订单，不支持修改运费!\n"
"\n"
"已经处理以下交货明细（产品、结算数量和价格）：\n"
"\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/product_category.py:0
#, python-format
msgid ""
"You cannot delete the deliveries product category as it is used on the "
"delivery carriers products."
msgstr "您不能删除交货产品类别，因为该类别用于交货承运商产品。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot pack products into the same package when they have different "
"carriers (i.e. check that all of their transfers have a carrier assigned and"
" are using the same carrier)."
msgstr "若产品的承运商不同，则不能将产品打包到同一个包裹中（即检查所有转运产品是否都指定了承运商，并使用同一个承运商）。"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr "您有多个运单/面单追踪链接，它们在聊天中可用。"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr "物流商网站没有找到此订单的链接。请检查您设置的交货方式。"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_zip_prefix_list
#: model:ir.ui.menu,name:delivery.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "邮编前缀"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_prefix_ids
msgid "Zip Prefixes"
msgstr "邮编前缀"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDShipping Weight:"
msgstr "^A0N,44,33^货运重量："

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDWeight:"
msgstr "^A0N,44,33^重量："

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.label_package_template_view_delivery
msgid "^FO310,200"
msgstr "^FO310,200"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.label_package_template_view_delivery
msgid "^FS"
msgstr "^FS"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "例如，UPS　快递"
