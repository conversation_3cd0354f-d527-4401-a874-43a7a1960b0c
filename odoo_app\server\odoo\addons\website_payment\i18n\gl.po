# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_payment
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:50+0000\n"
"PO-Revision-Date: 2015-09-08 10:40+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Galician (http://www.transifex.com/odoo/odoo-9/language/gl/)\n"
"Language: gl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "<option value=\"\">Country...</option>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Payments"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Stripe"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Amount"
msgstr "Cantidade"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Amount ("
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/res_config_settings.py:0
#, python-format
msgid "Configure %s"
msgstr ""

#. module: website_payment
#: model:ir.actions.server,name:website_payment.action_stripe_connect_account
msgid "Connect to Stripe"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#: model:ir.model,name:website_payment.model_res_country
#, python-format
msgid "Country"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Country\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Descriptions"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation Button"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Email\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__first_provider_label
msgid "First Provider Label"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__is_stripe_supported_country
#: model:ir.model.fields,field_description:website_payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Maximum"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Minimum"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Name"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Name\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr ""

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__none
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr ""

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__other_than_paypal
msgid "Other than Paypal"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Payment Details"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr ""

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__paypal_only
msgid "Paypal Only"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__providers_state
msgid "Providers State"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Server Error"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Shop - Payment"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Step"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Stripe Connect is not available in your country, please use another payment provider."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple Pay, etc. as well as recurring charges."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Validation Error"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View Alternatives"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View other providers"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "We could not obtain payment fees."
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_provider__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
msgid "Website: Donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Write us a comment"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Your comment"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr ""
