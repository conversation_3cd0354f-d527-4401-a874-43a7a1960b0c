# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_adyen
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-14 12:49+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"Language: fo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid ""
"<strong>Warning:</strong> To capture the amount manually, you also need to set\n"
"                    the Capture Delay to manual on your Adyen account settings."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "A request was sent to void the transaction with reference %s (%s)."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_key
msgid "API Key"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields.selection,name:payment_adyen.selection__payment_provider__code__adyen
msgid "Adyen"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "An error occurred during the processing of your payment. Please try again."
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_checkout_api_url
msgid "Checkout API URL"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_client_key
msgid "Client Key"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__code
msgid "Code"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "HMAC Key"
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect Payment Details"
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid "Learn More"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "Merchant Account"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Please verify your payment details."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment state: %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing payment state."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received refund data with missing transaction values"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_recurring_api_url
msgid "Recurring API URL"
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "Shopper Reference"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_key
msgid "The API key of the webservice user"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "The HMAC key of the webhook"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_checkout_api_url
msgid "The base URL for the Checkout API endpoints"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_recurring_api_url
msgid "The base URL for the Recurring API endpoints"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The capture of the transaction with reference %s failed."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The capture of the transaction with reference %s has been requested (%s)."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_client_key
msgid "The client key of the webservice user"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "The code of the merchant account to use with this provider"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "The unique reference of the partner owning this token"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The void of the transaction with reference %s failed."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/utils.py:0
#, python-format
msgid "Unrecognized field %s in street format."
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Your payment was refused. Please try again."
msgstr ""
