# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: digest
#: model:mail.template,subject:digest.digest_mail_template
msgid "${'%s: %s' % (ctx.get('user', user).company_id.name, object.name)}"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_0
msgid "${', '.join(users.mapped('name'))} signed up. Say hello in the"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_0
msgid ""
"% set users = object.env['res.users'].search([('share', '=' , False)], limit=10, order='id desc')\n"
"    % set channel_id = object.env.ref('mail.channel_all_employees').id\n"
"    <strong style=\"font-size: 16px;\">Did you know...?</strong>"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_1
msgid ""
"<strong style=\"font-size: 16px;\">Get things done with activities</strong>"
msgstr ""

#. module: digest
#: model:mail.template,body_html:digest.digest_mail_template
msgid ""
"<table style=\"width: 100%; border-spacing: 0; font-family: Helvetica,Arial,Verdana,sans-serif;\">\n"
"    <tr>\n"
"        <td align=\"center\" valign=\"top\" style=\"border-collapse: collapse; padding: 0\">\n"
"            % set user = ctx.get('user', user)\n"
"            % set company = user.company_id\n"
"            % set data = object.compute_kpis(company, user)\n"
"            % set tips = object.compute_tips(company, user)\n"
"            % set kpi_actions = object.compute_kpis_actions(company, user)\n"
"            % set kpis = data.yesterday.keys()\n"
"            <table style=\"width: 100%; max-width: 600px; border-spacing: 0; border: 1px solid #e7e7e7; border-bottom: none; color: #6e7172; line-height: 23px; text-align: left;\">\n"
"                <tr>\n"
"                    <td style=\"border-collapse: collapse; padding: 10px 20px; text-align: left;\">\n"
"                        <strong style=\"color: #000000; font-size: 22px; line-height: 32px;\">${object.name}</strong>\n"
"                        <div style=\"color: #000000; font-size: 15px;\">${datetime.date.today().strftime('%B %d, %Y')}</div>\n"
"                    </td>\n"
"                    <td style=\"text-align: right; padding: 10px 20px\">\n"
"                        <img style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" src=\"/logo.png?company=${company.id}\"/>\n"
"                        <div style=\"padding-top: 5px;\">\n"
"                            <a href=\"/web/login\" style=\"background-color: #875A7B; margin-left: 5px; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">Connect</a>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align: center;\">\n"
"                    <hr width=\"95%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 14px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"            % for kpi in kpis:\n"
"                <table style=\"border-spacing: 0; width: 100%; max-width: 600px;\">\n"
"                    <tr>\n"
"                        <td style=\"border-collapse: collapse; background-color: #ffffff; border-left: 1px solid #e7e7e7; border-right: 1px solid #e7e7e7; line-height: 21px; padding: 0 20px 10px 20px; text-align: left;\"><br/>\n"
"                            <span style=\"color: #3d466e; font-size: 18px; font-weight: 500; line-height: 23px;\">${object.fields_get()[kpi]['string']}</span>\n"
"                            %if kpi in kpi_actions:\n"
"                                <span style=\"float: right;\">\n"
"                                    <a href=\"/web#action=${kpi_actions[kpi]}\">View more</a>\n"
"                                </span>\n"
"                            %endif\n"
"                        </td>\n"
"                    </tr>\n"
"                    <tr>\n"
"                        <td style=\"border-collapse: collapse; margin: 0; padding:0;\">\n"
"                            <table style=\"width: 100%; border-spacing: 0; background-color: #f9f9f9; border: 1px solid #e7e7e7; border-top: none;\">\n"
"                                <tr>\n"
"                                    <td style=\"border-collapse: collapse; margin: 0; padding: 0; display: block; border-top: 2px solid #56b3b5;\">\n"
"                                        <table style=\"width: 100%; max-width: 199px; border-spacing: 0;\">\n"
"                                            <tr>\n"
"                                                <td style=\"border-collapse: collapse; padding: 20px; text-align: center;\">\n"
"                                                    <span style=\"color: #56b3b5; font-size: 35px; font-weight: bold; text-decoration: none; line-height: 36px;\">${data['yesterday'][kpi][kpi]}</span><br/>\n"
"                                                    <span style=\"color: #888888; display: inline-block; font-size: 12px; line-height: 18px; text-transform: uppercase;\">Yesterday</span>\n"
"                                                    % if data['yesterday'][kpi]['margin'] != 0.0:\n"
"                                                        <span style=\"color: #888888; display: block; font-size: 12px; line-height: 18px; text-transform: uppercase;\">\n"
"                                                            % if data['yesterday'][kpi]['margin'] is greaterthan(0.0):\n"
"                                                                <span style=\"color: #0bbc22;\">▲</span>${\"%.2f\" % data['yesterday'][kpi]['margin']} %\n"
"                                                            % endif\n"
"                                                            % if data['yesterday'][kpi]['margin'] is lessthan(0.0):\n"
"                                                                <span style=\"color: #ff0000;\">▼</span>${\"%.2f\" % data['yesterday'][kpi]['margin']} %\n"
"                                                            % endif\n"
"                                                        </span>\n"
"                                                    % endif\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                    <td style=\"border-collapse: collapse; margin: 0; padding: 0; border-top: 2px solid #9a5b82;\">\n"
"                                        <table style=\"width: 100%; max-width: 199px; border-spacing: 0; margin: 0; padding: 0;\">\n"
"                                            <tr>\n"
"                                                <td style=\"border-collapse: collapse; padding: 20px; text-align: center;\">\n"
"                                                    <span style=\"color: #9a5b82; font-size: 35px; font-weight: bold; text-decoration: none; line-height: 36px;\">${data['lastweek'][kpi][kpi]}</span><br/>\n"
"                                                    <span style=\"color: #888888; display: inline-block; font-size: 12px; line-height: 18px; text-transform: uppercase;\">Last 7 Days</span>\n"
"                                                    % if data['lastweek'][kpi]['margin'] != 0.0:\n"
"                                                        <span style=\"color: #888888; display: block; font-size: 12px; line-height: 18px; text-transform: uppercase;\">\n"
"                                                            % if data['lastweek'][kpi]['margin'] is greaterthan(0.0):\n"
"                                                                <span style=\"color: #0bbc22;\">▲</span>${\"%.2f\" % data['lastweek'][kpi]['margin']} %\n"
"                                                            % endif\n"
"                                                            % if data['lastweek'][kpi]['margin'] is lessthan(0.0):\n"
"                                                                <span style=\"color: #ff0000;\">▼</span>${\"%.2f\" % data['lastweek'][kpi]['margin']} %\n"
"                                                            %endif\n"
"                                                        </span>\n"
"                                                    %endif\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                    <td style=\"border-collapse: collapse; margin: 0; padding: 15px; border-top: 2px solid #56b3b5;\">\n"
"                                        <table style=\"width: 100%; max-width: 199px; border-spacing: 0; margin: 0; padding: 0;\">\n"
"                                            <tr>\n"
"                                                <td style=\"border-collapse: collapse; margin: 0; text-align: center;\">\n"
"                                                    <span style=\"color: #56b3b5; font-size: 35px; font-weight: bold; text-decoration: none; line-height: 36px\">${data['lastmonth'][kpi][kpi]}</span><br/>\n"
"                                                    <span style=\"color: #888888; display: inline-block; font-size: 12px; line-height: 18px; text-transform: uppercase;\">Last 30 Days</span>\n"
"                                                    % if data['lastmonth'][kpi]['margin'] != 0.0:\n"
"                                                        <span style=\"color: #888888; display: block; font-size: 12px; line-height: 18px; text-transform: uppercase;\">\n"
"                                                             % if data['lastmonth'][kpi]['margin'] is greaterthan(0.0):\n"
"                                                                <span style=\"color: #0bbc22;\">▲</span>${\"%.2f\" % data['lastmonth'][kpi]['margin']} %\n"
"                                                            % endif\n"
"                                                            % if data['lastmonth'][kpi]['margin'] is lessthan(0.0):\n"
"                                                                <span style=\"color: #ff0000;\">▼</span>${\"%.2f\" % data['lastmonth'][kpi]['margin']} %\n"
"                                                            %endif\n"
"                                                        </span>\n"
"                                                    %endif\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            % endfor\n"
"            % if tips:\n"
"                <table style=\"width: 100%; max-width: 600px; margin-top: 5px; border: 1px solid #e7e7e7;\">\n"
"                    <tr>\n"
"                        <td style=\"border-collapse: collapse; background-color: #ffffff; line-height: 21px; padding: 0px 20px 20px;\"><br/>\n"
"                            <div style=\"color: #3d466e; line-height: 23px;\">${tips | safe}</div>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            % endif\n"
"            <table style=\"width: 100%; max-width: 600px; margin-top: 5px; border: 1px solid #e7e7e7;\">\n"
"                <tr>\n"
"                    <td style=\"border-collapse: collapse; background-color: #ffffff; line-height: 21px; padding: 0 20px 10px 20px; text-align: center;\"><br/>\n"
"                        <div style=\"color: #3d466e; font-size: 16px; font-weight: 600; line-height: 23px;\">Run your business from anywhere with Odoo Mobile.</div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"padding-bottom:20px;\">\n"
"                        <div style=\"text-align: center;\"><a href=\"https://play.google.com/store/apps/details?id=com.odoo.mobile\" target=\"_blank\"><img src=\"/digest/static/src/img/google_play.png\" style=\"display: inline-block; height: 30px; margin-left: auto; margin-right: 12px;\"/></a><a href=\"https://itunes.apple.com/us/app/odoo/id1272543640\" target=\"_blank\"><img src=\"/digest/static/src/img/app_store.png\" style=\"display: inline-block; height: 30px; margin-left: 12px; margin-right: auto;\"/></a>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"            <table style=\"margin-top: 5px; border: 1px solid #e7e7e7; font-size: 15px; width: 100%; max-width: 600px;\">\n"
"                <tr>\n"
"                    <td style=\"border-collapse: collapse; margin: 0; padding: 10px 20px;\">\n"
"                        % if user.has_group('base.group_system'):\n"
"                            <div style=\"margin-top: 20px;\"> \n"
"                                Want to customize this email?\n"
"                                <a href=\"/web#view_type=form&amp;model=digest.digest&amp;id=${object.id}\" target=\"_blank\" style=\"color: #875A7B;\">Choose the metrics you care about</a>\n"
"                            </div>\n"
"                            <br/>\n"
"                        % endif\n"
"                        <p style=\"font-size: 11px; margin-top: 10px;\">\n"
"                            <strong>\n"
"                                Sent by\n"
"                                <a href=\"https://www.odoo.com\" style=\"text-decoration: none; color: #875A7B;\">Odoo</a> - <a href=\"/web#view_type=form&amp;model=digest.digest&amp;id=${object.id}\" target=\"_blank\" style=\"color: #888888;\">Unsubscribe</a>\n"
"                            </strong>\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
msgid "Activated"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"Create or edit the mail template: you may get computed KPI's value using "
"these fields:"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate for everyone"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
msgid "Deactivated"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr ""

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.cron,name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_digest_tip
msgid "Digest Tips"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__template_id
msgid "Email Template"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "How to customize your digest?"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "In order to build your customized digest, follow these steps:"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
msgid "Name"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Send Date"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Select your KPIs in the KPI's tab."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Subscribe"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_2
msgid "To reach your full potential with Odoo, have a look at our"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Unsubscribe me"
msgstr ""

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "Users"
msgstr ""

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr ""

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Weekly Stats in Odoo"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_0
msgid ""
"You can ping colleagues by tagging them in your messages using \"@\". They "
"will be instantly notified.<br>"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_1
msgid ""
"You don't have any activity scheduled. Use activities on any business "
"document to schedule meetings, calls and todos."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "You may want to add new computed fields with Odoo Studio:"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_2
msgid "and discover new tools to work better and faster."
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_2
msgid "apps videos"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_mail_0
msgid "company's discussion channel."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"first create a boolean field called\n"
"                                                <code>kpi_myfield</code>\n"
"                                                and display it in the KPI's tab;"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"then create a computed field called\n"
"                                                <code>kpi_myfield_value</code>\n"
"                                                that will compute your customized KPI."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"you must create 2 fields on the\n"
"                                                <code>digest</code>\n"
"                                                object:"
msgstr ""
