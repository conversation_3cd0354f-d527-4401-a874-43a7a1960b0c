# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_test
# 
# Translators:
# <PERSON>, 2018
# se<PERSON> huang <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2016-08-05 12:55+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2019\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid ""
"<br/>\n"
"                        <strong>Description:</strong>"
msgstr ""
"<br/>\n"
"                        <strong>描述:</strong>"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "<strong>Name:</strong>"
msgstr "<strong>名稱:</strong>"

#. module: account_test
#: model:ir.model,name:account_test.model_report_account_test_report_accounttest
msgid "Account Test Report"
msgstr "帳戶測試報告"

#. module: account_test
#: model:ir.model,name:account_test.model_accounting_assert_test
msgid "Accounting Assert Test"
msgstr "會計試算測試"

#. module: account_test
#: model:ir.actions.act_window,name:account_test.action_accounting_assert
#: model:ir.actions.report,name:account_test.account_assert_test_report
#: model:ir.ui.menu,name:account_test.menu_action_license
msgid "Accounting Tests"
msgstr "會計測試"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "Accouting tests on"
msgstr "會計測試"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__active
msgid "Active"
msgstr "有效"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_03
msgid "Check if movement lines are balanced and have the same date and period"
msgstr "檢查憑證行是否平衡並且有相同的時間和日期"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_07
msgid ""
"Check on bank statement that the Closing Balance = Starting Balance + sum of"
" statement lines"
msgstr "在銀行對帳單檢查，期末餘額 = 期初餘額 + 本期發生額"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_06
msgid "Check that paid/reconciled invoices are not in 'Open' state"
msgstr "檢查支付/已調節發票並不在'開啟'狀態"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05_2
msgid ""
"Check that reconciled account moves, that define Payable and Receivable "
"accounts, are belonging to reconciled invoices"
msgstr "檢查已調節的會計憑證，屬於已調節發票的應付科目和應收科目"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05
msgid ""
"Check that reconciled invoice for Sales/Purchases has reconciled entries for"
" Payable and Receivable Accounts"
msgstr "檢查已收和已付科目中已調節的分錄相關的銷售/採購發票"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_01
msgid "Check the balance: Debit sum = Credit sum"
msgstr "檢查是否平衡：借方合計=貸方合計"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Code Help"
msgstr "代碼幫助"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid ""
"Code should always set a variable named `result` with the result of your test, that can be a list or\n"
"a dictionary. If `result` is an empty list, it means that the test was succesful. Otherwise it will\n"
"try to translate and print what is inside `result`.\n"
"\n"
"If the result of your test is a dictionary, you can set a variable named `column_order` to choose in\n"
"what order you want to print `result`'s content.\n"
"\n"
"Should you need them, you can also use the following variables into your code:\n"
"    * cr: cursor to the database\n"
"    * uid: ID of the current user\n"
"\n"
"In any ways, the code must be legal python statements with correct indentation (if needed).\n"
"\n"
"Example: \n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"
msgstr ""
"代碼應該總是設​置一個名為\"result\"變量來保存您的測試結果，這可以是列表或字典。 如果\"result\"變量是一個空列表，這意味著測試是成功的。 否則，它會嘗試翻譯和列印\"result\"變量中的內容.\n"
"\n"
"如果輸出的結果在列表或字典中您可以在`result`的內容中設定一個名為`column_order`的變量。\n"
"\n"
"若您需要它們，您同樣可以使用以列變量至您的代碼中：\n"
"    * cr: cursor to the database\n"
"    * uid: ID of the current user\n"
"\n"
"時刻留意的事是,所編寫的代碼均需要在python標準語法範圍內 (若您需要的話)。\n"
"\n"
"例如:：\n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"

#. module: account_test
#: model_terms:ir.actions.act_window,help:account_test.action_accounting_assert
msgid "Create a new accounting test"
msgstr "創建一個新的會計測試"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_uid
msgid "Created by"
msgstr "創建者"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_date
msgid "Created on"
msgstr "創建時間"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Description"
msgstr "描述"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__display_name
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Expression"
msgstr "表達式"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__id
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__id
msgid "ID"
msgstr "ID"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test____last_update
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest____last_update
msgid "Last Modified on"
msgstr "最後修改時間"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_uid
msgid "Last Updated by"
msgstr "最後更新人"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_date
msgid "Last Updated on"
msgstr "最後更新時間"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Python Code"
msgstr "Python 代碼"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__code_exec
msgid "Python code"
msgstr "Python代碼"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__sequence
msgid "Sequence"
msgstr "序列"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_01
msgid "Test 1: General balance"
msgstr "測試 1: 總帳平衡"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_03
msgid "Test 3: Movement lines"
msgstr "測試 3: 憑證行"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05
msgid ""
"Test 5.1 : Payable and Receivable accountant lines of reconciled invoices"
msgstr "測試 5.1 : 已調節發票的應收及應付分錄"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05_2
msgid "Test 5.2 : Reconcilied invoices and Payable/Receivable accounts"
msgstr "測試 5.2  : 已核銷的發票和應收/應付科目"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_06
msgid "Test 6 : Invoices status"
msgstr "測試6  : 發票狀態"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_07
msgid "Test 7 : Closing balance on bank statements"
msgstr "測試 7：在銀行對帳單上關帳餘額"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__desc
msgid "Test Description"
msgstr "測試描述"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__name
msgid "Test Name"
msgstr "測試項"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_tree
msgid "Tests"
msgstr "測試"

#. module: account_test
#: code:addons/account_test/report/report_account_test.py:53
#, python-format
msgid "The test was passed successfully"
msgstr "測試通過"
