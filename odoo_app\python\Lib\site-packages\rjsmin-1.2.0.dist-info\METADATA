Metadata-Version: 2.1
Name: rjsmin
Version: 1.2.0
Summary: Javascript Minifier
Home-page: http://opensource.perlig.de/rjsmin/
Author: <PERSON>
Author-email: <EMAIL>
License: Apache License, Version 2.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: C
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: Jython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Text Processing
Classifier: Topic :: Text Processing :: Filters
Classifier: Topic :: Utilities
License-File: LICENSE

=====================
 Javascript Minifier
=====================

rJSmin is a javascript minifier written in python.

The minifier is based on the semantics of `jsmin.c by Douglas Crockford`_\.

The module is a re-implementation aiming for speed, so it can be used at
runtime (rather than during a preprocessing step). Usually it produces the
same results as the original ``jsmin.c``. It differs in the following ways:

- there is no error detection: unterminated string, regex and comment
  literals are treated as regular javascript code and minified as such.
- Control characters inside string and regex literals are left untouched; they
  are not converted to spaces (nor to \n)
- Newline characters are not allowed inside string and regex literals, except
  for line continuations in string literals (ECMA-5).
- "return /regex/" is recognized correctly.
- More characters are allowed before regexes.
- Line terminators after regex literals are handled more sensibly
- "+ +" and "- -" sequences are not collapsed to '++' or '--'
- Newlines before ! operators are removed more sensibly
- (Unnested) template literals are supported (ECMA-6)
- Comments starting with an exclamation mark (``!``) can be kept optionally
- rJSmin does not handle streams, but only complete strings. (However, the
  module provides a "streamy" interface).

Since most parts of the logic are handled by the regex engine it's way faster
than the original python port of ``jsmin.c`` by Baruch Even. The speed factor
varies between about 6 and 55 depending on input and python version (it gets
faster the more compressed the input already is).  Compared to the
speed-refactored python port by Dave St.Germain the performance gain is less
dramatic but still between 3 and 50 (for huge inputs)). See the
docs/BENCHMARKS file for details.

rjsmin.c is a reimplementation of rjsmin.py in C and speeds it up even more.

Supported python versions are 2.7 and 3.6+.

.. _jsmin.c by Douglas Crockford: http://www.crockford.com/javascript/jsmin.c


Copyright and License
~~~~~~~~~~~~~~~~~~~~~

Copyright 2011 - 2021
André Malo or his licensors, as applicable.

The whole package (except for the files in the bench/ directory) is
distributed under the Apache License Version 2.0. You'll find a copy in the
root directory of the distribution or online at:
<http://www.apache.org/licenses/LICENSE-2.0>.


Bugs
~~~~

No bugs, of course. ;-)
But if you've found one or have an idea how to improve rjsmin, feel free
to send a pull request on `github <https://github.com/ndparker/rjsmin>`_
or send a mail to <<EMAIL>>.


Author Information
~~~~~~~~~~~~~~~~~~

André "nd" Malo <nd perlig.de>
GPG: 0x029C942244325167


    If God intended people to be naked, they would be born that way.
    -- Oscar Wilde

.. vim:tw=72 syntax=rest
