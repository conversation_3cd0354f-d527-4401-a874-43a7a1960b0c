# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__code
msgid "Code"
msgstr "Код"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_version
msgid "Interface Version"
msgstr "Версия интерфейса"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_merchant_id
msgid "Merchant ID"
msgstr "ID продавца"

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Не найдено ни одной транзакции, соответствующей ссылке %s."

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_provider
msgid "Payment Provider"
msgstr "Поставщик платежей"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "платеж"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_prod_url
msgid "Production URL"
msgstr "URL-производства"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_secret
msgid "SIPS Secret Key"
msgstr "Секретный ключ SIPS"

#. module: payment_sips
#: model_terms:ir.ui.view,arch_db:payment_sips.payment_provider_form
msgid "Secret Key"
msgstr "Секретный ключ"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_key_version
msgid "Secret Key Version"
msgstr "Версия секретного ключа"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_provider__code__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_test_url
msgid "Test URL"
msgstr "URL тест"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__sips_merchant_id
msgid "The ID solely used to identify the merchant account with Sips"
msgstr ""
"Идентификатор, используемый исключительно для идентификации торгового счета "
"с помощью Sips"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Технический код данного провайдера платежей."

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Unrecognized response received from the payment provider."
msgstr "Нераспознанный ответ, полученный от поставщика платежей."
