# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_portal
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Servisi RAM d.o.o. <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid " Copy"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "(Disable two-factor authentication)"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        Two-factor authentication not enabled"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Two-factor authentication enabled\n"
"                    </span>"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Added On</strong>"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Trusted Device</strong>"
msgstr ""

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Copied !"
msgstr "Kopirano !"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Enable two-factor authentication"
msgstr ""

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Operation failed for unknown reason."
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Revoke All"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Two-factor authentication"
msgstr ""

#. module: auth_totp_portal
#: model:ir.model,name:auth_totp_portal.model_res_users
msgid "User"
msgstr "Korisnik"
