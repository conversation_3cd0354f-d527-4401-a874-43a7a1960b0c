<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="importlib.metadata – Accessing package metadata" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/importlib.metadata.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/importlib/metadata/__init__.py importlib.metadata is a library that provides access to the metadata of an installed Distribution Package, such as its entry points or its top-level ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/importlib/metadata/__init__.py importlib.metadata is a library that provides access to the metadata of an installed Distribution Package, such as its entry points or its top-level ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>importlib.metadata – Accessing package metadata &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="The initialization of the sys.path module search path" href="sys_path_init.html" />
    <link rel="prev" title="importlib.resources.abc – Abstract base classes for resources" href="importlib.resources.abc.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/importlib.metadata.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#functional-api">Functional API</a><ul>
<li><a class="reference internal" href="#entry-points">Entry points</a></li>
<li><a class="reference internal" href="#distribution-metadata">Distribution metadata</a></li>
<li><a class="reference internal" href="#distribution-versions">Distribution versions</a></li>
<li><a class="reference internal" href="#distribution-files">Distribution files</a></li>
<li><a class="reference internal" href="#distribution-requirements">Distribution requirements</a></li>
<li><a class="reference internal" href="#mapping-import-to-distribution-packages">Mapping import to distribution packages</a></li>
</ul>
</li>
<li><a class="reference internal" href="#distributions">Distributions</a></li>
<li><a class="reference internal" href="#distribution-discovery">Distribution Discovery</a></li>
<li><a class="reference internal" href="#extending-the-search-algorithm">Extending the search algorithm</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.resources.abc.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code> – Abstract base classes for resources</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sys_path_init.html"
                          title="next chapter">The initialization of the <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code> module search path</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.metadata.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sys_path_init.html" title="The initialization of the sys.path module search path"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="importlib.resources.abc.html" title="importlib.resources.abc – Abstract base classes for resources"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-importlib.metadata">
<span id="importlib-metadata-accessing-package-metadata"></span><span id="using"></span><h1><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata<a class="headerlink" href="#module-importlib.metadata" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> is no longer provisional.</p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/metadata/__init__.py">Lib/importlib/metadata/__init__.py</a></p>
<p><code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> is a library that provides access to
the metadata of an installed <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a>,
such as its entry points
or its top-level names (<a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Import-Package">Import Package</a>s, modules, if any).
Built in part on Python’s import system, this library
intends to replace similar functionality in the <a class="reference external" href="https://setuptools.readthedocs.io/en/latest/pkg_resources.html#entry-points">entry point
API</a> and <a class="reference external" href="https://setuptools.readthedocs.io/en/latest/pkg_resources.html#metadata-api">metadata API</a> of <code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code>.  Along with
<a class="reference internal" href="importlib.resources.html#module-importlib.resources" title="importlib.resources: Package resource reading, opening, and access"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code></a>,
this package can eliminate the need to use the older and less efficient
<code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code> package.</p>
<p><code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> operates on third-party <em>distribution packages</em>
installed into Python’s <code class="docutils literal notranslate"><span class="pre">site-packages</span></code> directory via tools such as
<a class="reference external" href="https://pypi.org/project/pip/">pip</a>.
Specifically, it works with distributions with discoverable
<code class="docutils literal notranslate"><span class="pre">dist-info</span></code> or <code class="docutils literal notranslate"><span class="pre">egg-info</span></code> directories,
and metadata defined by the <a class="reference external" href="https://packaging.python.org/en/latest/specifications/core-metadata/#core-metadata">Core metadata specifications</a>.</p>
<div class="admonition important">
<p class="admonition-title">Important</p>
<p>These are <em>not</em> necessarily equivalent to or correspond 1:1 with
the top-level <em>import package</em> names
that can be imported inside Python code.
One <em>distribution package</em> can contain multiple <em>import packages</em>
(and single modules),
and one top-level <em>import package</em>
may map to multiple <em>distribution packages</em>
if it is a namespace package.
You can use <a class="reference internal" href="#package-distributions"><span class="std std-ref">package_distributions()</span></a>
to get a mapping between them.</p>
</div>
<p>By default, distribution metadata can live on the file system
or in zip archives on
<a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.  Through an extension mechanism, the metadata can live almost
anywhere.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://importlib-metadata.readthedocs.io/">https://importlib-metadata.readthedocs.io/</a></dt><dd><p>The documentation for <code class="docutils literal notranslate"><span class="pre">importlib_metadata</span></code>, which supplies a
backport of <code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code>.
This includes an <a class="reference external" href="https://importlib-metadata.readthedocs.io/en/latest/api.html">API reference</a>
for this module’s classes and functions,
as well as a <a class="reference external" href="https://importlib-metadata.readthedocs.io/en/latest/migration.html">migration guide</a>
for existing users of <code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code>.</p>
</dd>
</dl>
</div>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading">¶</a></h2>
<p>Let’s say you wanted to get the version string for a
<a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a> you’ve installed
using <code class="docutils literal notranslate"><span class="pre">pip</span></code>.  We start by creating a virtual environment and installing
something into it:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>example
<span class="gp">$ </span><span class="nb">source</span><span class="w"> </span>example/bin/activate
<span class="gp gp-VirtualEnv">(example)</span> <span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>pip<span class="w"> </span>install<span class="w"> </span>wheel
</pre></div>
</div>
<p>You can get the version string for <code class="docutils literal notranslate"><span class="pre">wheel</span></code> by running the following:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="go">(example) $ python</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">importlib.metadata</span> <span class="kn">import</span> <span class="n">version</span>  
<span class="gp">&gt;&gt;&gt; </span><span class="n">version</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
<span class="go">&#39;0.32.3&#39;</span>
</pre></div>
</div>
<p>You can also get a collection of entry points selectable by properties of the EntryPoint (typically ‘group’ or ‘name’), such as
<code class="docutils literal notranslate"><span class="pre">console_scripts</span></code>, <code class="docutils literal notranslate"><span class="pre">distutils.commands</span></code> and others.  Each group contains a
collection of <a class="reference internal" href="#entry-points"><span class="std std-ref">EntryPoint</span></a> objects.</p>
<p>You can get the <a class="reference internal" href="#metadata"><span class="std std-ref">metadata for a distribution</span></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">metadata</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">))</span>  
<span class="go">[&#39;Metadata-Version&#39;, &#39;Name&#39;, &#39;Version&#39;, &#39;Summary&#39;, &#39;Home-page&#39;, &#39;Author&#39;, &#39;Author-email&#39;, &#39;Maintainer&#39;, &#39;Maintainer-email&#39;, &#39;License&#39;, &#39;Project-URL&#39;, &#39;Project-URL&#39;, &#39;Project-URL&#39;, &#39;Keywords&#39;, &#39;Platform&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Classifier&#39;, &#39;Requires-Python&#39;, &#39;Provides-Extra&#39;, &#39;Requires-Dist&#39;, &#39;Requires-Dist&#39;]</span>
</pre></div>
</div>
<p>You can also get a <a class="reference internal" href="#version"><span class="std std-ref">distribution’s version number</span></a>, list its
<a class="reference internal" href="#files"><span class="std std-ref">constituent files</span></a>, and get a list of the distribution’s
<a class="reference internal" href="#requirements"><span class="std std-ref">Distribution requirements</span></a>.</p>
</section>
<section id="functional-api">
<h2>Functional API<a class="headerlink" href="#functional-api" title="Link to this heading">¶</a></h2>
<p>This package provides the following functionality via its public API.</p>
<section id="entry-points">
<span id="id2"></span><h3>Entry points<a class="headerlink" href="#entry-points" title="Link to this heading">¶</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">entry_points()</span></code> function returns a collection of entry points.
Entry points are represented by <code class="docutils literal notranslate"><span class="pre">EntryPoint</span></code> instances;
each <code class="docutils literal notranslate"><span class="pre">EntryPoint</span></code> has a <code class="docutils literal notranslate"><span class="pre">.name</span></code>, <code class="docutils literal notranslate"><span class="pre">.group</span></code>, and <code class="docutils literal notranslate"><span class="pre">.value</span></code> attributes and
a <code class="docutils literal notranslate"><span class="pre">.load()</span></code> method to resolve the value.  There are also <code class="docutils literal notranslate"><span class="pre">.module</span></code>,
<code class="docutils literal notranslate"><span class="pre">.attr</span></code>, and <code class="docutils literal notranslate"><span class="pre">.extras</span></code> attributes for getting the components of the
<code class="docutils literal notranslate"><span class="pre">.value</span></code> attribute.</p>
<p>Query all entry points:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">eps</span> <span class="o">=</span> <span class="n">entry_points</span><span class="p">()</span>  
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">entry_points()</span></code> function returns an <code class="docutils literal notranslate"><span class="pre">EntryPoints</span></code> object,
a collection of all <code class="docutils literal notranslate"><span class="pre">EntryPoint</span></code> objects with <code class="docutils literal notranslate"><span class="pre">names</span></code> and <code class="docutils literal notranslate"><span class="pre">groups</span></code>
attributes for convenience:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">eps</span><span class="o">.</span><span class="n">groups</span><span class="p">)</span>  
<span class="go">[&#39;console_scripts&#39;, &#39;distutils.commands&#39;, &#39;distutils.setup_keywords&#39;, &#39;egg_info.writers&#39;, &#39;setuptools.installation&#39;]</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">EntryPoints</span></code> has a <code class="docutils literal notranslate"><span class="pre">select</span></code> method to select entry points
matching specific properties. Select entry points in the
<code class="docutils literal notranslate"><span class="pre">console_scripts</span></code> group:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">scripts</span> <span class="o">=</span> <span class="n">eps</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="n">group</span><span class="o">=</span><span class="s1">&#39;console_scripts&#39;</span><span class="p">)</span>  
</pre></div>
</div>
<p>Equivalently, since <code class="docutils literal notranslate"><span class="pre">entry_points</span></code> passes keyword arguments
through to select:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">scripts</span> <span class="o">=</span> <span class="n">entry_points</span><span class="p">(</span><span class="n">group</span><span class="o">=</span><span class="s1">&#39;console_scripts&#39;</span><span class="p">)</span>  
</pre></div>
</div>
<p>Pick out a specific script named “wheel” (found in the wheel project):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;wheel&#39;</span> <span class="ow">in</span> <span class="n">scripts</span><span class="o">.</span><span class="n">names</span>  
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">wheel</span> <span class="o">=</span> <span class="n">scripts</span><span class="p">[</span><span class="s1">&#39;wheel&#39;</span><span class="p">]</span>  
</pre></div>
</div>
<p>Equivalently, query for that entry point during selection:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="n">wheel</span><span class="p">,)</span> <span class="o">=</span> <span class="n">entry_points</span><span class="p">(</span><span class="n">group</span><span class="o">=</span><span class="s1">&#39;console_scripts&#39;</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
<span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="n">wheel</span><span class="p">,)</span> <span class="o">=</span> <span class="n">entry_points</span><span class="p">()</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="n">group</span><span class="o">=</span><span class="s1">&#39;console_scripts&#39;</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
</pre></div>
</div>
<p>Inspect the resolved entry point:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">wheel</span>  
<span class="go">EntryPoint(name=&#39;wheel&#39;, value=&#39;wheel.cli:main&#39;, group=&#39;console_scripts&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">wheel</span><span class="o">.</span><span class="n">module</span>  
<span class="go">&#39;wheel.cli&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">wheel</span><span class="o">.</span><span class="n">attr</span>  
<span class="go">&#39;main&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">wheel</span><span class="o">.</span><span class="n">extras</span>  
<span class="go">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">main</span> <span class="o">=</span> <span class="n">wheel</span><span class="o">.</span><span class="n">load</span><span class="p">()</span>  
<span class="gp">&gt;&gt;&gt; </span><span class="n">main</span>  
<span class="go">&lt;function main at 0x103528488&gt;</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">group</span></code> and <code class="docutils literal notranslate"><span class="pre">name</span></code> are arbitrary values defined by the package author
and usually a client will wish to resolve all entry points for a particular
group.  Read <a class="reference external" href="https://setuptools.pypa.io/en/latest/userguide/entry_point.html">the setuptools docs</a>
for more information on entry points, their definition, and usage.</p>
<p><em>Compatibility Note</em></p>
<p>The “selectable” entry points were introduced in <code class="docutils literal notranslate"><span class="pre">importlib_metadata</span></code>
3.6 and Python 3.10. Prior to those changes, <code class="docutils literal notranslate"><span class="pre">entry_points</span></code> accepted
no parameters and always returned a dictionary of entry points, keyed
by group. With <code class="docutils literal notranslate"><span class="pre">importlib_metadata</span></code> 5.0 and Python 3.12,
<code class="docutils literal notranslate"><span class="pre">entry_points</span></code> always returns an <code class="docutils literal notranslate"><span class="pre">EntryPoints</span></code> object. See
<a class="reference external" href="https://pypi.org/project/backports.entry-points-selectable">backports.entry_points_selectable</a>
for compatibility options.</p>
</section>
<section id="distribution-metadata">
<span id="metadata"></span><h3>Distribution metadata<a class="headerlink" href="#distribution-metadata" title="Link to this heading">¶</a></h3>
<p>Every <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a> includes some metadata,
which you can extract using the
<code class="docutils literal notranslate"><span class="pre">metadata()</span></code> function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">wheel_metadata</span> <span class="o">=</span> <span class="n">metadata</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
</pre></div>
</div>
<p>The keys of the returned data structure, a <code class="docutils literal notranslate"><span class="pre">PackageMetadata</span></code>,
name the metadata keywords, and
the values are returned unparsed from the distribution metadata:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">wheel_metadata</span><span class="p">[</span><span class="s1">&#39;Requires-Python&#39;</span><span class="p">]</span>  
<span class="go">&#39;&gt;=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*&#39;</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">PackageMetadata</span></code> also presents a <code class="docutils literal notranslate"><span class="pre">json</span></code> attribute that returns
all the metadata in a JSON-compatible form per <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0566/"><strong>PEP 566</strong></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">wheel_metadata</span><span class="o">.</span><span class="n">json</span><span class="p">[</span><span class="s1">&#39;requires_python&#39;</span><span class="p">]</span>
<span class="go">&#39;&gt;=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The actual type of the object returned by <code class="docutils literal notranslate"><span class="pre">metadata()</span></code> is an
implementation detail and should be accessed only through the interface
described by the
<a class="reference external" href="https://importlib-metadata.readthedocs.io/en/latest/api.html#importlib_metadata.PackageMetadata">PackageMetadata protocol</a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <code class="docutils literal notranslate"><span class="pre">Description</span></code> is now included in the metadata when presented
through the payload. Line continuation characters have been removed.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">json</span></code> attribute was added.</p>
</div>
</section>
<section id="distribution-versions">
<span id="version"></span><h3>Distribution versions<a class="headerlink" href="#distribution-versions" title="Link to this heading">¶</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">version()</span></code> function is the quickest way to get a
<a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a>’s version
number, as a string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">version</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
<span class="go">&#39;0.32.3&#39;</span>
</pre></div>
</div>
</section>
<section id="distribution-files">
<span id="files"></span><h3>Distribution files<a class="headerlink" href="#distribution-files" title="Link to this heading">¶</a></h3>
<p>You can also get the full set of files contained within a distribution.  The
<code class="docutils literal notranslate"><span class="pre">files()</span></code> function takes a <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a> name
and returns all of the
files installed by this distribution.  Each file object returned is a
<code class="docutils literal notranslate"><span class="pre">PackagePath</span></code>, a <a class="reference internal" href="pathlib.html#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.PurePath</span></code></a> derived object with additional <code class="docutils literal notranslate"><span class="pre">dist</span></code>,
<code class="docutils literal notranslate"><span class="pre">size</span></code>, and <code class="docutils literal notranslate"><span class="pre">hash</span></code> properties as indicated by the metadata.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">util</span> <span class="o">=</span> <span class="p">[</span><span class="n">p</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">files</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span> <span class="k">if</span> <span class="s1">&#39;util.py&#39;</span> <span class="ow">in</span> <span class="nb">str</span><span class="p">(</span><span class="n">p</span><span class="p">)][</span><span class="mi">0</span><span class="p">]</span>  
<span class="gp">&gt;&gt;&gt; </span><span class="n">util</span>  
<span class="go">PackagePath(&#39;wheel/util.py&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">util</span><span class="o">.</span><span class="n">size</span>  
<span class="go">859</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">util</span><span class="o">.</span><span class="n">dist</span>  
<span class="go">&lt;importlib.metadata._hooks.PathDistribution object at 0x101e0cef0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">util</span><span class="o">.</span><span class="n">hash</span>  
<span class="go">&lt;FileHash mode: sha256 value: bYkw5oMccfazVCoYQwKkkemoVyMAFoR34mmKBx8R1NI&gt;</span>
</pre></div>
</div>
<p>Once you have the file, you can also read its contents:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">util</span><span class="o">.</span><span class="n">read_text</span><span class="p">())</span>  
<span class="go">import base64</span>
<span class="go">import sys</span>
<span class="go">...</span>
<span class="go">def as_bytes(s):</span>
<span class="go">    if isinstance(s, text_type):</span>
<span class="go">        return s.encode(&#39;utf-8&#39;)</span>
<span class="go">    return s</span>
</pre></div>
</div>
<p>You can also use the <code class="docutils literal notranslate"><span class="pre">locate</span></code> method to get a the absolute path to the
file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">util</span><span class="o">.</span><span class="n">locate</span><span class="p">()</span>  
<span class="go">PosixPath(&#39;/home/<USER>/example/lib/site-packages/wheel/util.py&#39;)</span>
</pre></div>
</div>
<p>In the case where the metadata file listing files
(RECORD or SOURCES.txt) is missing, <code class="docutils literal notranslate"><span class="pre">files()</span></code> will
return <code class="docutils literal notranslate"><span class="pre">None</span></code>. The caller may wish to wrap calls to
<code class="docutils literal notranslate"><span class="pre">files()</span></code> in <a class="reference external" href="https://more-itertools.readthedocs.io/en/stable/api.html#more_itertools.always_iterable">always_iterable</a>
or otherwise guard against this condition if the target
distribution is not known to have the metadata present.</p>
</section>
<section id="distribution-requirements">
<span id="requirements"></span><h3>Distribution requirements<a class="headerlink" href="#distribution-requirements" title="Link to this heading">¶</a></h3>
<p>To get the full set of requirements for a <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a>,
use the <code class="docutils literal notranslate"><span class="pre">requires()</span></code>
function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">requires</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
<span class="go">[&quot;pytest (&gt;=3.0.0) ; extra == &#39;test&#39;&quot;, &quot;pytest-cov ; extra == &#39;test&#39;&quot;]</span>
</pre></div>
</div>
</section>
<section id="mapping-import-to-distribution-packages">
<span id="import-distribution-package-mapping"></span><span id="package-distributions"></span><h3>Mapping import to distribution packages<a class="headerlink" href="#mapping-import-to-distribution-packages" title="Link to this heading">¶</a></h3>
<p>A convenience method to resolve the <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a>
name (or names, in the case of a namespace package)
that provide each importable top-level
Python module or <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Import-Package">Import Package</a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">packages_distributions</span><span class="p">()</span>
<span class="go">{&#39;importlib_metadata&#39;: [&#39;importlib-metadata&#39;], &#39;yaml&#39;: [&#39;PyYAML&#39;], &#39;jaraco&#39;: [&#39;jaraco.classes&#39;, &#39;jaraco.functools&#39;], ...}</span>
</pre></div>
</div>
<p>Some editable installs, <a class="reference external" href="https://github.com/pypa/packaging-problems/issues/609">do not supply top-level names</a>, and thus this
function is not reliable with such installs.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</section>
</section>
<section id="distributions">
<span id="id9"></span><h2>Distributions<a class="headerlink" href="#distributions" title="Link to this heading">¶</a></h2>
<p>While the above API is the most common and convenient usage, you can get all
of that information from the <code class="docutils literal notranslate"><span class="pre">Distribution</span></code> class.  A <code class="docutils literal notranslate"><span class="pre">Distribution</span></code> is an
abstract object that represents the metadata for
a Python <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a>.  You can
get the <code class="docutils literal notranslate"><span class="pre">Distribution</span></code> instance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">importlib.metadata</span> <span class="kn">import</span> <span class="n">distribution</span>  
<span class="gp">&gt;&gt;&gt; </span><span class="n">dist</span> <span class="o">=</span> <span class="n">distribution</span><span class="p">(</span><span class="s1">&#39;wheel&#39;</span><span class="p">)</span>  
</pre></div>
</div>
<p>Thus, an alternative way to get the version number is through the
<code class="docutils literal notranslate"><span class="pre">Distribution</span></code> instance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">dist</span><span class="o">.</span><span class="n">version</span>  
<span class="go">&#39;0.32.3&#39;</span>
</pre></div>
</div>
<p>There are all kinds of additional metadata available on the <code class="docutils literal notranslate"><span class="pre">Distribution</span></code>
instance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">dist</span><span class="o">.</span><span class="n">metadata</span><span class="p">[</span><span class="s1">&#39;Requires-Python&#39;</span><span class="p">]</span>  
<span class="go">&#39;&gt;=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dist</span><span class="o">.</span><span class="n">metadata</span><span class="p">[</span><span class="s1">&#39;License&#39;</span><span class="p">]</span>  
<span class="go">&#39;MIT&#39;</span>
</pre></div>
</div>
<p>The full set of available metadata is not described here.
See the <a class="reference external" href="https://packaging.python.org/en/latest/specifications/core-metadata/#core-metadata">Core metadata specifications</a> for additional details.</p>
</section>
<section id="distribution-discovery">
<h2>Distribution Discovery<a class="headerlink" href="#distribution-discovery" title="Link to this heading">¶</a></h2>
<p>By default, this package provides built-in support for discovery of metadata
for file system and zip file <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a>s.
This metadata finder search defaults to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>, but varies slightly in how it interprets those values from how other import machinery does. In particular:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> does not honor <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects on <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> will incidentally honor <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a> objects on <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> even though such values will be ignored for imports.</p></li>
</ul>
</section>
<section id="extending-the-search-algorithm">
<h2>Extending the search algorithm<a class="headerlink" href="#extending-the-search-algorithm" title="Link to this heading">¶</a></h2>
<p>Because <a class="reference external" href="https://packaging.python.org/en/latest/glossary/#term-Distribution-Package">Distribution Package</a> metadata
is not available through <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> searches, or
package loaders directly,
the metadata for a distribution is found through import
system <a class="reference internal" href="../reference/import.html#finders-and-loaders"><span class="std std-ref">finders</span></a>.  To find a distribution package’s metadata,
<code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> queries the list of <a class="reference internal" href="../glossary.html#term-meta-path-finder"><span class="xref std std-term">meta path finders</span></a> on
<a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a>.</p>
<p>By default <code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> installs a finder for distribution packages
found on the file system.
This finder doesn’t actually find any <em>distributions</em>,
but it can find their metadata.</p>
<p>The abstract class <a class="reference internal" href="importlib.html#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.MetaPathFinder</span></code></a> defines the
interface expected of finders by Python’s import system.
<code class="docutils literal notranslate"><span class="pre">importlib.metadata</span></code> extends this protocol by looking for an optional
<code class="docutils literal notranslate"><span class="pre">find_distributions</span></code> callable on the finders from
<a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a> and presents this extended interface as the
<code class="docutils literal notranslate"><span class="pre">DistributionFinder</span></code> abstract base class, which defines this abstract
method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@abc</span><span class="o">.</span><span class="n">abstractmethod</span>
<span class="k">def</span> <span class="nf">find_distributions</span><span class="p">(</span><span class="n">context</span><span class="o">=</span><span class="n">DistributionFinder</span><span class="o">.</span><span class="n">Context</span><span class="p">()):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Return an iterable of all Distribution instances capable of</span>
<span class="sd">    loading the metadata for packages for the indicated ``context``.</span>
<span class="sd">    &quot;&quot;&quot;</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">DistributionFinder.Context</span></code> object provides <code class="docutils literal notranslate"><span class="pre">.path</span></code> and <code class="docutils literal notranslate"><span class="pre">.name</span></code>
properties indicating the path to search and name to match and may
supply other relevant context.</p>
<p>What this means in practice is that to support finding distribution package
metadata in locations other than the file system, subclass
<code class="docutils literal notranslate"><span class="pre">Distribution</span></code> and implement the abstract methods. Then from
a custom finder, return instances of this derived <code class="docutils literal notranslate"><span class="pre">Distribution</span></code> in the
<code class="docutils literal notranslate"><span class="pre">find_distributions()</span></code> method.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#functional-api">Functional API</a><ul>
<li><a class="reference internal" href="#entry-points">Entry points</a></li>
<li><a class="reference internal" href="#distribution-metadata">Distribution metadata</a></li>
<li><a class="reference internal" href="#distribution-versions">Distribution versions</a></li>
<li><a class="reference internal" href="#distribution-files">Distribution files</a></li>
<li><a class="reference internal" href="#distribution-requirements">Distribution requirements</a></li>
<li><a class="reference internal" href="#mapping-import-to-distribution-packages">Mapping import to distribution packages</a></li>
</ul>
</li>
<li><a class="reference internal" href="#distributions">Distributions</a></li>
<li><a class="reference internal" href="#distribution-discovery">Distribution Discovery</a></li>
<li><a class="reference internal" href="#extending-the-search-algorithm">Extending the search algorithm</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.resources.abc.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code> – Abstract base classes for resources</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sys_path_init.html"
                          title="next chapter">The initialization of the <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code> module search path</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.metadata.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sys_path_init.html" title="The initialization of the sys.path module search path"
             >next</a> |</li>
        <li class="right" >
          <a href="importlib.resources.abc.html" title="importlib.resources.abc – Abstract base classes for resources"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>