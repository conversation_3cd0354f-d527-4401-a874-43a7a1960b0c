.left-pane {
    max-width: 500px;
}

.orders .col {
    flex: 1;

    &.very-narrow {
        flex: 0.2;
    }

    &.narrow {
        flex: 0.5;
    }

    &.wide {
        flex: 1.5;
    }
}

.order-row:hover {
    color: white;
    background-color:  $primary !important;
}

@media screen and (max-width: 640px) {
    .screen-full-width {
        flex-direction: column;
        height: auto;
        min-height: 100%;
    }
    .controls {
        position: sticky;
        top: 0;
        background: $gray-300;
        z-index: 100;
    }

    .ticket-screen .order,
    .ticket-screen .leftpane {
        max-width: inherit;
    }

    .ticket-screen .rightpane {
        width: auto;
    }
    .pos .orders {
        position: sticky;
    }
}

@media screen and (min-width: 992px) {
    .order-row {
        display: flex;
        cursor: pointer;
    }

    .order-row:nth-child(odd) {
        background: $gray-200;
    }

    .order-row:nth-child(even) {
        background: white;
    }
}

@media screen and (max-width: 992px) {
    .order-row {
        margin: 5px;
        border: 1px solid #C9CCD2; /* $border-color */
        background: white; /* $o-view-background-color */
    }
}

@media screen and (min-width: 992px) {
    .pos .orders .col.end {
        justify-content: flex-end;
    }
}

@media screen and (max-width: 992px) {
    .pos .orders .order-row > .col > div:first-child {
        font-weight: bold;
    }

    .ticket-screen .order-row .delete-button {
        margin-top: 5px;
        justify-content: center;
        background: #d23f3a /* o-text-color('danger') */ ;
        color: white;
    }
    .ticket-screen .order-row .delete-button .fa-trash {
        margin-right: 5px;
    }
}
