<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="wave — Read and write WAV files" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/wave.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/wave.py The wave module provides a convenient interface to the Waveform Audio “WAVE” (or “WAV”) file format. Only uncompressed PCM encoded wave files are supported. The wave module..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/wave.py The wave module provides a convenient interface to the Waveform Audio “WAVE” (or “WAV”) file format. Only uncompressed PCM encoded wave files are supported. The wave module..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>wave — Read and write WAV files &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="colorsys — Conversions between color systems" href="colorsys.html" />
    <link rel="prev" title="Multimedia Services" href="mm.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/wave.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code> — Read and write WAV files</a><ul>
<li><a class="reference internal" href="#wave-read-objects">Wave_read Objects</a></li>
<li><a class="reference internal" href="#wave-write-objects">Wave_write Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="mm.html"
                          title="previous chapter">Multimedia Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="colorsys.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">colorsys</span></code> — Conversions between color systems</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/wave.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="colorsys.html" title="colorsys — Conversions between color systems"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="mm.html" title="Multimedia Services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="mm.html" accesskey="U">Multimedia Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code> — Read and write WAV files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-wave">
<span id="wave-read-and-write-wav-files"></span><h1><a class="reference internal" href="#module-wave" title="wave: Provide an interface to the WAV sound format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code></a> — Read and write WAV files<a class="headerlink" href="#module-wave" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/wave.py">Lib/wave.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-wave" title="wave: Provide an interface to the WAV sound format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code></a> module provides a convenient interface to the Waveform Audio
“WAVE” (or “WAV”) file format. Only uncompressed PCM encoded wave files are
supported.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Support for <code class="docutils literal notranslate"><span class="pre">WAVE_FORMAT_EXTENSIBLE</span></code> headers was added, provided that the
extended format is <code class="docutils literal notranslate"><span class="pre">KSDATAFORMAT_SUBTYPE_PCM</span></code>.</p>
</div>
<p>The <a class="reference internal" href="#module-wave" title="wave: Provide an interface to the WAV sound format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code></a> module defines the following function and exception:</p>
<dl class="py function">
<dt class="sig sig-object py" id="wave.open">
<span class="sig-prename descclassname"><span class="pre">wave.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.open" title="Link to this definition">¶</a></dt>
<dd><p>If <em>file</em> is a string, open the file by that name, otherwise treat it as a
file-like object.  <em>mode</em> can be:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">'rb'</span></code></dt><dd><p>Read only mode.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'wb'</span></code></dt><dd><p>Write only mode.</p>
</dd>
</dl>
<p>Note that it does not allow read/write WAV files.</p>
<p>A <em>mode</em> of <code class="docutils literal notranslate"><span class="pre">'rb'</span></code> returns a <a class="reference internal" href="#wave.Wave_read" title="wave.Wave_read"><code class="xref py py-class docutils literal notranslate"><span class="pre">Wave_read</span></code></a> object, while a <em>mode</em> of
<code class="docutils literal notranslate"><span class="pre">'wb'</span></code> returns a <a class="reference internal" href="#wave.Wave_write" title="wave.Wave_write"><code class="xref py py-class docutils literal notranslate"><span class="pre">Wave_write</span></code></a> object.  If <em>mode</em> is omitted and a
file-like object is passed as <em>file</em>, <code class="docutils literal notranslate"><span class="pre">file.mode</span></code> is used as the default
value for <em>mode</em>.</p>
<p>If you pass in a file-like object, the wave object will not close it when its
<code class="docutils literal notranslate"><span class="pre">close()</span></code> method is called; it is the caller’s responsibility to close
the file object.</p>
<p>The <a class="reference internal" href="#wave.open" title="wave.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function may be used in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  When
the <code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> block completes, the <a class="reference internal" href="#wave.Wave_read.close" title="wave.Wave_read.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Wave_read.close()</span></code></a> or
<a class="reference internal" href="#wave.Wave_write.close" title="wave.Wave_write.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Wave_write.close()</span></code></a> method is called.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for unseekable files.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="wave.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wave.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#wave.Error" title="Link to this definition">¶</a></dt>
<dd><p>An error raised when something is impossible because it violates the WAV
specification or hits an implementation deficiency.</p>
</dd></dl>

<section id="wave-read-objects">
<span id="id1"></span><h2>Wave_read Objects<a class="headerlink" href="#wave-read-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="wave.Wave_read">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wave.</span></span><span class="sig-name descname"><span class="pre">Wave_read</span></span><a class="headerlink" href="#wave.Wave_read" title="Link to this definition">¶</a></dt>
<dd><p>Read a WAV file.</p>
<p>Wave_read objects, as returned by <a class="reference internal" href="#wave.open" title="wave.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>, have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the stream if it was opened by <a class="reference internal" href="#module-wave" title="wave: Provide an interface to the WAV sound format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code></a>, and make the instance
unusable.  This is called automatically on object collection.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getnchannels">
<span class="sig-name descname"><span class="pre">getnchannels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getnchannels" title="Link to this definition">¶</a></dt>
<dd><p>Returns number of audio channels (<code class="docutils literal notranslate"><span class="pre">1</span></code> for mono, <code class="docutils literal notranslate"><span class="pre">2</span></code> for stereo).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getsampwidth">
<span class="sig-name descname"><span class="pre">getsampwidth</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getsampwidth" title="Link to this definition">¶</a></dt>
<dd><p>Returns sample width in bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getframerate">
<span class="sig-name descname"><span class="pre">getframerate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getframerate" title="Link to this definition">¶</a></dt>
<dd><p>Returns sampling frequency.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getnframes">
<span class="sig-name descname"><span class="pre">getnframes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getnframes" title="Link to this definition">¶</a></dt>
<dd><p>Returns number of audio frames.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getcomptype">
<span class="sig-name descname"><span class="pre">getcomptype</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getcomptype" title="Link to this definition">¶</a></dt>
<dd><p>Returns compression type (<code class="docutils literal notranslate"><span class="pre">'NONE'</span></code> is the only supported type).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getcompname">
<span class="sig-name descname"><span class="pre">getcompname</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getcompname" title="Link to this definition">¶</a></dt>
<dd><p>Human-readable version of <a class="reference internal" href="#wave.Wave_read.getcomptype" title="wave.Wave_read.getcomptype"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getcomptype()</span></code></a>. Usually <code class="docutils literal notranslate"><span class="pre">'not</span> <span class="pre">compressed'</span></code>
parallels <code class="docutils literal notranslate"><span class="pre">'NONE'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getparams">
<span class="sig-name descname"><span class="pre">getparams</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getparams" title="Link to this definition">¶</a></dt>
<dd><p>Returns a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a> <code class="docutils literal notranslate"><span class="pre">(nchannels,</span> <span class="pre">sampwidth,</span>
<span class="pre">framerate,</span> <span class="pre">nframes,</span> <span class="pre">comptype,</span> <span class="pre">compname)</span></code>, equivalent to output of the
<code class="docutils literal notranslate"><span class="pre">get*()</span></code> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.readframes">
<span class="sig-name descname"><span class="pre">readframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.readframes" title="Link to this definition">¶</a></dt>
<dd><p>Reads and returns at most <em>n</em> frames of audio, as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.rewind">
<span class="sig-name descname"><span class="pre">rewind</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.rewind" title="Link to this definition">¶</a></dt>
<dd><p>Rewind the file pointer to the beginning of the audio stream.</p>
</dd></dl>

<p>The following two methods are defined for compatibility with the <a class="reference internal" href="aifc.html#module-aifc" title="aifc: Read and write audio files in AIFF or AIFC format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">aifc</span></code></a>
module, and don’t do anything interesting.</p>
<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getmarkers">
<span class="sig-name descname"><span class="pre">getmarkers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getmarkers" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.getmark">
<span class="sig-name descname"><span class="pre">getmark</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.getmark" title="Link to this definition">¶</a></dt>
<dd><p>Raise an error.</p>
</dd></dl>

<p>The following two methods define a term “position” which is compatible between
them, and is otherwise implementation dependent.</p>
<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.setpos">
<span class="sig-name descname"><span class="pre">setpos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.setpos" title="Link to this definition">¶</a></dt>
<dd><p>Set the file pointer to the specified position.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_read.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_read.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return current file pointer position.</p>
</dd></dl>

</dd></dl>

</section>
<section id="wave-write-objects">
<span id="id2"></span><h2>Wave_write Objects<a class="headerlink" href="#wave-write-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="wave.Wave_write">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wave.</span></span><span class="sig-name descname"><span class="pre">Wave_write</span></span><a class="headerlink" href="#wave.Wave_write" title="Link to this definition">¶</a></dt>
<dd><p>Write a WAV file.</p>
<p>Wave_write objects, as returned by <a class="reference internal" href="#wave.open" title="wave.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.</p>
<p>For seekable output streams, the <code class="docutils literal notranslate"><span class="pre">wave</span></code> header will automatically be updated
to reflect the number of frames actually written.  For unseekable streams, the
<em>nframes</em> value must be accurate when the first frame data is written.  An
accurate <em>nframes</em> value can be achieved either by calling
<a class="reference internal" href="#wave.Wave_write.setnframes" title="wave.Wave_write.setnframes"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setnframes()</span></code></a> or <a class="reference internal" href="#wave.Wave_write.setparams" title="wave.Wave_write.setparams"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setparams()</span></code></a> with the number
of frames that will be written before <a class="reference internal" href="#wave.Wave_write.close" title="wave.Wave_write.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> is called and
then using <a class="reference internal" href="#wave.Wave_write.writeframesraw" title="wave.Wave_write.writeframesraw"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframesraw()</span></code></a> to write the frame data, or by
calling <a class="reference internal" href="#wave.Wave_write.writeframes" title="wave.Wave_write.writeframes"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframes()</span></code></a> with all of the frame data to be
written.  In the latter case <a class="reference internal" href="#wave.Wave_write.writeframes" title="wave.Wave_write.writeframes"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframes()</span></code></a> will calculate
the number of frames in the data and set <em>nframes</em> accordingly before writing
the frame data.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for unseekable files.</p>
</div>
<p>Wave_write objects have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.close" title="Link to this definition">¶</a></dt>
<dd><p>Make sure <em>nframes</em> is correct, and close the file if it was opened by
<a class="reference internal" href="#module-wave" title="wave: Provide an interface to the WAV sound format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code></a>.  This method is called upon object collection.  It will raise
an exception if the output stream is not seekable and <em>nframes</em> does not
match the number of frames actually written.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.setnchannels">
<span class="sig-name descname"><span class="pre">setnchannels</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.setnchannels" title="Link to this definition">¶</a></dt>
<dd><p>Set the number of channels.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.setsampwidth">
<span class="sig-name descname"><span class="pre">setsampwidth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.setsampwidth" title="Link to this definition">¶</a></dt>
<dd><p>Set the sample width to <em>n</em> bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.setframerate">
<span class="sig-name descname"><span class="pre">setframerate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.setframerate" title="Link to this definition">¶</a></dt>
<dd><p>Set the frame rate to <em>n</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>A non-integral input to this method is rounded to the nearest
integer.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.setnframes">
<span class="sig-name descname"><span class="pre">setnframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.setnframes" title="Link to this definition">¶</a></dt>
<dd><p>Set the number of frames to <em>n</em>.  This will be changed later if the number
of frames actually written is different (this update attempt will
raise an error if the output stream is not seekable).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.setcomptype">
<span class="sig-name descname"><span class="pre">setcomptype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.setcomptype" title="Link to this definition">¶</a></dt>
<dd><p>Set the compression type and description. At the moment, only compression type
<code class="docutils literal notranslate"><span class="pre">NONE</span></code> is supported, meaning no compression.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.setparams">
<span class="sig-name descname"><span class="pre">setparams</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tuple</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.setparams" title="Link to this definition">¶</a></dt>
<dd><p>The <em>tuple</em> should be <code class="docutils literal notranslate"><span class="pre">(nchannels,</span> <span class="pre">sampwidth,</span> <span class="pre">framerate,</span> <span class="pre">nframes,</span> <span class="pre">comptype,</span>
<span class="pre">compname)</span></code>, with values valid for the <code class="docutils literal notranslate"><span class="pre">set*()</span></code> methods.  Sets all
parameters.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return current position in the file, with the same disclaimer for the
<a class="reference internal" href="#wave.Wave_read.tell" title="wave.Wave_read.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Wave_read.tell()</span></code></a> and <a class="reference internal" href="#wave.Wave_read.setpos" title="wave.Wave_read.setpos"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Wave_read.setpos()</span></code></a> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.writeframesraw">
<span class="sig-name descname"><span class="pre">writeframesraw</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.writeframesraw" title="Link to this definition">¶</a></dt>
<dd><p>Write audio frames, without correcting <em>nframes</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Any <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wave.Wave_write.writeframes">
<span class="sig-name descname"><span class="pre">writeframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wave.Wave_write.writeframes" title="Link to this definition">¶</a></dt>
<dd><p>Write audio frames and make sure <em>nframes</em> is correct.  It will raise an
error if the output stream is not seekable and the total number of frames
that have been written after <em>data</em> has been written does not match the
previously set value for <em>nframes</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Any <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
<p>Note that it is invalid to set any parameters after calling <a class="reference internal" href="#wave.Wave_write.writeframes" title="wave.Wave_write.writeframes"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframes()</span></code></a>
or <a class="reference internal" href="#wave.Wave_write.writeframesraw" title="wave.Wave_write.writeframesraw"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframesraw()</span></code></a>, and any attempt to do so will raise
<a class="reference internal" href="#wave.Error" title="wave.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">wave.Error</span></code></a>.</p>
</dd></dl>

</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code> — Read and write WAV files</a><ul>
<li><a class="reference internal" href="#wave-read-objects">Wave_read Objects</a></li>
<li><a class="reference internal" href="#wave-write-objects">Wave_write Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="mm.html"
                          title="previous chapter">Multimedia Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="colorsys.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">colorsys</span></code> — Conversions between color systems</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/wave.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="colorsys.html" title="colorsys — Conversions between color systems"
             >next</a> |</li>
        <li class="right" >
          <a href="mm.html" title="Multimedia Services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="mm.html" >Multimedia Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code> — Read and write WAV files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>