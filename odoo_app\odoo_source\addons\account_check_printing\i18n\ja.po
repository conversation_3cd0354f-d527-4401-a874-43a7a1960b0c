# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid " : Check Number Sequence"
msgstr ":番号順をチェック"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr "<span>&amp;nbsp;</span>"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr "プリンタの設定に合うように生成された小切手の余白を調整します。"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "金額の文字表記"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "取消"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "小切手のレイアウト"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "小切手の左マージン"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "小切手の番号"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "小切手の印刷"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "小切手の右マージン"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "小切手のシーケンス"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "小切手の上余白"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Check numbers can only consist of digits"
msgstr "小切手番号は数字のみが使えます。"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "印刷済の小切手が番号付けされていない場合は、このオプションをチェックしてください。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "印刷対象の小切手"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "小切手"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "小切手の番号シーケンス"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
#, python-format
msgid "Checks to Print"
msgstr "印刷する小切手"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "印刷する小切手"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "会社"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "作成者"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "作成日"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "表示名"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "コンフィグレーションパネルに移動"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr "一度に複数の小切手を印刷するためには、同じ銀行の台帳に属している必要があります。"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "仕訳帳"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_move
msgid "Journal Entry"
msgstr "仕訳"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "手動番号割当"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"手動： Odoo以外の方法で支払を受けます。\n"
"決済プロバイダー： 各決済プロバイダーにはそれぞれ独自の決済方法があります。オンラインで購入またはサブスクリプション利用する際にパートナーによって保存された決済トークンによって、カード上またはカード宛に取引を要求することができます。\n"
"小切手：小切手で請求書を支払い、Odooから印刷することができます。\n"
"バッチ入金： 複数の顧客の小切手を一度に収集し、銀行へのバッチ入金を生成して送信します。会計_バッチ_決済（account_batch_payment）モジュールが必要です。\n"
"SEPAダイレクトデビット： パートナーから付与された権限により、SEPAゾーンで支払いを受けることができます。会計_SEPA（account_sepa）モジュールが必要です。\n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "マルチページをチェックするスタブ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "次の小切手番号"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
#, python-format
msgid "Next Check Number should only contains numbers."
msgstr "次の小切手番号は数字だけであるべきです。"

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "なし"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_users__property_payment_method_id
msgid "Payment Method"
msgstr "支払方法"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "支払方法"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "支払"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr "小切手を支払い方法として選択する場合は、支払い方法に'小切手'を選択していると共に、未消込みである必要があります。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "印刷したい最初の印刷済み小切手の番号を入力してください。"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_bank_statement_line__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_move__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__preferred_payment_method_id
msgid "Preferred Payment Method"
msgstr "優先支払方法"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,help:account_check_printing.field_res_users__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr ""
"この仕入先に支払う際に使用するお好みの支払方法。これは、お好みの支払方法で仕入先請求書をフィルタして、大量に支払を登録する時に使用されます。使用例: "
"一括振込に銀行ファイルを作成。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "印刷"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "小切手印刷"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "印刷チェック"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "印刷日付ラベル"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "前の番号のチェックを印刷"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_register
msgid "Register Payment"
msgstr "支払登録"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "右マージン"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"あなたの小切手に印刷される小切手用紙に対応するフォーマットを選択します。\n"
"印刷機能を無効にするためには、'なし'を選択。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "送信日"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "次に印刷された小切手のシーケンス番号。"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr "確認番号を表示する"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr "チェックレイアウトにて何か問題が起きました、請求書発行/会計設定で別のレイアウトを選択して、もう一度お試しください。"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""
"次の番号は、すでに使用されています:\n"
"%s"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr "最後のチェックの数は%sでした。銀行によって却下された小切手の利用を回避するために、より大きな番号を使用することができます。"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"選択された台帳は、小切手番号を印刷するように構成されています。印刷済みの小切手用紙は、既に番号を持っているか、現在の番号が間違っている場合は、ジャーナルの設定ページで変更できます。"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr "このオプションは、それらが単一ページに収まらない場合は、複数ページのチェックの詳細情報（スタブ）を印刷することができます。"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"このオプションにより、CPAごとに小切手へ日付ラベルを印刷することができます。\n"
"印刷済の小切手台紙に日付ラベルが含まれている場合、無効にしてください。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "これは、支払いに対応する小切手の数を節約することができます。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "送付済みを選択解除"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "無効な小切手"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr ""
"あなたが小切手のレイアウトを選択する必要があります。このため、'小切手のレイアウト'とセット1を検索し、請求書発行/会計セッティングに行きます。"
