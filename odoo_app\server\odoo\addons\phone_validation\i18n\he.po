# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* phone_validation
# 
# Translators:
# NoaFarkash, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# דודי מלכ<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>tem <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/phone_blacklist.py:0
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid " Please correct the number and try again."
msgstr "נא לתקן את המספר ולנסות שוב."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__active
msgid "Active"
msgstr "פעיל"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Add a phone number in the blacklist"
msgstr ""

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_search
msgid "Archived"
msgstr "בארכיון"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Phone Number?"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_attachment_count
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_base
msgid "Base"
msgstr "בסיס"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Blacklist"
msgstr "רשימה שחורה "

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Blacklist Date"
msgstr "תאריך רשימה שחורה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "אל תכלול טלפון נייד מהרשימה שחורה"

#. module: phone_validation
#: model:ir.actions.act_window,name:phone_validation.phone_blacklist_action
msgid "Blacklisted Phone Numbers"
msgstr "מספרי טלפון ברשימה השחורה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "אל תכלול טלפון מהרשימה שחורה"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Blacklisted phone numbers won't receive SMS Mailings anymore."
msgstr ""

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/res_users.py:0
#, python-format
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Confirm"
msgstr "אשר"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Discard"
msgstr "בטל"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__display_name
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "שדה המשמש לאחסון מספר טלפון מאומת. עוזר לזרז חיפושים והשוואות."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_follower_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_partner_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__has_message
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__id
msgid "ID"
msgstr "מזהה"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"אם מספר הטלפון המחוטא נמצא ברשימה השחורה, איש הקשר לא יקבל יותר הודעות דואר "
"המוניות מכל רשימה"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: not a valid country prefix."
msgstr ""

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: not enough digits."
msgstr ""

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: probably invalid number of digits."
msgstr ""

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#: code:addons/phone_validation/tools/phone_validation.py:0
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: too many digits."
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"מציין אם מספר טלפון המופיע ברשימה השחורה הוא מספר טלפון. עוזר להבחין איזה "
"מספר מופיע ברשימה השחורה כאשר במודל יש גם שדה נייד וגם טלפון."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"מציין אם מספר טלפון מחוטא ברשימה השחורה הוא מספר טלפון. עוזר להבחין איזה "
"מספר מופיע ברשימה השחורה כאשר במודל יש גם שדה נייד וגם טלפון."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Invalid number %s: probably incorrect prefix."
msgstr ""

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Invalid primary phone field on model %s"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_is_follower
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Missing definition of phone fields."
msgstr ""

#. module: phone_validation
#: model:ir.model.constraint,message:phone_validation.constraint_phone_blacklist_unique_number
msgid "Number already exists"
msgstr "מספר כבר קיים"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__number
msgid "Number should be E164 formatted"
msgstr ""

#. module: phone_validation
#: model:ir.ui.menu,name:phone_validation.phone_menu_main
msgid "Phone / SMS"
msgstr "טלפון / SMS"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist
#: model:ir.ui.menu,name:phone_validation.phone_blacklist_menu
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Phone Blacklist"
msgstr "רשימה שחורה של טלפונים"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "שותף ברשימה השחורה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__number
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__phone
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Phone Number"
msgstr "מספר טלפון"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_mobile_search
msgid "Phone/Mobile"
msgstr "נייד"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Reason"
msgstr "סיבה"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist_remove
msgid "Remove phone from blacklist"
msgstr "הסרת הטלפון מרשימה שחורה"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized
msgid "Sanitized Number"
msgstr "מספר מאומת"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Unable to parse %(phone)s: %(error)s"
msgstr ""

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Unblacklist"
msgstr "הסרה מרשימה שחורה"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/wizard/phone_blacklist_remove.py:0
#, python-format
msgid "Unblock Reason: %(reason)s"
msgstr ""

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_users
msgid "User"
msgstr "משתמש"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "phone_blacklist_removal"
msgstr ""
