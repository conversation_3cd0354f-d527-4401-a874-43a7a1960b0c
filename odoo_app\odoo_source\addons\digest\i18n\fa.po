# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <PERSON><PERSON><PERSON> <iam<PERSON><EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON> <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# Most<PERSON>a <PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Check our Documentation"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr "<span class=\"button\" id=\"button_open_report\">بازکردن گزارش</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr "<span class=\"odoo_link_text\">اودو</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "فعال"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Activated"
msgstr "فعال شده"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""
"کاربران جدید را به عنوان گیرنده‌ی ایمیل‌های دوره‌ای با شاخص‌های تعریف شده "
"اضافه کنید"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "گروه مجاز"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "فیلدهای در دسترس"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Choose the metrics you care about"
msgstr "معیارهایی را که به آن‌ها اهمیت می‌دهید انتخاب کنید."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "شرکت"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "تنظیمات ایمیل‌های خلاصه"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Connect"
msgstr "اتصال"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "کاربران متصل"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "ارز"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Custom"
msgstr "سفارشی"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "روزانه"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate"
msgstr "غیرفعال"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Deactivated"
msgstr "غیرفعال‌شده"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "خلاصه"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "ایمیل خلاصه"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "ایمیل‌های خلاصه"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "اشتراک‌های خلاصه‌"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "نکات خلاصه"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Title"
msgstr "عنوان خلاصه"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Documentation"
msgstr "مستندات"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Email Address"
msgstr "نشانی ایمیل"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "عمومی"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "گروه‌بندی برمبنای"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""
"سوالی درباره یک سند دارید؟ روی تصویر کاربر مسئول کلیک کنید تا مکالمه‌ای را "
"شروع کنید. اگر آواتار او دارای نقطه سبز است، او آنلاین است."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "شناسه"

#. module: digest
#. odoo-python
#: code:addons/digest/controllers/portal.py:0
#, python-format
msgid "Invalid periodicity set on digest"
msgstr "دوره‌بندی نامعتبر برای خلاصه‌گزارش تنظیم شده است."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "آیا کاربر اشتراک دارد؟"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "خلاصه شاخص‌های کلیدی عملکرد"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr "نکته هضم KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr "نکات هضم KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "شاخاص‌های کلیدی عملکرد"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "پیام ایمیل KPI مجموع کل ارزش"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "قیمت KPI کاربران متصل شده Res"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 24 hours"
msgstr "آخرین ۲۴ ساعت"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 30 Days"
msgstr "30 روز گذشته"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 7 Days"
msgstr "7 روز گذشته"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr "آخرین اصلاح در"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages Sent"
msgstr "ارسال شده‌ها پیام‌ها"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "ماهانه"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "نام"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""
"کاربران جدید به‌طور خودکار به‌عنوان دریافت‌کننده‌ی ایمیل هفتگی زیر اضافه "
"می‌شوند."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Mailing Date"
msgstr "تاریخ ارسال بعدی"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "Odoo موبایل"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "دوره‌ای"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "راه اندازی شده به وسیله"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Prefer a broader overview ?"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""
"ALT را در هر صفحه فشار دهید تا میانبرهای هر دکمه در صفحه را برجسته کنید. این"
" کار برای پردازش دسته‌ای چندین سند مفید است."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "فصلی"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "گیرندگان"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr "کسب و کار خود را از هر کجا با <b>اودو موبایل</b> مدیریت کنید."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "هم اکنون ارسال شود"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "ارسال شده توسط"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "آمار"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "وضعیت"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Switch to weekly Digests"
msgstr "به خلاصه هفتگی تغییر دهید"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "شرح نکته"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "نکته: یک ماشین حساب در اودو"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "نکته: برای گفتگو با یک کاربر روی آواتار کلیک کنید"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "نکته: چگونه کاربران را در یادداشت‌های داخلی مطلع کنیم؟"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "نکته: دانش قدرت است"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "نکته: با استفاده از میانبرها، جریان کار خود را تسریع کنید"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "نکته: یک ماشین حساب در اودو"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "نکته: برای چت با یک کاربر بر روی آواتار کلیک کنید"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "نکته: چگونه کاربران را در یادداشت‌های داخلی پین کنیم؟"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "نکته: دانش قدرت است"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "نکته: با استفاده از میانبرها، جریان کاری خود را سرعت ببخشید"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "Title"
msgstr "عنوان"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""
"برای مطلع کردن کسی در یک پیام از \"@\" استفاده کنید، یا برای لینک کردن به یک"
" کانال از \"#\" استفاده کنید. سعی کنید @OdooBot را مطلع کنید تا این ویژگی را"
" آزمایش کنید."

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr "به منظور نمایش نکته هضم در قالب ایمیل بر اساس سفارش استفاده می‌شود."

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "User"
msgstr "کاربر"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "کاربرانی که قبلاً این نکته را دریافت کرده‌اند"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Want to add your own KPIs?<br/>"
msgstr "آیا می‌خواهید شاخص‌های کلیدی عملکرد خود را اضافه کنید؟<br/>"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Want to customize this email?"
msgstr "آیا می‌خواهید این ایمیل را شخصی‌سازی کنید؟"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""
"ما متوجه شده‌ایم که شما در چند روز گذشته ارتباط برقرار نکرده‌اید. ما به‌طور "
"خودکار ترجیحات شما را به خلاصه‌های %(new_perioridicy_str)s تغییر داده‌ایم."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "هفتگی"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""
"هنگام ویرایش یک عدد، می‌توانید با تایپ کاراکتر `=` از فرمول‌ها استفاده کنید."
" این موضوع زمانی مفید است که برای محاسبه حاشیه یا تخفیف در یک نقل‌قول، سفارش"
" فروش یا فاکتور باشد."

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""
"هنگام دنبال کردن اسناد، از آیکون مداد برای تنظیم دقیق اطلاعاتی که می‌خواهید دریافت کنید استفاده کنید.\n"
"یک پروژه / تیم فروش را دنبال کنید تا وظایف این پروژه / فرصت‌های این تیم را پیگیری کنید."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from:<br/>"
msgstr "شما با موفقیت از اشتراک خارج شده‌اید از:<br/>"

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "خلاصه‌ی دوره‌ای اودووی شما"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "مثال: هضم هفتگی شما"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "monthly"
msgstr "ماهانه"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "quarterly"
msgstr "فصلی"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "weekly"
msgstr "هفتگی"
