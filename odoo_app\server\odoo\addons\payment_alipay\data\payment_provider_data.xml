<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment_provider_alipay" model="payment.provider">
        <field name="name">Alipay</field>
        <field name="image_128" type="base64" file="payment_alipay/static/description/icon.png"/>
        <field name="module_id" ref="base.module_payment_alipay"/>
        <!-- https://intl.alipay.com/ihome/home/<USER>/buy.htm?topic=paymentMethods -->
        <field name="payment_method_ids"
               eval="[(6, 0, [
                   ref('payment.payment_method_card'),
                   ref('payment.payment_method_rabbit_line_pay'),
                   ref('payment.payment_method_truemoney'),
                   ref('payment.payment_method_boost'),
                   ref('payment.payment_method_touch_n_go'),
                   ref('payment.payment_method_gcash'),
                   ref('payment.payment_method_billease'),
                   ref('payment.payment_method_bpi'),
                   ref('payment.payment_method_maya'),
                   ref('payment.payment_method_dana'),
                   ref('payment.payment_method_akulaku'),
                   ref('payment.payment_method_kredivo'),
                   ref('payment.payment_method_kakaopay'),
                   ref('payment.payment_method_naver_pay'),
                   ref('payment.payment_method_toss_pay'),
                   ref('payment.payment_method_alipay'),
                   ref('payment.payment_method_alipay_hk'),
                   ref('payment.payment_method_dolfin'),
                   ref('payment.payment_method_grabpay'),
                   ref('payment.payment_method_gopay'),
                   ref('payment.payment_method_linkaja'),
                   ref('payment.payment_method_ovo'),
                   ref('payment.payment_method_paypay'),
                   ref('payment.payment_method_zalopay'),
                   ref('payment.payment_method_bangkok_bank'),
                   ref('payment.payment_method_bank_of_ayudhya'),
                   ref('payment.payment_method_krungthai_bank'),
                   ref('payment.payment_method_scb'),
                   ref('payment.payment_method_blik'),
                   ref('payment.payment_method_gsb'),
                   ref('payment.payment_method_kasikorn_bank'),
                   ref('payment.payment_method_promptpay'),
                   ref('payment.payment_method_paynow'),
                   ref('payment.payment_method_bni'),
                   ref('payment.payment_method_mandiri'),
                   ref('payment.payment_method_maybank'),
                   ref('payment.payment_method_cimb_niaga'),
                   ref('payment.payment_method_bsi'),
                   ref('payment.payment_method_qris'),
                   ref('payment.payment_method_pix'),
                   ref('payment.payment_method_bancontact'),
                   ref('payment.payment_method_giropay'),
                   ref('payment.payment_method_ideal'),
                   ref('payment.payment_method_payu'),
                   ref('payment.payment_method_p24'),
                   ref('payment.payment_method_sofort'),
                   ref('payment.payment_method_eps'),
                   ref('payment.payment_method_bancomat_pay'),
                   ref('payment.payment_method_brankas'),
                   ref('payment.payment_method_pay_easy'),
                   ref('payment.payment_method_fpx'),
               ])]"/>
        <field name="code">alipay</field>
        <field name="redirect_form_view_id" ref="redirect_form"/>
    </record>

</odoo>
