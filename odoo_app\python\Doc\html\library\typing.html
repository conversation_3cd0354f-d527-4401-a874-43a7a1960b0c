<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="typing — Support for type hints" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/typing.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/typing.py This module provides runtime support for type hints. Consider the function below: The function moon_weight takes an argument expected to be an instance of float, as indic..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/typing.py This module provides runtime support for type hints. Consider the function below: The function moon_weight takes an argument expected to be an instance of float, as indic..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>typing — Support for type hints &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="pydoc — Documentation generator and online help system" href="pydoc.html" />
    <link rel="prev" title="Development Tools" href="development.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/typing.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code> — Support for type hints</a><ul>
<li><a class="reference internal" href="#specification-for-the-python-type-system">Specification for the Python Type System</a></li>
<li><a class="reference internal" href="#type-aliases">Type aliases</a></li>
<li><a class="reference internal" href="#newtype">NewType</a></li>
<li><a class="reference internal" href="#annotating-callable-objects">Annotating callable objects</a></li>
<li><a class="reference internal" href="#generics">Generics</a></li>
<li><a class="reference internal" href="#annotating-tuples">Annotating tuples</a></li>
<li><a class="reference internal" href="#the-type-of-class-objects">The type of class objects</a></li>
<li><a class="reference internal" href="#user-defined-generic-types">User-defined generic types</a></li>
<li><a class="reference internal" href="#the-any-type">The <code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code> type</a></li>
<li><a class="reference internal" href="#nominal-vs-structural-subtyping">Nominal vs structural subtyping</a></li>
<li><a class="reference internal" href="#module-contents">Module contents</a><ul>
<li><a class="reference internal" href="#special-typing-primitives">Special typing primitives</a><ul>
<li><a class="reference internal" href="#special-types">Special types</a></li>
<li><a class="reference internal" href="#special-forms">Special forms</a></li>
<li><a class="reference internal" href="#building-generic-types-and-type-aliases">Building generic types and type aliases</a></li>
<li><a class="reference internal" href="#other-special-directives">Other special directives</a></li>
</ul>
</li>
<li><a class="reference internal" href="#protocols">Protocols</a></li>
<li><a class="reference internal" href="#abcs-for-working-with-io">ABCs for working with IO</a></li>
<li><a class="reference internal" href="#functions-and-decorators">Functions and decorators</a></li>
<li><a class="reference internal" href="#introspection-helpers">Introspection helpers</a></li>
<li><a class="reference internal" href="#constant">Constant</a></li>
<li><a class="reference internal" href="#deprecated-aliases">Deprecated aliases</a><ul>
<li><a class="reference internal" href="#aliases-to-built-in-types">Aliases to built-in types</a></li>
<li><a class="reference internal" href="#aliases-to-types-in-collections">Aliases to types in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-other-concrete-types">Aliases to other concrete types</a></li>
<li><a class="reference internal" href="#aliases-to-container-abcs-in-collections-abc">Aliases to container ABCs in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-asynchronous-abcs-in-collections-abc">Aliases to asynchronous ABCs in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-other-abcs-in-collections-abc">Aliases to other ABCs in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-contextlib-abcs">Aliases to <code class="xref py py-mod docutils literal notranslate"><span class="pre">contextlib</span></code> ABCs</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#deprecation-timeline-of-major-features">Deprecation Timeline of Major Features</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="development.html"
                          title="previous chapter">Development Tools</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pydoc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> — Documentation generator and online help system</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/typing.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pydoc.html" title="pydoc — Documentation generator and online help system"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="development.html" title="Development Tools"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" accesskey="U">Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code> — Support for type hints</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="typing-support-for-type-hints">
<h1><a class="reference internal" href="#module-typing" title="typing: Support for type hints (see :pep:`484`)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code></a> — Support for type hints<a class="headerlink" href="#typing-support-for-type-hints" title="Link to this heading">¶</a></h1>
<div class="versionadded" id="module-typing">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/typing.py">Lib/typing.py</a></p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The Python runtime does not enforce function and variable type annotations.
They can be used by third party tools such as <a class="reference internal" href="../glossary.html#term-static-type-checker"><span class="xref std std-term">type checkers</span></a>,
IDEs, linters, etc.</p>
</div>
<hr class="docutils" />
<p>This module provides runtime support for type hints.</p>
<p>Consider the function below:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">moon_weight</span><span class="p">(</span><span class="n">earth_weight</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
    <span class="k">return</span> <span class="sa">f</span><span class="s1">&#39;On the moon, you would weigh </span><span class="si">{</span><span class="n">earth_weight</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">0.166</span><span class="si">}</span><span class="s1"> kilograms.&#39;</span>
</pre></div>
</div>
<p>The function <code class="docutils literal notranslate"><span class="pre">moon_weight</span></code> takes an argument expected to be an instance of <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>,
as indicated by the <em>type hint</em> <code class="docutils literal notranslate"><span class="pre">earth_weight:</span> <span class="pre">float</span></code>. The function is expected to
return an instance of <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, as indicated by the <code class="docutils literal notranslate"><span class="pre">-&gt;</span> <span class="pre">str</span></code> hint.</p>
<p>While type hints can be simple classes like <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> or <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>,
they can also be more complex. The <a class="reference internal" href="#module-typing" title="typing: Support for type hints (see :pep:`484`)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code></a> module provides a vocabulary of
more advanced type hints.</p>
<p>New features are frequently added to the <code class="docutils literal notranslate"><span class="pre">typing</span></code> module.
The <a class="reference external" href="https://pypi.org/project/typing-extensions/">typing_extensions</a> package
provides backports of these new features to older versions of Python.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://mypy.readthedocs.io/en/stable/cheat_sheet_py3.html">“Typing cheat sheet”</a></dt><dd><p>A quick overview of type hints (hosted at the mypy docs)</p>
</dd>
<dt>“Type System Reference” section of <a class="reference external" href="https://mypy.readthedocs.io/en/stable/index.html">the mypy docs</a></dt><dd><p>The Python typing system is standardised via PEPs, so this reference
should broadly apply to most Python type checkers. (Some parts may still
be specific to mypy.)</p>
</dd>
<dt><a class="reference external" href="https://typing.readthedocs.io/en/latest/">“Static Typing with Python”</a></dt><dd><p>Type-checker-agnostic documentation written by the community detailing
type system features, useful typing related tools and typing best
practices.</p>
</dd>
</dl>
</div>
<section id="specification-for-the-python-type-system">
<span id="relevant-peps"></span><h2>Specification for the Python Type System<a class="headerlink" href="#specification-for-the-python-type-system" title="Link to this heading">¶</a></h2>
<p>The canonical, up-to-date specification of the Python type system can be
found at <a class="reference external" href="https://typing.readthedocs.io/en/latest/spec/index.html">“Specification for the Python type system”</a>.</p>
</section>
<section id="type-aliases">
<span id="id2"></span><h2>Type aliases<a class="headerlink" href="#type-aliases" title="Link to this heading">¶</a></h2>
<p>A type alias is defined using the <a class="reference internal" href="../reference/simple_stmts.html#type"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">type</span></code></a> statement, which creates
an instance of <a class="reference internal" href="#typing.TypeAliasType" title="typing.TypeAliasType"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeAliasType</span></code></a>. In this example,
<code class="docutils literal notranslate"><span class="pre">Vector</span></code> and <code class="docutils literal notranslate"><span class="pre">list[float]</span></code> will be treated equivalently by static type
checkers:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">type</span> <span class="n">Vector</span> <span class="o">=</span> <span class="nb">list</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>

<span class="k">def</span> <span class="nf">scale</span><span class="p">(</span><span class="n">scalar</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span> <span class="n">vector</span><span class="p">:</span> <span class="n">Vector</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Vector</span><span class="p">:</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">scalar</span> <span class="o">*</span> <span class="n">num</span> <span class="k">for</span> <span class="n">num</span> <span class="ow">in</span> <span class="n">vector</span><span class="p">]</span>

<span class="c1"># passes type checking; a list of floats qualifies as a Vector.</span>
<span class="n">new_vector</span> <span class="o">=</span> <span class="n">scale</span><span class="p">(</span><span class="mf">2.0</span><span class="p">,</span> <span class="p">[</span><span class="mf">1.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">4.2</span><span class="p">,</span> <span class="mf">5.4</span><span class="p">])</span>
</pre></div>
</div>
<p>Type aliases are useful for simplifying complex type signatures. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Sequence</span>

<span class="nb">type</span> <span class="n">ConnectionOptions</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span>
<span class="nb">type</span> <span class="n">Address</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span>
<span class="nb">type</span> <span class="n">Server</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">[</span><span class="n">Address</span><span class="p">,</span> <span class="n">ConnectionOptions</span><span class="p">]</span>

<span class="k">def</span> <span class="nf">broadcast_message</span><span class="p">(</span><span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">servers</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="n">Server</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>

<span class="c1"># The static type checker will treat the previous type signature as</span>
<span class="c1"># being exactly equivalent to this one.</span>
<span class="k">def</span> <span class="nf">broadcast_message</span><span class="p">(</span>
        <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">servers</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">],</span> <span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="../reference/simple_stmts.html#type"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">type</span></code></a> statement is new in Python 3.12. For backwards
compatibility, type aliases can also be created through simple assignment:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Vector</span> <span class="o">=</span> <span class="nb">list</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>
</pre></div>
</div>
<p>Or marked with <a class="reference internal" href="#typing.TypeAlias" title="typing.TypeAlias"><code class="xref py py-data docutils literal notranslate"><span class="pre">TypeAlias</span></code></a> to make it explicit that this is a type alias,
not a normal variable assignment:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeAlias</span>

<span class="n">Vector</span><span class="p">:</span> <span class="n">TypeAlias</span> <span class="o">=</span> <span class="nb">list</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>
</pre></div>
</div>
</section>
<section id="newtype">
<span id="distinct"></span><h2>NewType<a class="headerlink" href="#newtype" title="Link to this heading">¶</a></h2>
<p>Use the <a class="reference internal" href="#typing.NewType" title="typing.NewType"><code class="xref py py-class docutils literal notranslate"><span class="pre">NewType</span></code></a> helper to create distinct types:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">NewType</span>

<span class="n">UserId</span> <span class="o">=</span> <span class="n">NewType</span><span class="p">(</span><span class="s1">&#39;UserId&#39;</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>
<span class="n">some_id</span> <span class="o">=</span> <span class="n">UserId</span><span class="p">(</span><span class="mi">524313</span><span class="p">)</span>
</pre></div>
</div>
<p>The static type checker will treat the new type as if it were a subclass
of the original type. This is useful in helping catch logical errors:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">get_user_name</span><span class="p">(</span><span class="n">user_id</span><span class="p">:</span> <span class="n">UserId</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
    <span class="o">...</span>

<span class="c1"># passes type checking</span>
<span class="n">user_a</span> <span class="o">=</span> <span class="n">get_user_name</span><span class="p">(</span><span class="n">UserId</span><span class="p">(</span><span class="mi">42351</span><span class="p">))</span>

<span class="c1"># fails type checking; an int is not a UserId</span>
<span class="n">user_b</span> <span class="o">=</span> <span class="n">get_user_name</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>You may still perform all <code class="docutils literal notranslate"><span class="pre">int</span></code> operations on a variable of type <code class="docutils literal notranslate"><span class="pre">UserId</span></code>,
but the result will always be of type <code class="docutils literal notranslate"><span class="pre">int</span></code>. This lets you pass in a
<code class="docutils literal notranslate"><span class="pre">UserId</span></code> wherever an <code class="docutils literal notranslate"><span class="pre">int</span></code> might be expected, but will prevent you from
accidentally creating a <code class="docutils literal notranslate"><span class="pre">UserId</span></code> in an invalid way:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># &#39;output&#39; is of type &#39;int&#39;, not &#39;UserId&#39;</span>
<span class="n">output</span> <span class="o">=</span> <span class="n">UserId</span><span class="p">(</span><span class="mi">23413</span><span class="p">)</span> <span class="o">+</span> <span class="n">UserId</span><span class="p">(</span><span class="mi">54341</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that these checks are enforced only by the static type checker. At runtime,
the statement <code class="docutils literal notranslate"><span class="pre">Derived</span> <span class="pre">=</span> <span class="pre">NewType('Derived',</span> <span class="pre">Base)</span></code> will make <code class="docutils literal notranslate"><span class="pre">Derived</span></code> a
callable that immediately returns whatever parameter you pass it. That means
the expression <code class="docutils literal notranslate"><span class="pre">Derived(some_value)</span></code> does not create a new class or introduce
much overhead beyond that of a regular function call.</p>
<p>More precisely, the expression <code class="docutils literal notranslate"><span class="pre">some_value</span> <span class="pre">is</span> <span class="pre">Derived(some_value)</span></code> is always
true at runtime.</p>
<p>It is invalid to create a subtype of <code class="docutils literal notranslate"><span class="pre">Derived</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">NewType</span>

<span class="n">UserId</span> <span class="o">=</span> <span class="n">NewType</span><span class="p">(</span><span class="s1">&#39;UserId&#39;</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>

<span class="c1"># Fails at runtime and does not pass type checking</span>
<span class="k">class</span> <span class="nc">AdminUserId</span><span class="p">(</span><span class="n">UserId</span><span class="p">):</span> <span class="k">pass</span>
</pre></div>
</div>
<p>However, it is possible to create a <a class="reference internal" href="#typing.NewType" title="typing.NewType"><code class="xref py py-class docutils literal notranslate"><span class="pre">NewType</span></code></a> based on a ‘derived’ <code class="docutils literal notranslate"><span class="pre">NewType</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">NewType</span>

<span class="n">UserId</span> <span class="o">=</span> <span class="n">NewType</span><span class="p">(</span><span class="s1">&#39;UserId&#39;</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>

<span class="n">ProUserId</span> <span class="o">=</span> <span class="n">NewType</span><span class="p">(</span><span class="s1">&#39;ProUserId&#39;</span><span class="p">,</span> <span class="n">UserId</span><span class="p">)</span>
</pre></div>
</div>
<p>and typechecking for <code class="docutils literal notranslate"><span class="pre">ProUserId</span></code> will work as expected.</p>
<p>See <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0484/"><strong>PEP 484</strong></a> for more details.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Recall that the use of a type alias declares two types to be <em>equivalent</em> to
one another. Doing <code class="docutils literal notranslate"><span class="pre">type</span> <span class="pre">Alias</span> <span class="pre">=</span> <span class="pre">Original</span></code> will make the static type checker
treat <code class="docutils literal notranslate"><span class="pre">Alias</span></code> as being <em>exactly equivalent</em> to <code class="docutils literal notranslate"><span class="pre">Original</span></code> in all cases.
This is useful when you want to simplify complex type signatures.</p>
<p>In contrast, <code class="docutils literal notranslate"><span class="pre">NewType</span></code> declares one type to be a <em>subtype</em> of another.
Doing <code class="docutils literal notranslate"><span class="pre">Derived</span> <span class="pre">=</span> <span class="pre">NewType('Derived',</span> <span class="pre">Original)</span></code> will make the static type
checker treat <code class="docutils literal notranslate"><span class="pre">Derived</span></code> as a <em>subclass</em> of <code class="docutils literal notranslate"><span class="pre">Original</span></code>, which means a
value of type <code class="docutils literal notranslate"><span class="pre">Original</span></code> cannot be used in places where a value of type
<code class="docutils literal notranslate"><span class="pre">Derived</span></code> is expected. This is useful when you want to prevent logic
errors with minimal runtime cost.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">NewType</span></code> is now a class rather than a function.  As a result, there is
some additional runtime cost when calling <code class="docutils literal notranslate"><span class="pre">NewType</span></code> over a regular
function.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The performance of calling <code class="docutils literal notranslate"><span class="pre">NewType</span></code> has been restored to its level in
Python 3.9.</p>
</div>
</section>
<section id="annotating-callable-objects">
<span id="annotating-callables"></span><h2>Annotating callable objects<a class="headerlink" href="#annotating-callable-objects" title="Link to this heading">¶</a></h2>
<p>Functions – or other <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> objects – can be annotated using
<a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Callable</span></code></a> or <a class="reference internal" href="#typing.Callable" title="typing.Callable"><code class="xref py py-data docutils literal notranslate"><span class="pre">typing.Callable</span></code></a>.
<code class="docutils literal notranslate"><span class="pre">Callable[[int],</span> <span class="pre">str]</span></code> signifies a function that takes a single parameter
of type <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> and returns a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.</p>
<p>For example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Callable</span><span class="p">,</span> <span class="n">Awaitable</span>

<span class="k">def</span> <span class="nf">feeder</span><span class="p">(</span><span class="n">get_next_item</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[],</span> <span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>  <span class="c1"># Body</span>

<span class="k">def</span> <span class="nf">async_query</span><span class="p">(</span><span class="n">on_success</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">],</span> <span class="kc">None</span><span class="p">],</span>
                <span class="n">on_error</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">,</span> <span class="ne">Exception</span><span class="p">],</span> <span class="kc">None</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>  <span class="c1"># Body</span>

<span class="k">async</span> <span class="k">def</span> <span class="nf">on_update</span><span class="p">(</span><span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>  <span class="c1"># Body</span>

<span class="n">callback</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">str</span><span class="p">],</span> <span class="n">Awaitable</span><span class="p">[</span><span class="kc">None</span><span class="p">]]</span> <span class="o">=</span> <span class="n">on_update</span>
</pre></div>
</div>
<p>The subscription syntax must always be used with exactly two values: the
argument list and the return type.  The argument list must be a list of types,
a <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a>, <a class="reference internal" href="#typing.Concatenate" title="typing.Concatenate"><code class="xref py py-data docutils literal notranslate"><span class="pre">Concatenate</span></code></a>, or an ellipsis. The return type must
be a single type.</p>
<p>If a literal ellipsis <code class="docutils literal notranslate"><span class="pre">...</span></code> is given as the argument list, it indicates that
a callable with any arbitrary parameter list would be acceptable:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">concat</span><span class="p">(</span><span class="n">x</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>

<span class="n">x</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[</span><span class="o">...</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span>
<span class="n">x</span> <span class="o">=</span> <span class="nb">str</span>     <span class="c1"># OK</span>
<span class="n">x</span> <span class="o">=</span> <span class="n">concat</span>  <span class="c1"># Also OK</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Callable</span></code> cannot express complex signatures such as functions that take a
variadic number of arguments, <a class="reference internal" href="#overload"><span class="std std-ref">overloaded functions</span></a>, or
functions that have keyword-only parameters. However, these signatures can be
expressed by defining a <a class="reference internal" href="#typing.Protocol" title="typing.Protocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">Protocol</span></code></a> class with a
<a class="reference internal" href="../reference/datamodel.html#object.__call__" title="object.__call__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__call__()</span></code></a> method:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Iterable</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Protocol</span>

<span class="k">class</span> <span class="nc">Combiner</span><span class="p">(</span><span class="n">Protocol</span><span class="p">):</span>
    <span class="k">def</span> <span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">vals</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">maxlen</span><span class="p">:</span> <span class="nb">int</span> <span class="o">|</span> <span class="kc">None</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">list</span><span class="p">[</span><span class="nb">bytes</span><span class="p">]:</span> <span class="o">...</span>

<span class="k">def</span> <span class="nf">batch_proc</span><span class="p">(</span><span class="n">data</span><span class="p">:</span> <span class="n">Iterable</span><span class="p">[</span><span class="nb">bytes</span><span class="p">],</span> <span class="n">cb_results</span><span class="p">:</span> <span class="n">Combiner</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bytes</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">data</span><span class="p">:</span>
        <span class="o">...</span>

<span class="k">def</span> <span class="nf">good_cb</span><span class="p">(</span><span class="o">*</span><span class="n">vals</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">maxlen</span><span class="p">:</span> <span class="nb">int</span> <span class="o">|</span> <span class="kc">None</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">list</span><span class="p">[</span><span class="nb">bytes</span><span class="p">]:</span>
    <span class="o">...</span>
<span class="k">def</span> <span class="nf">bad_cb</span><span class="p">(</span><span class="o">*</span><span class="n">vals</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">maxitems</span><span class="p">:</span> <span class="nb">int</span> <span class="o">|</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">list</span><span class="p">[</span><span class="nb">bytes</span><span class="p">]:</span>
    <span class="o">...</span>

<span class="n">batch_proc</span><span class="p">([],</span> <span class="n">good_cb</span><span class="p">)</span>  <span class="c1"># OK</span>
<span class="n">batch_proc</span><span class="p">([],</span> <span class="n">bad_cb</span><span class="p">)</span>   <span class="c1"># Error! Argument 2 has incompatible type because of</span>
                         <span class="c1"># different name and kind in the callback</span>
</pre></div>
</div>
<p>Callables which take other callables as arguments may indicate that their
parameter types are dependent on each other using <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a>.
Additionally, if that callable adds or removes arguments from other
callables, the <a class="reference internal" href="#typing.Concatenate" title="typing.Concatenate"><code class="xref py py-data docutils literal notranslate"><span class="pre">Concatenate</span></code></a> operator may be used.  They
take the form <code class="docutils literal notranslate"><span class="pre">Callable[ParamSpecVariable,</span> <span class="pre">ReturnType]</span></code> and
<code class="docutils literal notranslate"><span class="pre">Callable[Concatenate[Arg1Type,</span> <span class="pre">Arg2Type,</span> <span class="pre">...,</span> <span class="pre">ParamSpecVariable],</span> <span class="pre">ReturnType]</span></code>
respectively.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">Callable</span></code> now supports <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> and <a class="reference internal" href="#typing.Concatenate" title="typing.Concatenate"><code class="xref py py-data docutils literal notranslate"><span class="pre">Concatenate</span></code></a>.
See <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0612/"><strong>PEP 612</strong></a> for more details.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The documentation for <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> and <a class="reference internal" href="#typing.Concatenate" title="typing.Concatenate"><code class="xref py py-class docutils literal notranslate"><span class="pre">Concatenate</span></code></a> provides
examples of usage in <code class="docutils literal notranslate"><span class="pre">Callable</span></code>.</p>
</div>
</section>
<section id="generics">
<span id="id3"></span><h2>Generics<a class="headerlink" href="#generics" title="Link to this heading">¶</a></h2>
<p>Since type information about objects kept in containers cannot be statically
inferred in a generic way, many container classes in the standard library support
subscription to denote the expected types of container elements.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Mapping</span><span class="p">,</span> <span class="n">Sequence</span>

<span class="k">class</span> <span class="nc">Employee</span><span class="p">:</span> <span class="o">...</span>

<span class="c1"># Sequence[Employee] indicates that all elements in the sequence</span>
<span class="c1"># must be instances of &quot;Employee&quot;.</span>
<span class="c1"># Mapping[str, str] indicates that all keys and all values in the mapping</span>
<span class="c1"># must be strings.</span>
<span class="k">def</span> <span class="nf">notify_by_email</span><span class="p">(</span><span class="n">employees</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="n">Employee</span><span class="p">],</span>
                    <span class="n">overrides</span><span class="p">:</span> <span class="n">Mapping</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span> <span class="o">...</span>
</pre></div>
</div>
<p>Generic functions and classes can be parameterized by using
<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter syntax</span></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Sequence</span>

<span class="k">def</span> <span class="nf">first</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">l</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="n">T</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">T</span><span class="p">:</span>  <span class="c1"># Function is generic over the TypeVar &quot;T&quot;</span>
    <span class="k">return</span> <span class="n">l</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</pre></div>
</div>
<p>Or by using the <a class="reference internal" href="#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a> factory directly:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Sequence</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span>

<span class="n">U</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;U&#39;</span><span class="p">)</span>                  <span class="c1"># Declare type variable &quot;U&quot;</span>

<span class="k">def</span> <span class="nf">second</span><span class="p">(</span><span class="n">l</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="n">U</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">U</span><span class="p">:</span>  <span class="c1"># Function is generic over the TypeVar &quot;U&quot;</span>
    <span class="k">return</span> <span class="n">l</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Syntactic support for generics is new in Python 3.12.</p>
</div>
</section>
<section id="annotating-tuples">
<span id="id4"></span><h2>Annotating tuples<a class="headerlink" href="#annotating-tuples" title="Link to this heading">¶</a></h2>
<p>For most containers in Python, the typing system assumes that all elements in
the container will be of the same type. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Mapping</span>

<span class="c1"># Type checker will infer that all elements in ``x`` are meant to be ints</span>
<span class="n">x</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

<span class="c1"># Type checker error: ``list`` only accepts a single type argument:</span>
<span class="n">y</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;foo&#39;</span><span class="p">]</span>

<span class="c1"># Type checker will infer that all keys in ``z`` are meant to be strings,</span>
<span class="c1"># and that all values in ``z`` are meant to be either strings or ints</span>
<span class="n">z</span><span class="p">:</span> <span class="n">Mapping</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span> <span class="o">|</span> <span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
</pre></div>
</div>
<p><a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> only accepts one type argument, so a type checker would emit an
error on the <code class="docutils literal notranslate"><span class="pre">y</span></code> assignment above. Similarly,
<a class="reference internal" href="collections.abc.html#collections.abc.Mapping" title="collections.abc.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mapping</span></code></a> only accepts two type arguments: the first
indicates the type of the keys, and the second indicates the type of the
values.</p>
<p>Unlike most other Python containers, however, it is common in idiomatic Python
code for tuples to have elements which are not all of the same type. For this
reason, tuples are special-cased in Python’s typing system. <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>
accepts <em>any number</em> of type arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># OK: ``x`` is assigned to a tuple of length 1 where the sole element is an int</span>
<span class="n">x</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="mi">5</span><span class="p">,)</span>

<span class="c1"># OK: ``y`` is assigned to a tuple of length 2;</span>
<span class="c1"># element 1 is an int, element 2 is a str</span>
<span class="n">y</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="s2">&quot;foo&quot;</span><span class="p">)</span>

<span class="c1"># Error: the type annotation indicates a tuple of length 1,</span>
<span class="c1"># but ``z`` has been assigned to a tuple of length 3</span>
<span class="n">z</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
<p>To denote a tuple which could be of <em>any</em> length, and in which all elements are
of the same type <code class="docutils literal notranslate"><span class="pre">T</span></code>, use <code class="docutils literal notranslate"><span class="pre">tuple[T,</span> <span class="pre">...]</span></code>. To denote an empty tuple, use
<code class="docutils literal notranslate"><span class="pre">tuple[()]</span></code>. Using plain <code class="docutils literal notranslate"><span class="pre">tuple</span></code> as an annotation is equivalent to using
<code class="docutils literal notranslate"><span class="pre">tuple[Any,</span> <span class="pre">...]</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="o">...</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="c1"># These reassignments are OK: ``tuple[int, ...]`` indicates x can be of any length</span>
<span class="n">x</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">x</span> <span class="o">=</span> <span class="p">()</span>
<span class="c1"># This reassignment is an error: all elements in ``x`` must be ints</span>
<span class="n">x</span> <span class="o">=</span> <span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="s2">&quot;bar&quot;</span><span class="p">)</span>

<span class="c1"># ``y`` can only ever be assigned to an empty tuple</span>
<span class="n">y</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[()]</span> <span class="o">=</span> <span class="p">()</span>

<span class="n">z</span><span class="p">:</span> <span class="nb">tuple</span> <span class="o">=</span> <span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="s2">&quot;bar&quot;</span><span class="p">)</span>
<span class="c1"># These reassignments are OK: plain ``tuple`` is equivalent to ``tuple[Any, ...]``</span>
<span class="n">z</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">z</span> <span class="o">=</span> <span class="p">()</span>
</pre></div>
</div>
</section>
<section id="the-type-of-class-objects">
<span id="type-of-class-objects"></span><h2>The type of class objects<a class="headerlink" href="#the-type-of-class-objects" title="Link to this heading">¶</a></h2>
<p>A variable annotated with <code class="docutils literal notranslate"><span class="pre">C</span></code> may accept a value of type <code class="docutils literal notranslate"><span class="pre">C</span></code>. In
contrast, a variable annotated with <code class="docutils literal notranslate"><span class="pre">type[C]</span></code> (or
<a class="reference internal" href="#typing.Type" title="typing.Type"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Type[C]</span></code></a>) may accept values that are classes
themselves – specifically, it will accept the <em>class object</em> of <code class="docutils literal notranslate"><span class="pre">C</span></code>. For
example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">a</span> <span class="o">=</span> <span class="mi">3</span>         <span class="c1"># Has type ``int``</span>
<span class="n">b</span> <span class="o">=</span> <span class="nb">int</span>       <span class="c1"># Has type ``type[int]``</span>
<span class="n">c</span> <span class="o">=</span> <span class="nb">type</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>   <span class="c1"># Also has type ``type[int]``</span>
</pre></div>
</div>
<p>Note that <code class="docutils literal notranslate"><span class="pre">type[C]</span></code> is covariant:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">User</span><span class="p">:</span> <span class="o">...</span>
<span class="k">class</span> <span class="nc">ProUser</span><span class="p">(</span><span class="n">User</span><span class="p">):</span> <span class="o">...</span>
<span class="k">class</span> <span class="nc">TeamUser</span><span class="p">(</span><span class="n">User</span><span class="p">):</span> <span class="o">...</span>

<span class="k">def</span> <span class="nf">make_new_user</span><span class="p">(</span><span class="n">user_class</span><span class="p">:</span> <span class="nb">type</span><span class="p">[</span><span class="n">User</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">User</span><span class="p">:</span>
    <span class="c1"># ...</span>
    <span class="k">return</span> <span class="n">user_class</span><span class="p">()</span>

<span class="n">make_new_user</span><span class="p">(</span><span class="n">User</span><span class="p">)</span>      <span class="c1"># OK</span>
<span class="n">make_new_user</span><span class="p">(</span><span class="n">ProUser</span><span class="p">)</span>   <span class="c1"># Also OK: ``type[ProUser]`` is a subtype of ``type[User]``</span>
<span class="n">make_new_user</span><span class="p">(</span><span class="n">TeamUser</span><span class="p">)</span>  <span class="c1"># Still fine</span>
<span class="n">make_new_user</span><span class="p">(</span><span class="n">User</span><span class="p">())</span>    <span class="c1"># Error: expected ``type[User]`` but got ``User``</span>
<span class="n">make_new_user</span><span class="p">(</span><span class="nb">int</span><span class="p">)</span>       <span class="c1"># Error: ``type[int]`` is not a subtype of ``type[User]``</span>
</pre></div>
</div>
<p>The only legal parameters for <a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> are classes, <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>,
<a class="reference internal" href="#generics"><span class="std std-ref">type variables</span></a>, and unions of any of these types.
For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">new_non_team_user</span><span class="p">(</span><span class="n">user_class</span><span class="p">:</span> <span class="nb">type</span><span class="p">[</span><span class="n">BasicUser</span> <span class="o">|</span> <span class="n">ProUser</span><span class="p">]):</span> <span class="o">...</span>

<span class="n">new_non_team_user</span><span class="p">(</span><span class="n">BasicUser</span><span class="p">)</span>  <span class="c1"># OK</span>
<span class="n">new_non_team_user</span><span class="p">(</span><span class="n">ProUser</span><span class="p">)</span>    <span class="c1"># OK</span>
<span class="n">new_non_team_user</span><span class="p">(</span><span class="n">TeamUser</span><span class="p">)</span>   <span class="c1"># Error: ``type[TeamUser]`` is not a subtype</span>
                              <span class="c1"># of ``type[BasicUser | ProUser]``</span>
<span class="n">new_non_team_user</span><span class="p">(</span><span class="n">User</span><span class="p">)</span>       <span class="c1"># Also an error</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type[Any]</span></code> is equivalent to <a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>, which is the root of Python’s
<a class="reference internal" href="../reference/datamodel.html#metaclasses"><span class="std std-ref">metaclass hierarchy</span></a>.</p>
</section>
<section id="user-defined-generic-types">
<span id="user-defined-generics"></span><h2>User-defined generic types<a class="headerlink" href="#user-defined-generic-types" title="Link to this heading">¶</a></h2>
<p>A user-defined class can be defined as a generic class.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">logging</span> <span class="kn">import</span> <span class="n">Logger</span>

<span class="k">class</span> <span class="nc">LoggedVar</span><span class="p">[</span><span class="n">T</span><span class="p">]:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">T</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">logger</span><span class="p">:</span> <span class="n">Logger</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">name</span> <span class="o">=</span> <span class="n">name</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">logger</span> <span class="o">=</span> <span class="n">logger</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">=</span> <span class="n">value</span>

    <span class="k">def</span> <span class="nf">set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">new</span><span class="p">:</span> <span class="n">T</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">log</span><span class="p">(</span><span class="s1">&#39;Set &#39;</span> <span class="o">+</span> <span class="nb">repr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="p">))</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">=</span> <span class="n">new</span>

    <span class="k">def</span> <span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T</span><span class="p">:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">log</span><span class="p">(</span><span class="s1">&#39;Get &#39;</span> <span class="o">+</span> <span class="nb">repr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="p">))</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="k">def</span> <span class="nf">log</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s1">&#39;</span><span class="si">%s</span><span class="s1">: </span><span class="si">%s</span><span class="s1">&#39;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="n">message</span><span class="p">)</span>
</pre></div>
</div>
<p>This syntax indicates that the class <code class="docutils literal notranslate"><span class="pre">LoggedVar</span></code> is parameterised around a
single <a class="reference internal" href="#typevar"><span class="std std-ref">type variable</span></a> <code class="docutils literal notranslate"><span class="pre">T</span></code> . This also makes <code class="docutils literal notranslate"><span class="pre">T</span></code> valid as
a type within the class body.</p>
<p>Generic classes implicitly inherit from <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a>. For compatibility
with Python 3.11 and lower, it is also possible to inherit explicitly from
<a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> to indicate a generic class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span><span class="p">,</span> <span class="n">Generic</span>

<span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;T&#39;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">LoggedVar</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Generic classes have <a class="reference internal" href="../reference/datamodel.html#object.__class_getitem__" title="object.__class_getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__class_getitem__()</span></code></a> methods, meaning they
can be parameterised at runtime (e.g. <code class="docutils literal notranslate"><span class="pre">LoggedVar[int]</span></code> below):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Iterable</span>

<span class="k">def</span> <span class="nf">zero_all_vars</span><span class="p">(</span><span class="nb">vars</span><span class="p">:</span> <span class="n">Iterable</span><span class="p">[</span><span class="n">LoggedVar</span><span class="p">[</span><span class="nb">int</span><span class="p">]])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">var</span> <span class="ow">in</span> <span class="nb">vars</span><span class="p">:</span>
        <span class="n">var</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>A generic type can have any number of type variables. All varieties of
<a class="reference internal" href="#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a> are permissible as parameters for a generic type:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span><span class="p">,</span> <span class="n">Generic</span><span class="p">,</span> <span class="n">Sequence</span>

<span class="k">class</span> <span class="nc">WeirdTrio</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="n">B</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="nb">bytes</span><span class="p">],</span> <span class="n">S</span><span class="p">:</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">)]:</span>
    <span class="o">...</span>

<span class="n">OldT</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;OldT&#39;</span><span class="p">,</span> <span class="n">contravariant</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="n">OldB</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;OldB&#39;</span><span class="p">,</span> <span class="n">bound</span><span class="o">=</span><span class="n">Sequence</span><span class="p">[</span><span class="nb">bytes</span><span class="p">],</span> <span class="n">covariant</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="n">OldS</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;OldS&#39;</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">OldWeirdTrio</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">OldT</span><span class="p">,</span> <span class="n">OldB</span><span class="p">,</span> <span class="n">OldS</span><span class="p">]):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Each type variable argument to <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> must be distinct.
This is thus invalid:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span><span class="p">,</span> <span class="n">Generic</span>
<span class="o">...</span>

<span class="k">class</span> <span class="nc">Pair</span><span class="p">[</span><span class="n">M</span><span class="p">,</span> <span class="n">M</span><span class="p">]:</span>  <span class="c1"># SyntaxError</span>
    <span class="o">...</span>

<span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;T&#39;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">Pair</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="n">T</span><span class="p">]):</span>   <span class="c1"># INVALID</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Generic classes can also inherit from other classes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Sized</span>

<span class="k">class</span> <span class="nc">LinkedList</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">Sized</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>When inheriting from generic classes, some type parameters could be fixed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Mapping</span>

<span class="k">class</span> <span class="nc">MyDict</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">Mapping</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">T</span><span class="p">]):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>In this case <code class="docutils literal notranslate"><span class="pre">MyDict</span></code> has a single parameter, <code class="docutils literal notranslate"><span class="pre">T</span></code>.</p>
<p>Using a generic class without specifying type parameters assumes
<a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> for each position. In the following example, <code class="docutils literal notranslate"><span class="pre">MyIterable</span></code> is
not generic but implicitly inherits from <code class="docutils literal notranslate"><span class="pre">Iterable[Any]</span></code>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Iterable</span>

<span class="k">class</span> <span class="nc">MyIterable</span><span class="p">(</span><span class="n">Iterable</span><span class="p">):</span> <span class="c1"># Same as Iterable[Any]</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>User-defined generic type aliases are also supported. Examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Iterable</span>

<span class="nb">type</span> <span class="n">Response</span><span class="p">[</span><span class="n">S</span><span class="p">]</span> <span class="o">=</span> <span class="n">Iterable</span><span class="p">[</span><span class="n">S</span><span class="p">]</span> <span class="o">|</span> <span class="nb">int</span>

<span class="c1"># Return type here is same as Iterable[str] | int</span>
<span class="k">def</span> <span class="nf">response</span><span class="p">(</span><span class="n">query</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Response</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
    <span class="o">...</span>

<span class="nb">type</span> <span class="n">Vec</span><span class="p">[</span><span class="n">T</span><span class="p">]</span> <span class="o">=</span> <span class="n">Iterable</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="n">T</span><span class="p">]]</span>

<span class="k">def</span> <span class="nf">inproduct</span><span class="p">[</span><span class="n">T</span><span class="p">:</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">,</span> <span class="nb">complex</span><span class="p">)](</span><span class="n">v</span><span class="p">:</span> <span class="n">Vec</span><span class="p">[</span><span class="n">T</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">T</span><span class="p">:</span> <span class="c1"># Same as Iterable[tuple[T, T]]</span>
    <span class="k">return</span> <span class="nb">sum</span><span class="p">(</span><span class="n">x</span><span class="o">*</span><span class="n">y</span> <span class="k">for</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="n">v</span><span class="p">)</span>
</pre></div>
</div>
<p>For backward compatibility, generic type aliases can also be created
through a simple assignment:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Iterable</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span>

<span class="n">S</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;S&quot;</span><span class="p">)</span>
<span class="n">Response</span> <span class="o">=</span> <span class="n">Iterable</span><span class="p">[</span><span class="n">S</span><span class="p">]</span> <span class="o">|</span> <span class="nb">int</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> no longer has a custom metaclass.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Syntactic support for generics and type aliases is new in version 3.12.
Previously, generic classes had to explicitly inherit from <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a>
or contain a type variable in one of their bases.</p>
</div>
<p>User-defined generics for parameter expressions are also supported via parameter
specification variables in the form <code class="docutils literal notranslate"><span class="pre">[**P]</span></code>.  The behavior is consistent
with type variables’ described above as parameter specification variables are
treated by the typing module as a specialized type variable.  The one exception
to this is that a list of types can be used to substitute a <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Z</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="o">**</span><span class="n">P</span><span class="p">]:</span> <span class="o">...</span>  <span class="c1"># T is a TypeVar; P is a ParamSpec</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Z</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="p">[</span><span class="nb">dict</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span>
<span class="go">__main__.Z[int, [dict, float]]</span>
</pre></div>
</div>
<p>Classes generic over a <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> can also be created using explicit
inheritance from <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a>. In this case, <code class="docutils literal notranslate"><span class="pre">**</span></code> is not used:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">ParamSpec</span><span class="p">,</span> <span class="n">Generic</span>

<span class="n">P</span> <span class="o">=</span> <span class="n">ParamSpec</span><span class="p">(</span><span class="s1">&#39;P&#39;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">Z</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">P</span><span class="p">]):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Another difference between <a class="reference internal" href="#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a> and <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> is that a
generic with only one parameter specification variable will accept
parameter lists in the forms <code class="docutils literal notranslate"><span class="pre">X[[Type1,</span> <span class="pre">Type2,</span> <span class="pre">...]]</span></code> and also
<code class="docutils literal notranslate"><span class="pre">X[Type1,</span> <span class="pre">Type2,</span> <span class="pre">...]</span></code> for aesthetic reasons.  Internally, the latter is converted
to the former, so the following are equivalent:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">X</span><span class="p">[</span><span class="o">**</span><span class="n">P</span><span class="p">]:</span> <span class="o">...</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">X</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span>
<span class="go">__main__.X[[int, str]]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">X</span><span class="p">[[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span>
<span class="go">__main__.X[[int, str]]</span>
</pre></div>
</div>
<p>Note that generics with <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> may not have correct
<code class="docutils literal notranslate"><span class="pre">__parameters__</span></code> after substitution in some cases because they
are intended primarily for static type checking.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> can now be parameterized over parameter expressions.
See <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> and <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0612/"><strong>PEP 612</strong></a> for more details.</p>
</div>
<p>A user-defined generic class can have ABCs as base classes without a metaclass
conflict. Generic metaclasses are not supported. The outcome of parameterizing
generics is cached, and most types in the typing module are <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a> and
comparable for equality.</p>
</section>
<section id="the-any-type">
<h2>The <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> type<a class="headerlink" href="#the-any-type" title="Link to this heading">¶</a></h2>
<p>A special kind of type is <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>. A static type checker will treat
every type as being compatible with <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> and <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> as being
compatible with every type.</p>
<p>This means that it is possible to perform any operation or method call on a
value of type <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> and assign it to any variable:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Any</span>

<span class="n">a</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="kc">None</span>
<span class="n">a</span> <span class="o">=</span> <span class="p">[]</span>          <span class="c1"># OK</span>
<span class="n">a</span> <span class="o">=</span> <span class="mi">2</span>           <span class="c1"># OK</span>

<span class="n">s</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span>
<span class="n">s</span> <span class="o">=</span> <span class="n">a</span>           <span class="c1"># OK</span>

<span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">item</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
    <span class="c1"># Passes type checking; &#39;item&#39; could be any type,</span>
    <span class="c1"># and that type might have a &#39;bar&#39; method</span>
    <span class="n">item</span><span class="o">.</span><span class="n">bar</span><span class="p">()</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Notice that no type checking is performed when assigning a value of type
<a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> to a more precise type. For example, the static type checker did
not report an error when assigning <code class="docutils literal notranslate"><span class="pre">a</span></code> to <code class="docutils literal notranslate"><span class="pre">s</span></code> even though <code class="docutils literal notranslate"><span class="pre">s</span></code> was
declared to be of type <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> and receives an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> value at
runtime!</p>
<p>Furthermore, all functions without a return type or parameter types will
implicitly default to using <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">legacy_parser</span><span class="p">(</span><span class="n">text</span><span class="p">):</span>
    <span class="o">...</span>
    <span class="k">return</span> <span class="n">data</span>

<span class="c1"># A static type checker will treat the above</span>
<span class="c1"># as having the same signature as:</span>
<span class="k">def</span> <span class="nf">legacy_parser</span><span class="p">(</span><span class="n">text</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Any</span><span class="p">:</span>
    <span class="o">...</span>
    <span class="k">return</span> <span class="n">data</span>
</pre></div>
</div>
<p>This behavior allows <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> to be used as an <em>escape hatch</em> when you
need to mix dynamically and statically typed code.</p>
<p>Contrast the behavior of <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> with the behavior of <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>.
Similar to <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>, every type is a subtype of <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>. However,
unlike <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>, the reverse is not true: <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> is <em>not</em> a
subtype of every other type.</p>
<p>That means when the type of a value is <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>, a type checker will
reject almost all operations on it, and assigning it to a variable (or using
it as a return value) of a more specialized type is a type error. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">hash_a</span><span class="p">(</span><span class="n">item</span><span class="p">:</span> <span class="nb">object</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
    <span class="c1"># Fails type checking; an object does not have a &#39;magic&#39; method.</span>
    <span class="n">item</span><span class="o">.</span><span class="n">magic</span><span class="p">()</span>
    <span class="o">...</span>

<span class="k">def</span> <span class="nf">hash_b</span><span class="p">(</span><span class="n">item</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
    <span class="c1"># Passes type checking</span>
    <span class="n">item</span><span class="o">.</span><span class="n">magic</span><span class="p">()</span>
    <span class="o">...</span>

<span class="c1"># Passes type checking, since ints and strs are subclasses of object</span>
<span class="n">hash_a</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
<span class="n">hash_a</span><span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">)</span>

<span class="c1"># Passes type checking, since Any is compatible with all types</span>
<span class="n">hash_b</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
<span class="n">hash_b</span><span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Use <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> to indicate that a value could be any type in a typesafe
manner. Use <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> to indicate that a value is dynamically typed.</p>
</section>
<section id="nominal-vs-structural-subtyping">
<h2>Nominal vs structural subtyping<a class="headerlink" href="#nominal-vs-structural-subtyping" title="Link to this heading">¶</a></h2>
<p>Initially <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0484/"><strong>PEP 484</strong></a> defined the Python static type system as using
<em>nominal subtyping</em>. This means that a class <code class="docutils literal notranslate"><span class="pre">A</span></code> is allowed where
a class <code class="docutils literal notranslate"><span class="pre">B</span></code> is expected if and only if <code class="docutils literal notranslate"><span class="pre">A</span></code> is a subclass of <code class="docutils literal notranslate"><span class="pre">B</span></code>.</p>
<p>This requirement previously also applied to abstract base classes, such as
<a class="reference internal" href="collections.abc.html#collections.abc.Iterable" title="collections.abc.Iterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">Iterable</span></code></a>. The problem with this approach is that a class had
to be explicitly marked to support them, which is unpythonic and unlike
what one would normally do in idiomatic dynamically typed Python code.
For example, this conforms to <span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0484/"><strong>PEP 484</strong></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Sized</span><span class="p">,</span> <span class="n">Iterable</span><span class="p">,</span> <span class="n">Iterator</span>

<span class="k">class</span> <span class="nc">Bucket</span><span class="p">(</span><span class="n">Sized</span><span class="p">,</span> <span class="n">Iterable</span><span class="p">[</span><span class="nb">int</span><span class="p">]):</span>
    <span class="o">...</span>
    <span class="k">def</span> <span class="fm">__len__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span> <span class="o">...</span>
    <span class="k">def</span> <span class="fm">__iter__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Iterator</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span> <span class="o">...</span>
</pre></div>
</div>
<p><span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0544/"><strong>PEP 544</strong></a> allows to solve this problem by allowing users to write
the above code without explicit base classes in the class definition,
allowing <code class="docutils literal notranslate"><span class="pre">Bucket</span></code> to be implicitly considered a subtype of both <code class="docutils literal notranslate"><span class="pre">Sized</span></code>
and <code class="docutils literal notranslate"><span class="pre">Iterable[int]</span></code> by static type checkers. This is known as
<em>structural subtyping</em> (or static duck-typing):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Iterator</span><span class="p">,</span> <span class="n">Iterable</span>

<span class="k">class</span> <span class="nc">Bucket</span><span class="p">:</span>  <span class="c1"># Note: no base classes</span>
    <span class="o">...</span>
    <span class="k">def</span> <span class="fm">__len__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span> <span class="o">...</span>
    <span class="k">def</span> <span class="fm">__iter__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Iterator</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span> <span class="o">...</span>

<span class="k">def</span> <span class="nf">collect</span><span class="p">(</span><span class="n">items</span><span class="p">:</span> <span class="n">Iterable</span><span class="p">[</span><span class="nb">int</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span> <span class="o">...</span>
<span class="n">result</span> <span class="o">=</span> <span class="n">collect</span><span class="p">(</span><span class="n">Bucket</span><span class="p">())</span>  <span class="c1"># Passes type check</span>
</pre></div>
</div>
<p>Moreover, by subclassing a special class <a class="reference internal" href="#typing.Protocol" title="typing.Protocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">Protocol</span></code></a>, a user
can define new custom protocols to fully enjoy structural subtyping
(see examples below).</p>
</section>
<section id="module-contents">
<h2>Module contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">typing</span></code> module defines the following classes, functions and decorators.</p>
<section id="special-typing-primitives">
<h3>Special typing primitives<a class="headerlink" href="#special-typing-primitives" title="Link to this heading">¶</a></h3>
<section id="special-types">
<h4>Special types<a class="headerlink" href="#special-types" title="Link to this heading">¶</a></h4>
<p>These can be used as types in annotations. They do not support subscription
using <code class="docutils literal notranslate"><span class="pre">[]</span></code>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="typing.Any">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Any</span></span><a class="headerlink" href="#typing.Any" title="Link to this definition">¶</a></dt>
<dd><p>Special type indicating an unconstrained type.</p>
<ul class="simple">
<li><p>Every type is compatible with <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>.</p></li>
<li><p><a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> is compatible with every type.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a> can now be used as a base class. This can be useful for
avoiding type checker errors with classes that can duck type anywhere or
are highly dynamic.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.AnyStr">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">AnyStr</span></span><a class="headerlink" href="#typing.AnyStr" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#typing-constrained-typevar"><span class="std std-ref">constrained type variable</span></a>.</p>
<p>Definition:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">AnyStr</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;AnyStr&#39;</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">AnyStr</span></code> is meant to be used for functions that may accept <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> or
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> arguments but cannot allow the two to mix.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">concat</span><span class="p">(</span><span class="n">a</span><span class="p">:</span> <span class="n">AnyStr</span><span class="p">,</span> <span class="n">b</span><span class="p">:</span> <span class="n">AnyStr</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AnyStr</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">a</span> <span class="o">+</span> <span class="n">b</span>

<span class="n">concat</span><span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="s2">&quot;bar&quot;</span><span class="p">)</span>    <span class="c1"># OK, output has type &#39;str&#39;</span>
<span class="n">concat</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="sa">b</span><span class="s2">&quot;bar&quot;</span><span class="p">)</span>  <span class="c1"># OK, output has type &#39;bytes&#39;</span>
<span class="n">concat</span><span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="sa">b</span><span class="s2">&quot;bar&quot;</span><span class="p">)</span>   <span class="c1"># Error, cannot mix str and bytes</span>
</pre></div>
</div>
<p>Note that, despite its name, <code class="docutils literal notranslate"><span class="pre">AnyStr</span></code> has nothing to do with the
<a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-class docutils literal notranslate"><span class="pre">Any</span></code></a> type, nor does it mean “any string”. In particular, <code class="docutils literal notranslate"><span class="pre">AnyStr</span></code>
and <code class="docutils literal notranslate"><span class="pre">str</span> <span class="pre">|</span> <span class="pre">bytes</span></code> are different from each other and have different use
cases:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Invalid use of AnyStr:</span>
<span class="c1"># The type variable is used only once in the function signature,</span>
<span class="c1"># so cannot be &quot;solved&quot; by the type checker</span>
<span class="k">def</span> <span class="nf">greet_bad</span><span class="p">(</span><span class="n">cond</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AnyStr</span><span class="p">:</span>
    <span class="k">return</span> <span class="s2">&quot;hi there!&quot;</span> <span class="k">if</span> <span class="n">cond</span> <span class="k">else</span> <span class="sa">b</span><span class="s2">&quot;greetings!&quot;</span>

<span class="c1"># The better way of annotating this function:</span>
<span class="k">def</span> <span class="nf">greet_proper</span><span class="p">(</span><span class="n">cond</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span> <span class="o">|</span> <span class="nb">bytes</span><span class="p">:</span>
    <span class="k">return</span> <span class="s2">&quot;hi there!&quot;</span> <span class="k">if</span> <span class="n">cond</span> <span class="k">else</span> <span class="sa">b</span><span class="s2">&quot;greetings!&quot;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.LiteralString">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">LiteralString</span></span><a class="headerlink" href="#typing.LiteralString" title="Link to this definition">¶</a></dt>
<dd><p>Special type that includes only literal strings.</p>
<p>Any string
literal is compatible with <code class="docutils literal notranslate"><span class="pre">LiteralString</span></code>, as is another
<code class="docutils literal notranslate"><span class="pre">LiteralString</span></code>. However, an object typed as just <code class="docutils literal notranslate"><span class="pre">str</span></code> is not.
A string created by composing <code class="docutils literal notranslate"><span class="pre">LiteralString</span></code>-typed objects
is also acceptable as a <code class="docutils literal notranslate"><span class="pre">LiteralString</span></code>.</p>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">run_query</span><span class="p">(</span><span class="n">sql</span><span class="p">:</span> <span class="n">LiteralString</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>

<span class="k">def</span> <span class="nf">caller</span><span class="p">(</span><span class="n">arbitrary_string</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">literal_string</span><span class="p">:</span> <span class="n">LiteralString</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="n">run_query</span><span class="p">(</span><span class="s2">&quot;SELECT * FROM students&quot;</span><span class="p">)</span>  <span class="c1"># OK</span>
    <span class="n">run_query</span><span class="p">(</span><span class="n">literal_string</span><span class="p">)</span>  <span class="c1"># OK</span>
    <span class="n">run_query</span><span class="p">(</span><span class="s2">&quot;SELECT * FROM &quot;</span> <span class="o">+</span> <span class="n">literal_string</span><span class="p">)</span>  <span class="c1"># OK</span>
    <span class="n">run_query</span><span class="p">(</span><span class="n">arbitrary_string</span><span class="p">)</span>  <span class="c1"># type checker error</span>
    <span class="n">run_query</span><span class="p">(</span>  <span class="c1"># type checker error</span>
        <span class="sa">f</span><span class="s2">&quot;SELECT * FROM students WHERE name = </span><span class="si">{</span><span class="n">arbitrary_string</span><span class="si">}</span><span class="s2">&quot;</span>
    <span class="p">)</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">LiteralString</span></code> is useful for sensitive APIs where arbitrary user-generated
strings could generate problems. For example, the two cases above
that generate type checker errors could be vulnerable to an SQL
injection attack.</p>
<p>See <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0675/"><strong>PEP 675</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Never">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Never</span></span><a class="headerlink" href="#typing.Never" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://en.wikipedia.org/wiki/Bottom_type">bottom type</a>,
a type that has no members.</p>
<p>This can be used to define a function that should never be
called, or a function that never returns:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Never</span>

<span class="k">def</span> <span class="nf">never_call_me</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="n">Never</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="k">pass</span>

<span class="k">def</span> <span class="nf">int_or_str</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="nb">int</span> <span class="o">|</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="n">never_call_me</span><span class="p">(</span><span class="n">arg</span><span class="p">)</span>  <span class="c1"># type checker error</span>
    <span class="k">match</span> <span class="n">arg</span><span class="p">:</span>
        <span class="k">case</span> <span class="nb">int</span><span class="p">():</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;It&#39;s an int&quot;</span><span class="p">)</span>
        <span class="k">case</span> <span class="nb">str</span><span class="p">():</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;It&#39;s a str&quot;</span><span class="p">)</span>
        <span class="k">case</span><span class="w"> </span><span class="k">_</span><span class="p">:</span>
            <span class="n">never_call_me</span><span class="p">(</span><span class="n">arg</span><span class="p">)</span>  <span class="c1"># OK, arg is of type Never</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>On older Python versions, <a class="reference internal" href="#typing.NoReturn" title="typing.NoReturn"><code class="xref py py-data docutils literal notranslate"><span class="pre">NoReturn</span></code></a> may be used to express the
same concept. <code class="docutils literal notranslate"><span class="pre">Never</span></code> was added to make the intended meaning more explicit.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.NoReturn">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">NoReturn</span></span><a class="headerlink" href="#typing.NoReturn" title="Link to this definition">¶</a></dt>
<dd><p>Special type indicating that a function never returns.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">NoReturn</span>

<span class="k">def</span> <span class="nf">stop</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">NoReturn</span><span class="p">:</span>
    <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s1">&#39;no way&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">NoReturn</span></code> can also be used as a
<a class="reference external" href="https://en.wikipedia.org/wiki/Bottom_type">bottom type</a>, a type that
has no values. Starting in Python 3.11, the <a class="reference internal" href="#typing.Never" title="typing.Never"><code class="xref py py-data docutils literal notranslate"><span class="pre">Never</span></code></a> type should
be used for this concept instead. Type checkers should treat the two
equivalently.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Self">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Self</span></span><a class="headerlink" href="#typing.Self" title="Link to this definition">¶</a></dt>
<dd><p>Special type to represent the current enclosed class.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Self</span><span class="p">,</span> <span class="n">reveal_type</span>

<span class="k">class</span> <span class="nc">Foo</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">return_self</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Self</span><span class="p">:</span>
        <span class="o">...</span>
        <span class="k">return</span> <span class="bp">self</span>

<span class="k">class</span> <span class="nc">SubclassOfFoo</span><span class="p">(</span><span class="n">Foo</span><span class="p">):</span> <span class="k">pass</span>

<span class="n">reveal_type</span><span class="p">(</span><span class="n">Foo</span><span class="p">()</span><span class="o">.</span><span class="n">return_self</span><span class="p">())</span>  <span class="c1"># Revealed type is &quot;Foo&quot;</span>
<span class="n">reveal_type</span><span class="p">(</span><span class="n">SubclassOfFoo</span><span class="p">()</span><span class="o">.</span><span class="n">return_self</span><span class="p">())</span>  <span class="c1"># Revealed type is &quot;SubclassOfFoo&quot;</span>
</pre></div>
</div>
<p>This annotation is semantically equivalent to the following,
albeit in a more succinct fashion:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span>

<span class="n">Self</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;Self&quot;</span><span class="p">,</span> <span class="n">bound</span><span class="o">=</span><span class="s2">&quot;Foo&quot;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">Foo</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">return_self</span><span class="p">(</span><span class="bp">self</span><span class="p">:</span> <span class="n">Self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Self</span><span class="p">:</span>
        <span class="o">...</span>
        <span class="k">return</span> <span class="bp">self</span>
</pre></div>
</div>
<p>In general, if something returns <code class="docutils literal notranslate"><span class="pre">self</span></code>, as in the above examples, you
should use <code class="docutils literal notranslate"><span class="pre">Self</span></code> as the return annotation. If <code class="docutils literal notranslate"><span class="pre">Foo.return_self</span></code> was
annotated as returning <code class="docutils literal notranslate"><span class="pre">&quot;Foo&quot;</span></code>, then the type checker would infer the
object returned from <code class="docutils literal notranslate"><span class="pre">SubclassOfFoo.return_self</span></code> as being of type <code class="docutils literal notranslate"><span class="pre">Foo</span></code>
rather than <code class="docutils literal notranslate"><span class="pre">SubclassOfFoo</span></code>.</p>
<p>Other common use cases include:</p>
<ul class="simple">
<li><p><a class="reference internal" href="functions.html#classmethod" title="classmethod"><code class="xref py py-class docutils literal notranslate"><span class="pre">classmethod</span></code></a>s that are used as alternative constructors and return instances
of the <code class="docutils literal notranslate"><span class="pre">cls</span></code> parameter.</p></li>
<li><p>Annotating an <a class="reference internal" href="../reference/datamodel.html#object.__enter__" title="object.__enter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__enter__()</span></code></a> method which returns self.</p></li>
</ul>
<p>You should not use <code class="docutils literal notranslate"><span class="pre">Self</span></code> as the return annotation if the method is not
guaranteed to return an instance of a subclass when the class is
subclassed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Eggs</span><span class="p">:</span>
    <span class="c1"># Self would be an incorrect return annotation here,</span>
    <span class="c1"># as the object returned is always an instance of Eggs,</span>
    <span class="c1"># even in subclasses</span>
    <span class="k">def</span> <span class="nf">returns_eggs</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="s2">&quot;Eggs&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Eggs</span><span class="p">()</span>
</pre></div>
</div>
<p>See <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0673/"><strong>PEP 673</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.TypeAlias">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TypeAlias</span></span><a class="headerlink" href="#typing.TypeAlias" title="Link to this definition">¶</a></dt>
<dd><p>Special annotation for explicitly declaring a <a class="reference internal" href="#type-aliases"><span class="std std-ref">type alias</span></a>.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeAlias</span>

<span class="n">Factors</span><span class="p">:</span> <span class="n">TypeAlias</span> <span class="o">=</span> <span class="nb">list</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">TypeAlias</span></code> is particularly useful on older Python versions for annotating
aliases that make use of forward references, as it can be hard for type
checkers to distinguish these from normal variable assignments:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Generic</span><span class="p">,</span> <span class="n">TypeAlias</span><span class="p">,</span> <span class="n">TypeVar</span>

<span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>

<span class="c1"># &quot;Box&quot; does not exist yet,</span>
<span class="c1"># so we have to use quotes for the forward reference on Python &lt;3.12.</span>
<span class="c1"># Using ``TypeAlias`` tells the type checker that this is a type alias declaration,</span>
<span class="c1"># not a variable assignment to a string.</span>
<span class="n">BoxOfStrings</span><span class="p">:</span> <span class="n">TypeAlias</span> <span class="o">=</span> <span class="s2">&quot;Box[str]&quot;</span>

<span class="k">class</span> <span class="nc">Box</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">make_box_of_strings</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BoxOfStrings</span><span class="p">:</span> <span class="o">...</span>
</pre></div>
</div>
<p>See <span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0613/"><strong>PEP 613</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12: </span><a class="reference internal" href="#typing.TypeAlias" title="typing.TypeAlias"><code class="xref py py-data docutils literal notranslate"><span class="pre">TypeAlias</span></code></a> is deprecated in favor of the <a class="reference internal" href="../reference/simple_stmts.html#type"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">type</span></code></a> statement,
which creates instances of <a class="reference internal" href="#typing.TypeAliasType" title="typing.TypeAliasType"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeAliasType</span></code></a>
and which natively supports forward references.
Note that while <a class="reference internal" href="#typing.TypeAlias" title="typing.TypeAlias"><code class="xref py py-data docutils literal notranslate"><span class="pre">TypeAlias</span></code></a> and <a class="reference internal" href="#typing.TypeAliasType" title="typing.TypeAliasType"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeAliasType</span></code></a> serve
similar purposes and have similar names, they are distinct and the
latter is not the type of the former.
Removal of <a class="reference internal" href="#typing.TypeAlias" title="typing.TypeAlias"><code class="xref py py-data docutils literal notranslate"><span class="pre">TypeAlias</span></code></a> is not currently planned, but users
are encouraged to migrate to <a class="reference internal" href="../reference/simple_stmts.html#type"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">type</span></code></a> statements.</p>
</div>
</dd></dl>

</section>
<section id="special-forms">
<h4>Special forms<a class="headerlink" href="#special-forms" title="Link to this heading">¶</a></h4>
<p>These can be used as types in annotations. They all support subscription using
<code class="docutils literal notranslate"><span class="pre">[]</span></code>, but each has a unique syntax.</p>
<dl class="py data">
<dt class="sig sig-object py" id="typing.Union">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Union</span></span><a class="headerlink" href="#typing.Union" title="Link to this definition">¶</a></dt>
<dd><p>Union type; <code class="docutils literal notranslate"><span class="pre">Union[X,</span> <span class="pre">Y]</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">X</span> <span class="pre">|</span> <span class="pre">Y</span></code> and means either X or Y.</p>
<p>To define a union, use e.g. <code class="docutils literal notranslate"><span class="pre">Union[int,</span> <span class="pre">str]</span></code> or the shorthand <code class="docutils literal notranslate"><span class="pre">int</span> <span class="pre">|</span> <span class="pre">str</span></code>. Using that shorthand is recommended. Details:</p>
<ul>
<li><p>The arguments must be types and there must be at least one.</p></li>
<li><p>Unions of unions are flattened, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Union</span><span class="p">[</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">],</span> <span class="nb">float</span><span class="p">]</span> <span class="o">==</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span>
</pre></div>
</div>
</li>
<li><p>Unions of a single argument vanish, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">==</span> <span class="nb">int</span>  <span class="c1"># The constructor actually returns int</span>
</pre></div>
</div>
</li>
<li><p>Redundant arguments are skipped, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span> <span class="o">==</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span> <span class="o">==</span> <span class="nb">int</span> <span class="o">|</span> <span class="nb">str</span>
</pre></div>
</div>
</li>
<li><p>When comparing unions, the argument order is ignored, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span> <span class="o">==</span> <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span>
</pre></div>
</div>
</li>
<li><p>You cannot subclass or instantiate a <code class="docutils literal notranslate"><span class="pre">Union</span></code>.</p></li>
<li><p>You cannot write <code class="docutils literal notranslate"><span class="pre">Union[X][Y]</span></code>.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Don’t remove explicit subclasses from unions at runtime.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Unions can now be written as <code class="docutils literal notranslate"><span class="pre">X</span> <span class="pre">|</span> <span class="pre">Y</span></code>. See
<a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">union type expressions</span></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Optional">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Optional</span></span><a class="headerlink" href="#typing.Optional" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Optional[X]</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">X</span> <span class="pre">|</span> <span class="pre">None</span></code> (or <code class="docutils literal notranslate"><span class="pre">Union[X,</span> <span class="pre">None]</span></code>).</p>
<p>Note that this is not the same concept as an optional argument,
which is one that has a default.  An optional argument with a
default does not require the <code class="docutils literal notranslate"><span class="pre">Optional</span></code> qualifier on its type
annotation just because it is optional. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>On the other hand, if an explicit value of <code class="docutils literal notranslate"><span class="pre">None</span></code> is allowed, the
use of <code class="docutils literal notranslate"><span class="pre">Optional</span></code> is appropriate, whether the argument is optional
or not. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Optional can now be written as <code class="docutils literal notranslate"><span class="pre">X</span> <span class="pre">|</span> <span class="pre">None</span></code>. See
<a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">union type expressions</span></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Concatenate">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Concatenate</span></span><a class="headerlink" href="#typing.Concatenate" title="Link to this definition">¶</a></dt>
<dd><p>Special form for annotating higher-order functions.</p>
<p><code class="docutils literal notranslate"><span class="pre">Concatenate</span></code> can be used in conjunction with <a class="reference internal" href="#annotating-callables"><span class="std std-ref">Callable</span></a> and
<a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> to annotate a higher-order callable which adds, removes,
or transforms parameters of another
callable.  Usage is in the form
<code class="docutils literal notranslate"><span class="pre">Concatenate[Arg1Type,</span> <span class="pre">Arg2Type,</span> <span class="pre">...,</span> <span class="pre">ParamSpecVariable]</span></code>. <code class="docutils literal notranslate"><span class="pre">Concatenate</span></code>
is currently only valid when used as the first argument to a <a class="reference internal" href="#annotating-callables"><span class="std std-ref">Callable</span></a>.
The last parameter to <code class="docutils literal notranslate"><span class="pre">Concatenate</span></code> must be a <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> or
ellipsis (<code class="docutils literal notranslate"><span class="pre">...</span></code>).</p>
<p>For example, to annotate a decorator <code class="docutils literal notranslate"><span class="pre">with_lock</span></code> which provides a
<a class="reference internal" href="threading.html#threading.Lock" title="threading.Lock"><code class="xref py py-class docutils literal notranslate"><span class="pre">threading.Lock</span></code></a> to the decorated function,  <code class="docutils literal notranslate"><span class="pre">Concatenate</span></code> can be
used to indicate that <code class="docutils literal notranslate"><span class="pre">with_lock</span></code> expects a callable which takes in a
<code class="docutils literal notranslate"><span class="pre">Lock</span></code> as the first argument, and returns a callable with a different type
signature.  In this case, the <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> indicates that the returned
callable’s parameter types are dependent on the parameter types of the
callable being passed in:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Callable</span>
<span class="kn">from</span> <span class="nn">threading</span> <span class="kn">import</span> <span class="n">Lock</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Concatenate</span>

<span class="c1"># Use this lock to ensure that only one thread is executing a function</span>
<span class="c1"># at any time.</span>
<span class="n">my_lock</span> <span class="o">=</span> <span class="n">Lock</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">with_lock</span><span class="p">[</span><span class="o">**</span><span class="n">P</span><span class="p">,</span> <span class="n">R</span><span class="p">](</span><span class="n">f</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[</span><span class="n">Concatenate</span><span class="p">[</span><span class="n">Lock</span><span class="p">,</span> <span class="n">P</span><span class="p">],</span> <span class="n">R</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">Callable</span><span class="p">[</span><span class="n">P</span><span class="p">,</span> <span class="n">R</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&#39;&#39;&#39;A type-safe decorator which provides a lock.&#39;&#39;&#39;</span>
    <span class="k">def</span> <span class="nf">inner</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">:</span> <span class="n">P</span><span class="o">.</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">:</span> <span class="n">P</span><span class="o">.</span><span class="n">kwargs</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">R</span><span class="p">:</span>
        <span class="c1"># Provide the lock as the first argument.</span>
        <span class="k">return</span> <span class="n">f</span><span class="p">(</span><span class="n">my_lock</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">inner</span>

<span class="nd">@with_lock</span>
<span class="k">def</span> <span class="nf">sum_threadsafe</span><span class="p">(</span><span class="n">lock</span><span class="p">:</span> <span class="n">Lock</span><span class="p">,</span> <span class="n">numbers</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">float</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&#39;&#39;&#39;Add a list of numbers together in a thread-safe manner.&#39;&#39;&#39;</span>
    <span class="k">with</span> <span class="n">lock</span><span class="p">:</span>
        <span class="k">return</span> <span class="nb">sum</span><span class="p">(</span><span class="n">numbers</span><span class="p">)</span>

<span class="c1"># We don&#39;t need to pass in the lock ourselves thanks to the decorator.</span>
<span class="n">sum_threadsafe</span><span class="p">([</span><span class="mf">1.1</span><span class="p">,</span> <span class="mf">2.2</span><span class="p">,</span> <span class="mf">3.3</span><span class="p">])</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0612/"><strong>PEP 612</strong></a> – Parameter Specification Variables (the PEP which introduced
<code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code> and <code class="docutils literal notranslate"><span class="pre">Concatenate</span></code>)</p></li>
<li><p><a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a></p></li>
<li><p><a class="reference internal" href="#annotating-callables"><span class="std std-ref">Annotating callable objects</span></a></p></li>
</ul>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Literal">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Literal</span></span><a class="headerlink" href="#typing.Literal" title="Link to this definition">¶</a></dt>
<dd><p>Special typing form to define “literal types”.</p>
<p><code class="docutils literal notranslate"><span class="pre">Literal</span></code> can be used to indicate to type checkers that the
annotated object has a value equivalent to one of the
provided literals.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">validate_simple</span><span class="p">(</span><span class="n">data</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Literal</span><span class="p">[</span><span class="kc">True</span><span class="p">]:</span>  <span class="c1"># always returns True</span>
    <span class="o">...</span>

<span class="nb">type</span> <span class="n">Mode</span> <span class="o">=</span> <span class="n">Literal</span><span class="p">[</span><span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">]</span>
<span class="k">def</span> <span class="nf">open_helper</span><span class="p">(</span><span class="n">file</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">mode</span><span class="p">:</span> <span class="n">Mode</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
    <span class="o">...</span>

<span class="n">open_helper</span><span class="p">(</span><span class="s1">&#39;/some/path&#39;</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span>      <span class="c1"># Passes type check</span>
<span class="n">open_helper</span><span class="p">(</span><span class="s1">&#39;/other/path&#39;</span><span class="p">,</span> <span class="s1">&#39;typo&#39;</span><span class="p">)</span>  <span class="c1"># Error in type checker</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Literal[...]</span></code> cannot be subclassed. At runtime, an arbitrary value
is allowed as type argument to <code class="docutils literal notranslate"><span class="pre">Literal[...]</span></code>, but type checkers may
impose restrictions. See <span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0586/"><strong>PEP 586</strong></a> for more details about literal types.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9.1: </span><code class="docutils literal notranslate"><span class="pre">Literal</span></code> now de-duplicates parameters.  Equality comparisons of
<code class="docutils literal notranslate"><span class="pre">Literal</span></code> objects are no longer order dependent. <code class="docutils literal notranslate"><span class="pre">Literal</span></code> objects
will now raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception during equality comparisons
if one of their parameters are not <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.ClassVar">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ClassVar</span></span><a class="headerlink" href="#typing.ClassVar" title="Link to this definition">¶</a></dt>
<dd><p>Special type construct to mark class variables.</p>
<p>As introduced in <span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-0526/"><strong>PEP 526</strong></a>, a variable annotation wrapped in ClassVar
indicates that a given attribute is intended to be used as a class variable
and should not be set on instances of that class. Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Starship</span><span class="p">:</span>
    <span class="n">stats</span><span class="p">:</span> <span class="n">ClassVar</span><span class="p">[</span><span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]</span> <span class="o">=</span> <span class="p">{}</span> <span class="c1"># class variable</span>
    <span class="n">damage</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">10</span>                     <span class="c1"># instance variable</span>
</pre></div>
</div>
<p><a class="reference internal" href="#typing.ClassVar" title="typing.ClassVar"><code class="xref py py-data docutils literal notranslate"><span class="pre">ClassVar</span></code></a> accepts only types and cannot be further subscribed.</p>
<p><a class="reference internal" href="#typing.ClassVar" title="typing.ClassVar"><code class="xref py py-data docutils literal notranslate"><span class="pre">ClassVar</span></code></a> is not a class itself, and should not
be used with <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> or <a class="reference internal" href="functions.html#issubclass" title="issubclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">issubclass()</span></code></a>.
<a class="reference internal" href="#typing.ClassVar" title="typing.ClassVar"><code class="xref py py-data docutils literal notranslate"><span class="pre">ClassVar</span></code></a> does not change Python runtime behavior, but
it can be used by third-party type checkers. For example, a type checker
might flag the following code as an error:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">enterprise_d</span> <span class="o">=</span> <span class="n">Starship</span><span class="p">(</span><span class="mi">3000</span><span class="p">)</span>
<span class="n">enterprise_d</span><span class="o">.</span><span class="n">stats</span> <span class="o">=</span> <span class="p">{}</span> <span class="c1"># Error, setting class variable on instance</span>
<span class="n">Starship</span><span class="o">.</span><span class="n">stats</span> <span class="o">=</span> <span class="p">{}</span>     <span class="c1"># This is OK</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Final">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Final</span></span><a class="headerlink" href="#typing.Final" title="Link to this definition">¶</a></dt>
<dd><p>Special typing construct to indicate final names to type checkers.</p>
<p>Final names cannot be reassigned in any scope. Final names declared in class
scopes cannot be overridden in subclasses.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">MAX_SIZE</span><span class="p">:</span> <span class="n">Final</span> <span class="o">=</span> <span class="mi">9000</span>
<span class="n">MAX_SIZE</span> <span class="o">+=</span> <span class="mi">1</span>  <span class="c1"># Error reported by type checker</span>

<span class="k">class</span> <span class="nc">Connection</span><span class="p">:</span>
    <span class="n">TIMEOUT</span><span class="p">:</span> <span class="n">Final</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mi">10</span>

<span class="k">class</span> <span class="nc">FastConnector</span><span class="p">(</span><span class="n">Connection</span><span class="p">):</span>
    <span class="n">TIMEOUT</span> <span class="o">=</span> <span class="mi">1</span>  <span class="c1"># Error reported by type checker</span>
</pre></div>
</div>
<p>There is no runtime checking of these properties. See <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0591/"><strong>PEP 591</strong></a> for
more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Required">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Required</span></span><a class="headerlink" href="#typing.Required" title="Link to this definition">¶</a></dt>
<dd><p>Special typing construct to mark a <a class="reference internal" href="#typing.TypedDict" title="typing.TypedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></a> key as required.</p>
<p>This is mainly useful for <code class="docutils literal notranslate"><span class="pre">total=False</span></code> TypedDicts. See <a class="reference internal" href="#typing.TypedDict" title="typing.TypedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></a>
and <span class="target" id="index-13"></span><a class="pep reference external" href="https://peps.python.org/pep-0655/"><strong>PEP 655</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.NotRequired">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">NotRequired</span></span><a class="headerlink" href="#typing.NotRequired" title="Link to this definition">¶</a></dt>
<dd><p>Special typing construct to mark a <a class="reference internal" href="#typing.TypedDict" title="typing.TypedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></a> key as potentially
missing.</p>
<p>See <a class="reference internal" href="#typing.TypedDict" title="typing.TypedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></a> and <span class="target" id="index-14"></span><a class="pep reference external" href="https://peps.python.org/pep-0655/"><strong>PEP 655</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Annotated">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Annotated</span></span><a class="headerlink" href="#typing.Annotated" title="Link to this definition">¶</a></dt>
<dd><p>Special typing form to add context-specific metadata to an annotation.</p>
<p>Add metadata <code class="docutils literal notranslate"><span class="pre">x</span></code> to a given type <code class="docutils literal notranslate"><span class="pre">T</span></code> by using the annotation
<code class="docutils literal notranslate"><span class="pre">Annotated[T,</span> <span class="pre">x]</span></code>. Metadata added using <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> can be used by
static analysis tools or at runtime. At runtime, the metadata is stored
in a <code class="xref py py-attr docutils literal notranslate"><span class="pre">__metadata__</span></code> attribute.</p>
<p>If a library or tool encounters an annotation <code class="docutils literal notranslate"><span class="pre">Annotated[T,</span> <span class="pre">x]</span></code> and has
no special logic for the metadata, it should ignore the metadata and simply
treat the annotation as <code class="docutils literal notranslate"><span class="pre">T</span></code>. As such, <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> can be useful for code
that wants to use annotations for purposes outside Python’s static typing
system.</p>
<p>Using <code class="docutils literal notranslate"><span class="pre">Annotated[T,</span> <span class="pre">x]</span></code> as an annotation still allows for static
typechecking of <code class="docutils literal notranslate"><span class="pre">T</span></code>, as type checkers will simply ignore the metadata <code class="docutils literal notranslate"><span class="pre">x</span></code>.
In this way, <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> differs from the
<a class="reference internal" href="#typing.no_type_check" title="typing.no_type_check"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;no_type_check</span></code></a> decorator, which can also be used for
adding annotations outside the scope of the typing system, but
completely disables typechecking for a function or class.</p>
<p>The responsibility of how to interpret the metadata
lies with the tool or library encountering an
<code class="docutils literal notranslate"><span class="pre">Annotated</span></code> annotation. A tool or library encountering an <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> type
can scan through the metadata elements to determine if they are of interest
(e.g., using <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a>).</p>
<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">Annotated[&lt;type&gt;,</span> <span class="pre">&lt;metadata&gt;]</span></span></dt>
<dd></dd></dl>

<p>Here is an example of how you might use <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> to add metadata to
type annotations if you were doing range analysis:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ValueRange</span><span class="p">:</span>
    <span class="n">lo</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">hi</span><span class="p">:</span> <span class="nb">int</span>

<span class="n">T1</span> <span class="o">=</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="o">-</span><span class="mi">10</span><span class="p">,</span> <span class="mi">5</span><span class="p">)]</span>
<span class="n">T2</span> <span class="o">=</span> <span class="n">Annotated</span><span class="p">[</span><span class="n">T1</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="o">-</span><span class="mi">20</span><span class="p">,</span> <span class="mi">3</span><span class="p">)]</span>
</pre></div>
</div>
<p>Details of the syntax:</p>
<ul>
<li><p>The first argument to <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> must be a valid type</p></li>
<li><p>Multiple metadata elements can be supplied (<code class="docutils literal notranslate"><span class="pre">Annotated</span></code> supports variadic
arguments):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ctype</span><span class="p">:</span>
    <span class="n">kind</span><span class="p">:</span> <span class="nb">str</span>

<span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="n">ctype</span><span class="p">(</span><span class="s2">&quot;char&quot;</span><span class="p">)]</span>
</pre></div>
</div>
<p>It is up to the tool consuming the annotations to decide whether the
client is allowed to add multiple metadata elements to one annotation and how to
merge those annotations.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">Annotated</span></code> must be subscripted with at least two arguments (
<code class="docutils literal notranslate"><span class="pre">Annotated[int]</span></code> is not valid)</p></li>
<li><p>The order of the metadata elements is preserved and matters for equality
checks:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">assert</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="n">ctype</span><span class="p">(</span><span class="s2">&quot;char&quot;</span><span class="p">)]</span> <span class="o">!=</span> <span class="n">Annotated</span><span class="p">[</span>
    <span class="nb">int</span><span class="p">,</span> <span class="n">ctype</span><span class="p">(</span><span class="s2">&quot;char&quot;</span><span class="p">),</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
<span class="p">]</span>
</pre></div>
</div>
</li>
<li><p>Nested <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> types are flattened. The order of the metadata elements
starts with the innermost annotation:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">assert</span> <span class="n">Annotated</span><span class="p">[</span><span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">)],</span> <span class="n">ctype</span><span class="p">(</span><span class="s2">&quot;char&quot;</span><span class="p">)]</span> <span class="o">==</span> <span class="n">Annotated</span><span class="p">[</span>
    <span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="n">ctype</span><span class="p">(</span><span class="s2">&quot;char&quot;</span><span class="p">)</span>
<span class="p">]</span>
</pre></div>
</div>
</li>
<li><p>Duplicated metadata elements are not removed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">assert</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">)]</span> <span class="o">!=</span> <span class="n">Annotated</span><span class="p">[</span>
    <span class="nb">int</span><span class="p">,</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="n">ValueRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
<span class="p">]</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">Annotated</span></code> can be used with nested and generic aliases:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">MaxLen</span><span class="p">:</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">int</span>

<span class="nb">type</span> <span class="n">Vec</span><span class="p">[</span><span class="n">T</span><span class="p">]</span> <span class="o">=</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="n">T</span><span class="p">]],</span> <span class="n">MaxLen</span><span class="p">(</span><span class="mi">10</span><span class="p">)]</span>

<span class="c1"># When used in a type annotation, a type checker will treat &quot;V&quot; the same as</span>
<span class="c1"># ``Annotated[list[tuple[int, int]], MaxLen(10)]``:</span>
<span class="nb">type</span> <span class="n">V</span> <span class="o">=</span> <span class="n">Vec</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">Annotated</span></code> cannot be used with an unpacked <a class="reference internal" href="#typing.TypeVarTuple" title="typing.TypeVarTuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVarTuple</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">type</span> <span class="n">Variadic</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">]</span> <span class="o">=</span> <span class="n">Annotated</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">,</span> <span class="n">Ann1</span><span class="p">]</span>  <span class="c1"># NOT valid</span>
</pre></div>
</div>
<p>This would be equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Annotated</span><span class="p">[</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="p">,</span> <span class="n">T3</span><span class="p">,</span> <span class="o">...</span><span class="p">,</span> <span class="n">Ann1</span><span class="p">]</span>
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">T1</span></code>, <code class="docutils literal notranslate"><span class="pre">T2</span></code>, etc. are <a class="reference internal" href="#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVars</span></code></a>. This would be
invalid: only one type should be passed to Annotated.</p>
</li>
<li><p>By default, <a class="reference internal" href="#typing.get_type_hints" title="typing.get_type_hints"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_type_hints()</span></code></a> strips the metadata from annotations.
Pass <code class="docutils literal notranslate"><span class="pre">include_extras=True</span></code> to have the metadata preserved:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Annotated</span><span class="p">,</span> <span class="n">get_type_hints</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">func</span><span class="p">(</span><span class="n">x</span><span class="p">:</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="s2">&quot;metadata&quot;</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span> <span class="k">pass</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">get_type_hints</span><span class="p">(</span><span class="n">func</span><span class="p">)</span>
<span class="go">{&#39;x&#39;: &lt;class &#39;int&#39;&gt;, &#39;return&#39;: &lt;class &#39;NoneType&#39;&gt;}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">get_type_hints</span><span class="p">(</span><span class="n">func</span><span class="p">,</span> <span class="n">include_extras</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">{&#39;x&#39;: typing.Annotated[int, &#39;metadata&#39;], &#39;return&#39;: &lt;class &#39;NoneType&#39;&gt;}</span>
</pre></div>
</div>
</li>
<li><p>At runtime, the metadata associated with an <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> type can be
retrieved via the <code class="xref py py-attr docutils literal notranslate"><span class="pre">__metadata__</span></code> attribute:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Annotated</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">X</span> <span class="o">=</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="s2">&quot;very&quot;</span><span class="p">,</span> <span class="s2">&quot;important&quot;</span><span class="p">,</span> <span class="s2">&quot;metadata&quot;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">X</span>
<span class="go">typing.Annotated[int, &#39;very&#39;, &#39;important&#39;, &#39;metadata&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">X</span><span class="o">.</span><span class="n">__metadata__</span>
<span class="go">(&#39;very&#39;, &#39;important&#39;, &#39;metadata&#39;)</span>
</pre></div>
</div>
</li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-15"></span><a class="pep reference external" href="https://peps.python.org/pep-0593/"><strong>PEP 593</strong></a> - Flexible function and variable annotations</dt><dd><p>The PEP introducing <code class="docutils literal notranslate"><span class="pre">Annotated</span></code> to the standard library.</p>
</dd>
</dl>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.TypeGuard">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TypeGuard</span></span><a class="headerlink" href="#typing.TypeGuard" title="Link to this definition">¶</a></dt>
<dd><p>Special typing construct for marking user-defined type guard functions.</p>
<p><code class="docutils literal notranslate"><span class="pre">TypeGuard</span></code> can be used to annotate the return type of a user-defined
type guard function.  <code class="docutils literal notranslate"><span class="pre">TypeGuard</span></code> only accepts a single type argument.
At runtime, functions marked this way should return a boolean.</p>
<p><code class="docutils literal notranslate"><span class="pre">TypeGuard</span></code> aims to benefit <em>type narrowing</em> – a technique used by static
type checkers to determine a more precise type of an expression within a
program’s code flow.  Usually type narrowing is done by analyzing
conditional code flow and applying the narrowing to a block of code.  The
conditional expression here is sometimes referred to as a “type guard”:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">is_str</span><span class="p">(</span><span class="n">val</span><span class="p">:</span> <span class="nb">str</span> <span class="o">|</span> <span class="nb">float</span><span class="p">):</span>
    <span class="c1"># &quot;isinstance&quot; type guard</span>
    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">val</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
        <span class="c1"># Type of ``val`` is narrowed to ``str``</span>
        <span class="o">...</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Else, type of ``val`` is narrowed to ``float``.</span>
        <span class="o">...</span>
</pre></div>
</div>
<p>Sometimes it would be convenient to use a user-defined boolean function
as a type guard.  Such a function should use <code class="docutils literal notranslate"><span class="pre">TypeGuard[...]</span></code> as its
return type to alert static type checkers to this intention.</p>
<p>Using  <code class="docutils literal notranslate"><span class="pre">-&gt;</span> <span class="pre">TypeGuard</span></code> tells the static type checker that for a given
function:</p>
<ol class="arabic simple">
<li><p>The return value is a boolean.</p></li>
<li><p>If the return value is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the type of its argument
is the type inside <code class="docutils literal notranslate"><span class="pre">TypeGuard</span></code>.</p></li>
</ol>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">is_str_list</span><span class="p">(</span><span class="n">val</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">object</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">TypeGuard</span><span class="p">[</span><span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&#39;&#39;&#39;Determines whether all objects in the list are strings&#39;&#39;&#39;</span>
    <span class="k">return</span> <span class="nb">all</span><span class="p">(</span><span class="nb">isinstance</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">val</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">func1</span><span class="p">(</span><span class="n">val</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">object</span><span class="p">]):</span>
    <span class="k">if</span> <span class="n">is_str_list</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
        <span class="c1"># Type of ``val`` is narrowed to ``list[str]``.</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot; &quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">val</span><span class="p">))</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Type of ``val`` remains as ``list[object]``.</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Not a list of strings!&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">is_str_list</span></code> is a class or instance method, then the type in
<code class="docutils literal notranslate"><span class="pre">TypeGuard</span></code> maps to the type of the second parameter after <code class="docutils literal notranslate"><span class="pre">cls</span></code> or
<code class="docutils literal notranslate"><span class="pre">self</span></code>.</p>
<p>In short, the form <code class="docutils literal notranslate"><span class="pre">def</span> <span class="pre">foo(arg:</span> <span class="pre">TypeA)</span> <span class="pre">-&gt;</span> <span class="pre">TypeGuard[TypeB]:</span> <span class="pre">...</span></code>,
means that if <code class="docutils literal notranslate"><span class="pre">foo(arg)</span></code> returns <code class="docutils literal notranslate"><span class="pre">True</span></code>, then <code class="docutils literal notranslate"><span class="pre">arg</span></code> narrows from
<code class="docutils literal notranslate"><span class="pre">TypeA</span></code> to <code class="docutils literal notranslate"><span class="pre">TypeB</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="docutils literal notranslate"><span class="pre">TypeB</span></code> need not be a narrower form of <code class="docutils literal notranslate"><span class="pre">TypeA</span></code> – it can even be a
wider form. The main reason is to allow for things like
narrowing <code class="docutils literal notranslate"><span class="pre">list[object]</span></code> to <code class="docutils literal notranslate"><span class="pre">list[str]</span></code> even though the latter
is not a subtype of the former, since <code class="docutils literal notranslate"><span class="pre">list</span></code> is invariant.
The responsibility of writing type-safe type guards is left to the user.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">TypeGuard</span></code> also works with type variables.  See <span class="target" id="index-16"></span><a class="pep reference external" href="https://peps.python.org/pep-0647/"><strong>PEP 647</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Unpack">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Unpack</span></span><a class="headerlink" href="#typing.Unpack" title="Link to this definition">¶</a></dt>
<dd><p>Typing operator to conceptually mark an object as having been unpacked.</p>
<p>For example, using the unpack operator <code class="docutils literal notranslate"><span class="pre">*</span></code> on a
<a class="reference internal" href="#typevartuple"><span class="std std-ref">type variable tuple</span></a> is equivalent to using <code class="docutils literal notranslate"><span class="pre">Unpack</span></code>
to mark the type variable tuple as having been unpacked:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Ts</span> <span class="o">=</span> <span class="n">TypeVarTuple</span><span class="p">(</span><span class="s1">&#39;Ts&#39;</span><span class="p">)</span>
<span class="n">tup</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">]</span>
<span class="c1"># Effectively does:</span>
<span class="n">tup</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="n">Unpack</span><span class="p">[</span><span class="n">Ts</span><span class="p">]]</span>
</pre></div>
</div>
<p>In fact, <code class="docutils literal notranslate"><span class="pre">Unpack</span></code> can be used interchangeably with <code class="docutils literal notranslate"><span class="pre">*</span></code> in the context
of <a class="reference internal" href="#typing.TypeVarTuple" title="typing.TypeVarTuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.TypeVarTuple</span></code></a> and
<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.tuple</span></code></a> types. You might see <code class="docutils literal notranslate"><span class="pre">Unpack</span></code> being used
explicitly in older versions of Python, where <code class="docutils literal notranslate"><span class="pre">*</span></code> couldn’t be used in
certain places:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># In older versions of Python, TypeVarTuple and Unpack</span>
<span class="c1"># are located in the `typing_extensions` backports package.</span>
<span class="kn">from</span> <span class="nn">typing_extensions</span> <span class="kn">import</span> <span class="n">TypeVarTuple</span><span class="p">,</span> <span class="n">Unpack</span>

<span class="n">Ts</span> <span class="o">=</span> <span class="n">TypeVarTuple</span><span class="p">(</span><span class="s1">&#39;Ts&#39;</span><span class="p">)</span>
<span class="n">tup</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">]</span>         <span class="c1"># Syntax error on Python &lt;= 3.10!</span>
<span class="n">tup</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="n">Unpack</span><span class="p">[</span><span class="n">Ts</span><span class="p">]]</span>  <span class="c1"># Semantically equivalent, and backwards-compatible</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Unpack</span></code> can also be used along with <a class="reference internal" href="#typing.TypedDict" title="typing.TypedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.TypedDict</span></code></a> for typing
<code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> in a function signature:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypedDict</span><span class="p">,</span> <span class="n">Unpack</span>

<span class="k">class</span> <span class="nc">Movie</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">year</span><span class="p">:</span> <span class="nb">int</span>

<span class="c1"># This function expects two keyword arguments - `name` of type `str`</span>
<span class="c1"># and `year` of type `int`.</span>
<span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">:</span> <span class="n">Unpack</span><span class="p">[</span><span class="n">Movie</span><span class="p">]):</span> <span class="o">...</span>
</pre></div>
</div>
<p>See <span class="target" id="index-17"></span><a class="pep reference external" href="https://peps.python.org/pep-0692/"><strong>PEP 692</strong></a> for more details on using <code class="docutils literal notranslate"><span class="pre">Unpack</span></code> for <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> typing.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</section>
<section id="building-generic-types-and-type-aliases">
<h4>Building generic types and type aliases<a class="headerlink" href="#building-generic-types-and-type-aliases" title="Link to this heading">¶</a></h4>
<p>The following classes should not be used directly as annotations.
Their intended purpose is to be building blocks
for creating generic types and type aliases.</p>
<p>These objects can be created through special syntax
(<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter lists</span></a> and the <a class="reference internal" href="../reference/simple_stmts.html#type"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">type</span></code></a> statement).
For compatibility with Python 3.11 and earlier, they can also be created
without the dedicated syntax, as documented below.</p>
<dl class="py class">
<dt class="sig sig-object py" id="typing.Generic">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Generic</span></span><a class="headerlink" href="#typing.Generic" title="Link to this definition">¶</a></dt>
<dd><p>Abstract base class for generic types.</p>
<p>A generic type is typically declared by adding a list of type parameters
after the class name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Mapping</span><span class="p">[</span><span class="n">KT</span><span class="p">,</span> <span class="n">VT</span><span class="p">]:</span>
    <span class="k">def</span> <span class="fm">__getitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">KT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">VT</span><span class="p">:</span>
        <span class="o">...</span>
        <span class="c1"># Etc.</span>
</pre></div>
</div>
<p>Such a class implicitly inherits from <code class="docutils literal notranslate"><span class="pre">Generic</span></code>.
The runtime semantics of this syntax are discussed in the
<a class="reference internal" href="../reference/compound_stmts.html#generic-classes"><span class="std std-ref">Language Reference</span></a>.</p>
<p>This class can then be used as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">lookup_name</span><span class="p">[</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">](</span><span class="n">mapping</span><span class="p">:</span> <span class="n">Mapping</span><span class="p">[</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="n">X</span><span class="p">,</span> <span class="n">default</span><span class="p">:</span> <span class="n">Y</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Y</span><span class="p">:</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">mapping</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
    <span class="k">except</span> <span class="ne">KeyError</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">default</span>
</pre></div>
</div>
<p>Here the brackets after the function name indicate a
<a class="reference internal" href="../reference/compound_stmts.html#generic-functions"><span class="std std-ref">generic function</span></a>.</p>
<p>For backwards compatibility, generic classes can also be
declared by explicitly inheriting from
<code class="docutils literal notranslate"><span class="pre">Generic</span></code>. In this case, the type parameters must be declared
separately:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">KT</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;KT&#39;</span><span class="p">)</span>
<span class="n">VT</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;VT&#39;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">Mapping</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">KT</span><span class="p">,</span> <span class="n">VT</span><span class="p">]):</span>
    <span class="k">def</span> <span class="fm">__getitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">KT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">VT</span><span class="p">:</span>
        <span class="o">...</span>
        <span class="c1"># Etc.</span>
</pre></div>
</div>
</dd></dl>

<dl class="py class" id="typevar">
<dt class="sig sig-object py" id="typing.TypeVar">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TypeVar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">constraints</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bound</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">covariant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">contravariant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">infer_variance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.TypeVar" title="Link to this definition">¶</a></dt>
<dd><p>Type variable.</p>
<p>The preferred way to construct a type variable is via the dedicated syntax
for <a class="reference internal" href="../reference/compound_stmts.html#generic-functions"><span class="std std-ref">generic functions</span></a>,
<a class="reference internal" href="../reference/compound_stmts.html#generic-classes"><span class="std std-ref">generic classes</span></a>, and
<a class="reference internal" href="../reference/compound_stmts.html#generic-type-aliases"><span class="std std-ref">generic type aliases</span></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Sequence</span><span class="p">[</span><span class="n">T</span><span class="p">]:</span>  <span class="c1"># T is a TypeVar</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>This syntax can also be used to create bound and constrained type
variables:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">StrSequence</span><span class="p">[</span><span class="n">S</span><span class="p">:</span> <span class="nb">str</span><span class="p">]:</span>  <span class="c1"># S is a TypeVar bound to str</span>
    <span class="o">...</span>


<span class="k">class</span> <span class="nc">StrOrBytesSequence</span><span class="p">[</span><span class="n">A</span><span class="p">:</span> <span class="p">(</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">)]:</span>  <span class="c1"># A is a TypeVar constrained to str or bytes</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>However, if desired, reusable type variables can also be constructed manually, like so:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;T&#39;</span><span class="p">)</span>  <span class="c1"># Can be anything</span>
<span class="n">S</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;S&#39;</span><span class="p">,</span> <span class="n">bound</span><span class="o">=</span><span class="nb">str</span><span class="p">)</span>  <span class="c1"># Can be any subtype of str</span>
<span class="n">A</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;A&#39;</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">)</span>  <span class="c1"># Must be exactly str or bytes</span>
</pre></div>
</div>
<p>Type variables exist primarily for the benefit of static type
checkers.  They serve as the parameters for generic types as well
as for generic function and type alias definitions.
See <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> for more
information on generic types.  Generic functions work as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">repeat</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">x</span><span class="p">:</span> <span class="n">T</span><span class="p">,</span> <span class="n">n</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Sequence</span><span class="p">[</span><span class="n">T</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Return a list containing n references to x.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">x</span><span class="p">]</span><span class="o">*</span><span class="n">n</span>


<span class="k">def</span> <span class="nf">print_capitalized</span><span class="p">[</span><span class="n">S</span><span class="p">:</span> <span class="nb">str</span><span class="p">](</span><span class="n">x</span><span class="p">:</span> <span class="n">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">S</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Print x capitalized, and return x.&quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="o">.</span><span class="n">capitalize</span><span class="p">())</span>
    <span class="k">return</span> <span class="n">x</span>


<span class="k">def</span> <span class="nf">concatenate</span><span class="p">[</span><span class="n">A</span><span class="p">:</span> <span class="p">(</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">)](</span><span class="n">x</span><span class="p">:</span> <span class="n">A</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="n">A</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">A</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Add two strings or bytes objects together.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>
</pre></div>
</div>
<p>Note that type variables can be <em>bound</em>, <em>constrained</em>, or neither, but
cannot be both bound <em>and</em> constrained.</p>
<p>The variance of type variables is inferred by type checkers when they are created
through the <a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter syntax</span></a> or when
<code class="docutils literal notranslate"><span class="pre">infer_variance=True</span></code> is passed.
Manually created type variables may be explicitly marked covariant or contravariant by passing
<code class="docutils literal notranslate"><span class="pre">covariant=True</span></code> or <code class="docutils literal notranslate"><span class="pre">contravariant=True</span></code>.
By default, manually created type variables are invariant.
See <span class="target" id="index-18"></span><a class="pep reference external" href="https://peps.python.org/pep-0484/"><strong>PEP 484</strong></a> and <span class="target" id="index-19"></span><a class="pep reference external" href="https://peps.python.org/pep-0695/"><strong>PEP 695</strong></a> for more details.</p>
<p>Bound type variables and constrained type variables have different
semantics in several important ways. Using a <em>bound</em> type variable means
that the <code class="docutils literal notranslate"><span class="pre">TypeVar</span></code> will be solved using the most specific type possible:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span> <span class="o">=</span> <span class="n">print_capitalized</span><span class="p">(</span><span class="s1">&#39;a string&#39;</span><span class="p">)</span>
<span class="n">reveal_type</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>  <span class="c1"># revealed type is str</span>

<span class="k">class</span> <span class="nc">StringSubclass</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="k">pass</span>

<span class="n">y</span> <span class="o">=</span> <span class="n">print_capitalized</span><span class="p">(</span><span class="n">StringSubclass</span><span class="p">(</span><span class="s1">&#39;another string&#39;</span><span class="p">))</span>
<span class="n">reveal_type</span><span class="p">(</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># revealed type is StringSubclass</span>

<span class="n">z</span> <span class="o">=</span> <span class="n">print_capitalized</span><span class="p">(</span><span class="mi">45</span><span class="p">)</span>  <span class="c1"># error: int is not a subtype of str</span>
</pre></div>
</div>
<p>Type variables can be bound to concrete types, abstract types (ABCs or
protocols), and even unions of types:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Can be anything with an __abs__ method</span>
<span class="k">def</span> <span class="nf">print_abs</span><span class="p">[</span><span class="n">T</span><span class="p">:</span> <span class="n">SupportsAbs</span><span class="p">](</span><span class="n">arg</span><span class="p">:</span> <span class="n">T</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Absolute value:&quot;</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">arg</span><span class="p">))</span>

<span class="n">U</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;U&#39;</span><span class="p">,</span> <span class="n">bound</span><span class="o">=</span><span class="nb">str</span><span class="o">|</span><span class="nb">bytes</span><span class="p">)</span>  <span class="c1"># Can be any subtype of the union str|bytes</span>
<span class="n">V</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s1">&#39;V&#39;</span><span class="p">,</span> <span class="n">bound</span><span class="o">=</span><span class="n">SupportsAbs</span><span class="p">)</span>  <span class="c1"># Can be anything with an __abs__ method</span>
</pre></div>
</div>
<p id="typing-constrained-typevar">Using a <em>constrained</em> type variable, however, means that the <code class="docutils literal notranslate"><span class="pre">TypeVar</span></code>
can only ever be solved as being exactly one of the constraints given:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">a</span> <span class="o">=</span> <span class="n">concatenate</span><span class="p">(</span><span class="s1">&#39;one&#39;</span><span class="p">,</span> <span class="s1">&#39;two&#39;</span><span class="p">)</span>
<span class="n">reveal_type</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>  <span class="c1"># revealed type is str</span>

<span class="n">b</span> <span class="o">=</span> <span class="n">concatenate</span><span class="p">(</span><span class="n">StringSubclass</span><span class="p">(</span><span class="s1">&#39;one&#39;</span><span class="p">),</span> <span class="n">StringSubclass</span><span class="p">(</span><span class="s1">&#39;two&#39;</span><span class="p">))</span>
<span class="n">reveal_type</span><span class="p">(</span><span class="n">b</span><span class="p">)</span>  <span class="c1"># revealed type is str, despite StringSubclass being passed in</span>

<span class="n">c</span> <span class="o">=</span> <span class="n">concatenate</span><span class="p">(</span><span class="s1">&#39;one&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;two&#39;</span><span class="p">)</span>  <span class="c1"># error: type variable &#39;A&#39; can be either str or bytes in a function call, but not both</span>
</pre></div>
</div>
<p>At runtime, <code class="docutils literal notranslate"><span class="pre">isinstance(x,</span> <span class="pre">T)</span></code> will raise <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVar.__name__">
<span class="sig-name descname"><span class="pre">__name__</span></span><a class="headerlink" href="#typing.TypeVar.__name__" title="Link to this definition">¶</a></dt>
<dd><p>The name of the type variable.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVar.__covariant__">
<span class="sig-name descname"><span class="pre">__covariant__</span></span><a class="headerlink" href="#typing.TypeVar.__covariant__" title="Link to this definition">¶</a></dt>
<dd><p>Whether the type var has been explicitly marked as covariant.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVar.__contravariant__">
<span class="sig-name descname"><span class="pre">__contravariant__</span></span><a class="headerlink" href="#typing.TypeVar.__contravariant__" title="Link to this definition">¶</a></dt>
<dd><p>Whether the type var has been explicitly marked as contravariant.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVar.__infer_variance__">
<span class="sig-name descname"><span class="pre">__infer_variance__</span></span><a class="headerlink" href="#typing.TypeVar.__infer_variance__" title="Link to this definition">¶</a></dt>
<dd><p>Whether the type variable’s variance should be inferred by type checkers.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVar.__bound__">
<span class="sig-name descname"><span class="pre">__bound__</span></span><a class="headerlink" href="#typing.TypeVar.__bound__" title="Link to this definition">¶</a></dt>
<dd><p>The bound of the type variable, if any.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>For type variables created through <a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter syntax</span></a>,
the bound is evaluated only when the attribute is accessed, not when
the type variable is created (see <a class="reference internal" href="../reference/executionmodel.html#lazy-evaluation"><span class="std std-ref">Lazy evaluation</span></a>).</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVar.__constraints__">
<span class="sig-name descname"><span class="pre">__constraints__</span></span><a class="headerlink" href="#typing.TypeVar.__constraints__" title="Link to this definition">¶</a></dt>
<dd><p>A tuple containing the constraints of the type variable, if any.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>For type variables created through <a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter syntax</span></a>,
the constraints are evaluated only when the attribute is accessed, not when
the type variable is created (see <a class="reference internal" href="../reference/executionmodel.html#lazy-evaluation"><span class="std std-ref">Lazy evaluation</span></a>).</p>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Type variables can now be declared using the
<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter</span></a> syntax introduced by <span class="target" id="index-20"></span><a class="pep reference external" href="https://peps.python.org/pep-0695/"><strong>PEP 695</strong></a>.
The <code class="docutils literal notranslate"><span class="pre">infer_variance</span></code> parameter was added.</p>
</div>
</dd></dl>

<dl class="py class" id="typevartuple">
<dt class="sig sig-object py" id="typing.TypeVarTuple">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TypeVarTuple</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.TypeVarTuple" title="Link to this definition">¶</a></dt>
<dd><p>Type variable tuple. A specialized form of <a class="reference internal" href="#typevar"><span class="std std-ref">type variable</span></a>
that enables <em>variadic</em> generics.</p>
<p>Type variable tuples can be declared in <a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter lists</span></a>
using a single asterisk (<code class="docutils literal notranslate"><span class="pre">*</span></code>) before the name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">move_first_element_to_last</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="o">*</span><span class="n">Ts</span><span class="p">](</span><span class="n">tup</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="o">*</span><span class="n">Ts</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">,</span> <span class="n">T</span><span class="p">]:</span>
    <span class="k">return</span> <span class="p">(</span><span class="o">*</span><span class="n">tup</span><span class="p">[</span><span class="mi">1</span><span class="p">:],</span> <span class="n">tup</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</pre></div>
</div>
<p>Or by explicitly invoking the <code class="docutils literal notranslate"><span class="pre">TypeVarTuple</span></code> constructor:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>
<span class="n">Ts</span> <span class="o">=</span> <span class="n">TypeVarTuple</span><span class="p">(</span><span class="s2">&quot;Ts&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">move_first_element_to_last</span><span class="p">(</span><span class="n">tup</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="o">*</span><span class="n">Ts</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">,</span> <span class="n">T</span><span class="p">]:</span>
    <span class="k">return</span> <span class="p">(</span><span class="o">*</span><span class="n">tup</span><span class="p">[</span><span class="mi">1</span><span class="p">:],</span> <span class="n">tup</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
</pre></div>
</div>
<p>A normal type variable enables parameterization with a single type. A type
variable tuple, in contrast, allows parameterization with an
<em>arbitrary</em> number of types by acting like an <em>arbitrary</em> number of type
variables wrapped in a tuple. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># T is bound to int, Ts is bound to ()</span>
<span class="c1"># Return value is (1,), which has type tuple[int]</span>
<span class="n">move_first_element_to_last</span><span class="p">(</span><span class="n">tup</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,))</span>

<span class="c1"># T is bound to int, Ts is bound to (str,)</span>
<span class="c1"># Return value is (&#39;spam&#39;, 1), which has type tuple[str, int]</span>
<span class="n">move_first_element_to_last</span><span class="p">(</span><span class="n">tup</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;spam&#39;</span><span class="p">))</span>

<span class="c1"># T is bound to int, Ts is bound to (str, float)</span>
<span class="c1"># Return value is (&#39;spam&#39;, 3.0, 1), which has type tuple[str, float, int]</span>
<span class="n">move_first_element_to_last</span><span class="p">(</span><span class="n">tup</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="mf">3.0</span><span class="p">))</span>

<span class="c1"># This fails to type check (and fails at runtime)</span>
<span class="c1"># because tuple[()] is not compatible with tuple[T, *Ts]</span>
<span class="c1"># (at least one element is required)</span>
<span class="n">move_first_element_to_last</span><span class="p">(</span><span class="n">tup</span><span class="o">=</span><span class="p">())</span>
</pre></div>
</div>
<p>Note the use of the unpacking operator <code class="docutils literal notranslate"><span class="pre">*</span></code> in <code class="docutils literal notranslate"><span class="pre">tuple[T,</span> <span class="pre">*Ts]</span></code>.
Conceptually, you can think of <code class="docutils literal notranslate"><span class="pre">Ts</span></code> as a tuple of type variables
<code class="docutils literal notranslate"><span class="pre">(T1,</span> <span class="pre">T2,</span> <span class="pre">...)</span></code>. <code class="docutils literal notranslate"><span class="pre">tuple[T,</span> <span class="pre">*Ts]</span></code> would then become
<code class="docutils literal notranslate"><span class="pre">tuple[T,</span> <span class="pre">*(T1,</span> <span class="pre">T2,</span> <span class="pre">...)]</span></code>, which is equivalent to
<code class="docutils literal notranslate"><span class="pre">tuple[T,</span> <span class="pre">T1,</span> <span class="pre">T2,</span> <span class="pre">...]</span></code>. (Note that in older versions of Python, you might
see this written using <a class="reference internal" href="#typing.Unpack" title="typing.Unpack"><code class="xref py py-data docutils literal notranslate"><span class="pre">Unpack</span></code></a> instead, as
<code class="docutils literal notranslate"><span class="pre">Unpack[Ts]</span></code>.)</p>
<p>Type variable tuples must <em>always</em> be unpacked. This helps distinguish type
variable tuples from normal type variables:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span><span class="p">:</span> <span class="n">Ts</span>          <span class="c1"># Not valid</span>
<span class="n">x</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="n">Ts</span><span class="p">]</span>   <span class="c1"># Not valid</span>
<span class="n">x</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">]</span>  <span class="c1"># The correct way to do it</span>
</pre></div>
</div>
<p>Type variable tuples can be used in the same contexts as normal type
variables. For example, in class definitions, arguments, and return types:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Array</span><span class="p">[</span><span class="o">*</span><span class="n">Shape</span><span class="p">]:</span>
    <span class="k">def</span> <span class="fm">__getitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Shape</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span> <span class="o">...</span>
    <span class="k">def</span> <span class="fm">__abs__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="s2">&quot;Array[*Shape]&quot;</span><span class="p">:</span> <span class="o">...</span>
    <span class="k">def</span> <span class="nf">get_shape</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Shape</span><span class="p">]:</span> <span class="o">...</span>
</pre></div>
</div>
<p>Type variable tuples can be happily combined with normal type variables:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Array</span><span class="p">[</span><span class="n">DType</span><span class="p">,</span> <span class="o">*</span><span class="n">Shape</span><span class="p">]:</span>  <span class="c1"># This is fine</span>
    <span class="k">pass</span>

<span class="k">class</span> <span class="nc">Array2</span><span class="p">[</span><span class="o">*</span><span class="n">Shape</span><span class="p">,</span> <span class="n">DType</span><span class="p">]:</span>  <span class="c1"># This would also be fine</span>
    <span class="k">pass</span>

<span class="k">class</span> <span class="nc">Height</span><span class="p">:</span> <span class="o">...</span>
<span class="k">class</span> <span class="nc">Width</span><span class="p">:</span> <span class="o">...</span>

<span class="n">float_array_1d</span><span class="p">:</span> <span class="n">Array</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="n">Height</span><span class="p">]</span> <span class="o">=</span> <span class="n">Array</span><span class="p">()</span>     <span class="c1"># Totally fine</span>
<span class="n">int_array_2d</span><span class="p">:</span> <span class="n">Array</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">Height</span><span class="p">,</span> <span class="n">Width</span><span class="p">]</span> <span class="o">=</span> <span class="n">Array</span><span class="p">()</span>  <span class="c1"># Yup, fine too</span>
</pre></div>
</div>
<p>However, note that at most one type variable tuple may appear in a single
list of type arguments or type parameters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">,</span> <span class="o">*</span><span class="n">Ts</span><span class="p">]</span>            <span class="c1"># Not valid</span>
<span class="k">class</span> <span class="nc">Array</span><span class="p">[</span><span class="o">*</span><span class="n">Shape</span><span class="p">,</span> <span class="o">*</span><span class="n">Shape</span><span class="p">]:</span>  <span class="c1"># Not valid</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p>Finally, an unpacked type variable tuple can be used as the type annotation
of <code class="docutils literal notranslate"><span class="pre">*args</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">call_soon</span><span class="p">[</span><span class="o">*</span><span class="n">Ts</span><span class="p">](</span>
         <span class="n">callback</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="o">*</span><span class="n">Ts</span><span class="p">],</span> <span class="kc">None</span><span class="p">],</span>
         <span class="o">*</span><span class="n">args</span><span class="p">:</span> <span class="o">*</span><span class="n">Ts</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>
    <span class="n">callback</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">)</span>
</pre></div>
</div>
<p>In contrast to non-unpacked annotations of <code class="docutils literal notranslate"><span class="pre">*args</span></code> - e.g. <code class="docutils literal notranslate"><span class="pre">*args:</span> <span class="pre">int</span></code>,
which would specify that <em>all</em> arguments are <code class="docutils literal notranslate"><span class="pre">int</span></code> - <code class="docutils literal notranslate"><span class="pre">*args:</span> <span class="pre">*Ts</span></code>
enables reference to the types of the <em>individual</em> arguments in <code class="docutils literal notranslate"><span class="pre">*args</span></code>.
Here, this allows us to ensure the types of the <code class="docutils literal notranslate"><span class="pre">*args</span></code> passed
to <code class="docutils literal notranslate"><span class="pre">call_soon</span></code> match the types of the (positional) arguments of
<code class="docutils literal notranslate"><span class="pre">callback</span></code>.</p>
<p>See <span class="target" id="index-21"></span><a class="pep reference external" href="https://peps.python.org/pep-0646/"><strong>PEP 646</strong></a> for more details on type variable tuples.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeVarTuple.__name__">
<span class="sig-name descname"><span class="pre">__name__</span></span><a class="headerlink" href="#typing.TypeVarTuple.__name__" title="Link to this definition">¶</a></dt>
<dd><p>The name of the type variable tuple.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Type variable tuples can now be declared using the
<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter</span></a> syntax introduced by <span class="target" id="index-22"></span><a class="pep reference external" href="https://peps.python.org/pep-0695/"><strong>PEP 695</strong></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.ParamSpec">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ParamSpec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bound</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">covariant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">contravariant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.ParamSpec" title="Link to this definition">¶</a></dt>
<dd><p>Parameter specification variable.  A specialized version of
<a class="reference internal" href="#typevar"><span class="std std-ref">type variables</span></a>.</p>
<p>In <a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter lists</span></a>, parameter specifications
can be declared with two asterisks (<code class="docutils literal notranslate"><span class="pre">**</span></code>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">type</span> <span class="n">IntFunc</span><span class="p">[</span><span class="o">**</span><span class="n">P</span><span class="p">]</span> <span class="o">=</span> <span class="n">Callable</span><span class="p">[</span><span class="n">P</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span>
</pre></div>
</div>
<p>For compatibility with Python 3.11 and earlier, <code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code> objects
can also be created as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">P</span> <span class="o">=</span> <span class="n">ParamSpec</span><span class="p">(</span><span class="s1">&#39;P&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Parameter specification variables exist primarily for the benefit of static
type checkers.  They are used to forward the parameter types of one
callable to another callable – a pattern commonly found in higher order
functions and decorators.  They are only valid when used in <code class="docutils literal notranslate"><span class="pre">Concatenate</span></code>,
or as the first argument to <code class="docutils literal notranslate"><span class="pre">Callable</span></code>, or as parameters for user-defined
Generics.  See <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> for more information on generic types.</p>
<p>For example, to add basic logging to a function, one can create a decorator
<code class="docutils literal notranslate"><span class="pre">add_logging</span></code> to log function calls.  The parameter specification variable
tells the type checker that the callable passed into the decorator and the
new callable returned by it have inter-dependent type parameters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Callable</span>
<span class="kn">import</span> <span class="nn">logging</span>

<span class="k">def</span> <span class="nf">add_logging</span><span class="p">[</span><span class="n">T</span><span class="p">,</span> <span class="o">**</span><span class="n">P</span><span class="p">](</span><span class="n">f</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[</span><span class="n">P</span><span class="p">,</span> <span class="n">T</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">Callable</span><span class="p">[</span><span class="n">P</span><span class="p">,</span> <span class="n">T</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&#39;&#39;&#39;A type-safe decorator to add logging to a function.&#39;&#39;&#39;</span>
    <span class="k">def</span> <span class="nf">inner</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">:</span> <span class="n">P</span><span class="o">.</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">:</span> <span class="n">P</span><span class="o">.</span><span class="n">kwargs</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T</span><span class="p">:</span>
        <span class="n">logging</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">f</span><span class="o">.</span><span class="vm">__name__</span><span class="si">}</span><span class="s1"> was called&#39;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">f</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">inner</span>

<span class="nd">@add_logging</span>
<span class="k">def</span> <span class="nf">add_two</span><span class="p">(</span><span class="n">x</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&#39;&#39;&#39;Add two numbers together.&#39;&#39;&#39;</span>
    <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>
</pre></div>
</div>
<p>Without <code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code>, the simplest way to annotate this previously was to
use a <a class="reference internal" href="#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a> with bound <code class="docutils literal notranslate"><span class="pre">Callable[...,</span> <span class="pre">Any]</span></code>.  However this
causes two problems:</p>
<ol class="arabic simple">
<li><p>The type checker can’t type check the <code class="docutils literal notranslate"><span class="pre">inner</span></code> function because
<code class="docutils literal notranslate"><span class="pre">*args</span></code> and <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> have to be typed <a class="reference internal" href="#typing.Any" title="typing.Any"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>.</p></li>
<li><p><a class="reference internal" href="#typing.cast" title="typing.cast"><code class="xref py py-func docutils literal notranslate"><span class="pre">cast()</span></code></a> may be required in the body of the <code class="docutils literal notranslate"><span class="pre">add_logging</span></code>
decorator when returning the <code class="docutils literal notranslate"><span class="pre">inner</span></code> function, or the static type
checker must be told to ignore the <code class="docutils literal notranslate"><span class="pre">return</span> <span class="pre">inner</span></code>.</p></li>
</ol>
<dl class="py attribute">
<dt class="sig sig-object py" id="typing.ParamSpec.args">
<span class="sig-name descname"><span class="pre">args</span></span><a class="headerlink" href="#typing.ParamSpec.args" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.ParamSpec.kwargs">
<span class="sig-name descname"><span class="pre">kwargs</span></span><a class="headerlink" href="#typing.ParamSpec.kwargs" title="Link to this definition">¶</a></dt>
<dd><p>Since <code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code> captures both positional and keyword parameters,
<code class="docutils literal notranslate"><span class="pre">P.args</span></code> and <code class="docutils literal notranslate"><span class="pre">P.kwargs</span></code> can be used to split a <code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code> into its
components.  <code class="docutils literal notranslate"><span class="pre">P.args</span></code> represents the tuple of positional parameters in a
given call and should only be used to annotate <code class="docutils literal notranslate"><span class="pre">*args</span></code>.  <code class="docutils literal notranslate"><span class="pre">P.kwargs</span></code>
represents the mapping of keyword parameters to their values in a given call,
and should be only be used to annotate <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code>.  Both
attributes require the annotated parameter to be in scope. At runtime,
<code class="docutils literal notranslate"><span class="pre">P.args</span></code> and <code class="docutils literal notranslate"><span class="pre">P.kwargs</span></code> are instances respectively of
<a class="reference internal" href="#typing.ParamSpecArgs" title="typing.ParamSpecArgs"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpecArgs</span></code></a> and <a class="reference internal" href="#typing.ParamSpecKwargs" title="typing.ParamSpecKwargs"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpecKwargs</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.ParamSpec.__name__">
<span class="sig-name descname"><span class="pre">__name__</span></span><a class="headerlink" href="#typing.ParamSpec.__name__" title="Link to this definition">¶</a></dt>
<dd><p>The name of the parameter specification.</p>
</dd></dl>

<p>Parameter specification variables created with <code class="docutils literal notranslate"><span class="pre">covariant=True</span></code> or
<code class="docutils literal notranslate"><span class="pre">contravariant=True</span></code> can be used to declare covariant or contravariant
generic types.  The <code class="docutils literal notranslate"><span class="pre">bound</span></code> argument is also accepted, similar to
<a class="reference internal" href="#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a>.  However the actual semantics of these keywords are yet to
be decided.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Parameter specifications can now be declared using the
<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameter</span></a> syntax introduced by <span class="target" id="index-23"></span><a class="pep reference external" href="https://peps.python.org/pep-0695/"><strong>PEP 695</strong></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Only parameter specification variables defined in global scope can
be pickled.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><span class="target" id="index-24"></span><a class="pep reference external" href="https://peps.python.org/pep-0612/"><strong>PEP 612</strong></a> – Parameter Specification Variables (the PEP which introduced
<code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code> and <code class="docutils literal notranslate"><span class="pre">Concatenate</span></code>)</p></li>
<li><p><a class="reference internal" href="#typing.Concatenate" title="typing.Concatenate"><code class="xref py py-data docutils literal notranslate"><span class="pre">Concatenate</span></code></a></p></li>
<li><p><a class="reference internal" href="#annotating-callables"><span class="std std-ref">Annotating callable objects</span></a></p></li>
</ul>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.ParamSpecArgs">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ParamSpecArgs</span></span><a class="headerlink" href="#typing.ParamSpecArgs" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.ParamSpecKwargs">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ParamSpecKwargs</span></span><a class="headerlink" href="#typing.ParamSpecKwargs" title="Link to this definition">¶</a></dt>
<dd><p>Arguments and keyword arguments attributes of a <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a>. The
<code class="docutils literal notranslate"><span class="pre">P.args</span></code> attribute of a <code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code> is an instance of <code class="docutils literal notranslate"><span class="pre">ParamSpecArgs</span></code>,
and <code class="docutils literal notranslate"><span class="pre">P.kwargs</span></code> is an instance of <code class="docutils literal notranslate"><span class="pre">ParamSpecKwargs</span></code>. They are intended
for runtime introspection and have no special meaning to static type checkers.</p>
<p>Calling <a class="reference internal" href="#typing.get_origin" title="typing.get_origin"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_origin()</span></code></a> on either of these objects will return the
original <code class="docutils literal notranslate"><span class="pre">ParamSpec</span></code>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">ParamSpec</span><span class="p">,</span> <span class="n">get_origin</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">P</span> <span class="o">=</span> <span class="n">ParamSpec</span><span class="p">(</span><span class="s2">&quot;P&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">get_origin</span><span class="p">(</span><span class="n">P</span><span class="o">.</span><span class="n">args</span><span class="p">)</span> <span class="ow">is</span> <span class="n">P</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">get_origin</span><span class="p">(</span><span class="n">P</span><span class="o">.</span><span class="n">kwargs</span><span class="p">)</span> <span class="ow">is</span> <span class="n">P</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.TypeAliasType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TypeAliasType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_params</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.TypeAliasType" title="Link to this definition">¶</a></dt>
<dd><p>The type of type aliases created through the <a class="reference internal" href="../reference/simple_stmts.html#type"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">type</span></code></a> statement.</p>
<p>Example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">Alias</span> <span class="o">=</span> <span class="nb">int</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">Alias</span><span class="p">)</span>
<span class="go">&lt;class &#39;typing.TypeAliasType&#39;&gt;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeAliasType.__name__">
<span class="sig-name descname"><span class="pre">__name__</span></span><a class="headerlink" href="#typing.TypeAliasType.__name__" title="Link to this definition">¶</a></dt>
<dd><p>The name of the type alias:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">Alias</span> <span class="o">=</span> <span class="nb">int</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Alias</span><span class="o">.</span><span class="vm">__name__</span>
<span class="go">&#39;Alias&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeAliasType.__module__">
<span class="sig-name descname"><span class="pre">__module__</span></span><a class="headerlink" href="#typing.TypeAliasType.__module__" title="Link to this definition">¶</a></dt>
<dd><p>The module in which the type alias was defined:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">Alias</span> <span class="o">=</span> <span class="nb">int</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Alias</span><span class="o">.</span><span class="vm">__module__</span>
<span class="go">&#39;__main__&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeAliasType.__type_params__">
<span class="sig-name descname"><span class="pre">__type_params__</span></span><a class="headerlink" href="#typing.TypeAliasType.__type_params__" title="Link to this definition">¶</a></dt>
<dd><p>The type parameters of the type alias, or an empty tuple if the alias is
not generic:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">ListOrSet</span><span class="p">[</span><span class="n">T</span><span class="p">]</span> <span class="o">=</span> <span class="nb">list</span><span class="p">[</span><span class="n">T</span><span class="p">]</span> <span class="o">|</span> <span class="nb">set</span><span class="p">[</span><span class="n">T</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ListOrSet</span><span class="o">.</span><span class="n">__type_params__</span>
<span class="go">(T,)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">NotGeneric</span> <span class="o">=</span> <span class="nb">int</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">NotGeneric</span><span class="o">.</span><span class="n">__type_params__</span>
<span class="go">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypeAliasType.__value__">
<span class="sig-name descname"><span class="pre">__value__</span></span><a class="headerlink" href="#typing.TypeAliasType.__value__" title="Link to this definition">¶</a></dt>
<dd><p>The type alias’s value. This is <a class="reference internal" href="../reference/executionmodel.html#lazy-evaluation"><span class="std std-ref">lazily evaluated</span></a>,
so names used in the definition of the alias are not resolved until the
<code class="docutils literal notranslate"><span class="pre">__value__</span></code> attribute is accessed:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">Mutually</span> <span class="o">=</span> <span class="n">Recursive</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span> <span class="n">Recursive</span> <span class="o">=</span> <span class="n">Mutually</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Mutually</span>
<span class="go">Mutually</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Recursive</span>
<span class="go">Recursive</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Mutually</span><span class="o">.</span><span class="n">__value__</span>
<span class="go">Recursive</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Recursive</span><span class="o">.</span><span class="n">__value__</span>
<span class="go">Mutually</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="other-special-directives">
<h4>Other special directives<a class="headerlink" href="#other-special-directives" title="Link to this heading">¶</a></h4>
<p>These functions and classes should not be used directly as annotations.
Their intended purpose is to be building blocks for creating and declaring
types.</p>
<dl class="py class">
<dt class="sig sig-object py" id="typing.NamedTuple">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">NamedTuple</span></span><a class="headerlink" href="#typing.NamedTuple" title="Link to this definition">¶</a></dt>
<dd><p>Typed version of <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">collections.namedtuple()</span></code></a>.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Employee</span><span class="p">(</span><span class="n">NamedTuple</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span>
</pre></div>
</div>
<p>This is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Employee</span> <span class="o">=</span> <span class="n">collections</span><span class="o">.</span><span class="n">namedtuple</span><span class="p">(</span><span class="s1">&#39;Employee&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">,</span> <span class="s1">&#39;id&#39;</span><span class="p">])</span>
</pre></div>
</div>
<p>To give a field a default value, you can assign to it in the class body:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Employee</span><span class="p">(</span><span class="n">NamedTuple</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">3</span>

<span class="n">employee</span> <span class="o">=</span> <span class="n">Employee</span><span class="p">(</span><span class="s1">&#39;Guido&#39;</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">employee</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="mi">3</span>
</pre></div>
</div>
<p>Fields with a default value must come after any fields without a default.</p>
<p>The resulting class has an extra attribute <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code> giving a
dict that maps the field names to the field types.  (The field names are in
the <code class="docutils literal notranslate"><span class="pre">_fields</span></code> attribute and the default values are in the
<code class="docutils literal notranslate"><span class="pre">_field_defaults</span></code> attribute, both of which are part of the <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a>
API.)</p>
<p><code class="docutils literal notranslate"><span class="pre">NamedTuple</span></code> subclasses can also have docstrings and methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Employee</span><span class="p">(</span><span class="n">NamedTuple</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Represents an employee.&quot;&quot;&quot;</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">3</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="sa">f</span><span class="s1">&#39;&lt;Employee </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s1">, id=</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">id</span><span class="si">}</span><span class="s1">&gt;&#39;</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">NamedTuple</span></code> subclasses can be generic:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Group</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">NamedTuple</span><span class="p">):</span>
    <span class="n">key</span><span class="p">:</span> <span class="n">T</span>
    <span class="n">group</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">T</span><span class="p">]</span>
</pre></div>
</div>
<p>Backward-compatible usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># For creating a generic NamedTuple on Python 3.11 or lower</span>
<span class="k">class</span> <span class="nc">Group</span><span class="p">(</span><span class="n">NamedTuple</span><span class="p">,</span> <span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
    <span class="n">key</span><span class="p">:</span> <span class="n">T</span>
    <span class="n">group</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">T</span><span class="p">]</span>

<span class="c1"># A functional syntax is also supported</span>
<span class="n">Employee</span> <span class="o">=</span> <span class="n">NamedTuple</span><span class="p">(</span><span class="s1">&#39;Employee&#39;</span><span class="p">,</span> <span class="p">[(</span><span class="s1">&#39;name&#39;</span><span class="p">,</span> <span class="nb">str</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;id&#39;</span><span class="p">,</span> <span class="nb">int</span><span class="p">)])</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added support for <span class="target" id="index-25"></span><a class="pep reference external" href="https://peps.python.org/pep-0526/"><strong>PEP 526</strong></a> variable annotation syntax.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.1: </span>Added support for default values, methods, and docstrings.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <code class="docutils literal notranslate"><span class="pre">_field_types</span></code> and <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code> attributes are
now regular dictionaries instead of instances of <code class="docutils literal notranslate"><span class="pre">OrderedDict</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Removed the <code class="docutils literal notranslate"><span class="pre">_field_types</span></code> attribute in favor of the more
standard <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code> attribute which has the same information.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added support for generic namedtuples.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.NewType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">NewType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.NewType" title="Link to this definition">¶</a></dt>
<dd><p>Helper class to create low-overhead <a class="reference internal" href="#distinct"><span class="std std-ref">distinct types</span></a>.</p>
<p>A <code class="docutils literal notranslate"><span class="pre">NewType</span></code> is considered a distinct type by a typechecker. At runtime,
however, calling a <code class="docutils literal notranslate"><span class="pre">NewType</span></code> returns its argument unchanged.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">UserId</span> <span class="o">=</span> <span class="n">NewType</span><span class="p">(</span><span class="s1">&#39;UserId&#39;</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>  <span class="c1"># Declare the NewType &quot;UserId&quot;</span>
<span class="n">first_user</span> <span class="o">=</span> <span class="n">UserId</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>  <span class="c1"># &quot;UserId&quot; returns the argument unchanged at runtime</span>
</pre></div>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="typing.NewType.__module__">
<span class="sig-name descname"><span class="pre">__module__</span></span><a class="headerlink" href="#typing.NewType.__module__" title="Link to this definition">¶</a></dt>
<dd><p>The module in which the new type is defined.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.NewType.__name__">
<span class="sig-name descname"><span class="pre">__name__</span></span><a class="headerlink" href="#typing.NewType.__name__" title="Link to this definition">¶</a></dt>
<dd><p>The name of the new type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.NewType.__supertype__">
<span class="sig-name descname"><span class="pre">__supertype__</span></span><a class="headerlink" href="#typing.NewType.__supertype__" title="Link to this definition">¶</a></dt>
<dd><p>The type that the new type is based on.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">NewType</span></code> is now a class rather than a function.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Protocol">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Protocol</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Generic</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Protocol" title="Link to this definition">¶</a></dt>
<dd><p>Base class for protocol classes.</p>
<p>Protocol classes are defined like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Proto</span><span class="p">(</span><span class="n">Protocol</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">meth</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="o">...</span>
</pre></div>
</div>
<p>Such classes are primarily used with static type checkers that recognize
structural subtyping (static duck-typing), for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">meth</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="mi">0</span>

<span class="k">def</span> <span class="nf">func</span><span class="p">(</span><span class="n">x</span><span class="p">:</span> <span class="n">Proto</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">x</span><span class="o">.</span><span class="n">meth</span><span class="p">()</span>

<span class="n">func</span><span class="p">(</span><span class="n">C</span><span class="p">())</span>  <span class="c1"># Passes static type check</span>
</pre></div>
</div>
<p>See <span class="target" id="index-26"></span><a class="pep reference external" href="https://peps.python.org/pep-0544/"><strong>PEP 544</strong></a> for more details. Protocol classes decorated with
<a class="reference internal" href="#typing.runtime_checkable" title="typing.runtime_checkable"><code class="xref py py-func docutils literal notranslate"><span class="pre">runtime_checkable()</span></code></a> (described later) act as simple-minded runtime
protocols that check only the presence of given attributes, ignoring their
type signatures.</p>
<p>Protocol classes can be generic, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">GenProto</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">Protocol</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">meth</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T</span><span class="p">:</span>
        <span class="o">...</span>
</pre></div>
</div>
<p>In code that needs to be compatible with Python 3.11 or older, generic
Protocols can be written as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">GenProto</span><span class="p">(</span><span class="n">Protocol</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
    <span class="k">def</span> <span class="nf">meth</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T</span><span class="p">:</span>
        <span class="o">...</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.runtime_checkable">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">runtime_checkable</span></span><a class="headerlink" href="#typing.runtime_checkable" title="Link to this definition">¶</a></dt>
<dd><p>Mark a protocol class as a runtime protocol.</p>
<p>Such a protocol can be used with <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> and <a class="reference internal" href="functions.html#issubclass" title="issubclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">issubclass()</span></code></a>.
This raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> when applied to a non-protocol class.  This
allows a simple-minded structural check, very similar to “one trick ponies”
in <a class="reference internal" href="collections.abc.html#module-collections.abc" title="collections.abc: Abstract base classes for containers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a> such as <a class="reference internal" href="collections.abc.html#collections.abc.Iterable" title="collections.abc.Iterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">Iterable</span></code></a>.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@runtime_checkable</span>
<span class="k">class</span> <span class="nc">Closable</span><span class="p">(</span><span class="n">Protocol</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">close</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span> <span class="o">...</span>

<span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="nb">open</span><span class="p">(</span><span class="s1">&#39;/some/file&#39;</span><span class="p">),</span> <span class="n">Closable</span><span class="p">)</span>

<span class="nd">@runtime_checkable</span>
<span class="k">class</span> <span class="nc">Named</span><span class="p">(</span><span class="n">Protocol</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

<span class="kn">import</span> <span class="nn">threading</span>
<span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">threading</span><span class="o">.</span><span class="n">Thread</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s1">&#39;Bob&#39;</span><span class="p">),</span> <span class="n">Named</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="xref py py-func docutils literal notranslate"><span class="pre">runtime_checkable()</span></code> will check only the presence of the required
methods or attributes, not their type signatures or types.
For example, <a class="reference internal" href="ssl.html#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLObject</span></code></a>
is a class, therefore it passes an <a class="reference internal" href="functions.html#issubclass" title="issubclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">issubclass()</span></code></a>
check against <a class="reference internal" href="#annotating-callables"><span class="std std-ref">Callable</span></a>. However, the
<code class="docutils literal notranslate"><span class="pre">ssl.SSLObject.__init__</span></code> method exists only to raise a
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> with a more informative message, therefore making
it impossible to call (instantiate) <a class="reference internal" href="ssl.html#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLObject</span></code></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>An <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> check against a runtime-checkable protocol can be
surprisingly slow compared to an <code class="docutils literal notranslate"><span class="pre">isinstance()</span></code> check against
a non-protocol class. Consider using alternative idioms such as
<a class="reference internal" href="functions.html#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a> calls for structural checks in performance-sensitive
code.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The internal implementation of <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> checks against
runtime-checkable protocols now uses <a class="reference internal" href="inspect.html#inspect.getattr_static" title="inspect.getattr_static"><code class="xref py py-func docutils literal notranslate"><span class="pre">inspect.getattr_static()</span></code></a>
to look up attributes (previously, <a class="reference internal" href="functions.html#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a> was used).
As a result, some objects which used to be considered instances
of a runtime-checkable protocol may no longer be considered instances
of that protocol on Python 3.12+, and vice versa.
Most users are unlikely to be affected by this change.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The members of a runtime-checkable protocol are now considered “frozen”
at runtime as soon as the class has been created. Monkey-patching
attributes onto a runtime-checkable protocol will still work, but will
have no impact on <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> checks comparing objects to the
protocol. See <a class="reference internal" href="../whatsnew/3.12.html#whatsnew-typing-py312"><span class="std std-ref">“What’s new in Python 3.12”</span></a>
for more details.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.TypedDict">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TypedDict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dict</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.TypedDict" title="Link to this definition">¶</a></dt>
<dd><p>Special construct to add type hints to a dictionary.
At runtime it is a plain <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.</p>
<p><code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> declares a dictionary type that expects all of its
instances to have a certain set of keys, where each key is
associated with a value of a consistent type. This expectation
is not checked at runtime but is only enforced by type checkers.
Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">x</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">y</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">label</span><span class="p">:</span> <span class="nb">str</span>

<span class="n">a</span><span class="p">:</span> <span class="n">Point2D</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;label&#39;</span><span class="p">:</span> <span class="s1">&#39;good&#39;</span><span class="p">}</span>  <span class="c1"># OK</span>
<span class="n">b</span><span class="p">:</span> <span class="n">Point2D</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;z&#39;</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span> <span class="s1">&#39;label&#39;</span><span class="p">:</span> <span class="s1">&#39;bad&#39;</span><span class="p">}</span>           <span class="c1"># Fails type check</span>

<span class="k">assert</span> <span class="n">Point2D</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">label</span><span class="o">=</span><span class="s1">&#39;first&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="nb">dict</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">label</span><span class="o">=</span><span class="s1">&#39;first&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>To allow using this feature with older versions of Python that do not
support <span class="target" id="index-27"></span><a class="pep reference external" href="https://peps.python.org/pep-0526/"><strong>PEP 526</strong></a>, <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> supports two additional equivalent
syntactic forms:</p>
<ul>
<li><p>Using a literal <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> as the second argument:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Point2D</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s1">&#39;Point2D&#39;</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;label&#39;</span><span class="p">:</span> <span class="nb">str</span><span class="p">})</span>
</pre></div>
</div>
</li>
<li><p>Using keyword arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Point2D</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s1">&#39;Point2D&#39;</span><span class="p">,</span> <span class="n">x</span><span class="o">=</span><span class="nb">int</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="nb">int</span><span class="p">,</span> <span class="n">label</span><span class="o">=</span><span class="nb">str</span><span class="p">)</span>
</pre></div>
</div>
</li>
</ul>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The keyword-argument syntax is deprecated in 3.11 and will be removed
in 3.13. It may also be unsupported by static type checkers.</p>
</div>
<p>The functional syntax should also be used when any of the keys are not valid
<a class="reference internal" href="../reference/lexical_analysis.html#identifiers"><span class="std std-ref">identifiers</span></a>, for example because they are keywords or contain hyphens.
Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># raises SyntaxError</span>
<span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="ow">in</span><span class="p">:</span> <span class="nb">int</span>  <span class="c1"># &#39;in&#39; is a keyword</span>
    <span class="n">x</span><span class="o">-</span><span class="n">y</span><span class="p">:</span> <span class="nb">int</span>  <span class="c1"># name with hyphens</span>

<span class="c1"># OK, functional syntax</span>
<span class="n">Point2D</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s1">&#39;Point2D&#39;</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;in&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;x-y&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">})</span>
</pre></div>
</div>
<p>By default, all keys must be present in a <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code>. It is possible to
mark individual keys as non-required using <a class="reference internal" href="#typing.NotRequired" title="typing.NotRequired"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotRequired</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">x</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">y</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">label</span><span class="p">:</span> <span class="n">NotRequired</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

<span class="c1"># Alternative syntax</span>
<span class="n">Point2D</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s1">&#39;Point2D&#39;</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;label&#39;</span><span class="p">:</span> <span class="n">NotRequired</span><span class="p">[</span><span class="nb">str</span><span class="p">]})</span>
</pre></div>
</div>
<p>This means that a <code class="docutils literal notranslate"><span class="pre">Point2D</span></code> <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> can have the <code class="docutils literal notranslate"><span class="pre">label</span></code>
key omitted.</p>
<p>It is also possible to mark all keys as non-required by default
by specifying a totality of <code class="docutils literal notranslate"><span class="pre">False</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
    <span class="n">x</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">y</span><span class="p">:</span> <span class="nb">int</span>

<span class="c1"># Alternative syntax</span>
<span class="n">Point2D</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s1">&#39;Point2D&#39;</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span> <span class="nb">int</span><span class="p">},</span> <span class="n">total</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</pre></div>
</div>
<p>This means that a <code class="docutils literal notranslate"><span class="pre">Point2D</span></code> <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> can have any of the keys
omitted. A type checker is only expected to support a literal <code class="docutils literal notranslate"><span class="pre">False</span></code> or
<code class="docutils literal notranslate"><span class="pre">True</span></code> as the value of the <code class="docutils literal notranslate"><span class="pre">total</span></code> argument. <code class="docutils literal notranslate"><span class="pre">True</span></code> is the default,
and makes all items defined in the class body required.</p>
<p>Individual keys of a <code class="docutils literal notranslate"><span class="pre">total=False</span></code> <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> can be marked as
required using <a class="reference internal" href="#typing.Required" title="typing.Required"><code class="xref py py-data docutils literal notranslate"><span class="pre">Required</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
    <span class="n">x</span><span class="p">:</span> <span class="n">Required</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>
    <span class="n">y</span><span class="p">:</span> <span class="n">Required</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>
    <span class="n">label</span><span class="p">:</span> <span class="nb">str</span>

<span class="c1"># Alternative syntax</span>
<span class="n">Point2D</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s1">&#39;Point2D&#39;</span><span class="p">,</span> <span class="p">{</span>
    <span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="n">Required</span><span class="p">[</span><span class="nb">int</span><span class="p">],</span>
    <span class="s1">&#39;y&#39;</span><span class="p">:</span> <span class="n">Required</span><span class="p">[</span><span class="nb">int</span><span class="p">],</span>
    <span class="s1">&#39;label&#39;</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">},</span> <span class="n">total</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</pre></div>
</div>
<p>It is possible for a <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> type to inherit from one or more other <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> types
using the class-based syntax.
Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point3D</span><span class="p">(</span><span class="n">Point2D</span><span class="p">):</span>
    <span class="n">z</span><span class="p">:</span> <span class="nb">int</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Point3D</span></code> has three items: <code class="docutils literal notranslate"><span class="pre">x</span></code>, <code class="docutils literal notranslate"><span class="pre">y</span></code> and <code class="docutils literal notranslate"><span class="pre">z</span></code>. It is equivalent to this
definition:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point3D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">x</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">y</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">z</span><span class="p">:</span> <span class="nb">int</span>
</pre></div>
</div>
<p>A <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> cannot inherit from a non-<code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> class,
except for <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a>. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">X</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">x</span><span class="p">:</span> <span class="nb">int</span>

<span class="k">class</span> <span class="nc">Y</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">y</span><span class="p">:</span> <span class="nb">int</span>

<span class="k">class</span> <span class="nc">Z</span><span class="p">(</span><span class="nb">object</span><span class="p">):</span> <span class="k">pass</span>  <span class="c1"># A non-TypedDict class</span>

<span class="k">class</span> <span class="nc">XY</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">):</span> <span class="k">pass</span>  <span class="c1"># OK</span>

<span class="k">class</span> <span class="nc">XZ</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Z</span><span class="p">):</span> <span class="k">pass</span>  <span class="c1"># raises TypeError</span>
</pre></div>
</div>
<p>A <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> can be generic:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Group</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">key</span><span class="p">:</span> <span class="n">T</span>
    <span class="n">group</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">T</span><span class="p">]</span>
</pre></div>
</div>
<p>To create a generic <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> that is compatible with Python 3.11
or lower, inherit from <a class="reference internal" href="#typing.Generic" title="typing.Generic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generic</span></code></a> explicitly:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">Group</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">,</span> <span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
    <span class="n">key</span><span class="p">:</span> <span class="n">T</span>
    <span class="n">group</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">T</span><span class="p">]</span>
</pre></div>
</div>
<p>A <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> can be introspected via annotations dicts
(see <a class="reference internal" href="../howto/annotations.html#annotations-howto"><span class="std std-ref">Annotations Best Practices</span></a> for more information on annotations best practices),
<a class="reference internal" href="#typing.TypedDict.__total__" title="typing.TypedDict.__total__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__total__</span></code></a>, <a class="reference internal" href="#typing.TypedDict.__required_keys__" title="typing.TypedDict.__required_keys__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__required_keys__</span></code></a>, and <a class="reference internal" href="#typing.TypedDict.__optional_keys__" title="typing.TypedDict.__optional_keys__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__optional_keys__</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypedDict.__total__">
<span class="sig-name descname"><span class="pre">__total__</span></span><a class="headerlink" href="#typing.TypedDict.__total__" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Point2D.__total__</span></code> gives the value of the <code class="docutils literal notranslate"><span class="pre">total</span></code> argument.
Example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypedDict</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span> <span class="k">pass</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Point2D</span><span class="o">.</span><span class="n">__total__</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span> <span class="k">pass</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Point2D</span><span class="o">.</span><span class="n">__total__</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Point3D</span><span class="p">(</span><span class="n">Point2D</span><span class="p">):</span> <span class="k">pass</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Point3D</span><span class="o">.</span><span class="n">__total__</span>
<span class="go">True</span>
</pre></div>
</div>
<p>This attribute reflects <em>only</em> the value of the <code class="docutils literal notranslate"><span class="pre">total</span></code> argument
to the current <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> class, not whether the class is semantically
total. For example, a <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> with <code class="docutils literal notranslate"><span class="pre">__total__</span></code> set to True may
have keys marked with <a class="reference internal" href="#typing.NotRequired" title="typing.NotRequired"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotRequired</span></code></a>, or it may inherit from another
<code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> with <code class="docutils literal notranslate"><span class="pre">total=False</span></code>. Therefore, it is generally better to use
<a class="reference internal" href="#typing.TypedDict.__required_keys__" title="typing.TypedDict.__required_keys__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__required_keys__</span></code></a> and <a class="reference internal" href="#typing.TypedDict.__optional_keys__" title="typing.TypedDict.__optional_keys__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__optional_keys__</span></code></a> for introspection.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypedDict.__required_keys__">
<span class="sig-name descname"><span class="pre">__required_keys__</span></span><a class="headerlink" href="#typing.TypedDict.__required_keys__" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="typing.TypedDict.__optional_keys__">
<span class="sig-name descname"><span class="pre">__optional_keys__</span></span><a class="headerlink" href="#typing.TypedDict.__optional_keys__" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Point2D.__required_keys__</span></code> and <code class="docutils literal notranslate"><span class="pre">Point2D.__optional_keys__</span></code> return
<a class="reference internal" href="stdtypes.html#frozenset" title="frozenset"><code class="xref py py-class docutils literal notranslate"><span class="pre">frozenset</span></code></a> objects containing required and non-required keys, respectively.</p>
<p>Keys marked with <a class="reference internal" href="#typing.Required" title="typing.Required"><code class="xref py py-data docutils literal notranslate"><span class="pre">Required</span></code></a> will always appear in <code class="docutils literal notranslate"><span class="pre">__required_keys__</span></code>
and keys marked with <a class="reference internal" href="#typing.NotRequired" title="typing.NotRequired"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotRequired</span></code></a> will always appear in <code class="docutils literal notranslate"><span class="pre">__optional_keys__</span></code>.</p>
<p>For backwards compatibility with Python 3.10 and below,
it is also possible to use inheritance to declare both required and
non-required keys in the same <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> . This is done by declaring a
<code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> with one value for the <code class="docutils literal notranslate"><span class="pre">total</span></code> argument and then
inheriting from it in another <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> with a different value for
<code class="docutils literal notranslate"><span class="pre">total</span></code>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Point2D</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">x</span><span class="p">:</span> <span class="nb">int</span>
<span class="gp">... </span>    <span class="n">y</span><span class="p">:</span> <span class="nb">int</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Point3D</span><span class="p">(</span><span class="n">Point2D</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">z</span><span class="p">:</span> <span class="nb">int</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Point3D</span><span class="o">.</span><span class="n">__required_keys__</span> <span class="o">==</span> <span class="nb">frozenset</span><span class="p">({</span><span class="s1">&#39;z&#39;</span><span class="p">})</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Point3D</span><span class="o">.</span><span class="n">__optional_keys__</span> <span class="o">==</span> <span class="nb">frozenset</span><span class="p">({</span><span class="s1">&#39;x&#39;</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">})</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">annotations</span></code> is used or if annotations
are given as strings, annotations are not evaluated when the
<code class="docutils literal notranslate"><span class="pre">TypedDict</span></code> is defined. Therefore, the runtime introspection that
<code class="docutils literal notranslate"><span class="pre">__required_keys__</span></code> and <code class="docutils literal notranslate"><span class="pre">__optional_keys__</span></code> rely on may not work
properly, and the values of the attributes may be incorrect.</p>
</div>
</dd></dl>

<p>See <span class="target" id="index-28"></span><a class="pep reference external" href="https://peps.python.org/pep-0589/"><strong>PEP 589</strong></a> for more examples and detailed rules of using <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added support for marking individual keys as <a class="reference internal" href="#typing.Required" title="typing.Required"><code class="xref py py-data docutils literal notranslate"><span class="pre">Required</span></code></a> or <a class="reference internal" href="#typing.NotRequired" title="typing.NotRequired"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotRequired</span></code></a>.
See <span class="target" id="index-29"></span><a class="pep reference external" href="https://peps.python.org/pep-0655/"><strong>PEP 655</strong></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added support for generic <code class="docutils literal notranslate"><span class="pre">TypedDict</span></code>s.</p>
</div>
</dd></dl>

</section>
</section>
<section id="protocols">
<h3>Protocols<a class="headerlink" href="#protocols" title="Link to this heading">¶</a></h3>
<p>The following protocols are provided by the typing module. All are decorated
with <a class="reference internal" href="#typing.runtime_checkable" title="typing.runtime_checkable"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;runtime_checkable</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsAbs">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsAbs</span></span><a class="headerlink" href="#typing.SupportsAbs" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__abs__</span></code> that is covariant
in its return type.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsBytes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsBytes</span></span><a class="headerlink" href="#typing.SupportsBytes" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__bytes__</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsComplex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsComplex</span></span><a class="headerlink" href="#typing.SupportsComplex" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__complex__</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsFloat">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsFloat</span></span><a class="headerlink" href="#typing.SupportsFloat" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__float__</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsIndex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsIndex</span></span><a class="headerlink" href="#typing.SupportsIndex" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__index__</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsInt">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsInt</span></span><a class="headerlink" href="#typing.SupportsInt" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__int__</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.SupportsRound">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">SupportsRound</span></span><a class="headerlink" href="#typing.SupportsRound" title="Link to this definition">¶</a></dt>
<dd><p>An ABC with one abstract method <code class="docutils literal notranslate"><span class="pre">__round__</span></code>
that is covariant in its return type.</p>
</dd></dl>

</section>
<section id="abcs-for-working-with-io">
<h3>ABCs for working with IO<a class="headerlink" href="#abcs-for-working-with-io" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="typing.IO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">IO</span></span><a class="headerlink" href="#typing.IO" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="typing.TextIO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TextIO</span></span><a class="headerlink" href="#typing.TextIO" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="typing.BinaryIO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">BinaryIO</span></span><a class="headerlink" href="#typing.BinaryIO" title="Link to this definition">¶</a></dt>
<dd><p>Generic type <code class="docutils literal notranslate"><span class="pre">IO[AnyStr]</span></code> and its subclasses <code class="docutils literal notranslate"><span class="pre">TextIO(IO[str])</span></code>
and <code class="docutils literal notranslate"><span class="pre">BinaryIO(IO[bytes])</span></code>
represent the types of I/O streams such as returned by
<a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.</p>
</dd></dl>

</section>
<section id="functions-and-decorators">
<h3>Functions and decorators<a class="headerlink" href="#functions-and-decorators" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="typing.cast">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">cast</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">typ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.cast" title="Link to this definition">¶</a></dt>
<dd><p>Cast a value to a type.</p>
<p>This returns the value unchanged.  To the type checker this
signals that the return value has the designated type, but at
runtime we intentionally don’t check anything (we want this
to be as fast as possible).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.assert_type">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">assert_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">val</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">typ</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.assert_type" title="Link to this definition">¶</a></dt>
<dd><p>Ask a static type checker to confirm that <em>val</em> has an inferred type of <em>typ</em>.</p>
<p>At runtime this does nothing: it returns the first argument unchanged with no
checks or side effects, no matter the actual type of the argument.</p>
<p>When a static type checker encounters a call to <code class="docutils literal notranslate"><span class="pre">assert_type()</span></code>, it
emits an error if the value is not of the specified type:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">greet</span><span class="p">(</span><span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="n">assert_type</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span>  <span class="c1"># OK, inferred type of `name` is `str`</span>
    <span class="n">assert_type</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>  <span class="c1"># type checker error</span>
</pre></div>
</div>
<p>This function is useful for ensuring the type checker’s understanding of a
script is in line with the developer’s intentions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">complex_function</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="nb">object</span><span class="p">):</span>
    <span class="c1"># Do some complex type-narrowing logic,</span>
    <span class="c1"># after which we hope the inferred type will be `int`</span>
    <span class="o">...</span>
    <span class="c1"># Test whether the type checker correctly understands our function</span>
    <span class="n">assert_type</span><span class="p">(</span><span class="n">arg</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.assert_never">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">assert_never</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.assert_never" title="Link to this definition">¶</a></dt>
<dd><p>Ask a static type checker to confirm that a line of code is unreachable.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">int_or_str</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="nb">int</span> <span class="o">|</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="k">match</span> <span class="n">arg</span><span class="p">:</span>
        <span class="k">case</span> <span class="nb">int</span><span class="p">():</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;It&#39;s an int&quot;</span><span class="p">)</span>
        <span class="k">case</span> <span class="nb">str</span><span class="p">():</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;It&#39;s a str&quot;</span><span class="p">)</span>
        <span class="k">case</span><span class="w"> </span><span class="k">_</span> <span class="k">as</span> <span class="n">unreachable</span><span class="p">:</span>
            <span class="n">assert_never</span><span class="p">(</span><span class="n">unreachable</span><span class="p">)</span>
</pre></div>
</div>
<p>Here, the annotations allow the type checker to infer that the
last case can never execute, because <code class="docutils literal notranslate"><span class="pre">arg</span></code> is either
an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> or a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, and both options are covered by
earlier cases.</p>
<p>If a type checker finds that a call to <code class="docutils literal notranslate"><span class="pre">assert_never()</span></code> is
reachable, it will emit an error. For example, if the type annotation
for <code class="docutils literal notranslate"><span class="pre">arg</span></code> was instead <code class="docutils literal notranslate"><span class="pre">int</span> <span class="pre">|</span> <span class="pre">str</span> <span class="pre">|</span> <span class="pre">float</span></code>, the type checker would
emit an error pointing out that <code class="docutils literal notranslate"><span class="pre">unreachable</span></code> is of type <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>.
For a call to <code class="docutils literal notranslate"><span class="pre">assert_never</span></code> to pass type checking, the inferred type of
the argument passed in must be the bottom type, <a class="reference internal" href="#typing.Never" title="typing.Never"><code class="xref py py-data docutils literal notranslate"><span class="pre">Never</span></code></a>, and nothing
else.</p>
<p>At runtime, this throws an exception when called.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://typing.readthedocs.io/en/latest/source/unreachable.html">Unreachable Code and Exhaustiveness Checking</a> has more
information about exhaustiveness checking with static typing.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.reveal_type">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">reveal_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.reveal_type" title="Link to this definition">¶</a></dt>
<dd><p>Ask a static type checker to reveal the inferred type of an expression.</p>
<p>When a static type checker encounters a call to this function,
it emits a diagnostic with the inferred type of the argument. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span>
<span class="n">reveal_type</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>  <span class="c1"># Revealed type is &quot;builtins.int&quot;</span>
</pre></div>
</div>
<p>This can be useful when you want to debug how your type checker
handles a particular piece of code.</p>
<p>At runtime, this function prints the runtime type of its argument to
<a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a> and returns the argument unchanged (allowing the call to
be used within an expression):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span> <span class="o">=</span> <span class="n">reveal_type</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>  <span class="c1"># prints &quot;Runtime type is int&quot;</span>
<span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>  <span class="c1"># prints &quot;1&quot;</span>
</pre></div>
</div>
<p>Note that the runtime type may be different from (more or less specific
than) the type statically inferred by a type checker.</p>
<p>Most type checkers support <code class="docutils literal notranslate"><span class="pre">reveal_type()</span></code> anywhere, even if the
name is not imported from <code class="docutils literal notranslate"><span class="pre">typing</span></code>. Importing the name from
<code class="docutils literal notranslate"><span class="pre">typing</span></code>, however, allows your code to run without runtime errors and
communicates intent more clearly.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.dataclass_transform">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">dataclass_transform</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">eq_default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">order_default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kw_only_default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frozen_default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">field_specifiers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.dataclass_transform" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to mark an object as providing
<a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">dataclass</span></code></a>-like behavior.</p>
<p><code class="docutils literal notranslate"><span class="pre">dataclass_transform</span></code> may be used to
decorate a class, metaclass, or a function that is itself a decorator.
The presence of <code class="docutils literal notranslate"><span class="pre">&#64;dataclass_transform()</span></code> tells a static type checker that the
decorated object performs runtime “magic” that
transforms a class in a similar way to
<a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;dataclasses.dataclass</span></code></a>.</p>
<p>Example usage with a decorator function:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@dataclass_transform</span><span class="p">()</span>
<span class="k">def</span> <span class="nf">create_model</span><span class="p">[</span><span class="n">T</span><span class="p">](</span><span class="bp">cls</span><span class="p">:</span> <span class="nb">type</span><span class="p">[</span><span class="n">T</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">type</span><span class="p">[</span><span class="n">T</span><span class="p">]:</span>
    <span class="o">...</span>
    <span class="k">return</span> <span class="bp">cls</span>

<span class="nd">@create_model</span>
<span class="k">class</span> <span class="nc">CustomerModel</span><span class="p">:</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
</pre></div>
</div>
<p>On a base class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@dataclass_transform</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">ModelBase</span><span class="p">:</span> <span class="o">...</span>

<span class="k">class</span> <span class="nc">CustomerModel</span><span class="p">(</span><span class="n">ModelBase</span><span class="p">):</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
</pre></div>
</div>
<p>On a metaclass:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@dataclass_transform</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">ModelMeta</span><span class="p">(</span><span class="nb">type</span><span class="p">):</span> <span class="o">...</span>

<span class="k">class</span> <span class="nc">ModelBase</span><span class="p">(</span><span class="n">metaclass</span><span class="o">=</span><span class="n">ModelMeta</span><span class="p">):</span> <span class="o">...</span>

<span class="k">class</span> <span class="nc">CustomerModel</span><span class="p">(</span><span class="n">ModelBase</span><span class="p">):</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">CustomerModel</span></code> classes defined above will
be treated by type checkers similarly to classes created with
<a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;dataclasses.dataclass</span></code></a>.
For example, type checkers will assume these classes have
<code class="docutils literal notranslate"><span class="pre">__init__</span></code> methods that accept <code class="docutils literal notranslate"><span class="pre">id</span></code> and <code class="docutils literal notranslate"><span class="pre">name</span></code>.</p>
<p>The decorated class, metaclass, or function may accept the following bool
arguments which type checkers will assume have the same effect as they
would have on the
<a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;dataclasses.dataclass</span></code></a> decorator: <code class="docutils literal notranslate"><span class="pre">init</span></code>,
<code class="docutils literal notranslate"><span class="pre">eq</span></code>, <code class="docutils literal notranslate"><span class="pre">order</span></code>, <code class="docutils literal notranslate"><span class="pre">unsafe_hash</span></code>, <code class="docutils literal notranslate"><span class="pre">frozen</span></code>, <code class="docutils literal notranslate"><span class="pre">match_args</span></code>,
<code class="docutils literal notranslate"><span class="pre">kw_only</span></code>, and <code class="docutils literal notranslate"><span class="pre">slots</span></code>. It must be possible for the value of these
arguments (<code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code>) to be statically evaluated.</p>
<p>The arguments to the <code class="docutils literal notranslate"><span class="pre">dataclass_transform</span></code> decorator can be used to
customize the default behaviors of the decorated class, metaclass, or
function:</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>eq_default</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – Indicates whether the <code class="docutils literal notranslate"><span class="pre">eq</span></code> parameter is assumed to be
<code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> if it is omitted by the caller.
Defaults to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p></li>
<li><p><strong>order_default</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – Indicates whether the <code class="docutils literal notranslate"><span class="pre">order</span></code> parameter is
assumed to be <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> if it is omitted by the caller.
Defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><strong>kw_only_default</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – Indicates whether the <code class="docutils literal notranslate"><span class="pre">kw_only</span></code> parameter is
assumed to be <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> if it is omitted by the caller.
Defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><strong>frozen_default</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – <p>Indicates whether the <code class="docutils literal notranslate"><span class="pre">frozen</span></code> parameter is
assumed to be <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> if it is omitted by the caller.
Defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</p></li>
<li><p><strong>field_specifiers</strong> (<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><em>tuple</em></a><em>[</em><a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><em>Callable</em></a><em>[</em><em>...</em><em>, </em><em>Any</em><em>]</em><em>, </em><em>...</em><em>]</em>) – Specifies a static list of supported classes
or functions that describe fields, similar to <a class="reference internal" href="dataclasses.html#dataclasses.field" title="dataclasses.field"><code class="xref py py-func docutils literal notranslate"><span class="pre">dataclasses.field()</span></code></a>.
Defaults to <code class="docutils literal notranslate"><span class="pre">()</span></code>.</p></li>
<li><p><strong>**kwargs</strong> (<em>Any</em>) – Arbitrary other keyword arguments are accepted in order to allow for
possible future extensions.</p></li>
</ul>
</dd>
</dl>
<p>Type checkers recognize the following optional parameters on field
specifiers:</p>
<table class="docutils align-default" id="id7">
<caption><span class="caption-text"><strong>Recognised parameters for field specifiers</strong></span><a class="headerlink" href="#id7" title="Link to this table">¶</a></caption>
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Parameter name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">init</span></code></p></td>
<td><p>Indicates whether the field should be included in the
synthesized <code class="docutils literal notranslate"><span class="pre">__init__</span></code> method. If unspecified, <code class="docutils literal notranslate"><span class="pre">init</span></code> defaults to
<code class="docutils literal notranslate"><span class="pre">True</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">default</span></code></p></td>
<td><p>Provides the default value for the field.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">default_factory</span></code></p></td>
<td><p>Provides a runtime callback that returns the
default value for the field. If neither <code class="docutils literal notranslate"><span class="pre">default</span></code> nor
<code class="docutils literal notranslate"><span class="pre">default_factory</span></code> are specified, the field is assumed to have no
default value and must be provided a value when the class is
instantiated.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">factory</span></code></p></td>
<td><p>An alias for the <code class="docutils literal notranslate"><span class="pre">default_factory</span></code> parameter on field specifiers.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kw_only</span></code></p></td>
<td><p>Indicates whether the field should be marked as
keyword-only. If <code class="docutils literal notranslate"><span class="pre">True</span></code>, the field will be keyword-only. If
<code class="docutils literal notranslate"><span class="pre">False</span></code>, it will not be keyword-only. If unspecified, the value of
the <code class="docutils literal notranslate"><span class="pre">kw_only</span></code> parameter on the object decorated with
<code class="docutils literal notranslate"><span class="pre">dataclass_transform</span></code> will be used, or if that is unspecified, the
value of <code class="docutils literal notranslate"><span class="pre">kw_only_default</span></code> on <code class="docutils literal notranslate"><span class="pre">dataclass_transform</span></code> will be used.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">alias</span></code></p></td>
<td><p>Provides an alternative name for the field. This alternative
name is used in the synthesized <code class="docutils literal notranslate"><span class="pre">__init__</span></code> method.</p></td>
</tr>
</tbody>
</table>
<p>At runtime, this decorator records its arguments in the
<code class="docutils literal notranslate"><span class="pre">__dataclass_transform__</span></code> attribute on the decorated object.
It has no other runtime effect.</p>
<p>See <span class="target" id="index-30"></span><a class="pep reference external" href="https://peps.python.org/pep-0681/"><strong>PEP 681</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function" id="overload">
<dt class="sig sig-object py" id="typing.overload">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">overload</span></span><a class="headerlink" href="#typing.overload" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for creating overloaded functions and methods.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code> decorator allows describing functions and methods
that support multiple different combinations of argument types. A series
of <code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code>-decorated definitions must be followed by exactly one
non-<code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code>-decorated definition (for the same function/method).</p>
<p><code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code>-decorated definitions are for the benefit of the
type checker only, since they will be overwritten by the
non-<code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code>-decorated definition. The non-<code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code>-decorated
definition, meanwhile, will be used at
runtime but should be ignored by a type checker.  At runtime, calling
an <code class="docutils literal notranslate"><span class="pre">&#64;overload</span></code>-decorated function directly will raise
<a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
<p>An example of overload that gives a more
precise type than can be expressed using a union or a type variable:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@overload</span>
<span class="k">def</span> <span class="nf">process</span><span class="p">(</span><span class="n">response</span><span class="p">:</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="o">...</span>
<span class="nd">@overload</span>
<span class="k">def</span> <span class="nf">process</span><span class="p">(</span><span class="n">response</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
    <span class="o">...</span>
<span class="nd">@overload</span>
<span class="k">def</span> <span class="nf">process</span><span class="p">(</span><span class="n">response</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
    <span class="o">...</span>
<span class="k">def</span> <span class="nf">process</span><span class="p">(</span><span class="n">response</span><span class="p">):</span>
    <span class="o">...</span>  <span class="c1"># actual implementation goes here</span>
</pre></div>
</div>
<p>See <span class="target" id="index-31"></span><a class="pep reference external" href="https://peps.python.org/pep-0484/"><strong>PEP 484</strong></a> for more details and comparison with other typing semantics.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Overloaded functions can now be introspected at runtime using
<a class="reference internal" href="#typing.get_overloads" title="typing.get_overloads"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_overloads()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.get_overloads">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">get_overloads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.get_overloads" title="Link to this definition">¶</a></dt>
<dd><p>Return a sequence of <a class="reference internal" href="#typing.overload" title="typing.overload"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;overload</span></code></a>-decorated definitions for
<em>func</em>.</p>
<p><em>func</em> is the function object for the implementation of the
overloaded function. For example, given the definition of <code class="docutils literal notranslate"><span class="pre">process</span></code> in
the documentation for <a class="reference internal" href="#typing.overload" title="typing.overload"><code class="xref py py-func docutils literal notranslate"><span class="pre">&#64;overload</span></code></a>,
<code class="docutils literal notranslate"><span class="pre">get_overloads(process)</span></code> will return a sequence of three function objects
for the three defined overloads. If called on a function with no overloads,
<code class="docutils literal notranslate"><span class="pre">get_overloads()</span></code> returns an empty sequence.</p>
<p><code class="docutils literal notranslate"><span class="pre">get_overloads()</span></code> can be used for introspecting an overloaded function at
runtime.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.clear_overloads">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">clear_overloads</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#typing.clear_overloads" title="Link to this definition">¶</a></dt>
<dd><p>Clear all registered overloads in the internal registry.</p>
<p>This can be used to reclaim the memory used by the registry.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.final">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">final</span></span><a class="headerlink" href="#typing.final" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to indicate final methods and final classes.</p>
<p>Decorating a method with <code class="docutils literal notranslate"><span class="pre">&#64;final</span></code> indicates to a type checker that the
method cannot be overridden in a subclass. Decorating a class with <code class="docutils literal notranslate"><span class="pre">&#64;final</span></code>
indicates that it cannot be subclassed.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Base</span><span class="p">:</span>
    <span class="nd">@final</span>
    <span class="k">def</span> <span class="nf">done</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
        <span class="o">...</span>
<span class="k">class</span> <span class="nc">Sub</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">done</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>  <span class="c1"># Error reported by type checker</span>
        <span class="o">...</span>

<span class="nd">@final</span>
<span class="k">class</span> <span class="nc">Leaf</span><span class="p">:</span>
    <span class="o">...</span>
<span class="k">class</span> <span class="nc">Other</span><span class="p">(</span><span class="n">Leaf</span><span class="p">):</span>  <span class="c1"># Error reported by type checker</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>There is no runtime checking of these properties. See <span class="target" id="index-32"></span><a class="pep reference external" href="https://peps.python.org/pep-0591/"><strong>PEP 591</strong></a> for
more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The decorator will now attempt to set a <code class="docutils literal notranslate"><span class="pre">__final__</span></code> attribute to <code class="docutils literal notranslate"><span class="pre">True</span></code>
on the decorated object. Thus, a check like
<code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">getattr(obj,</span> <span class="pre">&quot;__final__&quot;,</span> <span class="pre">False)</span></code> can be used at runtime
to determine whether an object <code class="docutils literal notranslate"><span class="pre">obj</span></code> has been marked as final.
If the decorated object does not support setting attributes,
the decorator returns the object unchanged without raising an exception.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.no_type_check">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">no_type_check</span></span><a class="headerlink" href="#typing.no_type_check" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to indicate that annotations are not type hints.</p>
<p>This works as a class or function <a class="reference internal" href="../glossary.html#term-decorator"><span class="xref std std-term">decorator</span></a>.  With a class, it
applies recursively to all methods and classes defined in that class
(but not to methods defined in its superclasses or subclasses). Type
checkers will ignore all annotations in a function or class with this
decorator.</p>
<p><code class="docutils literal notranslate"><span class="pre">&#64;no_type_check</span></code> mutates the decorated object in place.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.no_type_check_decorator">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">no_type_check_decorator</span></span><a class="headerlink" href="#typing.no_type_check_decorator" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to give another decorator the <a class="reference internal" href="#typing.no_type_check" title="typing.no_type_check"><code class="xref py py-func docutils literal notranslate"><span class="pre">no_type_check()</span></code></a> effect.</p>
<p>This wraps the decorator with something that wraps the decorated
function in <a class="reference internal" href="#typing.no_type_check" title="typing.no_type_check"><code class="xref py py-func docutils literal notranslate"><span class="pre">no_type_check()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.override">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">override</span></span><a class="headerlink" href="#typing.override" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to indicate that a method in a subclass is intended to override a
method or attribute in a superclass.</p>
<p>Type checkers should emit an error if a method decorated with <code class="docutils literal notranslate"><span class="pre">&#64;override</span></code>
does not, in fact, override anything.
This helps prevent bugs that may occur when a base class is changed without
an equivalent change to a child class.</p>
<p>For example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Base</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">log_status</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
        <span class="o">...</span>

<span class="k">class</span> <span class="nc">Sub</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="nd">@override</span>
    <span class="k">def</span> <span class="nf">log_status</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>  <span class="c1"># Okay: overrides Base.log_status</span>
        <span class="o">...</span>

    <span class="nd">@override</span>
    <span class="k">def</span> <span class="nf">done</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>  <span class="c1"># Error reported by type checker</span>
        <span class="o">...</span>
</pre></div>
</div>
<p>There is no runtime checking of this property.</p>
<p>The decorator will attempt to set an <code class="docutils literal notranslate"><span class="pre">__override__</span></code> attribute to <code class="docutils literal notranslate"><span class="pre">True</span></code> on
the decorated object. Thus, a check like
<code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">getattr(obj,</span> <span class="pre">&quot;__override__&quot;,</span> <span class="pre">False)</span></code> can be used at runtime to determine
whether an object <code class="docutils literal notranslate"><span class="pre">obj</span></code> has been marked as an override.  If the decorated object
does not support setting attributes, the decorator returns the object unchanged
without raising an exception.</p>
<p>See <span class="target" id="index-33"></span><a class="pep reference external" href="https://peps.python.org/pep-0698/"><strong>PEP 698</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.type_check_only">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">type_check_only</span></span><a class="headerlink" href="#typing.type_check_only" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to mark a class or function as unavailable at runtime.</p>
<p>This decorator is itself not available at runtime. It is mainly
intended to mark classes that are defined in type stub files if
an implementation returns an instance of a private class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@type_check_only</span>
<span class="k">class</span> <span class="nc">Response</span><span class="p">:</span>  <span class="c1"># private or not available at runtime</span>
    <span class="n">code</span><span class="p">:</span> <span class="nb">int</span>
    <span class="k">def</span> <span class="nf">get_header</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span> <span class="o">...</span>

<span class="k">def</span> <span class="nf">fetch_response</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Response</span><span class="p">:</span> <span class="o">...</span>
</pre></div>
</div>
<p>Note that returning instances of private classes is not recommended.
It is usually preferable to make such classes public.</p>
</dd></dl>

</section>
<section id="introspection-helpers">
<h3>Introspection helpers<a class="headerlink" href="#introspection-helpers" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="typing.get_type_hints">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">get_type_hints</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globalns</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localns</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_extras</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.get_type_hints" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary containing type hints for a function, method, module
or class object.</p>
<p>This is often the same as <code class="docutils literal notranslate"><span class="pre">obj.__annotations__</span></code>. In addition,
forward references encoded as string literals are handled by evaluating
them in <code class="docutils literal notranslate"><span class="pre">globals</span></code> and <code class="docutils literal notranslate"><span class="pre">locals</span></code> namespaces. For a class <code class="docutils literal notranslate"><span class="pre">C</span></code>, return
a dictionary constructed by merging all the <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code> along
<code class="docutils literal notranslate"><span class="pre">C.__mro__</span></code> in reverse order.</p>
<p>The function recursively replaces all <code class="docutils literal notranslate"><span class="pre">Annotated[T,</span> <span class="pre">...]</span></code> with <code class="docutils literal notranslate"><span class="pre">T</span></code>,
unless <code class="docutils literal notranslate"><span class="pre">include_extras</span></code> is set to <code class="docutils literal notranslate"><span class="pre">True</span></code> (see <a class="reference internal" href="#typing.Annotated" title="typing.Annotated"><code class="xref py py-class docutils literal notranslate"><span class="pre">Annotated</span></code></a> for
more information). For example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Student</span><span class="p">(</span><span class="n">NamedTuple</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="s1">&#39;some marker&#39;</span><span class="p">]</span>

<span class="k">assert</span> <span class="n">get_type_hints</span><span class="p">(</span><span class="n">Student</span><span class="p">)</span> <span class="o">==</span> <span class="p">{</span><span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="nb">str</span><span class="p">}</span>
<span class="k">assert</span> <span class="n">get_type_hints</span><span class="p">(</span><span class="n">Student</span><span class="p">,</span> <span class="n">include_extras</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span> <span class="o">==</span> <span class="p">{</span><span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="nb">str</span><span class="p">}</span>
<span class="k">assert</span> <span class="n">get_type_hints</span><span class="p">(</span><span class="n">Student</span><span class="p">,</span> <span class="n">include_extras</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="o">==</span> <span class="p">{</span>
    <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="n">Annotated</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="s1">&#39;some marker&#39;</span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#typing.get_type_hints" title="typing.get_type_hints"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_type_hints()</span></code></a> does not work with imported
<a class="reference internal" href="#type-aliases"><span class="std std-ref">type aliases</span></a> that include forward references.
Enabling postponed evaluation of annotations (<span class="target" id="index-34"></span><a class="pep reference external" href="https://peps.python.org/pep-0563/"><strong>PEP 563</strong></a>) may remove
the need for most forward references.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added <code class="docutils literal notranslate"><span class="pre">include_extras</span></code> parameter as part of <span class="target" id="index-35"></span><a class="pep reference external" href="https://peps.python.org/pep-0593/"><strong>PEP 593</strong></a>.
See the documentation on <a class="reference internal" href="#typing.Annotated" title="typing.Annotated"><code class="xref py py-data docutils literal notranslate"><span class="pre">Annotated</span></code></a> for more information.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Previously, <code class="docutils literal notranslate"><span class="pre">Optional[t]</span></code> was added for function and method annotations
if a default value equal to <code class="docutils literal notranslate"><span class="pre">None</span></code> was set.
Now the annotation is returned unchanged.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.get_origin">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">get_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.get_origin" title="Link to this definition">¶</a></dt>
<dd><p>Get the unsubscripted version of a type: for a typing object of the form
<code class="docutils literal notranslate"><span class="pre">X[Y,</span> <span class="pre">Z,</span> <span class="pre">...]</span></code> return <code class="docutils literal notranslate"><span class="pre">X</span></code>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">X</span></code> is a typing-module alias for a builtin or
<a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a> class, it will be normalized to the original class.
If <code class="docutils literal notranslate"><span class="pre">X</span></code> is an instance of <a class="reference internal" href="#typing.ParamSpecArgs" title="typing.ParamSpecArgs"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpecArgs</span></code></a> or <a class="reference internal" href="#typing.ParamSpecKwargs" title="typing.ParamSpecKwargs"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpecKwargs</span></code></a>,
return the underlying <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a>.
Return <code class="docutils literal notranslate"><span class="pre">None</span></code> for unsupported objects.</p>
<p>Examples:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">assert</span> <span class="n">get_origin</span><span class="p">(</span><span class="nb">str</span><span class="p">)</span> <span class="ow">is</span> <span class="kc">None</span>
<span class="k">assert</span> <span class="n">get_origin</span><span class="p">(</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">])</span> <span class="ow">is</span> <span class="nb">dict</span>
<span class="k">assert</span> <span class="n">get_origin</span><span class="p">(</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">])</span> <span class="ow">is</span> <span class="n">Union</span>
<span class="n">P</span> <span class="o">=</span> <span class="n">ParamSpec</span><span class="p">(</span><span class="s1">&#39;P&#39;</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">get_origin</span><span class="p">(</span><span class="n">P</span><span class="o">.</span><span class="n">args</span><span class="p">)</span> <span class="ow">is</span> <span class="n">P</span>
<span class="k">assert</span> <span class="n">get_origin</span><span class="p">(</span><span class="n">P</span><span class="o">.</span><span class="n">kwargs</span><span class="p">)</span> <span class="ow">is</span> <span class="n">P</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.get_args">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">get_args</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.get_args" title="Link to this definition">¶</a></dt>
<dd><p>Get type arguments with all substitutions performed: for a typing object
of the form <code class="docutils literal notranslate"><span class="pre">X[Y,</span> <span class="pre">Z,</span> <span class="pre">...]</span></code> return <code class="docutils literal notranslate"><span class="pre">(Y,</span> <span class="pre">Z,</span> <span class="pre">...)</span></code>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">X</span></code> is a union or <a class="reference internal" href="#typing.Literal" title="typing.Literal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Literal</span></code></a> contained in another
generic type, the order of <code class="docutils literal notranslate"><span class="pre">(Y,</span> <span class="pre">Z,</span> <span class="pre">...)</span></code> may be different from the order
of the original arguments <code class="docutils literal notranslate"><span class="pre">[Y,</span> <span class="pre">Z,</span> <span class="pre">...]</span></code> due to type caching.
Return <code class="docutils literal notranslate"><span class="pre">()</span></code> for unsupported objects.</p>
<p>Examples:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">assert</span> <span class="n">get_args</span><span class="p">(</span><span class="nb">int</span><span class="p">)</span> <span class="o">==</span> <span class="p">()</span>
<span class="k">assert</span> <span class="n">get_args</span><span class="p">(</span><span class="n">Dict</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">])</span> <span class="o">==</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">get_args</span><span class="p">(</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">])</span> <span class="o">==</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="typing.is_typeddict">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">is_typeddict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.is_typeddict" title="Link to this definition">¶</a></dt>
<dd><p>Check if a type is a <a class="reference internal" href="#typing.TypedDict" title="typing.TypedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></a>.</p>
<p>For example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Film</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">):</span>
    <span class="n">title</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">year</span><span class="p">:</span> <span class="nb">int</span>

<span class="k">assert</span> <span class="n">is_typeddict</span><span class="p">(</span><span class="n">Film</span><span class="p">)</span>
<span class="k">assert</span> <span class="ow">not</span> <span class="n">is_typeddict</span><span class="p">(</span><span class="nb">list</span> <span class="o">|</span> <span class="nb">str</span><span class="p">)</span>

<span class="c1"># TypedDict is a factory for creating typed dicts,</span>
<span class="c1"># not a typed dict itself</span>
<span class="k">assert</span> <span class="ow">not</span> <span class="n">is_typeddict</span><span class="p">(</span><span class="n">TypedDict</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.ForwardRef">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ForwardRef</span></span><a class="headerlink" href="#typing.ForwardRef" title="Link to this definition">¶</a></dt>
<dd><p>Class used for internal typing representation of string forward references.</p>
<p>For example, <code class="docutils literal notranslate"><span class="pre">List[&quot;SomeClass&quot;]</span></code> is implicitly transformed into
<code class="docutils literal notranslate"><span class="pre">List[ForwardRef(&quot;SomeClass&quot;)]</span></code>.  <code class="docutils literal notranslate"><span class="pre">ForwardRef</span></code> should not be instantiated by
a user, but may be used by introspection tools.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><span class="target" id="index-36"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> generic types such as <code class="docutils literal notranslate"><span class="pre">list[&quot;SomeClass&quot;]</span></code> will not be
implicitly transformed into <code class="docutils literal notranslate"><span class="pre">list[ForwardRef(&quot;SomeClass&quot;)]</span></code> and thus
will not automatically resolve to <code class="docutils literal notranslate"><span class="pre">list[SomeClass]</span></code>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.4.</span></p>
</div>
</dd></dl>

</section>
<section id="constant">
<h3>Constant<a class="headerlink" href="#constant" title="Link to this heading">¶</a></h3>
<dl class="py data">
<dt class="sig sig-object py" id="typing.TYPE_CHECKING">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">TYPE_CHECKING</span></span><a class="headerlink" href="#typing.TYPE_CHECKING" title="Link to this definition">¶</a></dt>
<dd><p>A special constant that is assumed to be <code class="docutils literal notranslate"><span class="pre">True</span></code> by 3rd party static
type checkers. It is <code class="docutils literal notranslate"><span class="pre">False</span></code> at runtime.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">TYPE_CHECKING</span><span class="p">:</span>
    <span class="kn">import</span> <span class="nn">expensive_mod</span>

<span class="k">def</span> <span class="nf">fun</span><span class="p">(</span><span class="n">arg</span><span class="p">:</span> <span class="s1">&#39;expensive_mod.SomeType&#39;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="n">local_var</span><span class="p">:</span> <span class="n">expensive_mod</span><span class="o">.</span><span class="n">AnotherType</span> <span class="o">=</span> <span class="n">other_fun</span><span class="p">()</span>
</pre></div>
</div>
<p>The first type annotation must be enclosed in quotes, making it a
“forward reference”, to hide the <code class="docutils literal notranslate"><span class="pre">expensive_mod</span></code> reference from the
interpreter runtime.  Type annotations for local variables are not
evaluated, so the second annotation does not need to be enclosed in quotes.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">annotations</span></code> is used,
annotations are not evaluated at function definition time.
Instead, they are stored as strings in <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code>.
This makes it unnecessary to use quotes around the annotation
(see <span class="target" id="index-37"></span><a class="pep reference external" href="https://peps.python.org/pep-0563/"><strong>PEP 563</strong></a>).</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
</dd></dl>

</section>
<section id="deprecated-aliases">
<span id="generic-concrete-collections"></span><span id="id6"></span><h3>Deprecated aliases<a class="headerlink" href="#deprecated-aliases" title="Link to this heading">¶</a></h3>
<p>This module defines several deprecated aliases to pre-existing
standard library classes. These were originally included in the typing
module in order to support parameterizing these generic classes using <code class="docutils literal notranslate"><span class="pre">[]</span></code>.
However, the aliases became redundant in Python 3.9 when the
corresponding pre-existing classes were enhanced to support <code class="docutils literal notranslate"><span class="pre">[]</span></code> (see
<span class="target" id="index-38"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a>).</p>
<p>The redundant types are deprecated as of Python 3.9. However, while the aliases
may be removed at some point, removal of these aliases is not currently
planned. As such, no deprecation warnings are currently issued by the
interpreter for these aliases.</p>
<p>If at some point it is decided to remove these deprecated aliases, a
deprecation warning will be issued by the interpreter for at least two releases
prior to removal. The aliases are guaranteed to remain in the typing module
without deprecation warnings until at least Python 3.14.</p>
<p>Type checkers are encouraged to flag uses of the deprecated types if the
program they are checking targets a minimum Python version of 3.9 or newer.</p>
<section id="aliases-to-built-in-types">
<span id="corresponding-to-built-in-types"></span><h4>Aliases to built-in types<a class="headerlink" href="#aliases-to-built-in-types" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="typing.Dict">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Dict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">dict,</span> <span class="pre">MutableMapping[KT,</span> <span class="pre">VT]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Dict" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.</p>
<p>Note that to annotate arguments, it is preferred
to use an abstract collection type such as <a class="reference internal" href="#typing.Mapping" title="typing.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mapping</span></code></a>
rather than to use <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> or <code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Dict</span></code>.</p>
<p>This type can be used as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">count_words</span><span class="p">(</span><span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]:</span>
    <span class="o">...</span>
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.dict</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-39"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.List">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">List</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">list,</span> <span class="pre">MutableSequence[T]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.List" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>.</p>
<p>Note that to annotate arguments, it is preferred
to use an abstract collection type such as <a class="reference internal" href="#typing.Sequence" title="typing.Sequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">Sequence</span></code></a> or
<a class="reference internal" href="#typing.Iterable" title="typing.Iterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">Iterable</span></code></a> rather than to use <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> or <code class="xref py py-class docutils literal notranslate"><span class="pre">typing.List</span></code>.</p>
<p>This type may be used as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">vec2</span><span class="p">[</span><span class="n">T</span><span class="p">:</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">)](</span><span class="n">x</span><span class="p">:</span> <span class="n">T</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="n">T</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">T</span><span class="p">]:</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">]</span>

<span class="k">def</span> <span class="nf">keep_positives</span><span class="p">[</span><span class="n">T</span><span class="p">:</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">)](</span><span class="n">vector</span><span class="p">:</span> <span class="n">Sequence</span><span class="p">[</span><span class="n">T</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">T</span><span class="p">]:</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">item</span> <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">vector</span> <span class="k">if</span> <span class="n">item</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">]</span>
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.list</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-40"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Set">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">set,</span> <span class="pre">MutableSet[T]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Set" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.set</span></code></a>.</p>
<p>Note that to annotate arguments, it is preferred
to use an abstract collection type such as <a class="reference internal" href="#typing.AbstractSet" title="typing.AbstractSet"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractSet</span></code></a>
rather than to use <a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">set</span></code></a> or <code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Set</span></code>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.set</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-41"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.FrozenSet">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">FrozenSet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">frozenset,</span> <span class="pre">AbstractSet[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.FrozenSet" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="stdtypes.html#frozenset" title="frozenset"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.frozenset</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="stdtypes.html#frozenset" title="frozenset"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.frozenset</span></code></a>
now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-42"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Tuple">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Tuple</span></span><a class="headerlink" href="#typing.Tuple" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias for <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>.</p>
<p><a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> and <code class="docutils literal notranslate"><span class="pre">Tuple</span></code> are special-cased in the type system; see
<a class="reference internal" href="#annotating-tuples"><span class="std std-ref">Annotating tuples</span></a> for more details.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.tuple</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-43"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Type">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[CT_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Type" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
<p>See <a class="reference internal" href="#type-of-class-objects"><span class="std std-ref">The type of class objects</span></a> for details on using <a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> or
<code class="docutils literal notranslate"><span class="pre">typing.Type</span></code> in type annotations.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">builtins.type</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-44"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

</section>
<section id="aliases-to-types-in-collections">
<span id="corresponding-to-types-in-collections"></span><h4>Aliases to types in <a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a><a class="headerlink" href="#aliases-to-types-in-collections" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="typing.DefaultDict">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">DefaultDict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">collections.defaultdict,</span> <span class="pre">MutableMapping[KT,</span> <span class="pre">VT]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.DefaultDict" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.html#collections.defaultdict" title="collections.defaultdict"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.defaultdict</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.html#collections.defaultdict" title="collections.defaultdict"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.defaultdict</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-45"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.OrderedDict">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">OrderedDict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">collections.OrderedDict,</span> <span class="pre">MutableMapping[KT,</span> <span class="pre">VT]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.OrderedDict" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.html#collections.OrderedDict" title="collections.OrderedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.OrderedDict</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.html#collections.OrderedDict" title="collections.OrderedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.OrderedDict</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-46"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.ChainMap">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ChainMap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">collections.ChainMap,</span> <span class="pre">MutableMapping[KT,</span> <span class="pre">VT]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.ChainMap" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.html#collections.ChainMap" title="collections.ChainMap"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.ChainMap</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.1.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.html#collections.ChainMap" title="collections.ChainMap"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.ChainMap</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-47"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Counter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Counter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">collections.Counter,</span> <span class="pre">Dict[T,</span> <span class="pre">int]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Counter" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.html#collections.Counter" title="collections.Counter"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.Counter</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.1.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.html#collections.Counter" title="collections.Counter"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.Counter</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-48"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Deque">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Deque</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">deque,</span> <span class="pre">MutableSequence[T]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Deque" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.html#collections.deque" title="collections.deque"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.deque</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.1.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.html#collections.deque" title="collections.deque"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.deque</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-49"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

</section>
<section id="aliases-to-other-concrete-types">
<span id="other-concrete-types"></span><h4>Aliases to other concrete types<a class="headerlink" href="#aliases-to-other-concrete-types" title="Link to this heading">¶</a></h4>
<blockquote>
<div><div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.8, will be removed in version 3.13: </span>The <code class="docutils literal notranslate"><span class="pre">typing.io</span></code> namespace is deprecated and will be removed.
These types should be directly imported from <code class="docutils literal notranslate"><span class="pre">typing</span></code> instead.</p>
</div>
</div></blockquote>
<dl class="py class">
<dt class="sig sig-object py" id="typing.Pattern">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Pattern</span></span><a class="headerlink" href="#typing.Pattern" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="typing.Match">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Match</span></span><a class="headerlink" href="#typing.Match" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated aliases corresponding to the return types from
<a class="reference internal" href="re.html#re.compile" title="re.compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.compile()</span></code></a> and <a class="reference internal" href="re.html#re.match" title="re.match"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.match()</span></code></a>.</p>
<p>These types (and the corresponding functions) are generic over
<a class="reference internal" href="#typing.AnyStr" title="typing.AnyStr"><code class="xref py py-data docutils literal notranslate"><span class="pre">AnyStr</span></code></a>. <code class="docutils literal notranslate"><span class="pre">Pattern</span></code> can be specialised as <code class="docutils literal notranslate"><span class="pre">Pattern[str]</span></code> or
<code class="docutils literal notranslate"><span class="pre">Pattern[bytes]</span></code>; <code class="docutils literal notranslate"><span class="pre">Match</span></code> can be specialised as <code class="docutils literal notranslate"><span class="pre">Match[str]</span></code> or
<code class="docutils literal notranslate"><span class="pre">Match[bytes]</span></code>.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.8, will be removed in version 3.13: </span>The <code class="docutils literal notranslate"><span class="pre">typing.re</span></code> namespace is deprecated and will be removed.
These types should be directly imported from <code class="docutils literal notranslate"><span class="pre">typing</span></code> instead.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Classes <code class="docutils literal notranslate"><span class="pre">Pattern</span></code> and <code class="docutils literal notranslate"><span class="pre">Match</span></code> from <a class="reference internal" href="re.html#module-re" title="re: Regular expression operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code></a> now support <code class="docutils literal notranslate"><span class="pre">[]</span></code>.
See <span class="target" id="index-50"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Text">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Text</span></span><a class="headerlink" href="#typing.Text" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias for <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.</p>
<p><code class="docutils literal notranslate"><span class="pre">Text</span></code> is provided to supply a forward
compatible path for Python 2 code: in Python 2, <code class="docutils literal notranslate"><span class="pre">Text</span></code> is an alias for
<code class="docutils literal notranslate"><span class="pre">unicode</span></code>.</p>
<p>Use <code class="docutils literal notranslate"><span class="pre">Text</span></code> to indicate that a value must contain a unicode string in
a manner that is compatible with both Python 2 and Python 3:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">add_unicode_checkmark</span><span class="p">(</span><span class="n">text</span><span class="p">:</span> <span class="n">Text</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Text</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">text</span> <span class="o">+</span> <span class="sa">u</span><span class="s1">&#39; </span><span class="se">\u2713</span><span class="s1">&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>Python 2 is no longer supported, and most type checkers also no longer
support type checking Python 2 code. Removal of the alias is not
currently planned, but users are encouraged to use
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> instead of <code class="docutils literal notranslate"><span class="pre">Text</span></code>.</p>
</div>
</dd></dl>

</section>
<section id="aliases-to-container-abcs-in-collections-abc">
<span id="corresponding-to-collections-in-collections-abc"></span><span id="abstract-base-classes"></span><h4>Aliases to container ABCs in <a class="reference internal" href="collections.abc.html#module-collections.abc" title="collections.abc: Abstract base classes for containers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a><a class="headerlink" href="#aliases-to-container-abcs-in-collections-abc" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="typing.AbstractSet">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">AbstractSet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Collection[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.AbstractSet" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Set" title="collections.abc.Set"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Set</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Set" title="collections.abc.Set"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Set</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-51"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.ByteString">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ByteString</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Sequence[int]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.ByteString" title="Link to this definition">¶</a></dt>
<dd><p>This type represents the types <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>, <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>,
and <a class="reference internal" href="stdtypes.html#memoryview" title="memoryview"><code class="xref py py-class docutils literal notranslate"><span class="pre">memoryview</span></code></a> of byte sequences.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.9, will be removed in version 3.14: </span>Prefer <a class="reference internal" href="collections.abc.html#collections.abc.Buffer" title="collections.abc.Buffer"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Buffer</span></code></a>, or a union like <code class="docutils literal notranslate"><span class="pre">bytes</span> <span class="pre">|</span> <span class="pre">bytearray</span> <span class="pre">|</span> <span class="pre">memoryview</span></code>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Collection">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Collection</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Sized,</span> <span class="pre">Iterable[T_co],</span> <span class="pre">Container[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Collection" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Collection" title="collections.abc.Collection"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Collection</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Collection" title="collections.abc.Collection"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Collection</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-52"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Container">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Container</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Container" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Container" title="collections.abc.Container"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Container</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Container" title="collections.abc.Container"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Container</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-53"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.ItemsView">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ItemsView</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">MappingView,</span> <span class="pre">AbstractSet[tuple[KT_co,</span> <span class="pre">VT_co]]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.ItemsView" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.ItemsView" title="collections.abc.ItemsView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.ItemsView</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.ItemsView" title="collections.abc.ItemsView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.ItemsView</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-54"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.KeysView">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">KeysView</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">MappingView,</span> <span class="pre">AbstractSet[KT_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.KeysView" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.KeysView" title="collections.abc.KeysView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.KeysView</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.KeysView" title="collections.abc.KeysView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.KeysView</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-55"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Mapping">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Mapping</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Collection[KT],</span> <span class="pre">Generic[KT,</span> <span class="pre">VT_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Mapping" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Mapping" title="collections.abc.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Mapping</span></code></a>.</p>
<p>This type can be used as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">get_position_in_index</span><span class="p">(</span><span class="n">word_list</span><span class="p">:</span> <span class="n">Mapping</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">],</span> <span class="n">word</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">word_list</span><span class="p">[</span><span class="n">word</span><span class="p">]</span>
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Mapping" title="collections.abc.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Mapping</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-56"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.MappingView">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">MappingView</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Sized</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.MappingView" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.MappingView" title="collections.abc.MappingView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MappingView</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.MappingView" title="collections.abc.MappingView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MappingView</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-57"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.MutableMapping">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">MutableMapping</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Mapping[KT,</span> <span class="pre">VT]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.MutableMapping" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.MutableMapping" title="collections.abc.MutableMapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MutableMapping</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.MutableMapping" title="collections.abc.MutableMapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MutableMapping</span></code></a>
now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-58"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.MutableSequence">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">MutableSequence</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Sequence[T]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.MutableSequence" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.MutableSequence" title="collections.abc.MutableSequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MutableSequence</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.MutableSequence" title="collections.abc.MutableSequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MutableSequence</span></code></a>
now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-59"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.MutableSet">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">MutableSet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">AbstractSet[T]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.MutableSet" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.MutableSet" title="collections.abc.MutableSet"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MutableSet</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.MutableSet" title="collections.abc.MutableSet"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.MutableSet</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-60"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Sequence">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Sequence</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Reversible[T_co],</span> <span class="pre">Collection[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Sequence" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Sequence" title="collections.abc.Sequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Sequence</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Sequence" title="collections.abc.Sequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Sequence</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-61"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.ValuesView">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ValuesView</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">MappingView,</span> <span class="pre">Collection[_VT_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.ValuesView" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.ValuesView" title="collections.abc.ValuesView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.ValuesView</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.ValuesView" title="collections.abc.ValuesView"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.ValuesView</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-62"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

</section>
<section id="aliases-to-asynchronous-abcs-in-collections-abc">
<span id="asynchronous-programming"></span><h4>Aliases to asynchronous ABCs in <a class="reference internal" href="collections.abc.html#module-collections.abc" title="collections.abc: Abstract base classes for containers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a><a class="headerlink" href="#aliases-to-asynchronous-abcs-in-collections-abc" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="typing.Coroutine">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Coroutine</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Awaitable[ReturnType],</span> <span class="pre">Generic[YieldType,</span> <span class="pre">SendType,</span> <span class="pre">ReturnType]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Coroutine" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Coroutine" title="collections.abc.Coroutine"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Coroutine</span></code></a>.</p>
<p>The variance and order of type variables
correspond to those of <a class="reference internal" href="#typing.Generator" title="typing.Generator"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Coroutine</span>
<span class="n">c</span><span class="p">:</span> <span class="n">Coroutine</span><span class="p">[</span><span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span>  <span class="c1"># Some coroutine defined elsewhere</span>
<span class="n">x</span> <span class="o">=</span> <span class="n">c</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="s1">&#39;hi&#39;</span><span class="p">)</span>                   <span class="c1"># Inferred type of &#39;x&#39; is list[str]</span>
<span class="k">async</span> <span class="k">def</span> <span class="nf">bar</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="n">y</span> <span class="o">=</span> <span class="k">await</span> <span class="n">c</span>                    <span class="c1"># Inferred type of &#39;y&#39; is int</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Coroutine" title="collections.abc.Coroutine"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Coroutine</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-63"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.AsyncGenerator">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">AsyncGenerator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">AsyncIterator[YieldType],</span> <span class="pre">Generic[YieldType,</span> <span class="pre">SendType]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.AsyncGenerator" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.AsyncGenerator" title="collections.abc.AsyncGenerator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.AsyncGenerator</span></code></a>.</p>
<p>An async generator can be annotated by the generic type
<code class="docutils literal notranslate"><span class="pre">AsyncGenerator[YieldType,</span> <span class="pre">SendType]</span></code>. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">async</span> <span class="k">def</span> <span class="nf">echo_round</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">AsyncGenerator</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]:</span>
    <span class="n">sent</span> <span class="o">=</span> <span class="k">yield</span> <span class="mi">0</span>
    <span class="k">while</span> <span class="n">sent</span> <span class="o">&gt;=</span> <span class="mf">0.0</span><span class="p">:</span>
        <span class="n">rounded</span> <span class="o">=</span> <span class="k">await</span> <span class="nb">round</span><span class="p">(</span><span class="n">sent</span><span class="p">)</span>
        <span class="n">sent</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">rounded</span>
</pre></div>
</div>
<p>Unlike normal generators, async generators cannot return a value, so there
is no <code class="docutils literal notranslate"><span class="pre">ReturnType</span></code> type parameter. As with <a class="reference internal" href="#typing.Generator" title="typing.Generator"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>, the
<code class="docutils literal notranslate"><span class="pre">SendType</span></code> behaves contravariantly.</p>
<p>If your generator will only yield values, set the <code class="docutils literal notranslate"><span class="pre">SendType</span></code> to
<code class="docutils literal notranslate"><span class="pre">None</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">async</span> <span class="k">def</span> <span class="nf">infinite_stream</span><span class="p">(</span><span class="n">start</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AsyncGenerator</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">start</span>
        <span class="n">start</span> <span class="o">=</span> <span class="k">await</span> <span class="n">increment</span><span class="p">(</span><span class="n">start</span><span class="p">)</span>
</pre></div>
</div>
<p>Alternatively, annotate your generator as having a return type of
either <code class="docutils literal notranslate"><span class="pre">AsyncIterable[YieldType]</span></code> or <code class="docutils literal notranslate"><span class="pre">AsyncIterator[YieldType]</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">async</span> <span class="k">def</span> <span class="nf">infinite_stream</span><span class="p">(</span><span class="n">start</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AsyncIterator</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">start</span>
        <span class="n">start</span> <span class="o">=</span> <span class="k">await</span> <span class="n">increment</span><span class="p">(</span><span class="n">start</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.1.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.AsyncGenerator" title="collections.abc.AsyncGenerator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.AsyncGenerator</span></code></a>
now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-64"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.AsyncIterable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">AsyncIterable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.AsyncIterable" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.AsyncIterable" title="collections.abc.AsyncIterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.AsyncIterable</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.AsyncIterable" title="collections.abc.AsyncIterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.AsyncIterable</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-65"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.AsyncIterator">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">AsyncIterator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">AsyncIterable[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.AsyncIterator" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.AsyncIterator" title="collections.abc.AsyncIterator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.AsyncIterator</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.AsyncIterator" title="collections.abc.AsyncIterator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.AsyncIterator</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-66"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Awaitable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Awaitable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Awaitable" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Awaitable" title="collections.abc.Awaitable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Awaitable</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Awaitable" title="collections.abc.Awaitable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Awaitable</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-67"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

</section>
<section id="aliases-to-other-abcs-in-collections-abc">
<span id="corresponding-to-other-types-in-collections-abc"></span><h4>Aliases to other ABCs in <a class="reference internal" href="collections.abc.html#module-collections.abc" title="collections.abc: Abstract base classes for containers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a><a class="headerlink" href="#aliases-to-other-abcs-in-collections-abc" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="typing.Iterable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Iterable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Iterable" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Iterable" title="collections.abc.Iterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Iterable</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Iterable" title="collections.abc.Iterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Iterable</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-68"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Iterator">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Iterator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Iterable[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Iterator" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Iterator" title="collections.abc.Iterator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Iterator</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Iterator" title="collections.abc.Iterator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Iterator</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-69"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="typing.Callable">
<span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Callable</span></span><a class="headerlink" href="#typing.Callable" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Callable</span></code></a>.</p>
<p>See <a class="reference internal" href="#annotating-callables"><span class="std std-ref">Annotating callable objects</span></a> for details on how to use
<a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Callable</span></code></a> and <code class="docutils literal notranslate"><span class="pre">typing.Callable</span></code> in type annotations.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Callable</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-70"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">Callable</span></code> now supports <a class="reference internal" href="#typing.ParamSpec" title="typing.ParamSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ParamSpec</span></code></a> and <a class="reference internal" href="#typing.Concatenate" title="typing.Concatenate"><code class="xref py py-data docutils literal notranslate"><span class="pre">Concatenate</span></code></a>.
See <span class="target" id="index-71"></span><a class="pep reference external" href="https://peps.python.org/pep-0612/"><strong>PEP 612</strong></a> for more details.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Generator">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Generator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Iterator[YieldType],</span> <span class="pre">Generic[YieldType,</span> <span class="pre">SendType,</span> <span class="pre">ReturnType]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Generator" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Generator" title="collections.abc.Generator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Generator</span></code></a>.</p>
<p>A generator can be annotated by the generic type
<code class="docutils literal notranslate"><span class="pre">Generator[YieldType,</span> <span class="pre">SendType,</span> <span class="pre">ReturnType]</span></code>. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">echo_round</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Generator</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
    <span class="n">sent</span> <span class="o">=</span> <span class="k">yield</span> <span class="mi">0</span>
    <span class="k">while</span> <span class="n">sent</span> <span class="o">&gt;=</span> <span class="mi">0</span><span class="p">:</span>
        <span class="n">sent</span> <span class="o">=</span> <span class="k">yield</span> <span class="nb">round</span><span class="p">(</span><span class="n">sent</span><span class="p">)</span>
    <span class="k">return</span> <span class="s1">&#39;Done&#39;</span>
</pre></div>
</div>
<p>Note that unlike many other generics in the typing module, the <code class="docutils literal notranslate"><span class="pre">SendType</span></code>
of <a class="reference internal" href="#typing.Generator" title="typing.Generator"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a> behaves contravariantly, not covariantly or
invariantly.</p>
<p>If your generator will only yield values, set the <code class="docutils literal notranslate"><span class="pre">SendType</span></code> and
<code class="docutils literal notranslate"><span class="pre">ReturnType</span></code> to <code class="docutils literal notranslate"><span class="pre">None</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">infinite_stream</span><span class="p">(</span><span class="n">start</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Generator</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">start</span>
        <span class="n">start</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>
</div>
<p>Alternatively, annotate your generator as having a return type of
either <code class="docutils literal notranslate"><span class="pre">Iterable[YieldType]</span></code> or <code class="docutils literal notranslate"><span class="pre">Iterator[YieldType]</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">infinite_stream</span><span class="p">(</span><span class="n">start</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Iterator</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">start</span>
        <span class="n">start</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Generator" title="collections.abc.Generator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Generator</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-72"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Hashable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Hashable</span></span><a class="headerlink" href="#typing.Hashable" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Hashable" title="collections.abc.Hashable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Hashable</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12: </span>Use <a class="reference internal" href="collections.abc.html#collections.abc.Hashable" title="collections.abc.Hashable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Hashable</span></code></a> directly instead.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Reversible">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Reversible</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Iterable[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.Reversible" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Reversible" title="collections.abc.Reversible"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Reversible</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="collections.abc.html#collections.abc.Reversible" title="collections.abc.Reversible"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Reversible</span></code></a> now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-73"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.Sized">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">Sized</span></span><a class="headerlink" href="#typing.Sized" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="collections.abc.html#collections.abc.Sized" title="collections.abc.Sized"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Sized</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12: </span>Use <a class="reference internal" href="collections.abc.html#collections.abc.Sized" title="collections.abc.Sized"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Sized</span></code></a> directly instead.</p>
</div>
</dd></dl>

</section>
<section id="aliases-to-contextlib-abcs">
<span id="context-manager-types"></span><h4>Aliases to <a class="reference internal" href="contextlib.html#module-contextlib" title="contextlib: Utilities for with-statement contexts."><code class="xref py py-mod docutils literal notranslate"><span class="pre">contextlib</span></code></a> ABCs<a class="headerlink" href="#aliases-to-contextlib-abcs" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="typing.ContextManager">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">ContextManager</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.ContextManager" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="contextlib.html#contextlib.AbstractContextManager" title="contextlib.AbstractContextManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextlib.AbstractContextManager</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="contextlib.html#contextlib.AbstractContextManager" title="contextlib.AbstractContextManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextlib.AbstractContextManager</span></code></a>
now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-74"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="typing.AsyncContextManager">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">typing.</span></span><span class="sig-name descname"><span class="pre">AsyncContextManager</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">Generic[T_co]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#typing.AsyncContextManager" title="Link to this definition">¶</a></dt>
<dd><p>Deprecated alias to <a class="reference internal" href="contextlib.html#contextlib.AbstractAsyncContextManager" title="contextlib.AbstractAsyncContextManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextlib.AbstractAsyncContextManager</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span><a class="reference internal" href="contextlib.html#contextlib.AbstractAsyncContextManager" title="contextlib.AbstractAsyncContextManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextlib.AbstractAsyncContextManager</span></code></a>
now supports subscripting (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).
See <span class="target" id="index-75"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> and <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Type</span></a>.</p>
</div>
</dd></dl>

</section>
</section>
</section>
<section id="deprecation-timeline-of-major-features">
<h2>Deprecation Timeline of Major Features<a class="headerlink" href="#deprecation-timeline-of-major-features" title="Link to this heading">¶</a></h2>
<p>Certain features in <code class="docutils literal notranslate"><span class="pre">typing</span></code> are deprecated and may be removed in a future
version of Python. The following table summarizes major deprecations for your
convenience. This is subject to change, and not all deprecations are listed.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Feature</p></th>
<th class="head"><p>Deprecated in</p></th>
<th class="head"><p>Projected removal</p></th>
<th class="head"><p>PEP/issue</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">typing.io</span></code> and <code class="docutils literal notranslate"><span class="pre">typing.re</span></code> submodules</p></td>
<td><p>3.8</p></td>
<td><p>3.13</p></td>
<td><p><a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=38291">bpo-38291</a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">typing</span></code> versions of standard collections</p></td>
<td><p>3.9</p></td>
<td><p>Undecided (see <a class="reference internal" href="#deprecated-aliases"><span class="std std-ref">Deprecated aliases</span></a> for more information)</p></td>
<td><p><span class="target" id="index-76"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#typing.ByteString" title="typing.ByteString"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.ByteString</span></code></a></p></td>
<td><p>3.9</p></td>
<td><p>3.14</p></td>
<td><p><a class="reference external" href="https://github.com/python/cpython/issues/91896">gh-91896</a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#typing.Text" title="typing.Text"><code class="xref py py-data docutils literal notranslate"><span class="pre">typing.Text</span></code></a></p></td>
<td><p>3.11</p></td>
<td><p>Undecided</p></td>
<td><p><a class="reference external" href="https://github.com/python/cpython/issues/92332">gh-92332</a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#typing.Hashable" title="typing.Hashable"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Hashable</span></code></a> and <a class="reference internal" href="#typing.Sized" title="typing.Sized"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Sized</span></code></a></p></td>
<td><p>3.12</p></td>
<td><p>Undecided</p></td>
<td><p><a class="reference external" href="https://github.com/python/cpython/issues/94309">gh-94309</a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#typing.TypeAlias" title="typing.TypeAlias"><code class="xref py py-data docutils literal notranslate"><span class="pre">typing.TypeAlias</span></code></a></p></td>
<td><p>3.12</p></td>
<td><p>Undecided</p></td>
<td><p><span class="target" id="index-77"></span><a class="pep reference external" href="https://peps.python.org/pep-0695/"><strong>PEP 695</strong></a></p></td>
</tr>
</tbody>
</table>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code> — Support for type hints</a><ul>
<li><a class="reference internal" href="#specification-for-the-python-type-system">Specification for the Python Type System</a></li>
<li><a class="reference internal" href="#type-aliases">Type aliases</a></li>
<li><a class="reference internal" href="#newtype">NewType</a></li>
<li><a class="reference internal" href="#annotating-callable-objects">Annotating callable objects</a></li>
<li><a class="reference internal" href="#generics">Generics</a></li>
<li><a class="reference internal" href="#annotating-tuples">Annotating tuples</a></li>
<li><a class="reference internal" href="#the-type-of-class-objects">The type of class objects</a></li>
<li><a class="reference internal" href="#user-defined-generic-types">User-defined generic types</a></li>
<li><a class="reference internal" href="#the-any-type">The <code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code> type</a></li>
<li><a class="reference internal" href="#nominal-vs-structural-subtyping">Nominal vs structural subtyping</a></li>
<li><a class="reference internal" href="#module-contents">Module contents</a><ul>
<li><a class="reference internal" href="#special-typing-primitives">Special typing primitives</a><ul>
<li><a class="reference internal" href="#special-types">Special types</a></li>
<li><a class="reference internal" href="#special-forms">Special forms</a></li>
<li><a class="reference internal" href="#building-generic-types-and-type-aliases">Building generic types and type aliases</a></li>
<li><a class="reference internal" href="#other-special-directives">Other special directives</a></li>
</ul>
</li>
<li><a class="reference internal" href="#protocols">Protocols</a></li>
<li><a class="reference internal" href="#abcs-for-working-with-io">ABCs for working with IO</a></li>
<li><a class="reference internal" href="#functions-and-decorators">Functions and decorators</a></li>
<li><a class="reference internal" href="#introspection-helpers">Introspection helpers</a></li>
<li><a class="reference internal" href="#constant">Constant</a></li>
<li><a class="reference internal" href="#deprecated-aliases">Deprecated aliases</a><ul>
<li><a class="reference internal" href="#aliases-to-built-in-types">Aliases to built-in types</a></li>
<li><a class="reference internal" href="#aliases-to-types-in-collections">Aliases to types in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-other-concrete-types">Aliases to other concrete types</a></li>
<li><a class="reference internal" href="#aliases-to-container-abcs-in-collections-abc">Aliases to container ABCs in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-asynchronous-abcs-in-collections-abc">Aliases to asynchronous ABCs in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-other-abcs-in-collections-abc">Aliases to other ABCs in <code class="xref py py-mod docutils literal notranslate"><span class="pre">collections.abc</span></code></a></li>
<li><a class="reference internal" href="#aliases-to-contextlib-abcs">Aliases to <code class="xref py py-mod docutils literal notranslate"><span class="pre">contextlib</span></code> ABCs</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#deprecation-timeline-of-major-features">Deprecation Timeline of Major Features</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="development.html"
                          title="previous chapter">Development Tools</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pydoc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> — Documentation generator and online help system</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/typing.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pydoc.html" title="pydoc — Documentation generator and online help system"
             >next</a> |</li>
        <li class="right" >
          <a href="development.html" title="Development Tools"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" >Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code> — Support for type hints</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>