<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.DebugWidget">
        <Transition visible="props.state.showWidget" name="'o-fade'" leaveDuration="200" t-slot-scope="transition">
            <div class="debug-widget bg-100 p-2 shadow-lg" t-att-class="transition.className" t-ref="root" t-att-style="style">
                <header class="drag-handle">
                    <h2>Debug Window</h2>
                </header>
                <div class="toggle position-absolute top-0 end-0 p-2" t-on-click="() => debug.toggleWidget()" title="Dismiss"
                    role="img" aria-label="Dismiss"><i class="fa fa-fw fa-times" /></div>
                <div class="content overflow-hidden">

                    <div class="debug-electronic-scale">
                        <h5 class="category">Electronic Scale</h5>
                        <input t-model="state.weightInput" type="text" class="weight form-control"></input>
                        <ul class="d-flex flex-column align-items-start mb-3">
                            <li class="set_weight btn btn-link" t-on-click="setWeight">Set Weight</li>
                            <li class="reset_weight btn btn-link" t-on-click="resetWeight">Reset</li>
                        </ul>
                    </div>

                    <div class="debug-barcode-scanner">
                        <h5 class="category">Barcode Scanner</h5>
                        <ul class="d-flex flex-column align-items-start mb-3">
                            <li>
                                <input t-model="state.barcodeInput" type="text" class="ean form-control" t-att-disabled="!barcodeReader" t-attf-placeholder="{{ !barcodeReader ? 'No nomenclature' : '' }}"/>
                            </li>
                            <li class="button btn btn-link barcode" t-on-click="barcodeScan">Scan</li>
                            <li class="button btn btn-link custom_ean" t-on-click="barcodeScanEAN">Scan EAN-13</li>
                        </ul>
                    </div>

                    <div class="debug-orders">
                        <h5 class="category">Orders</h5>
                        <ul class="mb-3">
                            <li class="button btn btn-link" t-on-click="deleteOrders">
                                Delete Paid Orders
                            </li>
                            <li class="button btn btn-link" t-on-click="deleteUnpaidOrders">
                                Delete Unpaid Orders
                            </li>
                            <li t-if="!state.isPaidOrdersReady" class="button btn btn-link"
                                t-on-click="preparePaidOrders">
                                Export Paid Orders
                            </li>
                            <a t-else="" t-att-download="paidOrdersFilename" t-att-href="paidOrdersURL"
                            t-on-click="() => { state.isPaidOrdersReady = !state.isPaidOrdersReady }">
                                <li class="button btn btn-link">
                                    Download Paid Orders
                                </li>
                            </a>
                            <li t-if="!state.isUnpaidOrdersReady" class="button btn btn-link"
                                t-on-click="prepareUnpaidOrders">
                                Export Unpaid Orders
                            </li>
                            <a t-else="" t-att-download="unpaidOrdersFilename"
                            t-att-href="unpaidOrdersURL"
                            t-on-click="() => { state.isUnpaidOrdersReady = !state.isUnpaidOrdersReady }">
                                <li class="button btn btn-link">
                                    Download Unpaid Orders
                                </li>
                            </a>
                            <li class="button btn btn-link import_orders" style="position:relative">
                                Import Orders
                                <input t-on-change="importOrders" type="file"
                                    style="opacity:0;position:absolute;top:0;left:0;right:0;bottom:0;margin:0;cursor:pointer" />
                            </li>
                        </ul>
                    </div>

                    <div class="debug-hardware-status d-flex flex-column">
                        <h5 class="category">Hardware Status</h5>
                        <ul class="mb-3">
                            <li class="status weighing">Weighing</li>
                            <li class="button btn btn-link display_refresh" t-on-click="refreshDisplay">
                                Refresh Display
                            </li>
                        </ul>
                    </div>

                    <div class="debug-hardware-events">
                        <h5 class="category">Hardware Events</h5>
                        <ul class="mb-3">
                            <li class="event" t-ref="open_cashbox">Open Cashbox</li>
                            <li class="event" t-ref="print_receipt">Print Receipt</li>
                            <li class="event" t-ref="scale_read">Read Weighing Scale</li>
                        </ul>
                    </div>

                    <div class="debug-others">
                        <h5 class="category">Others</h5>
                        <ul class="mb-3">
                            <li class="event">
                                <span>Buffer: </span>
                                <t t-esc="bufferRepr" />
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </Transition>
    </t>

</templates>
