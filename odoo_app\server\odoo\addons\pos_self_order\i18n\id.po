# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_self_order
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_self_order
#: model:ir.actions.report,print_report_name:pos_self_order.report_self_order_qr_codes_page
msgid "\"QR codes\""
msgstr "\"Kode QR\""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid ""
"%s is not available anymore, it has thus been removed from your order. "
"Please review your order and validate it again."
msgstr ""
"%s tidak tersedia lagi, dan oleh karenanya telah dihapus dari pesanan Anda. "
"Silakan tinjau order Anda dan validasi lagi."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "/ Tracker number:"
msgstr "/ nomor Tracker:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"<br/>\n"
"                    URL:"
msgstr ""
"<br/>\n"
"                    URL:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"<span>Please note that the kiosk only works with Adyen &amp; Stripe "
"terminals</span>"
msgstr ""
"<span>Mohon catat bahwa kiosk hanya bekerja dengan terminal Adyen &amp; "
"Stripe</span>"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
msgid ""
"Access rights of this user will be used when visiting self order website "
"when no session is open."
msgstr ""
"Hak akses user ini akan digunakan saat mengunjungi website pesan sendiri "
"saat tidak ada sesi yang terbuka."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__active
msgid "Active"
msgstr "Aktif"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add Languages"
msgstr "Tambahkan Bahasa"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add an image to brand your header."
msgstr "Tambahkan gambar untuk memberikan brand ke header Anda."

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Add images"
msgstr "Tambahkan gambar-gambar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Add to Cart"
msgstr "Tambahkan ke Keranjang"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Add to cart"
msgstr "Tambahkan ke keranjang"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr ""
"Sesuaikan tingkat pajak berdasarkan apakah pelanggan makan di tempat atau "
"takeout."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "All the items will be removed from the cart."
msgstr "Semua barang akan dihapus dari keranjang."

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Alternative Fiscal Position"
msgstr "Posisi Fiskal Alternatif"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "An error has occurred"
msgstr "Terjadi error"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Any items already sent will not be canceled"
msgstr "Barang apapun yang sudah dikirim tidak dapat dibatalkan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Are you sure you want to cancel this order?"
msgstr "Apakah Anda yakin ingin membatalkan pesanan ini?"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
#, python-format
msgid "Available"
msgstr "Tersedia"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Available Languages"
msgstr "Bahasa-Bahasa Tersedia"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Available in Self"
msgstr "Tersedia di Sendiri"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__self_order_available
msgid "Available in Self Order"
msgstr "Tersedia di Pesan Sendiri"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Available interface languages"
msgstr "Bahasa antar muka yang tersedia"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Back"
msgstr "Kembali"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Cancel"
msgstr "Batal"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Cancel Order"
msgstr "Batalkan Pesanan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.js:0
#, python-format
msgid "Cancel order"
msgstr "Batalkan pesanan"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Choose the kiosk mode"
msgstr "Pilih mode kiosk"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Choose the self ordering mode"
msgstr "Pilih mode order sendiri"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Choose when the customer will pay"
msgstr "Pilih kapan pelanggan akan membayar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#, python-format
msgid "Choose your"
msgstr "Pilih "

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Choose your eating location"
msgstr "Pilih lokasi makan Anda"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
#, python-format
msgid "Close"
msgstr "Tutup"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Close Session"
msgstr "Tutup Sesi"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Combo"
msgstr "Combo"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "Baris Combo"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "Induk Combo"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_id
msgid "Combo line reference"
msgstr "Referensi baris combo"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Confirm"
msgstr "Konfirmasi"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Connection lost, please try again later"
msgstr "Koneksi hilang, silakan coba lagi nanti"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Could you please confirm your table number?"
msgstr "Tolong konfirmasi nomor meja Anda"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.js:0
#, python-format
msgid "Current"
msgstr "Saat Ini"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "Custom Links"
msgstr "Link Kustom"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_self_order_custom_link
msgid ""
"Custom links that the restaurant can configure to be displayed on the self "
"order screen"
msgstr ""
"Link kustom yang restoran dapat konfigurasi agar ditampilkan pada layar self"
" order"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Customize Header"
msgstr "Kustomisasi Header"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__danger
msgid "Danger"
msgstr "Bahaya"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__dark
msgid "Dark"
msgstr "Gelap"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default"
msgstr "Default"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default Language"
msgstr "Default Language"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default User"
msgstr "User Default"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default language for the kiosk mode"
msgstr "Bahasa default untuk mode kiosk"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__nothing
msgid "Disable"
msgstr "Nonaktifkan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#: code:addons/pos_self_order/static/src/app/components/language_popup/language_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Discard"
msgstr "Buang"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Download QR Codes"
msgstr "Unduh Kode QR"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Each Order"
msgstr "Setiap Pesanan"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Each table in your floor plan is assigned a unique QR code based on your configuration. For security reasons,\n"
"                    both the point of sale and table names are encrypted in the generated URL, as shown in the example below:."
msgstr ""
"Setiap meja di denah lantai ditetapkan kode QR unik berdasarkan konfigurasi Anda. Untuk alasan keamanan,\n"
"                    baik POS dan nama meja dienkripsi di URL yang dibuat, seperti yang ditunjukkan di halaman di bawah:."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Eat In"
msgstr "Makan Di Tempat"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Eat in / Take out"
msgstr "Makan Di Tempat / Take out"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Edit"
msgstr "Edit"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/attribute_selection/attribute_selection.xml:0
#, python-format
msgid "Enter your custom value"
msgstr "Masukkan value kustom"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Feel free to use and print this QR code as many times as needed according to"
" your requirements."
msgstr ""
"Silakan gunakan dan cetak kode QR ini sebanyak yang Anda butuhkan sesuai "
"kebutuhan Anda."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Fiscal Positions"
msgstr "Fiscal Position"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Follow instructions on the terminal"
msgstr "Ikuti instruksi pada terminal"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "From your Kiosk, open this URL:"
msgstr "Dari Kiosk Anda, buka URL ini:"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Generic"
msgstr "Umum"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Get a tracker and enter its number here"
msgstr "Dapatkan pelacak dan masukkan nomornya di sini"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid ""
"Hey, looks like you forgot to create products or add them to pos_config. "
"Please add them before using the Self Order"
msgstr ""
"Hey, sepertinya Anda lupa membuat produk atau menambahkannya ke pos_config. "
"Silakan tambahkan sebelum menggunakan Pesan Sendiri"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Home buttons"
msgstr "Tombol home"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Hope you enjoyed your meal!"
msgstr "Semoga Anda menikmati makanan Anda!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to customize"
msgstr "Cara mengustomisasi"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to use"
msgstr "Cara menggunakan"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__id
msgid "ID"
msgstr "ID"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,help:pos_self_order.field_product_template__self_order_available
msgid "If this product is available in the Self Order screens"
msgstr "Bila produk ini tersedia di layar Pesan Sendiri"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"If you need customized QR codes, start by scanning the relevant QR code to acquire the URL. Then, make\n"
"                use of a QR code generator like https://www.qrcode-monkey.com or https://www.qr-code-generator.com"
msgstr ""
"Bila Anda ingin kode QR kustom, mulai dengan scan kode QR yang relevan untuk mendapatkan URL. Lalu, gunakan\n"
"                pembuat kode QR seperti https://www.qrcode-monkey.com or https://www.qr-code-generator.com"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Image to display on the self order screen"
msgstr "Gambar untuk ditampilkan pada layar self order"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid ""
"In Self-Order mode, you must have at least one table to generate QR codes"
msgstr ""
"Di mode Pesan-Sendiri, Anda harus memiliki setidaknya satu meja untuk "
"membuat kode QR"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__inactive
msgid "Inactive"
msgstr "Tidak Aktif"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__info
msgid "Info"
msgstr "Informasi"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Information about your product for Self Order and Kiosk"
msgstr "Informasi mengenai produk Anda untuk Pesan Sendiri dan Kiosk"

#. module: pos_self_order
#: model:ir.actions.act_window,name:pos_self_order.action_pos_self_order_search_view
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__kiosk
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_search_view
msgid "Kiosk"
msgstr "Kiosk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__name
msgid "Label"
msgstr "Label"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Language"
msgstr "Bahasa"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Languages available for the kiosk mode"
msgstr "Bahasa yang tersedia untuk mode kiosk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Let your customers order using their mobile or a kiosk."
msgstr "Biarkan pelanggan Anda memesan menggunakan mobile atau kiosk mereka."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__light
msgid "Light"
msgstr "Cahaya"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Loading..."
msgstr "Memuat..."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online or order with the QR codes on your tables"
msgstr ""
"Mudahkan pelanggan untuk menavigasi menu online\n"
"                Anda atau pesan dengan kode QR pada meja Anda"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online with the QR codes on your tables"
msgstr ""
"Mudahkan pelanggan untuk menavigasi menu online\n"
"                Anda dengan kode QR pada meja Anda"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Meal"
msgstr "Makanan"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Mobile Menu"
msgstr "Menu Mobile"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Mobile self-order & Kiosk"
msgstr "Pesan-sendiri & Kiosk Mobile"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Order"
msgstr "Pesanan Saya"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Orders"
msgstr "Orderku"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "BARU"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "CATATAN"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Name of the image to display on the self order screen"
msgstr "Nama gambar untuk ditampilkan pada layar self order"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Next"
msgstr "Next"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "No"
msgstr "Tidak"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "No order found"
msgstr "Tidak ada pesanan yang ditemukan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "No products found"
msgstr "Tidak ada produk yang ditemukan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Not available"
msgstr "Tidak tersedia"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Not available in Self"
msgstr "Tidak tersedia di Sendiri"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "Only pay after each is available with kiosk mode."
msgstr "Hanya bayar setelah semuanya tersedia di mode kiosk."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Open Kiosk"
msgstr "Buka Kiosk"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "Open in New Tab"
msgstr "Buka di Tab Baru"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Order"
msgstr "Order"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: model:pos_self_order.custom_link,name:pos_self_order.default_custom_link
#, python-format
msgid "Order Now"
msgstr "Pesan Sekarang"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Order number:"
msgstr "Nomor pesanan:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Order to pick-up at the counter"
msgstr "Pesan untuk diambil pada kasir"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Orders not found on server"
msgstr "Order tidak ditemukan pada server"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Out of stock"
msgstr "Out of stock"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Pay"
msgstr "Bayar"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Pay After:"
msgstr "Bayar Setelah:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Pay after"
msgstr "Bayar setelah"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Pay at the cashier"
msgstr "Bayar di kasir"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Personalize your splash screen by adding one or multiple images to create a "
"slideshow"
msgstr ""
"Personalisasikan splash screen Anda dengan menambahkan satu atau lebih "
"gambar untuk membuat slideshow"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Pickup At Counter"
msgstr "Ambil Di Kasir"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__counter
msgid "Pickup zone"
msgstr "Zona Pickup"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Please wait until the price is loaded"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_session.py:0
#, python-format
msgid "PoS Order by Session"
msgstr "Pesanan POS berdasarkan Sesi"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Konfigurasi Point of Sale"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Baris Order POS"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order
msgid "Point of Sale Orders"
msgstr "Order Point of Sale"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Metode Pembayaran Point of Sale POS"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesi Point of Sale"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Point of sale:"
msgstr "POS:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid "Points of Sale"
msgstr "POS"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__link_html
msgid "Preview"
msgstr "Pratinjau"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Preview Web interface"
msgstr "Pratinjau antarmuka Website"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__primary
msgid "Primary"
msgstr "Primer"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Print QR Codes"
msgstr "Cetak Kode QR"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_template
msgid "Product"
msgstr "Produk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__description_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__description_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Product Description for Self Order"
msgstr "Keterangan Produk untuk Pesan Sendiri"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "Informasi Produk"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_product
msgid "Product Variant"
msgstr "Varian Produk"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Product image"
msgstr "Gambar Produk"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product is not available"
msgstr "Produk tidak tersedia"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product not found"
msgstr "Produk tidak ditemukan"

#. module: pos_self_order
#: model:ir.actions.report,name:pos_self_order.report_self_order_qr_codes_page
msgid "QR Codes"
msgstr "QR Code"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "QR codes can only be generated in mobile or consultation mode."
msgstr "Kode QR hanya dapat dibuat di mode mobile atau konsultasi."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__consultation
msgid "QR menu"
msgstr "Menu QR"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "QR menu & Kiosk activation"
msgstr "Aktivasi menu QR & Kiosk"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__mobile
msgid "QR menu + Ordering"
msgstr "Menu QR + Pemesanan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Quantity select"
msgstr "Pilih kuantitas"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Reset QR Codes"
msgstr "Reset Kode QR"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_restaurant_table
msgid "Restaurant Table"
msgstr "Meja Restoran"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Retry"
msgstr "Ulangi"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "Search product"
msgstr "Cari produ"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__secondary
msgid "Secondary"
msgstr "Sekunder"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__access_token
#: model:ir.model.fields,field_description:pos_self_order.field_restaurant_table__identifier
msgid "Security Token"
msgstr "Token Keamanan"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid ""
"Select for which points of sale you want to display this link. Leave empty "
"to display it for all points of sale. You have to select among the points of"
" sale that have the 'QR Code Menu' feature enabled."
msgstr ""
"Pilih untuk POS mana Anda ingin menampilkan link ini. Biarkan kosong untuk "
"menampilkannya di semua POS. Anda harus memilih dari antara POS yang "
"mengaktifkan fitur 'Menu Kode QR'."

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Self Kiosk"
msgstr "Kiosk Sendiri"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
msgid "Self Order Kiosk Image Brand"
msgstr "Brand Gambar Kiosk Pesan Sendiri"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Self Order Kiosk Image Brand Name"
msgstr "Nama Brand Gambar Kiosk Pesan Sendiri"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Self Order:"
msgstr "Pesan Sendiri:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Self Ordering"
msgstr "Memesan Sendiri"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Self Ordering Enabled"
msgstr "Self Order Diaktifkan"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Self Ordering Mode"
msgstr "Mode Memesan Sendiri"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_url
msgid "Self Ordering Url"
msgstr "Url Memesan Sendiri"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Self-ordering availability:"
msgstr "Ketersediaan memesan-sendiri:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Service"
msgstr "Layanan"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Service at"
msgstr "Layanan ke"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Service at Table"
msgstr "Layanan ke Meja"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Service at table"
msgstr "Layanan ke meja"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Splash screens"
msgstr "Splash screen"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Start Kiosk"
msgstr "Mulai Kiosk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__status
msgid "Status"
msgstr "Status"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__style
msgid "Style"
msgstr "Gaya"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__success
msgid "Success"
msgstr "Sukses"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__table
msgid "Table"
msgstr "Tabel"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__table_stand_number
msgid "Table Stand Number"
msgstr "Nomor Stand Meja"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Table Tracker:"
msgstr "Pelacak Meja:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Table detective time!"
msgstr "Table detective time!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Table:"
msgstr "Meja:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__take_away
msgid "Take Away"
msgstr "Take Away"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Take Out"
msgstr "Take Out"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_takeaway
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_takeaway
msgid "Takeaway"
msgstr "Takeaway"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Tax:"
msgstr "Pajak:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Taxes:"
msgstr "Pajak:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Thanks a lot!"
msgstr "Terima kasih sekali!"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "The Self-Order default user must be a POS user"
msgstr "User default Pesan-Sendiri harus merupakan user POS"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "The user must be a POS user"
msgstr "User harus merupakan user POS"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Ini berguna untuk restoran dengan layanan di tempat atau bungkus pulang "
"dengan pajak yang spesifik."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__url
msgid "URL"
msgstr "URL"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "URL:"
msgstr "URL:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Uncategorised"
msgstr "Tidak dalam kategori"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__warning
msgid "Warning"
msgstr "Peringatan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "We're currently closed"
msgstr "Saat ini kami tutup"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid "We're currently closed."
msgstr "Kamisaat ini sudah tutup."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "We're preparing your order!"
msgstr "Kami sedang menyiapkan pesanan Anda!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Yes"
msgstr "Ya"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.js:0
#, python-format
msgid "You cannot edit a posted orderline !"
msgstr "Anda tidak dapat mengedit barisorder yang diposting !"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "You're not authorized to perform this action"
msgstr "Anda tidak diizinkan untuk melakukan action ini"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Your Order"
msgstr "Pesanan Anda"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Your Selection"
msgstr "Pilihan Anda"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been canceled"
msgstr "Pesanan Anda telah dibatalkan"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been paid"
msgstr "Pesanan Anda telah dibayar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Your order number"
msgstr "Nomor pesanan Anda"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order status has been changed"
msgstr "Status pesanan Anda telah diubah"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "empty = all points of sale"
msgstr "kosong = semua POS"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "https://odoo.com"
msgstr "https://odoo.com"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "odoo"
msgstr "odoo"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "options"
msgstr "opsi"
