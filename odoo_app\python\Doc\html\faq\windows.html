<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Python on Windows FAQ" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/faq/windows.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Contents: Python on Windows FAQ- How do I run a Python program under Windows?, How do I make Python scripts executable?, Why does Python sometimes take so long to start?, How do I make an executabl..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Contents: Python on Windows FAQ- How do I run a Python program under Windows?, How do I make Python scripts executable?, Why does Python sometimes take so long to start?, How do I make an executabl..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Python on Windows FAQ &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Graphic User Interface FAQ" href="gui.html" />
    <link rel="prev" title="Extending/Embedding FAQ" href="extending.html" />
    <link rel="canonical" href="https://docs.python.org/3/faq/windows.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Python on Windows FAQ</a><ul>
<li><a class="reference internal" href="#how-do-i-run-a-python-program-under-windows">How do I run a Python program under Windows?</a></li>
<li><a class="reference internal" href="#how-do-i-make-python-scripts-executable">How do I make Python scripts executable?</a></li>
<li><a class="reference internal" href="#why-does-python-sometimes-take-so-long-to-start">Why does Python sometimes take so long to start?</a></li>
<li><a class="reference internal" href="#how-do-i-make-an-executable-from-a-python-script">How do I make an executable from a Python script?</a></li>
<li><a class="reference internal" href="#is-a-pyd-file-the-same-as-a-dll">Is a <code class="docutils literal notranslate"><span class="pre">*.pyd</span></code> file the same as a DLL?</a></li>
<li><a class="reference internal" href="#how-can-i-embed-python-into-a-windows-application">How can I embed Python into a Windows application?</a></li>
<li><a class="reference internal" href="#how-do-i-keep-editors-from-inserting-tabs-into-my-python-source">How do I keep editors from inserting tabs into my Python source?</a></li>
<li><a class="reference internal" href="#how-do-i-check-for-a-keypress-without-blocking">How do I check for a keypress without blocking?</a></li>
<li><a class="reference internal" href="#how-do-i-solve-the-missing-api-ms-win-crt-runtime-l1-1-0-dll-error">How do I solve the missing api-ms-win-crt-runtime-l1-1-0.dll error?</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="extending.html"
                          title="previous chapter">Extending/Embedding FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gui.html"
                          title="next chapter">Graphic User Interface FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/windows.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gui.html" title="Graphic User Interface FAQ"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="extending.html" title="Extending/Embedding FAQ"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python on Windows FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="python-on-windows-faq">
<span id="windows-faq"></span><h1><a class="toc-backref" href="#id1" role="doc-backlink">Python on Windows FAQ</a><a class="headerlink" href="#python-on-windows-faq" title="Link to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#python-on-windows-faq" id="id1">Python on Windows FAQ</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-run-a-python-program-under-windows" id="id2">How do I run a Python program under Windows?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-make-python-scripts-executable" id="id3">How do I make Python scripts executable?</a></p></li>
<li><p><a class="reference internal" href="#why-does-python-sometimes-take-so-long-to-start" id="id4">Why does Python sometimes take so long to start?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-make-an-executable-from-a-python-script" id="id5">How do I make an executable from a Python script?</a></p></li>
<li><p><a class="reference internal" href="#is-a-pyd-file-the-same-as-a-dll" id="id6">Is a <code class="docutils literal notranslate"><span class="pre">*.pyd</span></code> file the same as a DLL?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-embed-python-into-a-windows-application" id="id7">How can I embed Python into a Windows application?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-keep-editors-from-inserting-tabs-into-my-python-source" id="id8">How do I keep editors from inserting tabs into my Python source?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-check-for-a-keypress-without-blocking" id="id9">How do I check for a keypress without blocking?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-solve-the-missing-api-ms-win-crt-runtime-l1-1-0-dll-error" id="id10">How do I solve the missing api-ms-win-crt-runtime-l1-1-0.dll error?</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="how-do-i-run-a-python-program-under-windows">
<span id="faq-run-program-under-windows"></span><h2><a class="toc-backref" href="#id2" role="doc-backlink">How do I run a Python program under Windows?</a><a class="headerlink" href="#how-do-i-run-a-python-program-under-windows" title="Link to this heading">¶</a></h2>
<p>This is not necessarily a straightforward question. If you are already familiar
with running programs from the Windows command line then everything will seem
obvious; otherwise, you might need a little more guidance.</p>
<p>Unless you use some sort of integrated development environment, you will end up
<em>typing</em> Windows commands into what is referred to as a
“Command prompt window”.  Usually you can create such a window from your
search bar by searching for <code class="docutils literal notranslate"><span class="pre">cmd</span></code>.  You should be able to recognize
when you have started such a window because you will see a Windows “command
prompt”, which usually looks like this:</p>
<div class="highlight-doscon notranslate"><div class="highlight"><pre><span></span><span class="gp">C:\&gt;</span>
</pre></div>
</div>
<p>The letter may be different, and there might be other things after it, so you
might just as easily see something like:</p>
<div class="highlight-doscon notranslate"><div class="highlight"><pre><span></span><span class="gp">D:\YourName\Projects\Python&gt;</span>
</pre></div>
</div>
<p>depending on how your computer has been set up and what else you have recently
done with it.  Once you have started such a window, you are well on the way to
running Python programs.</p>
<p>You need to realize that your Python scripts have to be processed by another
program called the Python <em>interpreter</em>.  The interpreter reads your script,
compiles it into bytecodes, and then executes the bytecodes to run your
program. So, how do you arrange for the interpreter to handle your Python?</p>
<p>First, you need to make sure that your command window recognises the word
“py” as an instruction to start the interpreter.  If you have opened a
command window, you should try entering the command <code class="docutils literal notranslate"><span class="pre">py</span></code> and hitting
return:</p>
<div class="highlight-doscon notranslate"><div class="highlight"><pre><span></span><span class="gp">C:\Users\<USER>\Users\YourName&gt;
</pre></div>
</div>
<p>So now you’ll ask the <code class="docutils literal notranslate"><span class="pre">py</span></code> command to give your script to Python by
typing <code class="docutils literal notranslate"><span class="pre">py</span></code> followed by your script path:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>C:\Users\<USER>\hello.py
hello
</pre></div>
</div>
</section>
<section id="how-do-i-make-python-scripts-executable">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">How do I make Python scripts executable?</a><a class="headerlink" href="#how-do-i-make-python-scripts-executable" title="Link to this heading">¶</a></h2>
<p>On Windows, the standard Python installer already associates the .py
extension with a file type (Python.File) and gives that file type an open
command that runs the interpreter (<code class="docutils literal notranslate"><span class="pre">D:\Program</span> <span class="pre">Files\Python\python.exe</span> <span class="pre">&quot;%1&quot;</span>
<span class="pre">%*</span></code>).  This is enough to make scripts executable from the command prompt as
‘foo.py’.  If you’d rather be able to execute the script by simple typing ‘foo’
with no extension you need to add .py to the PATHEXT environment variable.</p>
</section>
<section id="why-does-python-sometimes-take-so-long-to-start">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Why does Python sometimes take so long to start?</a><a class="headerlink" href="#why-does-python-sometimes-take-so-long-to-start" title="Link to this heading">¶</a></h2>
<p>Usually Python starts very quickly on Windows, but occasionally there are bug
reports that Python suddenly begins to take a long time to start up.  This is
made even more puzzling because Python will work fine on other Windows systems
which appear to be configured identically.</p>
<p>The problem may be caused by a misconfiguration of virus checking software on
the problem machine.  Some virus scanners have been known to introduce startup
overhead of two orders of magnitude when the scanner is configured to monitor
all reads from the filesystem.  Try checking the configuration of virus scanning
software on your systems to ensure that they are indeed configured identically.
McAfee, when configured to scan all file system read activity, is a particular
offender.</p>
</section>
<section id="how-do-i-make-an-executable-from-a-python-script">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">How do I make an executable from a Python script?</a><a class="headerlink" href="#how-do-i-make-an-executable-from-a-python-script" title="Link to this heading">¶</a></h2>
<p>See <a class="reference internal" href="programming.html#faq-create-standalone-binary"><span class="std std-ref">How can I create a stand-alone binary from a Python script?</span></a> for a list of tools that can be used to
make executables.</p>
</section>
<section id="is-a-pyd-file-the-same-as-a-dll">
<h2><a class="toc-backref" href="#id6" role="doc-backlink">Is a <code class="docutils literal notranslate"><span class="pre">*.pyd</span></code> file the same as a DLL?</a><a class="headerlink" href="#is-a-pyd-file-the-same-as-a-dll" title="Link to this heading">¶</a></h2>
<p>Yes, .pyd files are dll’s, but there are a few differences.  If you have a DLL
named <code class="docutils literal notranslate"><span class="pre">foo.pyd</span></code>, then it must have a function <code class="docutils literal notranslate"><span class="pre">PyInit_foo()</span></code>.  You can then
write Python “import foo”, and Python will search for foo.pyd (as well as
foo.py, foo.pyc) and if it finds it, will attempt to call <code class="docutils literal notranslate"><span class="pre">PyInit_foo()</span></code> to
initialize it.  You do not link your .exe with foo.lib, as that would cause
Windows to require the DLL to be present.</p>
<p>Note that the search path for foo.pyd is PYTHONPATH, not the same as the path
that Windows uses to search for foo.dll.  Also, foo.pyd need not be present to
run your program, whereas if you linked your program with a dll, the dll is
required.  Of course, foo.pyd is required if you want to say <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">foo</span></code>.  In
a DLL, linkage is declared in the source code with <code class="docutils literal notranslate"><span class="pre">__declspec(dllexport)</span></code>.
In a .pyd, linkage is defined in a list of available functions.</p>
</section>
<section id="how-can-i-embed-python-into-a-windows-application">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">How can I embed Python into a Windows application?</a><a class="headerlink" href="#how-can-i-embed-python-into-a-windows-application" title="Link to this heading">¶</a></h2>
<p>Embedding the Python interpreter in a Windows app can be summarized as follows:</p>
<ol class="arabic">
<li><p>Do <strong>not</strong> build Python into your .exe file directly.  On Windows, Python must
be a DLL to handle importing modules that are themselves DLL’s.  (This is the
first key undocumented fact.)  Instead, link to <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.dll</span></code>; it is
typically installed in <code class="docutils literal notranslate"><span class="pre">C:\Windows\System</span></code>.  <em>NN</em> is the Python version, a
number such as “33” for Python 3.3.</p>
<p>You can link to Python in two different ways.  Load-time linking means
linking against <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.lib</span></code>, while run-time linking means linking
against <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.dll</span></code>.  (General note: <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.lib</span></code> is the
so-called “import lib” corresponding to <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.dll</span></code>.  It merely
defines symbols for the linker.)</p>
<p>Run-time linking greatly simplifies link options; everything happens at run
time.  Your code must load <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.dll</span></code> using the Windows
<code class="docutils literal notranslate"><span class="pre">LoadLibraryEx()</span></code> routine.  The code must also use access routines and data
in <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">NN</span></em><span class="pre">.dll</span></code> (that is, Python’s C API’s) using pointers obtained
by the Windows <code class="docutils literal notranslate"><span class="pre">GetProcAddress()</span></code> routine.  Macros can make using these
pointers transparent to any C code that calls routines in Python’s C API.</p>
</li>
<li><p>If you use SWIG, it is easy to create a Python “extension module” that will
make the app’s data and methods available to Python.  SWIG will handle just
about all the grungy details for you.  The result is C code that you link
<em>into</em> your .exe file (!)  You do <strong>not</strong> have to create a DLL file, and this
also simplifies linking.</p></li>
<li><p>SWIG will create an init function (a C function) whose name depends on the
name of the extension module.  For example, if the name of the module is leo,
the init function will be called initleo().  If you use SWIG shadow classes,
as you should, the init function will be called initleoc().  This initializes
a mostly hidden helper class used by the shadow class.</p>
<p>The reason you can link the C code in step 2 into your .exe file is that
calling the initialization function is equivalent to importing the module
into Python! (This is the second key undocumented fact.)</p>
</li>
<li><p>In short, you can use the following code to initialize the Python interpreter
with your extension module.</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;Python.h&gt;</span>
<span class="p">...</span>
<span class="n">Py_Initialize</span><span class="p">();</span><span class="w">  </span><span class="c1">// Initialize Python.</span>
<span class="n">initmyAppc</span><span class="p">();</span><span class="w">  </span><span class="c1">// Initialize (import) the helper class.</span>
<span class="n">PyRun_SimpleString</span><span class="p">(</span><span class="s">&quot;import myApp&quot;</span><span class="p">);</span><span class="w">  </span><span class="c1">// Import the shadow class.</span>
</pre></div>
</div>
</li>
<li><p>There are two problems with Python’s C API which will become apparent if you
use a compiler other than MSVC, the compiler used to build pythonNN.dll.</p>
<p>Problem 1: The so-called “Very High Level” functions that take <code class="docutils literal notranslate"><span class="pre">FILE</span> <span class="pre">*</span></code>
arguments will not work in a multi-compiler environment because each
compiler’s notion of a <code class="docutils literal notranslate"><span class="pre">struct</span> <span class="pre">FILE</span></code> will be different.  From an implementation
standpoint these are very low level functions.</p>
<p>Problem 2: SWIG generates the following code when generating wrappers to void
functions:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">Py_INCREF</span><span class="p">(</span><span class="n">Py_None</span><span class="p">);</span>
<span class="n">_resultobj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_None</span><span class="p">;</span>
<span class="k">return</span><span class="w"> </span><span class="n">_resultobj</span><span class="p">;</span>
</pre></div>
</div>
<p>Alas, Py_None is a macro that expands to a reference to a complex data
structure called _Py_NoneStruct inside pythonNN.dll.  Again, this code will
fail in a mult-compiler environment.  Replace such code by:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">return</span><span class="w"> </span><span class="n">Py_BuildValue</span><span class="p">(</span><span class="s">&quot;&quot;</span><span class="p">);</span>
</pre></div>
</div>
<p>It may be possible to use SWIG’s <code class="docutils literal notranslate"><span class="pre">%typemap</span></code> command to make the change
automatically, though I have not been able to get this to work (I’m a
complete SWIG newbie).</p>
</li>
<li><p>Using a Python shell script to put up a Python interpreter window from inside
your Windows app is not a good idea; the resulting window will be independent
of your app’s windowing system.  Rather, you (or the wxPythonWindow class)
should create a “native” interpreter window.  It is easy to connect that
window to the Python interpreter.  You can redirect Python’s i/o to _any_
object that supports read and write, so all you need is a Python object
(defined in your extension module) that contains read() and write() methods.</p></li>
</ol>
</section>
<section id="how-do-i-keep-editors-from-inserting-tabs-into-my-python-source">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">How do I keep editors from inserting tabs into my Python source?</a><a class="headerlink" href="#how-do-i-keep-editors-from-inserting-tabs-into-my-python-source" title="Link to this heading">¶</a></h2>
<p>The FAQ does not recommend using tabs, and the Python style guide, <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0008/"><strong>PEP 8</strong></a>,
recommends 4 spaces for distributed Python code; this is also the Emacs
python-mode default.</p>
<p>Under any editor, mixing tabs and spaces is a bad idea.  MSVC is no different in
this respect, and is easily configured to use spaces: Take <span class="menuselection">Tools
‣ Options ‣ Tabs</span>, and for file type “Default” set “Tab size” and “Indent
size” to 4, and select the “Insert spaces” radio button.</p>
<p>Python raises <a class="reference internal" href="../library/exceptions.html#IndentationError" title="IndentationError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndentationError</span></code></a> or <a class="reference internal" href="../library/exceptions.html#TabError" title="TabError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TabError</span></code></a> if mixed tabs
and spaces are causing problems in leading whitespace.
You may also run the <a class="reference internal" href="../library/tabnanny.html#module-tabnanny" title="tabnanny: Tool for detecting white space related problems in Python source files in a directory tree."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tabnanny</span></code></a> module to check a directory tree
in batch mode.</p>
</section>
<section id="how-do-i-check-for-a-keypress-without-blocking">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">How do I check for a keypress without blocking?</a><a class="headerlink" href="#how-do-i-check-for-a-keypress-without-blocking" title="Link to this heading">¶</a></h2>
<p>Use the <a class="reference internal" href="../library/msvcrt.html#module-msvcrt" title="msvcrt: Miscellaneous useful routines from the MS VC++ runtime. (Windows)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code></a> module.  This is a standard Windows-specific extension module.
It defines a function <code class="docutils literal notranslate"><span class="pre">kbhit()</span></code> which checks whether a keyboard hit is
present, and <code class="docutils literal notranslate"><span class="pre">getch()</span></code> which gets one character without echoing it.</p>
</section>
<section id="how-do-i-solve-the-missing-api-ms-win-crt-runtime-l1-1-0-dll-error">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">How do I solve the missing api-ms-win-crt-runtime-l1-1-0.dll error?</a><a class="headerlink" href="#how-do-i-solve-the-missing-api-ms-win-crt-runtime-l1-1-0-dll-error" title="Link to this heading">¶</a></h2>
<p>This can occur on Python 3.5 and later when using Windows 8.1 or earlier without all updates having been installed.
First ensure your operating system is supported and is up to date, and if that does not resolve the issue,
visit the <a class="reference external" href="https://support.microsoft.com/en-us/help/3118401/">Microsoft support page</a>
for guidance on manually installing the C Runtime update.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Python on Windows FAQ</a><ul>
<li><a class="reference internal" href="#how-do-i-run-a-python-program-under-windows">How do I run a Python program under Windows?</a></li>
<li><a class="reference internal" href="#how-do-i-make-python-scripts-executable">How do I make Python scripts executable?</a></li>
<li><a class="reference internal" href="#why-does-python-sometimes-take-so-long-to-start">Why does Python sometimes take so long to start?</a></li>
<li><a class="reference internal" href="#how-do-i-make-an-executable-from-a-python-script">How do I make an executable from a Python script?</a></li>
<li><a class="reference internal" href="#is-a-pyd-file-the-same-as-a-dll">Is a <code class="docutils literal notranslate"><span class="pre">*.pyd</span></code> file the same as a DLL?</a></li>
<li><a class="reference internal" href="#how-can-i-embed-python-into-a-windows-application">How can I embed Python into a Windows application?</a></li>
<li><a class="reference internal" href="#how-do-i-keep-editors-from-inserting-tabs-into-my-python-source">How do I keep editors from inserting tabs into my Python source?</a></li>
<li><a class="reference internal" href="#how-do-i-check-for-a-keypress-without-blocking">How do I check for a keypress without blocking?</a></li>
<li><a class="reference internal" href="#how-do-i-solve-the-missing-api-ms-win-crt-runtime-l1-1-0-dll-error">How do I solve the missing api-ms-win-crt-runtime-l1-1-0.dll error?</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="extending.html"
                          title="previous chapter">Extending/Embedding FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gui.html"
                          title="next chapter">Graphic User Interface FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/windows.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gui.html" title="Graphic User Interface FAQ"
             >next</a> |</li>
        <li class="right" >
          <a href="extending.html" title="Extending/Embedding FAQ"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python on Windows FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>