<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment_provider_ogone" model="payment.provider">
        <field name="name">Ogone</field>
        <field name="image_128"
               type="base64"
               file="payment_ogone/static/description/icon.png"/>
        <field name="module_id" ref="base.module_payment_ogone"/>
        <field name="payment_method_ids"
               eval="[(6, 0, [
                   ref('payment.payment_method_card'),
                   ref('payment.payment_method_bancontact'),
                   ref('payment.payment_method_belfius'),
                   ref('payment.payment_method_bizum'),
                   ref('payment.payment_method_klarna_paynow'),
                   ref('payment.payment_method_klarna_pay_over_time'),
                   ref('payment.payment_method_paypal'),
                   ref('payment.payment_method_sofort'),
                   ref('payment.payment_method_twint'),
                   ref('payment.payment_method_axis'),
                   ref('payment.payment_method_eps'),
                   ref('payment.payment_method_paypal'),
                   ref('payment.payment_method_sofort'),
                   ref('payment.payment_method_paylib'),
                   ref('payment.payment_method_p24'),
               ])]"/>
        <field name="code">ogone</field>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="allow_tokenization">True</field>
    </record>

</odoo>
