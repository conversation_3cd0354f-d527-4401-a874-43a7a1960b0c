<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="ssl — TLS/SSL wrapper for socket objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/ssl.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/ssl.py This module provides access to Transport Layer Security (often known as “Secure Sockets Layer”) encryption and peer authentication facilities for network sockets, both clien..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/ssl.py This module provides access to Transport Layer Security (often known as “Secure Sockets Layer”) encryption and peer authentication facilities for network sockets, both clien..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>ssl — TLS/SSL wrapper for socket objects &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="select — Waiting for I/O completion" href="select.html" />
    <link rel="prev" title="socket — Low-level networking interface" href="socket.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/ssl.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code> — TLS/SSL wrapper for socket objects</a><ul>
<li><a class="reference internal" href="#functions-constants-and-exceptions">Functions, Constants, and Exceptions</a><ul>
<li><a class="reference internal" href="#socket-creation">Socket creation</a></li>
<li><a class="reference internal" href="#context-creation">Context creation</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#random-generation">Random generation</a></li>
<li><a class="reference internal" href="#certificate-handling">Certificate handling</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
</ul>
</li>
<li><a class="reference internal" href="#ssl-sockets">SSL Sockets</a></li>
<li><a class="reference internal" href="#ssl-contexts">SSL Contexts</a></li>
<li><a class="reference internal" href="#certificates">Certificates</a><ul>
<li><a class="reference internal" href="#certificate-chains">Certificate chains</a></li>
<li><a class="reference internal" href="#ca-certificates">CA certificates</a></li>
<li><a class="reference internal" href="#combined-key-and-certificate">Combined key and certificate</a></li>
<li><a class="reference internal" href="#self-signed-certificates">Self-signed certificates</a></li>
</ul>
</li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#testing-for-ssl-support">Testing for SSL support</a></li>
<li><a class="reference internal" href="#client-side-operation">Client-side operation</a></li>
<li><a class="reference internal" href="#server-side-operation">Server-side operation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#notes-on-non-blocking-sockets">Notes on non-blocking sockets</a></li>
<li><a class="reference internal" href="#memory-bio-support">Memory BIO Support</a></li>
<li><a class="reference internal" href="#ssl-session">SSL session</a></li>
<li><a class="reference internal" href="#security-considerations">Security considerations</a><ul>
<li><a class="reference internal" href="#best-defaults">Best defaults</a></li>
<li><a class="reference internal" href="#manual-settings">Manual settings</a><ul>
<li><a class="reference internal" href="#verifying-certificates">Verifying certificates</a></li>
<li><a class="reference internal" href="#protocol-versions">Protocol versions</a></li>
<li><a class="reference internal" href="#cipher-selection">Cipher selection</a></li>
</ul>
</li>
<li><a class="reference internal" href="#multi-processing">Multi-processing</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tls-1-3">TLS 1.3</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="socket.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code> — Low-level networking interface</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="select.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code> — Waiting for I/O completion</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ssl.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="select.html" title="select — Waiting for I/O completion"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="socket.html" title="socket — Low-level networking interface"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" accesskey="U">Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code> — TLS/SSL wrapper for socket objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-ssl">
<span id="ssl-tls-ssl-wrapper-for-socket-objects"></span><h1><a class="reference internal" href="#module-ssl" title="ssl: TLS/SSL wrapper for socket objects"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code></a> — TLS/SSL wrapper for socket objects<a class="headerlink" href="#module-ssl" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/ssl.py">Lib/ssl.py</a></p>
<span id="index-0"></span><hr class="docutils" id="index-1" />
<p>This module provides access to Transport Layer Security (often known as “Secure
Sockets Layer”) encryption and peer authentication facilities for network
sockets, both client-side and server-side.  This module uses the OpenSSL
library. It is available on all modern Unix systems, Windows, macOS, and
probably additional platforms, as long as OpenSSL is installed on that platform.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Some behavior may be platform dependent, since calls are made to the
operating system socket APIs.  The installed version of OpenSSL may also
cause variations in behavior. For example, TLSv1.3 comes with OpenSSL version
1.1.1.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Don’t use this module without reading the <a class="reference internal" href="#ssl-security"><span class="std std-ref">Security considerations</span></a>.  Doing so
may lead to a false sense of security, as the default settings of the
ssl module are not necessarily appropriate for your application.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>This section documents the objects and functions in the <code class="docutils literal notranslate"><span class="pre">ssl</span></code> module; for more
general information about TLS, SSL, and certificates, the reader is referred to
the documents in the “See Also” section at the bottom.</p>
<p>This module provides a class, <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLSocket</span></code></a>, which is derived from the
<a class="reference internal" href="socket.html#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket.socket</span></code></a> type, and provides a socket-like wrapper that also
encrypts and decrypts the data going over the socket with SSL.  It supports
additional methods such as <code class="xref py py-meth docutils literal notranslate"><span class="pre">getpeercert()</span></code>, which retrieves the
certificate of the other side of the connection, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">cipher()</span></code>, which
retrieves the cipher being used for the secure connection.</p>
<p>For more sophisticated applications, the <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> class
helps manage settings and certificates, which can then be inherited
by SSL sockets created through the <a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a> method.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5.3: </span>Updated to support linking with OpenSSL 1.1.0</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>OpenSSL 0.9.8, 1.0.0 and 1.0.1 are deprecated and no longer supported.
In the future the ssl module will require at least OpenSSL 1.0.2 or
1.1.0.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0644/"><strong>PEP 644</strong></a> has been implemented. The ssl module requires OpenSSL 1.1.1
or newer.</p>
<p>Use of deprecated constants and functions result in deprecation warnings.</p>
</div>
<section id="functions-constants-and-exceptions">
<h2>Functions, Constants, and Exceptions<a class="headerlink" href="#functions-constants-and-exceptions" title="Link to this heading">¶</a></h2>
<section id="socket-creation">
<h3>Socket creation<a class="headerlink" href="#socket-creation" title="Link to this heading">¶</a></h3>
<p>Instances of <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> must be created using the
<a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a> method. The helper function
<a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a> returns a new context with secure default
settings.</p>
<p>Client socket example with default context and IPv4/IPv6 dual stack:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span>
<span class="kn">import</span> <span class="nn">ssl</span>

<span class="n">hostname</span> <span class="o">=</span> <span class="s1">&#39;www.python.org&#39;</span>
<span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">()</span>

<span class="k">with</span> <span class="n">socket</span><span class="o">.</span><span class="n">create_connection</span><span class="p">((</span><span class="n">hostname</span><span class="p">,</span> <span class="mi">443</span><span class="p">))</span> <span class="k">as</span> <span class="n">sock</span><span class="p">:</span>
    <span class="k">with</span> <span class="n">context</span><span class="o">.</span><span class="n">wrap_socket</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">server_hostname</span><span class="o">=</span><span class="n">hostname</span><span class="p">)</span> <span class="k">as</span> <span class="n">ssock</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">ssock</span><span class="o">.</span><span class="n">version</span><span class="p">())</span>
</pre></div>
</div>
<p>Client socket example with custom context and IPv4:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">hostname</span> <span class="o">=</span> <span class="s1">&#39;www.python.org&#39;</span>
<span class="c1"># PROTOCOL_TLS_CLIENT requires valid cert chain and hostname</span>
<span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLContext</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">PROTOCOL_TLS_CLIENT</span><span class="p">)</span>
<span class="n">context</span><span class="o">.</span><span class="n">load_verify_locations</span><span class="p">(</span><span class="s1">&#39;path/to/cabundle.pem&#39;</span><span class="p">)</span>

<span class="k">with</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="k">as</span> <span class="n">sock</span><span class="p">:</span>
    <span class="k">with</span> <span class="n">context</span><span class="o">.</span><span class="n">wrap_socket</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">server_hostname</span><span class="o">=</span><span class="n">hostname</span><span class="p">)</span> <span class="k">as</span> <span class="n">ssock</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">ssock</span><span class="o">.</span><span class="n">version</span><span class="p">())</span>
</pre></div>
</div>
<p>Server socket example listening on localhost IPv4:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLContext</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">PROTOCOL_TLS_SERVER</span><span class="p">)</span>
<span class="n">context</span><span class="o">.</span><span class="n">load_cert_chain</span><span class="p">(</span><span class="s1">&#39;/path/to/certchain.pem&#39;</span><span class="p">,</span> <span class="s1">&#39;/path/to/private.key&#39;</span><span class="p">)</span>

<span class="k">with</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="k">as</span> <span class="n">sock</span><span class="p">:</span>
    <span class="n">sock</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="s1">&#39;127.0.0.1&#39;</span><span class="p">,</span> <span class="mi">8443</span><span class="p">))</span>
    <span class="n">sock</span><span class="o">.</span><span class="n">listen</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
    <span class="k">with</span> <span class="n">context</span><span class="o">.</span><span class="n">wrap_socket</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">server_side</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="k">as</span> <span class="n">ssock</span><span class="p">:</span>
        <span class="n">conn</span><span class="p">,</span> <span class="n">addr</span> <span class="o">=</span> <span class="n">ssock</span><span class="o">.</span><span class="n">accept</span><span class="p">()</span>
        <span class="o">...</span>
</pre></div>
</div>
</section>
<section id="context-creation">
<h3>Context creation<a class="headerlink" href="#context-creation" title="Link to this heading">¶</a></h3>
<p>A convenience function helps create <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> objects for common
purposes.</p>
<dl class="py function">
<dt class="sig sig-object py" id="ssl.create_default_context">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">create_default_context</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">purpose</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Purpose.SERVER_AUTH</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cafile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capath</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cadata</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.create_default_context" title="Link to this definition">¶</a></dt>
<dd><p>Return a new <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> object with default settings for
the given <em>purpose</em>.  The settings are chosen by the <a class="reference internal" href="#module-ssl" title="ssl: TLS/SSL wrapper for socket objects"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code></a> module,
and usually represent a higher security level than when calling the
<a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> constructor directly.</p>
<p><em>cafile</em>, <em>capath</em>, <em>cadata</em> represent optional CA certificates to
trust for certificate verification, as in
<a class="reference internal" href="#ssl.SSLContext.load_verify_locations" title="ssl.SSLContext.load_verify_locations"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_verify_locations()</span></code></a>.  If all three are
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, this function can choose to trust the system’s default
CA certificates instead.</p>
<p>The settings are: <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> or
<a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a>, <a class="reference internal" href="#ssl.OP_NO_SSLv2" title="ssl.OP_NO_SSLv2"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv2</span></code></a>, and <a class="reference internal" href="#ssl.OP_NO_SSLv3" title="ssl.OP_NO_SSLv3"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv3</span></code></a>
with high encryption cipher suites without RC4 and
without unauthenticated cipher suites. Passing <a class="reference internal" href="#ssl.Purpose.SERVER_AUTH" title="ssl.Purpose.SERVER_AUTH"><code class="xref py py-const docutils literal notranslate"><span class="pre">SERVER_AUTH</span></code></a>
as <em>purpose</em> sets <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-data docutils literal notranslate"><span class="pre">verify_mode</span></code></a> to <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>
and either loads CA certificates (when at least one of <em>cafile</em>, <em>capath</em> or
<em>cadata</em> is given) or uses <a class="reference internal" href="#ssl.SSLContext.load_default_certs" title="ssl.SSLContext.load_default_certs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_default_certs()</span></code></a> to load
default CA certificates.</p>
<p>When <a class="reference internal" href="#ssl.SSLContext.keylog_filename" title="ssl.SSLContext.keylog_filename"><code class="xref py py-attr docutils literal notranslate"><span class="pre">keylog_filename</span></code></a> is supported and the environment
variable <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">SSLKEYLOGFILE</span></code> is set, <a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a>
enables key logging.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The protocol, options, cipher and other settings may change to more
restrictive values anytime without prior deprecation.  The values
represent a fair balance between compatibility and security.</p>
<p>If your application needs specific settings, you should create a
<a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> and apply the settings yourself.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you find that when certain older clients or servers attempt to connect
with a <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> created by this function that they get an error
stating “Protocol or cipher suite mismatch”, it may be that they only
support SSL3.0 which this function excludes using the
<a class="reference internal" href="#ssl.OP_NO_SSLv3" title="ssl.OP_NO_SSLv3"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv3</span></code></a>. SSL3.0 is widely considered to be <a class="reference external" href="https://en.wikipedia.org/wiki/POODLE">completely broken</a>. If you still wish to continue to
use this function but still allow SSL 3.0 connections you can re-enable
them using:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">ctx</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">(</span><span class="n">Purpose</span><span class="o">.</span><span class="n">CLIENT_AUTH</span><span class="p">)</span>
<span class="n">ctx</span><span class="o">.</span><span class="n">options</span> <span class="o">&amp;=</span> <span class="o">~</span><span class="n">ssl</span><span class="o">.</span><span class="n">OP_NO_SSLv3</span>
</pre></div>
</div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.4: </span>RC4 was dropped from the default cipher string.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>ChaCha20/Poly1305 was added to the default cipher string.</p>
<p>3DES was dropped from the default cipher string.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Support for key logging to <span class="target" id="index-4"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">SSLKEYLOGFILE</span></code> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The context now uses <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> or
<a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a> protocol instead of generic
<a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="exceptions">
<h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLError</span></span><a class="headerlink" href="#ssl.SSLError" title="Link to this definition">¶</a></dt>
<dd><p>Raised to signal an error from the underlying SSL implementation
(currently provided by the OpenSSL library).  This signifies some
problem in the higher-level encryption and authentication layer that’s
superimposed on the underlying network connection.  This error
is a subtype of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.  The error code and message of
<a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> instances are provided by the OpenSSL library.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> used to be a subtype of <a class="reference internal" href="socket.html#socket.error" title="socket.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">socket.error</span></code></a>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLError.library">
<span class="sig-name descname"><span class="pre">library</span></span><a class="headerlink" href="#ssl.SSLError.library" title="Link to this definition">¶</a></dt>
<dd><p>A string mnemonic designating the OpenSSL submodule in which the error
occurred, such as <code class="docutils literal notranslate"><span class="pre">SSL</span></code>, <code class="docutils literal notranslate"><span class="pre">PEM</span></code> or <code class="docutils literal notranslate"><span class="pre">X509</span></code>.  The range of possible
values depends on the OpenSSL version.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLError.reason">
<span class="sig-name descname"><span class="pre">reason</span></span><a class="headerlink" href="#ssl.SSLError.reason" title="Link to this definition">¶</a></dt>
<dd><p>A string mnemonic designating the reason this error occurred, for
example <code class="docutils literal notranslate"><span class="pre">CERTIFICATE_VERIFY_FAILED</span></code>.  The range of possible
values depends on the OpenSSL version.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLZeroReturnError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLZeroReturnError</span></span><a class="headerlink" href="#ssl.SSLZeroReturnError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> raised when trying to read or write and
the SSL connection has been closed cleanly.  Note that this doesn’t
mean that the underlying transport (read TCP) has been closed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLWantReadError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLWantReadError</span></span><a class="headerlink" href="#ssl.SSLWantReadError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> raised by a <a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking SSL socket</span></a> when trying to read or write data, but more data needs
to be received on the underlying TCP transport before the request can be
fulfilled.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLWantWriteError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLWantWriteError</span></span><a class="headerlink" href="#ssl.SSLWantWriteError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> raised by a <a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking SSL socket</span></a> when trying to read or write data, but more data needs
to be sent on the underlying TCP transport before the request can be
fulfilled.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLSyscallError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLSyscallError</span></span><a class="headerlink" href="#ssl.SSLSyscallError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> raised when a system error was encountered
while trying to fulfill an operation on a SSL socket.  Unfortunately,
there is no easy way to inspect the original errno number.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLEOFError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLEOFError</span></span><a class="headerlink" href="#ssl.SSLEOFError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> raised when the SSL connection has been
terminated abruptly.  Generally, you shouldn’t try to reuse the underlying
transport when this error is encountered.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.SSLCertVerificationError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLCertVerificationError</span></span><a class="headerlink" href="#ssl.SSLCertVerificationError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> raised when certificate validation has
failed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLCertVerificationError.verify_code">
<span class="sig-name descname"><span class="pre">verify_code</span></span><a class="headerlink" href="#ssl.SSLCertVerificationError.verify_code" title="Link to this definition">¶</a></dt>
<dd><p>A numeric error number that denotes the verification error.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLCertVerificationError.verify_message">
<span class="sig-name descname"><span class="pre">verify_message</span></span><a class="headerlink" href="#ssl.SSLCertVerificationError.verify_message" title="Link to this definition">¶</a></dt>
<dd><p>A human readable string of the verification error.</p>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ssl.CertificateError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">CertificateError</span></span><a class="headerlink" href="#ssl.CertificateError" title="Link to this definition">¶</a></dt>
<dd><p>An alias for <a class="reference internal" href="#ssl.SSLCertVerificationError" title="ssl.SSLCertVerificationError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLCertVerificationError</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The exception is now an alias for <a class="reference internal" href="#ssl.SSLCertVerificationError" title="ssl.SSLCertVerificationError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLCertVerificationError</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="random-generation">
<h3>Random generation<a class="headerlink" href="#random-generation" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="ssl.RAND_bytes">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">RAND_bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">num</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.RAND_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>num</em> cryptographically strong pseudo-random bytes. Raises an
<a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLError</span></code></a> if the PRNG has not been seeded with enough data or if the
operation is not supported by the current RAND method. <a class="reference internal" href="#ssl.RAND_status" title="ssl.RAND_status"><code class="xref py py-func docutils literal notranslate"><span class="pre">RAND_status()</span></code></a>
can be used to check the status of the PRNG and <a class="reference internal" href="#ssl.RAND_add" title="ssl.RAND_add"><code class="xref py py-func docutils literal notranslate"><span class="pre">RAND_add()</span></code></a> can be used
to seed the PRNG.</p>
<p>For almost all applications <a class="reference internal" href="os.html#os.urandom" title="os.urandom"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.urandom()</span></code></a> is preferable.</p>
<p>Read the Wikipedia article, <a class="reference external" href="https://en.wikipedia.org/wiki/Cryptographically_secure_pseudorandom_number_generator">Cryptographically secure pseudorandom number
generator (CSPRNG)</a>,
to get the requirements of a cryptographically strong generator.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.RAND_status">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">RAND_status</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.RAND_status" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the SSL pseudo-random number generator has been seeded
with ‘enough’ randomness, and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.  You can use
<code class="xref py py-func docutils literal notranslate"><span class="pre">ssl.RAND_egd()</span></code> and <a class="reference internal" href="#ssl.RAND_add" title="ssl.RAND_add"><code class="xref py py-func docutils literal notranslate"><span class="pre">ssl.RAND_add()</span></code></a> to increase the randomness of
the pseudo-random number generator.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.RAND_add">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">RAND_add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entropy</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.RAND_add" title="Link to this definition">¶</a></dt>
<dd><p>Mix the given <em>bytes</em> into the SSL pseudo-random number generator.  The
parameter <em>entropy</em> (a float) is a lower bound on the entropy contained in
string (so you can always use <code class="docutils literal notranslate"><span class="pre">0.0</span></code>).  See <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1750.html"><strong>RFC 1750</strong></a> for more
information on sources of entropy.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

</section>
<section id="certificate-handling">
<h3>Certificate handling<a class="headerlink" href="#certificate-handling" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="ssl.cert_time_to_seconds">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">cert_time_to_seconds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cert_time</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.cert_time_to_seconds" title="Link to this definition">¶</a></dt>
<dd><p>Return the time in seconds since the Epoch, given the <code class="docutils literal notranslate"><span class="pre">cert_time</span></code>
string representing the “notBefore” or “notAfter” date from a
certificate in <code class="docutils literal notranslate"><span class="pre">&quot;%b</span> <span class="pre">%d</span> <span class="pre">%H:%M:%S</span> <span class="pre">%Y</span> <span class="pre">%Z&quot;</span></code> strptime format (C
locale).</p>
<p>Here’s an example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">ssl</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">timestamp</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">cert_time_to_seconds</span><span class="p">(</span><span class="s2">&quot;Jan  5 09:34:43 2018 GMT&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">timestamp</span>  
<span class="go">1515144883</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcfromtimestamp</span><span class="p">(</span><span class="n">timestamp</span><span class="p">))</span>  
<span class="go">2018-01-05 09:34:43</span>
</pre></div>
</div>
<p>“notBefore” or “notAfter” dates must use GMT (<span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5280.html"><strong>RFC 5280</strong></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Interpret the input time as a time in UTC as specified by ‘GMT’
timezone in the input string. Local timezone was used
previously. Return an integer (no fractions of a second in the
input format)</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.get_server_certificate">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">get_server_certificate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">addr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ssl_version=PROTOCOL_TLS_CLIENT</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ca_certs=None</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.get_server_certificate" title="Link to this definition">¶</a></dt>
<dd><p>Given the address <code class="docutils literal notranslate"><span class="pre">addr</span></code> of an SSL-protected server, as a (<em>hostname</em>,
<em>port-number</em>) pair, fetches the server’s certificate, and returns it as a
PEM-encoded string.  If <code class="docutils literal notranslate"><span class="pre">ssl_version</span></code> is specified, uses that version of
the SSL protocol to attempt to connect to the server.  If <em>ca_certs</em> is
specified, it should be a file containing a list of root certificates, the
same format as used for the <em>cafile</em> parameter in
<a class="reference internal" href="#ssl.SSLContext.load_verify_locations" title="ssl.SSLContext.load_verify_locations"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_verify_locations()</span></code></a>.  The call will attempt to validate the
server certificate against that set of root certificates, and will fail
if the validation attempt fails.  A timeout can be specified with the
<code class="docutils literal notranslate"><span class="pre">timeout</span></code> parameter.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This function is now IPv6-compatible.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The default <em>ssl_version</em> is changed from <a class="reference internal" href="#ssl.PROTOCOL_SSLv3" title="ssl.PROTOCOL_SSLv3"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_SSLv3</span></code></a> to
<a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a> for maximum compatibility with modern servers.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>timeout</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.DER_cert_to_PEM_cert">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">DER_cert_to_PEM_cert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">DER_cert_bytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.DER_cert_to_PEM_cert" title="Link to this definition">¶</a></dt>
<dd><p>Given a certificate as a DER-encoded blob of bytes, returns a PEM-encoded
string version of the same certificate.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.PEM_cert_to_DER_cert">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PEM_cert_to_DER_cert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">PEM_cert_string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.PEM_cert_to_DER_cert" title="Link to this definition">¶</a></dt>
<dd><p>Given a certificate as an ASCII PEM string, returns a DER-encoded sequence of
bytes for that same certificate.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.get_default_verify_paths">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">get_default_verify_paths</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.get_default_verify_paths" title="Link to this definition">¶</a></dt>
<dd><p>Returns a named tuple with paths to OpenSSL’s default cafile and capath.
The paths are the same as used by
<a class="reference internal" href="#ssl.SSLContext.set_default_verify_paths" title="ssl.SSLContext.set_default_verify_paths"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_default_verify_paths()</span></code></a>. The return value is a
<a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> <code class="docutils literal notranslate"><span class="pre">DefaultVerifyPaths</span></code>:</p>
<ul class="simple">
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">cafile</span></code> - resolved path to cafile or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the file doesn’t exist,</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">capath</span></code> - resolved path to capath or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the directory doesn’t exist,</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">openssl_cafile_env</span></code> - OpenSSL’s environment key that points to a cafile,</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">openssl_cafile</span></code> - hard coded path to a cafile,</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">openssl_capath_env</span></code> - OpenSSL’s environment key that points to a capath,</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">openssl_capath</span></code> - hard coded path to a capath directory</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.enum_certificates">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">enum_certificates</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">store_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.enum_certificates" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve certificates from Windows’ system cert store. <em>store_name</em> may be
one of <code class="docutils literal notranslate"><span class="pre">CA</span></code>, <code class="docutils literal notranslate"><span class="pre">ROOT</span></code> or <code class="docutils literal notranslate"><span class="pre">MY</span></code>. Windows may provide additional cert
stores, too.</p>
<p>The function returns a list of (cert_bytes, encoding_type, trust) tuples.
The encoding_type specifies the encoding of cert_bytes. It is either
<code class="xref py py-const docutils literal notranslate"><span class="pre">x509_asn</span></code> for X.509 ASN.1 data or <code class="xref py py-const docutils literal notranslate"><span class="pre">pkcs_7_asn</span></code> for
PKCS#7 ASN.1 data. Trust specifies the purpose of the certificate as a set
of OIDS or exactly <code class="docutils literal notranslate"><span class="pre">True</span></code> if the certificate is trustworthy for all
purposes.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">enum_certificates</span><span class="p">(</span><span class="s2">&quot;CA&quot;</span><span class="p">)</span>
<span class="go">[(b&#39;data...&#39;, &#39;x509_asn&#39;, {&#39;*******.5.5.7.3.1&#39;, &#39;*******.5.5.7.3.2&#39;}),</span>
<span class="go"> (b&#39;data...&#39;, &#39;x509_asn&#39;, True)]</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ssl.enum_crls">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">enum_crls</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">store_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.enum_crls" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve CRLs from Windows’ system cert store. <em>store_name</em> may be
one of <code class="docutils literal notranslate"><span class="pre">CA</span></code>, <code class="docutils literal notranslate"><span class="pre">ROOT</span></code> or <code class="docutils literal notranslate"><span class="pre">MY</span></code>. Windows may provide additional cert
stores, too.</p>
<p>The function returns a list of (cert_bytes, encoding_type, trust) tuples.
The encoding_type specifies the encoding of cert_bytes. It is either
<code class="xref py py-const docutils literal notranslate"><span class="pre">x509_asn</span></code> for X.509 ASN.1 data or <code class="xref py py-const docutils literal notranslate"><span class="pre">pkcs_7_asn</span></code> for
PKCS#7 ASN.1 data.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

</section>
<section id="constants">
<h3>Constants<a class="headerlink" href="#constants" title="Link to this heading">¶</a></h3>
<blockquote>
<div><p>All constants are now <a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntEnum</span></code></a> or <a class="reference internal" href="enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntFlag</span></code></a> collections.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</div></blockquote>
<dl class="py data">
<dt class="sig sig-object py" id="ssl.CERT_NONE">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">CERT_NONE</span></span><a class="headerlink" href="#ssl.CERT_NONE" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_mode</span></code></a>.
Except for <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a>,
it is the default mode.  With client-side sockets, just about any
cert is accepted.  Validation errors, such as untrusted or expired cert,
are ignored and do not abort the TLS/SSL handshake.</p>
<p>In server mode, no certificate is requested from the client, so the client
does not send any for client cert authentication.</p>
<p>See the discussion of <a class="reference internal" href="#ssl-security"><span class="std std-ref">Security considerations</span></a> below.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.CERT_OPTIONAL">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">CERT_OPTIONAL</span></span><a class="headerlink" href="#ssl.CERT_OPTIONAL" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_mode</span></code></a>.
In client mode, <a class="reference internal" href="#ssl.CERT_OPTIONAL" title="ssl.CERT_OPTIONAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_OPTIONAL</span></code></a>
has the same meaning as <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>. It is recommended to
use <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a> for client-side sockets instead.</p>
<p>In server mode, a client certificate request is sent to the client.  The
client may either ignore the request or send a certificate in order
perform TLS client cert authentication.  If the client chooses to send
a certificate, it is verified.  Any verification error immediately aborts
the TLS handshake.</p>
<p>Use of this setting requires a valid set of CA certificates to
be passed to <a class="reference internal" href="#ssl.SSLContext.load_verify_locations" title="ssl.SSLContext.load_verify_locations"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_verify_locations()</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.CERT_REQUIRED">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">CERT_REQUIRED</span></span><a class="headerlink" href="#ssl.CERT_REQUIRED" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_mode</span></code></a>.
In this mode, certificates are
required from the other side of the socket connection; an <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLError</span></code></a>
will be raised if no certificate is provided, or if its validation fails.
This mode is <strong>not</strong> sufficient to verify a certificate in client mode as
it does not match hostnames.  <a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> must be
enabled as well to verify the authenticity of a cert.
<a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> uses <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a> and
enables <a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> by default.</p>
<p>With server socket, this mode provides mandatory TLS client cert
authentication.  A client certificate request is sent to the client and
the client must provide a valid and trusted certificate.</p>
<p>Use of this setting requires a valid set of CA certificates to
be passed to <a class="reference internal" href="#ssl.SSLContext.load_verify_locations" title="ssl.SSLContext.load_verify_locations"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_verify_locations()</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ssl.VerifyMode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VerifyMode</span></span><a class="headerlink" href="#ssl.VerifyMode" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntEnum</span></code></a> collection of CERT_* constants.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_DEFAULT">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_DEFAULT</span></span><a class="headerlink" href="#ssl.VERIFY_DEFAULT" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a>. In this mode, certificate
revocation lists (CRLs) are not checked. By default OpenSSL does neither
require nor verify CRLs.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_CRL_CHECK_LEAF">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_CRL_CHECK_LEAF</span></span><a class="headerlink" href="#ssl.VERIFY_CRL_CHECK_LEAF" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a>. In this mode, only the
peer cert is checked but none of the intermediate CA certificates. The mode
requires a valid CRL that is signed by the peer cert’s issuer (its direct
ancestor CA). If no proper CRL has been loaded with
<a class="reference internal" href="#ssl.SSLContext.load_verify_locations" title="ssl.SSLContext.load_verify_locations"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.load_verify_locations</span></code></a>, validation will fail.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_CRL_CHECK_CHAIN">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_CRL_CHECK_CHAIN</span></span><a class="headerlink" href="#ssl.VERIFY_CRL_CHECK_CHAIN" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a>. In this mode, CRLs of
all certificates in the peer cert chain are checked.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_X509_STRICT">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_X509_STRICT</span></span><a class="headerlink" href="#ssl.VERIFY_X509_STRICT" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a> to disable workarounds
for broken X.509 certificates.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_ALLOW_PROXY_CERTS">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_ALLOW_PROXY_CERTS</span></span><a class="headerlink" href="#ssl.VERIFY_ALLOW_PROXY_CERTS" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a> to enables proxy
certificate verification.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_X509_TRUSTED_FIRST">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_X509_TRUSTED_FIRST</span></span><a class="headerlink" href="#ssl.VERIFY_X509_TRUSTED_FIRST" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a>. It instructs OpenSSL to
prefer trusted certificates when building the trust chain to validate a
certificate. This flag is enabled by default.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.VERIFY_X509_PARTIAL_CHAIN">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VERIFY_X509_PARTIAL_CHAIN</span></span><a class="headerlink" href="#ssl.VERIFY_X509_PARTIAL_CHAIN" title="Link to this definition">¶</a></dt>
<dd><p>Possible value for <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a>. It instructs OpenSSL to
accept intermediate CAs in the trust store to be treated as trust-anchors,
in the same way as the self-signed root CA certificates. This makes it
possible to trust certificates issued by an intermediate CA without having
to trust its ancestor root CA.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ssl.VerifyFlags">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">VerifyFlags</span></span><a class="headerlink" href="#ssl.VerifyFlags" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntFlag</span></code></a> collection of VERIFY_* constants.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_TLS">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_TLS</span></span><a class="headerlink" href="#ssl.PROTOCOL_TLS" title="Link to this definition">¶</a></dt>
<dd><p>Selects the highest protocol version that both the client and server support.
Despite the name, this option can select both “SSL” and “TLS” protocols.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.10: </span>TLS clients and servers require different default settings for secure
communication. The generic TLS protocol constant is deprecated in
favor of <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> and <a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_TLS_CLIENT">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_TLS_CLIENT</span></span><a class="headerlink" href="#ssl.PROTOCOL_TLS_CLIENT" title="Link to this definition">¶</a></dt>
<dd><p>Auto-negotiate the highest protocol version that both the client and
server support, and configure the context client-side connections. The
protocol enables <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> by default.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_TLS_SERVER">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_TLS_SERVER</span></span><a class="headerlink" href="#ssl.PROTOCOL_TLS_SERVER" title="Link to this definition">¶</a></dt>
<dd><p>Auto-negotiate the highest protocol version that both the client and
server support, and configure the context server-side connections.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_SSLv23">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_SSLv23</span></span><a class="headerlink" href="#ssl.PROTOCOL_SSLv23" title="Link to this definition">¶</a></dt>
<dd><p>Alias for <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>Use <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_SSLv3">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_SSLv3</span></span><a class="headerlink" href="#ssl.PROTOCOL_SSLv3" title="Link to this definition">¶</a></dt>
<dd><p>Selects SSL version 3 as the channel encryption protocol.</p>
<p>This protocol is not available if OpenSSL is compiled with the
<code class="docutils literal notranslate"><span class="pre">no-ssl3</span></code> option.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>SSL version 3 is insecure.  Its use is highly discouraged.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>OpenSSL has deprecated all version specific protocols. Use the default
protocol <a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a> or <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a>
with <a class="reference internal" href="#ssl.SSLContext.minimum_version" title="ssl.SSLContext.minimum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.minimum_version</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.maximum_version</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_TLSv1">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_TLSv1</span></span><a class="headerlink" href="#ssl.PROTOCOL_TLSv1" title="Link to this definition">¶</a></dt>
<dd><p>Selects TLS version 1.0 as the channel encryption protocol.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>OpenSSL has deprecated all version specific protocols.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_TLSv1_1">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_TLSv1_1</span></span><a class="headerlink" href="#ssl.PROTOCOL_TLSv1_1" title="Link to this definition">¶</a></dt>
<dd><p>Selects TLS version 1.1 as the channel encryption protocol.
Available only with openssl version 1.0.1+.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>OpenSSL has deprecated all version specific protocols.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.PROTOCOL_TLSv1_2">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_TLSv1_2</span></span><a class="headerlink" href="#ssl.PROTOCOL_TLSv1_2" title="Link to this definition">¶</a></dt>
<dd><p>Selects TLS version 1.2 as the channel encryption protocol.
Available only with openssl version 1.0.1+.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>OpenSSL has deprecated all version specific protocols.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_ALL">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_ALL</span></span><a class="headerlink" href="#ssl.OP_ALL" title="Link to this definition">¶</a></dt>
<dd><p>Enables workarounds for various bugs present in other SSL implementations.
This option is set by default.  It does not necessarily set the same
flags as OpenSSL’s <code class="docutils literal notranslate"><span class="pre">SSL_OP_ALL</span></code> constant.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_SSLv2">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_SSLv2</span></span><a class="headerlink" href="#ssl.OP_NO_SSLv2" title="Link to this definition">¶</a></dt>
<dd><p>Prevents an SSLv2 connection.  This option is only applicable in
conjunction with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>.  It prevents the peers from
choosing SSLv2 as the protocol version.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>SSLv2 is deprecated</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_SSLv3">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_SSLv3</span></span><a class="headerlink" href="#ssl.OP_NO_SSLv3" title="Link to this definition">¶</a></dt>
<dd><p>Prevents an SSLv3 connection.  This option is only applicable in
conjunction with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>.  It prevents the peers from
choosing SSLv3 as the protocol version.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>SSLv3 is deprecated</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_TLSv1">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_TLSv1</span></span><a class="headerlink" href="#ssl.OP_NO_TLSv1" title="Link to this definition">¶</a></dt>
<dd><p>Prevents a TLSv1 connection.  This option is only applicable in
conjunction with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>.  It prevents the peers from
choosing TLSv1 as the protocol version.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>The option is deprecated since OpenSSL 1.1.0, use the new
<a class="reference internal" href="#ssl.SSLContext.minimum_version" title="ssl.SSLContext.minimum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.minimum_version</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.maximum_version</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_TLSv1_1">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_TLSv1_1</span></span><a class="headerlink" href="#ssl.OP_NO_TLSv1_1" title="Link to this definition">¶</a></dt>
<dd><p>Prevents a TLSv1.1 connection. This option is only applicable in conjunction
with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>. It prevents the peers from choosing TLSv1.1 as
the protocol version. Available only with openssl version 1.0.1+.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>The option is deprecated since OpenSSL 1.1.0.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_TLSv1_2">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_TLSv1_2</span></span><a class="headerlink" href="#ssl.OP_NO_TLSv1_2" title="Link to this definition">¶</a></dt>
<dd><p>Prevents a TLSv1.2 connection. This option is only applicable in conjunction
with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>. It prevents the peers from choosing TLSv1.2 as
the protocol version. Available only with openssl version 1.0.1+.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>The option is deprecated since OpenSSL 1.1.0.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_TLSv1_3">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_TLSv1_3</span></span><a class="headerlink" href="#ssl.OP_NO_TLSv1_3" title="Link to this definition">¶</a></dt>
<dd><p>Prevents a TLSv1.3 connection. This option is only applicable in conjunction
with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>. It prevents the peers from choosing TLSv1.3 as
the protocol version. TLS 1.3 is available with OpenSSL 1.1.1 or later.
When Python has been compiled against an older version of OpenSSL, the
flag defaults to <em>0</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>The option is deprecated since OpenSSL 1.1.0. It was added to 2.7.15 and
3.6.3 for backwards compatibility with OpenSSL 1.0.2.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_RENEGOTIATION">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_RENEGOTIATION</span></span><a class="headerlink" href="#ssl.OP_NO_RENEGOTIATION" title="Link to this definition">¶</a></dt>
<dd><p>Disable all renegotiation in TLSv1.2 and earlier. Do not send
HelloRequest messages, and ignore renegotiation requests via ClientHello.</p>
<p>This option is only available with OpenSSL 1.1.0h and later.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_CIPHER_SERVER_PREFERENCE">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_CIPHER_SERVER_PREFERENCE</span></span><a class="headerlink" href="#ssl.OP_CIPHER_SERVER_PREFERENCE" title="Link to this definition">¶</a></dt>
<dd><p>Use the server’s cipher ordering preference, rather than the client’s.
This option has no effect on client sockets and SSLv2 server sockets.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_SINGLE_DH_USE">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_SINGLE_DH_USE</span></span><a class="headerlink" href="#ssl.OP_SINGLE_DH_USE" title="Link to this definition">¶</a></dt>
<dd><p>Prevents re-use of the same DH key for distinct SSL sessions.  This
improves forward secrecy but requires more computational resources.
This option only applies to server sockets.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_SINGLE_ECDH_USE">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_SINGLE_ECDH_USE</span></span><a class="headerlink" href="#ssl.OP_SINGLE_ECDH_USE" title="Link to this definition">¶</a></dt>
<dd><p>Prevents re-use of the same ECDH key for distinct SSL sessions.  This
improves forward secrecy but requires more computational resources.
This option only applies to server sockets.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_ENABLE_MIDDLEBOX_COMPAT">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_ENABLE_MIDDLEBOX_COMPAT</span></span><a class="headerlink" href="#ssl.OP_ENABLE_MIDDLEBOX_COMPAT" title="Link to this definition">¶</a></dt>
<dd><p>Send dummy Change Cipher Spec (CCS) messages in TLS 1.3 handshake to make
a TLS 1.3 connection look more like a TLS 1.2 connection.</p>
<p>This option is only available with OpenSSL 1.1.1 and later.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_COMPRESSION">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_COMPRESSION</span></span><a class="headerlink" href="#ssl.OP_NO_COMPRESSION" title="Link to this definition">¶</a></dt>
<dd><p>Disable compression on the SSL channel.  This is useful if the application
protocol supports its own compression scheme.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ssl.Options">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">Options</span></span><a class="headerlink" href="#ssl.Options" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntFlag</span></code></a> collection of OP_* constants.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_NO_TICKET">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_NO_TICKET</span></span><a class="headerlink" href="#ssl.OP_NO_TICKET" title="Link to this definition">¶</a></dt>
<dd><p>Prevent client side from requesting a session ticket.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_IGNORE_UNEXPECTED_EOF">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_IGNORE_UNEXPECTED_EOF</span></span><a class="headerlink" href="#ssl.OP_IGNORE_UNEXPECTED_EOF" title="Link to this definition">¶</a></dt>
<dd><p>Ignore unexpected shutdown of TLS connections.</p>
<p>This option is only available with OpenSSL 3.0.0 and later.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_ENABLE_KTLS">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_ENABLE_KTLS</span></span><a class="headerlink" href="#ssl.OP_ENABLE_KTLS" title="Link to this definition">¶</a></dt>
<dd><p>Enable the use of the kernel TLS. To benefit from the feature, OpenSSL must
have been compiled with support for it, and the negotiated cipher suites and
extensions must be supported by it (a list of supported ones may vary by
platform and kernel version).</p>
<p>Note that with enabled kernel TLS some cryptographic operations are
performed by the kernel directly and not via any available OpenSSL
Providers. This might be undesirable if, for example, the application
requires all cryptographic operations to be performed by the FIPS provider.</p>
<p>This option is only available with OpenSSL 3.0.0 and later.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OP_LEGACY_SERVER_CONNECT">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OP_LEGACY_SERVER_CONNECT</span></span><a class="headerlink" href="#ssl.OP_LEGACY_SERVER_CONNECT" title="Link to this definition">¶</a></dt>
<dd><p>Allow legacy insecure renegotiation between OpenSSL and unpatched servers
only.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_ALPN">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_ALPN</span></span><a class="headerlink" href="#ssl.HAS_ALPN" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the <em>Application-Layer
Protocol Negotiation</em> TLS extension as described in <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7301.html"><strong>RFC 7301</strong></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_NEVER_CHECK_COMMON_NAME">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_NEVER_CHECK_COMMON_NAME</span></span><a class="headerlink" href="#ssl.HAS_NEVER_CHECK_COMMON_NAME" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support not checking subject
common name and <a class="reference internal" href="#ssl.SSLContext.hostname_checks_common_name" title="ssl.SSLContext.hostname_checks_common_name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.hostname_checks_common_name</span></code></a> is
writeable.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_ECDH">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_ECDH</span></span><a class="headerlink" href="#ssl.HAS_ECDH" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the Elliptic Curve-based
Diffie-Hellman key exchange.  This should be true unless the feature was
explicitly disabled by the distributor.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_SNI">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_SNI</span></span><a class="headerlink" href="#ssl.HAS_SNI" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the <em>Server Name
Indication</em> extension (as defined in <span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6066.html"><strong>RFC 6066</strong></a>).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_NPN">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_NPN</span></span><a class="headerlink" href="#ssl.HAS_NPN" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the <em>Next Protocol
Negotiation</em> as described in the <a class="reference external" href="https://en.wikipedia.org/wiki/Application-Layer_Protocol_Negotiation">Application Layer Protocol
Negotiation</a>.
When true, you can use the <a class="reference internal" href="#ssl.SSLContext.set_npn_protocols" title="ssl.SSLContext.set_npn_protocols"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_npn_protocols()</span></code></a> method to advertise
which protocols you want to support.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_SSLv2">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_SSLv2</span></span><a class="headerlink" href="#ssl.HAS_SSLv2" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the SSL 2.0 protocol.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_SSLv3">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_SSLv3</span></span><a class="headerlink" href="#ssl.HAS_SSLv3" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the SSL 3.0 protocol.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_TLSv1">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_TLSv1</span></span><a class="headerlink" href="#ssl.HAS_TLSv1" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the TLS 1.0 protocol.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_TLSv1_1">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_TLSv1_1</span></span><a class="headerlink" href="#ssl.HAS_TLSv1_1" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the TLS 1.1 protocol.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_TLSv1_2">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_TLSv1_2</span></span><a class="headerlink" href="#ssl.HAS_TLSv1_2" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the TLS 1.2 protocol.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.HAS_TLSv1_3">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">HAS_TLSv1_3</span></span><a class="headerlink" href="#ssl.HAS_TLSv1_3" title="Link to this definition">¶</a></dt>
<dd><p>Whether the OpenSSL library has built-in support for the TLS 1.3 protocol.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.CHANNEL_BINDING_TYPES">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">CHANNEL_BINDING_TYPES</span></span><a class="headerlink" href="#ssl.CHANNEL_BINDING_TYPES" title="Link to this definition">¶</a></dt>
<dd><p>List of supported TLS channel binding types.  Strings in this list
can be used as arguments to <a class="reference internal" href="#ssl.SSLSocket.get_channel_binding" title="ssl.SSLSocket.get_channel_binding"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.get_channel_binding()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OPENSSL_VERSION">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OPENSSL_VERSION</span></span><a class="headerlink" href="#ssl.OPENSSL_VERSION" title="Link to this definition">¶</a></dt>
<dd><p>The version string of the OpenSSL library loaded by the interpreter:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">OPENSSL_VERSION</span>
<span class="go">&#39;OpenSSL 1.0.2k  26 Jan 2017&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OPENSSL_VERSION_INFO">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OPENSSL_VERSION_INFO</span></span><a class="headerlink" href="#ssl.OPENSSL_VERSION_INFO" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of five integers representing version information about the
OpenSSL library:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">OPENSSL_VERSION_INFO</span>
<span class="go">(1, 0, 2, 11, 15)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.OPENSSL_VERSION_NUMBER">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">OPENSSL_VERSION_NUMBER</span></span><a class="headerlink" href="#ssl.OPENSSL_VERSION_NUMBER" title="Link to this definition">¶</a></dt>
<dd><p>The raw version number of the OpenSSL library, as a single integer:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">OPENSSL_VERSION_NUMBER</span>
<span class="go">268443839</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">hex</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">OPENSSL_VERSION_NUMBER</span><span class="p">)</span>
<span class="go">&#39;0x100020bf&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">ALERT_DESCRIPTION_HANDSHAKE_FAILURE</span></span><a class="headerlink" href="#ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="ssl.ALERT_DESCRIPTION_INTERNAL_ERROR">
<span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">ALERT_DESCRIPTION_INTERNAL_ERROR</span></span><a class="headerlink" href="#ssl.ALERT_DESCRIPTION_INTERNAL_ERROR" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">ALERT_DESCRIPTION_*</span></span></dt>
<dd><p>Alert Descriptions from <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5246.html"><strong>RFC 5246</strong></a> and others. The <a class="reference external" href="https://www.iana.org/assignments/tls-parameters/tls-parameters.xml#tls-parameters-6">IANA TLS Alert Registry</a>
contains this list and references to the RFCs where their meaning is defined.</p>
<p>Used as the return value of the callback function in
<a class="reference internal" href="#ssl.SSLContext.set_servername_callback" title="ssl.SSLContext.set_servername_callback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_servername_callback()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ssl.AlertDescription">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">AlertDescription</span></span><a class="headerlink" href="#ssl.AlertDescription" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntEnum</span></code></a> collection of ALERT_DESCRIPTION_* constants.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.Purpose.SERVER_AUTH">
<span class="sig-prename descclassname"><span class="pre">Purpose.</span></span><span class="sig-name descname"><span class="pre">SERVER_AUTH</span></span><a class="headerlink" href="#ssl.Purpose.SERVER_AUTH" title="Link to this definition">¶</a></dt>
<dd><p>Option for <a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.load_default_certs" title="ssl.SSLContext.load_default_certs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_default_certs()</span></code></a>.  This value indicates that the
context may be used to authenticate web servers (therefore, it will
be used to create client-side sockets).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ssl.Purpose.CLIENT_AUTH">
<span class="sig-prename descclassname"><span class="pre">Purpose.</span></span><span class="sig-name descname"><span class="pre">CLIENT_AUTH</span></span><a class="headerlink" href="#ssl.Purpose.CLIENT_AUTH" title="Link to this definition">¶</a></dt>
<dd><p>Option for <a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.load_default_certs" title="ssl.SSLContext.load_default_certs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_default_certs()</span></code></a>.  This value indicates that the
context may be used to authenticate web clients (therefore, it will
be used to create server-side sockets).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ssl.SSLErrorNumber">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLErrorNumber</span></span><a class="headerlink" href="#ssl.SSLErrorNumber" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntEnum</span></code></a> collection of SSL_ERROR_* constants.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ssl.TLSVersion">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">TLSVersion</span></span><a class="headerlink" href="#ssl.TLSVersion" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntEnum</span></code></a> collection of SSL and TLS versions for
<a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.maximum_version</span></code></a> and <a class="reference internal" href="#ssl.SSLContext.minimum_version" title="ssl.SSLContext.minimum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.minimum_version</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.MINIMUM_SUPPORTED">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">MINIMUM_SUPPORTED</span></span><a class="headerlink" href="#ssl.TLSVersion.MINIMUM_SUPPORTED" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.MAXIMUM_SUPPORTED">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">MAXIMUM_SUPPORTED</span></span><a class="headerlink" href="#ssl.TLSVersion.MAXIMUM_SUPPORTED" title="Link to this definition">¶</a></dt>
<dd><p>The minimum or maximum supported SSL or TLS version. These are magic
constants. Their values don’t reflect the lowest and highest available
TLS/SSL versions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.SSLv3">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">SSLv3</span></span><a class="headerlink" href="#ssl.TLSVersion.SSLv3" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.TLSv1">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">TLSv1</span></span><a class="headerlink" href="#ssl.TLSVersion.TLSv1" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.TLSv1_1">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">TLSv1_1</span></span><a class="headerlink" href="#ssl.TLSVersion.TLSv1_1" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.TLSv1_2">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">TLSv1_2</span></span><a class="headerlink" href="#ssl.TLSVersion.TLSv1_2" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.TLSVersion.TLSv1_3">
<span class="sig-prename descclassname"><span class="pre">TLSVersion.</span></span><span class="sig-name descname"><span class="pre">TLSv1_3</span></span><a class="headerlink" href="#ssl.TLSVersion.TLSv1_3" title="Link to this definition">¶</a></dt>
<dd><p>SSL 3.0 to TLS 1.3.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.10: </span>All <a class="reference internal" href="#ssl.TLSVersion" title="ssl.TLSVersion"><code class="xref py py-class docutils literal notranslate"><span class="pre">TLSVersion</span></code></a> members except <a class="reference internal" href="#ssl.TLSVersion.TLSv1_2" title="ssl.TLSVersion.TLSv1_2"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TLSVersion.TLSv1_2</span></code></a> and
<a class="reference internal" href="#ssl.TLSVersion.TLSv1_3" title="ssl.TLSVersion.TLSv1_3"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TLSVersion.TLSv1_3</span></code></a> are deprecated.</p>
</div>
</dd></dl>

</section>
</section>
<section id="ssl-sockets">
<h2>SSL Sockets<a class="headerlink" href="#ssl-sockets" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="ssl.SSLSocket">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLSocket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">socket.socket</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket" title="Link to this definition">¶</a></dt>
<dd><p>SSL sockets provide the following methods of <a class="reference internal" href="socket.html#socket-objects"><span class="std std-ref">Socket Objects</span></a>:</p>
<ul class="simple">
<li><p><a class="reference internal" href="socket.html#socket.socket.accept" title="socket.socket.accept"><code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.bind" title="socket.socket.bind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">bind()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.close" title="socket.socket.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.detach" title="socket.socket.detach"><code class="xref py py-meth docutils literal notranslate"><span class="pre">detach()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.fileno" title="socket.socket.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.getpeername" title="socket.socket.getpeername"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getpeername()</span></code></a>, <a class="reference internal" href="socket.html#socket.socket.getsockname" title="socket.socket.getsockname"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getsockname()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.getsockopt" title="socket.socket.getsockopt"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getsockopt()</span></code></a>, <a class="reference internal" href="socket.html#socket.socket.setsockopt" title="socket.socket.setsockopt"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setsockopt()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.gettimeout" title="socket.socket.gettimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">gettimeout()</span></code></a>, <a class="reference internal" href="socket.html#socket.socket.settimeout" title="socket.socket.settimeout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">settimeout()</span></code></a>,
<a class="reference internal" href="socket.html#socket.socket.setblocking" title="socket.socket.setblocking"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setblocking()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.listen" title="socket.socket.listen"><code class="xref py py-meth docutils literal notranslate"><span class="pre">listen()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makefile()</span></code></a></p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a>, <a class="reference internal" href="socket.html#socket.socket.recv_into" title="socket.socket.recv_into"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv_into()</span></code></a>
(but passing a non-zero <code class="docutils literal notranslate"><span class="pre">flags</span></code> argument is not allowed)</p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a>, <a class="reference internal" href="socket.html#socket.socket.sendall" title="socket.socket.sendall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendall()</span></code></a> (with
the same limitation)</p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.sendfile" title="socket.socket.sendfile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendfile()</span></code></a> (but <a class="reference internal" href="os.html#os.sendfile" title="os.sendfile"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.sendfile</span></code></a> will be used
for plain-text sockets only, else <a class="reference internal" href="socket.html#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a> will be used)</p></li>
<li><p><a class="reference internal" href="socket.html#socket.socket.shutdown" title="socket.socket.shutdown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shutdown()</span></code></a></p></li>
</ul>
<p>However, since the SSL (and TLS) protocol has its own framing atop
of TCP, the SSL sockets abstraction can, in certain respects, diverge from
the specification of normal, OS-level sockets.  See especially the
<a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">notes on non-blocking sockets</span></a>.</p>
<p>Instances of <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> must be created using the
<a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a> method.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <code class="xref py py-meth docutils literal notranslate"><span class="pre">sendfile()</span></code> method was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <code class="xref py py-meth docutils literal notranslate"><span class="pre">shutdown()</span></code> does not reset the socket timeout each time bytes
are received or sent. The socket timeout is now the maximum total duration
of the shutdown.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>It is deprecated to create a <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> instance directly, use
<a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a> to wrap a socket.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> instances must to created with
<a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">wrap_socket()</span></code></a>. In earlier versions, it was possible
to create instances directly. This was never documented or officially
supported.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Python now uses <code class="docutils literal notranslate"><span class="pre">SSL_read_ex</span></code> and <code class="docutils literal notranslate"><span class="pre">SSL_write_ex</span></code> internally. The
functions support reading and writing of data larger than 2 GB. Writing
zero-length data no longer fails with a protocol violation error.</p>
</div>
</dd></dl>

<p>SSL sockets also have the following additional methods and attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.read">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">len</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1024</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.read" title="Link to this definition">¶</a></dt>
<dd><p>Read up to <em>len</em> bytes of data from the SSL socket and return the result as
a <code class="docutils literal notranslate"><span class="pre">bytes</span></code> instance. If <em>buffer</em> is specified, then read into the buffer
instead, and return the number of bytes read.</p>
<p>Raise <a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a> or <a class="reference internal" href="#ssl.SSLWantWriteError" title="ssl.SSLWantWriteError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantWriteError</span></code></a> if the socket is
<a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking</span></a> and the read would block.</p>
<p>As at any time a re-negotiation is possible, a call to <a class="reference internal" href="#ssl.SSLSocket.read" title="ssl.SSLSocket.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> can also
cause write operations.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The socket timeout is no longer reset each time bytes are received or sent.
The socket timeout is now the maximum total duration to read up to <em>len</em>
bytes.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>Use <code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code> instead of <a class="reference internal" href="#ssl.SSLSocket.read" title="ssl.SSLSocket.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.write">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buf</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.write" title="Link to this definition">¶</a></dt>
<dd><p>Write <em>buf</em> to the SSL socket and return the number of bytes written. The
<em>buf</em> argument must be an object supporting the buffer interface.</p>
<p>Raise <a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a> or <a class="reference internal" href="#ssl.SSLWantWriteError" title="ssl.SSLWantWriteError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantWriteError</span></code></a> if the socket is
<a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking</span></a> and the write would block.</p>
<p>As at any time a re-negotiation is possible, a call to <a class="reference internal" href="#ssl.SSLSocket.write" title="ssl.SSLSocket.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> can
also cause read operations.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The socket timeout is no longer reset each time bytes are received or sent.
The socket timeout is now the maximum total duration to write <em>buf</em>.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>Use <code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code> instead of <a class="reference internal" href="#ssl.SSLSocket.write" title="ssl.SSLSocket.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a>.</p>
</div>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#ssl.SSLSocket.read" title="ssl.SSLSocket.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> and <a class="reference internal" href="#ssl.SSLSocket.write" title="ssl.SSLSocket.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> methods are the
low-level methods that read and write unencrypted, application-level data
and decrypt/encrypt it to encrypted, wire-level data. These methods
require an active SSL connection, i.e. the handshake was completed and
<a class="reference internal" href="#ssl.SSLSocket.unwrap" title="ssl.SSLSocket.unwrap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.unwrap()</span></code></a> was not called.</p>
<p>Normally you should use the socket API methods like
<a class="reference internal" href="socket.html#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> and <a class="reference internal" href="socket.html#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a> instead of these
methods.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.do_handshake">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">do_handshake</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.do_handshake" title="Link to this definition">¶</a></dt>
<dd><p>Perform the SSL setup handshake.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The handshake method also performs <code class="xref py py-func docutils literal notranslate"><span class="pre">match_hostname()</span></code> when the
<a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> attribute of the socket’s
<a class="reference internal" href="#ssl.SSLSocket.context" title="ssl.SSLSocket.context"><code class="xref py py-attr docutils literal notranslate"><span class="pre">context</span></code></a> is true.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The socket timeout is no longer reset each time bytes are received or sent.
The socket timeout is now the maximum total duration of the handshake.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Hostname or IP address is matched by OpenSSL during handshake. The
function <code class="xref py py-func docutils literal notranslate"><span class="pre">match_hostname()</span></code> is no longer used. In case OpenSSL
refuses a hostname or IP address, the handshake is aborted early and
a TLS alert message is sent to the peer.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.getpeercert">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">getpeercert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary_form</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.getpeercert" title="Link to this definition">¶</a></dt>
<dd><p>If there is no certificate for the peer on the other end of the connection,
return <code class="docutils literal notranslate"><span class="pre">None</span></code>.  If the SSL handshake hasn’t been done yet, raise
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p>If the <code class="docutils literal notranslate"><span class="pre">binary_form</span></code> parameter is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>, and a certificate was
received from the peer, this method returns a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> instance.  If the
certificate was not validated, the dict is empty.  If the certificate was
validated, it returns a dict with several keys, amongst them <code class="docutils literal notranslate"><span class="pre">subject</span></code>
(the principal for which the certificate was issued) and <code class="docutils literal notranslate"><span class="pre">issuer</span></code>
(the principal issuing the certificate).  If a certificate contains an
instance of the <em>Subject Alternative Name</em> extension (see <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3280.html"><strong>RFC 3280</strong></a>),
there will also be a <code class="docutils literal notranslate"><span class="pre">subjectAltName</span></code> key in the dictionary.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">subject</span></code> and <code class="docutils literal notranslate"><span class="pre">issuer</span></code> fields are tuples containing the sequence
of relative distinguished names (RDNs) given in the certificate’s data
structure for the respective fields, and each RDN is a sequence of
name-value pairs.  Here is a real-world example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">{</span><span class="s1">&#39;issuer&#39;</span><span class="p">:</span> <span class="p">(((</span><span class="s1">&#39;countryName&#39;</span><span class="p">,</span> <span class="s1">&#39;IL&#39;</span><span class="p">),),</span>
            <span class="p">((</span><span class="s1">&#39;organizationName&#39;</span><span class="p">,</span> <span class="s1">&#39;StartCom Ltd.&#39;</span><span class="p">),),</span>
            <span class="p">((</span><span class="s1">&#39;organizationalUnitName&#39;</span><span class="p">,</span>
              <span class="s1">&#39;Secure Digital Certificate Signing&#39;</span><span class="p">),),</span>
            <span class="p">((</span><span class="s1">&#39;commonName&#39;</span><span class="p">,</span>
              <span class="s1">&#39;StartCom Class 2 Primary Intermediate Server CA&#39;</span><span class="p">),)),</span>
 <span class="s1">&#39;notAfter&#39;</span><span class="p">:</span> <span class="s1">&#39;Nov 22 08:15:19 2013 GMT&#39;</span><span class="p">,</span>
 <span class="s1">&#39;notBefore&#39;</span><span class="p">:</span> <span class="s1">&#39;Nov 21 03:09:52 2011 GMT&#39;</span><span class="p">,</span>
 <span class="s1">&#39;serialNumber&#39;</span><span class="p">:</span> <span class="s1">&#39;95F0&#39;</span><span class="p">,</span>
 <span class="s1">&#39;subject&#39;</span><span class="p">:</span> <span class="p">(((</span><span class="s1">&#39;description&#39;</span><span class="p">,</span> <span class="s1">&#39;571208-SLe257oHY9fVQ07Z&#39;</span><span class="p">),),</span>
             <span class="p">((</span><span class="s1">&#39;countryName&#39;</span><span class="p">,</span> <span class="s1">&#39;US&#39;</span><span class="p">),),</span>
             <span class="p">((</span><span class="s1">&#39;stateOrProvinceName&#39;</span><span class="p">,</span> <span class="s1">&#39;California&#39;</span><span class="p">),),</span>
             <span class="p">((</span><span class="s1">&#39;localityName&#39;</span><span class="p">,</span> <span class="s1">&#39;San Francisco&#39;</span><span class="p">),),</span>
             <span class="p">((</span><span class="s1">&#39;organizationName&#39;</span><span class="p">,</span> <span class="s1">&#39;Electronic Frontier Foundation, Inc.&#39;</span><span class="p">),),</span>
             <span class="p">((</span><span class="s1">&#39;commonName&#39;</span><span class="p">,</span> <span class="s1">&#39;*.eff.org&#39;</span><span class="p">),),</span>
             <span class="p">((</span><span class="s1">&#39;emailAddress&#39;</span><span class="p">,</span> <span class="s1">&#39;<EMAIL>&#39;</span><span class="p">),)),</span>
 <span class="s1">&#39;subjectAltName&#39;</span><span class="p">:</span> <span class="p">((</span><span class="s1">&#39;DNS&#39;</span><span class="p">,</span> <span class="s1">&#39;*.eff.org&#39;</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;DNS&#39;</span><span class="p">,</span> <span class="s1">&#39;eff.org&#39;</span><span class="p">)),</span>
 <span class="s1">&#39;version&#39;</span><span class="p">:</span> <span class="mi">3</span><span class="p">}</span>
</pre></div>
</div>
<p>If the <code class="docutils literal notranslate"><span class="pre">binary_form</span></code> parameter is <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, and a certificate was
provided, this method returns the DER-encoded form of the entire certificate
as a sequence of bytes, or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if the peer did not provide a
certificate.  Whether the peer provides a certificate depends on the SSL
socket’s role:</p>
<ul class="simple">
<li><p>for a client SSL socket, the server will always provide a certificate,
regardless of whether validation was required;</p></li>
<li><p>for a server SSL socket, the client will only provide a certificate
when requested by the server; therefore <a class="reference internal" href="#ssl.SSLSocket.getpeercert" title="ssl.SSLSocket.getpeercert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getpeercert()</span></code></a> will return
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if you used <a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a> (rather than
<a class="reference internal" href="#ssl.CERT_OPTIONAL" title="ssl.CERT_OPTIONAL"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_OPTIONAL</span></code></a> or <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>).</p></li>
</ul>
<p>See also <a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.check_hostname</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The returned dictionary includes additional items such as <code class="docutils literal notranslate"><span class="pre">issuer</span></code>
and <code class="docutils literal notranslate"><span class="pre">notBefore</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised when the handshake isn’t done.
The returned dictionary includes additional X509v3 extension items
  such as <code class="docutils literal notranslate"><span class="pre">crlDistributionPoints</span></code>, <code class="docutils literal notranslate"><span class="pre">caIssuers</span></code> and <code class="docutils literal notranslate"><span class="pre">OCSP</span></code> URIs.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>IPv6 address strings no longer have a trailing new line.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.cipher">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">cipher</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.cipher" title="Link to this definition">¶</a></dt>
<dd><p>Returns a three-value tuple containing the name of the cipher being used, the
version of the SSL protocol that defines its use, and the number of secret
bits being used.  If no connection has been established, returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.shared_ciphers">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">shared_ciphers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.shared_ciphers" title="Link to this definition">¶</a></dt>
<dd><p>Return the list of ciphers available in both the client and server.  Each
entry of the returned list is a three-value tuple containing the name of the
cipher, the version of the SSL protocol that defines its use, and the number
of secret bits the cipher uses.  <a class="reference internal" href="#ssl.SSLSocket.shared_ciphers" title="ssl.SSLSocket.shared_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shared_ciphers()</span></code></a> returns
<code class="docutils literal notranslate"><span class="pre">None</span></code> if no connection has been established or the socket is a client
socket.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.compression">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">compression</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.compression" title="Link to this definition">¶</a></dt>
<dd><p>Return the compression algorithm being used as a string, or <code class="docutils literal notranslate"><span class="pre">None</span></code>
if the connection isn’t compressed.</p>
<p>If the higher-level protocol supports its own compression mechanism,
you can use <a class="reference internal" href="#ssl.OP_NO_COMPRESSION" title="ssl.OP_NO_COMPRESSION"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_COMPRESSION</span></code></a> to disable SSL-level compression.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.get_channel_binding">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">get_channel_binding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cb_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'tls-unique'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.get_channel_binding" title="Link to this definition">¶</a></dt>
<dd><p>Get channel binding data for current connection, as a bytes object.  Returns
<code class="docutils literal notranslate"><span class="pre">None</span></code> if not connected or the handshake has not been completed.</p>
<p>The <em>cb_type</em> parameter allow selection of the desired channel binding
type. Valid channel binding types are listed in the
<a class="reference internal" href="#ssl.CHANNEL_BINDING_TYPES" title="ssl.CHANNEL_BINDING_TYPES"><code class="xref py py-data docutils literal notranslate"><span class="pre">CHANNEL_BINDING_TYPES</span></code></a> list.  Currently only the ‘tls-unique’ channel
binding, defined by <span class="target" id="index-11"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5929.html"><strong>RFC 5929</strong></a>, is supported.  <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be
raised if an unsupported channel binding type is requested.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.selected_alpn_protocol">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">selected_alpn_protocol</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.selected_alpn_protocol" title="Link to this definition">¶</a></dt>
<dd><p>Return the protocol that was selected during the TLS handshake.  If
<a class="reference internal" href="#ssl.SSLContext.set_alpn_protocols" title="ssl.SSLContext.set_alpn_protocols"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_alpn_protocols()</span></code></a> was not called, if the other party does
not support ALPN, if this socket does not support any of the client’s
proposed protocols, or if the handshake has not happened yet, <code class="docutils literal notranslate"><span class="pre">None</span></code> is
returned.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.selected_npn_protocol">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">selected_npn_protocol</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.selected_npn_protocol" title="Link to this definition">¶</a></dt>
<dd><p>Return the higher-level protocol that was selected during the TLS/SSL
handshake. If <a class="reference internal" href="#ssl.SSLContext.set_npn_protocols" title="ssl.SSLContext.set_npn_protocols"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_npn_protocols()</span></code></a> was not called, or
if the other party does not support NPN, or if the handshake has not yet
happened, this will return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.10: </span>NPN has been superseded by ALPN</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.unwrap">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">unwrap</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.unwrap" title="Link to this definition">¶</a></dt>
<dd><p>Performs the SSL shutdown handshake, which removes the TLS layer from the
underlying socket, and returns the underlying socket object.  This can be
used to go from encrypted operation over a connection to unencrypted.  The
returned socket should always be used for further communication with the
other side of the connection, rather than the original socket.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.verify_client_post_handshake">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">verify_client_post_handshake</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.verify_client_post_handshake" title="Link to this definition">¶</a></dt>
<dd><p>Requests post-handshake authentication (PHA) from a TLS 1.3 client. PHA
can only be initiated for a TLS 1.3 connection from a server-side socket,
after the initial TLS handshake and with PHA enabled on both sides, see
<a class="reference internal" href="#ssl.SSLContext.post_handshake_auth" title="ssl.SSLContext.post_handshake_auth"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.post_handshake_auth</span></code></a>.</p>
<p>The method does not perform a cert exchange immediately. The server-side
sends a CertificateRequest during the next write event and expects the
client to respond with a certificate on the next read event.</p>
<p>If any precondition isn’t met (e.g. not TLS 1.3, PHA not enabled), an
<a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a> is raised.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Only available with OpenSSL 1.1.1 and TLS 1.3 enabled. Without TLS 1.3
support, the method raises <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.version">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.version" title="Link to this definition">¶</a></dt>
<dd><p>Return the actual SSL protocol version negotiated by the connection
as a string, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no secure connection is established.
As of this writing, possible return values include <code class="docutils literal notranslate"><span class="pre">&quot;SSLv2&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;SSLv3&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;TLSv1&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;TLSv1.1&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;TLSv1.2&quot;</span></code>.
Recent OpenSSL versions may define more return values.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLSocket.pending">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">pending</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLSocket.pending" title="Link to this definition">¶</a></dt>
<dd><p>Returns the number of already decrypted bytes available for read, pending on
the connection.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSocket.context">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">context</span></span><a class="headerlink" href="#ssl.SSLSocket.context" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> object this SSL socket is tied to.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSocket.server_side">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">server_side</span></span><a class="headerlink" href="#ssl.SSLSocket.server_side" title="Link to this definition">¶</a></dt>
<dd><p>A boolean which is <code class="docutils literal notranslate"><span class="pre">True</span></code> for server-side sockets and <code class="docutils literal notranslate"><span class="pre">False</span></code> for
client-side sockets.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSocket.server_hostname">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">server_hostname</span></span><a class="headerlink" href="#ssl.SSLSocket.server_hostname" title="Link to this definition">¶</a></dt>
<dd><p>Hostname of the server: <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> type, or <code class="docutils literal notranslate"><span class="pre">None</span></code> for server-side
socket or if the hostname was not specified in the constructor.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The attribute is now always ASCII text. When <code class="docutils literal notranslate"><span class="pre">server_hostname</span></code> is
an internationalized domain name (IDN), this attribute now stores the
A-label form (<code class="docutils literal notranslate"><span class="pre">&quot;xn--pythn-mua.org&quot;</span></code>), rather than the U-label form
(<code class="docutils literal notranslate"><span class="pre">&quot;pythön.org&quot;</span></code>).</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSocket.session">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">session</span></span><a class="headerlink" href="#ssl.SSLSocket.session" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#ssl.SSLSession" title="ssl.SSLSession"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSession</span></code></a> for this SSL connection. The session is available
for client and server side sockets after the TLS handshake has been
performed. For client sockets the session can be set before
<a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">do_handshake()</span></code></a> has been called to reuse a session.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSocket.session_reused">
<span class="sig-prename descclassname"><span class="pre">SSLSocket.</span></span><span class="sig-name descname"><span class="pre">session_reused</span></span><a class="headerlink" href="#ssl.SSLSocket.session_reused" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

</section>
<section id="ssl-contexts">
<h2>SSL Contexts<a class="headerlink" href="#ssl-contexts" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>An SSL context holds various data longer-lived than single SSL connections,
such as SSL configuration options, certificate(s) and private key(s).
It also manages a cache of SSL sessions for server-side sockets, in order
to speed up repeated connections from the same clients.</p>
<dl class="py class">
<dt class="sig sig-object py" id="ssl.SSLContext">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLContext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">protocol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext" title="Link to this definition">¶</a></dt>
<dd><p>Create a new SSL context.  You may pass <em>protocol</em> which must be one
of the <code class="docutils literal notranslate"><span class="pre">PROTOCOL_*</span></code> constants defined in this module.  The parameter
specifies which version of the SSL protocol to use.  Typically, the
server chooses a particular protocol version, and the client must adapt
to the server’s choice.  Most of the versions are not interoperable
with the other versions.  If not specified, the default is
<a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>; it provides the most compatibility with other
versions.</p>
<p>Here’s a table showing which versions in a client (down the side) can connect
to which versions in a server (along the top):</p>
<table class="docutils align-default">
<tbody>
<tr class="row-odd"><td><p><em>client</em> / <strong>server</strong></p></td>
<td><p><strong>SSLv2</strong></p></td>
<td><p><strong>SSLv3</strong></p></td>
<td><p><strong>TLS</strong> <a class="footnote-reference brackets" href="#id9" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>3<span class="fn-bracket">]</span></a></p></td>
<td><p><strong>TLSv1</strong></p></td>
<td><p><strong>TLSv1.1</strong></p></td>
<td><p><strong>TLSv1.2</strong></p></td>
</tr>
<tr class="row-even"><td><p><em>SSLv2</em></p></td>
<td><p>yes</p></td>
<td><p>no</p></td>
<td><p>no <a class="footnote-reference brackets" href="#id7" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></td>
<td><p>no</p></td>
<td><p>no</p></td>
<td><p>no</p></td>
</tr>
<tr class="row-odd"><td><p><em>SSLv3</em></p></td>
<td><p>no</p></td>
<td><p>yes</p></td>
<td><p>no <a class="footnote-reference brackets" href="#id8" id="id3" role="doc-noteref"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></a></p></td>
<td><p>no</p></td>
<td><p>no</p></td>
<td><p>no</p></td>
</tr>
<tr class="row-even"><td><p><em>TLS</em> (<em>SSLv23</em>) <a class="footnote-reference brackets" href="#id9" id="id4" role="doc-noteref"><span class="fn-bracket">[</span>3<span class="fn-bracket">]</span></a></p></td>
<td><p>no <a class="footnote-reference brackets" href="#id7" id="id5" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></td>
<td><p>no <a class="footnote-reference brackets" href="#id8" id="id6" role="doc-noteref"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></a></p></td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>yes</p></td>
</tr>
<tr class="row-odd"><td><p><em>TLSv1</em></p></td>
<td><p>no</p></td>
<td><p>no</p></td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>no</p></td>
<td><p>no</p></td>
</tr>
<tr class="row-even"><td><p><em>TLSv1.1</em></p></td>
<td><p>no</p></td>
<td><p>no</p></td>
<td><p>yes</p></td>
<td><p>no</p></td>
<td><p>yes</p></td>
<td><p>no</p></td>
</tr>
<tr class="row-odd"><td><p><em>TLSv1.2</em></p></td>
<td><p>no</p></td>
<td><p>no</p></td>
<td><p>yes</p></td>
<td><p>no</p></td>
<td><p>no</p></td>
<td><p>yes</p></td>
</tr>
</tbody>
</table>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id7" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></span>
<span class="backrefs">(<a role="doc-backlink" href="#id2">1</a>,<a role="doc-backlink" href="#id5">2</a>)</span>
<p><a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> disables SSLv2 with <a class="reference internal" href="#ssl.OP_NO_SSLv2" title="ssl.OP_NO_SSLv2"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv2</span></code></a> by default.</p>
</aside>
<aside class="footnote brackets" id="id8" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></span>
<span class="backrefs">(<a role="doc-backlink" href="#id3">1</a>,<a role="doc-backlink" href="#id6">2</a>)</span>
<p><a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> disables SSLv3 with <a class="reference internal" href="#ssl.OP_NO_SSLv3" title="ssl.OP_NO_SSLv3"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv3</span></code></a> by default.</p>
</aside>
<aside class="footnote brackets" id="id9" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span>3<span class="fn-bracket">]</span></span>
<span class="backrefs">(<a role="doc-backlink" href="#id1">1</a>,<a role="doc-backlink" href="#id4">2</a>)</span>
<p>TLS 1.3 protocol will be available with <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a> in
OpenSSL &gt;= 1.1.1. There is no dedicated PROTOCOL constant for just
TLS 1.3.</p>
</aside>
</aside>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a> lets the <a class="reference internal" href="#module-ssl" title="ssl: TLS/SSL wrapper for socket objects"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code></a> module choose
security settings for a given purpose.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The context is created with secure default values. The options
<a class="reference internal" href="#ssl.OP_NO_COMPRESSION" title="ssl.OP_NO_COMPRESSION"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_COMPRESSION</span></code></a>, <a class="reference internal" href="#ssl.OP_CIPHER_SERVER_PREFERENCE" title="ssl.OP_CIPHER_SERVER_PREFERENCE"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_CIPHER_SERVER_PREFERENCE</span></code></a>,
<a class="reference internal" href="#ssl.OP_SINGLE_DH_USE" title="ssl.OP_SINGLE_DH_USE"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_SINGLE_DH_USE</span></code></a>, <a class="reference internal" href="#ssl.OP_SINGLE_ECDH_USE" title="ssl.OP_SINGLE_ECDH_USE"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_SINGLE_ECDH_USE</span></code></a>,
<a class="reference internal" href="#ssl.OP_NO_SSLv2" title="ssl.OP_NO_SSLv2"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv2</span></code></a>,
and <a class="reference internal" href="#ssl.OP_NO_SSLv3" title="ssl.OP_NO_SSLv3"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv3</span></code></a> (except for <a class="reference internal" href="#ssl.PROTOCOL_SSLv3" title="ssl.PROTOCOL_SSLv3"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_SSLv3</span></code></a>) are
set by default. The initial cipher suite list contains only <code class="docutils literal notranslate"><span class="pre">HIGH</span></code>
ciphers, no <code class="docutils literal notranslate"><span class="pre">NULL</span></code> ciphers and no <code class="docutils literal notranslate"><span class="pre">MD5</span></code> ciphers.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.10: </span><a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> without protocol argument is deprecated. The
context class will either require <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> or
<a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a> protocol in the future.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The default cipher suites now include only secure AES and ChaCha20
ciphers with forward secrecy and security level 2. RSA and DH keys with
less than 2048 bits and ECC keys with less than 224 bits are prohibited.
<a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>, <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a>, and
<a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a> use TLS 1.2 as minimum TLS version.</p>
</div>
</dd></dl>

<p><a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> objects have the following methods and attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.cert_store_stats">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">cert_store_stats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.cert_store_stats" title="Link to this definition">¶</a></dt>
<dd><p>Get statistics about quantities of loaded X.509 certificates, count of
X.509 certificates flagged as CA certificates and certificate revocation
lists as dictionary.</p>
<p>Example for a context with one CA cert and one other cert:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">context</span><span class="o">.</span><span class="n">cert_store_stats</span><span class="p">()</span>
<span class="go">{&#39;crl&#39;: 0, &#39;x509_ca&#39;: 1, &#39;x509&#39;: 2}</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.load_cert_chain">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">load_cert_chain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">certfile</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">keyfile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.load_cert_chain" title="Link to this definition">¶</a></dt>
<dd><p>Load a private key and the corresponding certificate.  The <em>certfile</em>
string must be the path to a single file in PEM format containing the
certificate as well as any number of CA certificates needed to establish
the certificate’s authenticity.  The <em>keyfile</em> string, if present, must
point to a file containing the private key.  Otherwise the private
key will be taken from <em>certfile</em> as well.  See the discussion of
<a class="reference internal" href="#ssl-certificates"><span class="std std-ref">Certificates</span></a> for more information on how the certificate
is stored in the <em>certfile</em>.</p>
<p>The <em>password</em> argument may be a function to call to get the password for
decrypting the private key.  It will only be called if the private key is
encrypted and a password is necessary.  It will be called with no arguments,
and it should return a string, bytes, or bytearray.  If the return value is
a string it will be encoded as UTF-8 before using it to decrypt the key.
Alternatively a string, bytes, or bytearray value may be supplied directly
as the <em>password</em> argument.  It will be ignored if the private key is not
encrypted and no password is needed.</p>
<p>If the <em>password</em> argument is not specified and a password is required,
OpenSSL’s built-in password prompting mechanism will be used to
interactively prompt the user for a password.</p>
<p>An <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLError</span></code></a> is raised if the private key doesn’t
match with the certificate.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>New optional argument <em>password</em>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.load_default_certs">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">load_default_certs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">purpose</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Purpose.SERVER_AUTH</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.load_default_certs" title="Link to this definition">¶</a></dt>
<dd><p>Load a set of default “certification authority” (CA) certificates from
default locations. On Windows it loads CA certs from the <code class="docutils literal notranslate"><span class="pre">CA</span></code> and
<code class="docutils literal notranslate"><span class="pre">ROOT</span></code> system stores. On all systems it calls
<a class="reference internal" href="#ssl.SSLContext.set_default_verify_paths" title="ssl.SSLContext.set_default_verify_paths"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_default_verify_paths()</span></code></a>. In the future the method may
load CA certificates from other locations, too.</p>
<p>The <em>purpose</em> flag specifies what kind of CA certificates are loaded. The
default settings <a class="reference internal" href="#ssl.Purpose.SERVER_AUTH" title="ssl.Purpose.SERVER_AUTH"><code class="xref py py-const docutils literal notranslate"><span class="pre">Purpose.SERVER_AUTH</span></code></a> loads certificates, that are
flagged and trusted for TLS web server authentication (client side
sockets). <a class="reference internal" href="#ssl.Purpose.CLIENT_AUTH" title="ssl.Purpose.CLIENT_AUTH"><code class="xref py py-const docutils literal notranslate"><span class="pre">Purpose.CLIENT_AUTH</span></code></a> loads CA certificates for client
certificate verification on the server side.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.load_verify_locations">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">load_verify_locations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cafile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capath</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cadata</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.load_verify_locations" title="Link to this definition">¶</a></dt>
<dd><p>Load a set of “certification authority” (CA) certificates used to validate
other peers’ certificates when <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-data docutils literal notranslate"><span class="pre">verify_mode</span></code></a> is other than
<a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a>.  At least one of <em>cafile</em> or <em>capath</em> must be specified.</p>
<p>This method can also load certification revocation lists (CRLs) in PEM or
DER format. In order to make use of CRLs, <a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a>
must be configured properly.</p>
<p>The <em>cafile</em> string, if present, is the path to a file of concatenated
CA certificates in PEM format. See the discussion of
<a class="reference internal" href="#ssl-certificates"><span class="std std-ref">Certificates</span></a> for more information about how to arrange the
certificates in this file.</p>
<p>The <em>capath</em> string, if present, is
the path to a directory containing several CA certificates in PEM format,
following an <a class="reference external" href="https://www.openssl.org/docs/manmaster/man3/SSL_CTX_load_verify_locations.html">OpenSSL specific layout</a>.</p>
<p>The <em>cadata</em> object, if present, is either an ASCII string of one or more
PEM-encoded certificates or a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> of DER-encoded
certificates. Like with <em>capath</em> extra lines around PEM-encoded
certificates are ignored but at least one certificate must be present.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>New optional argument <em>cadata</em></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.get_ca_certs">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">get_ca_certs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary_form</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.get_ca_certs" title="Link to this definition">¶</a></dt>
<dd><p>Get a list of loaded “certification authority” (CA) certificates. If the
<code class="docutils literal notranslate"><span class="pre">binary_form</span></code> parameter is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> each list
entry is a dict like the output of <a class="reference internal" href="#ssl.SSLSocket.getpeercert" title="ssl.SSLSocket.getpeercert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.getpeercert()</span></code></a>. Otherwise
the method returns a list of DER-encoded certificates. The returned list
does not contain certificates from <em>capath</em> unless a certificate was
requested and loaded by a SSL connection.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Certificates in a capath directory aren’t loaded unless they have
been used at least once.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.get_ciphers">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">get_ciphers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.get_ciphers" title="Link to this definition">¶</a></dt>
<dd><p>Get a list of enabled ciphers. The list is in order of cipher priority.
See <a class="reference internal" href="#ssl.SSLContext.set_ciphers" title="ssl.SSLContext.set_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_ciphers()</span></code></a>.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ctx</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLContext</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">PROTOCOL_SSLv23</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ctx</span><span class="o">.</span><span class="n">set_ciphers</span><span class="p">(</span><span class="s1">&#39;ECDHE+AESGCM:!ECDSA&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ctx</span><span class="o">.</span><span class="n">get_ciphers</span><span class="p">()</span>
<span class="go">[{&#39;aead&#39;: True,</span>
<span class="go">  &#39;alg_bits&#39;: 256,</span>
<span class="go">  &#39;auth&#39;: &#39;auth-rsa&#39;,</span>
<span class="go">  &#39;description&#39;: &#39;ECDHE-RSA-AES256-GCM-SHA384 TLSv1.2 Kx=ECDH     Au=RSA  &#39;</span>
<span class="go">                 &#39;Enc=AESGCM(256) Mac=AEAD&#39;,</span>
<span class="go">  &#39;digest&#39;: None,</span>
<span class="go">  &#39;id&#39;: 50380848,</span>
<span class="go">  &#39;kea&#39;: &#39;kx-ecdhe&#39;,</span>
<span class="go">  &#39;name&#39;: &#39;ECDHE-RSA-AES256-GCM-SHA384&#39;,</span>
<span class="go">  &#39;protocol&#39;: &#39;TLSv1.2&#39;,</span>
<span class="go">  &#39;strength_bits&#39;: 256,</span>
<span class="go">  &#39;symmetric&#39;: &#39;aes-256-gcm&#39;},</span>
<span class="go"> {&#39;aead&#39;: True,</span>
<span class="go">  &#39;alg_bits&#39;: 128,</span>
<span class="go">  &#39;auth&#39;: &#39;auth-rsa&#39;,</span>
<span class="go">  &#39;description&#39;: &#39;ECDHE-RSA-AES128-GCM-SHA256 TLSv1.2 Kx=ECDH     Au=RSA  &#39;</span>
<span class="go">                 &#39;Enc=AESGCM(128) Mac=AEAD&#39;,</span>
<span class="go">  &#39;digest&#39;: None,</span>
<span class="go">  &#39;id&#39;: 50380847,</span>
<span class="go">  &#39;kea&#39;: &#39;kx-ecdhe&#39;,</span>
<span class="go">  &#39;name&#39;: &#39;ECDHE-RSA-AES128-GCM-SHA256&#39;,</span>
<span class="go">  &#39;protocol&#39;: &#39;TLSv1.2&#39;,</span>
<span class="go">  &#39;strength_bits&#39;: 128,</span>
<span class="go">  &#39;symmetric&#39;: &#39;aes-128-gcm&#39;}]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.set_default_verify_paths">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">set_default_verify_paths</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.set_default_verify_paths" title="Link to this definition">¶</a></dt>
<dd><p>Load a set of default “certification authority” (CA) certificates from
a filesystem path defined when building the OpenSSL library.  Unfortunately,
there’s no easy way to know whether this method succeeds: no error is
returned if no certificates are to be found.  When the OpenSSL library is
provided as part of the operating system, though, it is likely to be
configured properly.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.set_ciphers">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">set_ciphers</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ciphers</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.set_ciphers" title="Link to this definition">¶</a></dt>
<dd><p>Set the available ciphers for sockets created with this context.
It should be a string in the <a class="reference external" href="https://www.openssl.org/docs/manmaster/man1/ciphers.html">OpenSSL cipher list format</a>.
If no cipher can be selected (because compile-time options or other
configuration forbids use of all the specified ciphers), an
<a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLError</span></code></a> will be raised.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>when connected, the <a class="reference internal" href="#ssl.SSLSocket.cipher" title="ssl.SSLSocket.cipher"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.cipher()</span></code></a> method of SSL sockets will
give the currently selected cipher.</p>
<p>TLS 1.3 cipher suites cannot be disabled with
<a class="reference internal" href="#ssl.SSLContext.set_ciphers" title="ssl.SSLContext.set_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_ciphers()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.set_alpn_protocols">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">set_alpn_protocols</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">protocols</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.set_alpn_protocols" title="Link to this definition">¶</a></dt>
<dd><p>Specify which protocols the socket should advertise during the SSL/TLS
handshake. It should be a list of ASCII strings, like <code class="docutils literal notranslate"><span class="pre">['http/1.1',</span>
<span class="pre">'spdy/2']</span></code>, ordered by preference. The selection of a protocol will happen
during the handshake, and will play out according to <span class="target" id="index-12"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7301.html"><strong>RFC 7301</strong></a>. After a
successful handshake, the <a class="reference internal" href="#ssl.SSLSocket.selected_alpn_protocol" title="ssl.SSLSocket.selected_alpn_protocol"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.selected_alpn_protocol()</span></code></a> method will
return the agreed-upon protocol.</p>
<p>This method will raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> if <a class="reference internal" href="#ssl.HAS_ALPN" title="ssl.HAS_ALPN"><code class="xref py py-data docutils literal notranslate"><span class="pre">HAS_ALPN</span></code></a> is
<code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.set_npn_protocols">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">set_npn_protocols</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">protocols</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.set_npn_protocols" title="Link to this definition">¶</a></dt>
<dd><p>Specify which protocols the socket should advertise during the SSL/TLS
handshake. It should be a list of strings, like <code class="docutils literal notranslate"><span class="pre">['http/1.1',</span> <span class="pre">'spdy/2']</span></code>,
ordered by preference. The selection of a protocol will happen during the
handshake, and will play out according to the <a class="reference external" href="https://en.wikipedia.org/wiki/Application-Layer_Protocol_Negotiation">Application Layer Protocol Negotiation</a>. After a
successful handshake, the <a class="reference internal" href="#ssl.SSLSocket.selected_npn_protocol" title="ssl.SSLSocket.selected_npn_protocol"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.selected_npn_protocol()</span></code></a> method will
return the agreed-upon protocol.</p>
<p>This method will raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> if <a class="reference internal" href="#ssl.HAS_NPN" title="ssl.HAS_NPN"><code class="xref py py-data docutils literal notranslate"><span class="pre">HAS_NPN</span></code></a> is
<code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.10: </span>NPN has been superseded by ALPN</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.sni_callback">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">sni_callback</span></span><a class="headerlink" href="#ssl.SSLContext.sni_callback" title="Link to this definition">¶</a></dt>
<dd><p>Register a callback function that will be called after the TLS Client Hello
handshake message has been received by the SSL/TLS server when the TLS client
specifies a server name indication. The server name indication mechanism
is specified in <span class="target" id="index-13"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6066.html"><strong>RFC 6066</strong></a> section 3 - Server Name Indication.</p>
<p>Only one callback can be set per <code class="docutils literal notranslate"><span class="pre">SSLContext</span></code>.  If <em>sni_callback</em>
is set to <code class="docutils literal notranslate"><span class="pre">None</span></code> then the callback is disabled. Calling this function a
subsequent time will disable the previously registered callback.</p>
<p>The callback function will be called with three
arguments; the first being the <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLSocket</span></code></a>, the second is a string
that represents the server name that the client is intending to communicate
(or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if the TLS Client Hello does not contain a server name)
and the third argument is the original <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a>. The server name
argument is text. For internationalized domain name, the server
name is an IDN A-label (<code class="docutils literal notranslate"><span class="pre">&quot;xn--pythn-mua.org&quot;</span></code>).</p>
<p>A typical use of this callback is to change the <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLSocket</span></code></a>’s
<a class="reference internal" href="#ssl.SSLSocket.context" title="ssl.SSLSocket.context"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLSocket.context</span></code></a> attribute to a new object of type
<a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> representing a certificate chain that matches the server
name.</p>
<p>Due to the early negotiation phase of the TLS connection, only limited
methods and attributes are usable like
<a class="reference internal" href="#ssl.SSLSocket.selected_alpn_protocol" title="ssl.SSLSocket.selected_alpn_protocol"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.selected_alpn_protocol()</span></code></a> and <a class="reference internal" href="#ssl.SSLSocket.context" title="ssl.SSLSocket.context"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLSocket.context</span></code></a>.
The <a class="reference internal" href="#ssl.SSLSocket.getpeercert" title="ssl.SSLSocket.getpeercert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.getpeercert()</span></code></a>,
<a class="reference internal" href="#ssl.SSLSocket.cipher" title="ssl.SSLSocket.cipher"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.cipher()</span></code></a> and <a class="reference internal" href="#ssl.SSLSocket.compression" title="ssl.SSLSocket.compression"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.compression()</span></code></a> methods require that
the TLS connection has progressed beyond the TLS Client Hello and therefore
will not return meaningful values nor can they be called safely.</p>
<p>The <em>sni_callback</em> function must return <code class="docutils literal notranslate"><span class="pre">None</span></code> to allow the
TLS negotiation to continue.  If a TLS failure is required, a constant
<a class="reference internal" href="#ssl.ALERT_DESCRIPTION_INTERNAL_ERROR" title="ssl.ALERT_DESCRIPTION_INTERNAL_ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ALERT_DESCRIPTION_*</span></code></a> can be
returned.  Other return values will result in a TLS fatal error with
<a class="reference internal" href="#ssl.ALERT_DESCRIPTION_INTERNAL_ERROR" title="ssl.ALERT_DESCRIPTION_INTERNAL_ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ALERT_DESCRIPTION_INTERNAL_ERROR</span></code></a>.</p>
<p>If an exception is raised from the <em>sni_callback</em> function the TLS
connection will terminate with a fatal TLS alert message
<a class="reference internal" href="#ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE" title="ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE"><code class="xref py py-const docutils literal notranslate"><span class="pre">ALERT_DESCRIPTION_HANDSHAKE_FAILURE</span></code></a>.</p>
<p>This method will raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> if the OpenSSL library
had OPENSSL_NO_TLSEXT defined when it was built.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.set_servername_callback">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">set_servername_callback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_name_callback</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.set_servername_callback" title="Link to this definition">¶</a></dt>
<dd><p>This is a legacy API retained for backwards compatibility. When possible,
you should use <a class="reference internal" href="#ssl.SSLContext.sni_callback" title="ssl.SSLContext.sni_callback"><code class="xref py py-attr docutils literal notranslate"><span class="pre">sni_callback</span></code></a> instead. The given <em>server_name_callback</em>
is similar to <em>sni_callback</em>, except that when the server hostname is an
IDN-encoded internationalized domain name, the <em>server_name_callback</em>
receives a decoded U-label (<code class="docutils literal notranslate"><span class="pre">&quot;pythön.org&quot;</span></code>).</p>
<p>If there is an decoding error on the server name, the TLS connection will
terminate with an <a class="reference internal" href="#ssl.ALERT_DESCRIPTION_INTERNAL_ERROR" title="ssl.ALERT_DESCRIPTION_INTERNAL_ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ALERT_DESCRIPTION_INTERNAL_ERROR</span></code></a> fatal TLS
alert message to the client.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.load_dh_params">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">load_dh_params</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dhfile</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.load_dh_params" title="Link to this definition">¶</a></dt>
<dd><p>Load the key generation parameters for Diffie-Hellman (DH) key exchange.
Using DH key exchange improves forward secrecy at the expense of
computational resources (both on the server and on the client).
The <em>dhfile</em> parameter should be the path to a file containing DH
parameters in PEM format.</p>
<p>This setting doesn’t apply to client sockets.  You can also use the
<a class="reference internal" href="#ssl.OP_SINGLE_DH_USE" title="ssl.OP_SINGLE_DH_USE"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_SINGLE_DH_USE</span></code></a> option to further improve security.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.set_ecdh_curve">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">set_ecdh_curve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">curve_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.set_ecdh_curve" title="Link to this definition">¶</a></dt>
<dd><p>Set the curve name for Elliptic Curve-based Diffie-Hellman (ECDH) key
exchange.  ECDH is significantly faster than regular DH while arguably
as secure.  The <em>curve_name</em> parameter should be a string describing
a well-known elliptic curve, for example <code class="docutils literal notranslate"><span class="pre">prime256v1</span></code> for a widely
supported curve.</p>
<p>This setting doesn’t apply to client sockets.  You can also use the
<a class="reference internal" href="#ssl.OP_SINGLE_ECDH_USE" title="ssl.OP_SINGLE_ECDH_USE"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_SINGLE_ECDH_USE</span></code></a> option to further improve security.</p>
<p>This method is not available if <a class="reference internal" href="#ssl.HAS_ECDH" title="ssl.HAS_ECDH"><code class="xref py py-data docutils literal notranslate"><span class="pre">HAS_ECDH</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://vincent.bernat.ch/en/blog/2011-ssl-perfect-forward-secrecy">SSL/TLS &amp; Perfect Forward Secrecy</a></dt><dd><p>Vincent Bernat.</p>
</dd>
</dl>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.wrap_socket">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">wrap_socket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sock</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">do_handshake_on_connect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_ragged_eofs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server_hostname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">session</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.wrap_socket" title="Link to this definition">¶</a></dt>
<dd><p>Wrap an existing Python socket <em>sock</em> and return an instance of
<a class="reference internal" href="#ssl.SSLContext.sslsocket_class" title="ssl.SSLContext.sslsocket_class"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.sslsocket_class</span></code></a> (default <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>). The
returned SSL socket is tied to the context, its settings and certificates.
<em>sock</em> must be a <a class="reference internal" href="socket.html#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a> socket; other
socket types are unsupported.</p>
<p>The parameter <code class="docutils literal notranslate"><span class="pre">server_side</span></code> is a boolean which identifies whether
server-side or client-side behavior is desired from this socket.</p>
<p>For client-side sockets, the context construction is lazy; if the
underlying socket isn’t connected yet, the context construction will be
performed after <code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code> is called on the socket.  For
server-side sockets, if the socket has no remote peer, it is assumed
to be a listening socket, and the server-side SSL wrapping is
automatically performed on client connections accepted via the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code> method. The method may raise <a class="reference internal" href="#ssl.SSLError" title="ssl.SSLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLError</span></code></a>.</p>
<p>On client connections, the optional parameter <em>server_hostname</em> specifies
the hostname of the service which we are connecting to.  This allows a
single server to host multiple SSL-based services with distinct certificates,
quite similarly to HTTP virtual hosts. Specifying <em>server_hostname</em> will
raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>server_side</em> is true.</p>
<p>The parameter <code class="docutils literal notranslate"><span class="pre">do_handshake_on_connect</span></code> specifies whether to do the SSL
handshake automatically after doing a <code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.connect()</span></code>, or whether the
application program will call it explicitly, by invoking the
<a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.do_handshake()</span></code></a> method.  Calling
<a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.do_handshake()</span></code></a> explicitly gives the program control over the
blocking behavior of the socket I/O involved in the handshake.</p>
<p>The parameter <code class="docutils literal notranslate"><span class="pre">suppress_ragged_eofs</span></code> specifies how the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.recv()</span></code> method should signal unexpected EOF from the other end
of the connection.  If specified as <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> (the default), it returns a
normal EOF (an empty bytes object) in response to unexpected EOF errors
raised from the underlying socket; if <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>, it will raise the
exceptions back to the caller.</p>
<p><em>session</em>, see <a class="reference internal" href="#ssl.SSLSocket.session" title="ssl.SSLSocket.session"><code class="xref py py-attr docutils literal notranslate"><span class="pre">session</span></code></a>.</p>
<p>To wrap an <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> in another <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>, use
<a class="reference internal" href="#ssl.SSLContext.wrap_bio" title="ssl.SSLContext.wrap_bio"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_bio()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Always allow a server_hostname to be passed, even if OpenSSL does not
have SNI.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>session</em> argument was added.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The method returns an instance of <a class="reference internal" href="#ssl.SSLContext.sslsocket_class" title="ssl.SSLContext.sslsocket_class"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.sslsocket_class</span></code></a>
instead of hard-coded <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>.</p>
</div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.sslsocket_class">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">sslsocket_class</span></span><a class="headerlink" href="#ssl.SSLContext.sslsocket_class" title="Link to this definition">¶</a></dt>
<dd><p>The return type of <a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a>, defaults to
<a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>. The attribute can be overridden on instance of class
in order to return a custom subclass of <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.wrap_bio">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">wrap_bio</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">incoming</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outgoing</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server_hostname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">session</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.wrap_bio" title="Link to this definition">¶</a></dt>
<dd><p>Wrap the BIO objects <em>incoming</em> and <em>outgoing</em> and return an instance of
<a class="reference internal" href="#ssl.SSLContext.sslobject_class" title="ssl.SSLContext.sslobject_class"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.sslobject_class</span></code></a> (default <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a>). The SSL
routines will read input data from the incoming BIO and write data to the
outgoing BIO.</p>
<p>The <em>server_side</em>, <em>server_hostname</em> and <em>session</em> parameters have the
same meaning as in <a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>session</em> argument was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The method returns an instance of <a class="reference internal" href="#ssl.SSLContext.sslobject_class" title="ssl.SSLContext.sslobject_class"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.sslobject_class</span></code></a>
instead of hard-coded <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.sslobject_class">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">sslobject_class</span></span><a class="headerlink" href="#ssl.SSLContext.sslobject_class" title="Link to this definition">¶</a></dt>
<dd><p>The return type of <a class="reference internal" href="#ssl.SSLContext.wrap_bio" title="ssl.SSLContext.wrap_bio"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_bio()</span></code></a>, defaults to
<a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a>. The attribute can be overridden on instance of class
in order to return a custom subclass of <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.SSLContext.session_stats">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">session_stats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.SSLContext.session_stats" title="Link to this definition">¶</a></dt>
<dd><p>Get statistics about the SSL sessions created or managed by this context.
A dictionary is returned which maps the names of each <a class="reference external" href="https://www.openssl.org/docs/man1.1.1/man3/SSL_CTX_sess_number.html">piece of information</a> to their
numeric values.  For example, here is the total number of hits and misses
in the session cache since the context was created:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">stats</span> <span class="o">=</span> <span class="n">context</span><span class="o">.</span><span class="n">session_stats</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">stats</span><span class="p">[</span><span class="s1">&#39;hits&#39;</span><span class="p">],</span> <span class="n">stats</span><span class="p">[</span><span class="s1">&#39;misses&#39;</span><span class="p">]</span>
<span class="go">(0, 0)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.check_hostname">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">check_hostname</span></span><a class="headerlink" href="#ssl.SSLContext.check_hostname" title="Link to this definition">¶</a></dt>
<dd><p>Whether to match the peer cert’s hostname in
<a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.do_handshake()</span></code></a>. The context’s
<a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">verify_mode</span></code></a> must be set to <a class="reference internal" href="#ssl.CERT_OPTIONAL" title="ssl.CERT_OPTIONAL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_OPTIONAL</span></code></a> or
<a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>, and you must pass <em>server_hostname</em> to
<a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">wrap_socket()</span></code></a> in order to match the hostname.  Enabling
hostname checking automatically sets <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">verify_mode</span></code></a> from
<a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a> to <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>.  It cannot be set back to
<a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a> as long as hostname checking is enabled. The
<a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> protocol enables hostname checking by default.
With other protocols, hostname checking must be enabled explicitly.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span><span class="o">,</span> <span class="nn">ssl</span>

<span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLContext</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">PROTOCOL_TLSv1_2</span><span class="p">)</span>
<span class="n">context</span><span class="o">.</span><span class="n">verify_mode</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">CERT_REQUIRED</span>
<span class="n">context</span><span class="o">.</span><span class="n">check_hostname</span> <span class="o">=</span> <span class="kc">True</span>
<span class="n">context</span><span class="o">.</span><span class="n">load_default_certs</span><span class="p">()</span>

<span class="n">s</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">,</span> <span class="n">socket</span><span class="o">.</span><span class="n">SOCK_STREAM</span><span class="p">)</span>
<span class="n">ssl_sock</span> <span class="o">=</span> <span class="n">context</span><span class="o">.</span><span class="n">wrap_socket</span><span class="p">(</span><span class="n">s</span><span class="p">,</span> <span class="n">server_hostname</span><span class="o">=</span><span class="s1">&#39;www.verisign.com&#39;</span><span class="p">)</span>
<span class="n">ssl_sock</span><span class="o">.</span><span class="n">connect</span><span class="p">((</span><span class="s1">&#39;www.verisign.com&#39;</span><span class="p">,</span> <span class="mi">443</span><span class="p">))</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">verify_mode</span></code></a> is now automatically changed
to <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>  when hostname checking is enabled and
<a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">verify_mode</span></code></a> is <a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a>. Previously
the same operation would have failed with a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.keylog_filename">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">keylog_filename</span></span><a class="headerlink" href="#ssl.SSLContext.keylog_filename" title="Link to this definition">¶</a></dt>
<dd><p>Write TLS keys to a keylog file, whenever key material is generated or
received. The keylog file is designed for debugging purposes only. The
file format is specified by NSS and used by many traffic analyzers such
as Wireshark. The log file is opened in append-only mode. Writes are
synchronized between threads, but not between processes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.maximum_version">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">maximum_version</span></span><a class="headerlink" href="#ssl.SSLContext.maximum_version" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#ssl.TLSVersion" title="ssl.TLSVersion"><code class="xref py py-class docutils literal notranslate"><span class="pre">TLSVersion</span></code></a> enum member representing the highest supported
TLS version. The value defaults to <a class="reference internal" href="#ssl.TLSVersion.MAXIMUM_SUPPORTED" title="ssl.TLSVersion.MAXIMUM_SUPPORTED"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TLSVersion.MAXIMUM_SUPPORTED</span></code></a>.
The attribute is read-only for protocols other than <a class="reference internal" href="#ssl.PROTOCOL_TLS" title="ssl.PROTOCOL_TLS"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PROTOCOL_TLS</span></code></a>,
<a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a>, and <a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a>.</p>
<p>The attributes <a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">maximum_version</span></code></a>,
<a class="reference internal" href="#ssl.SSLContext.minimum_version" title="ssl.SSLContext.minimum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">minimum_version</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.options" title="ssl.SSLContext.options"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.options</span></code></a> all affect the supported SSL
and TLS versions of the context. The implementation does not prevent
invalid combination. For example a context with
<a class="reference internal" href="#ssl.OP_NO_TLSv1_2" title="ssl.OP_NO_TLSv1_2"><code class="xref py py-attr docutils literal notranslate"><span class="pre">OP_NO_TLSv1_2</span></code></a> in <a class="reference internal" href="#ssl.SSLContext.options" title="ssl.SSLContext.options"><code class="xref py py-attr docutils literal notranslate"><span class="pre">options</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">maximum_version</span></code></a> set to <a class="reference internal" href="#ssl.TLSVersion.TLSv1_2" title="ssl.TLSVersion.TLSv1_2"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TLSVersion.TLSv1_2</span></code></a>
will not be able to establish a TLS 1.2 connection.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.minimum_version">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">minimum_version</span></span><a class="headerlink" href="#ssl.SSLContext.minimum_version" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.maximum_version</span></code></a> except it is the lowest
supported version or <a class="reference internal" href="#ssl.TLSVersion.MINIMUM_SUPPORTED" title="ssl.TLSVersion.MINIMUM_SUPPORTED"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TLSVersion.MINIMUM_SUPPORTED</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.num_tickets">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">num_tickets</span></span><a class="headerlink" href="#ssl.SSLContext.num_tickets" title="Link to this definition">¶</a></dt>
<dd><p>Control the number of TLS 1.3 session tickets of a
<a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a> context. The setting has no impact on TLS
1.0 to 1.2 connections.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.options">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">options</span></span><a class="headerlink" href="#ssl.SSLContext.options" title="Link to this definition">¶</a></dt>
<dd><p>An integer representing the set of SSL options enabled on this context.
The default value is <a class="reference internal" href="#ssl.OP_ALL" title="ssl.OP_ALL"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_ALL</span></code></a>, but you can specify other options
such as <a class="reference internal" href="#ssl.OP_NO_SSLv2" title="ssl.OP_NO_SSLv2"><code class="xref py py-data docutils literal notranslate"><span class="pre">OP_NO_SSLv2</span></code></a> by ORing them together.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#ssl.SSLContext.options" title="ssl.SSLContext.options"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.options</span></code></a> returns <a class="reference internal" href="#ssl.Options" title="ssl.Options"><code class="xref py py-class docutils literal notranslate"><span class="pre">Options</span></code></a> flags:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">()</span><span class="o">.</span><span class="n">options</span>  
<span class="go">&lt;Options.OP_ALL|OP_NO_SSLv3|OP_NO_SSLv2|OP_NO_COMPRESSION: 2197947391&gt;</span>
</pre></div>
</div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>All <code class="docutils literal notranslate"><span class="pre">OP_NO_SSL*</span></code> and <code class="docutils literal notranslate"><span class="pre">OP_NO_TLS*</span></code> options have been deprecated since
Python 3.7. Use <a class="reference internal" href="#ssl.SSLContext.minimum_version" title="ssl.SSLContext.minimum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.minimum_version</span></code></a> and
<a class="reference internal" href="#ssl.SSLContext.maximum_version" title="ssl.SSLContext.maximum_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.maximum_version</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.post_handshake_auth">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">post_handshake_auth</span></span><a class="headerlink" href="#ssl.SSLContext.post_handshake_auth" title="Link to this definition">¶</a></dt>
<dd><p>Enable TLS 1.3 post-handshake client authentication. Post-handshake auth
is disabled by default and a server can only request a TLS client
certificate during the initial handshake. When enabled, a server may
request a TLS client certificate at any time after the handshake.</p>
<p>When enabled on client-side sockets, the client signals the server that
it supports post-handshake authentication.</p>
<p>When enabled on server-side sockets, <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_mode</span></code></a> must
be set to <a class="reference internal" href="#ssl.CERT_OPTIONAL" title="ssl.CERT_OPTIONAL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_OPTIONAL</span></code></a> or <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>, too. The
actual client cert exchange is delayed until
<a class="reference internal" href="#ssl.SSLSocket.verify_client_post_handshake" title="ssl.SSLSocket.verify_client_post_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.verify_client_post_handshake()</span></code></a> is called and some I/O is
performed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.protocol">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">protocol</span></span><a class="headerlink" href="#ssl.SSLContext.protocol" title="Link to this definition">¶</a></dt>
<dd><p>The protocol version chosen when constructing the context.  This attribute
is read-only.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.hostname_checks_common_name">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">hostname_checks_common_name</span></span><a class="headerlink" href="#ssl.SSLContext.hostname_checks_common_name" title="Link to this definition">¶</a></dt>
<dd><p>Whether <a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> falls back to verify the cert’s
subject common name in the absence of a subject alternative name
extension (default: true).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The flag had no effect with OpenSSL before version 1.1.1l. Python 3.8.9,
3.9.3, and 3.10 include workarounds for previous versions.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.security_level">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">security_level</span></span><a class="headerlink" href="#ssl.SSLContext.security_level" title="Link to this definition">¶</a></dt>
<dd><p>An integer representing the <a class="reference external" href="https://www.openssl.org/docs/manmaster/man3/SSL_CTX_get_security_level.html">security level</a>
for the context. This attribute is read-only.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.verify_flags">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">verify_flags</span></span><a class="headerlink" href="#ssl.SSLContext.verify_flags" title="Link to this definition">¶</a></dt>
<dd><p>The flags for certificate verification operations. You can set flags like
<a class="reference internal" href="#ssl.VERIFY_CRL_CHECK_LEAF" title="ssl.VERIFY_CRL_CHECK_LEAF"><code class="xref py py-data docutils literal notranslate"><span class="pre">VERIFY_CRL_CHECK_LEAF</span></code></a> by ORing them together. By default OpenSSL
does neither require nor verify certificate revocation lists (CRLs).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#ssl.SSLContext.verify_flags" title="ssl.SSLContext.verify_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_flags</span></code></a> returns <a class="reference internal" href="#ssl.VerifyFlags" title="ssl.VerifyFlags"><code class="xref py py-class docutils literal notranslate"><span class="pre">VerifyFlags</span></code></a> flags:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">()</span><span class="o">.</span><span class="n">verify_flags</span>  
<span class="go">&lt;VerifyFlags.VERIFY_X509_TRUSTED_FIRST: 32768&gt;</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLContext.verify_mode">
<span class="sig-prename descclassname"><span class="pre">SSLContext.</span></span><span class="sig-name descname"><span class="pre">verify_mode</span></span><a class="headerlink" href="#ssl.SSLContext.verify_mode" title="Link to this definition">¶</a></dt>
<dd><p>Whether to try to verify other peers’ certificates and how to behave
if verification fails.  This attribute must be one of
<a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a>, <a class="reference internal" href="#ssl.CERT_OPTIONAL" title="ssl.CERT_OPTIONAL"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_OPTIONAL</span></code></a> or <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.verify_mode</span></code></a> returns <a class="reference internal" href="#ssl.VerifyMode" title="ssl.VerifyMode"><code class="xref py py-class docutils literal notranslate"><span class="pre">VerifyMode</span></code></a> enum:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">()</span><span class="o">.</span><span class="n">verify_mode</span>  
<span class="go">&lt;VerifyMode.CERT_REQUIRED: 2&gt;</span>
</pre></div>
</div>
</div>
</dd></dl>

</section>
<section id="certificates">
<span id="ssl-certificates"></span><span id="index-15"></span><span id="index-14"></span><h2>Certificates<a class="headerlink" href="#certificates" title="Link to this heading">¶</a></h2>
<p>Certificates in general are part of a public-key / private-key system.  In this
system, each <em>principal</em>, (which may be a machine, or a person, or an
organization) is assigned a unique two-part encryption key.  One part of the key
is public, and is called the <em>public key</em>; the other part is kept secret, and is
called the <em>private key</em>.  The two parts are related, in that if you encrypt a
message with one of the parts, you can decrypt it with the other part, and
<strong>only</strong> with the other part.</p>
<p>A certificate contains information about two principals.  It contains the name
of a <em>subject</em>, and the subject’s public key.  It also contains a statement by a
second principal, the <em>issuer</em>, that the subject is who they claim to be, and
that this is indeed the subject’s public key.  The issuer’s statement is signed
with the issuer’s private key, which only the issuer knows.  However, anyone can
verify the issuer’s statement by finding the issuer’s public key, decrypting the
statement with it, and comparing it to the other information in the certificate.
The certificate also contains information about the time period over which it is
valid.  This is expressed as two fields, called “notBefore” and “notAfter”.</p>
<p>In the Python use of certificates, a client or server can use a certificate to
prove who they are.  The other side of a network connection can also be required
to produce a certificate, and that certificate can be validated to the
satisfaction of the client or server that requires such validation.  The
connection attempt can be set to raise an exception if the validation fails.
Validation is done automatically, by the underlying OpenSSL framework; the
application need not concern itself with its mechanics.  But the application
does usually need to provide sets of certificates to allow this process to take
place.</p>
<p>Python uses files to contain certificates.  They should be formatted as “PEM”
(see <span class="target" id="index-16"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1422.html"><strong>RFC 1422</strong></a>), which is a base-64 encoded form wrapped with a header line
and a footer line:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-----</span><span class="n">BEGIN</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">...</span> <span class="p">(</span><span class="n">certificate</span> <span class="ow">in</span> <span class="n">base64</span> <span class="n">PEM</span> <span class="n">encoding</span><span class="p">)</span> <span class="o">...</span>
<span class="o">-----</span><span class="n">END</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
</pre></div>
</div>
<section id="certificate-chains">
<h3>Certificate chains<a class="headerlink" href="#certificate-chains" title="Link to this heading">¶</a></h3>
<p>The Python files which contain certificates can contain a sequence of
certificates, sometimes called a <em>certificate chain</em>.  This chain should start
with the specific certificate for the principal who “is” the client or server,
and then the certificate for the issuer of that certificate, and then the
certificate for the issuer of <em>that</em> certificate, and so on up the chain till
you get to a certificate which is <em>self-signed</em>, that is, a certificate which
has the same subject and issuer, sometimes called a <em>root certificate</em>.  The
certificates should just be concatenated together in the certificate file.  For
example, suppose we had a three certificate chain, from our server certificate
to the certificate of the certification authority that signed our server
certificate, to the root certificate of the agency which issued the
certification authority’s certificate:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-----</span><span class="n">BEGIN</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">...</span> <span class="p">(</span><span class="n">certificate</span> <span class="k">for</span> <span class="n">your</span> <span class="n">server</span><span class="p">)</span><span class="o">...</span>
<span class="o">-----</span><span class="n">END</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">-----</span><span class="n">BEGIN</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">...</span> <span class="p">(</span><span class="n">the</span> <span class="n">certificate</span> <span class="k">for</span> <span class="n">the</span> <span class="n">CA</span><span class="p">)</span><span class="o">...</span>
<span class="o">-----</span><span class="n">END</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">-----</span><span class="n">BEGIN</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">...</span> <span class="p">(</span><span class="n">the</span> <span class="n">root</span> <span class="n">certificate</span> <span class="k">for</span> <span class="n">the</span> <span class="n">CA</span><span class="s1">&#39;s issuer)...</span>
<span class="o">-----</span><span class="n">END</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
</pre></div>
</div>
</section>
<section id="ca-certificates">
<h3>CA certificates<a class="headerlink" href="#ca-certificates" title="Link to this heading">¶</a></h3>
<p>If you are going to require validation of the other side of the connection’s
certificate, you need to provide a “CA certs” file, filled with the certificate
chains for each issuer you are willing to trust.  Again, this file just contains
these chains concatenated together.  For validation, Python will use the first
chain it finds in the file which matches.  The platform’s certificates file can
be used by calling <a class="reference internal" href="#ssl.SSLContext.load_default_certs" title="ssl.SSLContext.load_default_certs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_default_certs()</span></code></a>, this is done
automatically with <a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a>.</p>
</section>
<section id="combined-key-and-certificate">
<h3>Combined key and certificate<a class="headerlink" href="#combined-key-and-certificate" title="Link to this heading">¶</a></h3>
<p>Often the private key is stored in the same file as the certificate; in this
case, only the <code class="docutils literal notranslate"><span class="pre">certfile</span></code> parameter to <a class="reference internal" href="#ssl.SSLContext.load_cert_chain" title="ssl.SSLContext.load_cert_chain"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_cert_chain()</span></code></a>
needs to be passed.  If the private key is stored
with the certificate, it should come before the first certificate in
the certificate chain:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-----</span><span class="n">BEGIN</span> <span class="n">RSA</span> <span class="n">PRIVATE</span> <span class="n">KEY</span><span class="o">-----</span>
<span class="o">...</span> <span class="p">(</span><span class="n">private</span> <span class="n">key</span> <span class="ow">in</span> <span class="n">base64</span> <span class="n">encoding</span><span class="p">)</span> <span class="o">...</span>
<span class="o">-----</span><span class="n">END</span> <span class="n">RSA</span> <span class="n">PRIVATE</span> <span class="n">KEY</span><span class="o">-----</span>
<span class="o">-----</span><span class="n">BEGIN</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
<span class="o">...</span> <span class="p">(</span><span class="n">certificate</span> <span class="ow">in</span> <span class="n">base64</span> <span class="n">PEM</span> <span class="n">encoding</span><span class="p">)</span> <span class="o">...</span>
<span class="o">-----</span><span class="n">END</span> <span class="n">CERTIFICATE</span><span class="o">-----</span>
</pre></div>
</div>
</section>
<section id="self-signed-certificates">
<h3>Self-signed certificates<a class="headerlink" href="#self-signed-certificates" title="Link to this heading">¶</a></h3>
<p>If you are going to create a server that provides SSL-encrypted connection
services, you will need to acquire a certificate for that service.  There are
many ways of acquiring appropriate certificates, such as buying one from a
certification authority.  Another common practice is to generate a self-signed
certificate.  The simplest way to do this is with the OpenSSL package, using
something like the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">%</span> <span class="n">openssl</span> <span class="n">req</span> <span class="o">-</span><span class="n">new</span> <span class="o">-</span><span class="n">x509</span> <span class="o">-</span><span class="n">days</span> <span class="mi">365</span> <span class="o">-</span><span class="n">nodes</span> <span class="o">-</span><span class="n">out</span> <span class="n">cert</span><span class="o">.</span><span class="n">pem</span> <span class="o">-</span><span class="n">keyout</span> <span class="n">cert</span><span class="o">.</span><span class="n">pem</span>
<span class="n">Generating</span> <span class="n">a</span> <span class="mi">1024</span> <span class="n">bit</span> <span class="n">RSA</span> <span class="n">private</span> <span class="n">key</span>
<span class="o">.......++++++</span>
<span class="o">.............................++++++</span>
<span class="n">writing</span> <span class="n">new</span> <span class="n">private</span> <span class="n">key</span> <span class="n">to</span> <span class="s1">&#39;cert.pem&#39;</span>
<span class="o">-----</span>
<span class="n">You</span> <span class="n">are</span> <span class="n">about</span> <span class="n">to</span> <span class="n">be</span> <span class="n">asked</span> <span class="n">to</span> <span class="n">enter</span> <span class="n">information</span> <span class="n">that</span> <span class="n">will</span> <span class="n">be</span> <span class="n">incorporated</span>
<span class="n">into</span> <span class="n">your</span> <span class="n">certificate</span> <span class="n">request</span><span class="o">.</span>
<span class="n">What</span> <span class="n">you</span> <span class="n">are</span> <span class="n">about</span> <span class="n">to</span> <span class="n">enter</span> <span class="ow">is</span> <span class="n">what</span> <span class="ow">is</span> <span class="n">called</span> <span class="n">a</span> <span class="n">Distinguished</span> <span class="n">Name</span> <span class="ow">or</span> <span class="n">a</span> <span class="n">DN</span><span class="o">.</span>
<span class="n">There</span> <span class="n">are</span> <span class="n">quite</span> <span class="n">a</span> <span class="n">few</span> <span class="n">fields</span> <span class="n">but</span> <span class="n">you</span> <span class="n">can</span> <span class="n">leave</span> <span class="n">some</span> <span class="n">blank</span>
<span class="n">For</span> <span class="n">some</span> <span class="n">fields</span> <span class="n">there</span> <span class="n">will</span> <span class="n">be</span> <span class="n">a</span> <span class="n">default</span> <span class="n">value</span><span class="p">,</span>
<span class="n">If</span> <span class="n">you</span> <span class="n">enter</span> <span class="s1">&#39;.&#39;</span><span class="p">,</span> <span class="n">the</span> <span class="n">field</span> <span class="n">will</span> <span class="n">be</span> <span class="n">left</span> <span class="n">blank</span><span class="o">.</span>
<span class="o">-----</span>
<span class="n">Country</span> <span class="n">Name</span> <span class="p">(</span><span class="mi">2</span> <span class="n">letter</span> <span class="n">code</span><span class="p">)</span> <span class="p">[</span><span class="n">AU</span><span class="p">]:</span><span class="n">US</span>
<span class="n">State</span> <span class="ow">or</span> <span class="n">Province</span> <span class="n">Name</span> <span class="p">(</span><span class="n">full</span> <span class="n">name</span><span class="p">)</span> <span class="p">[</span><span class="n">Some</span><span class="o">-</span><span class="n">State</span><span class="p">]:</span><span class="n">MyState</span>
<span class="n">Locality</span> <span class="n">Name</span> <span class="p">(</span><span class="n">eg</span><span class="p">,</span> <span class="n">city</span><span class="p">)</span> <span class="p">[]:</span><span class="n">Some</span> <span class="n">City</span>
<span class="n">Organization</span> <span class="n">Name</span> <span class="p">(</span><span class="n">eg</span><span class="p">,</span> <span class="n">company</span><span class="p">)</span> <span class="p">[</span><span class="n">Internet</span> <span class="n">Widgits</span> <span class="n">Pty</span> <span class="n">Ltd</span><span class="p">]:</span><span class="n">My</span> <span class="n">Organization</span><span class="p">,</span> <span class="n">Inc</span><span class="o">.</span>
<span class="n">Organizational</span> <span class="n">Unit</span> <span class="n">Name</span> <span class="p">(</span><span class="n">eg</span><span class="p">,</span> <span class="n">section</span><span class="p">)</span> <span class="p">[]:</span><span class="n">My</span> <span class="n">Group</span>
<span class="n">Common</span> <span class="n">Name</span> <span class="p">(</span><span class="n">eg</span><span class="p">,</span> <span class="n">YOUR</span> <span class="n">name</span><span class="p">)</span> <span class="p">[]:</span><span class="n">myserver</span><span class="o">.</span><span class="n">mygroup</span><span class="o">.</span><span class="n">myorganization</span><span class="o">.</span><span class="n">com</span>
<span class="n">Email</span> <span class="n">Address</span> <span class="p">[]:</span><span class="n">ops</span><span class="nd">@myserver</span><span class="o">.</span><span class="n">mygroup</span><span class="o">.</span><span class="n">myorganization</span><span class="o">.</span><span class="n">com</span>
<span class="o">%</span>
</pre></div>
</div>
<p>The disadvantage of a self-signed certificate is that it is its own root
certificate, and no one else will have it in their cache of known (and trusted)
root certificates.</p>
</section>
</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<section id="testing-for-ssl-support">
<h3>Testing for SSL support<a class="headerlink" href="#testing-for-ssl-support" title="Link to this heading">¶</a></h3>
<p>To test for the presence of SSL support in a Python installation, user code
should use the following idiom:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="p">:</span>
    <span class="kn">import</span> <span class="nn">ssl</span>
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="k">pass</span>
<span class="k">else</span><span class="p">:</span>
    <span class="o">...</span>  <span class="c1"># do something that requires SSL support</span>
</pre></div>
</div>
</section>
<section id="client-side-operation">
<h3>Client-side operation<a class="headerlink" href="#client-side-operation" title="Link to this heading">¶</a></h3>
<p>This example creates a SSL context with the recommended security settings
for client sockets, including automatic certificate verification:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">()</span>
</pre></div>
</div>
<p>If you prefer to tune security settings yourself, you might create
a context from scratch (but beware that you might not get the settings
right):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLContext</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">PROTOCOL_TLS_CLIENT</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">context</span><span class="o">.</span><span class="n">load_verify_locations</span><span class="p">(</span><span class="s2">&quot;/etc/ssl/certs/ca-bundle.crt&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>(this snippet assumes your operating system places a bundle of all CA
certificates in <code class="docutils literal notranslate"><span class="pre">/etc/ssl/certs/ca-bundle.crt</span></code>; if not, you’ll get an
error and have to adjust the location)</p>
<p>The <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-data docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> protocol configures the context for cert
validation and hostname verification. <a class="reference internal" href="#ssl.SSLContext.verify_mode" title="ssl.SSLContext.verify_mode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">verify_mode</span></code></a> is
set to <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-data docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a> and <a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> is set
to <code class="docutils literal notranslate"><span class="pre">True</span></code>. All other protocols create SSL contexts with insecure defaults.</p>
<p>When you use the context to connect to a server, <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>
and <a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">check_hostname</span></code></a> validate the server certificate: it
ensures that the server certificate was signed with one of the CA
certificates, checks the signature for correctness, and verifies other
properties like validity and identity of the hostname:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">context</span><span class="o">.</span><span class="n">wrap_socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">AF_INET</span><span class="p">),</span>
<span class="gp">... </span>                           <span class="n">server_hostname</span><span class="o">=</span><span class="s2">&quot;www.python.org&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">connect</span><span class="p">((</span><span class="s2">&quot;www.python.org&quot;</span><span class="p">,</span> <span class="mi">443</span><span class="p">))</span>
</pre></div>
</div>
<p>You may then fetch the certificate:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">cert</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getpeercert</span><span class="p">()</span>
</pre></div>
</div>
<p>Visual inspection shows that the certificate does identify the desired service
(that is, the HTTPS host <code class="docutils literal notranslate"><span class="pre">www.python.org</span></code>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">pprint</span><span class="p">(</span><span class="n">cert</span><span class="p">)</span>
<span class="go">{&#39;OCSP&#39;: (&#39;http://ocsp.digicert.com&#39;,),</span>
<span class="go"> &#39;caIssuers&#39;: (&#39;http://cacerts.digicert.com/DigiCertSHA2ExtendedValidationServerCA.crt&#39;,),</span>
<span class="go"> &#39;crlDistributionPoints&#39;: (&#39;http://crl3.digicert.com/sha2-ev-server-g1.crl&#39;,</span>
<span class="go">                           &#39;http://crl4.digicert.com/sha2-ev-server-g1.crl&#39;),</span>
<span class="go"> &#39;issuer&#39;: (((&#39;countryName&#39;, &#39;US&#39;),),</span>
<span class="go">            ((&#39;organizationName&#39;, &#39;DigiCert Inc&#39;),),</span>
<span class="go">            ((&#39;organizationalUnitName&#39;, &#39;www.digicert.com&#39;),),</span>
<span class="go">            ((&#39;commonName&#39;, &#39;DigiCert SHA2 Extended Validation Server CA&#39;),)),</span>
<span class="go"> &#39;notAfter&#39;: &#39;Sep  9 12:00:00 2016 GMT&#39;,</span>
<span class="go"> &#39;notBefore&#39;: &#39;Sep  5 00:00:00 2014 GMT&#39;,</span>
<span class="go"> &#39;serialNumber&#39;: &#39;01BB6F00122B177F36CAB49CEA8B6B26&#39;,</span>
<span class="go"> &#39;subject&#39;: (((&#39;businessCategory&#39;, &#39;Private Organization&#39;),),</span>
<span class="go">             ((&#39;*******.4.1.311.********&#39;, &#39;US&#39;),),</span>
<span class="go">             ((&#39;*******.4.1.311.********&#39;, &#39;Delaware&#39;),),</span>
<span class="go">             ((&#39;serialNumber&#39;, &#39;3359300&#39;),),</span>
<span class="go">             ((&#39;streetAddress&#39;, &#39;16 Allen Rd&#39;),),</span>
<span class="go">             ((&#39;postalCode&#39;, &#39;03894-4801&#39;),),</span>
<span class="go">             ((&#39;countryName&#39;, &#39;US&#39;),),</span>
<span class="go">             ((&#39;stateOrProvinceName&#39;, &#39;NH&#39;),),</span>
<span class="go">             ((&#39;localityName&#39;, &#39;Wolfeboro&#39;),),</span>
<span class="go">             ((&#39;organizationName&#39;, &#39;Python Software Foundation&#39;),),</span>
<span class="go">             ((&#39;commonName&#39;, &#39;www.python.org&#39;),)),</span>
<span class="go"> &#39;subjectAltName&#39;: ((&#39;DNS&#39;, &#39;www.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;pypi.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;docs.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;testpypi.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;bugs.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;wiki.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;hg.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;mail.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;packaging.python.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;pythonhosted.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;www.pythonhosted.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;test.pythonhosted.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;us.pycon.org&#39;),</span>
<span class="go">                    (&#39;DNS&#39;, &#39;id.python.org&#39;)),</span>
<span class="go"> &#39;version&#39;: 3}</span>
</pre></div>
</div>
<p>Now the SSL channel is established and the certificate verified, you can
proceed to talk with the server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">sendall</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;HEAD / HTTP/1.0</span><span class="se">\r\n</span><span class="s2">Host: linuxfr.org</span><span class="se">\r\n\r\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">pprint</span><span class="p">(</span><span class="n">conn</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;</span><span class="se">\r\n</span><span class="s2">&quot;</span><span class="p">))</span>
<span class="go">[b&#39;HTTP/1.1 200 OK&#39;,</span>
<span class="go"> b&#39;Date: Sat, 18 Oct 2014 18:27:20 GMT&#39;,</span>
<span class="go"> b&#39;Server: nginx&#39;,</span>
<span class="go"> b&#39;Content-Type: text/html; charset=utf-8&#39;,</span>
<span class="go"> b&#39;X-Frame-Options: SAMEORIGIN&#39;,</span>
<span class="go"> b&#39;Content-Length: 45679&#39;,</span>
<span class="go"> b&#39;Accept-Ranges: bytes&#39;,</span>
<span class="go"> b&#39;Via: 1.1 varnish&#39;,</span>
<span class="go"> b&#39;Age: 2188&#39;,</span>
<span class="go"> b&#39;X-Served-By: cache-lcy1134-LCY&#39;,</span>
<span class="go"> b&#39;X-Cache: HIT&#39;,</span>
<span class="go"> b&#39;X-Cache-Hits: 11&#39;,</span>
<span class="go"> b&#39;Vary: Cookie&#39;,</span>
<span class="go"> b&#39;Strict-Transport-Security: max-age=63072000; includeSubDomains&#39;,</span>
<span class="go"> b&#39;Connection: close&#39;,</span>
<span class="go"> b&#39;&#39;,</span>
<span class="go"> b&#39;&#39;]</span>
</pre></div>
</div>
<p>See the discussion of <a class="reference internal" href="#ssl-security"><span class="std std-ref">Security considerations</span></a> below.</p>
</section>
<section id="server-side-operation">
<h3>Server-side operation<a class="headerlink" href="#server-side-operation" title="Link to this heading">¶</a></h3>
<p>For server operation, typically you’ll need to have a server certificate, and
private key, each in a file.  You’ll first create a context holding the key
and the certificate, so that clients can check your authenticity.  Then
you’ll open a socket, bind it to a port, call <code class="xref py py-meth docutils literal notranslate"><span class="pre">listen()</span></code> on it, and start
waiting for clients to connect:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">socket</span><span class="o">,</span> <span class="nn">ssl</span>

<span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">Purpose</span><span class="o">.</span><span class="n">CLIENT_AUTH</span><span class="p">)</span>
<span class="n">context</span><span class="o">.</span><span class="n">load_cert_chain</span><span class="p">(</span><span class="n">certfile</span><span class="o">=</span><span class="s2">&quot;mycertfile&quot;</span><span class="p">,</span> <span class="n">keyfile</span><span class="o">=</span><span class="s2">&quot;mykeyfile&quot;</span><span class="p">)</span>

<span class="n">bindsocket</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">()</span>
<span class="n">bindsocket</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="s1">&#39;myaddr.example.com&#39;</span><span class="p">,</span> <span class="mi">10023</span><span class="p">))</span>
<span class="n">bindsocket</span><span class="o">.</span><span class="n">listen</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
</pre></div>
</div>
<p>When a client connects, you’ll call <code class="xref py py-meth docutils literal notranslate"><span class="pre">accept()</span></code> on the socket to get the
new socket from the other end, and use the context’s <a class="reference internal" href="#ssl.SSLContext.wrap_socket" title="ssl.SSLContext.wrap_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.wrap_socket()</span></code></a>
method to create a server-side SSL socket for the connection:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">newsocket</span><span class="p">,</span> <span class="n">fromaddr</span> <span class="o">=</span> <span class="n">bindsocket</span><span class="o">.</span><span class="n">accept</span><span class="p">()</span>
    <span class="n">connstream</span> <span class="o">=</span> <span class="n">context</span><span class="o">.</span><span class="n">wrap_socket</span><span class="p">(</span><span class="n">newsocket</span><span class="p">,</span> <span class="n">server_side</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">deal_with_client</span><span class="p">(</span><span class="n">connstream</span><span class="p">)</span>
    <span class="k">finally</span><span class="p">:</span>
        <span class="n">connstream</span><span class="o">.</span><span class="n">shutdown</span><span class="p">(</span><span class="n">socket</span><span class="o">.</span><span class="n">SHUT_RDWR</span><span class="p">)</span>
        <span class="n">connstream</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>Then you’ll read data from the <code class="docutils literal notranslate"><span class="pre">connstream</span></code> and do something with it till you
are finished with the client (or the client is finished with you):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">deal_with_client</span><span class="p">(</span><span class="n">connstream</span><span class="p">):</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">connstream</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span>
    <span class="c1"># empty data means the client is finished with us</span>
    <span class="k">while</span> <span class="n">data</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">do_something</span><span class="p">(</span><span class="n">connstream</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
            <span class="c1"># we&#39;ll assume do_something returns False</span>
            <span class="c1"># when we&#39;re finished with client</span>
            <span class="k">break</span>
        <span class="n">data</span> <span class="o">=</span> <span class="n">connstream</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1024</span><span class="p">)</span>
    <span class="c1"># finished with client</span>
</pre></div>
</div>
<p>And go back to listening for new client connections (of course, a real server
would probably handle each client connection in a separate thread, or put
the sockets in <a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking mode</span></a> and use an event loop).</p>
</section>
</section>
<section id="notes-on-non-blocking-sockets">
<span id="ssl-nonblocking"></span><h2>Notes on non-blocking sockets<a class="headerlink" href="#notes-on-non-blocking-sockets" title="Link to this heading">¶</a></h2>
<p>SSL sockets behave slightly different than regular sockets in
non-blocking mode. When working with non-blocking sockets, there are
thus several things you need to be aware of:</p>
<ul>
<li><p>Most <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> methods will raise either
<a class="reference internal" href="#ssl.SSLWantWriteError" title="ssl.SSLWantWriteError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantWriteError</span></code></a> or <a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a> instead of
<a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> if an I/O operation would
block. <a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a> will be raised if a read operation on
the underlying socket is necessary, and <a class="reference internal" href="#ssl.SSLWantWriteError" title="ssl.SSLWantWriteError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantWriteError</span></code></a> for
a write operation on the underlying socket. Note that attempts to
<em>write</em> to an SSL socket may require <em>reading</em> from the underlying
socket first, and attempts to <em>read</em> from the SSL socket may require
a prior <em>write</em> to the underlying socket.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>In earlier Python versions, the <code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.send()</span></code> method
returned zero instead of raising <a class="reference internal" href="#ssl.SSLWantWriteError" title="ssl.SSLWantWriteError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantWriteError</span></code></a> or
<a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a>.</p>
</div>
</li>
<li><p>Calling <a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select()</span></code></a> tells you that the OS-level socket can be
read from (or written to), but it does not imply that there is sufficient
data at the upper SSL layer.  For example, only part of an SSL frame might
have arrived.  Therefore, you must be ready to handle <code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.recv()</span></code>
and <code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.send()</span></code> failures, and retry after another call to
<a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select()</span></code></a>.</p></li>
<li><p>Conversely, since the SSL layer has its own framing, a SSL socket may
still have data available for reading without <a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select()</span></code></a>
being aware of it.  Therefore, you should first call
<code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.recv()</span></code> to drain any potentially available data, and then
only block on a <a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select()</span></code></a> call if still necessary.</p>
<p>(of course, similar provisions apply when using other primitives such as
<a class="reference internal" href="select.html#select.poll" title="select.poll"><code class="xref py py-func docutils literal notranslate"><span class="pre">poll()</span></code></a>, or those in the <a class="reference internal" href="selectors.html#module-selectors" title="selectors: High-level I/O multiplexing."><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code></a> module)</p>
</li>
<li><p>The SSL handshake itself will be non-blocking: the
<a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.do_handshake()</span></code></a> method has to be retried until it returns
successfully.  Here is a synopsis using <a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select()</span></code></a> to wait for
the socket’s readiness:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">sock</span><span class="o">.</span><span class="n">do_handshake</span><span class="p">()</span>
        <span class="k">break</span>
    <span class="k">except</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLWantReadError</span><span class="p">:</span>
        <span class="n">select</span><span class="o">.</span><span class="n">select</span><span class="p">([</span><span class="n">sock</span><span class="p">],</span> <span class="p">[],</span> <span class="p">[])</span>
    <span class="k">except</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLWantWriteError</span><span class="p">:</span>
        <span class="n">select</span><span class="o">.</span><span class="n">select</span><span class="p">([],</span> <span class="p">[</span><span class="n">sock</span><span class="p">],</span> <span class="p">[])</span>
</pre></div>
</div>
</li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The <a class="reference internal" href="asyncio.html#module-asyncio" title="asyncio: Asynchronous I/O."><code class="xref py py-mod docutils literal notranslate"><span class="pre">asyncio</span></code></a> module supports <a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking SSL sockets</span></a> and provides a
higher level API. It polls for events using the <a class="reference internal" href="selectors.html#module-selectors" title="selectors: High-level I/O multiplexing."><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code></a> module and
handles <a class="reference internal" href="#ssl.SSLWantWriteError" title="ssl.SSLWantWriteError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantWriteError</span></code></a>, <a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a> and
<a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> exceptions. It runs the SSL handshake asynchronously
as well.</p>
</div>
</section>
<section id="memory-bio-support">
<h2>Memory BIO Support<a class="headerlink" href="#memory-bio-support" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>Ever since the SSL module was introduced in Python 2.6, the <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>
class has provided two related but distinct areas of functionality:</p>
<ul class="simple">
<li><p>SSL protocol handling</p></li>
<li><p>Network IO</p></li>
</ul>
<p>The network IO API is identical to that provided by <a class="reference internal" href="socket.html#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket.socket</span></code></a>,
from which <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> also inherits. This allows an SSL socket to be
used as a drop-in replacement for a regular socket, making it very easy to add
SSL support to an existing application.</p>
<p>Combining SSL protocol handling and network IO usually works well, but there
are some cases where it doesn’t. An example is async IO frameworks that want to
use a different IO multiplexing model than the “select/poll on a file
descriptor” (readiness based) model that is assumed by <a class="reference internal" href="socket.html#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket.socket</span></code></a>
and by the internal OpenSSL socket IO routines. This is mostly relevant for
platforms like Windows where this model is not efficient. For this purpose, a
reduced scope variant of <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> called <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a> is
provided.</p>
<dl class="py class">
<dt class="sig sig-object py" id="ssl.SSLObject">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLObject</span></span><a class="headerlink" href="#ssl.SSLObject" title="Link to this definition">¶</a></dt>
<dd><p>A reduced-scope variant of <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> representing an SSL protocol
instance that does not contain any network IO methods. This class is
typically used by framework authors that want to implement asynchronous IO
for SSL through memory buffers.</p>
<p>This class implements an interface on top of a low-level SSL object as
implemented by OpenSSL. This object captures the state of an SSL connection
but does not provide any network IO itself. IO needs to be performed through
separate “BIO” objects which are OpenSSL’s IO abstraction layer.</p>
<p>This class has no public constructor.  An <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a> instance
must be created using the <a class="reference internal" href="#ssl.SSLContext.wrap_bio" title="ssl.SSLContext.wrap_bio"><code class="xref py py-meth docutils literal notranslate"><span class="pre">wrap_bio()</span></code></a> method. This
method will create the <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a> instance and bind it to a
pair of BIOs. The <em>incoming</em> BIO is used to pass data from Python to the
SSL protocol instance, while the <em>outgoing</em> BIO is used to pass data the
other way around.</p>
<p>The following methods are available:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#ssl.SSLSocket.context" title="ssl.SSLSocket.context"><code class="xref py py-attr docutils literal notranslate"><span class="pre">context</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.server_side" title="ssl.SSLSocket.server_side"><code class="xref py py-attr docutils literal notranslate"><span class="pre">server_side</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.server_hostname" title="ssl.SSLSocket.server_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">server_hostname</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.session" title="ssl.SSLSocket.session"><code class="xref py py-attr docutils literal notranslate"><span class="pre">session</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.session_reused" title="ssl.SSLSocket.session_reused"><code class="xref py py-attr docutils literal notranslate"><span class="pre">session_reused</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.read" title="ssl.SSLSocket.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.write" title="ssl.SSLSocket.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.getpeercert" title="ssl.SSLSocket.getpeercert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getpeercert()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.selected_alpn_protocol" title="ssl.SSLSocket.selected_alpn_protocol"><code class="xref py py-meth docutils literal notranslate"><span class="pre">selected_alpn_protocol()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.selected_npn_protocol" title="ssl.SSLSocket.selected_npn_protocol"><code class="xref py py-meth docutils literal notranslate"><span class="pre">selected_npn_protocol()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.cipher" title="ssl.SSLSocket.cipher"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cipher()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.shared_ciphers" title="ssl.SSLSocket.shared_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shared_ciphers()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.compression" title="ssl.SSLSocket.compression"><code class="xref py py-meth docutils literal notranslate"><span class="pre">compression()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.pending" title="ssl.SSLSocket.pending"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pending()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">do_handshake()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.verify_client_post_handshake" title="ssl.SSLSocket.verify_client_post_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">verify_client_post_handshake()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.unwrap" title="ssl.SSLSocket.unwrap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unwrap()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.get_channel_binding" title="ssl.SSLSocket.get_channel_binding"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_channel_binding()</span></code></a></p></li>
<li><p><a class="reference internal" href="#ssl.SSLSocket.version" title="ssl.SSLSocket.version"><code class="xref py py-meth docutils literal notranslate"><span class="pre">version()</span></code></a></p></li>
</ul>
<p>When compared to <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a>, this object lacks the following
features:</p>
<ul class="simple">
<li><p>Any form of network IO; <code class="docutils literal notranslate"><span class="pre">recv()</span></code> and <code class="docutils literal notranslate"><span class="pre">send()</span></code> read and write only to
the underlying <a class="reference internal" href="#ssl.MemoryBIO" title="ssl.MemoryBIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">MemoryBIO</span></code></a> buffers.</p></li>
<li><p>There is no <em>do_handshake_on_connect</em> machinery. You must always manually
call <a class="reference internal" href="#ssl.SSLSocket.do_handshake" title="ssl.SSLSocket.do_handshake"><code class="xref py py-meth docutils literal notranslate"><span class="pre">do_handshake()</span></code></a> to start the handshake.</p></li>
<li><p>There is no handling of <em>suppress_ragged_eofs</em>. All end-of-file conditions
that are in violation of the protocol are reported via the
<a class="reference internal" href="#ssl.SSLEOFError" title="ssl.SSLEOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLEOFError</span></code></a> exception.</p></li>
<li><p>The method <a class="reference internal" href="#ssl.SSLSocket.unwrap" title="ssl.SSLSocket.unwrap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unwrap()</span></code></a> call does not return anything,
unlike for an SSL socket where it returns the underlying socket.</p></li>
<li><p>The <em>server_name_callback</em> callback passed to
<a class="reference internal" href="#ssl.SSLContext.set_servername_callback" title="ssl.SSLContext.set_servername_callback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_servername_callback()</span></code></a> will get an <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a>
instance instead of a <a class="reference internal" href="#ssl.SSLSocket" title="ssl.SSLSocket"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSocket</span></code></a> instance as its first parameter.</p></li>
</ul>
<p>Some notes related to the use of <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a>:</p>
<ul class="simple">
<li><p>All IO on an <a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a> is <a class="reference internal" href="#ssl-nonblocking"><span class="std std-ref">non-blocking</span></a>.
This means that for example <a class="reference internal" href="#ssl.SSLSocket.read" title="ssl.SSLSocket.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> will raise an
<a class="reference internal" href="#ssl.SSLWantReadError" title="ssl.SSLWantReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SSLWantReadError</span></code></a> if it needs more data than the incoming BIO has
available.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#ssl.SSLObject" title="ssl.SSLObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLObject</span></code></a> instances must be created with
<a class="reference internal" href="#ssl.SSLContext.wrap_bio" title="ssl.SSLContext.wrap_bio"><code class="xref py py-meth docutils literal notranslate"><span class="pre">wrap_bio()</span></code></a>. In earlier versions, it was possible to
create instances directly. This was never documented or officially
supported.</p>
</div>
</dd></dl>

<p>An SSLObject communicates with the outside world using memory buffers. The
class <a class="reference internal" href="#ssl.MemoryBIO" title="ssl.MemoryBIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">MemoryBIO</span></code></a> provides a memory buffer that can be used for this
purpose.  It wraps an OpenSSL memory BIO (Basic IO) object:</p>
<dl class="py class">
<dt class="sig sig-object py" id="ssl.MemoryBIO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">MemoryBIO</span></span><a class="headerlink" href="#ssl.MemoryBIO" title="Link to this definition">¶</a></dt>
<dd><p>A memory buffer that can be used to pass data between Python and an SSL
protocol instance.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.MemoryBIO.pending">
<span class="sig-name descname"><span class="pre">pending</span></span><a class="headerlink" href="#ssl.MemoryBIO.pending" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of bytes currently in the memory buffer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.MemoryBIO.eof">
<span class="sig-name descname"><span class="pre">eof</span></span><a class="headerlink" href="#ssl.MemoryBIO.eof" title="Link to this definition">¶</a></dt>
<dd><p>A boolean indicating whether the memory BIO is current at the end-of-file
position.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.MemoryBIO.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.MemoryBIO.read" title="Link to this definition">¶</a></dt>
<dd><p>Read up to <em>n</em> bytes from the memory buffer. If <em>n</em> is not specified or
negative, all bytes are returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.MemoryBIO.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buf</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ssl.MemoryBIO.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the bytes from <em>buf</em> to the memory BIO. The <em>buf</em> argument must be an
object supporting the buffer protocol.</p>
<p>The return value is the number of bytes written, which is always equal to
the length of <em>buf</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ssl.MemoryBIO.write_eof">
<span class="sig-name descname"><span class="pre">write_eof</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ssl.MemoryBIO.write_eof" title="Link to this definition">¶</a></dt>
<dd><p>Write an EOF marker to the memory BIO. After this method has been called, it
is illegal to call <a class="reference internal" href="#ssl.MemoryBIO.write" title="ssl.MemoryBIO.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a>. The attribute <a class="reference internal" href="#ssl.MemoryBIO.eof" title="ssl.MemoryBIO.eof"><code class="xref py py-attr docutils literal notranslate"><span class="pre">eof</span></code></a> will
become true after all data currently in the buffer has been read.</p>
</dd></dl>

</dd></dl>

</section>
<section id="ssl-session">
<h2>SSL session<a class="headerlink" href="#ssl-session" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="ssl.SSLSession">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ssl.</span></span><span class="sig-name descname"><span class="pre">SSLSession</span></span><a class="headerlink" href="#ssl.SSLSession" title="Link to this definition">¶</a></dt>
<dd><p>Session object used by <a class="reference internal" href="#ssl.SSLSocket.session" title="ssl.SSLSocket.session"><code class="xref py py-attr docutils literal notranslate"><span class="pre">session</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSession.id">
<span class="sig-name descname"><span class="pre">id</span></span><a class="headerlink" href="#ssl.SSLSession.id" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSession.time">
<span class="sig-name descname"><span class="pre">time</span></span><a class="headerlink" href="#ssl.SSLSession.time" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSession.timeout">
<span class="sig-name descname"><span class="pre">timeout</span></span><a class="headerlink" href="#ssl.SSLSession.timeout" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSession.ticket_lifetime_hint">
<span class="sig-name descname"><span class="pre">ticket_lifetime_hint</span></span><a class="headerlink" href="#ssl.SSLSession.ticket_lifetime_hint" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ssl.SSLSession.has_ticket">
<span class="sig-name descname"><span class="pre">has_ticket</span></span><a class="headerlink" href="#ssl.SSLSession.has_ticket" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="security-considerations">
<span id="ssl-security"></span><h2>Security considerations<a class="headerlink" href="#security-considerations" title="Link to this heading">¶</a></h2>
<section id="best-defaults">
<h3>Best defaults<a class="headerlink" href="#best-defaults" title="Link to this heading">¶</a></h3>
<p>For <strong>client use</strong>, if you don’t have any special requirements for your
security policy, it is highly recommended that you use the
<a class="reference internal" href="#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">create_default_context()</span></code></a> function to create your SSL context.
It will load the system’s trusted CA certificates, enable certificate
validation and hostname checking, and try to choose reasonably secure
protocol and cipher settings.</p>
<p>For example, here is how you would use the <a class="reference internal" href="smtplib.html#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">smtplib.SMTP</span></code></a> class to
create a trusted, secure connection to a SMTP server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">ssl</span><span class="o">,</span> <span class="nn">smtplib</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">smtp</span> <span class="o">=</span> <span class="n">smtplib</span><span class="o">.</span><span class="n">SMTP</span><span class="p">(</span><span class="s2">&quot;mail.python.org&quot;</span><span class="p">,</span> <span class="n">port</span><span class="o">=</span><span class="mi">587</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">create_default_context</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">smtp</span><span class="o">.</span><span class="n">starttls</span><span class="p">(</span><span class="n">context</span><span class="o">=</span><span class="n">context</span><span class="p">)</span>
<span class="go">(220, b&#39;2.0.0 Ready to start TLS&#39;)</span>
</pre></div>
</div>
<p>If a client certificate is needed for the connection, it can be added with
<a class="reference internal" href="#ssl.SSLContext.load_cert_chain" title="ssl.SSLContext.load_cert_chain"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.load_cert_chain()</span></code></a>.</p>
<p>By contrast, if you create the SSL context by calling the <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a>
constructor yourself, it will not have certificate validation nor hostname
checking enabled by default.  If you do so, please read the paragraphs below
to achieve a good security level.</p>
</section>
<section id="manual-settings">
<h3>Manual settings<a class="headerlink" href="#manual-settings" title="Link to this heading">¶</a></h3>
<section id="verifying-certificates">
<h4>Verifying certificates<a class="headerlink" href="#verifying-certificates" title="Link to this heading">¶</a></h4>
<p>When calling the <a class="reference internal" href="#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> constructor directly,
<a class="reference internal" href="#ssl.CERT_NONE" title="ssl.CERT_NONE"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_NONE</span></code></a> is the default.  Since it does not authenticate the other
peer, it can be insecure, especially in client mode where most of time you
would like to ensure the authenticity of the server you’re talking to.
Therefore, when in client mode, it is highly recommended to use
<a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a>.  However, it is in itself not sufficient; you also
have to check that the server certificate, which can be obtained by calling
<a class="reference internal" href="#ssl.SSLSocket.getpeercert" title="ssl.SSLSocket.getpeercert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLSocket.getpeercert()</span></code></a>, matches the desired service.  For many
protocols and applications, the service can be identified by the hostname.
This common check is automatically performed when
<a class="reference internal" href="#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.check_hostname</span></code></a> is enabled.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Hostname matchings is now performed by OpenSSL. Python no longer uses
<code class="xref py py-func docutils literal notranslate"><span class="pre">match_hostname()</span></code>.</p>
</div>
<p>In server mode, if you want to authenticate your clients using the SSL layer
(rather than using a higher-level authentication mechanism), you’ll also have
to specify <a class="reference internal" href="#ssl.CERT_REQUIRED" title="ssl.CERT_REQUIRED"><code class="xref py py-const docutils literal notranslate"><span class="pre">CERT_REQUIRED</span></code></a> and similarly check the client certificate.</p>
</section>
<section id="protocol-versions">
<h4>Protocol versions<a class="headerlink" href="#protocol-versions" title="Link to this heading">¶</a></h4>
<p>SSL versions 2 and 3 are considered insecure and are therefore dangerous to
use.  If you want maximum compatibility between clients and servers, it is
recommended to use <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a> or
<a class="reference internal" href="#ssl.PROTOCOL_TLS_SERVER" title="ssl.PROTOCOL_TLS_SERVER"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS_SERVER</span></code></a> as the protocol version. SSLv2 and SSLv3 are
disabled by default.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">client_context</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">SSLContext</span><span class="p">(</span><span class="n">ssl</span><span class="o">.</span><span class="n">PROTOCOL_TLS_CLIENT</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">client_context</span><span class="o">.</span><span class="n">minimum_version</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">TLSVersion</span><span class="o">.</span><span class="n">TLSv1_3</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">client_context</span><span class="o">.</span><span class="n">maximum_version</span> <span class="o">=</span> <span class="n">ssl</span><span class="o">.</span><span class="n">TLSVersion</span><span class="o">.</span><span class="n">TLSv1_3</span>
</pre></div>
</div>
<p>The SSL context created above will only allow TLSv1.3 and later (if
supported by your system) connections to a server. <a class="reference internal" href="#ssl.PROTOCOL_TLS_CLIENT" title="ssl.PROTOCOL_TLS_CLIENT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PROTOCOL_TLS_CLIENT</span></code></a>
implies certificate validation and hostname checks by default. You have to
load certificates into the context.</p>
</section>
<section id="cipher-selection">
<h4>Cipher selection<a class="headerlink" href="#cipher-selection" title="Link to this heading">¶</a></h4>
<p>If you have advanced security requirements, fine-tuning of the ciphers
enabled when negotiating a SSL session is possible through the
<a class="reference internal" href="#ssl.SSLContext.set_ciphers" title="ssl.SSLContext.set_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_ciphers()</span></code></a> method.  Starting from Python 3.2.3, the
ssl module disables certain weak ciphers by default, but you may want
to further restrict the cipher choice. Be sure to read OpenSSL’s documentation
about the <a class="reference external" href="https://www.openssl.org/docs/man1.1.1/man1/ciphers.html#CIPHER-LIST-FORMAT">cipher list format</a>.
If you want to check which ciphers are enabled by a given cipher list, use
<a class="reference internal" href="#ssl.SSLContext.get_ciphers" title="ssl.SSLContext.get_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.get_ciphers()</span></code></a> or the <code class="docutils literal notranslate"><span class="pre">openssl</span> <span class="pre">ciphers</span></code> command on your
system.</p>
</section>
</section>
<section id="multi-processing">
<h3>Multi-processing<a class="headerlink" href="#multi-processing" title="Link to this heading">¶</a></h3>
<p>If using this module as part of a multi-processed application (using,
for example the <a class="reference internal" href="multiprocessing.html#module-multiprocessing" title="multiprocessing: Process-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code></a> or <a class="reference internal" href="concurrent.futures.html#module-concurrent.futures" title="concurrent.futures: Execute computations concurrently using threads or processes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent.futures</span></code></a> modules),
be aware that OpenSSL’s internal random number generator does not properly
handle forked processes.  Applications must change the PRNG state of the
parent process if they use any SSL feature with <a class="reference internal" href="os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fork()</span></code></a>.  Any
successful call of <a class="reference internal" href="#ssl.RAND_add" title="ssl.RAND_add"><code class="xref py py-func docutils literal notranslate"><span class="pre">RAND_add()</span></code></a> or <a class="reference internal" href="#ssl.RAND_bytes" title="ssl.RAND_bytes"><code class="xref py py-func docutils literal notranslate"><span class="pre">RAND_bytes()</span></code></a> is
sufficient.</p>
</section>
</section>
<section id="tls-1-3">
<span id="ssl-tlsv1-3"></span><h2>TLS 1.3<a class="headerlink" href="#tls-1-3" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>The TLS 1.3 protocol behaves slightly differently than previous version
of TLS/SSL. Some new TLS 1.3 features are not yet available.</p>
<ul class="simple">
<li><p>TLS 1.3 uses a disjunct set of cipher suites. All AES-GCM and
ChaCha20 cipher suites are enabled by default.  The method
<a class="reference internal" href="#ssl.SSLContext.set_ciphers" title="ssl.SSLContext.set_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.set_ciphers()</span></code></a> cannot enable or disable any TLS 1.3
ciphers yet, but <a class="reference internal" href="#ssl.SSLContext.get_ciphers" title="ssl.SSLContext.get_ciphers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SSLContext.get_ciphers()</span></code></a> returns them.</p></li>
<li><p>Session tickets are no longer sent as part of the initial handshake and
are handled differently.  <a class="reference internal" href="#ssl.SSLSocket.session" title="ssl.SSLSocket.session"><code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLSocket.session</span></code></a> and <a class="reference internal" href="#ssl.SSLSession" title="ssl.SSLSession"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLSession</span></code></a>
are not compatible with TLS 1.3.</p></li>
<li><p>Client-side certificates are also no longer verified during the initial
handshake.  A server can request a certificate at any time.  Clients
process certificate requests while they send or receive application data
from the server.</p></li>
<li><p>TLS 1.3 features like early data, deferred TLS client cert request,
signature algorithm configuration, and rekeying are not supported yet.</p></li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Class <a class="reference internal" href="socket.html#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket.socket</span></code></a></dt><dd><p>Documentation of underlying <a class="reference internal" href="socket.html#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> class</p>
</dd>
<dt><a class="reference external" href="https://httpd.apache.org/docs/trunk/en/ssl/ssl_intro.html">SSL/TLS Strong Encryption: An Introduction</a></dt><dd><p>Intro from the Apache HTTP Server documentation</p>
</dd>
<dt><span class="target" id="index-17"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1422.html"><strong>RFC 1422: Privacy Enhancement for Internet Electronic Mail: Part II: Certificate-Based Key Management</strong></a></dt><dd><p>Steve Kent</p>
</dd>
<dt><span class="target" id="index-18"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4086.html"><strong>RFC 4086: Randomness Requirements for Security</strong></a></dt><dd><p>Donald E., Jeffrey I. Schiller</p>
</dd>
<dt><span class="target" id="index-19"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5280.html"><strong>RFC 5280: Internet X.509 Public Key Infrastructure Certificate and Certificate Revocation List (CRL) Profile</strong></a></dt><dd><p>D. Cooper</p>
</dd>
<dt><span class="target" id="index-20"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5246.html"><strong>RFC 5246: The Transport Layer Security (TLS) Protocol Version 1.2</strong></a></dt><dd><p>T. Dierks et. al.</p>
</dd>
<dt><span class="target" id="index-21"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6066.html"><strong>RFC 6066: Transport Layer Security (TLS) Extensions</strong></a></dt><dd><p>D. Eastlake</p>
</dd>
<dt><a class="reference external" href="https://www.iana.org/assignments/tls-parameters/tls-parameters.xml">IANA TLS: Transport Layer Security (TLS) Parameters</a></dt><dd><p>IANA</p>
</dd>
<dt><span class="target" id="index-22"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7525.html"><strong>RFC 7525: Recommendations for Secure Use of Transport Layer Security (TLS) and Datagram Transport Layer Security (DTLS)</strong></a></dt><dd><p>IETF</p>
</dd>
<dt><a class="reference external" href="https://wiki.mozilla.org/Security/Server_Side_TLS">Mozilla’s Server Side TLS recommendations</a></dt><dd><p>Mozilla</p>
</dd>
</dl>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code> — TLS/SSL wrapper for socket objects</a><ul>
<li><a class="reference internal" href="#functions-constants-and-exceptions">Functions, Constants, and Exceptions</a><ul>
<li><a class="reference internal" href="#socket-creation">Socket creation</a></li>
<li><a class="reference internal" href="#context-creation">Context creation</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#random-generation">Random generation</a></li>
<li><a class="reference internal" href="#certificate-handling">Certificate handling</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
</ul>
</li>
<li><a class="reference internal" href="#ssl-sockets">SSL Sockets</a></li>
<li><a class="reference internal" href="#ssl-contexts">SSL Contexts</a></li>
<li><a class="reference internal" href="#certificates">Certificates</a><ul>
<li><a class="reference internal" href="#certificate-chains">Certificate chains</a></li>
<li><a class="reference internal" href="#ca-certificates">CA certificates</a></li>
<li><a class="reference internal" href="#combined-key-and-certificate">Combined key and certificate</a></li>
<li><a class="reference internal" href="#self-signed-certificates">Self-signed certificates</a></li>
</ul>
</li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#testing-for-ssl-support">Testing for SSL support</a></li>
<li><a class="reference internal" href="#client-side-operation">Client-side operation</a></li>
<li><a class="reference internal" href="#server-side-operation">Server-side operation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#notes-on-non-blocking-sockets">Notes on non-blocking sockets</a></li>
<li><a class="reference internal" href="#memory-bio-support">Memory BIO Support</a></li>
<li><a class="reference internal" href="#ssl-session">SSL session</a></li>
<li><a class="reference internal" href="#security-considerations">Security considerations</a><ul>
<li><a class="reference internal" href="#best-defaults">Best defaults</a></li>
<li><a class="reference internal" href="#manual-settings">Manual settings</a><ul>
<li><a class="reference internal" href="#verifying-certificates">Verifying certificates</a></li>
<li><a class="reference internal" href="#protocol-versions">Protocol versions</a></li>
<li><a class="reference internal" href="#cipher-selection">Cipher selection</a></li>
</ul>
</li>
<li><a class="reference internal" href="#multi-processing">Multi-processing</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tls-1-3">TLS 1.3</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="socket.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code> — Low-level networking interface</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="select.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code> — Waiting for I/O completion</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ssl.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="select.html" title="select — Waiting for I/O completion"
             >next</a> |</li>
        <li class="right" >
          <a href="socket.html" title="socket — Low-level networking interface"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" >Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code> — TLS/SSL wrapper for socket objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>