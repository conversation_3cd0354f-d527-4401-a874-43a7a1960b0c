<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="http.cookies — HTTP state management" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/http.cookies.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/http/cookies.py The http.cookies module defines classes for abstracting the concept of cookies, an HTTP state management mechanism. It supports both simple string-only cookies, and..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/http/cookies.py The http.cookies module defines classes for abstracting the concept of cookies, an HTTP state management mechanism. It supports both simple string-only cookies, and..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>http.cookies — HTTP state management &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="http.cookiejar — Cookie handling for HTTP clients" href="http.cookiejar.html" />
    <link rel="prev" title="http.server — HTTP servers" href="http.server.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/http.cookies.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a><ul>
<li><a class="reference internal" href="#cookie-objects">Cookie Objects</a></li>
<li><a class="reference internal" href="#morsel-objects">Morsel Objects</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.server.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="http.cookiejar.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.cookies.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="http.cookiejar.html" title="http.cookiejar — Cookie handling for HTTP clients"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="http.server.html" title="http.server — HTTP servers"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-http.cookies">
<span id="http-cookies-http-state-management"></span><h1><a class="reference internal" href="#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a> — HTTP state management<a class="headerlink" href="#module-http.cookies" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/http/cookies.py">Lib/http/cookies.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a> module defines classes for abstracting the concept of
cookies, an HTTP state management mechanism. It supports both simple string-only
cookies, and provides an abstraction for having any serializable data-type as
cookie value.</p>
<p>The module formerly strictly applied the parsing rules described in the
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> and <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2068.html"><strong>RFC 2068</strong></a> specifications.  It has since been discovered that
MSIE 3.0x didn’t follow the character rules outlined in those specs; many
current-day browsers and servers have also relaxed parsing rules when it comes
to cookie handling.  As a result, this module now uses parsing rules that are a
bit less strict than they once were.</p>
<p>The character set, <a class="reference internal" href="string.html#string.ascii_letters" title="string.ascii_letters"><code class="xref py py-data docutils literal notranslate"><span class="pre">string.ascii_letters</span></code></a>, <a class="reference internal" href="string.html#string.digits" title="string.digits"><code class="xref py py-data docutils literal notranslate"><span class="pre">string.digits</span></code></a> and
<code class="docutils literal notranslate"><span class="pre">!#$%&amp;'*+-.^_`|~:</span></code> denote the set of valid characters allowed by this module
in a cookie name (as <a class="reference internal" href="#http.cookies.Morsel.key" title="http.cookies.Morsel.key"><code class="xref py py-attr docutils literal notranslate"><span class="pre">key</span></code></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Allowed ‘:’ as a valid cookie name character.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On encountering an invalid cookie, <a class="reference internal" href="#http.cookies.CookieError" title="http.cookies.CookieError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CookieError</span></code></a> is raised, so if your
cookie data comes from a browser you should always prepare for invalid data
and catch <a class="reference internal" href="#http.cookies.CookieError" title="http.cookies.CookieError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CookieError</span></code></a> on parsing.</p>
</div>
<dl class="py exception">
<dt class="sig sig-object py" id="http.cookies.CookieError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookies.</span></span><span class="sig-name descname"><span class="pre">CookieError</span></span><a class="headerlink" href="#http.cookies.CookieError" title="Link to this definition">¶</a></dt>
<dd><p>Exception failing because of <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> invalidity: incorrect attributes,
incorrect <em class="mailheader">Set-Cookie</em> header, etc.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookies.BaseCookie">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookies.</span></span><span class="sig-name descname"><span class="pre">BaseCookie</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">input</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.BaseCookie" title="Link to this definition">¶</a></dt>
<dd><p>This class is a dictionary-like object whose keys are strings and whose values
are <a class="reference internal" href="#http.cookies.Morsel" title="http.cookies.Morsel"><code class="xref py py-class docutils literal notranslate"><span class="pre">Morsel</span></code></a> instances. Note that upon setting a key to a value, the
value is first converted to a <a class="reference internal" href="#http.cookies.Morsel" title="http.cookies.Morsel"><code class="xref py py-class docutils literal notranslate"><span class="pre">Morsel</span></code></a> containing the key and the value.</p>
<p>If <em>input</em> is given, it is passed to the <a class="reference internal" href="#http.cookies.BaseCookie.load" title="http.cookies.BaseCookie.load"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load()</span></code></a> method.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookies.SimpleCookie">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookies.</span></span><span class="sig-name descname"><span class="pre">SimpleCookie</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">input</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.SimpleCookie" title="Link to this definition">¶</a></dt>
<dd><p>This class derives from <a class="reference internal" href="#http.cookies.BaseCookie" title="http.cookies.BaseCookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCookie</span></code></a> and overrides <a class="reference internal" href="#http.cookies.BaseCookie.value_decode" title="http.cookies.BaseCookie.value_decode"><code class="xref py py-meth docutils literal notranslate"><span class="pre">value_decode()</span></code></a>
and <a class="reference internal" href="#http.cookies.BaseCookie.value_encode" title="http.cookies.BaseCookie.value_encode"><code class="xref py py-meth docutils literal notranslate"><span class="pre">value_encode()</span></code></a>. <code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleCookie</span></code> supports
strings as cookie values. When setting the value, <code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleCookie</span></code>
calls the builtin <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> to convert
the value to a string. Values received from HTTP are kept as strings.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="http.cookiejar.html#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a></dt><dd><p>HTTP cookie handling for web <em>clients</em>.  The <a class="reference internal" href="http.cookiejar.html#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> and
<a class="reference internal" href="#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a> modules do not depend on each other.</p>
</dd>
<dt><span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> - HTTP State Management Mechanism</dt><dd><p>This is the state management specification implemented by this module.</p>
</dd>
</dl>
</div>
<section id="cookie-objects">
<span id="id1"></span><h2>Cookie Objects<a class="headerlink" href="#cookie-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.BaseCookie.value_decode">
<span class="sig-prename descclassname"><span class="pre">BaseCookie.</span></span><span class="sig-name descname"><span class="pre">value_decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.BaseCookie.value_decode" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple <code class="docutils literal notranslate"><span class="pre">(real_value,</span> <span class="pre">coded_value)</span></code> from a string representation.
<code class="docutils literal notranslate"><span class="pre">real_value</span></code> can be any type. This method does no decoding in
<a class="reference internal" href="#http.cookies.BaseCookie" title="http.cookies.BaseCookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCookie</span></code></a> — it exists so it can be overridden.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.BaseCookie.value_encode">
<span class="sig-prename descclassname"><span class="pre">BaseCookie.</span></span><span class="sig-name descname"><span class="pre">value_encode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.BaseCookie.value_encode" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple <code class="docutils literal notranslate"><span class="pre">(real_value,</span> <span class="pre">coded_value)</span></code>. <em>val</em> can be any type, but
<code class="docutils literal notranslate"><span class="pre">coded_value</span></code> will always be converted to a string.
This method does no encoding in <a class="reference internal" href="#http.cookies.BaseCookie" title="http.cookies.BaseCookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCookie</span></code></a> — it exists so it can
be overridden.</p>
<p>In general, it should be the case that <a class="reference internal" href="#http.cookies.BaseCookie.value_encode" title="http.cookies.BaseCookie.value_encode"><code class="xref py py-meth docutils literal notranslate"><span class="pre">value_encode()</span></code></a> and
<a class="reference internal" href="#http.cookies.BaseCookie.value_decode" title="http.cookies.BaseCookie.value_decode"><code class="xref py py-meth docutils literal notranslate"><span class="pre">value_decode()</span></code></a> are inverses on the range of <em>value_decode</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.BaseCookie.output">
<span class="sig-prename descclassname"><span class="pre">BaseCookie.</span></span><span class="sig-name descname"><span class="pre">output</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">header</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'Set-Cookie:'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sep</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'\r\n'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.BaseCookie.output" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representation suitable to be sent as HTTP headers. <em>attrs</em> and
<em>header</em> are sent to each <a class="reference internal" href="#http.cookies.Morsel" title="http.cookies.Morsel"><code class="xref py py-class docutils literal notranslate"><span class="pre">Morsel</span></code></a>’s <a class="reference internal" href="#http.cookies.BaseCookie.output" title="http.cookies.BaseCookie.output"><code class="xref py py-meth docutils literal notranslate"><span class="pre">output()</span></code></a> method. <em>sep</em> is used
to join the headers together, and is by default the combination <code class="docutils literal notranslate"><span class="pre">'\r\n'</span></code>
(CRLF).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.BaseCookie.js_output">
<span class="sig-prename descclassname"><span class="pre">BaseCookie.</span></span><span class="sig-name descname"><span class="pre">js_output</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.BaseCookie.js_output" title="Link to this definition">¶</a></dt>
<dd><p>Return an embeddable JavaScript snippet, which, if run on a browser which
supports JavaScript, will act the same as if the HTTP headers was sent.</p>
<p>The meaning for <em>attrs</em> is the same as in <a class="reference internal" href="#http.cookies.BaseCookie.output" title="http.cookies.BaseCookie.output"><code class="xref py py-meth docutils literal notranslate"><span class="pre">output()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.BaseCookie.load">
<span class="sig-prename descclassname"><span class="pre">BaseCookie.</span></span><span class="sig-name descname"><span class="pre">load</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rawdata</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.BaseCookie.load" title="Link to this definition">¶</a></dt>
<dd><p>If <em>rawdata</em> is a string, parse it as an <code class="docutils literal notranslate"><span class="pre">HTTP_COOKIE</span></code> and add the values
found there as <a class="reference internal" href="#http.cookies.Morsel" title="http.cookies.Morsel"><code class="xref py py-class docutils literal notranslate"><span class="pre">Morsel</span></code></a>s. If it is a dictionary, it is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="n">rawdata</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
    <span class="n">cookie</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">v</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="morsel-objects">
<span id="id2"></span><h2>Morsel Objects<a class="headerlink" href="#morsel-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="http.cookies.Morsel">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookies.</span></span><span class="sig-name descname"><span class="pre">Morsel</span></span><a class="headerlink" href="#http.cookies.Morsel" title="Link to this definition">¶</a></dt>
<dd><p>Abstract a key/value pair, which has some <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> attributes.</p>
<p>Morsels are dictionary-like objects, whose set of keys is constant — the valid
<span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> attributes, which are:</p>
<blockquote>
<div><dl class="py attribute">
<dt class="sig sig-object py" id="http.cookies.Morsel.expires">
<span class="sig-name descname"><span class="pre">expires</span></span><a class="headerlink" href="#http.cookies.Morsel.expires" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#http.cookies.Morsel.path" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.comment">
<span class="sig-name descname"><span class="pre">comment</span></span><a class="headerlink" href="#http.cookies.Morsel.comment" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.domain">
<span class="sig-name descname"><span class="pre">domain</span></span><a class="headerlink" href="#http.cookies.Morsel.domain" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">max-age</span></span></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.secure">
<span class="sig-name descname"><span class="pre">secure</span></span><a class="headerlink" href="#http.cookies.Morsel.secure" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#http.cookies.Morsel.version" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.httponly">
<span class="sig-name descname"><span class="pre">httponly</span></span><a class="headerlink" href="#http.cookies.Morsel.httponly" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="http.cookies.Morsel.samesite">
<span class="sig-name descname"><span class="pre">samesite</span></span><a class="headerlink" href="#http.cookies.Morsel.samesite" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</div></blockquote>
<p>The attribute <a class="reference internal" href="#http.cookies.Morsel.httponly" title="http.cookies.Morsel.httponly"><code class="xref py py-attr docutils literal notranslate"><span class="pre">httponly</span></code></a> specifies that the cookie is only transferred
in HTTP requests, and is not accessible through JavaScript. This is intended
to mitigate some forms of cross-site scripting.</p>
<p>The attribute <a class="reference internal" href="#http.cookies.Morsel.samesite" title="http.cookies.Morsel.samesite"><code class="xref py py-attr docutils literal notranslate"><span class="pre">samesite</span></code></a> specifies that the browser is not allowed to
send the cookie along with cross-site requests. This helps to mitigate CSRF
attacks. Valid values for this attribute are “Strict” and “Lax”.</p>
<p>The keys are case-insensitive and their default value is <code class="docutils literal notranslate"><span class="pre">''</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><code class="xref py py-meth docutils literal notranslate"><span class="pre">__eq__()</span></code> now takes <a class="reference internal" href="#http.cookies.Morsel.key" title="http.cookies.Morsel.key"><code class="xref py py-attr docutils literal notranslate"><span class="pre">key</span></code></a> and <a class="reference internal" href="#http.cookies.Morsel.value" title="http.cookies.Morsel.value"><code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code></a>
into account.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Attributes <a class="reference internal" href="#http.cookies.Morsel.key" title="http.cookies.Morsel.key"><code class="xref py py-attr docutils literal notranslate"><span class="pre">key</span></code></a>, <a class="reference internal" href="#http.cookies.Morsel.value" title="http.cookies.Morsel.value"><code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code></a> and
<a class="reference internal" href="#http.cookies.Morsel.coded_value" title="http.cookies.Morsel.coded_value"><code class="xref py py-attr docutils literal notranslate"><span class="pre">coded_value</span></code></a> are read-only.  Use <a class="reference internal" href="#http.cookies.Morsel.set" title="http.cookies.Morsel.set"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set()</span></code></a> for
setting them.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added support for the <a class="reference internal" href="#http.cookies.Morsel.samesite" title="http.cookies.Morsel.samesite"><code class="xref py py-attr docutils literal notranslate"><span class="pre">samesite</span></code></a> attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookies.Morsel.value">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#http.cookies.Morsel.value" title="Link to this definition">¶</a></dt>
<dd><p>The value of the cookie.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookies.Morsel.coded_value">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">coded_value</span></span><a class="headerlink" href="#http.cookies.Morsel.coded_value" title="Link to this definition">¶</a></dt>
<dd><p>The encoded value of the cookie — this is what should be sent.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookies.Morsel.key">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">key</span></span><a class="headerlink" href="#http.cookies.Morsel.key" title="Link to this definition">¶</a></dt>
<dd><p>The name of the cookie.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.set">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">coded_value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.set" title="Link to this definition">¶</a></dt>
<dd><p>Set the <em>key</em>, <em>value</em> and <em>coded_value</em> attributes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.isReservedKey">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">isReservedKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">K</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.isReservedKey" title="Link to this definition">¶</a></dt>
<dd><p>Whether <em>K</em> is a member of the set of keys of a <a class="reference internal" href="#http.cookies.Morsel" title="http.cookies.Morsel"><code class="xref py py-class docutils literal notranslate"><span class="pre">Morsel</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.output">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">output</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">header</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'Set-Cookie:'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.output" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representation of the Morsel, suitable to be sent as an HTTP
header. By default, all the attributes are included, unless <em>attrs</em> is given, in
which case it should be a list of attributes to use. <em>header</em> is by default
<code class="docutils literal notranslate"><span class="pre">&quot;Set-Cookie:&quot;</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.js_output">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">js_output</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.js_output" title="Link to this definition">¶</a></dt>
<dd><p>Return an embeddable JavaScript snippet, which, if run on a browser which
supports JavaScript, will act the same as if the HTTP header was sent.</p>
<p>The meaning for <em>attrs</em> is the same as in <a class="reference internal" href="#http.cookies.Morsel.output" title="http.cookies.Morsel.output"><code class="xref py py-meth docutils literal notranslate"><span class="pre">output()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.OutputString">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">OutputString</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.OutputString" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representing the Morsel, without any surrounding HTTP or
JavaScript.</p>
<p>The meaning for <em>attrs</em> is the same as in <a class="reference internal" href="#http.cookies.Morsel.output" title="http.cookies.Morsel.output"><code class="xref py py-meth docutils literal notranslate"><span class="pre">output()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.update">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">values</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.update" title="Link to this definition">¶</a></dt>
<dd><p>Update the values in the Morsel dictionary with the values in the dictionary
<em>values</em>.  Raise an error if any of the keys in the <em>values</em> dict is not a
valid <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> attribute.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>an error is raised for invalid keys.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.copy">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.copy" title="Link to this definition">¶</a></dt>
<dd><p>Return a shallow copy of the Morsel object.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>return a Morsel object instead of a dict.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookies.Morsel.setdefault">
<span class="sig-prename descclassname"><span class="pre">Morsel.</span></span><span class="sig-name descname"><span class="pre">setdefault</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookies.Morsel.setdefault" title="Link to this definition">¶</a></dt>
<dd><p>Raise an error if key is not a valid <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> attribute, otherwise
behave the same as <a class="reference internal" href="stdtypes.html#dict.setdefault" title="dict.setdefault"><code class="xref py py-meth docutils literal notranslate"><span class="pre">dict.setdefault()</span></code></a>.</p>
</dd></dl>

</section>
<section id="example">
<span id="cookie-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>The following example demonstrates how to use the <a class="reference internal" href="#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a> module.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">http</span> <span class="kn">import</span> <span class="n">cookies</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;fig&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;newton&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;sugar&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;wafer&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="p">)</span> <span class="c1"># generate HTTP headers</span>
<span class="go">Set-Cookie: fig=newton</span>
<span class="go">Set-Cookie: sugar=wafer</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="o">.</span><span class="n">output</span><span class="p">())</span> <span class="c1"># same thing</span>
<span class="go">Set-Cookie: fig=newton</span>
<span class="go">Set-Cookie: sugar=wafer</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;rocky&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;road&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;rocky&quot;</span><span class="p">][</span><span class="s2">&quot;path&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;/cookie&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="o">.</span><span class="n">output</span><span class="p">(</span><span class="n">header</span><span class="o">=</span><span class="s2">&quot;Cookie:&quot;</span><span class="p">))</span>
<span class="go">Cookie: rocky=road; Path=/cookie</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="o">.</span><span class="n">output</span><span class="p">(</span><span class="n">attrs</span><span class="o">=</span><span class="p">[],</span> <span class="n">header</span><span class="o">=</span><span class="s2">&quot;Cookie:&quot;</span><span class="p">))</span>
<span class="go">Cookie: rocky=road</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="s2">&quot;chips=ahoy; vienna=finger&quot;</span><span class="p">)</span> <span class="c1"># load from a string (HTTP header)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="p">)</span>
<span class="go">Set-Cookie: chips=ahoy</span>
<span class="go">Set-Cookie: vienna=finger</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="s1">&#39;keebler=&quot;E=everybody; L=</span><span class="se">\\</span><span class="s1">&quot;Loves</span><span class="se">\\</span><span class="s1">&quot;; fudge=</span><span class="se">\\</span><span class="s1">012;&quot;;&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="p">)</span>
<span class="go">Set-Cookie: keebler=&quot;E=everybody; L=\&quot;Loves\&quot;; fudge=\012;&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;oreo&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;doublestuff&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;oreo&quot;</span><span class="p">][</span><span class="s2">&quot;path&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;/&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="p">)</span>
<span class="go">Set-Cookie: oreo=doublestuff; Path=/</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;twix&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;none for you&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;twix&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">value</span>
<span class="go">&#39;none for you&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span> <span class="o">=</span> <span class="n">cookies</span><span class="o">.</span><span class="n">SimpleCookie</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;number&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">7</span> <span class="c1"># equivalent to C[&quot;number&quot;] = str(7)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;string&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;seven&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;number&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">value</span>
<span class="go">&#39;7&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">C</span><span class="p">[</span><span class="s2">&quot;string&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">value</span>
<span class="go">&#39;seven&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">C</span><span class="p">)</span>
<span class="go">Set-Cookie: number=7</span>
<span class="go">Set-Cookie: string=seven</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a><ul>
<li><a class="reference internal" href="#cookie-objects">Cookie Objects</a></li>
<li><a class="reference internal" href="#morsel-objects">Morsel Objects</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.server.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="http.cookiejar.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.cookies.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="http.cookiejar.html" title="http.cookiejar — Cookie handling for HTTP clients"
             >next</a> |</li>
        <li class="right" >
          <a href="http.server.html" title="http.server — HTTP servers"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>