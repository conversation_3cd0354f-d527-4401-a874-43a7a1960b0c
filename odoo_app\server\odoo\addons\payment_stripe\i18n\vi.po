# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot display the payment form"
msgstr "Không thể hiển thị biểu mẫu thanh toán"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__code
msgid "Code"
msgstr "Mã"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Connect Stripe"
msgstr "Connect Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Không thể thiết lập kết nối với API."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Delivery"
msgstr "Giao hàng"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Enable Apple Pay"
msgstr "Bật Apple Pay"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Free Shipping"
msgstr "Miễn phí giao hàng"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Generate your webhook"
msgstr "Tạo webhook của bạn"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Get your Secret and Publishable keys"
msgstr "Lấy khóa bí mật và có thể hiển thị của bạn"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"Nếu webhook được bật trên tài khoản Stripe của bạn, mã bí mật ký tên này "
"phải được thiết lập để xác thực các thông báo được gửi từ Stripe đến Odoo."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect payment details"
msgstr "Thông thanh toán không chính xác"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Không tìm thấy giao dịch nào khớp với mã %s."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Other Payment Providers"
msgstr "Các nhà cung cấp dịch vụ thanh toán khác"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_provider
msgid "Payment Provider"
msgstr "Nhà cung cấp dịch vụ thanh toán"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_provider_onboarding
msgid "Payment Providers"
msgstr "Nhà cung cấp dịch vụ thanh toán"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Mã thanh toán"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Giao dịch thanh toán"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "Xử lý thanh toán không thành công"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Please use live credentials to enable Apple Pay."
msgstr ""
"Vui lòng dùng thông tin đăng nhập đang sử dụng để kích hoạt Apple Pay."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "Publishable Key"
msgstr "Mã khóa có thể hiển thị "

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "Dữ liệu đã nhận với trạng thái mục đích không hợp lệ: %s"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "Dữ liệu đã nhận bị thiếu trạng thái mục đích."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "Dữ liệu đã nhận bị thiếu mã người bán"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_secret_key
msgid "Secret Key"
msgstr "Mã khóa bí mật"

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_provider__code__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect không khả dụng ở quốc gia của bạn, vui lòng sử dụng nhà cung "
"cấp dịch vụ thanh toán khác."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_mandate
msgid "Stripe Mandate"
msgstr "Uỷ nhiệm Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "ID phương thức thanh toán Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "Lỗi proxy Stripe: %(error)s"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr "Proxy Stripe: Đã xảy ra lỗi khi liên lạc với proxy."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "Proxy Stripe: Không thể thiết lập kết nối."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"Giao tiếp với API không thành công.\n"
"Stripe đã cung cấp cho chúng tôi thông tin sau:\n"
"'%s'"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The customer left the payment page."
msgstr "Khách hàng đã rời khỏi trang thanh toán."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr "Mã khoá chỉ được sử dụng để xác định tài khoản với Stripe."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The refund did not go through. Please log into your Stripe Dashboard to get "
"more information on that matter, and address any accounting discrepancies."
msgstr ""
"Hoàn tiền không được thực hiện. Vui lòng đăng nhập vào Trang chủ Stripe của "
"bạn để biết thêm thông tin về vấn đề đó và xử lý các chênh lệch về kế toán."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Mã kỹ thuật của nhà cung cấp dịch vụ thanh toán này."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "Giao dịch không được liên kết với token."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "Không thể chuyển đổi token thanh toán sang API mới."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "Mã bí mật ký tên Webhook"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "Webhook Stripe của bạn đã được thiết lập thành công!"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"Bạn không thể tạo Webhook Stripe nếu không thiết lập Mã khóa bí mật Stripe."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot set the provider state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""
"Bạn không thể đặt trạng thái nhà cung cấp thành Đã bật cho đến khi quá trình"
" hướng dẫn Stripe của bạn hoàn tất."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot set the provider to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"Bạn không thể đặt nhà cung cấp ở Chế độ kiểm thử khi nhà cung cấp đó được "
"liên kết với tài khoản Stripe của bạn."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "Webhook Stripe của bạn đã được thiết lập."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Your order"
msgstr "Đơn hàng của bạn"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Your web domain was successfully verified."
msgstr "Miền trang web của bạn đã được xác minh thành công."
