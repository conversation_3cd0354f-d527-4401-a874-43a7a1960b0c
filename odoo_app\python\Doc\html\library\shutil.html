<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="shutil — High-level file operations" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/shutil.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/shutil.py The shutil module offers a number of high-level operations on files and collections of files. In particular, functions are provided which support file copying and removal..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/shutil.py The shutil module offers a number of high-level operations on files and collections of files. In particular, functions are provided which support file copying and removal..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>shutil — High-level file operations &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Data Persistence" href="persistence.html" />
    <link rel="prev" title="linecache — Random access to text lines" href="linecache.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/shutil.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code> — High-level file operations</a><ul>
<li><a class="reference internal" href="#directory-and-files-operations">Directory and files operations</a><ul>
<li><a class="reference internal" href="#platform-dependent-efficient-copy-operations">Platform-dependent efficient copy operations</a></li>
<li><a class="reference internal" href="#copytree-example">copytree example</a></li>
<li><a class="reference internal" href="#rmtree-example">rmtree example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#archiving-operations">Archiving operations</a><ul>
<li><a class="reference internal" href="#archiving-example">Archiving example</a></li>
<li><a class="reference internal" href="#archiving-example-with-base-dir">Archiving example with <em>base_dir</em></a></li>
</ul>
</li>
<li><a class="reference internal" href="#querying-the-size-of-the-output-terminal">Querying the size of the output terminal</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="linecache.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">linecache</span></code> — Random access to text lines</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="persistence.html"
                          title="next chapter">Data Persistence</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/shutil.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="persistence.html" title="Data Persistence"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="linecache.html" title="linecache — Random access to text lines"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" accesskey="U">File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code> — High-level file operations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-shutil">
<span id="shutil-high-level-file-operations"></span><h1><a class="reference internal" href="#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> — High-level file operations<a class="headerlink" href="#module-shutil" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/shutil.py">Lib/shutil.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> module offers a number of high-level operations on files and
collections of files.  In particular, functions are provided  which support file
copying and removal. For operations on individual files, see also the
<a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> module.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Even the higher-level file copying functions (<a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.copy()</span></code></a>,
<a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.copy2()</span></code></a>) cannot copy all file metadata.</p>
<p>On POSIX platforms, this means that file owner and group are lost as well
as ACLs.  On Mac OS, the resource fork and other metadata are not used.
This means that resources will be lost and file type and creator codes will
not be correct. On Windows, file owners, ACLs and alternate data streams
are not copied.</p>
</div>
<section id="directory-and-files-operations">
<span id="file-operations"></span><h2>Directory and files operations<a class="headerlink" href="#directory-and-files-operations" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="shutil.copyfileobj">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copyfileobj</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fsrc</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fdst</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copyfileobj" title="Link to this definition">¶</a></dt>
<dd><p>Copy the contents of the <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file-like object</span></a> <em>fsrc</em> to the file-like object <em>fdst</em>.
The integer <em>length</em>, if given, is the buffer size. In particular, a negative
<em>length</em> value means to copy the data without looping over the source data in
chunks; by default the data is read in chunks to avoid uncontrolled memory
consumption. Note that if the current file position of the <em>fsrc</em> object is not
0, only the contents from the current file position to the end of the file will
be copied.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.copyfile">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copyfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copyfile" title="Link to this definition">¶</a></dt>
<dd><p>Copy the contents (no metadata) of the file named <em>src</em> to a file named
<em>dst</em> and return <em>dst</em> in the most efficient way possible.
<em>src</em> and <em>dst</em> are <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like objects</span></a> or path names given as strings.</p>
<p><em>dst</em> must be the complete target file name; look at <a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a>
for a copy that accepts a target directory path.  If <em>src</em> and <em>dst</em>
specify the same file, <a class="reference internal" href="#shutil.SameFileError" title="shutil.SameFileError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SameFileError</span></code></a> is raised.</p>
<p>The destination location must be writable; otherwise, an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>
exception will be raised. If <em>dst</em> already exists, it will be replaced.
Special files such as character or block devices and pipes cannot be
copied with this function.</p>
<p>If <em>follow_symlinks</em> is false and <em>src</em> is a symbolic link,
a new symbolic link will be created instead of copying the
file <em>src</em> points to.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copyfile</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> used to be raised instead of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.
Added <em>follow_symlinks</em> argument.
Now returns <em>dst</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raise <a class="reference internal" href="#shutil.SameFileError" title="shutil.SameFileError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SameFileError</span></code></a> instead of <a class="reference internal" href="#shutil.Error" title="shutil.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a>.  Since the former is
a subclass of the latter, this change is backward compatible.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Platform-specific fast-copy syscalls may be used internally in order to
copy the file more efficiently. See
<a class="reference internal" href="#shutil-platform-dependent-efficient-copy-operations"><span class="std std-ref">Platform-dependent efficient copy operations</span></a> section.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="shutil.SameFileError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">SameFileError</span></span><a class="headerlink" href="#shutil.SameFileError" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised if source and destination in <a class="reference internal" href="#shutil.copyfile" title="shutil.copyfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">copyfile()</span></code></a>
are the same file.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.copymode">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copymode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copymode" title="Link to this definition">¶</a></dt>
<dd><p>Copy the permission bits from <em>src</em> to <em>dst</em>.  The file contents, owner, and
group are unaffected.  <em>src</em> and <em>dst</em> are <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like objects</span></a> or path names
given as strings.
If <em>follow_symlinks</em> is false, and both <em>src</em> and <em>dst</em> are symbolic links,
<a class="reference internal" href="#shutil.copymode" title="shutil.copymode"><code class="xref py py-func docutils literal notranslate"><span class="pre">copymode()</span></code></a> will attempt to modify the mode of <em>dst</em> itself (rather
than the file it points to).  This functionality is not available on every
platform; please see <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> for more information.  If
<a class="reference internal" href="#shutil.copymode" title="shutil.copymode"><code class="xref py py-func docutils literal notranslate"><span class="pre">copymode()</span></code></a> cannot modify symbolic links on the local platform, and it
is asked to do so, it will do nothing and return.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copymode</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added <em>follow_symlinks</em> argument.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.copystat">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copystat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copystat" title="Link to this definition">¶</a></dt>
<dd><p>Copy the permission bits, last access time, last modification time, and
flags from <em>src</em> to <em>dst</em>.  On Linux, <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> also copies the
“extended attributes” where possible.  The file contents, owner, and
group are unaffected.  <em>src</em> and <em>dst</em> are <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like objects</span></a> or path
names given as strings.</p>
<p>If <em>follow_symlinks</em> is false, and <em>src</em> and <em>dst</em> both
refer to symbolic links, <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> will operate on
the symbolic links themselves rather than the files the
symbolic links refer to—reading the information from the
<em>src</em> symbolic link, and writing the information to the
<em>dst</em> symbolic link.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Not all platforms provide the ability to examine and
modify symbolic links.  Python itself can tell you what
functionality is locally available.</p>
<ul class="simple">
<li><p>If <code class="docutils literal notranslate"><span class="pre">os.chmod</span> <span class="pre">in</span> <span class="pre">os.supports_follow_symlinks</span></code> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> can modify the permission
bits of a symbolic link.</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">os.utime</span> <span class="pre">in</span> <span class="pre">os.supports_follow_symlinks</span></code> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> can modify the last access
and modification times of a symbolic link.</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">os.chflags</span> <span class="pre">in</span> <span class="pre">os.supports_follow_symlinks</span></code> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> can modify the flags of
a symbolic link.  (<code class="docutils literal notranslate"><span class="pre">os.chflags</span></code> is not available on
all platforms.)</p></li>
</ul>
<p>On platforms where some or all of this functionality
is unavailable, when asked to modify a symbolic link,
<a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> will copy everything it can.
<a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> never returns failure.</p>
<p>Please see <a class="reference internal" href="os.html#os.supports_follow_symlinks" title="os.supports_follow_symlinks"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.supports_follow_symlinks</span></code></a>
for more information.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copystat</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added <em>follow_symlinks</em> argument and support for Linux extended attributes.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.copy">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copy" title="Link to this definition">¶</a></dt>
<dd><p>Copies the file <em>src</em> to the file or directory <em>dst</em>.  <em>src</em> and <em>dst</em>
should be <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like objects</span></a> or strings.  If
<em>dst</em> specifies a directory, the file will be copied into <em>dst</em> using the
base filename from <em>src</em>. If <em>dst</em> specifies a file that already exists,
it will be replaced. Returns the path to the newly created file.</p>
<p>If <em>follow_symlinks</em> is false, and <em>src</em> is a symbolic link,
<em>dst</em> will be created as a symbolic link.  If <em>follow_symlinks</em>
is true and <em>src</em> is a symbolic link, <em>dst</em> will be a copy of
the file <em>src</em> refers to.</p>
<p><a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a> copies the file data and the file’s permission
mode (see <a class="reference internal" href="os.html#os.chmod" title="os.chmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chmod()</span></code></a>).  Other metadata, like the
file’s creation and modification times, is not preserved.
To preserve all file metadata from the original, use
<a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a> instead.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copyfile</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copymode</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added <em>follow_symlinks</em> argument.
Now returns path to the newly created file.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Platform-specific fast-copy syscalls may be used internally in order to
copy the file more efficiently. See
<a class="reference internal" href="#shutil-platform-dependent-efficient-copy-operations"><span class="std std-ref">Platform-dependent efficient copy operations</span></a> section.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.copy2">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copy2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copy2" title="Link to this definition">¶</a></dt>
<dd><p>Identical to <a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a> except that <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a>
also attempts to preserve file metadata.</p>
<p>When <em>follow_symlinks</em> is false, and <em>src</em> is a symbolic
link, <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a> attempts to copy all metadata from the
<em>src</em> symbolic link to the newly created <em>dst</em> symbolic link.
However, this functionality is not available on all platforms.
On platforms where some or all of this functionality is
unavailable, <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a> will preserve all the metadata
it can; <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a> never raises an exception because it
cannot preserve file metadata.</p>
<p><a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a> uses <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> to copy the file metadata.
Please see <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a> for more information
about platform support for modifying symbolic link metadata.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copyfile</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copystat</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added <em>follow_symlinks</em> argument, try to copy extended
file system attributes too (currently Linux only).
Now returns path to the newly created file.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Platform-specific fast-copy syscalls may be used internally in order to
copy the file more efficiently. See
<a class="reference internal" href="#shutil-platform-dependent-efficient-copy-operations"><span class="std std-ref">Platform-dependent efficient copy operations</span></a> section.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.ignore_patterns">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">ignore_patterns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">patterns</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.ignore_patterns" title="Link to this definition">¶</a></dt>
<dd><p>This factory function creates a function that can be used as a callable for
<a class="reference internal" href="#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a>'s <em>ignore</em> argument, ignoring files and directories that
match one of the glob-style <em>patterns</em> provided.  See the example below.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.copytree">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">copytree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">copy_function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">copy2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_dangling_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dirs_exist_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.copytree" title="Link to this definition">¶</a></dt>
<dd><p>Recursively copy an entire directory tree rooted at <em>src</em> to a directory
named <em>dst</em> and return the destination directory.  All intermediate
directories needed to contain <em>dst</em> will also be created by default.</p>
<p>Permissions and times of directories are copied with <a class="reference internal" href="#shutil.copystat" title="shutil.copystat"><code class="xref py py-func docutils literal notranslate"><span class="pre">copystat()</span></code></a>,
individual files are copied using <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a>.</p>
<p>If <em>symlinks</em> is true, symbolic links in the source tree are represented as
symbolic links in the new tree and the metadata of the original links will
be copied as far as the platform allows; if false or omitted, the contents
and metadata of the linked files are copied to the new tree.</p>
<p>When <em>symlinks</em> is false, if the file pointed by the symlink doesn’t
exist, an exception will be added in the list of errors raised in
an <a class="reference internal" href="#shutil.Error" title="shutil.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a> exception at the end of the copy process.
You can set the optional <em>ignore_dangling_symlinks</em> flag to true if you
want to silence this exception. Notice that this option has no effect
on platforms that don’t support <a class="reference internal" href="os.html#os.symlink" title="os.symlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.symlink()</span></code></a>.</p>
<p>If <em>ignore</em> is given, it must be a callable that will receive as its
arguments the directory being visited by <a class="reference internal" href="#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a>, and a list of its
contents, as returned by <a class="reference internal" href="os.html#os.listdir" title="os.listdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.listdir()</span></code></a>.  Since <a class="reference internal" href="#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a> is
called recursively, the <em>ignore</em> callable will be called once for each
directory that is copied.  The callable must return a sequence of directory
and file names relative to the current directory (i.e. a subset of the items
in its second argument); these names will then be ignored in the copy
process.  <a class="reference internal" href="#shutil.ignore_patterns" title="shutil.ignore_patterns"><code class="xref py py-func docutils literal notranslate"><span class="pre">ignore_patterns()</span></code></a> can be used to create such a callable that
ignores names based on glob-style patterns.</p>
<p>If exception(s) occur, an <a class="reference internal" href="#shutil.Error" title="shutil.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a> is raised with a list of reasons.</p>
<p>If <em>copy_function</em> is given, it must be a callable that will be used to copy
each file. It will be called with the source path and the destination path
as arguments. By default, <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a> is used, but any function
that supports the same signature (like <a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a>) can be used.</p>
<p>If <em>dirs_exist_ok</em> is false (the default) and <em>dst</em> already exists, a
<a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> is raised. If <em>dirs_exist_ok</em> is true, the copying
operation will continue if it encounters existing directories, and files
within the <em>dst</em> tree will be overwritten by corresponding files from the
<em>src</em> tree.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.copytree</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>copy_function</em> argument to be able to provide a custom copy
function.
Added the <em>ignore_dangling_symlinks</em> argument to silence dangling symlinks
errors when <em>symlinks</em> is false.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Copy metadata when <em>symlinks</em> is false.
Now returns <em>dst</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Platform-specific fast-copy syscalls may be used internally in order to
copy the file more efficiently. See
<a class="reference internal" href="#shutil-platform-dependent-efficient-copy-operations"><span class="std std-ref">Platform-dependent efficient copy operations</span></a> section.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>dirs_exist_ok</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.rmtree">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">rmtree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">onerror</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">onexc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dir_fd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.rmtree" title="Link to this definition">¶</a></dt>
<dd><p id="index-1">Delete an entire directory tree; <em>path</em> must point to a directory (but not a
symbolic link to a directory).  If <em>ignore_errors</em> is true, errors resulting
from failed removals will be ignored; if false or omitted, such errors are
handled by calling a handler specified by <em>onexc</em> or <em>onerror</em> or, if both
are omitted, exceptions are propagated to the caller.</p>
<p>This function can support <a class="reference internal" href="os.html#dir-fd"><span class="std std-ref">paths relative to directory descriptors</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On platforms that support the necessary fd-based functions a symlink
attack resistant version of <a class="reference internal" href="#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmtree()</span></code></a> is used by default.  On other
platforms, the <a class="reference internal" href="#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmtree()</span></code></a> implementation is susceptible to a symlink
attack: given proper timing and circumstances, attackers can manipulate
symlinks on the filesystem to delete files they wouldn’t be able to access
otherwise.  Applications can use the <a class="reference internal" href="#shutil.rmtree.avoids_symlink_attacks" title="shutil.rmtree.avoids_symlink_attacks"><code class="xref py py-data docutils literal notranslate"><span class="pre">rmtree.avoids_symlink_attacks</span></code></a>
function attribute to determine which case applies.</p>
</div>
<p>If <em>onexc</em> is provided, it must be a callable that accepts three parameters:
<em>function</em>, <em>path</em>, and <em>excinfo</em>.</p>
<p>The first parameter, <em>function</em>, is the function which raised the exception;
it depends on the platform and implementation.  The second parameter,
<em>path</em>, will be the path name passed to <em>function</em>.  The third parameter,
<em>excinfo</em>, is the exception that was raised. Exceptions raised by <em>onexc</em>
will not be caught.</p>
<p>The deprecated <em>onerror</em> is similar to <em>onexc</em>, except that the third
parameter it receives is the tuple returned from <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.rmtree</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">path</span></code>, <code class="docutils literal notranslate"><span class="pre">dir_fd</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added a symlink attack resistant version that is used automatically
if platform supports fd-based functions.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>On Windows, will no longer delete the contents of a directory junction
before removing the junction.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <em>dir_fd</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>onexc</em> parameter, deprecated <em>onerror</em>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="shutil.rmtree.avoids_symlink_attacks">
<span class="sig-prename descclassname"><span class="pre">rmtree.</span></span><span class="sig-name descname"><span class="pre">avoids_symlink_attacks</span></span><a class="headerlink" href="#shutil.rmtree.avoids_symlink_attacks" title="Link to this definition">¶</a></dt>
<dd><p>Indicates whether the current platform and implementation provides a
symlink attack resistant version of <a class="reference internal" href="#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmtree()</span></code></a>.  Currently this is
only true for platforms supporting fd-based directory access functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.move">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">move</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">copy_function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">copy2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.move" title="Link to this definition">¶</a></dt>
<dd><p>Recursively move a file or directory (<em>src</em>) to another location and return
the destination.</p>
<p>If <em>dst</em> is an existing directory or a symlink to a directory, then <em>src</em>
is moved inside that directory. The destination path in that directory must
not already exist.</p>
<p>If <em>dst</em> already exists but is not a directory, it may be overwritten
depending on <a class="reference internal" href="os.html#os.rename" title="os.rename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rename()</span></code></a> semantics.</p>
<p>If the destination is on the current filesystem, then <a class="reference internal" href="os.html#os.rename" title="os.rename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rename()</span></code></a> is
used. Otherwise, <em>src</em> is copied to the destination using <em>copy_function</em>
and then removed.  In case of symlinks, a new symlink pointing to the target
of <em>src</em> will be created as the destination and <em>src</em> will be removed.</p>
<p>If <em>copy_function</em> is given, it must be a callable that takes two arguments,
<em>src</em> and the destination, and will be used to copy <em>src</em> to the destination
if <a class="reference internal" href="os.html#os.rename" title="os.rename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rename()</span></code></a> cannot be used.  If the source is a directory,
<a class="reference internal" href="#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a> is called, passing it the <em>copy_function</em>. The
default <em>copy_function</em> is <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a>.  Using <a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a> as the
<em>copy_function</em> allows the move to succeed when it is not possible to also
copy the metadata, at the expense of not copying any of the metadata.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.move</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">src</span></code>, <code class="docutils literal notranslate"><span class="pre">dst</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added explicit symlink handling for foreign filesystems, thus adapting
it to the behavior of GNU’s <strong class="program">mv</strong>.
Now returns <em>dst</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>copy_function</em> keyword argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Platform-specific fast-copy syscalls may be used internally in order to
copy the file more efficiently. See
<a class="reference internal" href="#shutil-platform-dependent-efficient-copy-operations"><span class="std std-ref">Platform-dependent efficient copy operations</span></a> section.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> for both <em>src</em> and <em>dst</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.disk_usage">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">disk_usage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.disk_usage" title="Link to this definition">¶</a></dt>
<dd><p>Return disk usage statistics about the given path as a <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a>
with the attributes <em>total</em>, <em>used</em> and <em>free</em>, which are the amount of
total, used and free space, in bytes. <em>path</em> may be a file or a
directory.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Unix filesystems, <em>path</em> must point to a path within a <strong>mounted</strong>
filesystem partition. On those platforms, CPython doesn’t attempt to
retrieve disk usage information from non-mounted filesystems.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>On Windows, <em>path</em> can now be a file or directory.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.chown">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">chown</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.chown" title="Link to this definition">¶</a></dt>
<dd><p>Change owner <em>user</em> and/or <em>group</em> of the given <em>path</em>.</p>
<p><em>user</em> can be a system user name or a uid; the same applies to <em>group</em>. At
least one argument is required.</p>
<p>See also <a class="reference internal" href="os.html#os.chown" title="os.chown"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chown()</span></code></a>, the underlying function.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.chown</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">path</span></code>, <code class="docutils literal notranslate"><span class="pre">user</span></code>, <code class="docutils literal notranslate"><span class="pre">group</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.which">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">which</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">os.F_OK</span> <span class="pre">|</span> <span class="pre">os.X_OK</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.which" title="Link to this definition">¶</a></dt>
<dd><p>Return the path to an executable which would be run if the given <em>cmd</em> was
called.  If no <em>cmd</em> would be called, return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p><em>mode</em> is a permission mask passed to <a class="reference internal" href="os.html#os.access" title="os.access"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.access()</span></code></a>, by default
determining if the file exists and executable.</p>
<p>When no <em>path</em> is specified, the results of <a class="reference internal" href="os.html#os.environ" title="os.environ"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.environ()</span></code></a> are used,
returning either the “PATH” value or a fallback of <a class="reference internal" href="os.html#os.defpath" title="os.defpath"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.defpath</span></code></a>.</p>
<p>On Windows, the current directory is prepended to the <em>path</em> if <em>mode</em> does
not include <code class="docutils literal notranslate"><span class="pre">os.X_OK</span></code>. When the <em>mode</em> does include <code class="docutils literal notranslate"><span class="pre">os.X_OK</span></code>, the
Windows API <code class="docutils literal notranslate"><span class="pre">NeedCurrentDirectoryForExePathW</span></code> will be consulted to
determine if the current directory should be prepended to <em>path</em>. To avoid
consulting the current working directory for executables: set the environment
variable <code class="docutils literal notranslate"><span class="pre">NoDefaultCurrentDirectoryInExePath</span></code>.</p>
<p>Also on Windows, the <code class="docutils literal notranslate"><span class="pre">PATHEXT</span></code> variable is used to resolve commands
that may not already include an extension. For example, if you call
<code class="docutils literal notranslate"><span class="pre">shutil.which(&quot;python&quot;)</span></code>, <a class="reference internal" href="#shutil.which" title="shutil.which"><code class="xref py py-func docutils literal notranslate"><span class="pre">which()</span></code></a> will search <code class="docutils literal notranslate"><span class="pre">PATHEXT</span></code>
to know that it should look for <code class="docutils literal notranslate"><span class="pre">python.exe</span></code> within the <em>path</em>
directories. For example, on Windows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">shutil</span><span class="o">.</span><span class="n">which</span><span class="p">(</span><span class="s2">&quot;python&quot;</span><span class="p">)</span>
<span class="go">&#39;C:\\Python33\\python.EXE&#39;</span>
</pre></div>
</div>
<p>This is also applied when <em>cmd</em> is a path that contains a directory
component:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&gt;&gt;</span> <span class="n">shutil</span><span class="o">.</span><span class="n">which</span><span class="p">(</span><span class="s2">&quot;C:</span><span class="se">\\</span><span class="s2">Python33</span><span class="se">\\</span><span class="s2">python&quot;</span><span class="p">)</span>
<span class="s1">&#39;C:</span><span class="se">\\</span><span class="s1">Python33</span><span class="se">\\</span><span class="s1">python.EXE&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> type is now accepted.  If <em>cmd</em> type is
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>, the result type is also <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>On Windows, the current directory is no longer prepended to the search
path if <em>mode</em> includes <code class="docutils literal notranslate"><span class="pre">os.X_OK</span></code> and WinAPI
<code class="docutils literal notranslate"><span class="pre">NeedCurrentDirectoryForExePathW(cmd)</span></code> is false, else the current
directory is prepended even if it is already in the search path;
<code class="docutils literal notranslate"><span class="pre">PATHEXT</span></code> is used now even when <em>cmd</em> includes a directory component
or ends with an extension that is in <code class="docutils literal notranslate"><span class="pre">PATHEXT</span></code>; and filenames that
have no extension can now be found.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12.1: </span>On Windows, if <em>mode</em> includes <code class="docutils literal notranslate"><span class="pre">os.X_OK</span></code>, executables with an
extension in <code class="docutils literal notranslate"><span class="pre">PATHEXT</span></code> will be preferred over executables without a
matching extension.
This brings behavior closer to that of Python 3.11.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="shutil.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#shutil.Error" title="Link to this definition">¶</a></dt>
<dd><p>This exception collects exceptions that are raised during a multi-file
operation. For <a class="reference internal" href="#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a>, the exception argument is a list of 3-tuples
(<em>srcname</em>, <em>dstname</em>, <em>exception</em>).</p>
</dd></dl>

<section id="platform-dependent-efficient-copy-operations">
<span id="shutil-platform-dependent-efficient-copy-operations"></span><h3>Platform-dependent efficient copy operations<a class="headerlink" href="#platform-dependent-efficient-copy-operations" title="Link to this heading">¶</a></h3>
<p>Starting from Python 3.8, all functions involving a file copy
(<a class="reference internal" href="#shutil.copyfile" title="shutil.copyfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">copyfile()</span></code></a>, <a class="reference internal" href="#shutil.copy" title="shutil.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a>, <a class="reference internal" href="#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy2()</span></code></a>,
<a class="reference internal" href="#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a>, and <a class="reference internal" href="#shutil.move" title="shutil.move"><code class="xref py py-func docutils literal notranslate"><span class="pre">move()</span></code></a>) may use
platform-specific “fast-copy” syscalls in order to copy the file more
efficiently (see <a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=33671">bpo-33671</a>).
“fast-copy” means that the copying operation occurs within the kernel, avoiding
the use of userspace buffers in Python as in “<code class="docutils literal notranslate"><span class="pre">outfd.write(infd.read())</span></code>”.</p>
<p>On macOS <a class="reference external" href="http://www.manpagez.com/man/3/copyfile/">fcopyfile</a> is used to copy the file content (not metadata).</p>
<p>On Linux <a class="reference internal" href="os.html#os.sendfile" title="os.sendfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.sendfile()</span></code></a> is used.</p>
<p>On Windows <a class="reference internal" href="#shutil.copyfile" title="shutil.copyfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.copyfile()</span></code></a> uses a bigger default buffer size (1 MiB
instead of 64 KiB) and a <a class="reference internal" href="stdtypes.html#memoryview" title="memoryview"><code class="xref py py-func docutils literal notranslate"><span class="pre">memoryview()</span></code></a>-based variant of
<a class="reference internal" href="#shutil.copyfileobj" title="shutil.copyfileobj"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.copyfileobj()</span></code></a> is used.</p>
<p>If the fast-copy operation fails and no data was written in the destination
file then shutil will silently fallback on using less efficient
<a class="reference internal" href="#shutil.copyfileobj" title="shutil.copyfileobj"><code class="xref py py-func docutils literal notranslate"><span class="pre">copyfileobj()</span></code></a> function internally.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8.</span></p>
</div>
</section>
<section id="copytree-example">
<span id="shutil-copytree-example"></span><h3>copytree example<a class="headerlink" href="#copytree-example" title="Link to this heading">¶</a></h3>
<p>An example that uses the <a class="reference internal" href="#shutil.ignore_patterns" title="shutil.ignore_patterns"><code class="xref py py-func docutils literal notranslate"><span class="pre">ignore_patterns()</span></code></a> helper:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">shutil</span> <span class="kn">import</span> <span class="n">copytree</span><span class="p">,</span> <span class="n">ignore_patterns</span>

<span class="n">copytree</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">destination</span><span class="p">,</span> <span class="n">ignore</span><span class="o">=</span><span class="n">ignore_patterns</span><span class="p">(</span><span class="s1">&#39;*.pyc&#39;</span><span class="p">,</span> <span class="s1">&#39;tmp*&#39;</span><span class="p">))</span>
</pre></div>
</div>
<p>This will copy everything except <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files and files or directories whose
name starts with <code class="docutils literal notranslate"><span class="pre">tmp</span></code>.</p>
<p>Another example that uses the <em>ignore</em> argument to add a logging call:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">shutil</span> <span class="kn">import</span> <span class="n">copytree</span>
<span class="kn">import</span> <span class="nn">logging</span>

<span class="k">def</span> <span class="nf">_logpath</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">names</span><span class="p">):</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s1">&#39;Working in </span><span class="si">%s</span><span class="s1">&#39;</span><span class="p">,</span> <span class="n">path</span><span class="p">)</span>
    <span class="k">return</span> <span class="p">[]</span>   <span class="c1"># nothing will be ignored</span>

<span class="n">copytree</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">destination</span><span class="p">,</span> <span class="n">ignore</span><span class="o">=</span><span class="n">_logpath</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="rmtree-example">
<span id="shutil-rmtree-example"></span><h3>rmtree example<a class="headerlink" href="#rmtree-example" title="Link to this heading">¶</a></h3>
<p>This example shows how to remove a directory tree on Windows where some
of the files have their read-only bit set. It uses the onexc callback
to clear the readonly bit and reattempt the remove. Any subsequent failure
will propagate.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span><span class="o">,</span> <span class="nn">stat</span>
<span class="kn">import</span> <span class="nn">shutil</span>

<span class="k">def</span> <span class="nf">remove_readonly</span><span class="p">(</span><span class="n">func</span><span class="p">,</span> <span class="n">path</span><span class="p">,</span> <span class="n">_</span><span class="p">):</span>
    <span class="s2">&quot;Clear the readonly bit and reattempt the removal&quot;</span>
    <span class="n">os</span><span class="o">.</span><span class="n">chmod</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">stat</span><span class="o">.</span><span class="n">S_IWRITE</span><span class="p">)</span>
    <span class="n">func</span><span class="p">(</span><span class="n">path</span><span class="p">)</span>

<span class="n">shutil</span><span class="o">.</span><span class="n">rmtree</span><span class="p">(</span><span class="n">directory</span><span class="p">,</span> <span class="n">onexc</span><span class="o">=</span><span class="n">remove_readonly</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="archiving-operations">
<span id="id1"></span><h2>Archiving operations<a class="headerlink" href="#archiving-operations" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added support for the <em>xztar</em> format.</p>
</div>
<p>High-level utilities to create and read compressed and archived files are also
provided.  They rely on the <a class="reference internal" href="zipfile.html#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> and <a class="reference internal" href="tarfile.html#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> modules.</p>
<dl class="py function">
<dt class="sig sig-object py" id="shutil.make_archive">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">make_archive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">base_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">root_dir</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">base_dir</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">verbose</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">dry_run</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">owner</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">logger</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.make_archive" title="Link to this definition">¶</a></dt>
<dd><p>Create an archive file (such as zip or tar) and return its name.</p>
<p><em>base_name</em> is the name of the file to create, including the path, minus
any format-specific extension.</p>
<p><em>format</em> is the archive format: one of
“zip” (if the <a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module is available), “tar”, “gztar” (if the
<a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module is available), “bztar” (if the <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> module is
available), or “xztar” (if the <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> module is available).</p>
<p><em>root_dir</em> is a directory that will be the root directory of the
archive, all paths in the archive will be relative to it; for example,
we typically chdir into <em>root_dir</em> before creating the archive.</p>
<p><em>base_dir</em> is the directory where we start archiving from;
i.e. <em>base_dir</em> will be the common prefix of all files and
directories in the archive.  <em>base_dir</em> must be given relative
to <em>root_dir</em>.  See <a class="reference internal" href="#shutil-archiving-example-with-basedir"><span class="std std-ref">Archiving example with base_dir</span></a> for how to
use <em>base_dir</em> and <em>root_dir</em> together.</p>
<p><em>root_dir</em> and <em>base_dir</em> both default to the current directory.</p>
<p>If <em>dry_run</em> is true, no archive is created, but the operations that would be
executed are logged to <em>logger</em>.</p>
<p><em>owner</em> and <em>group</em> are used when creating a tar archive. By default,
uses the current owner and group.</p>
<p><em>logger</em> must be an object compatible with <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0282/"><strong>PEP 282</strong></a>, usually an instance of
<a class="reference internal" href="logging.html#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">logging.Logger</span></code></a>.</p>
<p>The <em>verbose</em> argument is unused and deprecated.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.make_archive</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">base_name</span></code>, <code class="docutils literal notranslate"><span class="pre">format</span></code>, <code class="docutils literal notranslate"><span class="pre">root_dir</span></code>, <code class="docutils literal notranslate"><span class="pre">base_dir</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function is not thread-safe when custom archivers registered
with <a class="reference internal" href="#shutil.register_archive_format" title="shutil.register_archive_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_archive_format()</span></code></a> do not support the <em>root_dir</em>
argument.  In this case it
temporarily changes the current working directory of the process
to <em>root_dir</em> to perform archiving.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The modern pax (POSIX.1-2001) format is now used instead of
the legacy GNU format for archives created with <code class="docutils literal notranslate"><span class="pre">format=&quot;tar&quot;</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10.6: </span>This function is now made thread-safe during creation of standard
<code class="docutils literal notranslate"><span class="pre">.zip</span></code> and tar archives.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.get_archive_formats">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">get_archive_formats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.get_archive_formats" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of supported formats for archiving.
Each element of the returned sequence is a tuple <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">description)</span></code>.</p>
<p>By default <a class="reference internal" href="#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> provides these formats:</p>
<ul class="simple">
<li><p><em>zip</em>: ZIP file (if the <a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module is available).</p></li>
<li><p><em>tar</em>: Uncompressed tar file. Uses POSIX.1-2001 pax format for new archives.</p></li>
<li><p><em>gztar</em>: gzip’ed tar-file (if the <a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module is available).</p></li>
<li><p><em>bztar</em>: bzip2’ed tar-file (if the <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> module is available).</p></li>
<li><p><em>xztar</em>: xz’ed tar-file (if the <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> module is available).</p></li>
</ul>
<p>You can register new formats or provide your own archiver for any existing
formats, by using <a class="reference internal" href="#shutil.register_archive_format" title="shutil.register_archive_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_archive_format()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.register_archive_format">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">register_archive_format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">extra_args</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">description</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.register_archive_format" title="Link to this definition">¶</a></dt>
<dd><p>Register an archiver for the format <em>name</em>.</p>
<p><em>function</em> is the callable that will be used to unpack archives. The callable
will receive the <em>base_name</em> of the file to create, followed by the
<em>base_dir</em> (which defaults to <a class="reference internal" href="os.html#os.curdir" title="os.curdir"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.curdir</span></code></a>) to start archiving from.
Further arguments are passed as keyword arguments: <em>owner</em>, <em>group</em>,
<em>dry_run</em> and <em>logger</em> (as passed in <a class="reference internal" href="#shutil.make_archive" title="shutil.make_archive"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_archive()</span></code></a>).</p>
<p>If <em>function</em> has the custom attribute <code class="docutils literal notranslate"><span class="pre">function.supports_root_dir</span></code> set to <code class="docutils literal notranslate"><span class="pre">True</span></code>,
the <em>root_dir</em> argument is passed as a keyword argument.
Otherwise the current working directory of the process is temporarily
changed to <em>root_dir</em> before calling <em>function</em>.
In this case <a class="reference internal" href="#shutil.make_archive" title="shutil.make_archive"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_archive()</span></code></a> is not thread-safe.</p>
<p>If given, <em>extra_args</em> is a sequence of <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code> pairs that will be
used as extra keywords arguments when the archiver callable is used.</p>
<p><em>description</em> is used by <a class="reference internal" href="#shutil.get_archive_formats" title="shutil.get_archive_formats"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_archive_formats()</span></code></a> which returns the
list of archivers.  Defaults to an empty string.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added support for functions supporting the <em>root_dir</em> argument.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.unregister_archive_format">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">unregister_archive_format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.unregister_archive_format" title="Link to this definition">¶</a></dt>
<dd><p>Remove the archive format <em>name</em> from the list of supported formats.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.unpack_archive">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">unpack_archive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">extract_dir</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">filter</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.unpack_archive" title="Link to this definition">¶</a></dt>
<dd><p>Unpack an archive. <em>filename</em> is the full path of the archive.</p>
<p><em>extract_dir</em> is the name of the target directory where the archive is
unpacked. If not provided, the current working directory is used.</p>
<p><em>format</em> is the archive format: one of “zip”, “tar”, “gztar”, “bztar”, or
“xztar”.  Or any other format registered with
<a class="reference internal" href="#shutil.register_unpack_format" title="shutil.register_unpack_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_unpack_format()</span></code></a>.  If not provided, <a class="reference internal" href="#shutil.unpack_archive" title="shutil.unpack_archive"><code class="xref py py-func docutils literal notranslate"><span class="pre">unpack_archive()</span></code></a>
will use the archive file name extension and see if an unpacker was
registered for that extension.  In case none is found,
a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p>The keyword-only <em>filter</em> argument is passed to the underlying unpacking
function. For zip files, <em>filter</em> is not accepted.
For tar files, it is recommended to set it to <code class="docutils literal notranslate"><span class="pre">'data'</span></code>,
unless using features specific to tar and UNIX-like filesystems.
(See <a class="reference internal" href="tarfile.html#tarfile-extraction-filter"><span class="std std-ref">Extraction filters</span></a> for details.)
The <code class="docutils literal notranslate"><span class="pre">'data'</span></code> filter will become the default for tar files
in Python 3.14.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">shutil.unpack_archive</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">filename</span></code>, <code class="docutils literal notranslate"><span class="pre">extract_dir</span></code>, <code class="docutils literal notranslate"><span class="pre">format</span></code>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Never extract archives from untrusted sources without prior inspection.
It is possible that files are created outside of the path specified in
the <em>extract_dir</em> argument, e.g. members that have absolute filenames
starting with “/” or filenames with two dots “..”.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> for <em>filename</em> and <em>extract_dir</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>filter</em> argument.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.register_unpack_format">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">register_unpack_format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extensions</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">extra_args</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">description</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.register_unpack_format" title="Link to this definition">¶</a></dt>
<dd><p>Registers an unpack format. <em>name</em> is the name of the format and
<em>extensions</em> is a list of extensions corresponding to the format, like
<code class="docutils literal notranslate"><span class="pre">.zip</span></code> for Zip files.</p>
<p><em>function</em> is the callable that will be used to unpack archives. The
callable will receive:</p>
<ul class="simple">
<li><p>the path of the archive, as a positional argument;</p></li>
<li><p>the directory the archive must be extracted to, as a positional argument;</p></li>
<li><p>possibly a <em>filter</em> keyword argument, if it was given to
<a class="reference internal" href="#shutil.unpack_archive" title="shutil.unpack_archive"><code class="xref py py-func docutils literal notranslate"><span class="pre">unpack_archive()</span></code></a>;</p></li>
<li><p>additional keyword arguments, specified by <em>extra_args</em> as a sequence
of <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code> tuples.</p></li>
</ul>
<p><em>description</em> can be provided to describe the format, and will be returned
by the <a class="reference internal" href="#shutil.get_unpack_formats" title="shutil.get_unpack_formats"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_unpack_formats()</span></code></a> function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.unregister_unpack_format">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">unregister_unpack_format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.unregister_unpack_format" title="Link to this definition">¶</a></dt>
<dd><p>Unregister an unpack format. <em>name</em> is the name of the format.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="shutil.get_unpack_formats">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">get_unpack_formats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#shutil.get_unpack_formats" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of all registered formats for unpacking.
Each element of the returned sequence is a tuple
<code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">extensions,</span> <span class="pre">description)</span></code>.</p>
<p>By default <a class="reference internal" href="#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> provides these formats:</p>
<ul class="simple">
<li><p><em>zip</em>: ZIP file (unpacking compressed files works only if the corresponding
module is available).</p></li>
<li><p><em>tar</em>: uncompressed tar file.</p></li>
<li><p><em>gztar</em>: gzip’ed tar-file (if the <a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module is available).</p></li>
<li><p><em>bztar</em>: bzip2’ed tar-file (if the <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> module is available).</p></li>
<li><p><em>xztar</em>: xz’ed tar-file (if the <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> module is available).</p></li>
</ul>
<p>You can register new formats or provide your own unpacker for any existing
formats, by using <a class="reference internal" href="#shutil.register_unpack_format" title="shutil.register_unpack_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_unpack_format()</span></code></a>.</p>
</dd></dl>

<section id="archiving-example">
<span id="shutil-archiving-example"></span><h3>Archiving example<a class="headerlink" href="#archiving-example" title="Link to this heading">¶</a></h3>
<p>In this example, we create a gzip’ed tar-file archive containing all files
found in the <code class="file docutils literal notranslate"><span class="pre">.ssh</span></code> directory of the user:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">shutil</span> <span class="kn">import</span> <span class="n">make_archive</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">archive_name</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="s1">&#39;~&#39;</span><span class="p">,</span> <span class="s1">&#39;myarchive&#39;</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">root_dir</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="s1">&#39;~&#39;</span><span class="p">,</span> <span class="s1">&#39;.ssh&#39;</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">make_archive</span><span class="p">(</span><span class="n">archive_name</span><span class="p">,</span> <span class="s1">&#39;gztar&#39;</span><span class="p">,</span> <span class="n">root_dir</span><span class="p">)</span>
<span class="go">&#39;/Users/<USER>/myarchive.tar.gz&#39;</span>
</pre></div>
</div>
<p>The resulting archive contains:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>tar<span class="w"> </span>-tzvf<span class="w"> </span>/Users/<USER>/myarchive.tar.gz
<span class="go">drwx------ tarek/staff       0 2010-02-01 16:23:40 ./</span>
<span class="go">-rw-r--r-- tarek/staff     609 2008-06-09 13:26:54 ./authorized_keys</span>
<span class="go">-rwxr-xr-x tarek/staff      65 2008-06-09 13:26:54 ./config</span>
<span class="go">-rwx------ tarek/staff     668 2008-06-09 13:26:54 ./id_dsa</span>
<span class="go">-rwxr-xr-x tarek/staff     609 2008-06-09 13:26:54 ./id_dsa.pub</span>
<span class="go">-rw------- tarek/staff    1675 2008-06-09 13:26:54 ./id_rsa</span>
<span class="go">-rw-r--r-- tarek/staff     397 2008-06-09 13:26:54 ./id_rsa.pub</span>
<span class="go">-rw-r--r-- tarek/staff   37192 2010-02-06 18:23:10 ./known_hosts</span>
</pre></div>
</div>
</section>
<section id="archiving-example-with-base-dir">
<span id="shutil-archiving-example-with-basedir"></span><h3>Archiving example with <em>base_dir</em><a class="headerlink" href="#archiving-example-with-base-dir" title="Link to this heading">¶</a></h3>
<p>In this example, similar to the <a class="reference internal" href="#shutil-archiving-example">one above</a>,
we show how to use <a class="reference internal" href="#shutil.make_archive" title="shutil.make_archive"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_archive()</span></code></a>, but this time with the usage of
<em>base_dir</em>.  We now have the following directory structure:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>tree<span class="w"> </span>tmp
<span class="go">tmp</span>
<span class="go">└── root</span>
<span class="go">    └── structure</span>
<span class="go">        ├── content</span>
<span class="go">            └── please_add.txt</span>
<span class="go">        └── do_not_add.txt</span>
</pre></div>
</div>
<p>In the final archive, <code class="file docutils literal notranslate"><span class="pre">please_add.txt</span></code> should be included, but
<code class="file docutils literal notranslate"><span class="pre">do_not_add.txt</span></code> should not.  Therefore we use the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">shutil</span> <span class="kn">import</span> <span class="n">make_archive</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">archive_name</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="s1">&#39;~&#39;</span><span class="p">,</span> <span class="s1">&#39;myarchive&#39;</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">make_archive</span><span class="p">(</span>
<span class="gp">... </span>    <span class="n">archive_name</span><span class="p">,</span>
<span class="gp">... </span>    <span class="s1">&#39;tar&#39;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">root_dir</span><span class="o">=</span><span class="s1">&#39;tmp/root&#39;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">base_dir</span><span class="o">=</span><span class="s1">&#39;structure/content&#39;</span><span class="p">,</span>
<span class="gp">... </span><span class="p">)</span>
<span class="go">&#39;/Users/<USER>/my_archive.tar&#39;</span>
</pre></div>
</div>
<p>Listing the files in the resulting archive gives us:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile<span class="w"> </span>-l<span class="w"> </span>/Users/<USER>/myarchive.tar
<span class="go">structure/content/</span>
<span class="go">structure/content/please_add.txt</span>
</pre></div>
</div>
</section>
</section>
<section id="querying-the-size-of-the-output-terminal">
<h2>Querying the size of the output terminal<a class="headerlink" href="#querying-the-size-of-the-output-terminal" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="shutil.get_terminal_size">
<span class="sig-prename descclassname"><span class="pre">shutil.</span></span><span class="sig-name descname"><span class="pre">get_terminal_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fallback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">(columns,</span> <span class="pre">lines)</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#shutil.get_terminal_size" title="Link to this definition">¶</a></dt>
<dd><p>Get the size of the terminal window.</p>
<p>For each of the two dimensions, the environment variable, <code class="docutils literal notranslate"><span class="pre">COLUMNS</span></code>
and <code class="docutils literal notranslate"><span class="pre">LINES</span></code> respectively, is checked. If the variable is defined and
the value is a positive integer, it is used.</p>
<p>When <code class="docutils literal notranslate"><span class="pre">COLUMNS</span></code> or <code class="docutils literal notranslate"><span class="pre">LINES</span></code> is not defined, which is the common case,
the terminal connected to <a class="reference internal" href="sys.html#sys.__stdout__" title="sys.__stdout__"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.__stdout__</span></code></a> is queried
by invoking <a class="reference internal" href="os.html#os.get_terminal_size" title="os.get_terminal_size"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.get_terminal_size()</span></code></a>.</p>
<p>If the terminal size cannot be successfully queried, either because
the system doesn’t support querying, or because we are not
connected to a terminal, the value given in <code class="docutils literal notranslate"><span class="pre">fallback</span></code> parameter
is used. <code class="docutils literal notranslate"><span class="pre">fallback</span></code> defaults to <code class="docutils literal notranslate"><span class="pre">(80,</span> <span class="pre">24)</span></code> which is the default
size used by many terminal emulators.</p>
<p>The value returned is a named tuple of type <a class="reference internal" href="os.html#os.terminal_size" title="os.terminal_size"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.terminal_size</span></code></a>.</p>
<p>See also: The Single UNIX Specification, Version 2,
<a class="reference external" href="https://pubs.opengroup.org/onlinepubs/7908799/xbd/envvar.html#tag_002_003">Other Environment Variables</a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <code class="docutils literal notranslate"><span class="pre">fallback</span></code> values are also used if <a class="reference internal" href="os.html#os.get_terminal_size" title="os.get_terminal_size"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.get_terminal_size()</span></code></a>
returns zeroes.</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code> — High-level file operations</a><ul>
<li><a class="reference internal" href="#directory-and-files-operations">Directory and files operations</a><ul>
<li><a class="reference internal" href="#platform-dependent-efficient-copy-operations">Platform-dependent efficient copy operations</a></li>
<li><a class="reference internal" href="#copytree-example">copytree example</a></li>
<li><a class="reference internal" href="#rmtree-example">rmtree example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#archiving-operations">Archiving operations</a><ul>
<li><a class="reference internal" href="#archiving-example">Archiving example</a></li>
<li><a class="reference internal" href="#archiving-example-with-base-dir">Archiving example with <em>base_dir</em></a></li>
</ul>
</li>
<li><a class="reference internal" href="#querying-the-size-of-the-output-terminal">Querying the size of the output terminal</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="linecache.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">linecache</span></code> — Random access to text lines</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="persistence.html"
                          title="next chapter">Data Persistence</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/shutil.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="persistence.html" title="Data Persistence"
             >next</a> |</li>
        <li class="right" >
          <a href="linecache.html" title="linecache — Random access to text lines"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" >File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code> — High-level file operations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>