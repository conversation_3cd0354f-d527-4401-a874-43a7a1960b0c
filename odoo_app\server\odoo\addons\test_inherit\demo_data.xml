<odoo>
    <data>
        <record id="mother_a" model="test.inherit.mother">
            <field name="name">Mother A</field>
            <field name="state">a</field>
        </record>

        <record id="mother_b" model="test.inherit.mother">
            <field name="name">Mother B</field>
            <field name="state">b</field>
        </record>

        <record id="mother_c" model="test.inherit.mother">
            <field name="name">Mother C</field>
            <field name="state">c</field>
        </record>

        <record id="mother_d" model="test.inherit.mother">
            <field name="name">Mother D</field>
            <field name="state">d</field>
        </record>
    </data>
</odoo>
