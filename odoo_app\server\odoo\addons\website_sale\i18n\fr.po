# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" catégorie."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"\"Optional\" allows guests to register from the order confirmation email to "
"track their order."
msgstr ""
"En cochant \"Optionnel\", vos clients peuvent s'inscrire à partir de "
"l'e-mail de confirmation de la commande pour suivre leur commande."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s avis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s avis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "&amp; Shipping"
msgstr "&amp; Expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "&amp;nbsp;(<i>Your shipping address will be requested later)</i>"
msgstr "&amp;nbsp;(<i>Votre adresse d'expédition sera demandée plus tard)</i>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"
msgstr "&amp;nbsp;article(s)&amp;nbsp;-&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. Affichage des résultats pour '"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__100_pc
msgid "100 %"
msgstr "100 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "100 percent"
msgstr "100 pour cent"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__50_pc
msgid "50 %"
msgstr "50 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "50 percent"
msgstr "50 pour cent"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__66_pc
msgid "66 %"
msgstr "66 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "66 percent"
msgstr "66 pour cent"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<b class=\"w-100\">Order summary</b>"
msgstr "<b class=\"w-100\">Résumé de la commande</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
msgid "<b>Categories</b>"
msgstr "<b>Catégories</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Communication : </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
msgid "<b>Price Range</b>"
msgstr "<b>Fourchette de prix</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Pricelist</b>"
msgstr "<b>Liste de prix</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<b>Shipping: </b>"
msgstr "<b>Expédition : </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Sort By</b>"
msgstr "<b>Trier par</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_tags
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Tags</b>"
msgstr "<b>Étiquettes</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                Garantie satisfait ou remboursé de 30 jours<br/>\n"
"Expédition : 2-3 jours ouvrables"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Return to shipping"
msgstr ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Retour à l'expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Buy now"
msgstr ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Acheter maintenant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart
msgid "<i class=\"fa fa-cart-plus me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-cart-plus me-2\"/>Ajouter au panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Buy Now"
msgstr ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Acheter maintenant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Site Web\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Modifier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.row_addresses
msgid ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-inline\">Add "
"address</span>"
msgstr ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-inline\">Ajouter "
"une adresse</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print me-2\"/>Print"
msgstr "<i class=\"fa fa-print me-2\"/>Imprimer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_re_order_btn
msgid ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Order Again"
msgstr ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Recommander"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Add to cart"
msgstr ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Ajouter au panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<i class=\"fw-light fa fa-angle-left me-2\"/>Discard"
msgstr "<i class=\"fw-light fa fa-angle-left me-2\"/>Ignorer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<option value=\"\" selected=\"true\">-</option>"
msgstr "<option value=\"\" selected=\"true\">-</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Pays...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">État / Province...</option>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Vous trouverez tous les paniers abandonnés ici, par exemple les paniers générés par les visiteurs de votre site web il y a plus d'une heure, qui n'ont pas encore été confirmés.</p>\n"
"                        <p>Vous devez envoyer un e-mail aux clients pour les encourager !</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<small class=\"d-none d-lg-inline text-muted\">Sort By:</small>"
msgstr "<small class=\"d-none d-lg-inline text-muted\">Trier par :</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">Le changement de nom ou de numéro de "
"TVA n'est pas autorisé quand des documents ont été créés à votre compte. "
"Veuillez directement nous contacter pour cette opération.</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid ""
"<small class=\"mx-auto\"><b>Clear Filters</b></small>\n"
"                        <i class=\"oi oi-close\"/>"
msgstr ""
"<small class=\"mx-auto\"><b>Supprimer les filtres</b></small>\n"
"                        <i class=\"oi oi-close\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Add to cart</span>"
msgstr ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Ajouter au panier</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Les valeurs définies ici sont "
"spécifiques au site Web.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "<span class=\"mx-2 o_wsale_ppr_by\">by</span>"
msgstr "<span class=\"mx-2 o_wsale_ppr_by\">par</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_order_location\">\n"
"                    <b class=\"o_order_location_name\"/>\n"
"                    <br/>\n"
"                    <i class=\"o_order_location_address\"/>\n"
"                </span>\n"
"                <span class=\"fa fa-times ms-2 o_remove_order_location\" aria-label=\"Remove this location\" title=\"Remove this location\"/>"
msgstr ""
"<span class=\"o_order_location\">\n"
"                    <b class=\"o_order_location_name\"/>\n"
"                    <br/>\n"
"                    <i class=\"o_order_location_address\"/>\n"
"                </span>\n"
"                <span class=\"fa fa-times ms-2 o_remove_order_location\" aria-label=\"Supprimer cet emplacement\" title=\"Remove this location\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">Select to compute delivery rate</span>"
msgstr ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">Sélectionner pour calculer le tarif de livraison</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Previous\" title=\"Précédent\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Next\" title=\"Next\"/>"
msgstr ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Next\" title=\"Suivant\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "<span class=\"px-3\">or</span>"
msgstr "<span class=\"px-3\">ou</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Donnez vous votre "
"feedback</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Télécharger un document</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">Votre référence</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Based"
" on variants</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Basé "
"sur les variantes</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "<span class=\"visually-hidden\">filters active</span>"
msgstr "<span class=\"visually-hidden\">filtres actifs</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>Commande</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_kanban
msgid "<span>Show on product page</span>"
msgstr "<span>Afficher sur la page produit</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>Malheureusement, il est impossible de confirmer votre commande, puisque votre paiement ne couvre pas le montant total de votre panier.\n"
"                        Veuillez contacter le responsable de la boutique pour avoir plus d'informations.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>Prévisualiser la vidéo</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong><i class=\"oi oi-arrow-right\"/> View alternatives</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Voir les alternatives</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong>No suitable payment option could be found.</strong><br/>"
msgstr ""
"<strong>Aucune option de paiement appropriée n'a pu être "
"trouvée.</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Total :</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<strong>Warning!</strong>"
msgstr "<strong>Attention !</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">IL Y A DES ARTICLES DANS VOTRE PANIER.</h1>\n"
"                    Souhaitez-vous achever vos achats ?<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Combinaison de bureau</strong><br><t t-out=\"line.name or ''\">[FURN_7800] Combinaison de bureau, noir-brun : chaise + bureau + armoire.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Unités</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Reprendre la commande\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>Merci d'avoir fait vos achats chez <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "<u>Terms and Conditions</u>"
msgstr "<u>Conditions générales</u>"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Une description du produit que vous souhaitez communiquer à vos clients. "
"Cette description sera copiée sur chaque commande client, bon de livraison "
"et avoir/facture client"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"A detailed, formatted description to promote your product on this page. Use "
"'/' to discover more features."
msgstr ""
"Une description détaillée et formatée pour promouvoir votre produit sur "
"cette page. Utilisez '/' pour découvrir d'autres fonctionnalités."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"Un produit peut être soit un produit physique, soit un service que vous "
"vendez à vos clients."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "Une brève description qui apparaîtra également sur les documents."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "Abandonné"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
#: model:ir.model.fields,field_description:website_sale.field_sale_report__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "Panier abandonné"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "Paniers abandonnés"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "Paniers abandonnés à récupérer"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "Délai d'abandon"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__send_abandoned_cart_email
msgid "Abandoned Email"
msgstr "E-mail abandonné"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "À propos des produits de vente croisée"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Accept Terms & Conditions"
msgstr "Accepter les conditions générales"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "Taille de fichier acceptable"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "Accessoires pour le produit"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"Les accessoires apparaissent lorsque le client passe en revue son panier "
"avant de payer (stratégie de vente croisée)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "Produits accessoires "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Action"
msgstr "Action"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add"
msgstr "Ajouter"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Add To Cart"
msgstr "Ajouter au panier"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__add_to_cart_action
#: model:ir.model.fields,field_description:website_sale.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Action ajouter au panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "Ajouter un média"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a customizable form during checkout (after address)"
msgstr ""
"Ajouter un formulaire personnalisable lors du paiement (après l'adresse)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Add a reference price per UoM on products (i.e $/kg), in addition to the "
"sale price"
msgstr ""
"Ajouter un prix de référence par UdM sur les produits (i.e. $/kg), en plus "
"du prix de vente"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a strikethrough price, as a comparison"
msgstr "Ajouter un prix barré, à titre de comparaison"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Add one"
msgstr "Ajouter"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#, python-format
msgid "Add to Cart"
msgstr "Ajouter au panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
msgid "Add to Cart Button"
msgstr "Bouton Ajouter au panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Address"
msgstr "Adresse"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_autocomplete
msgid "Address Autocomplete"
msgstr "Autocomplétion des adresses"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "Tous les produits"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "Toutes les listes de prix"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow customers to pay in person at your stores"
msgstr "Permettre à vos clients de payer en personne dans vos magasins"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr ""
"Autoriser les clients à comparer les produits en se basant sur leurs "
"attributs"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow signed-in users to save product in a wishlist"
msgstr ""
"Permettre aux utilisateurs connectés d'enregistrer le produit dans une liste"
" de souhaits"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "Permettre à l'utilisateur final de choisir cette liste de prix"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow your customer to add products from previous order in their cart."
msgstr ""
"Permettre à vos clients d'ajouter des produits d'une commande précédente à "
"leur panier."

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_alternative_products
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_alternative_products
msgid "Alternative Products"
msgstr "Produits alternatifs"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "Nombre de paniers abandonnés"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "Anonymous express checkout partner for order %s"
msgstr "Partenaire de paiement rapide anonyme pour la commande %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Apartment, suite, etc."
msgstr "Appartement, suite, etc."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Appliquer"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Are you sure you want to delete this badge?"
msgstr "Êtes-vous sûr de vouloir supprimer ce badge ?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment"
msgstr "Assignation"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "Assigner des commandes en ligne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Attributes"
msgstr "Attributs"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Automatically send abandoned checkout emails"
msgstr "Envoyer automatiquement des e-mails en cas de paiement abandonné"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Average Rating"
msgstr "Évaluation moyenne"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Back to cart"
msgstr "Retour au panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Background"
msgstr "Arrière-plan"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Badge"
msgstr "Badge"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Badge Text"
msgstr "Texte du badge"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "Nombre d'unités de base"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "Nom de l'unité de base"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "Prix unitaire de base"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
msgid "Base Units"
msgstr "Unités de base"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "Attention !"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__big
msgid "Big"
msgstr "Grand"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing"
msgstr "Facturation"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Billing address"
msgstr "Adresse de facturation"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "Poubelle"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "Poubelles"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Boostez vos ventes avec plusieurs types de programmes : Bons de réduction, "
"Promotions, Cartes-cadeaux, Fidélité. Des conditions spécifiques peuvent "
"être fixées (produits, clients, montant minimum d'achat, période). Les "
"récompenses peuvent être des remises (% ou montant) ou des produits "
"gratuits."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Both"
msgstr "Les deux"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "Bas"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "Boîte"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "Boîtes"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "Marque"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_contact_us_button_url
msgid "Button URL"
msgstr "Bouton URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Button url"
msgstr "Bouton URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buttons"
msgstr "Boutons"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_buy_now_button
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buy Now"
msgstr "Acheter maintenant"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#, python-format
msgid "Buy now"
msgstr "Acheter maintenant"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "Meubles de rangement"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "L'Image 1024 peut être agrandie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__can_publish
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "Peut publier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cards"
msgstr "Cards"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__carousel
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Carousel"
msgstr "Carrousel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cart"
msgstr "Panier"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "Quantité du panier"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "E-mail de récupération du panier"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "E-mail de récupération du panier déjà envoyé"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "Les paniers sont considérés comme abandonnés après ce délai."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Categories"
msgstr "Catégories"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"Les catégories sont utilisées pour parcourir vos produits via\n"
"l'interface tactile."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Categories:"
msgstr "Catégories :"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "Catégorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "Description de la catégorie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Category:"
msgstr "Catégorie :"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "Chaise"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "Chaises"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"La modification du numéro de TVA n'est pas autorisée une fois que des "
"documents ont déjà été émis pour votre compte. Veuillez nous contacter "
"directement pour effectuer cette opération."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"La modification du nom de la société n'est pas autorisée une fois que des "
"documents ont été créés pour votre compte. Veuillez nous contacter "
"directement pour effectuer cette opération."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Vous ne pouvez pas modifier votre nom si des factures ont déjà été émises "
"pour votre compte. Veuillez nous contacter directement pour effectuer cette "
"modification."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Checkout"
msgstr "Paiement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Checkout Pages"
msgstr "Pages de paiement"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Catégories enfants"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery
msgid "Choose a delivery method"
msgstr "Choisissez un mode de livraison"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "Noël"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Ville"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "Clear Filters"
msgstr "Supprimer les filtres"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"Cliquez sur <i>'Nouveau'</i> dans le coin supérieur droit afin de créer "
"votre premier produit."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "Cliquez ici"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click here to open the reporting menu"
msgstr "Cliquez ici pour ouvrir le menu des rapports"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Save</em> to create the product."
msgstr "Cliquez sur <em>Enregistrer</em> pour créer le produit."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "Cliquez sur ce bouton pour que vos clients puissent le voir."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/cart_notification/cart_notification.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#, python-format
msgid "Close"
msgstr "Fermer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapse Category Recursive"
msgstr "Réduire la catégorie récursive"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Color"
msgstr "Couleur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Columns"
msgstr "Colonnes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid ""
"Comma-separated list of parts of product names, barcodes or internal "
"reference"
msgstr ""
"Liste séparée par des virgules de parties de noms de produits, de codes-"
"barres ou de références internes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Company Name"
msgstr "Nom de la société"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__compare_list_price
msgid "Compare to Price"
msgstr "Comparer au prix"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_price_comparison
#: model:res.groups,name:website_sale.group_product_price_comparison
msgid "Comparison Price"
msgstr "Comparaison de prix"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "Composants"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Calculer les frais d'expédition et expédier avec Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Shiprocket"
msgstr "Calculer les frais d'expédition et expédier avec Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Calculer les frais d'expédition et expédier avec DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Calculer les frais d'expédition et expédier avec FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Calculer les frais d'expédition et expédier avec UPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Calculer les frais d'expédition et expédier avec USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Calculer les frais d'expédition et expédier avec bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Calculer les frais d'expédition des commandes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Configure Form"
msgstr "Configurer le formulaire"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Confirm"
msgstr "Confirmer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "Confirm Order"
msgstr "Confirmer la commande"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm order"
msgstr "Confirmer la commande"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "Confirmé"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "Commandes confirmées"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Contact Us"
msgstr "Contactez-nous"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__contact_us_button_url
msgid "Contact Us Button URL"
msgstr "Bouton URL contactez-nous"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Content"
msgstr "Contenu"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Continue checkout"
msgstr "Continuer le paiement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"Continue checkout\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"
msgstr ""
"Continuer le paiement\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Continue shopping"
msgstr "Poursuivre les achats"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "Canapés"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Pays"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Create"
msgstr "Créer"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "Créer un nouveau produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
msgid "Created on"
msgstr "Créé le"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "Date de création"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "Catégorie actuelle ou toutes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "Unité de mesure personnalisée"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Client"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Compte client"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__account_on_checkout
#: model:ir.model.fields,field_description:website_sale.field_website__account_on_checkout
msgid "Customer Accounts"
msgstr "Comptes clients"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "Pays du client"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "Avis client :"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."
msgstr ""
"Le client doit être connecté pour que son adresse e-mail soit connue.     \n"
"\n"
"- Si un client potentiel crée un ou plusieurs paiements abandonnés et puis conclut une vente avant l'envoi de l'e-mail de récupération, l'e-mail ne sera pas envoyé.     \n"
"\n"
"- Si l'utilisateur a envoyé un e-mail de récupération manuellement, l'e-mail ne sera pas envoyé une deuxième fois.     \n"
"\n"
"- Si une erreur est survenue lors du traitement du paiement quand le client a essayé de procéder au paiement, l'e-mail ne sera pas envoyé.     \n"
"\n"
"- Si votre boutique ne propose pas la livraison à l'adresse du client, l'e-mail ne sera pas envoyé.     \n"
"\n"
"- Si aucun des produits qui se trouvent dans le panier ne sont disponibles à l'achat (stock vide par exemple), l'e-mail ne sera pas envoyé.     \n"
"\n"
"- Si tous les produits dans le panier sont gratuits et le client ne visite pas la page d'expédition pour ajouter des frais d'expédition ou si la livraison est également offerte, l'e-mail ne sera pas envoyé."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Customers"
msgstr "Clients"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customize Abandoned Email Template"
msgstr "Personnalisation du modèle d'e-mail abandonné"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_config_settings.py:0
#: code:addons/website_sale/models/res_config_settings.py:0
#, python-format
msgid "Customize Email Templates"
msgstr "Personnalisation des modèles d'e-mail"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "Connecteur DHL Express"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "Modes d'expédition DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr ""
"DÉPOSEZ DES BLOCS DE CONSTRUCTION ICI POUR LES RENDRE DISPONIBLES POUR TOUS "
"LES PRODUITS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default"
msgstr "Par défaut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1/1)"
msgstr "Par défaut (1/1)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "Devise par défaut"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist if any"
msgstr "Liste de prix par défaut, le cas échéant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default Sort"
msgstr "Tri par défaut"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr ""
"Définissez une unité personnalisée à afficher dans le champ Prix par unité "
"de mesure."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "Définir une nouvelle catégorie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete Badge"
msgstr "Supprimer le badge"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery"
msgstr "Livraison"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "Coût de livraison"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__access_point_address
msgid "Delivery Point Address"
msgstr "Adresse du point de livraison"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr ""
"La livraison sera mise à jour après avoir choisi un nouveau mode de "
"livraison"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "Description"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr ""
"Description affichée sur le système d'eCommerce et les devis en ligne."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Description pour les devis en ligne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "Description pour le site web"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "Bureaux"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Détermine l'ordre d'affichage sur l'eCommerce du site web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "Digest"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__disabled
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__disabled
msgid "Disabled (buy as guest)"
msgstr "Désactivé (acheter en tant qu'invité)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Discard"
msgstr "Ignorer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Discount code..."
msgstr "Code de remise..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "Remises, Fidélité & Cartes-Cadeaux"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display Product Prices"
msgstr "Afficher les prix des produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Display Type"
msgstr "Type d'affichage"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"Afficher le prix unitaire de base sur vos pages d'eCommerce. Configurez le "
"prix sur 0 pour le cacher pour ce produit."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "Affiché en bas des pages produits"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"Affiche l'unité personnalisée pour les produits si elle est définie ou "
"l'unité de mesure sélectionnée dans le cas contraire."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Accès non disponible, ne pas tenir compte de cette donnée dans le digest "
"d'e-mail envoyé à l'utilisateur"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "Do you wish to clear your cart before adding products to it?"
msgstr "Voulez-vous vider votre panier avant d'ajouter de nouveaux produits ?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Documents"
msgstr "Documents"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_document.py:0
#, python-format
msgid ""
"Documents shown on product page cannot be restricted to a specific variant"
msgstr ""
"Les documents affichés sur la page produit ne peuvent pas être restreints à "
"une variante spécifique"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "Double-cliquez ici pour choisir une image décrivant votre produit."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "Faites glisser des blocs ici pour personnaliser l'en-tête pour \""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "Glissez le bloc du site web et déposez-le dans votre page."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "Tiroir"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "Tiroirs"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "Champs supplémentaires d'eCommerce"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr ""
"Informations supplémentaires d'eCommerce affichées sur la page produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_form
msgid "E-commerce"
msgstr "eCommerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "Code promotionnel du e-commerce"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "Modes d'expédition Easypost"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Ecommerce: Cart Recovery"
msgstr "Ecommerce : Récupération de panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Edit"
msgstr "Modifier"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "Modifiez le prix de ce produit en cliquant sur le montant."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "Modifier cette adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "E-mail"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "Embed Code"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_delivery
msgid "Enable Shipping"
msgstr "Activer l'expédition"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "Entrez votre nom pour votre nouveau produit"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "Erreur ! Vous ne pouvez pas créer de catégories récursives."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Images"
msgstr "Images extra"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Extra Info"
msgstr "Infos supplémentaires"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "Médias supplémentaires pour le produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Step"
msgstr "Étape supplémentaire"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_extra_checkout_step
msgid "Extra Step During Checkout"
msgstr "Étape supplémentaire lors du paiement"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "Images supplémentaires pour la variante"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "Médias supplémentaires pour la variante"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra info"
msgstr "Infos supplémentaires"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Featured"
msgstr "En vedette"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "Modes d'expédition FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "Champ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "Étiquette de champ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "Nom de champ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Fill"
msgstr "Remplir"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Fill in your address"
msgstr "Complétez votre adresse"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__fiscal_position_id
msgid "Fiscal Position"
msgstr "Position fiscale"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Free"
msgstr "Gratuit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "Depuis le site web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Full name"
msgstr "Nom complet"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "Meubles"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""
"Générer la facture automatiquement lorsque le paiement en ligne est "
"confirmé."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Affiche l'ordre de séquence d'une liste de catégories de produits."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__go_to_cart
#, python-format
msgid "Go to cart"
msgstr "Aller au panier"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__grid
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Grid"
msgstr "Grid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "Regrouper par"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__has_message
msgid "Has Message"
msgstr "A un message"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__none
msgid "Hidden"
msgstr "Masqué"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale
msgid "Hide 'Add To Cart' when price = 0"
msgstr "Cacher 'Ajouter au panier' quand le prix = 0"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Hours."
msgstr "Heures."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "Fichier très volumineux. L'image doit être optimisée/réduite."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "I agree to the"
msgstr "J'accepte les"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_tag_form_view_inherit_website_sale
msgid "If an image is set, the color will not be used on eCommerce."
msgstr ""
"Si une image est définie, la couleur ne sera pas utilisée dans l'eCommerce."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "If product price equals 0, replace 'Add to Cart' by 'Contact us'."
msgstr ""
"Si le prix du produit est égal à 0, remplacer 'Ajouter au panier' par "
"'Contactez-nous'."

#. module: website_sale
#: model:mail.template,description:website_sale.mail_template_sale_cart_recovery
msgid ""
"If the setting is set, sent to authenticated visitors who abandoned their "
"cart"
msgstr ""
"Si le paramètre est défini, envoyé aux visiteurs authentifiés qui ont "
"abandonné leur panier"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"Si vous commandez pour une personne externe, placez votre commande via le "
"backend. Si vous voulez modifier votre nom ou adresse e-mail, faites les "
"modifications dans les paramètres du compte ou contactez votre "
"administrateur."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""
"Si vous pensez qu'il s'agit d'une erreur, veuillez contacter "
"l'administrateur du site web."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_tag__image
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "Image"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "Image 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "Image 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "Image 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "Nom de l'image"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Spacing"
msgstr "Espacement image"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Zoom"
msgstr "Zoom image"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Size"
msgstr "Taille images"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Width"
msgstr "Largeur images"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Instant checkout, instead of adding to cart"
msgstr "Paiement instantané, au lieu d'ajouter au panier"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr ""
"Adresse e-mail incorrecte ! Veuillez saisir une adresse e-mail valable."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid image"
msgstr "Image non valide"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Facturation"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "Politique de facturation"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__is_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "Est publié"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "Émettre des factures aux clients"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr ""
"Il est interdit de modifier une commande client qui n'est pas en statut "
"Brouillon. "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"Il semble qu'un mode de livraison ne soit pas compatible avec votre adresse."
" Veuillez rafraîchir la page et réessayer."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"Il semble qu'il y ait déjà une transaction pour votre commande, vous ne "
"pouvez plus changer le mode de livraison"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#, python-format
msgid "Item(s) added to your cart"
msgstr "Article(s) ajouté(s) à votre panier"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "KPI site web total des ventes en ligne"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "Lampe"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "Lampes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Landscape (4/3)"
msgstr "Paysage (4/3)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "Le mois dernier"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "Dernière commande client en ligne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "Semaine dernière"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "L'année dernière "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Layout"
msgstr "Agencement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "Gauche"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "Laisser le client saisir une adresse d'expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer select a Mondial Relay shipping point"
msgstr "Laisser le client sélectionner un point relais Mondial Relay"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "Créons votre premier produit."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your eCommerce dashboard to get your eCommerce "
"website ready in no time."
msgstr ""
"Jetons maintenant un coup d’œil à votre tableau de bord eCommerce pour "
"lancer votre eCommerce en un rien de temps."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "Ampoule vendue séparément"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__show_line_subtotals_tax_selection
#: model:ir.model.fields,field_description:website_sale.field_website__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "Affichage du sous-total des lignes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Ligne de commande liée"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "List"
msgstr "Liste"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Magnifier on hover"
msgstr "Loupe au survol"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Mail only sent to signed in customers with items available for sale in their"
" cart."
msgstr ""
"Email uniquement envoyé aux clients connectés qui ont des articles "
"disponibles à la vente dans leur panier. "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Main image"
msgstr "Image principale"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr ""
"Gérer les promotions, bons de réduction, cartes de fidélité, cartes-cadeaux "
"& e-wallet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Manage pricelists to apply specific prices per country, customer, products, "
"etc"
msgstr ""
"Gérer les listes de prix pour appliquer des prix spécifiques par pays, "
"client, produits, etc."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__mandatory
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__mandatory
msgid "Mandatory (no guest checkout)"
msgstr "Obligatoire (pas de paiement en tant qu'invité)"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__medium
msgid "Medium"
msgstr "Médium"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_ids
msgid "Messages"
msgstr "Messages"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Mondial Relay"
msgstr "Mondial Relay"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_delivery_mondialrelay
msgid "Mondial Relay Connector"
msgstr "Connecteur Mondial Relay"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to first"
msgstr "Passer au premier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to last"
msgstr "Passer au dernier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to next"
msgstr "Passer au suivant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to previous"
msgstr "Passer au précédent"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "Multimédia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "My Cart"
msgstr "Mon panier"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Name"
msgstr "Nom"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Name (A-Z)"
msgstr "Nom (A-Z)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "Nom court"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "New"
msgstr "Nouveau"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_product_action_add
msgid "New Product"
msgstr "Nouveau produit"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "Nouveau !"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Newest Arrivals"
msgstr "Nouvelles arrivées"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "Nouveaux produits"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "Suivant (flèche de droite)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "No"
msgstr "Non"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "Aucun panier abandonné trouvé"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "No pick-up point available for that shipping address"
msgstr "Aucun point d'enlèvement disponible pour cette adresse d'expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "Aucun produit défini"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in this category."
msgstr "Aucun produit défini dans cette catégorie."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "Il n'y a encore aucune vue de produit pour ce visiteur."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "Aucun résultat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "Aucun résultat pour \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "Aucun résultat trouvé pour '"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Aucun mode d'expédition n'est disponible pour votre commande et adresse de "
"livraison. Veuillez nous contacter pour obtenir plus d'informations."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "No shipping method is selected."
msgstr "Aucun mode d'expédition sélectionné."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__none
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "Aucun"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "Not Published"
msgstr "Non publié"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/sale_variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "Pas disponible avec %s"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "Nombre de paniers abandonnés"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "Nombre de colonnes dans le grid de la boutique"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "Nombre de produits dans le grid de la boutique"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_picking
msgid "On Site Payments & Picking"
msgstr "Paiements & retrait sur site"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "Sur roues"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr ""
"Lorsque vous cliquez sur <b>Enregistrer</b>, votre produit est mis à jour."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "One product might have different attributes (size, color, ...)"
msgstr "Un produit peut avoir différents attributs (taille, couleur, ...)"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "Ventes en ligne"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "Analyse des ventes en ligne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "Services seulement"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"Seuls les sites web de l'entreprise sont autorisés.\n"
"Laissez le champ d'entreprise vide ou sélectionnez un site web de cette entreprise."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid "Open Sale Orders"
msgstr "Ouvrir les commandes clients"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "Open Source eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"Optimisation nécessaire ! Réduisez la taille de l'image ou augmentez les "
"paramètres de compression. "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option for: %s"
msgstr "Option pour : %s"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option: %s"
msgstr "Option : %s"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__optional
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__optional
msgid "Optional"
msgstr "Facultatif"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Options"
msgstr "Options"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "Options liées"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "Ou scannez-moi avec votre application bancaire."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "Date de la commande"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "Les lignes de commandes sont affichées sur le site web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Order overview"
msgstr "Aperçu de la commande"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
msgid "Orders"
msgstr "Commandes"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "Commandes à facturer"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "En rupture de stock"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Catégorie parente"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "Chemin parent"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "Parents et vous"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Pay now"
msgstr "Payer maintenant"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Payment"
msgstr "Paiement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Payment Information"
msgstr "Informations de paiement"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_methods
msgid "Payment Methods"
msgstr "Modes de paiement"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_providers
msgid "Payment Providers"
msgstr "Fournisseurs de paiement"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_token
msgid "Payment Token"
msgstr "Jeton de paiement"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "Jetons de paiement"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "Transactions"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Payment is already being processed."
msgstr "Le paiement est déjà en cours de traitement."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "Système d'ouverture avec une pédale"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Téléphone"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Numéro de téléphone"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pills"
msgstr "Boutons carrés"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "Veuillez indiquer une URL vidéo valide."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "Veuillez poursuivre avec votre panier actuel."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pop-up on Click"
msgstr "Pop-up au clic"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Portrait (4/5)"
msgstr "Portrait (4/5)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Position"
msgstr "Position"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_prevent_zero_price_sale
msgid "Prevent Sale of Zero Priced Product"
msgstr "Prévenir la vente de produits à prix nul"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "Précédent (flèche de gauche)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#, python-format
msgid "Price"
msgstr "Prix"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - High to Low"
msgstr "Prix - Décroissant"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - Low to High"
msgstr "Prix - Croissant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Price Filter"
msgstr "Filtre par prix"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "Prix unitaire"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Liste de prix disponible pour cet eCommerce/site web."

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Liste de prix"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_pricelist
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Listes de prix"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Prices displayed on your eCommerce"
msgstr "Prix affichés sur votre eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Imprimer"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "Traiter la commande une fois le paiement reçu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Prod. Desc."
msgstr "Descr. prod."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "Produit"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "Accessoires de produit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Attribut du produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid "Product Carousel"
msgstr "Carrousel de produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Catégorie de produits"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Comparison Tool"
msgstr "Outil de comparaison des produits"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_document
msgid "Product Document"
msgstr "Document du produit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "Image du produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "Images du produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "Product Name"
msgstr "Nom du produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Page"
msgstr "Page produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "Champs supplémentaires de la page produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_grid_columns
msgid "Product Page Grid Columns"
msgstr "Grid colonnes de la page produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_layout
msgid "Product Page Image Layout"
msgstr "Mise en page de l'image de la page produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_spacing
msgid "Product Page Image Spacing"
msgstr "Espacement de l'image de la page produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_width
msgid "Product Page Image Width"
msgstr "Largeur de l'image de la page produit"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_product_pages_list
msgid "Product Pages"
msgstr "Pages produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "Catégories publiques du produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Reference Price"
msgstr "Prix de référence du produit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_tag
msgid "Product Tag"
msgstr "Étiquette de produit"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_product_tags
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags"
msgstr "Étiquettes de produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags Filter"
msgstr "Filtre des étiquettes de produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "Modèle de produit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Ligne d'attribut du modèle du produit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Valeur d'attribut du modèle du produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Modèle de produit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "Variante de produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Variants"
msgstr "Variantes de produit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "Vues du produit"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "Historique des vues du produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "Noms du produit"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Product not found"
msgstr "Produit non trouvé"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "Ruban produit"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_pages
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "Produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products List"
msgstr "Liste des produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products Page"
msgstr "Page produits"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "Produits récemment vendus avec"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "Produits récemment vendus avec le produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Products Ribbon"
msgstr "Ruban produits"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "Vues de produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Promo Code"
msgstr "Code promo"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"L'URL vidéo fournie pour '%s' n'est pas valide. Veuillez entrer une URL "
"vidéo valide."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_search
msgid "Published"
msgstr "Publié"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "Pousser vers le bas"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "Pousser en bas de page"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "Pousser en haut de page"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "Pousser vers le haut"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Quantity"
msgstr "Quantité"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Radio"
msgstr "Radio"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Rating"
msgstr "Évaluation"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg_text
msgid "Rating Avg Text"
msgstr "Texte de l'évaluation moyenne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Évaluation des derniers feedbacks"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "Évaluation de la dernière image"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "Évaluation dernière valeur"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Évaluation de la satisfaction"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_text
msgid "Rating Text"
msgstr "Texte de l'évaluation"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "Nombre d'évaluations"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Re-Order"
msgstr "Renouveler la commande"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Re-order"
msgstr "Renouveler la commande"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_enabled_portal_reorder_button
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_portal_reorder_button
msgid "Re-order From Portal"
msgstr "Renouveler la commande depuis le portail"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "Produits vendus récemment"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "Produits vus récemment"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "E-mail de récupération envoyé"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "E-mail de récupération à envoyer"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "Renforcé pour des charges lourdes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Remove"
msgstr "Supprimer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Remove all"
msgstr "Tout supprimer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "Supprimer du panier"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Remove one"
msgstr "Supprimer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Replace"
msgstr "Remplacer"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_tag__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "Limiter la publication sur ce site web."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Resume Order"
msgstr "Reprendre la commande"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Return to shipping"
msgstr "Retour à l'expédition"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Review Order"
msgstr "Vérifier la commande"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
msgid "Ribbon"
msgstr "Ruban"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "Couleurs d'arrière-plan du ruban"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "Classe ruban"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "HTML du ruban"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "Couleur du texte du ruban"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "Droite"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimisé"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "Ventes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "Analyse des ventes"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Sales"
msgstr "Ventes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "Analyse des ventes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Rapport d'analyse des ventes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Équipe commerciale"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Vendeur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_card_group
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "Échantillon"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Save address"
msgstr "Enregistrer l'adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "Rechercher les commandes abandonnées"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Search bar"
msgstr "Barre de recherche"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select"
msgstr "Sélectionner"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Sélectionnez <b>Nouveau produit</b> pour le créer et gérer ses propriétés "
"pour booster vos ventes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select Quantity"
msgstr "Sélection quantité"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Select a pick-up point"
msgstr "Sélectionner un point d'enlèvement"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Sélectionnable"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Send After"
msgstr "Envoyer après"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "Envoyer un e-mail de récupération du panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "Envoyer un e-mail de récupération"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send after"
msgstr "Envoyer après"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send by Email"
msgstr "Envoyer par e-mail"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__send_abandoned_cart_email
msgid "Send email to customers who abandoned their cart."
msgstr "Envoyer un e-mail aux clients qui ont abandonné leur panier."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "SEO nom"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Services"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Share"
msgstr "Partager"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Ship to the same address"
msgstr "Expédier à la même adresse"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#, python-format
msgid "Shipping"
msgstr "Expédition"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr "Adresse d'expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "Frais d'expédition"

#. module: website_sale
#: model:ir.model,name:website_sale.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Modes d'expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Shipping address"
msgstr "Adresse d'expédition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket Shipping Methods"
msgstr "Modes d'expédition Shiprocket"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "Boutique"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Boutique - Paiement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Checkout Process"
msgstr "Boutique - Processus de paiement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Boutique - Confirmé"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Products"
msgstr "Boutique - Produits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "Boutique - Sélectionner un mode de paiement"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_default_sort
msgid "Shop Default Sort"
msgstr "Boutique Tri par défaut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "Panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty"
msgstr "Afficher vide"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show b2b Fields"
msgstr "Afficher les champs B2B"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_search
msgid "Show on Ecommerce"
msgstr "Afficher dans eCommerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_document__shown_on_product_page
msgid "Show on product page"
msgstr "Afficher sur page produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show/hide shopping cart"
msgstr "Afficher/masquer le panier"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Sign In"
msgstr "Se connecter"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "S'inscrire"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Sign in"
msgstr "Se connecter"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sign in/up at checkout"
msgstr "Se connecter/s'inscrire lors du paiement"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "Asseyez-vous confortablement"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "Taille"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "Taille X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "Taille Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "Incliné"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__small
msgid "Small"
msgstr "Petit(e)"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "Épuisé"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "Certains champs obligatoires sont vides."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "Désolé, nous ne pouvons pas livrer votre commande"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Sort by"
msgstr "Trier par"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_normal_website_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid "Specify unit"
msgstr "Préciser l'unité"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "État / Province"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Statut"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__stay
msgid "Stay on Product Page"
msgstr "Rester sur la page produit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street and Number"
msgstr "Rue et numéro"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect n'est pas disponible dans votre pays. Veuillez utiliser un "
"autre fournisseur de paiement."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Style"
msgstr "Style"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal"
msgstr "Sous-total"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"Suggérez des alternatives à vos clients (stratégie de vente incitative). Ces"
" produits apparaissent sur la page du produit. "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Suggested Accessories"
msgstr "Accessoires suggérés"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested accessories"
msgstr "Accessoires suggérés"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "Accessoires suggérés dans le panier eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "Étiquette"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Tags"
msgstr "Étiquettes"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_excluded
msgid "Tax Excluded"
msgstr "Hors taxes"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_included
msgid "Tax Included"
msgstr "Toutes taxes comprises"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tax Indication"
msgstr "Indication taxes"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__amount_delivery
msgid "Tax included or excluded depending on the website configuration."
msgstr ""
"Toutes taxes comprises ou hors taxes en fonction de la configuration du site"
" web."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes"
msgstr "Taxes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Text"
msgstr "Texte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale_text
msgid "Text to show instead of price"
msgstr "Texte à afficher à la place du prix"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Merci pour votre commande."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "Le #1"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Le jeton d'accès n'est pas valide."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,help:website_sale.field_product_template__compare_list_price
msgid ""
"The amount will be displayed strikethroughed on the eCommerce product page"
msgstr "Le montant sera affiché barré sur la page du produit de l'eCommerce"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has already been paid. Please refresh the page."
msgstr "Le panier a déjà été payé. Veuillez rafraîchir la page."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "Le panier a été mis à jour. Veuillez actualiser la page."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The company of the website you are trying to sale from (%s) is different "
"than the one you want to use (%s)"
msgstr ""
"Le site web de l'entreprise depuis lequel vous essayez de vendre (%s) est "
"différent de celui que vous voulez utiliser (%s)"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "L'URL complète pour accéder au document à travers le site web."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr ""
"Cette combinaison n'existe pas et ne peut donc pas être ajoutée au panier."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "Ce produit n'existe pas et ne peut donc pas être ajouté au panier."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given product does not have a price therefore it cannot be added to "
"cart."
msgstr ""
"Le produit donné n'a pas de prix donc il ne peut pas être ajouté au panier."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"Le mode sélectionné ici s'applique en tant que politique de facturation de "
"chaque nouveau produit créé, mais pas pour les produits existants."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "La commande a été annulée."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Edit Click on the page and enable 'Categories' to view all eCommerce "
"categories."
msgstr ""
"Le produit sera disponible dans chaque catégorie de l'eCommerce mentionnée. "
"Allez à Boutique > Modifier Cliquez sur la page et activez « Catégories » "
"pour afficher toutes les catégories eCommerce."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"Le temps pour marquer un panier abandonné peut être modifié dans les "
"paramètres."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"La valeur du nombre d'unités de base doit être supérieure à 0. Utilisez 0 "
"pour masquer le prix unitaire sur ce produit."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "Il n'y a pas de commande confirmée depuis le site web"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "Il n'y a pas encore de commande non payée provenant du site web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "Cette combinaison n'existe pas."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "Voici votre panier actuel."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid ""
"This partner has an open cart. Please note that the pricelist will not be "
"updated on that cart. Also, the cart might not be visible for the customer "
"until you update the pricelist of that cart."
msgstr ""
"Ce partenaire a un panier ouvert. Veuillez noter que la liste de prix ne "
"sera pas mise à jour sur ce panier. De plus, le panier peut ne pas être "
"visible pour le client tant que vous n'avez pas mis à jour la liste de prix "
"de ce panier."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/000.js:0
#, python-format
msgid "This product does not exist therefore it cannot be added to cart."
msgstr "Ce produit n'existe pas donc il ne peut pas être ajouté au panier."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "Ce produit n'a pas de combinaison existante"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "Ce produit n'est plus disponible."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "This product is not available for purchase."
msgstr "Ce produit n'est pas disponible à l'achat."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "Ce produit n'est pas publié."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "Ce code promo n'est pas disponible."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails"
msgstr "Vignettes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Pour envoyer des invitations en mode B2B, ouvrez un contact ou sélectionnez-"
"en plusieurs dans la liste et cliquez sur l'option 'Portal Access "
"Management' dans le menu déroulant *Action*."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top"
msgstr "Haut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top Bar"
msgstr "Barre supérieure"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "Nombre total de produits consultés"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "Nombre total de vues sur des produits"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"Vrai seulement pour des filtres de produit qui requièrent un product_id "
"parce qu'ils concernent la vente croisée"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "Afficher l'unité de mesure du prix pour l'eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "URL d'une vidéo présentant votre produit."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "Modes d'expédition USPS"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "Unité de mesure pour le prix unitaire des produits de l'eCommerce."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "Non payé"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
msgid "Unpaid Orders"
msgstr "Commandes non payées"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "Non publié"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "Téléchargez un document depuis votre librairie locale."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Use Google Places API to validate addresses entered by your visitors"
msgstr ""
"Utiliser Google Places API pour valider les adresses saisies par vos "
"visiteurs"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "VAT"
msgstr "TVA"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Excluded"
msgstr "Hors TVA"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Included"
msgstr "TVA comprise"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Variant"
msgstr "Variante"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__ribbon_id
msgid "Variant Ribbon"
msgstr "Ruban de variante"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Variants"
msgstr "Variantes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Vertical (2/3)"
msgstr "Vertical (2/3)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "URL vidéo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "Voir le produit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml:0
#, python-format
msgid "View cart"
msgstr "Voir le panier"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "View product"
msgstr "Voir le produit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Viewer"
msgstr "Visionneur"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "Visibilité"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "Visible"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "Visible sur le site web actuel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_tag__visible_on_ecommerce
msgid "Visible on eCommerce"
msgstr "Visible sur l'eCommerce"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "Pages visitées"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "Produits visités"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "Vues produit visiteur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "Historique vues produit visiteur"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#: model:ir.model.fields,field_description:website_sale.field_sale_order__shop_warning
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__shop_warning
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
#, python-format
msgid "Warning"
msgstr "Avertissement"

#. module: website_sale
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Garantie"

#. module: website_sale
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"Garantie délivrée à l'acheteur d'un article par le fabricant promettant la "
"réparation ou le remplacement, si nécessaire dans un délai imparti."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_tag__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "Site Web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "Catégorie de produits du site web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Catégories publiques du site web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "Séquence du site web"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Boutique en ligne"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtre des snippets du site web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "URL de site web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "Visiteur du site web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "Méta-description du site web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "Méta mots-clés site web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "Méta titre site web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "Image opengraph site web "

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created for eCommerce orders."
msgstr ""
"Site web via lequel cette facture a été créée pour les commandes de "
"l'eCommerce."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed for eCommerce orders."
msgstr ""
"Site web via lequel cette commande a été placée pour les commandes "
"eCommerce."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "Sites web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "What should be done on \"Add to Cart\"?"
msgstr "Que faut-il faire sur \"Ajouter au panier\" ?"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_tag__visible_on_ecommerce
msgid "Whether the tag is displayed on the eCommerce."
msgstr "Si l'étiquette s'affiche ou non sur l'eCommerce"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "Tableau blanc"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "Listes de souhaits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"Avec le premier mode, vous pouvez définir plusieurs prix dans le formulaire "
"de configuration produit (onglet Ventes). Avec le deuxième, vous fixez les "
"prix et les règles de calcul depuis les listes de prix."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "Avec trois pieds"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Yes"
msgstr "Oui"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"Vous modifiez vos adresses de <b>facturation et d'expédition</b> en même temps !<br/>\n"
"                                            Si vous souhaitez modifier votre adresse d'expédition, créez"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "You can't use a video as the product's main image."
msgstr ""
"Vous ne pouvez pas utiliser une vidéo comme image principale du produit."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "Vous n'avez pas de commande sur le site web"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "Il n'y a aucune commande en ligne à facturer."

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "Vous avez laissé des articles dans votre panier !"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"Vous trouverez ici tous les paniers abandonnés par vos visiteurs.\n"
"                S'ils ont saisi leur adresse, vous devriez leur envoyer un e-mail de récupération !"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.alternative_products
msgid ""
"Your Dynamic Snippet will be displayed here...\n"
"                                This message is displayed because youy did not provide both a filter and a template to use."
msgstr ""
"Votre snippet dynamique sera affiché ici...\n"
"                                Ce message s'affiche parce que vous n'avez pas défini le filtre et le modèle à utiliser."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Votre e-mail"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Votre nom"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Your cart is empty!"
msgstr "Votre panier est vide !"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Your cart is not ready to be paid, please verify previous steps."
msgstr ""
"Votre panier n'est pas prêt à être payé, veuillez vérifier les étapes "
"précédentes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr "Votre paiement a été autorisé."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "Votre précédent panier a déjà été réglé."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "Code postal"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "Préfixe postal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "Modes d'expédition Bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "e.g. Cheese Burger"
msgstr "par ex. Cheeseburger"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "par ex. lampe, poubelle"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "eCommerce"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "Catégories d'eCommerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__description_ecommerce
#: model:ir.model.fields,field_description:website_sale.field_product_template__description_ecommerce
msgid "eCommerce Description"
msgstr "Description eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "Visibilité du filtre d'eCommerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "Ventes eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "Boutique eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "eCommerce cart"
msgstr "Panier eCommerce"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_cron_send_availability_email_ir_actions_server
msgid "eCommerce: send email to customers about their abandoned cart"
msgstr ""
"eCommerce : envoyer un e-mail aux clients concernant leur panier abandonné"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr ""
"si vous voulez fusionner votre panier précédent et votre panier actuel."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"si vous voulez récupérer votre panier précédent. Votre panier actuel sera "
"remplacé par votre panier précédent."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "dans la catégorie \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "une nouvelle adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "or"
msgstr "ou"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "remove"
msgstr "supprimer"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "select to see available Pick-Up Locations"
msgstr "sélectionner pour voir les points d'enlèvement disponibles"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "terms &amp; conditions"
msgstr "conditions générales"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "pour suivre votre commande."
