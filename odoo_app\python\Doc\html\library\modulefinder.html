<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="modulefinder — Find modules used by a script" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/modulefinder.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/modulefinder.py This module provides a ModuleFinder class that can be used to determine the set of modules imported by a script. modulefinder.py can also be run as a script, giving..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/modulefinder.py This module provides a ModuleFinder class that can be used to determine the set of modules imported by a script. modulefinder.py can also be run as a script, giving..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>modulefinder — Find modules used by a script &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="runpy — Locating and executing Python modules" href="runpy.html" />
    <link rel="prev" title="pkgutil — Package extension utility" href="pkgutil.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/modulefinder.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code> — Find modules used by a script</a><ul>
<li><a class="reference internal" href="#example-usage-of-modulefinder">Example usage of <code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleFinder</span></code></a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pkgutil.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pkgutil</span></code> — Package extension utility</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="runpy.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">runpy</span></code> — Locating and executing Python modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/modulefinder.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="runpy.html" title="runpy — Locating and executing Python modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pkgutil.html" title="pkgutil — Package extension utility"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code> — Find modules used by a script</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-modulefinder">
<span id="modulefinder-find-modules-used-by-a-script"></span><h1><a class="reference internal" href="#module-modulefinder" title="modulefinder: Find modules used by a script."><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code></a> — Find modules used by a script<a class="headerlink" href="#module-modulefinder" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/modulefinder.py">Lib/modulefinder.py</a></p>
<hr class="docutils" />
<p>This module provides a <a class="reference internal" href="#modulefinder.ModuleFinder" title="modulefinder.ModuleFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleFinder</span></code></a> class that can be used to determine
the set of modules imported by a script. <code class="docutils literal notranslate"><span class="pre">modulefinder.py</span></code> can also be run as
a script, giving the filename of a Python script as its argument, after which a
report of the imported modules will be printed.</p>
<dl class="py function">
<dt class="sig sig-object py" id="modulefinder.AddPackagePath">
<span class="sig-prename descclassname"><span class="pre">modulefinder.</span></span><span class="sig-name descname"><span class="pre">AddPackagePath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pkg_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#modulefinder.AddPackagePath" title="Link to this definition">¶</a></dt>
<dd><p>Record that the package named <em>pkg_name</em> can be found in the specified <em>path</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="modulefinder.ReplacePackage">
<span class="sig-prename descclassname"><span class="pre">modulefinder.</span></span><span class="sig-name descname"><span class="pre">ReplacePackage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">oldname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#modulefinder.ReplacePackage" title="Link to this definition">¶</a></dt>
<dd><p>Allows specifying that the module named <em>oldname</em> is in fact the package named
<em>newname</em>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="modulefinder.ModuleFinder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">modulefinder.</span></span><span class="sig-name descname"><span class="pre">ModuleFinder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">excludes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">[]</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">replace_paths</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">[]</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#modulefinder.ModuleFinder" title="Link to this definition">¶</a></dt>
<dd><p>This class provides <a class="reference internal" href="#modulefinder.ModuleFinder.run_script" title="modulefinder.ModuleFinder.run_script"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run_script()</span></code></a> and <a class="reference internal" href="#modulefinder.ModuleFinder.report" title="modulefinder.ModuleFinder.report"><code class="xref py py-meth docutils literal notranslate"><span class="pre">report()</span></code></a> methods to determine
the set of modules imported by a script. <em>path</em> can be a list of directories to
search for modules; if not specified, <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> is used.  <em>debug</em> sets the
debugging level; higher values make the class print debugging messages about
what it’s doing. <em>excludes</em> is a list of module names to exclude from the
analysis. <em>replace_paths</em> is a list of <code class="docutils literal notranslate"><span class="pre">(oldpath,</span> <span class="pre">newpath)</span></code> tuples that will
be replaced in module paths.</p>
<dl class="py method">
<dt class="sig sig-object py" id="modulefinder.ModuleFinder.report">
<span class="sig-name descname"><span class="pre">report</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#modulefinder.ModuleFinder.report" title="Link to this definition">¶</a></dt>
<dd><p>Print a report to standard output that lists the modules imported by the
script and their paths, as well as modules that are missing or seem to be
missing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="modulefinder.ModuleFinder.run_script">
<span class="sig-name descname"><span class="pre">run_script</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pathname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#modulefinder.ModuleFinder.run_script" title="Link to this definition">¶</a></dt>
<dd><p>Analyze the contents of the <em>pathname</em> file, which must contain Python
code.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="modulefinder.ModuleFinder.modules">
<span class="sig-name descname"><span class="pre">modules</span></span><a class="headerlink" href="#modulefinder.ModuleFinder.modules" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping module names to modules. See
<a class="reference internal" href="#modulefinder-example"><span class="std std-ref">Example usage of ModuleFinder</span></a>.</p>
</dd></dl>

</dd></dl>

<section id="example-usage-of-modulefinder">
<span id="modulefinder-example"></span><h2>Example usage of <a class="reference internal" href="#modulefinder.ModuleFinder" title="modulefinder.ModuleFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleFinder</span></code></a><a class="headerlink" href="#example-usage-of-modulefinder" title="Link to this heading">¶</a></h2>
<p>The script that is going to get analyzed later on (bacon.py):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">re</span><span class="o">,</span> <span class="nn">itertools</span>

<span class="k">try</span><span class="p">:</span>
    <span class="kn">import</span> <span class="nn">baconhameggs</span>
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="k">pass</span>

<span class="k">try</span><span class="p">:</span>
    <span class="kn">import</span> <span class="nn">guido.python.ham</span>
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p>The script that will output the report of bacon.py:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">modulefinder</span> <span class="kn">import</span> <span class="n">ModuleFinder</span>

<span class="n">finder</span> <span class="o">=</span> <span class="n">ModuleFinder</span><span class="p">()</span>
<span class="n">finder</span><span class="o">.</span><span class="n">run_script</span><span class="p">(</span><span class="s1">&#39;bacon.py&#39;</span><span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Loaded modules:&#39;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">name</span><span class="p">,</span> <span class="n">mod</span> <span class="ow">in</span> <span class="n">finder</span><span class="o">.</span><span class="n">modules</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;</span><span class="si">%s</span><span class="s1">: &#39;</span> <span class="o">%</span> <span class="n">name</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="s1">&#39;&#39;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;,&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">mod</span><span class="o">.</span><span class="n">globalnames</span><span class="o">.</span><span class="n">keys</span><span class="p">())[:</span><span class="mi">3</span><span class="p">]))</span>

<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;-&#39;</span><span class="o">*</span><span class="mi">50</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Modules not imported:&#39;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">finder</span><span class="o">.</span><span class="n">badmodules</span><span class="o">.</span><span class="n">keys</span><span class="p">()))</span>
</pre></div>
</div>
<p>Sample output (may vary depending on the architecture):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Loaded</span> <span class="n">modules</span><span class="p">:</span>
<span class="n">_types</span><span class="p">:</span>
<span class="n">copyreg</span><span class="p">:</span>  <span class="n">_inverted_registry</span><span class="p">,</span><span class="n">_slotnames</span><span class="p">,</span><span class="n">__all__</span>
<span class="n">re</span><span class="o">.</span><span class="n">_compiler</span><span class="p">:</span>  <span class="n">isstring</span><span class="p">,</span><span class="n">_sre</span><span class="p">,</span><span class="n">_optimize_unicode</span>
<span class="n">_sre</span><span class="p">:</span>
<span class="n">re</span><span class="o">.</span><span class="n">_constants</span><span class="p">:</span>  <span class="n">REPEAT_ONE</span><span class="p">,</span><span class="n">makedict</span><span class="p">,</span><span class="n">AT_END_LINE</span>
<span class="n">sys</span><span class="p">:</span>
<span class="n">re</span><span class="p">:</span>  <span class="vm">__module__</span><span class="p">,</span><span class="n">finditer</span><span class="p">,</span><span class="n">_expand</span>
<span class="n">itertools</span><span class="p">:</span>
<span class="n">__main__</span><span class="p">:</span>  <span class="n">re</span><span class="p">,</span><span class="n">itertools</span><span class="p">,</span><span class="n">baconhameggs</span>
<span class="n">re</span><span class="o">.</span><span class="n">_parser</span><span class="p">:</span>  <span class="n">_PATTERNENDERS</span><span class="p">,</span><span class="n">SRE_FLAG_UNICODE</span>
<span class="n">array</span><span class="p">:</span>
<span class="n">types</span><span class="p">:</span>  <span class="vm">__module__</span><span class="p">,</span><span class="n">IntType</span><span class="p">,</span><span class="n">TypeType</span>
<span class="o">---------------------------------------------------</span>
<span class="n">Modules</span> <span class="ow">not</span> <span class="n">imported</span><span class="p">:</span>
<span class="n">guido</span><span class="o">.</span><span class="n">python</span><span class="o">.</span><span class="n">ham</span>
<span class="n">baconhameggs</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code> — Find modules used by a script</a><ul>
<li><a class="reference internal" href="#example-usage-of-modulefinder">Example usage of <code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleFinder</span></code></a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pkgutil.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pkgutil</span></code> — Package extension utility</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="runpy.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">runpy</span></code> — Locating and executing Python modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/modulefinder.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="runpy.html" title="runpy — Locating and executing Python modules"
             >next</a> |</li>
        <li class="right" >
          <a href="pkgutil.html" title="pkgutil — Package extension utility"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code> — Find modules used by a script</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>