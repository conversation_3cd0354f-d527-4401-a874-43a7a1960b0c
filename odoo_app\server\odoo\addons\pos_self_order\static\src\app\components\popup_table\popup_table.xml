<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.PopupTable">
        <div class="position-absolute bg-dark bg-opacity-25 w-100 h-100 fixed-top" />
        <div class="self_order_popup_table shadow-lg position-absolute fixed-bottom bg-white w-100 p-4 flex-column d-flex justify-content-between">
            <div class="mb-5 d-flex justify-content-between align-items-start">
                <div>
                    <h3>Table detective time!</h3>
                    <span>Could you please confirm your table number?<br/>Thanks a lot!</span>
                </div>
                <button class="btn btn-close" t-on-click="close"/>
            </div>
            <select class="form-select form-select-lg mb-5" t-model="state.selectedTable">
                <t t-foreach="availableFloor" t-as="floor" t-key="floor.id">
                    <option value="floor" disabled="true">
                        <t t-esc="floor.name" />
                    </option>
                    <option t-foreach="floor.tables" t-as="table" t-key="table.id" t-att-value="table.id">
                        <t t-esc="table.name" />
                    </option>
                </t>
            </select>
            <a
                type="button"
                t-on-click="() => this.setTable()"
                class="btn btn-primary py-3 my-2">
                Confirm
            </a>
        </div>
    </t>
</templates>
