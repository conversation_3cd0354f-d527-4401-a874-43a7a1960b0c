# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_viva_wallet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2024-02-07 08:49+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
msgid "API Key"
msgstr "คีย์ API"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Cannot process transactions with negative amount."
msgstr "ไม่สามารถประมวลผลธุรกรรมที่มียอดติดลบได้"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid "Client ID"
msgstr "ไอดีลูกค้า"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_secret
msgid "Client secret"
msgstr "รหัสลับลูกค้า"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ Odoo ได้ "
"โปรดตรวจสอบการเชื่อมต่ออินเตอร์เน็ตของคุณและลองอีกครั้ง"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "It is essential to provide API key for the use of viva wallet"
msgstr "จำเป็นต้องจัดเตรียมคีย์ API สำหรับการใช้ viva wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid "Merchant ID"
msgstr "ไอดีผู้ค้า"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Message from Viva Wallet: %s"
msgstr "ข้อความจาก Viva Wallet: %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to cancel a Viva Wallet payment"
msgstr ""
"เฉพาะ 'group_pos_user' เท่านั้นที่ได้รับอนุญาตให้ยกเลิกการชำระเงินผ่าน Viva "
"Wallet"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to get latest transaction status"
msgstr ""
"เฉพาะ 'group_pos_user' เท่านั้นที่ได้รับอนุญาตให้รับสถานะธุรกรรมล่าสุด"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to get the payment status from Viva Wallet"
msgstr ""
"เฉพาะ 'group_pos_user' เท่านั้นที่ได้รับอนุญาตให้รับสถานะการชำระเงินจาก Viva"
" Wallet"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to send a Viva Wallet payment request"
msgstr ""
"เฉพาะ 'group_pos_user' เท่านั้นที่ได้รับอนุญาตให้ส่งคำขอชำระเงินผ่าน Viva "
"Wallet"

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "วิธีการชำระเงินการขายหน้าร้าน"

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_session
msgid "Point of Sale Session"
msgstr "เซสชั่นการขายหน้าร้าน"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Run transactions in the test environment."
msgstr "ดำเนินการธุรกรรมในสภาพแวดล้อมทดลอง"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "Terminal ID"
msgstr "รหัสเทอร์มินัล"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Test mode"
msgstr "โหมดทดสอบ"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later. %s"
msgstr "มีปัญหาบางอย่างระหว่างเรากับ Viva Wallet โปรดลองอีกครั้งในภายหลัง %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later.%s)"
msgstr "มีปัญหาบางอย่างระหว่างเรากับ Viva Wallet โปรดลองอีกครั้งในภายหลัง %s)"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Unable to retrieve Viva Wallet Bearer Token: Please verify that the Client "
"ID and Client Secret are correct"
msgstr ""
"ไม่สามารถดึง Viva Wallet Bearer Token ได้: โปรดตรวจสอบว่า Client ID และ "
"Client Secret ถูกต้อง"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"
msgstr ""
"เมื่อใช้งานเชื่อมต่อกับ Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"
msgstr ""
"เมื่อใช้งานกับ Viva Wallet: https://developer.vivawallet.com/getting-"
"started/find-your-account-credentials/pos-apis-credentials/#find-your-pos-"
"apis-credentials"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_bearer_token
msgid "Viva Wallet Bearer Token"
msgstr "โทเค็น Bearer บน Viva Wallet "

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Viva Wallet Error"
msgstr "ข้อผิดพลาด Viva Wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_latest_response
msgid "Viva Wallet Latest Response"
msgstr "Viva Wallet ตอบกลับล่าสุด"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_endpoint
msgid "Viva Wallet Webhook Endpoint"
msgstr "จุดสิ้นสุด Webhook ของ Viva Wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_verification_key
msgid "Viva Wallet Webhook Verification Key"
msgstr "รหัสยืนยัน Viva Wallet Webhook"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Your transaction with Viva Wallet failed. Please try again later."
msgstr ""
"การทำธุรกรรมของคุณกับ Viva Wallet ล้มเหลว กรุณาลองใหม่อีกครั้งในภายหลัง."

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "[Terminal ID of the Viva Wallet terminal], for example: 16002169"
msgstr "[รหัสเทอร์มินัลของเทอร์มินัล Viva Wallet] เช่น: 16002169"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a pos payment provider not registered."
msgstr "ได้รับข้อความแจ้งผู้ให้บริการชำระเงิน POS ที่ไม่ได้ลงทะเบียน"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a terminal not registered in Odoo: %s"
msgstr "ได้รับข้อความสำหรับเทอร์มินัลที่ไม่ได้ลงทะเบียนใน Odoo: %s"
