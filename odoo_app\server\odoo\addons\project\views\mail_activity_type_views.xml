<?xml version="1.0"?>
<odoo>
    <record id="mail_activity_type_action_config_project_types" model="ir.actions.act_window">
        <field name="name">Activity Types</field>
        <field name="res_model">mail.activity.type</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">['|', ('res_model', '=', False), ('res_model', '=', 'project.task')]</field>
        <field name="context">{'default_res_model': 'project.task'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No activity types found. Let's create one!
            </p><p>
                Those represent the different categories of things you have to do (e.g. "Call" or "Send email").
            </p>
        </field>
    </record>
</odoo>
