<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="stat — Interpreting stat() results" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/stat.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/stat.py The stat module defines constants and functions for interpreting the results of os.stat(), os.fstat() and os.lstat()(if they exist). For complete details about the stat(), ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/stat.py The stat module defines constants and functions for interpreting the results of os.stat(), os.fstat() and os.lstat()(if they exist). For complete details about the stat(), ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>stat — Interpreting stat() results &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="filecmp — File and Directory Comparisons" href="filecmp.html" />
    <link rel="prev" title="fileinput — Iterate over lines from multiple input streams" href="fileinput.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/stat.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="fileinput.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code> — Iterate over lines from multiple input streams</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="filecmp.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code> — File and Directory Comparisons</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/stat.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="filecmp.html" title="filecmp — File and Directory Comparisons"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="fileinput.html" title="fileinput — Iterate over lines from multiple input streams"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" accesskey="U">File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code> — Interpreting <code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code> results</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-stat">
<span id="stat-interpreting-stat-results"></span><h1><a class="reference internal" href="#module-stat" title="stat: Utilities for interpreting the results of os.stat(), os.lstat() and os.fstat()."><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code></a> — Interpreting <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code></a> results<a class="headerlink" href="#module-stat" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/stat.py">Lib/stat.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-stat" title="stat: Utilities for interpreting the results of os.stat(), os.lstat() and os.fstat()."><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code></a> module defines constants and functions for interpreting the
results of <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>, <a class="reference internal" href="os.html#os.fstat" title="os.fstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fstat()</span></code></a> and <a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a> (if they
exist).  For complete details about the <code class="xref c c-func docutils literal notranslate"><span class="pre">stat()</span></code>, <code class="xref c c-func docutils literal notranslate"><span class="pre">fstat()</span></code> and
<code class="xref c c-func docutils literal notranslate"><span class="pre">lstat()</span></code> calls, consult the documentation for your system.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The stat module is backed by a C implementation.</p>
</div>
<p>The <a class="reference internal" href="#module-stat" title="stat: Utilities for interpreting the results of os.stat(), os.lstat() and os.fstat()."><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code></a> module defines the following functions to test for specific file
types:</p>
<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISDIR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISDIR</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISDIR" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a directory.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISCHR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISCHR</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISCHR" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a character special device file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISBLK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISBLK</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISBLK" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a block special device file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISREG">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISREG</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISREG" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a regular file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISFIFO">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISFIFO</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISFIFO" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a FIFO (named pipe).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISLNK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISLNK</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISLNK" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a symbolic link.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISSOCK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISSOCK</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISSOCK" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a socket.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISDOOR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISDOOR</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISDOOR" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a door.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISPORT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISPORT</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISPORT" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from an event port.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_ISWHT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISWHT</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_ISWHT" title="Link to this definition">¶</a></dt>
<dd><p>Return non-zero if the mode is from a whiteout.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<p>Two additional functions are defined for more general manipulation of the file’s
mode:</p>
<dl class="py function">
<dt class="sig sig-object py" id="stat.S_IMODE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IMODE</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_IMODE" title="Link to this definition">¶</a></dt>
<dd><p>Return the portion of the file’s mode that can be set by
<a class="reference internal" href="os.html#os.chmod" title="os.chmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chmod()</span></code></a>—that is, the file’s permission bits, plus the sticky
bit, set-group-id, and set-user-id bits (on systems that support them).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stat.S_IFMT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFMT</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.S_IFMT" title="Link to this definition">¶</a></dt>
<dd><p>Return the portion of the file’s mode that describes the file type (used by the
<code class="xref py py-func docutils literal notranslate"><span class="pre">S_IS*()</span></code> functions above).</p>
</dd></dl>

<p>Normally, you would use the <code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.is*()</span></code> functions for testing the type
of a file; the functions here are useful when you are doing multiple tests of
the same file and wish to avoid the overhead of the <code class="xref c c-func docutils literal notranslate"><span class="pre">stat()</span></code> system call
for each test.  These are also useful when checking for information about a file
that isn’t handled by <a class="reference internal" href="os.path.html#module-os.path" title="os.path: Operations on pathnames."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code></a>, like the tests for block and character
devices.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span><span class="o">,</span> <span class="nn">sys</span>
<span class="kn">from</span> <span class="nn">stat</span> <span class="kn">import</span> <span class="o">*</span>

<span class="k">def</span> <span class="nf">walktree</span><span class="p">(</span><span class="n">top</span><span class="p">,</span> <span class="n">callback</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&#39;&#39;&#39;recursively descend the directory tree rooted at top,</span>
<span class="sd">       calling the callback function for each regular file&#39;&#39;&#39;</span>

    <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">os</span><span class="o">.</span><span class="n">listdir</span><span class="p">(</span><span class="n">top</span><span class="p">):</span>
        <span class="n">pathname</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">top</span><span class="p">,</span> <span class="n">f</span><span class="p">)</span>
        <span class="n">mode</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">lstat</span><span class="p">(</span><span class="n">pathname</span><span class="p">)</span><span class="o">.</span><span class="n">st_mode</span>
        <span class="k">if</span> <span class="n">S_ISDIR</span><span class="p">(</span><span class="n">mode</span><span class="p">):</span>
            <span class="c1"># It&#39;s a directory, recurse into it</span>
            <span class="n">walktree</span><span class="p">(</span><span class="n">pathname</span><span class="p">,</span> <span class="n">callback</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">S_ISREG</span><span class="p">(</span><span class="n">mode</span><span class="p">):</span>
            <span class="c1"># It&#39;s a file, call the callback function</span>
            <span class="n">callback</span><span class="p">(</span><span class="n">pathname</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Unknown file type, print a message</span>
            <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Skipping </span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">pathname</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">visitfile</span><span class="p">(</span><span class="n">file</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;visiting&#39;</span><span class="p">,</span> <span class="n">file</span><span class="p">)</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">walktree</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="n">visitfile</span><span class="p">)</span>
</pre></div>
</div>
<p>An additional utility function is provided to convert a file’s mode in a human
readable string:</p>
<dl class="py function">
<dt class="sig sig-object py" id="stat.filemode">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">filemode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stat.filemode" title="Link to this definition">¶</a></dt>
<dd><p>Convert a file’s mode to a string of the form ‘-rwxrwxrwx’.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The function supports <a class="reference internal" href="#stat.S_IFDOOR" title="stat.S_IFDOOR"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IFDOOR</span></code></a>, <a class="reference internal" href="#stat.S_IFPORT" title="stat.S_IFPORT"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IFPORT</span></code></a> and
<a class="reference internal" href="#stat.S_IFWHT" title="stat.S_IFWHT"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IFWHT</span></code></a>.</p>
</div>
</dd></dl>

<p>All the variables below are simply symbolic indexes into the 10-tuple returned
by <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>, <a class="reference internal" href="os.html#os.fstat" title="os.fstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fstat()</span></code></a> or <a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_MODE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_MODE</span></span><a class="headerlink" href="#stat.ST_MODE" title="Link to this definition">¶</a></dt>
<dd><p>Inode protection mode.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_INO">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_INO</span></span><a class="headerlink" href="#stat.ST_INO" title="Link to this definition">¶</a></dt>
<dd><p>Inode number.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_DEV">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_DEV</span></span><a class="headerlink" href="#stat.ST_DEV" title="Link to this definition">¶</a></dt>
<dd><p>Device inode resides on.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_NLINK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_NLINK</span></span><a class="headerlink" href="#stat.ST_NLINK" title="Link to this definition">¶</a></dt>
<dd><p>Number of links to the inode.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_UID">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_UID</span></span><a class="headerlink" href="#stat.ST_UID" title="Link to this definition">¶</a></dt>
<dd><p>User id of the owner.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_GID">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_GID</span></span><a class="headerlink" href="#stat.ST_GID" title="Link to this definition">¶</a></dt>
<dd><p>Group id of the owner.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_SIZE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_SIZE</span></span><a class="headerlink" href="#stat.ST_SIZE" title="Link to this definition">¶</a></dt>
<dd><p>Size in bytes of a plain file; amount of data waiting on some special files.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_ATIME">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_ATIME</span></span><a class="headerlink" href="#stat.ST_ATIME" title="Link to this definition">¶</a></dt>
<dd><p>Time of last access.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_MTIME">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_MTIME</span></span><a class="headerlink" href="#stat.ST_MTIME" title="Link to this definition">¶</a></dt>
<dd><p>Time of last modification.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.ST_CTIME">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">ST_CTIME</span></span><a class="headerlink" href="#stat.ST_CTIME" title="Link to this definition">¶</a></dt>
<dd><p>The “ctime” as reported by the operating system.  On some systems (like Unix) is
the time of the last metadata change, and, on others (like Windows), is the
creation time (see platform documentation for details).</p>
</dd></dl>

<p>The interpretation of “file size” changes according to the file type.  For plain
files this is the size of the file in bytes.  For FIFOs and sockets under most
flavors of Unix (including Linux in particular), the “size” is the number of
bytes waiting to be read at the time of the call to <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>,
<a class="reference internal" href="os.html#os.fstat" title="os.fstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fstat()</span></code></a>, or <a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a>; this can sometimes be useful, especially
for polling one of these special files after a non-blocking open.  The meaning
of the size field for other character and block devices varies more, depending
on the implementation of the underlying system call.</p>
<p>The variables below define the flags used in the <a class="reference internal" href="#stat.ST_MODE" title="stat.ST_MODE"><code class="xref py py-data docutils literal notranslate"><span class="pre">ST_MODE</span></code></a> field.</p>
<p>Use of the functions above is more portable than use of the first set of flags:</p>
<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFSOCK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFSOCK</span></span><a class="headerlink" href="#stat.S_IFSOCK" title="Link to this definition">¶</a></dt>
<dd><p>Socket.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFLNK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFLNK</span></span><a class="headerlink" href="#stat.S_IFLNK" title="Link to this definition">¶</a></dt>
<dd><p>Symbolic link.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFREG">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFREG</span></span><a class="headerlink" href="#stat.S_IFREG" title="Link to this definition">¶</a></dt>
<dd><p>Regular file.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFBLK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFBLK</span></span><a class="headerlink" href="#stat.S_IFBLK" title="Link to this definition">¶</a></dt>
<dd><p>Block device.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFDIR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFDIR</span></span><a class="headerlink" href="#stat.S_IFDIR" title="Link to this definition">¶</a></dt>
<dd><p>Directory.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFCHR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFCHR</span></span><a class="headerlink" href="#stat.S_IFCHR" title="Link to this definition">¶</a></dt>
<dd><p>Character device.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFIFO">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFIFO</span></span><a class="headerlink" href="#stat.S_IFIFO" title="Link to this definition">¶</a></dt>
<dd><p>FIFO.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFDOOR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFDOOR</span></span><a class="headerlink" href="#stat.S_IFDOOR" title="Link to this definition">¶</a></dt>
<dd><p>Door.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFPORT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFPORT</span></span><a class="headerlink" href="#stat.S_IFPORT" title="Link to this definition">¶</a></dt>
<dd><p>Event port.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IFWHT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IFWHT</span></span><a class="headerlink" href="#stat.S_IFWHT" title="Link to this definition">¶</a></dt>
<dd><p>Whiteout.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#stat.S_IFDOOR" title="stat.S_IFDOOR"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IFDOOR</span></code></a>, <a class="reference internal" href="#stat.S_IFPORT" title="stat.S_IFPORT"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IFPORT</span></code></a> or <a class="reference internal" href="#stat.S_IFWHT" title="stat.S_IFWHT"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IFWHT</span></code></a> are defined as
0 when the platform does not have support for the file types.</p>
</div>
<p>The following flags can also be used in the <em>mode</em> argument of <a class="reference internal" href="os.html#os.chmod" title="os.chmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chmod()</span></code></a>:</p>
<dl class="py data">
<dt class="sig sig-object py" id="stat.S_ISUID">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISUID</span></span><a class="headerlink" href="#stat.S_ISUID" title="Link to this definition">¶</a></dt>
<dd><p>Set UID bit.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_ISGID">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISGID</span></span><a class="headerlink" href="#stat.S_ISGID" title="Link to this definition">¶</a></dt>
<dd><p>Set-group-ID bit.  This bit has several special uses.  For a directory
it indicates that BSD semantics is to be used for that directory:
files created there inherit their group ID from the directory, not
from the effective group ID of the creating process, and directories
created there will also get the <a class="reference internal" href="#stat.S_ISGID" title="stat.S_ISGID"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_ISGID</span></code></a> bit set.  For a
file that does not have the group execution bit (<a class="reference internal" href="#stat.S_IXGRP" title="stat.S_IXGRP"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IXGRP</span></code></a>)
set, the set-group-ID bit indicates mandatory file/record locking
(see also <a class="reference internal" href="#stat.S_ENFMT" title="stat.S_ENFMT"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_ENFMT</span></code></a>).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_ISVTX">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ISVTX</span></span><a class="headerlink" href="#stat.S_ISVTX" title="Link to this definition">¶</a></dt>
<dd><p>Sticky bit.  When this bit is set on a directory it means that a file
in that directory can be renamed or deleted only by the owner of the
file, by the owner of the directory, or by a privileged process.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IRWXU">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IRWXU</span></span><a class="headerlink" href="#stat.S_IRWXU" title="Link to this definition">¶</a></dt>
<dd><p>Mask for file owner permissions.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IRUSR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IRUSR</span></span><a class="headerlink" href="#stat.S_IRUSR" title="Link to this definition">¶</a></dt>
<dd><p>Owner has read permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IWUSR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IWUSR</span></span><a class="headerlink" href="#stat.S_IWUSR" title="Link to this definition">¶</a></dt>
<dd><p>Owner has write permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IXUSR">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IXUSR</span></span><a class="headerlink" href="#stat.S_IXUSR" title="Link to this definition">¶</a></dt>
<dd><p>Owner has execute permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IRWXG">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IRWXG</span></span><a class="headerlink" href="#stat.S_IRWXG" title="Link to this definition">¶</a></dt>
<dd><p>Mask for group permissions.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IRGRP">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IRGRP</span></span><a class="headerlink" href="#stat.S_IRGRP" title="Link to this definition">¶</a></dt>
<dd><p>Group has read permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IWGRP">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IWGRP</span></span><a class="headerlink" href="#stat.S_IWGRP" title="Link to this definition">¶</a></dt>
<dd><p>Group has write permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IXGRP">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IXGRP</span></span><a class="headerlink" href="#stat.S_IXGRP" title="Link to this definition">¶</a></dt>
<dd><p>Group has execute permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IRWXO">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IRWXO</span></span><a class="headerlink" href="#stat.S_IRWXO" title="Link to this definition">¶</a></dt>
<dd><p>Mask for permissions for others (not in group).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IROTH">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IROTH</span></span><a class="headerlink" href="#stat.S_IROTH" title="Link to this definition">¶</a></dt>
<dd><p>Others have read permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IWOTH">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IWOTH</span></span><a class="headerlink" href="#stat.S_IWOTH" title="Link to this definition">¶</a></dt>
<dd><p>Others have write permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IXOTH">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IXOTH</span></span><a class="headerlink" href="#stat.S_IXOTH" title="Link to this definition">¶</a></dt>
<dd><p>Others have execute permission.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_ENFMT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_ENFMT</span></span><a class="headerlink" href="#stat.S_ENFMT" title="Link to this definition">¶</a></dt>
<dd><p>System V file locking enforcement.  This flag is shared with <a class="reference internal" href="#stat.S_ISGID" title="stat.S_ISGID"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_ISGID</span></code></a>:
file/record locking is enforced on files that do not have the group
execution bit (<a class="reference internal" href="#stat.S_IXGRP" title="stat.S_IXGRP"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IXGRP</span></code></a>) set.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IREAD">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IREAD</span></span><a class="headerlink" href="#stat.S_IREAD" title="Link to this definition">¶</a></dt>
<dd><p>Unix V7 synonym for <a class="reference internal" href="#stat.S_IRUSR" title="stat.S_IRUSR"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IRUSR</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IWRITE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IWRITE</span></span><a class="headerlink" href="#stat.S_IWRITE" title="Link to this definition">¶</a></dt>
<dd><p>Unix V7 synonym for <a class="reference internal" href="#stat.S_IWUSR" title="stat.S_IWUSR"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IWUSR</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.S_IEXEC">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">S_IEXEC</span></span><a class="headerlink" href="#stat.S_IEXEC" title="Link to this definition">¶</a></dt>
<dd><p>Unix V7 synonym for <a class="reference internal" href="#stat.S_IXUSR" title="stat.S_IXUSR"><code class="xref py py-data docutils literal notranslate"><span class="pre">S_IXUSR</span></code></a>.</p>
</dd></dl>

<p>The following flags can be used in the <em>flags</em> argument of <a class="reference internal" href="os.html#os.chflags" title="os.chflags"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chflags()</span></code></a>:</p>
<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_NODUMP">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_NODUMP</span></span><a class="headerlink" href="#stat.UF_NODUMP" title="Link to this definition">¶</a></dt>
<dd><p>Do not dump the file.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_IMMUTABLE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_IMMUTABLE</span></span><a class="headerlink" href="#stat.UF_IMMUTABLE" title="Link to this definition">¶</a></dt>
<dd><p>The file may not be changed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_APPEND">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_APPEND</span></span><a class="headerlink" href="#stat.UF_APPEND" title="Link to this definition">¶</a></dt>
<dd><p>The file may only be appended to.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_OPAQUE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_OPAQUE</span></span><a class="headerlink" href="#stat.UF_OPAQUE" title="Link to this definition">¶</a></dt>
<dd><p>The directory is opaque when viewed through a union stack.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_NOUNLINK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_NOUNLINK</span></span><a class="headerlink" href="#stat.UF_NOUNLINK" title="Link to this definition">¶</a></dt>
<dd><p>The file may not be renamed or deleted.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_COMPRESSED">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_COMPRESSED</span></span><a class="headerlink" href="#stat.UF_COMPRESSED" title="Link to this definition">¶</a></dt>
<dd><p>The file is stored compressed (macOS 10.6+).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.UF_HIDDEN">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">UF_HIDDEN</span></span><a class="headerlink" href="#stat.UF_HIDDEN" title="Link to this definition">¶</a></dt>
<dd><p>The file should not be displayed in a GUI (macOS 10.5+).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.SF_ARCHIVED">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">SF_ARCHIVED</span></span><a class="headerlink" href="#stat.SF_ARCHIVED" title="Link to this definition">¶</a></dt>
<dd><p>The file may be archived.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.SF_IMMUTABLE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">SF_IMMUTABLE</span></span><a class="headerlink" href="#stat.SF_IMMUTABLE" title="Link to this definition">¶</a></dt>
<dd><p>The file may not be changed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.SF_APPEND">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">SF_APPEND</span></span><a class="headerlink" href="#stat.SF_APPEND" title="Link to this definition">¶</a></dt>
<dd><p>The file may only be appended to.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.SF_NOUNLINK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">SF_NOUNLINK</span></span><a class="headerlink" href="#stat.SF_NOUNLINK" title="Link to this definition">¶</a></dt>
<dd><p>The file may not be renamed or deleted.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="stat.SF_SNAPSHOT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">SF_SNAPSHOT</span></span><a class="headerlink" href="#stat.SF_SNAPSHOT" title="Link to this definition">¶</a></dt>
<dd><p>The file is a snapshot file.</p>
</dd></dl>

<p>See the *BSD or macOS systems man page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/chflags(2)">chflags(2)</a></em> for more information.</p>
<p>On Windows, the following file attribute constants are available for use when
testing bits in the <code class="docutils literal notranslate"><span class="pre">st_file_attributes</span></code> member returned by <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>.
See the <a class="reference external" href="https://msdn.microsoft.com/en-us/library/windows/desktop/gg258117.aspx">Windows API documentation</a>
for more detail on the meaning of these constants.</p>
<dl class="py data">
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_ARCHIVE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_ARCHIVE</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_ARCHIVE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_COMPRESSED">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_COMPRESSED</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_COMPRESSED" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_DEVICE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_DEVICE</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_DEVICE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_DIRECTORY">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_DIRECTORY</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_DIRECTORY" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_ENCRYPTED">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_ENCRYPTED</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_ENCRYPTED" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_HIDDEN">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_HIDDEN</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_HIDDEN" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_INTEGRITY_STREAM">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_INTEGRITY_STREAM</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_INTEGRITY_STREAM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_NORMAL">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_NORMAL</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_NORMAL" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_NOT_CONTENT_INDEXED">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_NOT_CONTENT_INDEXED</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_NOT_CONTENT_INDEXED" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_NO_SCRUB_DATA">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_NO_SCRUB_DATA</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_NO_SCRUB_DATA" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_OFFLINE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_OFFLINE</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_OFFLINE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_READONLY">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_READONLY</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_READONLY" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_REPARSE_POINT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_REPARSE_POINT</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_REPARSE_POINT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_SPARSE_FILE">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_SPARSE_FILE</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_SPARSE_FILE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_SYSTEM">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_SYSTEM</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_SYSTEM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_TEMPORARY">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_TEMPORARY</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_TEMPORARY" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.FILE_ATTRIBUTE_VIRTUAL">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">FILE_ATTRIBUTE_VIRTUAL</span></span><a class="headerlink" href="#stat.FILE_ATTRIBUTE_VIRTUAL" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<p>On Windows, the following constants are available for comparing against the
<code class="docutils literal notranslate"><span class="pre">st_reparse_tag</span></code> member returned by <a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a>. These are well-known
constants, but are not an exhaustive list.</p>
<dl class="py data">
<dt class="sig sig-object py" id="stat.IO_REPARSE_TAG_SYMLINK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">IO_REPARSE_TAG_SYMLINK</span></span><a class="headerlink" href="#stat.IO_REPARSE_TAG_SYMLINK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.IO_REPARSE_TAG_MOUNT_POINT">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">IO_REPARSE_TAG_MOUNT_POINT</span></span><a class="headerlink" href="#stat.IO_REPARSE_TAG_MOUNT_POINT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="stat.IO_REPARSE_TAG_APPEXECLINK">
<span class="sig-prename descclassname"><span class="pre">stat.</span></span><span class="sig-name descname"><span class="pre">IO_REPARSE_TAG_APPEXECLINK</span></span><a class="headerlink" href="#stat.IO_REPARSE_TAG_APPEXECLINK" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="fileinput.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code> — Iterate over lines from multiple input streams</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="filecmp.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">filecmp</span></code> — File and Directory Comparisons</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/stat.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="filecmp.html" title="filecmp — File and Directory Comparisons"
             >next</a> |</li>
        <li class="right" >
          <a href="fileinput.html" title="fileinput — Iterate over lines from multiple input streams"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" >File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">stat</span></code> — Interpreting <code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code> results</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>