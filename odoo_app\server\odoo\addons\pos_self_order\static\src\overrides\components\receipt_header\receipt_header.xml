<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.ReceiptHeader" t-inherit="point_of_sale.ReceiptHeader" t-inherit-mode="extension">
        <xpath expr="//h1[hasclass('tracking-number')]" position="after">
            <div t-if="props.data.pickingService" class="picking-service text-center">
                <span t-if="props.data.pickingService == 'table'" >Service at Table</span>
                <span t-else="">Pickup At Counter</span>
                <br/>
                <br/>
            </div>
            <div t-if="props.data.tableTracker" class="table-tracker text-center">
                Table Tracker:
                <br/>
                <t t-esc="props.data.tableTracker" />
                <br/>
                <br/>
            </div>
        </xpath>
    </t>
</templates>
