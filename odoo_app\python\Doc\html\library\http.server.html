<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="http.server — HTTP servers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/http.server.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/http/server.py This module defines classes for implementing HTTP servers. Availability: not Emscripten, not WASI. This module does not work or is not available on WebAssembly platf..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/http/server.py This module defines classes for implementing HTTP servers. Availability: not Emscripten, not WASI. This module does not work or is not available on WebAssembly platf..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>http.server — HTTP servers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="http.cookies — HTTP state management" href="http.cookies.html" />
    <link rel="prev" title="socketserver — A framework for network servers" href="socketserver.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/http.server.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a><ul>
<li><a class="reference internal" href="#security-considerations">Security Considerations</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="socketserver.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code> — A framework for network servers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="http.cookies.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.server.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="http.cookies.html" title="http.cookies — HTTP state management"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="socketserver.html" title="socketserver — A framework for network servers"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-http.server">
<span id="http-server-http-servers"></span><h1><a class="reference internal" href="#module-http.server" title="http.server: HTTP server and request handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code></a> — HTTP servers<a class="headerlink" href="#module-http.server" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/http/server.py">Lib/http/server.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module defines classes for implementing HTTP servers.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><a class="reference internal" href="#module-http.server" title="http.server: HTTP server and request handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code></a> is not recommended for production. It only implements
<a class="reference internal" href="#http-server-security"><span class="std std-ref">basic security checks</span></a>.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>One class, <a class="reference internal" href="#http.server.HTTPServer" title="http.server.HTTPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPServer</span></code></a>, is a <a class="reference internal" href="socketserver.html#socketserver.TCPServer" title="socketserver.TCPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">socketserver.TCPServer</span></code></a> subclass.
It creates and listens at the HTTP socket, dispatching the requests to a
handler.  Code to create and run the server looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="n">server_class</span><span class="o">=</span><span class="n">HTTPServer</span><span class="p">,</span> <span class="n">handler_class</span><span class="o">=</span><span class="n">BaseHTTPRequestHandler</span><span class="p">):</span>
    <span class="n">server_address</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">)</span>
    <span class="n">httpd</span> <span class="o">=</span> <span class="n">server_class</span><span class="p">(</span><span class="n">server_address</span><span class="p">,</span> <span class="n">handler_class</span><span class="p">)</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="http.server.HTTPServer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.server.</span></span><span class="sig-name descname"><span class="pre">HTTPServer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">RequestHandlerClass</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.HTTPServer" title="Link to this definition">¶</a></dt>
<dd><p>This class builds on the <a class="reference internal" href="socketserver.html#socketserver.TCPServer" title="socketserver.TCPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">TCPServer</span></code></a> class by storing
the server address as instance variables named <code class="xref py py-attr docutils literal notranslate"><span class="pre">server_name</span></code> and
<code class="xref py py-attr docutils literal notranslate"><span class="pre">server_port</span></code>. The server is accessible by the handler, typically
through the handler’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">server</span></code> instance variable.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.server.ThreadingHTTPServer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.server.</span></span><span class="sig-name descname"><span class="pre">ThreadingHTTPServer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">RequestHandlerClass</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.ThreadingHTTPServer" title="Link to this definition">¶</a></dt>
<dd><p>This class is identical to HTTPServer but uses threads to handle
requests by using the <a class="reference internal" href="socketserver.html#socketserver.ThreadingMixIn" title="socketserver.ThreadingMixIn"><code class="xref py py-class docutils literal notranslate"><span class="pre">ThreadingMixIn</span></code></a>. This
is useful to handle web browsers pre-opening sockets, on which
<a class="reference internal" href="#http.server.HTTPServer" title="http.server.HTTPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPServer</span></code></a> would wait indefinitely.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#http.server.HTTPServer" title="http.server.HTTPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPServer</span></code></a> and <a class="reference internal" href="#http.server.ThreadingHTTPServer" title="http.server.ThreadingHTTPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ThreadingHTTPServer</span></code></a> must be given
a <em>RequestHandlerClass</em> on instantiation, of which this module
provides three different variants:</p>
<dl class="py class">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.server.</span></span><span class="sig-name descname"><span class="pre">BaseHTTPRequestHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">client_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>This class is used to handle the HTTP requests that arrive at the server.  By
itself, it cannot respond to any actual HTTP requests; it must be subclassed
to handle each request method (e.g. GET or POST).
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHTTPRequestHandler</span></code></a> provides a number of class and instance
variables, and methods for use by subclasses.</p>
<p>The handler will parse the request and the headers, then call a method
specific to the request type. The method name is constructed from the
request. For example, for the request method <code class="docutils literal notranslate"><span class="pre">SPAM</span></code>, the <code class="xref py py-meth docutils literal notranslate"><span class="pre">do_SPAM()</span></code>
method will be called with no arguments. All of the relevant information is
stored in instance variables of the handler.  Subclasses should not need to
override or extend the <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> method.</p>
<p><a class="reference internal" href="#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHTTPRequestHandler</span></code></a> has the following instance variables:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.client_address">
<span class="sig-name descname"><span class="pre">client_address</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.client_address" title="Link to this definition">¶</a></dt>
<dd><p>Contains a tuple of the form <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> referring to the client’s
address.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.server">
<span class="sig-name descname"><span class="pre">server</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.server" title="Link to this definition">¶</a></dt>
<dd><p>Contains the server instance.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.close_connection">
<span class="sig-name descname"><span class="pre">close_connection</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.close_connection" title="Link to this definition">¶</a></dt>
<dd><p>Boolean that should be set before <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.handle_one_request" title="http.server.BaseHTTPRequestHandler.handle_one_request"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_one_request()</span></code></a> returns,
indicating if another request may be expected, or if the connection should
be shut down.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.requestline">
<span class="sig-name descname"><span class="pre">requestline</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.requestline" title="Link to this definition">¶</a></dt>
<dd><p>Contains the string representation of the HTTP request line. The
terminating CRLF is stripped. This attribute should be set by
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.handle_one_request" title="http.server.BaseHTTPRequestHandler.handle_one_request"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_one_request()</span></code></a>. If no valid request line was processed, it
should be set to the empty string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.command">
<span class="sig-name descname"><span class="pre">command</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.command" title="Link to this definition">¶</a></dt>
<dd><p>Contains the command (request type). For example, <code class="docutils literal notranslate"><span class="pre">'GET'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.path" title="Link to this definition">¶</a></dt>
<dd><p>Contains the request path. If query component of the URL is present,
then <code class="docutils literal notranslate"><span class="pre">path</span></code> includes the query. Using the terminology of <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3986.html"><strong>RFC 3986</strong></a>,
<code class="docutils literal notranslate"><span class="pre">path</span></code> here includes <code class="docutils literal notranslate"><span class="pre">hier-part</span></code> and the <code class="docutils literal notranslate"><span class="pre">query</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.request_version">
<span class="sig-name descname"><span class="pre">request_version</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.request_version" title="Link to this definition">¶</a></dt>
<dd><p>Contains the version string from the request. For example, <code class="docutils literal notranslate"><span class="pre">'HTTP/1.0'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.headers">
<span class="sig-name descname"><span class="pre">headers</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.headers" title="Link to this definition">¶</a></dt>
<dd><p>Holds an instance of the class specified by the <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.MessageClass" title="http.server.BaseHTTPRequestHandler.MessageClass"><code class="xref py py-attr docutils literal notranslate"><span class="pre">MessageClass</span></code></a> class
variable. This instance parses and manages the headers in the HTTP
request. The <a class="reference internal" href="http.client.html#http.client.parse_headers" title="http.client.parse_headers"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse_headers()</span></code></a> function from
<a class="reference internal" href="http.client.html#module-http.client" title="http.client: HTTP and HTTPS protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code></a> is used to parse the headers and it requires that the
HTTP request provide a valid <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a> style header.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.rfile">
<span class="sig-name descname"><span class="pre">rfile</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.rfile" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedIOBase</span></code></a> input stream, ready to read from
the start of the optional input data.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.wfile">
<span class="sig-name descname"><span class="pre">wfile</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.wfile" title="Link to this definition">¶</a></dt>
<dd><p>Contains the output stream for writing a response back to the
client. Proper adherence to the HTTP protocol must be used when writing to
this stream in order to achieve successful interoperation with HTTP
clients.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>This is an <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedIOBase</span></code></a> stream.</p>
</div>
</dd></dl>

<p><a class="reference internal" href="#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHTTPRequestHandler</span></code></a> has the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.server_version">
<span class="sig-name descname"><span class="pre">server_version</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.server_version" title="Link to this definition">¶</a></dt>
<dd><p>Specifies the server software version.  You may want to override this. The
format is multiple whitespace-separated strings, where each string is of
the form name[/version]. For example, <code class="docutils literal notranslate"><span class="pre">'BaseHTTP/0.2'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.sys_version">
<span class="sig-name descname"><span class="pre">sys_version</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.sys_version" title="Link to this definition">¶</a></dt>
<dd><p>Contains the Python system version, in a form usable by the
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.version_string" title="http.server.BaseHTTPRequestHandler.version_string"><code class="xref py py-attr docutils literal notranslate"><span class="pre">version_string</span></code></a> method and the <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.server_version" title="http.server.BaseHTTPRequestHandler.server_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">server_version</span></code></a> class
variable. For example, <code class="docutils literal notranslate"><span class="pre">'Python/1.4'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.error_message_format">
<span class="sig-name descname"><span class="pre">error_message_format</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.error_message_format" title="Link to this definition">¶</a></dt>
<dd><p>Specifies a format string that should be used by <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_error" title="http.server.BaseHTTPRequestHandler.send_error"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_error()</span></code></a> method
for building an error response to the client. The string is filled by
default with variables from <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.responses" title="http.server.BaseHTTPRequestHandler.responses"><code class="xref py py-attr docutils literal notranslate"><span class="pre">responses</span></code></a> based on the status code
that passed to <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_error" title="http.server.BaseHTTPRequestHandler.send_error"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_error()</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.error_content_type">
<span class="sig-name descname"><span class="pre">error_content_type</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.error_content_type" title="Link to this definition">¶</a></dt>
<dd><p>Specifies the Content-Type HTTP header of error responses sent to the
client.  The default value is <code class="docutils literal notranslate"><span class="pre">'text/html'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.protocol_version">
<span class="sig-name descname"><span class="pre">protocol_version</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.protocol_version" title="Link to this definition">¶</a></dt>
<dd><p>Specifies the HTTP version to which the server is conformant. It is sent
in responses to let the client know the server’s communication
capabilities for future requests. If set to
<code class="docutils literal notranslate"><span class="pre">'HTTP/1.1'</span></code>, the server will permit HTTP persistent connections;
however, your server <em>must</em> then include an accurate <code class="docutils literal notranslate"><span class="pre">Content-Length</span></code>
header (using <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_header" title="http.server.BaseHTTPRequestHandler.send_header"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_header()</span></code></a>) in all of its responses to clients.
For backwards compatibility, the setting defaults to <code class="docutils literal notranslate"><span class="pre">'HTTP/1.0'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.MessageClass">
<span class="sig-name descname"><span class="pre">MessageClass</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.MessageClass" title="Link to this definition">¶</a></dt>
<dd><p>Specifies an <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a>-like class to parse HTTP
headers.  Typically, this is not overridden, and it defaults to
<a class="reference internal" href="http.client.html#http.client.HTTPMessage" title="http.client.HTTPMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPMessage</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.responses">
<span class="sig-name descname"><span class="pre">responses</span></span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.responses" title="Link to this definition">¶</a></dt>
<dd><p>This attribute contains a mapping of error code integers to two-element tuples
containing a short and long message. For example, <code class="docutils literal notranslate"><span class="pre">{code:</span> <span class="pre">(shortmessage,</span>
<span class="pre">longmessage)}</span></code>. The <em>shortmessage</em> is usually used as the <em>message</em> key in an
error response, and <em>longmessage</em> as the <em>explain</em> key.  It is used by
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_response_only" title="http.server.BaseHTTPRequestHandler.send_response_only"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_response_only()</span></code></a> and <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_error" title="http.server.BaseHTTPRequestHandler.send_error"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_error()</span></code></a> methods.</p>
</dd></dl>

<p>A <a class="reference internal" href="#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHTTPRequestHandler</span></code></a> instance has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.handle">
<span class="sig-name descname"><span class="pre">handle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.handle" title="Link to this definition">¶</a></dt>
<dd><p>Calls <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.handle_one_request" title="http.server.BaseHTTPRequestHandler.handle_one_request"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_one_request()</span></code></a> once (or, if persistent connections are
enabled, multiple times) to handle incoming HTTP requests. You should
never need to override it; instead, implement appropriate <code class="xref py py-meth docutils literal notranslate"><span class="pre">do_*()</span></code>
methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.handle_one_request">
<span class="sig-name descname"><span class="pre">handle_one_request</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.handle_one_request" title="Link to this definition">¶</a></dt>
<dd><p>This method will parse and dispatch the request to the appropriate
<code class="xref py py-meth docutils literal notranslate"><span class="pre">do_*()</span></code> method.  You should never need to override it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.handle_expect_100">
<span class="sig-name descname"><span class="pre">handle_expect_100</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.handle_expect_100" title="Link to this definition">¶</a></dt>
<dd><p>When an HTTP/1.1 conformant server receives an <code class="docutils literal notranslate"><span class="pre">Expect:</span> <span class="pre">100-continue</span></code>
request header it responds back with a <code class="docutils literal notranslate"><span class="pre">100</span> <span class="pre">Continue</span></code> followed by <code class="docutils literal notranslate"><span class="pre">200</span>
<span class="pre">OK</span></code> headers.
This method can be overridden to raise an error if the server does not
want the client to continue.  For e.g. server can choose to send <code class="docutils literal notranslate"><span class="pre">417</span>
<span class="pre">Expectation</span> <span class="pre">Failed</span></code> as a response header and <code class="docutils literal notranslate"><span class="pre">return</span> <span class="pre">False</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.send_error">
<span class="sig-name descname"><span class="pre">send_error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">explain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.send_error" title="Link to this definition">¶</a></dt>
<dd><p>Sends and logs a complete error reply to the client. The numeric <em>code</em>
specifies the HTTP error code, with <em>message</em> as an optional, short, human
readable description of the error.  The <em>explain</em> argument can be used to
provide more detailed information about the error; it will be formatted
using the <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.error_message_format" title="http.server.BaseHTTPRequestHandler.error_message_format"><code class="xref py py-attr docutils literal notranslate"><span class="pre">error_message_format</span></code></a> attribute and emitted, after
a complete set of headers, as the response body.  The <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.responses" title="http.server.BaseHTTPRequestHandler.responses"><code class="xref py py-attr docutils literal notranslate"><span class="pre">responses</span></code></a>
attribute holds the default values for <em>message</em> and <em>explain</em> that
will be used if no value is provided; for unknown codes the default value
for both is the string <code class="docutils literal notranslate"><span class="pre">???</span></code>. The body will be empty if the method is
HEAD or the response code is one of the following: <code class="samp docutils literal notranslate"><span class="pre">1</span><em><span class="pre">xx</span></em></code>,
<code class="docutils literal notranslate"><span class="pre">204</span> <span class="pre">No</span> <span class="pre">Content</span></code>, <code class="docutils literal notranslate"><span class="pre">205</span> <span class="pre">Reset</span> <span class="pre">Content</span></code>, <code class="docutils literal notranslate"><span class="pre">304</span> <span class="pre">Not</span> <span class="pre">Modified</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The error response includes a Content-Length header.
Added the <em>explain</em> argument.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.send_response">
<span class="sig-name descname"><span class="pre">send_response</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.send_response" title="Link to this definition">¶</a></dt>
<dd><p>Adds a response header to the headers buffer and logs the accepted
request. The HTTP response line is written to the internal buffer,
followed by <em>Server</em> and <em>Date</em> headers. The values for these two headers
are picked up from the <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.version_string" title="http.server.BaseHTTPRequestHandler.version_string"><code class="xref py py-meth docutils literal notranslate"><span class="pre">version_string()</span></code></a> and
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.date_time_string" title="http.server.BaseHTTPRequestHandler.date_time_string"><code class="xref py py-meth docutils literal notranslate"><span class="pre">date_time_string()</span></code></a> methods, respectively. If the server does not
intend to send any other headers using the <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_header" title="http.server.BaseHTTPRequestHandler.send_header"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_header()</span></code></a> method,
then <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.send_response" title="http.server.BaseHTTPRequestHandler.send_response"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_response()</span></code></a> should be followed by an <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.end_headers" title="http.server.BaseHTTPRequestHandler.end_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">end_headers()</span></code></a>
call.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Headers are stored to an internal buffer and <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.end_headers" title="http.server.BaseHTTPRequestHandler.end_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">end_headers()</span></code></a>
needs to be called explicitly.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.send_header">
<span class="sig-name descname"><span class="pre">send_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">keyword</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.send_header" title="Link to this definition">¶</a></dt>
<dd><p>Adds the HTTP header to an internal buffer which will be written to the
output stream when either <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.end_headers" title="http.server.BaseHTTPRequestHandler.end_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">end_headers()</span></code></a> or <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.flush_headers" title="http.server.BaseHTTPRequestHandler.flush_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush_headers()</span></code></a> is
invoked. <em>keyword</em> should specify the header keyword, with <em>value</em>
specifying its value. Note that, after the send_header calls are done,
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.end_headers" title="http.server.BaseHTTPRequestHandler.end_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">end_headers()</span></code></a> MUST BE called in order to complete the operation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Headers are stored in an internal buffer.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.send_response_only">
<span class="sig-name descname"><span class="pre">send_response_only</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.send_response_only" title="Link to this definition">¶</a></dt>
<dd><p>Sends the response header only, used for the purposes when <code class="docutils literal notranslate"><span class="pre">100</span>
<span class="pre">Continue</span></code> response is sent by the server to the client. The headers not
buffered and sent directly the output stream.If the <em>message</em> is not
specified, the HTTP message corresponding the response <em>code</em>  is sent.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.end_headers">
<span class="sig-name descname"><span class="pre">end_headers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.end_headers" title="Link to this definition">¶</a></dt>
<dd><p>Adds a blank line
(indicating the end of the HTTP headers in the response)
to the headers buffer and calls <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.flush_headers" title="http.server.BaseHTTPRequestHandler.flush_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush_headers()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The buffered headers are written to the output stream.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.flush_headers">
<span class="sig-name descname"><span class="pre">flush_headers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.flush_headers" title="Link to this definition">¶</a></dt>
<dd><p>Finally send the headers to the output stream and flush the internal
headers buffer.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.log_request">
<span class="sig-name descname"><span class="pre">log_request</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'-'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'-'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.log_request" title="Link to this definition">¶</a></dt>
<dd><p>Logs an accepted (successful) request. <em>code</em> should specify the numeric
HTTP code associated with the response. If a size of the response is
available, then it should be passed as the <em>size</em> parameter.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.log_error">
<span class="sig-name descname"><span class="pre">log_error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.log_error" title="Link to this definition">¶</a></dt>
<dd><p>Logs an error when a request cannot be fulfilled. By default, it passes
the message to <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.log_message" title="http.server.BaseHTTPRequestHandler.log_message"><code class="xref py py-meth docutils literal notranslate"><span class="pre">log_message()</span></code></a>, so it takes the same arguments
(<em>format</em> and additional values).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.log_message">
<span class="sig-name descname"><span class="pre">log_message</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.log_message" title="Link to this definition">¶</a></dt>
<dd><p>Logs an arbitrary message to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>. This is typically overridden
to create custom error logging mechanisms. The <em>format</em> argument is a
standard printf-style format string, where the additional arguments to
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.log_message" title="http.server.BaseHTTPRequestHandler.log_message"><code class="xref py py-meth docutils literal notranslate"><span class="pre">log_message()</span></code></a> are applied as inputs to the formatting. The client
ip address and current date and time are prefixed to every message logged.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.version_string">
<span class="sig-name descname"><span class="pre">version_string</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.version_string" title="Link to this definition">¶</a></dt>
<dd><p>Returns the server software’s version string. This is a combination of the
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler.server_version" title="http.server.BaseHTTPRequestHandler.server_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">server_version</span></code></a> and <a class="reference internal" href="#http.server.BaseHTTPRequestHandler.sys_version" title="http.server.BaseHTTPRequestHandler.sys_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">sys_version</span></code></a> attributes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.date_time_string">
<span class="sig-name descname"><span class="pre">date_time_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.date_time_string" title="Link to this definition">¶</a></dt>
<dd><p>Returns the date and time given by <em>timestamp</em> (which must be <code class="docutils literal notranslate"><span class="pre">None</span></code> or in
the format returned by <a class="reference internal" href="time.html#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.time()</span></code></a>), formatted for a message
header. If <em>timestamp</em> is omitted, it uses the current date and time.</p>
<p>The result looks like <code class="docutils literal notranslate"><span class="pre">'Sun,</span> <span class="pre">06</span> <span class="pre">Nov</span> <span class="pre">1994</span> <span class="pre">08:49:37</span> <span class="pre">GMT'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.log_date_time_string">
<span class="sig-name descname"><span class="pre">log_date_time_string</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.log_date_time_string" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current date and time, formatted for logging.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.BaseHTTPRequestHandler.address_string">
<span class="sig-name descname"><span class="pre">address_string</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.BaseHTTPRequestHandler.address_string" title="Link to this definition">¶</a></dt>
<dd><p>Returns the client address.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Previously, a name lookup was performed. To avoid name resolution
delays, it now always returns the IP address.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.server.SimpleHTTPRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.server.</span></span><span class="sig-name descname"><span class="pre">SimpleHTTPRequestHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">client_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">directory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.SimpleHTTPRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>This class serves files from the directory <em>directory</em> and below,
or the current directory if <em>directory</em> is not provided, directly
mapping the directory structure to HTTP requests.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <em>directory</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>directory</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<p>A lot of the work, such as parsing the request, is done by the base class
<a class="reference internal" href="#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHTTPRequestHandler</span></code></a>.  This class implements the <a class="reference internal" href="#http.server.SimpleHTTPRequestHandler.do_GET" title="http.server.SimpleHTTPRequestHandler.do_GET"><code class="xref py py-func docutils literal notranslate"><span class="pre">do_GET()</span></code></a>
and <a class="reference internal" href="#http.server.SimpleHTTPRequestHandler.do_HEAD" title="http.server.SimpleHTTPRequestHandler.do_HEAD"><code class="xref py py-func docutils literal notranslate"><span class="pre">do_HEAD()</span></code></a> functions.</p>
<p>The following are defined as class-level attributes of
<a class="reference internal" href="#http.server.SimpleHTTPRequestHandler" title="http.server.SimpleHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHTTPRequestHandler</span></code></a>:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.SimpleHTTPRequestHandler.server_version">
<span class="sig-name descname"><span class="pre">server_version</span></span><a class="headerlink" href="#http.server.SimpleHTTPRequestHandler.server_version" title="Link to this definition">¶</a></dt>
<dd><p>This will be <code class="docutils literal notranslate"><span class="pre">&quot;SimpleHTTP/&quot;</span> <span class="pre">+</span> <span class="pre">__version__</span></code>, where <code class="docutils literal notranslate"><span class="pre">__version__</span></code> is
defined at the module level.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.SimpleHTTPRequestHandler.extensions_map">
<span class="sig-name descname"><span class="pre">extensions_map</span></span><a class="headerlink" href="#http.server.SimpleHTTPRequestHandler.extensions_map" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping suffixes into MIME types, contains custom overrides
for the default system mappings. The mapping is used case-insensitively,
and so should contain only lower-cased keys.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>This dictionary is no longer filled with the default system mappings,
but only contains overrides.</p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#http.server.SimpleHTTPRequestHandler" title="http.server.SimpleHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHTTPRequestHandler</span></code></a> class defines the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.server.SimpleHTTPRequestHandler.do_HEAD">
<span class="sig-name descname"><span class="pre">do_HEAD</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.SimpleHTTPRequestHandler.do_HEAD" title="Link to this definition">¶</a></dt>
<dd><p>This method serves the <code class="docutils literal notranslate"><span class="pre">'HEAD'</span></code> request type: it sends the headers it
would send for the equivalent <code class="docutils literal notranslate"><span class="pre">GET</span></code> request. See the <a class="reference internal" href="#http.server.SimpleHTTPRequestHandler.do_GET" title="http.server.SimpleHTTPRequestHandler.do_GET"><code class="xref py py-meth docutils literal notranslate"><span class="pre">do_GET()</span></code></a>
method for a more complete explanation of the possible headers.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.server.SimpleHTTPRequestHandler.do_GET">
<span class="sig-name descname"><span class="pre">do_GET</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.SimpleHTTPRequestHandler.do_GET" title="Link to this definition">¶</a></dt>
<dd><p>The request is mapped to a local file by interpreting the request as a
path relative to the current working directory.</p>
<p>If the request was mapped to a directory, the directory is checked for a
file named <code class="docutils literal notranslate"><span class="pre">index.html</span></code> or <code class="docutils literal notranslate"><span class="pre">index.htm</span></code> (in that order). If found, the
file’s contents are returned; otherwise a directory listing is generated
by calling the <code class="xref py py-meth docutils literal notranslate"><span class="pre">list_directory()</span></code> method. This method uses
<a class="reference internal" href="os.html#os.listdir" title="os.listdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.listdir()</span></code></a> to scan the directory, and returns a <code class="docutils literal notranslate"><span class="pre">404</span></code> error
response if the <a class="reference internal" href="os.html#os.listdir" title="os.listdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">listdir()</span></code></a> fails.</p>
<p>If the request was mapped to a file, it is opened. Any <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>
exception in opening the requested file is mapped to a <code class="docutils literal notranslate"><span class="pre">404</span></code>,
<code class="docutils literal notranslate"><span class="pre">'File</span> <span class="pre">not</span> <span class="pre">found'</span></code> error. If there was a <code class="docutils literal notranslate"><span class="pre">'If-Modified-Since'</span></code>
header in the request, and the file was not modified after this time,
a <code class="docutils literal notranslate"><span class="pre">304</span></code>, <code class="docutils literal notranslate"><span class="pre">'Not</span> <span class="pre">Modified'</span></code> response is sent. Otherwise, the content
type is guessed by calling the <code class="xref py py-meth docutils literal notranslate"><span class="pre">guess_type()</span></code> method, which in turn
uses the <em>extensions_map</em> variable, and the file contents are returned.</p>
<p>A <code class="docutils literal notranslate"><span class="pre">'Content-type:'</span></code> header with the guessed content type is output,
followed by a <code class="docutils literal notranslate"><span class="pre">'Content-Length:'</span></code> header with the file’s size and a
<code class="docutils literal notranslate"><span class="pre">'Last-Modified:'</span></code> header with the file’s modification time.</p>
<p>Then follows a blank line signifying the end of the headers, and then the
contents of the file are output. If the file’s MIME type starts with
<code class="docutils literal notranslate"><span class="pre">text/</span></code> the file is opened in text mode; otherwise binary mode is used.</p>
<p>For example usage, see the implementation of the <code class="docutils literal notranslate"><span class="pre">test</span></code> function
in <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/http/server.py">Lib/http/server.py</a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Support of the <code class="docutils literal notranslate"><span class="pre">'If-Modified-Since'</span></code> header.</p>
</div>
</dd></dl>

</dd></dl>

<p>The <a class="reference internal" href="#http.server.SimpleHTTPRequestHandler" title="http.server.SimpleHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHTTPRequestHandler</span></code></a> class can be used in the following
manner in order to create a very basic webserver serving files relative to
the current directory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">http.server</span>
<span class="kn">import</span> <span class="nn">socketserver</span>

<span class="n">PORT</span> <span class="o">=</span> <span class="mi">8000</span>

<span class="n">Handler</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span><span class="o">.</span><span class="n">SimpleHTTPRequestHandler</span>

<span class="k">with</span> <span class="n">socketserver</span><span class="o">.</span><span class="n">TCPServer</span><span class="p">((</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">PORT</span><span class="p">),</span> <span class="n">Handler</span><span class="p">)</span> <span class="k">as</span> <span class="n">httpd</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;serving at port&quot;</span><span class="p">,</span> <span class="n">PORT</span><span class="p">)</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p><a class="reference internal" href="#http.server.SimpleHTTPRequestHandler" title="http.server.SimpleHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHTTPRequestHandler</span></code></a> can also be subclassed to enhance behavior,
such as using different index file names by overriding the class attribute
<code class="xref py py-attr docutils literal notranslate"><span class="pre">index_pages</span></code>.</p>
<p id="http-server-cli"><a class="reference internal" href="#module-http.server" title="http.server: HTTP server and request handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code></a> can also be invoked directly using the <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a>
switch of the interpreter.  Similar to
the previous example, this serves files relative to the current directory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span>
</pre></div>
</div>
<p>The server listens to port 8000 by default. The default can be overridden
by passing the desired port number as an argument:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span> <span class="mi">9000</span>
</pre></div>
</div>
<p>By default, the server binds itself to all interfaces.  The option <code class="docutils literal notranslate"><span class="pre">-b/--bind</span></code>
specifies a specific address to which it should bind. Both IPv4 and IPv6
addresses are supported. For example, the following command causes the server
to bind to localhost only:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span> <span class="o">--</span><span class="n">bind</span> <span class="mf">127.0.0.1</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <code class="docutils literal notranslate"><span class="pre">--bind</span></code> option.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Support IPv6 in the <code class="docutils literal notranslate"><span class="pre">--bind</span></code> option.</p>
</div>
<p>By default, the server uses the current directory. The option <code class="docutils literal notranslate"><span class="pre">-d/--directory</span></code>
specifies a directory to which it should serve the files. For example,
the following command uses a specific directory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span> <span class="o">--</span><span class="n">directory</span> <span class="o">/</span><span class="n">tmp</span><span class="o">/</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <code class="docutils literal notranslate"><span class="pre">--directory</span></code> option.</p>
</div>
<p>By default, the server is conformant to HTTP/1.0. The option <code class="docutils literal notranslate"><span class="pre">-p/--protocol</span></code>
specifies the HTTP version to which the server is conformant. For example, the
following command runs an HTTP/1.1 conformant server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span> <span class="o">--</span><span class="n">protocol</span> <span class="n">HTTP</span><span class="o">/</span><span class="mf">1.1</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <code class="docutils literal notranslate"><span class="pre">--protocol</span></code> option.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="http.server.CGIHTTPRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.server.</span></span><span class="sig-name descname"><span class="pre">CGIHTTPRequestHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">client_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.server.CGIHTTPRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>This class is used to serve either files or output of CGI scripts from the
current directory and below. Note that mapping HTTP hierarchic structure to
local directory structure is exactly as in <a class="reference internal" href="#http.server.SimpleHTTPRequestHandler" title="http.server.SimpleHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHTTPRequestHandler</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>CGI scripts run by the <a class="reference internal" href="#http.server.CGIHTTPRequestHandler" title="http.server.CGIHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHTTPRequestHandler</span></code></a> class cannot execute
redirects (HTTP code 302), because code 200 (script output follows) is
sent prior to execution of the CGI script.  This pre-empts the status
code.</p>
</div>
<p>The class will however, run the CGI script, instead of serving it as a file,
if it guesses it to be a CGI script.  Only directory-based CGI are used —
the other common server configuration is to treat special extensions as
denoting CGI scripts.</p>
<p>The <code class="xref py py-func docutils literal notranslate"><span class="pre">do_GET()</span></code> and <code class="xref py py-func docutils literal notranslate"><span class="pre">do_HEAD()</span></code> functions are modified to run CGI scripts
and serve the output, instead of serving files, if the request leads to
somewhere below the <code class="docutils literal notranslate"><span class="pre">cgi_directories</span></code> path.</p>
<p>The <a class="reference internal" href="#http.server.CGIHTTPRequestHandler" title="http.server.CGIHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHTTPRequestHandler</span></code></a> defines the following data member:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.server.CGIHTTPRequestHandler.cgi_directories">
<span class="sig-name descname"><span class="pre">cgi_directories</span></span><a class="headerlink" href="#http.server.CGIHTTPRequestHandler.cgi_directories" title="Link to this definition">¶</a></dt>
<dd><p>This defaults to <code class="docutils literal notranslate"><span class="pre">['/cgi-bin',</span> <span class="pre">'/htbin']</span></code> and describes directories to
treat as containing CGI scripts.</p>
</dd></dl>

<p>The <a class="reference internal" href="#http.server.CGIHTTPRequestHandler" title="http.server.CGIHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHTTPRequestHandler</span></code></a> defines the following method:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.server.CGIHTTPRequestHandler.do_POST">
<span class="sig-name descname"><span class="pre">do_POST</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.server.CGIHTTPRequestHandler.do_POST" title="Link to this definition">¶</a></dt>
<dd><p>This method serves the <code class="docutils literal notranslate"><span class="pre">'POST'</span></code> request type, only allowed for CGI
scripts.  Error 501, “Can only POST to CGI scripts”, is output when trying
to POST to a non-CGI url.</p>
</dd></dl>

<p>Note that CGI scripts will be run with UID of user nobody, for security
reasons.  Problems with the CGI script will be translated to error 403.</p>
</dd></dl>

<p><a class="reference internal" href="#http.server.CGIHTTPRequestHandler" title="http.server.CGIHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHTTPRequestHandler</span></code></a> can be enabled in the command line by passing
the <code class="docutils literal notranslate"><span class="pre">--cgi</span></code> option:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">http</span><span class="o">.</span><span class="n">server</span> <span class="o">--</span><span class="n">cgi</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><a class="reference internal" href="#http.server.CGIHTTPRequestHandler" title="http.server.CGIHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHTTPRequestHandler</span></code></a> and the <code class="docutils literal notranslate"><span class="pre">--cgi</span></code> command line option
are not intended for use by untrusted clients and may be vulnerable
to exploitation. Always use within a secure environment.</p>
</div>
<section id="security-considerations">
<span id="http-server-security"></span><h2>Security Considerations<a class="headerlink" href="#security-considerations" title="Link to this heading">¶</a></h2>
<p id="index-3"><a class="reference internal" href="#http.server.SimpleHTTPRequestHandler" title="http.server.SimpleHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHTTPRequestHandler</span></code></a> will follow symbolic links when handling
requests, this makes it possible for files outside of the specified directory
to be served.</p>
<p>Earlier versions of Python did not scrub control characters from the
log messages emitted to stderr from <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">http.server</span></code> or the
default <a class="reference internal" href="#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHTTPRequestHandler</span></code></a> <code class="docutils literal notranslate"><span class="pre">.log_message</span></code>
implementation. This could allow remote clients connecting to your
server to send nefarious control codes to your terminal.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Control characters are scrubbed in stderr logs.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a><ul>
<li><a class="reference internal" href="#security-considerations">Security Considerations</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="socketserver.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code> — A framework for network servers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="http.cookies.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.server.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="http.cookies.html" title="http.cookies — HTTP state management"
             >next</a> |</li>
        <li class="right" >
          <a href="socketserver.html" title="socketserver — A framework for network servers"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>