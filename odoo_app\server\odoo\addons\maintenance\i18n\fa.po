# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* maintenance
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# far<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <y.shadman<PERSON>@gmail.com>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Category:</b>"
msgstr "<b>دسته‌بندی:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Model Number:</b>"
msgstr "<b>شماره مدل:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Request to:</b>"
msgstr "<b>درخواست به:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Serial Number:</b>"
msgstr "<b>شماره سریال:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"badge text-bg-warning float-end\">Canceled</span>"
msgstr "<span class=\"badge text-bg-warning float-end\">لغو شد</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<span class=\"badge text-bg-warning float-end\">Cancelled</span>"
msgstr "<span class=\"badge text-bg-warning float-end\">لغو شده</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"ml8\">hours</span>"
msgstr "<span class=\"ml8\">ساعت</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Reporting</span>"
msgstr "<span>گزارش</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Requests</span>"
msgstr "<span>درخواست شده</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Equipment:</strong>"
msgstr "<strong>تجهیزات:</strong>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Maintenance:</strong>"
msgstr "<strong>نگهداری:</strong>"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"یک دیکشنری پایتون که برای فراهم کردن مقادیر پیش‌فرض هنگام ایجاد رکوردهای "
"جدید برای این نام مستعار مورد  ارزیابی قرار می‌گیرد."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer3
#: model:maintenance.equipment,name:maintenance.equipment_computer5
msgid "Acer Laptop"
msgstr "لپتاب ایسر"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__active
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__active
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Active"
msgstr "فعال"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_ids
msgid "Activities"
msgstr "فعالیت ها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "دکوراسیون استثنایی فعالیت"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_state
msgid "Activity State"
msgstr "وضعیت فعالیت"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "آیکون نوع فعالیت"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.mail_activity_type_action_config_maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_menu_config_activity_type
msgid "Activity Types"
msgstr "انواع فعالیت"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid "Add a new equipment"
msgstr "افزودن تجهیزات جدید"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_category_action
msgid "Add a new equipment category"
msgstr "افزودن یک دسته تجهیزات جدید"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid "Add a new maintenance request"
msgstr "افزودن یک درخواست تعمیر و نگهداری جدید"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_stage_action
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_dashboard_action
msgid "Add a new stage in the maintenance request"
msgstr "افزودن یک مرحله جدید در درخواست تعمیر و نگهداری"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_team_action_settings
msgid "Add a team in the maintenance request"
msgstr "افزودن یک تیم در درخواست تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_id
msgid "Alias"
msgstr "مستعار"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "مستعار تماس امنیتی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain_id
msgid "Alias Domain"
msgstr "نام مستعار دامنه"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Alias Domain Name"
msgstr "دامین مستعار"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_full_name
msgid "Alias Email"
msgstr "ایمیل مستعار"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_name
msgid "Alias Name"
msgstr "نام مستعار"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias Status"
msgstr "وضعیت ایمیل مستعار"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias status assessed on the last message received."
msgstr "وضعیت نام مستعار در آخرین پیام دریافتی ارزیابی شد."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_model_id
msgid "Aliased Model"
msgstr "مدل استعاری"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "All"
msgstr "همه"

#. module: maintenance
#: model:ir.model.constraint,message:maintenance.constraint_maintenance_equipment_serial_no
msgid "Another asset already exists with this serial number!"
msgstr "دارایی دیگری در حال حاضر با این شماره سریال وجود دارد!"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__archive
msgid "Archive"
msgstr "بایگانی"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Archived"
msgstr "بایگانی شده"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Assign To User"
msgstr "واگذاری به کاربر"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Assigned"
msgstr "واگذار شده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "تاریخ واگذار شده"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Assigned to"
msgstr "واگذار شده به"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست ها"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Blocked"
msgstr "مسدود شده"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Cancel"
msgstr "لغو"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Cancelled"
msgstr "لغو شد"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__category_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Category"
msgstr "دسته بندی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
msgid "Category Name"
msgstr "نام دسته بندی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__close_date
msgid "Close Date"
msgstr "تاریخ بستن"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__color
msgid "Color Index"
msgstr "رنگ پس زمینه"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__note
msgid "Comments"
msgstr "نظرها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__company_id
msgid "Company"
msgstr "شرکت"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr "محاسبه شده به عنوان آخرین تاریخ شکست + MTBF"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_computer
msgid "Computers"
msgstr "رایانه‌ها"

#. module: maintenance
#: model:ir.model,name:maintenance.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_configuration
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Configuration"
msgstr "پیکربندی"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__corrective
msgid "Corrective"
msgstr "اصلاحی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__cost
msgid "Cost"
msgstr "هزینه"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr "ایجاد الگوهای سفارشی برگه کار"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Created By"
msgstr "ایجاد شده توسط"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "ایجاد شده توسط کاربر"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_open_count
msgid "Current Maintenance"
msgstr "تعمیر و نگهداری فعلی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "پیام برگشتی سفارشی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_res_config_settings__module_maintenance_worksheet
msgid "Custom Maintenance Worksheets"
msgstr "برگه‌های سفارشی نگهداری"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Custom Worksheets"
msgstr "کاربرگ‌های سفارشی"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_dashboard
msgid "Dashboard"
msgstr "داشبورد"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__request_date
msgid "Date requested for the maintenance to happen"
msgstr "تاریخ درخواست شده برای انجام تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__schedule_date
msgid ""
"Date the maintenance team plans the maintenance.  It should not differ much "
"from the Request Date. "
msgstr ""
"تاریخ برنامه ریزی تیم تعمیر و نگهداری. نباید با تاریخ درخواست تفاوت زیادی "
"داشته باشد. "

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__close_date
msgid "Date the maintenance was finished. "
msgstr "تاریخ اتمام تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__day
msgid "Days"
msgstr "روز"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_defaults
msgid "Default Values"
msgstr "مقادیر پیش فرض"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Delete"
msgstr "حذف"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__description
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Description"
msgstr "توصیف"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Done"
msgstr "انجام شده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__duration
msgid "Duration"
msgstr "مدت زمان"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__duration
msgid "Duration in hours."
msgstr "مدت زمان بر حسب ساعت"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Edit..."
msgstr "ویرایش…"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__effective_date
msgid "Effective Date"
msgstr "تاریخ اجرا"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_email
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Email Alias"
msgstr "مستعار ایمیل"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_id
msgid ""
"Email alias for this equipment category. New emails will automatically "
"create a new equipment under this category."
msgstr ""
"نام مستعار ایمیل برای این دسته تجهیزات. ایمیل های جدید به طور خودکار تجهیزات"
" جدیدی را تحت این دسته ایجاد می کنند."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__email_cc
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Email cc"
msgstr "ایمیل سی سی"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "دامنه‌ی ایمیل مانند 'example.com' در '<EMAIL>'"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_until
msgid "End Date"
msgstr "تاریخ پایان"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action_from_category_form
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__equipment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__equipment_ids
#: model:ir.ui.menu,name:maintenance.menu_equipment_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Equipment"
msgstr "تجهیزات"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_cat_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_mat_assign
msgid "Equipment Assigned"
msgstr "تجهیزات واگذار شده"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_category_action
#: model:ir.ui.menu,name:maintenance.menu_maintenance_cat
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Equipment Categories"
msgstr "دسته بندی تجهیزات"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__category_id
msgid "Equipment Category"
msgstr "دسته تجهیزات"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_count
msgid "Equipment Count"
msgstr "تعداد تجهیزات"

#. module: maintenance
#: model:res.groups,name:maintenance.group_equipment_manager
msgid "Equipment Manager"
msgstr "مدیر تجهیزات"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__name
msgid "Equipment Name"
msgstr "نام تجهیزات"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Estimated Next Failure"
msgstr "زمان تخمینی خرابی بعدی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr "زمان تخمینی تا خطای بعدی (به روز)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__expected_mtbf
msgid "Expected MTBF"
msgstr "میانگین مدت زمان خرابی پیش‌بینی‌شده"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__expected_mtbf
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Expected Mean Time Between Failure"
msgstr "مدت زمان مورد انتظار بین خرابی‌ها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__fold
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__fold
msgid "Folded in Maintenance Pipe"
msgstr "تا شده در تعمیر و نگهداری"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr "روند درخواست را دنبال کنید و با همکار ارتباط برقرار کنید."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_follower_ids
msgid "Followers"
msgstr "دنبال کنندگان"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "آیکون فونت عالی به عبارتی fa-tasks"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__forever
msgid "Forever"
msgstr "برای همیشه"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Future Activities"
msgstr "فعالیت های آینده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_google_slide
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__google_slide
msgid "Google Slide"
msgstr "اسلاید گوگل"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Google Slide Link"
msgstr "لینک اسلاید گوگل"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Group by..."
msgstr "گروه‌بندی بر مبنای..."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_printer1
msgid "HP Inkjet printer"
msgstr "پرینتر HP جوهری"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer11
#: model:maintenance.equipment,name:maintenance.equipment_computer9
msgid "HP Laptop"
msgstr "لپتاب HP"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__has_message
msgid "Has Message"
msgstr "آیا دارای پیام است"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__3
msgid "High"
msgstr "بالا"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "High-priority"
msgstr "اولویت بالا"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__id
msgid "ID"
msgstr "شناسه"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"شناسه رکورد والد حاوی نام مستعار (مثال: پروژه دارای نام مستعار ایجاد وظیفه)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon"
msgstr "شمایل"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "آیکون برای نشان دادن یک فعالیت استثنا."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "اگر علامت زده شود، برخی از پیام ها دارای خطای تحویل هستند."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"در صورت تنظیم، این محتوا به‌جای پیام پیش‌فرض، به‌طور خودکار برای کاربران "
"غیرمجاز ارسال می‌شود."

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__normal
#: model:maintenance.stage,name:maintenance.stage_1
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "In Progress"
msgstr "در جریان"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_type
msgid "Instruction"
msgstr "راهنما"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Instructions"
msgstr "راهنمایی"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_maintenance
msgid "Internal Maintenance"
msgstr "تعمیر و نگهداری داخلی"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Internal Notes"
msgstr "یادداشت های داخلی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_is_follower
msgid "Is Follower"
msgstr "آیا دنبال می کند"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__kanban_state
msgid "Kanban State"
msgstr "وضعیت کانبان"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Late Activities"
msgstr "فعالیتهای اخیر"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Latest Failure"
msgstr "اخرین خرابی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__latest_failure_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__latest_failure_date
msgid "Latest Failure Date"
msgstr "تاریخ آخرین خرابی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "تشخیص ورودی بر اساس قسمت محلی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__location
msgid "Location"
msgstr "مکان"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_losses
msgid "Losses Analysis"
msgstr "تجزیه و تحلیل ضرر و زیان"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__1
msgid "Low"
msgstr "پایین"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mtbf
msgid "MTBF"
msgstr "MTBF"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mttr
msgid "MTTR"
msgstr "MTTR"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_ids
#: model:ir.ui.menu,name:maintenance.menu_m_request
#: model:ir.ui.menu,name:maintenance.menu_maintenance_title
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Maintenance"
msgstr "نگهداری"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_request_calendar
msgid "Maintenance Calendar"
msgstr "تقویم تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_count
msgid "Maintenance Count"
msgstr "تعداد تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "تجهیزات نگهداری"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment_category
msgid "Maintenance Equipment Category"
msgstr "دسته تجهیزات تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_mixin
msgid "Maintenance Maintained Item"
msgstr "مورد نگهداری شده تعمیرات و نگهداری"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_request
#: model:mail.activity.type,name:maintenance.mail_act_maintenance_request
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_request_view_activity
msgid "Maintenance Request"
msgstr "درخواست نگهداری"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_cat_req_created
msgid "Maintenance Request Created"
msgstr "درخواست تعمیر و نگهداری ایجاد شد"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Maintenance Request Search"
msgstr "جستجوی درخواست تعمیر و نگهداری"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_tree
msgid "Maintenance Request Stage"
msgstr "مرحله درخواست تعمیر و نگهداری"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_search
msgid "Maintenance Request Stages"
msgstr "مراحل درخواست تعمیر و نگهداری"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_created
msgid "Maintenance Request created"
msgstr "درخواست تعمیر و نگهداری ایجاد شد"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_cal
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_from_equipment
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_link
#: model:ir.actions.act_window,name:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model:ir.actions.act_window,name:maintenance.maintenance_request_action_reports
#: model:ir.ui.menu,name:maintenance.maintenance_request_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_request_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Maintenance Requests"
msgstr "درخواستهای نگهداری"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_stage
msgid "Maintenance Stage"
msgstr "مرحله تعمیر و نگهداری"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_stage_configuration
msgid "Maintenance Stages"
msgstr "مراحل تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_team_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_tree
msgid "Maintenance Team"
msgstr "تیم تعمیر و نگهداری"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_dashboard_action
#: model:ir.model,name:maintenance.model_maintenance_team
#: model:ir.ui.menu,name:maintenance.menu_maintenance_teams
msgid "Maintenance Teams"
msgstr "تیم‌های تعمیر و نگهداری"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_type
msgid "Maintenance Type"
msgstr "نوع تعمیر و نگهداری"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time Between Failure"
msgstr "میانگین زمان بین خرابی"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mtbf
msgid ""
"Mean Time Between Failure, computed based on done corrective maintenances."
msgstr "زمان میانگین بین خرابی، محاسبه شده بر اساس تعمیرات اصلاحی انجام شده."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mttr
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time To Repair"
msgstr "میانگین زمان تعمیر"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_ids
msgid "Messages"
msgstr "پیام ها"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_metrology
msgid "Metrology"
msgstr "متروولوژی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__model
msgid "Model"
msgstr "مدل"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_monitor
msgid "Monitors"
msgstr "مــونــیــتــورهــا"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__month
msgid "Months"
msgstr "ماه"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__my_activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "موعد نهای فعالیت من"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "My Equipment"
msgstr "تجهیزات من"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "My Maintenances"
msgstr "تعمیر و نگهداری من"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Name"
msgstr "نام"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_0
msgid "New Request"
msgstr "درخواست جدید"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_calendar_event_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "رویداد تقویم فعالیت بعدی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد فعالیت بعدی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_summary
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_summary
msgid "Next Activity Summary"
msgstr "خلاصه فعالیت بعدی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_id
msgid "Next Activity Type"
msgstr "نوع فعالیت بعدی"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__2
msgid "Normal"
msgstr "عادی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__note
msgid "Note"
msgstr "یادداشت"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Notes"
msgstr "یادداشت‌ها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count
msgid "Number of Requests"
msgstr "تعداد درخواست ها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_block
msgid "Number of Requests Blocked"
msgstr "تعداد درخواست های مسدود شده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_date
msgid "Number of Requests Scheduled"
msgstr "تعداد درخواست های برنامه ریزی شده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_unscheduled
msgid "Number of Requests Unscheduled"
msgstr "تعداد درخواست های برنامه ریزی نشده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_high_priority
msgid "Number of Requests in High Priority"
msgstr "تعداد درخواست ها در اولویت بالا"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "تعداد پیام هایی که نیاز به اقدام دارند"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"شناسه اختیاری یک موضوع (رکورد) که تمام پیام‌های دریافتی به آن پیوست می‌شود، "
"حتی اگر به آن پاسخ نداده باشند. اگر تنظیم شود، ایجاد رکوردهای جدید به طور "
"کامل غیرفعال می شود."

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_oee
msgid "Overall Equipment Effectiveness (OEE)"
msgstr "اثربخشی کلی تجهیزات (OEE)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__owner_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Owner"
msgstr "مالک‌"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_pdf
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid "Parent Model"
msgstr "مدل والد"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "شناسه موضوع رکورد والد"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"مدل والد دارای نام مستعار. مدلی که مرجع نام مستعار را نگه می دارد لزوماً "
"مدلی نیست که توسط alias_model_id (مثال: پروژه (parent_model) و task (model))"
" ارائه شده است."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__instruction_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"آدرس URL ارائه Google خود را جایگذاری کنید. مطمئن شوید که دسترسی به سند "
"عمومی است."

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_phone
msgid "Phones"
msgstr "تلفن‌ها"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"خط مشی ارسال پیام در سند با استفاده از mailgateway.\n"
"- همه: همه می توانند پست کنند\n"
"- شرکا: فقط شرکای تأیید شده\n"
"- دنبال کنندگان: فقط دنبال کنندگان سند مرتبط یا اعضای کانال های زیر\n"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__preventive
msgid "Preventive"
msgstr "پیشگیرانه"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_printer
msgid "Printers"
msgstr "پـــرینترها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__priority
msgid "Priority"
msgstr "اولویت"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Product Information"
msgstr "اطلاعات محصول"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Ready"
msgstr "آماده"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__done
msgid "Ready for next stage"
msgstr "آماده برای مرحله بعد"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Record Colour"
msgstr "رنگ رکورد"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid "Record Thread ID"
msgstr "شناسه موضوع رکورد"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__recurring_maintenance
msgid "Recurrent"
msgstr "مکرر"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Reopen Request"
msgstr "درخواست را دوباره باز کنید"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_3
msgid "Repaired"
msgstr "تعمیر شده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_interval
msgid "Repeat Every"
msgstr "تکرار هر"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_unit
msgid "Repeat Unit"
msgstr "واحد تکرار"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_reports
msgid "Reporting"
msgstr "گزارش"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__request_ids
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Request"
msgstr "درخواست"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_created
msgid "Request Created"
msgstr "درخواست ایجاد شد"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__request_date
msgid "Request Date"
msgstr "تاریخ درخواست"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__done
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__done
msgid "Request Done"
msgstr "درخواست انجام شده"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid "Request planned for %s"
msgstr "درخواست برنامه‌ریزی شده برای %s"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Requested By"
msgstr "درخواست شده توسط"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "Requested by:"
msgstr "درخواست شده توسط:"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_ids
msgid "Requests"
msgstr "درخواست ها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__technician_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Responsible"
msgstr "پاسخگو"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_user_id
msgid "Responsible User"
msgstr "کاربر مسئول"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_monitor1
#: model:maintenance.equipment,name:maintenance.equipment_monitor4
#: model:maintenance.equipment,name:maintenance.equipment_monitor6
msgid "Samsung Monitor 15\""
msgstr "مانیتور سامسونگ 15\""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Scheduled"
msgstr "برنامه ریزی شد"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__schedule_date
msgid "Scheduled Date"
msgstr "تاریخ برنامه ریزی شده"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_4
msgid "Scrap"
msgstr "اسقاط"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__scrap_date
msgid "Scrap Date"
msgstr "تاریخ اسقاط"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Search"
msgstr "جستجو"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__serial_no
msgid "Serial Number"
msgstr "شماره سریال"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__archive
msgid ""
"Set archive to true to hide the maintenance request without deleting it."
msgstr ""
"بایگانی را روی درست تنظیم کنید تا درخواست تعمیر و نگهداری بدون حذف آن پنهان "
"شود."

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.action_maintenance_configuration
#: model:ir.ui.menu,name:maintenance.menu_maintenance_config
msgid "Settings"
msgstr "تنظیمات"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Show all records which has next action date is before today"
msgstr "تمام رکوردهایی که تاریخ اقدام بعدی آن قبل از امروز است را نشان بده"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_software
msgid "Software"
msgstr "نرم افزار"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__stage_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Stage"
msgstr "مرحله"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_stage_action
msgid "Stages"
msgstr "مراحل"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_status
msgid "Status Changed"
msgstr "وضعیت تغییر کرد"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"وضعیت بر اساس فعالیت ها\n"
"سررسید: تاریخ سررسید گذشته است\n"
"امروز: تاریخ فعالیت امروز است\n"
"برنامه ریزی شده: فعالیت های آینده."

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_status
msgid "Status changed"
msgstr "وضعیت تغییر کرد"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_subcontractor
msgid "Subcontractor"
msgstr "پیمانکار"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__name
msgid "Subjects"
msgstr "موضوعات"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Team"
msgstr "تیم"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__member_ids
msgid "Team Members"
msgstr "اعضای تیم"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__name
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "Team Name"
msgstr "نام تیم"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_team_action_settings
msgid "Teams"
msgstr "تیم‌ها"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Technician"
msgstr "متخصص فنی"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_text
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__text
msgid "Text"
msgstr "متن"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"مدلی (Odoo Document Kind) که این نام مستعار با آن مطابقت دارد. هر ایمیل "
"دریافتی که به سابقه موجود پاسخ نمی‌دهد باعث ایجاد یک رکورد جدید از این مدل "
"می‌شود (به عنوان مثال یک وظیفه پروژه)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"نام مستعار ایمیل، به عنوان مثال 'jobs'. اگر می خواهید ایمیل های "
"<<EMAIL>> را دریافت کنید"

#. module: maintenance
#: model:res.groups,comment:maintenance.group_equipment_manager
msgid "The user will be able to manage equipment."
msgstr "کاربر قادر خواهد بود تا تجهیزات را مدیریت کند."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__effective_date
msgid "This date will be used to compute the Mean Time Between Failure."
msgstr "این تاریخ برای محاسبه میانگین زمان بین خرابی استفاده خواهد شد."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "To Do"
msgstr "جهت اقدام"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Today Activities"
msgstr "فعالیت های امروز"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Top Priorities"
msgstr "اولویت های برتر"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid ""
"Track equipment and link it to an employee or department.\n"
"                You will be able to manage allocations, issues and maintenance of your equipment."
msgstr ""
"تجهیزات را پیگیری کنید و آن‌ها را به یک کارمند یا بخش مرتبط کنید.\n"
"می‌توانید تخصیص، مشکلات و نگهداری تجهیزات خود را مدیریت کنید."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع فعالیت استثنایی برای رکورد."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unassigned"
msgstr "محول نشده"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Under Maintenance"
msgstr "تحت تعمیر و نگهداری"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unread Messages"
msgstr "پیام های ناخوانده"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Unscheduled"
msgstr "برنامه ریزی نشده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_type
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__until
msgid "Until"
msgstr "تا"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Upload your PDF file."
msgstr "فایل PDF خود را بارگذاری کنید."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Used in location"
msgstr "استفاده شده در محل"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Vendor"
msgstr "فروشنده"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_ref
msgid "Vendor Reference"
msgstr "مرجع فروشنده"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__0
msgid "Very Low"
msgstr "خیلی کم"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__warranty_date
msgid "Warranty Expiration Date"
msgstr "تاریخ انقضای گارانتی"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__week
msgid "Weeks"
msgstr "هفته"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__year
msgid "Years"
msgstr "سال"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid ""
"You cannot delete an equipment category containing equipment or maintenance "
"requests."
msgstr ""
"شما نمی‌توانید دسته‌بندی تجهیزاتی که دارای تجهیزات یا درخواست‌های نگهداری "
"است را حذف کنید."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Your instructions"
msgstr "دستورات شما"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "days"
msgstr "روز"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "e.g. Internal Maintenance"
msgstr "به عنوان مثال، تعمیر و نگهداری داخلی"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "e.g. LED Monitor"
msgstr "به عنوان مثال، مانیتور LED"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. Monitors"
msgstr "به عنوان مثال، مانیتورها"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "e.g. Screen not working"
msgstr "به عنوان مثال، صفحه نمایش کار نمی کند"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. domain.com"
msgstr "به طورمثال  domain.com"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_graph
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_pivot
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_tree
msgid "maintenance Request"
msgstr "درخواست تعمیر و نگهداری"
