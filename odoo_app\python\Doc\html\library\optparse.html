<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="optparse — Parser for command line options" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/optparse.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/optparse.py optparse is a more convenient, flexible, and powerful library for parsing command-line options than the old getopt module. optparse uses a more declarative style of com..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/optparse.py optparse is a more convenient, flexible, and powerful library for parsing command-line options than the old getopt module. optparse uses a more declarative style of com..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>optparse — Parser for command line options &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="ossaudiodev — Access to OSS-compatible audio devices" href="ossaudiodev.html" />
    <link rel="prev" title="nntplib — NNTP protocol client" href="nntplib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/optparse.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a><ul>
<li><a class="reference internal" href="#background">Background</a><ul>
<li><a class="reference internal" href="#terminology">Terminology</a></li>
<li><a class="reference internal" href="#what-are-options-for">What are options for?</a></li>
<li><a class="reference internal" href="#what-are-positional-arguments-for">What are positional arguments for?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tutorial">Tutorial</a><ul>
<li><a class="reference internal" href="#understanding-option-actions">Understanding option actions</a></li>
<li><a class="reference internal" href="#the-store-action">The store action</a></li>
<li><a class="reference internal" href="#handling-boolean-flag-options">Handling boolean (flag) options</a></li>
<li><a class="reference internal" href="#other-actions">Other actions</a></li>
<li><a class="reference internal" href="#default-values">Default values</a></li>
<li><a class="reference internal" href="#generating-help">Generating help</a><ul>
<li><a class="reference internal" href="#grouping-options">Grouping Options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#printing-a-version-string">Printing a version string</a></li>
<li><a class="reference internal" href="#how-optparse-handles-errors">How <code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> handles errors</a></li>
<li><a class="reference internal" href="#putting-it-all-together">Putting it all together</a></li>
</ul>
</li>
<li><a class="reference internal" href="#reference-guide">Reference Guide</a><ul>
<li><a class="reference internal" href="#creating-the-parser">Creating the parser</a></li>
<li><a class="reference internal" href="#populating-the-parser">Populating the parser</a></li>
<li><a class="reference internal" href="#defining-options">Defining options</a></li>
<li><a class="reference internal" href="#option-attributes">Option attributes</a></li>
<li><a class="reference internal" href="#standard-option-actions">Standard option actions</a></li>
<li><a class="reference internal" href="#standard-option-types">Standard option types</a></li>
<li><a class="reference internal" href="#parsing-arguments">Parsing arguments</a></li>
<li><a class="reference internal" href="#querying-and-manipulating-your-option-parser">Querying and manipulating your option parser</a></li>
<li><a class="reference internal" href="#conflicts-between-options">Conflicts between options</a></li>
<li><a class="reference internal" href="#cleanup">Cleanup</a></li>
<li><a class="reference internal" href="#other-methods">Other methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#option-callbacks">Option Callbacks</a><ul>
<li><a class="reference internal" href="#defining-a-callback-option">Defining a callback option</a></li>
<li><a class="reference internal" href="#how-callbacks-are-called">How callbacks are called</a></li>
<li><a class="reference internal" href="#raising-errors-in-a-callback">Raising errors in a callback</a></li>
<li><a class="reference internal" href="#callback-example-1-trivial-callback">Callback example 1: trivial callback</a></li>
<li><a class="reference internal" href="#callback-example-2-check-option-order">Callback example 2: check option order</a></li>
<li><a class="reference internal" href="#callback-example-3-check-option-order-generalized">Callback example 3: check option order (generalized)</a></li>
<li><a class="reference internal" href="#callback-example-4-check-arbitrary-condition">Callback example 4: check arbitrary condition</a></li>
<li><a class="reference internal" href="#callback-example-5-fixed-arguments">Callback example 5: fixed arguments</a></li>
<li><a class="reference internal" href="#callback-example-6-variable-arguments">Callback example 6: variable arguments</a></li>
</ul>
</li>
<li><a class="reference internal" href="#extending-optparse">Extending <code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a><ul>
<li><a class="reference internal" href="#adding-new-types">Adding new types</a></li>
<li><a class="reference internal" href="#adding-new-actions">Adding new actions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="nntplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ossaudiodev.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code> — Access to OSS-compatible audio devices</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/optparse.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ossaudiodev.html" title="ossaudiodev — Access to OSS-compatible audio devices"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="nntplib.html" title="nntplib — NNTP protocol client"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-optparse">
<span id="optparse-parser-for-command-line-options"></span><h1><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> — Parser for command line options<a class="headerlink" href="#module-optparse" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/optparse.py">Lib/optparse.py</a></p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.2: </span>The <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> module is deprecated and will not be developed further;
development will continue with the <a class="reference internal" href="argparse.html#module-argparse" title="argparse: Command-line option and argument parsing library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">argparse</span></code></a> module.</p>
</div>
<hr class="docutils" />
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> is a more convenient, flexible, and powerful library for parsing
command-line options than the old <a class="reference internal" href="getopt.html#module-getopt" title="getopt: Portable parser for command line options; support both short and long option names."><code class="xref py py-mod docutils literal notranslate"><span class="pre">getopt</span></code></a> module.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> uses a
more declarative style of command-line parsing: you create an instance of
<a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>, populate it with options, and parse the command
line. <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> allows users to specify options in the conventional
GNU/POSIX syntax, and additionally generates usage and help messages for you.</p>
<p>Here’s an example of using <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> in a simple script:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">optparse</span> <span class="kn">import</span> <span class="n">OptionParser</span>
<span class="o">...</span>
<span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">()</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--file&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;write report to FILE&quot;</span><span class="p">,</span> <span class="n">metavar</span><span class="o">=</span><span class="s2">&quot;FILE&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="s2">&quot;--quiet&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;don&#39;t print status messages to stdout&quot;</span><span class="p">)</span>

<span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</pre></div>
</div>
<p>With these few lines of code, users of your script can now do the “usual thing”
on the command-line, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">--</span><span class="n">file</span><span class="o">=</span><span class="n">outfile</span> <span class="o">-</span><span class="n">q</span>
</pre></div>
</div>
<p>As it parses the command line, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> sets attributes of the
<code class="docutils literal notranslate"><span class="pre">options</span></code> object returned by <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a> based on user-supplied
command-line values.  When <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a> returns from parsing this command
line, <code class="docutils literal notranslate"><span class="pre">options.filename</span></code> will be <code class="docutils literal notranslate"><span class="pre">&quot;outfile&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">options.verbose</span></code> will be
<code class="docutils literal notranslate"><span class="pre">False</span></code>.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> supports both long and short options, allows short
options to be merged together, and allows options to be associated with their
arguments in a variety of ways.  Thus, the following command lines are all
equivalent to the above example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">-</span><span class="n">f</span> <span class="n">outfile</span> <span class="o">--</span><span class="n">quiet</span>
<span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">--</span><span class="n">quiet</span> <span class="o">--</span><span class="n">file</span> <span class="n">outfile</span>
<span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">-</span><span class="n">q</span> <span class="o">-</span><span class="n">foutfile</span>
<span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">-</span><span class="n">qfoutfile</span>
</pre></div>
</div>
<p>Additionally, users can run one of the following</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">-</span><span class="n">h</span>
<span class="o">&lt;</span><span class="n">yourscript</span><span class="o">&gt;</span> <span class="o">--</span><span class="n">help</span>
</pre></div>
</div>
<p>and <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will print out a brief summary of your script’s options:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Usage: &lt;yourscript&gt; [options]

Options:
  -h, --help            show this help message and exit
  -f FILE, --file=FILE  write report to FILE
  -q, --quiet           don&#39;t print status messages to stdout
</pre></div>
</div>
<p>where the value of <em>yourscript</em> is determined at runtime (normally from
<code class="docutils literal notranslate"><span class="pre">sys.argv[0]</span></code>).</p>
<section id="background">
<span id="optparse-background"></span><h2>Background<a class="headerlink" href="#background" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> was explicitly designed to encourage the creation of programs
with straightforward, conventional command-line interfaces.  To that end, it
supports only the most common command-line syntax and semantics conventionally
used under Unix.  If you are unfamiliar with these conventions, read this
section to acquaint yourself with them.</p>
<section id="terminology">
<span id="optparse-terminology"></span><h3>Terminology<a class="headerlink" href="#terminology" title="Link to this heading">¶</a></h3>
<dl>
<dt>argument</dt><dd><p>a string entered on the command-line, and passed by the shell to <code class="docutils literal notranslate"><span class="pre">execl()</span></code>
or <code class="docutils literal notranslate"><span class="pre">execv()</span></code>.  In Python, arguments are elements of <code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>
(<code class="docutils literal notranslate"><span class="pre">sys.argv[0]</span></code> is the name of the program being executed).  Unix shells
also use the term “word”.</p>
<p>It is occasionally desirable to substitute an argument list other than
<code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>, so you should read “argument” as “an element of
<code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>, or of some other list provided as a substitute for
<code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>”.</p>
</dd>
<dt>option</dt><dd><p>an argument used to supply extra information to guide or customize the
execution of a program.  There are many different syntaxes for options; the
traditional Unix syntax is a hyphen (“-”) followed by a single letter,
e.g. <code class="docutils literal notranslate"><span class="pre">-x</span></code> or <code class="docutils literal notranslate"><span class="pre">-F</span></code>.  Also, traditional Unix syntax allows multiple
options to be merged into a single argument, e.g. <code class="docutils literal notranslate"><span class="pre">-x</span> <span class="pre">-F</span></code> is equivalent
to <code class="docutils literal notranslate"><span class="pre">-xF</span></code>.  The GNU project introduced <code class="docutils literal notranslate"><span class="pre">--</span></code> followed by a series of
hyphen-separated words, e.g. <code class="docutils literal notranslate"><span class="pre">--file</span></code> or <code class="docutils literal notranslate"><span class="pre">--dry-run</span></code>.  These are the
only two option syntaxes provided by <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>.</p>
<p>Some other option syntaxes that the world has seen include:</p>
<ul class="simple">
<li><p>a hyphen followed by a few letters, e.g. <code class="docutils literal notranslate"><span class="pre">-pf</span></code> (this is <em>not</em> the same
as multiple options merged into a single argument)</p></li>
<li><p>a hyphen followed by a whole word, e.g. <code class="docutils literal notranslate"><span class="pre">-file</span></code> (this is technically
equivalent to the previous syntax, but they aren’t usually seen in the same
program)</p></li>
<li><p>a plus sign followed by a single letter, or a few letters, or a word, e.g.
<code class="docutils literal notranslate"><span class="pre">+f</span></code>, <code class="docutils literal notranslate"><span class="pre">+rgb</span></code></p></li>
<li><p>a slash followed by a letter, or a few letters, or a word, e.g. <code class="docutils literal notranslate"><span class="pre">/f</span></code>,
<code class="docutils literal notranslate"><span class="pre">/file</span></code></p></li>
</ul>
<p>These option syntaxes are not supported by <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>, and they never
will be.  This is deliberate: the first three are non-standard on any
environment, and the last only makes sense if you’re exclusively targeting
Windows or certain legacy platforms (e.g. VMS, MS-DOS).</p>
</dd>
<dt>option argument</dt><dd><p>an argument that follows an option, is closely associated with that option,
and is consumed from the argument list when that option is. With
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>, option arguments may either be in a separate argument from
their option:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-f foo
--file foo
</pre></div>
</div>
<p>or included in the same argument:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-ffoo
--file=foo
</pre></div>
</div>
<p>Typically, a given option either takes an argument or it doesn’t. Lots of
people want an “optional option arguments” feature, meaning that some options
will take an argument if they see it, and won’t if they don’t.  This is
somewhat controversial, because it makes parsing ambiguous: if <code class="docutils literal notranslate"><span class="pre">-a</span></code> takes
an optional argument and <code class="docutils literal notranslate"><span class="pre">-b</span></code> is another option entirely, how do we
interpret <code class="docutils literal notranslate"><span class="pre">-ab</span></code>?  Because of this ambiguity, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> does not
support this feature.</p>
</dd>
<dt>positional argument</dt><dd><p>something leftover in the argument list after options have been parsed, i.e.
after options and their arguments have been parsed and removed from the
argument list.</p>
</dd>
<dt>required option</dt><dd><p>an option that must be supplied on the command-line; note that the phrase
“required option” is self-contradictory in English.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> doesn’t
prevent you from implementing required options, but doesn’t give you much
help at it either.</p>
</dd>
</dl>
<p>For example, consider this hypothetical command-line:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">prog</span> <span class="o">-</span><span class="n">v</span> <span class="o">--</span><span class="n">report</span> <span class="n">report</span><span class="o">.</span><span class="n">txt</span> <span class="n">foo</span> <span class="n">bar</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">-v</span></code> and <code class="docutils literal notranslate"><span class="pre">--report</span></code> are both options.  Assuming that <code class="docutils literal notranslate"><span class="pre">--report</span></code>
takes one argument, <code class="docutils literal notranslate"><span class="pre">report.txt</span></code> is an option argument.  <code class="docutils literal notranslate"><span class="pre">foo</span></code> and
<code class="docutils literal notranslate"><span class="pre">bar</span></code> are positional arguments.</p>
</section>
<section id="what-are-options-for">
<span id="optparse-what-options-for"></span><h3>What are options for?<a class="headerlink" href="#what-are-options-for" title="Link to this heading">¶</a></h3>
<p>Options are used to provide extra information to tune or customize the execution
of a program.  In case it wasn’t clear, options are usually <em>optional</em>.  A
program should be able to run just fine with no options whatsoever.  (Pick a
random program from the Unix or GNU toolsets.  Can it run without any options at
all and still make sense?  The main exceptions are <code class="docutils literal notranslate"><span class="pre">find</span></code>, <code class="docutils literal notranslate"><span class="pre">tar</span></code>, and
<code class="docutils literal notranslate"><span class="pre">dd</span></code>—all of which are mutant oddballs that have been rightly criticized
for their non-standard syntax and confusing interfaces.)</p>
<p>Lots of people want their programs to have “required options”.  Think about it.
If it’s required, then it’s <em>not optional</em>!  If there is a piece of information
that your program absolutely requires in order to run successfully, that’s what
positional arguments are for.</p>
<p>As an example of good command-line interface design, consider the humble <code class="docutils literal notranslate"><span class="pre">cp</span></code>
utility, for copying files.  It doesn’t make much sense to try to copy files
without supplying a destination and at least one source. Hence, <code class="docutils literal notranslate"><span class="pre">cp</span></code> fails if
you run it with no arguments.  However, it has a flexible, useful syntax that
does not require any options at all:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">cp</span> <span class="n">SOURCE</span> <span class="n">DEST</span>
<span class="n">cp</span> <span class="n">SOURCE</span> <span class="o">...</span> <span class="n">DEST</span><span class="o">-</span><span class="n">DIR</span>
</pre></div>
</div>
<p>You can get pretty far with just that.  Most <code class="docutils literal notranslate"><span class="pre">cp</span></code> implementations provide a
bunch of options to tweak exactly how the files are copied: you can preserve
mode and modification time, avoid following symlinks, ask before clobbering
existing files, etc.  But none of this distracts from the core mission of
<code class="docutils literal notranslate"><span class="pre">cp</span></code>, which is to copy either one file to another, or several files to another
directory.</p>
</section>
<section id="what-are-positional-arguments-for">
<span id="optparse-what-positional-arguments-for"></span><h3>What are positional arguments for?<a class="headerlink" href="#what-are-positional-arguments-for" title="Link to this heading">¶</a></h3>
<p>Positional arguments are for those pieces of information that your program
absolutely, positively requires to run.</p>
<p>A good user interface should have as few absolute requirements as possible.  If
your program requires 17 distinct pieces of information in order to run
successfully, it doesn’t much matter <em>how</em> you get that information from the
user—most people will give up and walk away before they successfully run the
program.  This applies whether the user interface is a command-line, a
configuration file, or a GUI: if you make that many demands on your users, most
of them will simply give up.</p>
<p>In short, try to minimize the amount of information that users are absolutely
required to supply—use sensible defaults whenever possible.  Of course, you
also want to make your programs reasonably flexible.  That’s what options are
for.  Again, it doesn’t matter if they are entries in a config file, widgets in
the “Preferences” dialog of a GUI, or command-line options—the more options
you implement, the more flexible your program is, and the more complicated its
implementation becomes.  Too much flexibility has drawbacks as well, of course;
too many options can overwhelm users and make your code much harder to maintain.</p>
</section>
</section>
<section id="tutorial">
<span id="optparse-tutorial"></span><h2>Tutorial<a class="headerlink" href="#tutorial" title="Link to this heading">¶</a></h2>
<p>While <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> is quite flexible and powerful, it’s also straightforward
to use in most cases.  This section covers the code patterns that are common to
any <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>-based program.</p>
<p>First, you need to import the OptionParser class; then, early in the main
program, create an OptionParser instance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">optparse</span> <span class="kn">import</span> <span class="n">OptionParser</span>
<span class="o">...</span>
<span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">()</span>
</pre></div>
</div>
<p>Then you can start defining options.  The basic syntax is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="n">opt_str</span><span class="p">,</span> <span class="o">...</span><span class="p">,</span>
                  <span class="n">attr</span><span class="o">=</span><span class="n">value</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
<p>Each option has one or more option strings, such as <code class="docutils literal notranslate"><span class="pre">-f</span></code> or <code class="docutils literal notranslate"><span class="pre">--file</span></code>,
and several option attributes that tell <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> what to expect and what
to do when it encounters that option on the command line.</p>
<p>Typically, each option will have one short option string and one long option
string, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--file&quot;</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
<p>You’re free to define as many short option strings and as many long option
strings as you like (including zero), as long as there is at least one option
string overall.</p>
<p>The option strings passed to <a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.add_option()</span></code></a> are effectively
labels for the
option defined by that call.  For brevity, we will frequently refer to
<em>encountering an option</em> on the command line; in reality, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>
encounters <em>option strings</em> and looks up options from them.</p>
<p>Once all of your options are defined, instruct <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> to parse your
program’s command line:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</pre></div>
</div>
<p>(If you like, you can pass a custom argument list to <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a>, but
that’s rarely necessary: by default it uses <code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>.)</p>
<p><a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a> returns two values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code>, an object containing values for all of your options—e.g. if
<code class="docutils literal notranslate"><span class="pre">--file</span></code> takes a single string argument, then <code class="docutils literal notranslate"><span class="pre">options.file</span></code> will be the
filename supplied by the user, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the user did not supply that
option</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">args</span></code>, the list of positional arguments leftover after parsing options</p></li>
</ul>
<p>This tutorial section only covers the four most important option attributes:
<a class="reference internal" href="#optparse.Option.action" title="optparse.Option.action"><code class="xref py py-attr docutils literal notranslate"><span class="pre">action</span></code></a>, <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>, <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>
(destination), and <a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a>. Of these, <a class="reference internal" href="#optparse.Option.action" title="optparse.Option.action"><code class="xref py py-attr docutils literal notranslate"><span class="pre">action</span></code></a> is the
most fundamental.</p>
<section id="understanding-option-actions">
<span id="optparse-understanding-option-actions"></span><h3>Understanding option actions<a class="headerlink" href="#understanding-option-actions" title="Link to this heading">¶</a></h3>
<p>Actions tell <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> what to do when it encounters an option on the
command line.  There is a fixed set of actions hard-coded into <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>;
adding new actions is an advanced topic covered in section
<a class="reference internal" href="#optparse-extending-optparse"><span class="std std-ref">Extending optparse</span></a>.  Most actions tell <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> to store
a value in some variable—for example, take a string from the command line and
store it in an attribute of <code class="docutils literal notranslate"><span class="pre">options</span></code>.</p>
<p>If you don’t specify an option action, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> defaults to <code class="docutils literal notranslate"><span class="pre">store</span></code>.</p>
</section>
<section id="the-store-action">
<span id="optparse-store-action"></span><h3>The store action<a class="headerlink" href="#the-store-action" title="Link to this heading">¶</a></h3>
<p>The most common option action is <code class="docutils literal notranslate"><span class="pre">store</span></code>, which tells <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> to take
the next argument (or the remainder of the current argument), ensure that it is
of the correct type, and store it to your chosen destination.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--file&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;string&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Now let’s make up a fake command line and ask <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> to parse it:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">args</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;foo.txt&quot;</span><span class="p">]</span>
<span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
</pre></div>
</div>
<p>When <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> sees the option string <code class="docutils literal notranslate"><span class="pre">-f</span></code>, it consumes the next
argument, <code class="docutils literal notranslate"><span class="pre">foo.txt</span></code>, and stores it in <code class="docutils literal notranslate"><span class="pre">options.filename</span></code>.  So, after this
call to <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a>, <code class="docutils literal notranslate"><span class="pre">options.filename</span></code> is <code class="docutils literal notranslate"><span class="pre">&quot;foo.txt&quot;</span></code>.</p>
<p>Some other option types supported by <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> are <code class="docutils literal notranslate"><span class="pre">int</span></code> and <code class="docutils literal notranslate"><span class="pre">float</span></code>.
Here’s an option that expects an integer argument:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-n&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;int&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;num&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that this option has no long option string, which is perfectly acceptable.
Also, there’s no explicit action, since the default is <code class="docutils literal notranslate"><span class="pre">store</span></code>.</p>
<p>Let’s parse another fake command-line.  This time, we’ll jam the option argument
right up against the option: since <code class="docutils literal notranslate"><span class="pre">-n42</span></code> (one argument) is equivalent to
<code class="docutils literal notranslate"><span class="pre">-n</span> <span class="pre">42</span></code> (two arguments), the code</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">([</span><span class="s2">&quot;-n42&quot;</span><span class="p">])</span>
<span class="nb">print</span><span class="p">(</span><span class="n">options</span><span class="o">.</span><span class="n">num</span><span class="p">)</span>
</pre></div>
</div>
<p>will print <code class="docutils literal notranslate"><span class="pre">42</span></code>.</p>
<p>If you don’t specify a type, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> assumes <code class="docutils literal notranslate"><span class="pre">string</span></code>.  Combined with
the fact that the default action is <code class="docutils literal notranslate"><span class="pre">store</span></code>, that means our first example can
be a lot shorter:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--file&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>If you don’t supply a destination, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> figures out a sensible
default from the option strings: if the first long option string is
<code class="docutils literal notranslate"><span class="pre">--foo-bar</span></code>, then the default destination is <code class="docutils literal notranslate"><span class="pre">foo_bar</span></code>.  If there are no
long option strings, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> looks at the first short option string: the
default destination for <code class="docutils literal notranslate"><span class="pre">-f</span></code> is <code class="docutils literal notranslate"><span class="pre">f</span></code>.</p>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> also includes the built-in <code class="docutils literal notranslate"><span class="pre">complex</span></code> type.  Adding
types is covered in section <a class="reference internal" href="#optparse-extending-optparse"><span class="std std-ref">Extending optparse</span></a>.</p>
</section>
<section id="handling-boolean-flag-options">
<span id="optparse-handling-boolean-options"></span><h3>Handling boolean (flag) options<a class="headerlink" href="#handling-boolean-flag-options" title="Link to this heading">¶</a></h3>
<p>Flag options—set a variable to true or false when a particular option is
seen—are quite common.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> supports them with two separate actions,
<code class="docutils literal notranslate"><span class="pre">store_true</span></code> and <code class="docutils literal notranslate"><span class="pre">store_false</span></code>.  For example, you might have a <code class="docutils literal notranslate"><span class="pre">verbose</span></code>
flag that is turned on with <code class="docutils literal notranslate"><span class="pre">-v</span></code> and off with <code class="docutils literal notranslate"><span class="pre">-q</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Here we have two different options with the same destination, which is perfectly
OK.  (It just means you have to be a bit careful when setting default
values—see below.)</p>
<p>When <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> encounters <code class="docutils literal notranslate"><span class="pre">-v</span></code> on the command line, it sets
<code class="docutils literal notranslate"><span class="pre">options.verbose</span></code> to <code class="docutils literal notranslate"><span class="pre">True</span></code>; when it encounters <code class="docutils literal notranslate"><span class="pre">-q</span></code>,
<code class="docutils literal notranslate"><span class="pre">options.verbose</span></code> is set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</section>
<section id="other-actions">
<span id="optparse-other-actions"></span><h3>Other actions<a class="headerlink" href="#other-actions" title="Link to this heading">¶</a></h3>
<p>Some other actions supported by <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> are:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&quot;store_const&quot;</span></code></dt><dd><p>store a constant value, pre-set via <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Option.const</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code></dt><dd><p>append this option’s argument to a list</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;count&quot;</span></code></dt><dd><p>increment a counter by one</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;callback&quot;</span></code></dt><dd><p>call a specified function</p>
</dd>
</dl>
<p>These are covered in section <a class="reference internal" href="#optparse-reference-guide"><span class="std std-ref">Reference Guide</span></a>,
and section <a class="reference internal" href="#optparse-option-callbacks"><span class="std std-ref">Option Callbacks</span></a>.</p>
</section>
<section id="default-values">
<span id="optparse-default-values"></span><h3>Default values<a class="headerlink" href="#default-values" title="Link to this heading">¶</a></h3>
<p>All of the above examples involve setting some variable (the “destination”) when
certain command-line options are seen.  What happens if those options are never
seen?  Since we didn’t supply any defaults, they are all set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.  This
is usually fine, but sometimes you want more control.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> lets you
supply a default value for each destination, which is assigned before the
command line is parsed.</p>
<p>First, consider the verbose/quiet example.  If we want <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> to set
<code class="docutils literal notranslate"><span class="pre">verbose</span></code> to <code class="docutils literal notranslate"><span class="pre">True</span></code> unless <code class="docutils literal notranslate"><span class="pre">-q</span></code> is seen, then we can do this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Since default values apply to the <em>destination</em> rather than to any particular
option, and these two options happen to have the same destination, this is
exactly equivalent:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<p>Consider this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<p>Again, the default value for <code class="docutils literal notranslate"><span class="pre">verbose</span></code> will be <code class="docutils literal notranslate"><span class="pre">True</span></code>: the last default
value supplied for any particular destination is the one that counts.</p>
<p>A clearer way to specify default values is the <code class="xref py py-meth docutils literal notranslate"><span class="pre">set_defaults()</span></code> method of
OptionParser, which you can call at any time before calling
<a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">set_defaults</span><span class="p">(</span><span class="n">verbose</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="o">...</span><span class="p">)</span>
<span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</pre></div>
</div>
<p>As before, the last value specified for a given option destination is the one
that counts.  For clarity, try to use one method or the other of setting default
values, not both.</p>
</section>
<section id="generating-help">
<span id="optparse-generating-help"></span><h3>Generating help<a class="headerlink" href="#generating-help" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s ability to generate help and usage text automatically is
useful for creating user-friendly command-line interfaces.  All you have to do
is supply a <a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a> value for each option, and optionally a short
usage message for your whole program.  Here’s an OptionParser populated with
user-friendly (documented) options:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">usage</span> <span class="o">=</span> <span class="s2">&quot;usage: %prog [options] arg1 arg2&quot;</span>
<span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">usage</span><span class="o">=</span><span class="n">usage</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="s2">&quot;--verbose&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;make lots of noise [default]&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="s2">&quot;--quiet&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;be vewwy quiet (I&#39;m hunting wabbits)&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--filename&quot;</span><span class="p">,</span>
                  <span class="n">metavar</span><span class="o">=</span><span class="s2">&quot;FILE&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;write output to FILE&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-m&quot;</span><span class="p">,</span> <span class="s2">&quot;--mode&quot;</span><span class="p">,</span>
                  <span class="n">default</span><span class="o">=</span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;interaction mode: novice, intermediate, &quot;</span>
                       <span class="s2">&quot;or expert [default: </span><span class="si">%d</span><span class="s2">efault]&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>If <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> encounters either <code class="docutils literal notranslate"><span class="pre">-h</span></code> or <code class="docutils literal notranslate"><span class="pre">--help</span></code> on the
command-line, or if you just call <code class="xref py py-meth docutils literal notranslate"><span class="pre">parser.print_help()</span></code>, it prints the
following to standard output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Usage: &lt;yourscript&gt; [options] arg1 arg2

Options:
  -h, --help            show this help message and exit
  -v, --verbose         make lots of noise [default]
  -q, --quiet           be vewwy quiet (I&#39;m hunting wabbits)
  -f FILE, --filename=FILE
                        write output to FILE
  -m MODE, --mode=MODE  interaction mode: novice, intermediate, or
                        expert [default: intermediate]
</pre></div>
</div>
<p>(If the help output is triggered by a help option, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> exits after
printing the help text.)</p>
<p>There’s a lot going on here to help <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> generate the best possible
help message:</p>
<ul>
<li><p>the script defines its own usage message:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">usage</span> <span class="o">=</span> <span class="s2">&quot;usage: %prog [options] arg1 arg2&quot;</span>
</pre></div>
</div>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> expands <code class="docutils literal notranslate"><span class="pre">%prog</span></code> in the usage string to the name of the
current program, i.e. <code class="docutils literal notranslate"><span class="pre">os.path.basename(sys.argv[0])</span></code>.  The expanded string
is then printed before the detailed option help.</p>
<p>If you don’t supply a usage string, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> uses a bland but sensible
default: <code class="docutils literal notranslate"><span class="pre">&quot;Usage:</span> <span class="pre">%prog</span> <span class="pre">[options]&quot;</span></code>, which is fine if your script doesn’t
take any positional arguments.</p>
</li>
<li><p>every option defines a help string, and doesn’t worry about
line-wrapping—<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> takes care of wrapping lines and making
the help output look good.</p></li>
<li><p>options that take a value indicate this fact in their automatically generated
help message, e.g. for the “mode” option:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-</span><span class="n">m</span> <span class="n">MODE</span><span class="p">,</span> <span class="o">--</span><span class="n">mode</span><span class="o">=</span><span class="n">MODE</span>
</pre></div>
</div>
<p>Here, “MODE” is called the meta-variable: it stands for the argument that the
user is expected to supply to <code class="docutils literal notranslate"><span class="pre">-m</span></code>/<code class="docutils literal notranslate"><span class="pre">--mode</span></code>.  By default,
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> converts the destination variable name to uppercase and uses
that for the meta-variable.  Sometimes, that’s not what you want—for
example, the <code class="docutils literal notranslate"><span class="pre">--filename</span></code> option explicitly sets <code class="docutils literal notranslate"><span class="pre">metavar=&quot;FILE&quot;</span></code>,
resulting in this automatically generated option description:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-</span><span class="n">f</span> <span class="n">FILE</span><span class="p">,</span> <span class="o">--</span><span class="n">filename</span><span class="o">=</span><span class="n">FILE</span>
</pre></div>
</div>
<p>This is important for more than just saving space, though: the manually
written help text uses the meta-variable <code class="docutils literal notranslate"><span class="pre">FILE</span></code> to clue the user in that
there’s a connection between the semi-formal syntax <code class="docutils literal notranslate"><span class="pre">-f</span> <span class="pre">FILE</span></code> and the informal
semantic description “write output to FILE”. This is a simple but effective
way to make your help text a lot clearer and more useful for end users.</p>
</li>
<li><p>options that have a default value can include <code class="docutils literal notranslate"><span class="pre">%default</span></code> in the help
string—<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will replace it with <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> of the option’s
default value.  If an option has no default value (or the default value is
<code class="docutils literal notranslate"><span class="pre">None</span></code>), <code class="docutils literal notranslate"><span class="pre">%default</span></code> expands to <code class="docutils literal notranslate"><span class="pre">none</span></code>.</p></li>
</ul>
<section id="grouping-options">
<h4>Grouping Options<a class="headerlink" href="#grouping-options" title="Link to this heading">¶</a></h4>
<p>When dealing with many options, it is convenient to group these options for
better help output.  An <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a> can contain several option groups,
each of which can contain several options.</p>
<p>An option group is obtained using the class <a class="reference internal" href="#optparse.OptionGroup" title="optparse.OptionGroup"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionGroup</span></code></a>:</p>
<dl class="py class">
<dt class="sig sig-object py" id="optparse.OptionGroup">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">OptionGroup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parser</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">title</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionGroup" title="Link to this definition">¶</a></dt>
<dd><p>where</p>
<ul class="simple">
<li><p>parser is the <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a> instance the group will be inserted in
to</p></li>
<li><p>title is the group title</p></li>
<li><p>description, optional, is a long description of the group</p></li>
</ul>
</dd></dl>

<p><a class="reference internal" href="#optparse.OptionGroup" title="optparse.OptionGroup"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionGroup</span></code></a> inherits from <code class="xref py py-class docutils literal notranslate"><span class="pre">OptionContainer</span></code> (like
<a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>) and so the <code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option()</span></code> method can be used to add
an option to the group.</p>
<p>Once all the options are declared, using the <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a> method
<code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option_group()</span></code> the group is added to the previously defined parser.</p>
<p>Continuing with the parser defined in the previous section, adding an
<a class="reference internal" href="#optparse.OptionGroup" title="optparse.OptionGroup"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionGroup</span></code></a> to a parser is easy:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">group</span> <span class="o">=</span> <span class="n">OptionGroup</span><span class="p">(</span><span class="n">parser</span><span class="p">,</span> <span class="s2">&quot;Dangerous Options&quot;</span><span class="p">,</span>
                    <span class="s2">&quot;Caution: use these options at your own risk.  &quot;</span>
                    <span class="s2">&quot;It is believed that some of them bite.&quot;</span><span class="p">)</span>
<span class="n">group</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-g&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Group option.&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option_group</span><span class="p">(</span><span class="n">group</span><span class="p">)</span>
</pre></div>
</div>
<p>This would result in the following help output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Usage: &lt;yourscript&gt; [options] arg1 arg2

Options:
  -h, --help            show this help message and exit
  -v, --verbose         make lots of noise [default]
  -q, --quiet           be vewwy quiet (I&#39;m hunting wabbits)
  -f FILE, --filename=FILE
                        write output to FILE
  -m MODE, --mode=MODE  interaction mode: novice, intermediate, or
                        expert [default: intermediate]

  Dangerous Options:
    Caution: use these options at your own risk.  It is believed that some
    of them bite.

    -g                  Group option.
</pre></div>
</div>
<p>A bit more complete example might involve using more than one group: still
extending the previous example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">group</span> <span class="o">=</span> <span class="n">OptionGroup</span><span class="p">(</span><span class="n">parser</span><span class="p">,</span> <span class="s2">&quot;Dangerous Options&quot;</span><span class="p">,</span>
                    <span class="s2">&quot;Caution: use these options at your own risk.  &quot;</span>
                    <span class="s2">&quot;It is believed that some of them bite.&quot;</span><span class="p">)</span>
<span class="n">group</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-g&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Group option.&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option_group</span><span class="p">(</span><span class="n">group</span><span class="p">)</span>

<span class="n">group</span> <span class="o">=</span> <span class="n">OptionGroup</span><span class="p">(</span><span class="n">parser</span><span class="p">,</span> <span class="s2">&quot;Debug Options&quot;</span><span class="p">)</span>
<span class="n">group</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-d&quot;</span><span class="p">,</span> <span class="s2">&quot;--debug&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span>
                 <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Print debug information&quot;</span><span class="p">)</span>
<span class="n">group</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-s&quot;</span><span class="p">,</span> <span class="s2">&quot;--sql&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span>
                 <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Print all SQL statements executed&quot;</span><span class="p">)</span>
<span class="n">group</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-e&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Print every action done&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option_group</span><span class="p">(</span><span class="n">group</span><span class="p">)</span>
</pre></div>
</div>
<p>that results in the following output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Usage: &lt;yourscript&gt; [options] arg1 arg2

Options:
  -h, --help            show this help message and exit
  -v, --verbose         make lots of noise [default]
  -q, --quiet           be vewwy quiet (I&#39;m hunting wabbits)
  -f FILE, --filename=FILE
                        write output to FILE
  -m MODE, --mode=MODE  interaction mode: novice, intermediate, or expert
                        [default: intermediate]

  Dangerous Options:
    Caution: use these options at your own risk.  It is believed that some
    of them bite.

    -g                  Group option.

  Debug Options:
    -d, --debug         Print debug information
    -s, --sql           Print all SQL statements executed
    -e                  Print every action done
</pre></div>
</div>
<p>Another interesting method, in particular when working programmatically with
option groups is:</p>
<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.get_option_group">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">get_option_group</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">opt_str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.get_option_group" title="Link to this definition">¶</a></dt>
<dd><p>Return the <a class="reference internal" href="#optparse.OptionGroup" title="optparse.OptionGroup"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionGroup</span></code></a> to which the short or long option
string <em>opt_str</em> (e.g. <code class="docutils literal notranslate"><span class="pre">'-o'</span></code> or <code class="docutils literal notranslate"><span class="pre">'--option'</span></code>) belongs. If
there’s no such <a class="reference internal" href="#optparse.OptionGroup" title="optparse.OptionGroup"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionGroup</span></code></a>, return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

</section>
</section>
<section id="printing-a-version-string">
<span id="optparse-printing-version-string"></span><h3>Printing a version string<a class="headerlink" href="#printing-a-version-string" title="Link to this heading">¶</a></h3>
<p>Similar to the brief usage string, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> can also print a version
string for your program.  You have to supply the string as the <code class="docutils literal notranslate"><span class="pre">version</span></code>
argument to OptionParser:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">usage</span><span class="o">=</span><span class="s2">&quot;%prog [-f] [-q]&quot;</span><span class="p">,</span> <span class="n">version</span><span class="o">=</span><span class="s2">&quot;%prog 1.0&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">%prog</span></code> is expanded just like it is in <code class="docutils literal notranslate"><span class="pre">usage</span></code>.  Apart from that,
<code class="docutils literal notranslate"><span class="pre">version</span></code> can contain anything you like.  When you supply it, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>
automatically adds a <code class="docutils literal notranslate"><span class="pre">--version</span></code> option to your parser. If it encounters
this option on the command line, it expands your <code class="docutils literal notranslate"><span class="pre">version</span></code> string (by
replacing <code class="docutils literal notranslate"><span class="pre">%prog</span></code>), prints it to stdout, and exits.</p>
<p>For example, if your script is called <code class="docutils literal notranslate"><span class="pre">/usr/bin/foo</span></code>:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>/usr/bin/foo<span class="w"> </span>--version
<span class="go">foo 1.0</span>
</pre></div>
</div>
<p>The following two methods can be used to print and get the <code class="docutils literal notranslate"><span class="pre">version</span></code> string:</p>
<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.print_version">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">print_version</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.print_version" title="Link to this definition">¶</a></dt>
<dd><p>Print the version message for the current program (<code class="docutils literal notranslate"><span class="pre">self.version</span></code>) to
<em>file</em> (default stdout).  As with <a class="reference internal" href="#optparse.OptionParser.print_usage" title="optparse.OptionParser.print_usage"><code class="xref py py-meth docutils literal notranslate"><span class="pre">print_usage()</span></code></a>, any occurrence
of <code class="docutils literal notranslate"><span class="pre">%prog</span></code> in <code class="docutils literal notranslate"><span class="pre">self.version</span></code> is replaced with the name of the current
program.  Does nothing if <code class="docutils literal notranslate"><span class="pre">self.version</span></code> is empty or undefined.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.get_version">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">get_version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.get_version" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#optparse.OptionParser.print_version" title="optparse.OptionParser.print_version"><code class="xref py py-meth docutils literal notranslate"><span class="pre">print_version()</span></code></a> but returns the version string instead of
printing it.</p>
</dd></dl>

</section>
<section id="how-optparse-handles-errors">
<span id="optparse-how-optparse-handles-errors"></span><h3>How <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> handles errors<a class="headerlink" href="#how-optparse-handles-errors" title="Link to this heading">¶</a></h3>
<p>There are two broad classes of errors that <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> has to worry about:
programmer errors and user errors.  Programmer errors are usually erroneous
calls to <a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-func docutils literal notranslate"><span class="pre">OptionParser.add_option()</span></code></a>, e.g. invalid option strings, unknown
option attributes, missing option attributes, etc.  These are dealt with in the
usual way: raise an exception (either <a class="reference internal" href="#optparse.OptionError" title="optparse.OptionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">optparse.OptionError</span></code></a> or
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>) and let the program crash.</p>
<p>Handling user errors is much more important, since they are guaranteed to happen
no matter how stable your code is.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> can automatically detect
some user errors, such as bad option arguments (passing <code class="docutils literal notranslate"><span class="pre">-n</span> <span class="pre">4x</span></code> where
<code class="docutils literal notranslate"><span class="pre">-n</span></code> takes an integer argument), missing arguments (<code class="docutils literal notranslate"><span class="pre">-n</span></code> at the end
of the command line, where <code class="docutils literal notranslate"><span class="pre">-n</span></code> takes an argument of any type).  Also,
you can call <code class="xref py py-func docutils literal notranslate"><span class="pre">OptionParser.error()</span></code> to signal an application-defined error
condition:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
<span class="o">...</span>
<span class="k">if</span> <span class="n">options</span><span class="o">.</span><span class="n">a</span> <span class="ow">and</span> <span class="n">options</span><span class="o">.</span><span class="n">b</span><span class="p">:</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;options -a and -b are mutually exclusive&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>In either case, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> handles the error the same way: it prints the
program’s usage message and an error message to standard error and exits with
error status 2.</p>
<p>Consider the first example above, where the user passes <code class="docutils literal notranslate"><span class="pre">4x</span></code> to an option
that takes an integer:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>/usr/bin/foo<span class="w"> </span>-n<span class="w"> </span>4x
<span class="go">Usage: foo [options]</span>

<span class="go">foo: error: option -n: invalid integer value: &#39;4x&#39;</span>
</pre></div>
</div>
<p>Or, where the user fails to pass a value at all:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>/usr/bin/foo<span class="w"> </span>-n
<span class="go">Usage: foo [options]</span>

<span class="go">foo: error: -n option requires an argument</span>
</pre></div>
</div>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>-generated error messages take care always to mention the
option involved in the error; be sure to do the same when calling
<code class="xref py py-func docutils literal notranslate"><span class="pre">OptionParser.error()</span></code> from your application code.</p>
<p>If <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s default error-handling behaviour does not suit your needs,
you’ll need to subclass OptionParser and override its <code class="xref py py-meth docutils literal notranslate"><span class="pre">exit()</span></code>
and/or <code class="xref py py-meth docutils literal notranslate"><span class="pre">error()</span></code> methods.</p>
</section>
<section id="putting-it-all-together">
<span id="optparse-putting-it-all-together"></span><h3>Putting it all together<a class="headerlink" href="#putting-it-all-together" title="Link to this heading">¶</a></h3>
<p>Here’s what <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>-based scripts usually look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">optparse</span> <span class="kn">import</span> <span class="n">OptionParser</span>
<span class="o">...</span>
<span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
    <span class="n">usage</span> <span class="o">=</span> <span class="s2">&quot;usage: %prog [options] arg&quot;</span>
    <span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">usage</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--file&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">,</span>
                      <span class="n">help</span><span class="o">=</span><span class="s2">&quot;read data from FILENAME&quot;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="s2">&quot;--verbose&quot;</span><span class="p">,</span>
                      <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="s2">&quot;--quiet&quot;</span><span class="p">,</span>
                      <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
    <span class="o">...</span>
    <span class="p">(</span><span class="n">options</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">args</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
        <span class="n">parser</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;incorrect number of arguments&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">options</span><span class="o">.</span><span class="n">verbose</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;reading </span><span class="si">%s</span><span class="s2">...&quot;</span> <span class="o">%</span> <span class="n">options</span><span class="o">.</span><span class="n">filename</span><span class="p">)</span>
    <span class="o">...</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="reference-guide">
<span id="optparse-reference-guide"></span><h2>Reference Guide<a class="headerlink" href="#reference-guide" title="Link to this heading">¶</a></h2>
<section id="creating-the-parser">
<span id="optparse-creating-parser"></span><h3>Creating the parser<a class="headerlink" href="#creating-the-parser" title="Link to this heading">¶</a></h3>
<p>The first step in using <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> is to create an OptionParser instance.</p>
<dl class="py class">
<dt class="sig sig-object py" id="optparse.OptionParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">OptionParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser" title="Link to this definition">¶</a></dt>
<dd><p>The OptionParser constructor has no required arguments, but a number of
optional keyword arguments.  You should always pass them as keyword
arguments, i.e. do not rely on the order in which the arguments are declared.</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">usage</span></code> (default: <code class="docutils literal notranslate"><span class="pre">&quot;%prog</span> <span class="pre">[options]&quot;</span></code>)</dt><dd><p>The usage summary to print when your program is run incorrectly or with a
help option.  When <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> prints the usage string, it expands
<code class="docutils literal notranslate"><span class="pre">%prog</span></code> to <code class="docutils literal notranslate"><span class="pre">os.path.basename(sys.argv[0])</span></code> (or to <code class="docutils literal notranslate"><span class="pre">prog</span></code> if you
passed that keyword argument).  To suppress a usage message, pass the
special value <code class="xref py py-const docutils literal notranslate"><span class="pre">optparse.SUPPRESS_USAGE</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">option_list</span></code> (default: <code class="docutils literal notranslate"><span class="pre">[]</span></code>)</dt><dd><p>A list of Option objects to populate the parser with.  The options in
<code class="docutils literal notranslate"><span class="pre">option_list</span></code> are added after any options in <code class="docutils literal notranslate"><span class="pre">standard_option_list</span></code> (a
class attribute that may be set by OptionParser subclasses), but before
any version or help options. Deprecated; use <a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option()</span></code></a> after
creating the parser instead.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">option_class</span></code> (default: optparse.Option)</dt><dd><p>Class to use when adding options to the parser in <a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option()</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">version</span></code> (default: <code class="docutils literal notranslate"><span class="pre">None</span></code>)</dt><dd><p>A version string to print when the user supplies a version option. If you
supply a true value for <code class="docutils literal notranslate"><span class="pre">version</span></code>, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> automatically adds a
version option with the single option string <code class="docutils literal notranslate"><span class="pre">--version</span></code>.  The
substring <code class="docutils literal notranslate"><span class="pre">%prog</span></code> is expanded the same as for <code class="docutils literal notranslate"><span class="pre">usage</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">conflict_handler</span></code> (default: <code class="docutils literal notranslate"><span class="pre">&quot;error&quot;</span></code>)</dt><dd><p>Specifies what to do when options with conflicting option strings are
added to the parser; see section
<a class="reference internal" href="#optparse-conflicts-between-options"><span class="std std-ref">Conflicts between options</span></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">description</span></code> (default: <code class="docutils literal notranslate"><span class="pre">None</span></code>)</dt><dd><p>A paragraph of text giving a brief overview of your program.
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> reformats this paragraph to fit the current terminal width
and prints it when the user requests help (after <code class="docutils literal notranslate"><span class="pre">usage</span></code>, but before the
list of options).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">formatter</span></code> (default: a new <code class="xref py py-class docutils literal notranslate"><span class="pre">IndentedHelpFormatter</span></code>)</dt><dd><p>An instance of optparse.HelpFormatter that will be used for printing help
text.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> provides two concrete classes for this purpose:
IndentedHelpFormatter and TitledHelpFormatter.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">add_help_option</span></code> (default: <code class="docutils literal notranslate"><span class="pre">True</span></code>)</dt><dd><p>If true, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will add a help option (with option strings <code class="docutils literal notranslate"><span class="pre">-h</span></code>
and <code class="docutils literal notranslate"><span class="pre">--help</span></code>) to the parser.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">prog</span></code></dt><dd><p>The string to use when expanding <code class="docutils literal notranslate"><span class="pre">%prog</span></code> in <code class="docutils literal notranslate"><span class="pre">usage</span></code> and <code class="docutils literal notranslate"><span class="pre">version</span></code>
instead of <code class="docutils literal notranslate"><span class="pre">os.path.basename(sys.argv[0])</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">epilog</span></code> (default: <code class="docutils literal notranslate"><span class="pre">None</span></code>)</dt><dd><p>A paragraph of help text to print after the option help.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="populating-the-parser">
<span id="optparse-populating-parser"></span><h3>Populating the parser<a class="headerlink" href="#populating-the-parser" title="Link to this heading">¶</a></h3>
<p>There are several ways to populate the parser with options.  The preferred way
is by using <a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.add_option()</span></code></a>, as shown in section
<a class="reference internal" href="#optparse-tutorial"><span class="std std-ref">Tutorial</span></a>.  <code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option()</span></code> can be called in one of two ways:</p>
<ul class="simple">
<li><p>pass it an Option instance (as returned by <code class="xref py py-func docutils literal notranslate"><span class="pre">make_option()</span></code>)</p></li>
<li><p>pass it any combination of positional and keyword arguments that are
acceptable to <code class="xref py py-func docutils literal notranslate"><span class="pre">make_option()</span></code> (i.e., to the Option constructor), and it
will create the Option instance for you</p></li>
</ul>
<p>The other alternative is to pass a list of pre-constructed Option instances to
the OptionParser constructor, as in:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">option_list</span> <span class="o">=</span> <span class="p">[</span>
    <span class="n">make_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--filename&quot;</span><span class="p">,</span>
                <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;string&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">),</span>
    <span class="n">make_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="s2">&quot;--quiet&quot;</span><span class="p">,</span>
                <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">),</span>
    <span class="p">]</span>
<span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">option_list</span><span class="o">=</span><span class="n">option_list</span><span class="p">)</span>
</pre></div>
</div>
<p>(<code class="xref py py-func docutils literal notranslate"><span class="pre">make_option()</span></code> is a factory function for creating Option instances;
currently it is an alias for the Option constructor.  A future version of
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> may split Option into several classes, and <code class="xref py py-func docutils literal notranslate"><span class="pre">make_option()</span></code>
will pick the right class to instantiate.  Do not instantiate Option directly.)</p>
</section>
<section id="defining-options">
<span id="optparse-defining-options"></span><h3>Defining options<a class="headerlink" href="#defining-options" title="Link to this heading">¶</a></h3>
<p>Each Option instance represents a set of synonymous command-line option strings,
e.g. <code class="docutils literal notranslate"><span class="pre">-f</span></code> and <code class="docutils literal notranslate"><span class="pre">--file</span></code>.  You can specify any number of short or
long option strings, but you must specify at least one overall option string.</p>
<p>The canonical way to create an <a class="reference internal" href="#optparse.Option" title="optparse.Option"><code class="xref py py-class docutils literal notranslate"><span class="pre">Option</span></code></a> instance is with the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option()</span></code> method of <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.add_option">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">add_option</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">option</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.add_option" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">add_option</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">*opt_str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attr=value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>To define an option with only a short option string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="n">attr</span><span class="o">=</span><span class="n">value</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
<p>And to define an option with only a long option string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--foo&quot;</span><span class="p">,</span> <span class="n">attr</span><span class="o">=</span><span class="n">value</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
<p>The keyword arguments define attributes of the new Option object.  The most
important option attribute is <a class="reference internal" href="#optparse.Option.action" title="optparse.Option.action"><code class="xref py py-attr docutils literal notranslate"><span class="pre">action</span></code></a>, and it largely
determines which other attributes are relevant or required.  If you pass
irrelevant option attributes, or fail to pass required ones, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>
raises an <a class="reference internal" href="#optparse.OptionError" title="optparse.OptionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionError</span></code></a> exception explaining your mistake.</p>
<p>An option’s <em>action</em> determines what <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> does when it encounters
this option on the command-line.  The standard option actions hard-coded into
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> are:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code></dt><dd><p>store this option’s argument (default)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;store_const&quot;</span></code></dt><dd><p>store a constant value, pre-set via <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Option.const</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;store_true&quot;</span></code></dt><dd><p>store <code class="docutils literal notranslate"><span class="pre">True</span></code></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;store_false&quot;</span></code></dt><dd><p>store <code class="docutils literal notranslate"><span class="pre">False</span></code></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code></dt><dd><p>append this option’s argument to a list</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;append_const&quot;</span></code></dt><dd><p>append a constant value to a list, pre-set via <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Option.const</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;count&quot;</span></code></dt><dd><p>increment a counter by one</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;callback&quot;</span></code></dt><dd><p>call a specified function</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;help&quot;</span></code></dt><dd><p>print a usage message including all options and the documentation for them</p>
</dd>
</dl>
<p>(If you don’t supply an action, the default is <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code>.  For this action,
you may also supply <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> and <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> option
attributes; see <a class="reference internal" href="#optparse-standard-option-actions"><span class="std std-ref">Standard option actions</span></a>.)</p>
</dd></dl>

<p>As you can see, most actions involve storing or updating a value somewhere.
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> always creates a special object for this, conventionally called
<code class="docutils literal notranslate"><span class="pre">options</span></code>, which is an instance of <a class="reference internal" href="#optparse.Values" title="optparse.Values"><code class="xref py py-class docutils literal notranslate"><span class="pre">optparse.Values</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="optparse.Values">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">Values</span></span><a class="headerlink" href="#optparse.Values" title="Link to this definition">¶</a></dt>
<dd><p>An object holding parsed argument names and values as attributes.
Normally created by calling when calling <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.parse_args()</span></code></a>,
and can be overridden by a custom subclass passed to the <em>values</em> argument of
<a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.parse_args()</span></code></a> (as described in <a class="reference internal" href="#optparse-parsing-arguments"><span class="std std-ref">Parsing arguments</span></a>).</p>
</dd></dl>

<p>Option
arguments (and various other values) are stored as attributes of this object,
according to the <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> (destination) option attribute.</p>
<p>For example, when you call</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</pre></div>
</div>
<p>one of the first things <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> does is create the <code class="docutils literal notranslate"><span class="pre">options</span></code> object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span> <span class="o">=</span> <span class="n">Values</span><span class="p">()</span>
</pre></div>
</div>
<p>If one of the options in this parser is defined with</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">,</span> <span class="s2">&quot;--file&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;string&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>and the command-line being parsed includes any of the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-</span><span class="n">ffoo</span>
<span class="o">-</span><span class="n">f</span> <span class="n">foo</span>
<span class="o">--</span><span class="n">file</span><span class="o">=</span><span class="n">foo</span>
<span class="o">--</span><span class="n">file</span> <span class="n">foo</span>
</pre></div>
</div>
<p>then <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>, on seeing this option, will do the equivalent of</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">filename</span> <span class="o">=</span> <span class="s2">&quot;foo&quot;</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> and <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> option attributes are almost
as important as <a class="reference internal" href="#optparse.Option.action" title="optparse.Option.action"><code class="xref py py-attr docutils literal notranslate"><span class="pre">action</span></code></a>, but <a class="reference internal" href="#optparse.Option.action" title="optparse.Option.action"><code class="xref py py-attr docutils literal notranslate"><span class="pre">action</span></code></a> is the only
one that makes sense for <em>all</em> options.</p>
</section>
<section id="option-attributes">
<span id="optparse-option-attributes"></span><h3>Option attributes<a class="headerlink" href="#option-attributes" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="optparse.Option">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">Option</span></span><a class="headerlink" href="#optparse.Option" title="Link to this definition">¶</a></dt>
<dd><p>A single command line argument,
with various attributes passed by keyword to the constructor.
Normally created with <a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.add_option()</span></code></a> rather than directly,
and can be overridden by a custom class via the <em>option_class</em> argument
to <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>.</p>
</dd></dl>

<p>The following option attributes may be passed as keyword arguments to
<a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.add_option()</span></code></a>.  If you pass an option attribute that is not
relevant to a particular option, or fail to pass a required option attribute,
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> raises <a class="reference internal" href="#optparse.OptionError" title="optparse.OptionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionError</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.action">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">action</span></span><a class="headerlink" href="#optparse.Option.action" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code>)</p>
<p>Determines <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s behaviour when this option is seen on the
command line; the available options are documented <a class="reference internal" href="#optparse-standard-option-actions"><span class="std std-ref">here</span></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.type">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">type</span></span><a class="headerlink" href="#optparse.Option.type" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code>)</p>
<p>The argument type expected by this option (e.g., <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;int&quot;</span></code>);
the available option types are documented <a class="reference internal" href="#optparse-standard-option-types"><span class="std std-ref">here</span></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.dest">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">dest</span></span><a class="headerlink" href="#optparse.Option.dest" title="Link to this definition">¶</a></dt>
<dd><p>(default: derived from option strings)</p>
<p>If the option’s action implies writing or modifying a value somewhere, this
tells <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> where to write it: <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> names an
attribute of the <code class="docutils literal notranslate"><span class="pre">options</span></code> object that <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> builds as it parses
the command line.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.default">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">default</span></span><a class="headerlink" href="#optparse.Option.default" title="Link to this definition">¶</a></dt>
<dd><p>The value to use for this option’s destination if the option is not seen on
the command line.  See also <a class="reference internal" href="#optparse.OptionParser.set_defaults" title="optparse.OptionParser.set_defaults"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.set_defaults()</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.nargs">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">nargs</span></span><a class="headerlink" href="#optparse.Option.nargs" title="Link to this definition">¶</a></dt>
<dd><p>(default: 1)</p>
<p>How many arguments of type <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> should be consumed when this
option is seen.  If &gt; 1, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will store a tuple of values to
<a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.const">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">const</span></span><a class="headerlink" href="#optparse.Option.const" title="Link to this definition">¶</a></dt>
<dd><p>For actions that store a constant value, the constant value to store.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.choices">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">choices</span></span><a class="headerlink" href="#optparse.Option.choices" title="Link to this definition">¶</a></dt>
<dd><p>For options of type <code class="docutils literal notranslate"><span class="pre">&quot;choice&quot;</span></code>, the list of strings the user may choose
from.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.callback">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">callback</span></span><a class="headerlink" href="#optparse.Option.callback" title="Link to this definition">¶</a></dt>
<dd><p>For options with action <code class="docutils literal notranslate"><span class="pre">&quot;callback&quot;</span></code>, the callable to call when this option
is seen.  See section <a class="reference internal" href="#optparse-option-callbacks"><span class="std std-ref">Option Callbacks</span></a> for detail on the
arguments passed to the callable.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.callback_args">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">callback_args</span></span><a class="headerlink" href="#optparse.Option.callback_args" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="optparse.Option.callback_kwargs">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">callback_kwargs</span></span><a class="headerlink" href="#optparse.Option.callback_kwargs" title="Link to this definition">¶</a></dt>
<dd><p>Additional positional and keyword arguments to pass to <code class="docutils literal notranslate"><span class="pre">callback</span></code> after the
four standard callback arguments.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.help">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">help</span></span><a class="headerlink" href="#optparse.Option.help" title="Link to this definition">¶</a></dt>
<dd><p>Help text to print for this option when listing all available options after
the user supplies a <a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a> option (such as <code class="docutils literal notranslate"><span class="pre">--help</span></code>).  If
no help text is supplied, the option will be listed without help text.  To
hide this option, use the special value <code class="xref py py-const docutils literal notranslate"><span class="pre">optparse.SUPPRESS_HELP</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.metavar">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">metavar</span></span><a class="headerlink" href="#optparse.Option.metavar" title="Link to this definition">¶</a></dt>
<dd><p>(default: derived from option strings)</p>
<p>Stand-in for the option argument(s) to use when printing help text.  See
section <a class="reference internal" href="#optparse-tutorial"><span class="std std-ref">Tutorial</span></a> for an example.</p>
</dd></dl>

</section>
<section id="standard-option-actions">
<span id="optparse-standard-option-actions"></span><h3>Standard option actions<a class="headerlink" href="#standard-option-actions" title="Link to this heading">¶</a></h3>
<p>The various option actions all have slightly different requirements and effects.
Most actions have several relevant option attributes which you may specify to
guide <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s behaviour; a few have required attributes, which you
must specify for any option using that action.</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code> [relevant: <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>, <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>,
<a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a>, <a class="reference internal" href="#optparse.Option.choices" title="optparse.Option.choices"><code class="xref py py-attr docutils literal notranslate"><span class="pre">choices</span></code></a>]</p>
<p>The option must be followed by an argument, which is converted to a value
according to <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> and stored in <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.  If
<a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a> &gt; 1, multiple arguments will be consumed from the
command line; all will be converted according to <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> and
stored to <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> as a tuple.  See the
<a class="reference internal" href="#optparse-standard-option-types"><span class="std std-ref">Standard option types</span></a> section.</p>
<p>If <a class="reference internal" href="#optparse.Option.choices" title="optparse.Option.choices"><code class="xref py py-attr docutils literal notranslate"><span class="pre">choices</span></code></a> is supplied (a list or tuple of strings), the type
defaults to <code class="docutils literal notranslate"><span class="pre">&quot;choice&quot;</span></code>.</p>
<p>If <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> is not supplied, it defaults to <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code>.</p>
<p>If <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> is not supplied, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> derives a destination
from the first long option string (e.g., <code class="docutils literal notranslate"><span class="pre">--foo-bar</span></code> implies
<code class="docutils literal notranslate"><span class="pre">foo_bar</span></code>). If there are no long option strings, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> derives a
destination from the first short option string (e.g., <code class="docutils literal notranslate"><span class="pre">-f</span></code> implies <code class="docutils literal notranslate"><span class="pre">f</span></code>).</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-f&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-p&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;float&quot;</span><span class="p">,</span> <span class="n">nargs</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;point&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>As it parses the command line</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">-</span><span class="n">f</span> <span class="n">foo</span><span class="o">.</span><span class="n">txt</span> <span class="o">-</span><span class="n">p</span> <span class="mi">1</span> <span class="o">-</span><span class="mf">3.5</span> <span class="mi">4</span> <span class="o">-</span><span class="n">fbar</span><span class="o">.</span><span class="n">txt</span>
</pre></div>
</div>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will set</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">f</span> <span class="o">=</span> <span class="s2">&quot;foo.txt&quot;</span>
<span class="n">options</span><span class="o">.</span><span class="n">point</span> <span class="o">=</span> <span class="p">(</span><span class="mf">1.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">3.5</span><span class="p">,</span> <span class="mf">4.0</span><span class="p">)</span>
<span class="n">options</span><span class="o">.</span><span class="n">f</span> <span class="o">=</span> <span class="s2">&quot;bar.txt&quot;</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;store_const&quot;</span></code> [required: <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">const</span></code></a>; relevant:
<a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>]</p>
<p>The value <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">const</span></code></a> is stored in <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-q&quot;</span><span class="p">,</span> <span class="s2">&quot;--quiet&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="s2">&quot;--verbose&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--noisy&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">--noisy</span></code> is seen, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will set</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">verbose</span> <span class="o">=</span> <span class="mi">2</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;store_true&quot;</span></code> [relevant: <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>]</p>
<p>A special case of <code class="docutils literal notranslate"><span class="pre">&quot;store_const&quot;</span></code> that stores <code class="docutils literal notranslate"><span class="pre">True</span></code> to
<a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;store_false&quot;</span></code> [relevant: <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>]</p>
<p>Like <code class="docutils literal notranslate"><span class="pre">&quot;store_true&quot;</span></code>, but stores <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--clobber&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;clobber&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--no-clobber&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_false&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;clobber&quot;</span><span class="p">)</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code> [relevant: <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>, <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>,
<a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a>, <a class="reference internal" href="#optparse.Option.choices" title="optparse.Option.choices"><code class="xref py py-attr docutils literal notranslate"><span class="pre">choices</span></code></a>]</p>
<p>The option must be followed by an argument, which is appended to the list in
<a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.  If no default value for <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> is
supplied, an empty list is automatically created when <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> first
encounters this option on the command-line.  If <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a> &gt; 1,
multiple arguments are consumed, and a tuple of length <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a>
is appended to <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.</p>
<p>The defaults for <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> and <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> are the same as
for the <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code> action.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-t&quot;</span><span class="p">,</span> <span class="s2">&quot;--tracks&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;append&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;int&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">-t3</span></code> is seen on the command-line, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> does the equivalent
of:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">tracks</span> <span class="o">=</span> <span class="p">[]</span>
<span class="n">options</span><span class="o">.</span><span class="n">tracks</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="s2">&quot;3&quot;</span><span class="p">))</span>
</pre></div>
</div>
<p>If, a little later on, <code class="docutils literal notranslate"><span class="pre">--tracks=4</span></code> is seen, it does:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">tracks</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="s2">&quot;4&quot;</span><span class="p">))</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">append</span></code> action calls the <code class="docutils literal notranslate"><span class="pre">append</span></code> method on the current value of the
option.  This means that any default value specified must have an <code class="docutils literal notranslate"><span class="pre">append</span></code>
method.  It also means that if the default value is non-empty, the default
elements will be present in the parsed value for the option, with any values
from the command line appended after those default values:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--files&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;append&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;~/.mypkg/defaults&#39;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">opts</span><span class="p">,</span> <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">([</span><span class="s1">&#39;--files&#39;</span><span class="p">,</span> <span class="s1">&#39;overrides.mypkg&#39;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">opts</span><span class="o">.</span><span class="n">files</span>
<span class="go">[&#39;~/.mypkg/defaults&#39;, &#39;overrides.mypkg&#39;]</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;append_const&quot;</span></code> [required: <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">const</span></code></a>; relevant:
<a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>]</p>
<p>Like <code class="docutils literal notranslate"><span class="pre">&quot;store_const&quot;</span></code>, but the value <a class="reference internal" href="#optparse.Option.const" title="optparse.Option.const"><code class="xref py py-attr docutils literal notranslate"><span class="pre">const</span></code></a> is appended to
<a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>; as with <code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code>, <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> defaults to
<code class="docutils literal notranslate"><span class="pre">None</span></code>, and an empty list is automatically created the first time the option
is encountered.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;count&quot;</span></code> [relevant: <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>]</p>
<p>Increment the integer stored at <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>.  If no default value is
supplied, <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a> is set to zero before being incremented the
first time.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;count&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbosity&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The first time <code class="docutils literal notranslate"><span class="pre">-v</span></code> is seen on the command line, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> does the
equivalent of:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">=</span> <span class="mi">0</span>
<span class="n">options</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>
</div>
<p>Every subsequent occurrence of <code class="docutils literal notranslate"><span class="pre">-v</span></code> results in</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">options</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;callback&quot;</span></code> [required: <a class="reference internal" href="#optparse.Option.callback" title="optparse.Option.callback"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback</span></code></a>; relevant:
<a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>, <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a>, <a class="reference internal" href="#optparse.Option.callback_args" title="optparse.Option.callback_args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_args</span></code></a>,
<a class="reference internal" href="#optparse.Option.callback_kwargs" title="optparse.Option.callback_kwargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_kwargs</span></code></a>]</p>
<p>Call the function specified by <a class="reference internal" href="#optparse.Option.callback" title="optparse.Option.callback"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback</span></code></a>, which is called as</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
</pre></div>
</div>
<p>See section <a class="reference internal" href="#optparse-option-callbacks"><span class="std std-ref">Option Callbacks</span></a> for more detail.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;help&quot;</span></code></p>
<p>Prints a complete help message for all the options in the current option
parser.  The help message is constructed from the <code class="docutils literal notranslate"><span class="pre">usage</span></code> string passed to
OptionParser’s constructor and the <a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a> string passed to every
option.</p>
<p>If no <a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a> string is supplied for an option, it will still be
listed in the help message.  To omit an option entirely, use the special value
<code class="xref py py-const docutils literal notranslate"><span class="pre">optparse.SUPPRESS_HELP</span></code>.</p>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> automatically adds a <a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a> option to all
OptionParsers, so you do not normally need to create one.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">optparse</span> <span class="kn">import</span> <span class="n">OptionParser</span><span class="p">,</span> <span class="n">SUPPRESS_HELP</span>

<span class="c1"># usually, a help option is added automatically, but that can</span>
<span class="c1"># be suppressed using the add_help_option argument</span>
<span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">add_help_option</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-h&quot;</span><span class="p">,</span> <span class="s2">&quot;--help&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;help&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-v&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;verbose&quot;</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Be moderately verbose&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--file&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;filename&quot;</span><span class="p">,</span>
                  <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Input file to read data from&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--secret&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="n">SUPPRESS_HELP</span><span class="p">)</span>
</pre></div>
</div>
<p>If <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> sees either <code class="docutils literal notranslate"><span class="pre">-h</span></code> or <code class="docutils literal notranslate"><span class="pre">--help</span></code> on the command line,
it will print something like the following help message to stdout (assuming
<code class="docutils literal notranslate"><span class="pre">sys.argv[0]</span></code> is <code class="docutils literal notranslate"><span class="pre">&quot;foo.py&quot;</span></code>):</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Usage: foo.py [options]

Options:
  -h, --help        Show this help message and exit
  -v                Be moderately verbose
  --file=FILENAME   Input file to read data from
</pre></div>
</div>
<p>After printing the help message, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> terminates your process with
<code class="docutils literal notranslate"><span class="pre">sys.exit(0)</span></code>.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;version&quot;</span></code></p>
<p>Prints the version number supplied to the OptionParser to stdout and exits.
The version number is actually formatted and printed by the
<code class="docutils literal notranslate"><span class="pre">print_version()</span></code> method of OptionParser.  Generally only relevant if the
<code class="docutils literal notranslate"><span class="pre">version</span></code> argument is supplied to the OptionParser constructor.  As with
<a class="reference internal" href="#optparse.Option.help" title="optparse.Option.help"><code class="xref py py-attr docutils literal notranslate"><span class="pre">help</span></code></a> options, you will rarely create <code class="docutils literal notranslate"><span class="pre">version</span></code> options,
since <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> automatically adds them when needed.</p>
</li>
</ul>
</section>
<section id="standard-option-types">
<span id="optparse-standard-option-types"></span><h3>Standard option types<a class="headerlink" href="#standard-option-types" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> has five built-in option types: <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;int&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;choice&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;float&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;complex&quot;</span></code>.  If you need to add new
option types, see section <a class="reference internal" href="#optparse-extending-optparse"><span class="std std-ref">Extending optparse</span></a>.</p>
<p>Arguments to string options are not checked or converted in any way: the text on
the command line is stored in the destination (or passed to the callback) as-is.</p>
<p>Integer arguments (type <code class="docutils literal notranslate"><span class="pre">&quot;int&quot;</span></code>) are parsed as follows:</p>
<ul class="simple">
<li><p>if the number starts with <code class="docutils literal notranslate"><span class="pre">0x</span></code>, it is parsed as a hexadecimal number</p></li>
<li><p>if the number starts with <code class="docutils literal notranslate"><span class="pre">0</span></code>, it is parsed as an octal number</p></li>
<li><p>if the number starts with <code class="docutils literal notranslate"><span class="pre">0b</span></code>, it is parsed as a binary number</p></li>
<li><p>otherwise, the number is parsed as a decimal number</p></li>
</ul>
<p>The conversion is done by calling <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> with the appropriate base (2, 8,
10, or 16).  If this fails, so will <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>, although with a more useful
error message.</p>
<p><code class="docutils literal notranslate"><span class="pre">&quot;float&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;complex&quot;</span></code> option arguments are converted directly with
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a> and <a class="reference internal" href="functions.html#complex" title="complex"><code class="xref py py-func docutils literal notranslate"><span class="pre">complex()</span></code></a>, with similar error-handling.</p>
<p><code class="docutils literal notranslate"><span class="pre">&quot;choice&quot;</span></code> options are a subtype of <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code> options.  The
<a class="reference internal" href="#optparse.Option.choices" title="optparse.Option.choices"><code class="xref py py-attr docutils literal notranslate"><span class="pre">choices</span></code></a> option attribute (a sequence of strings) defines the
set of allowed option arguments.  <code class="xref py py-func docutils literal notranslate"><span class="pre">optparse.check_choice()</span></code> compares
user-supplied option arguments against this master list and raises
<a class="reference internal" href="#optparse.OptionValueError" title="optparse.OptionValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionValueError</span></code></a> if an invalid string is given.</p>
</section>
<section id="parsing-arguments">
<span id="optparse-parsing-arguments"></span><h3>Parsing arguments<a class="headerlink" href="#parsing-arguments" title="Link to this heading">¶</a></h3>
<p>The whole point of creating and populating an OptionParser is to call its
<a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a> method.</p>
<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.parse_args">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">parse_args</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">values</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.parse_args" title="Link to this definition">¶</a></dt>
<dd><p>Parse the command-line options found in <em>args</em>.</p>
<p>The input parameters are</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">args</span></code></dt><dd><p>the list of arguments to process (default: <code class="docutils literal notranslate"><span class="pre">sys.argv[1:]</span></code>)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">values</span></code></dt><dd><p>an <a class="reference internal" href="#optparse.Values" title="optparse.Values"><code class="xref py py-class docutils literal notranslate"><span class="pre">Values</span></code></a> object to store option arguments in (default: a
new instance of <a class="reference internal" href="#optparse.Values" title="optparse.Values"><code class="xref py py-class docutils literal notranslate"><span class="pre">Values</span></code></a>) – if you give an existing object, the
option defaults will not be initialized on it</p>
</dd>
</dl>
<p>and the return value is a pair <code class="docutils literal notranslate"><span class="pre">(options,</span> <span class="pre">args)</span></code> where</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">options</span></code></dt><dd><p>the same object that was passed in as <em>values</em>, or the <code class="docutils literal notranslate"><span class="pre">optparse.Values</span></code>
instance created by <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">args</span></code></dt><dd><p>the leftover positional arguments after all options have been processed</p>
</dd>
</dl>
</dd></dl>

<p>The most common usage is to supply neither keyword argument.  If you supply
<code class="docutils literal notranslate"><span class="pre">values</span></code>, it will be modified with repeated <a class="reference internal" href="functions.html#setattr" title="setattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">setattr()</span></code></a> calls (roughly one
for every option argument stored to an option destination) and returned by
<a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a>.</p>
<p>If <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a> encounters any errors in the argument list, it calls the
OptionParser’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">error()</span></code> method with an appropriate end-user error message.
This ultimately terminates your process with an exit status of 2 (the
traditional Unix exit status for command-line errors).</p>
</section>
<section id="querying-and-manipulating-your-option-parser">
<span id="optparse-querying-manipulating-option-parser"></span><h3>Querying and manipulating your option parser<a class="headerlink" href="#querying-and-manipulating-your-option-parser" title="Link to this heading">¶</a></h3>
<p>The default behavior of the option parser can be customized slightly, and you
can also poke around your option parser and see what’s there.  OptionParser
provides several methods to help you out:</p>
<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.disable_interspersed_args">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">disable_interspersed_args</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.disable_interspersed_args" title="Link to this definition">¶</a></dt>
<dd><p>Set parsing to stop on the first non-option.  For example, if <code class="docutils literal notranslate"><span class="pre">-a</span></code> and
<code class="docutils literal notranslate"><span class="pre">-b</span></code> are both simple options that take no arguments, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>
normally accepts this syntax:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">prog</span> <span class="o">-</span><span class="n">a</span> <span class="n">arg1</span> <span class="o">-</span><span class="n">b</span> <span class="n">arg2</span>
</pre></div>
</div>
<p>and treats it as equivalent to</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">prog</span> <span class="o">-</span><span class="n">a</span> <span class="o">-</span><span class="n">b</span> <span class="n">arg1</span> <span class="n">arg2</span>
</pre></div>
</div>
<p>To disable this feature, call <a class="reference internal" href="#optparse.OptionParser.disable_interspersed_args" title="optparse.OptionParser.disable_interspersed_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">disable_interspersed_args()</span></code></a>.  This
restores traditional Unix syntax, where option parsing stops with the first
non-option argument.</p>
<p>Use this if you have a command processor which runs another command which has
options of its own and you want to make sure these options don’t get
confused.  For example, each command might have a different set of options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.enable_interspersed_args">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">enable_interspersed_args</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.enable_interspersed_args" title="Link to this definition">¶</a></dt>
<dd><p>Set parsing to not stop on the first non-option, allowing interspersing
switches with command arguments.  This is the default behavior.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.get_option">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">get_option</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">opt_str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.get_option" title="Link to this definition">¶</a></dt>
<dd><p>Returns the Option instance with the option string <em>opt_str</em>, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if
no options have that option string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.has_option">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">has_option</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">opt_str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.has_option" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the OptionParser has an option with option string <em>opt_str</em>
(e.g., <code class="docutils literal notranslate"><span class="pre">-q</span></code> or <code class="docutils literal notranslate"><span class="pre">--verbose</span></code>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.remove_option">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">remove_option</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">opt_str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.remove_option" title="Link to this definition">¶</a></dt>
<dd><p>If the <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a> has an option corresponding to <em>opt_str</em>, that
option is removed.  If that option provided any other option strings, all of
those option strings become invalid. If <em>opt_str</em> does not occur in any
option belonging to this <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>, raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
</dd></dl>

</section>
<section id="conflicts-between-options">
<span id="optparse-conflicts-between-options"></span><h3>Conflicts between options<a class="headerlink" href="#conflicts-between-options" title="Link to this heading">¶</a></h3>
<p>If you’re not careful, it’s easy to define options with conflicting option
strings:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-n&quot;</span><span class="p">,</span> <span class="s2">&quot;--dry-run&quot;</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-n&quot;</span><span class="p">,</span> <span class="s2">&quot;--noisy&quot;</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
<p>(This is particularly true if you’ve defined your own OptionParser subclass with
some standard options.)</p>
<p>Every time you add an option, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> checks for conflicts with existing
options.  If it finds any, it invokes the current conflict-handling mechanism.
You can set the conflict-handling mechanism either in the constructor:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">conflict_handler</span><span class="o">=</span><span class="n">handler</span><span class="p">)</span>
</pre></div>
</div>
<p>or with a separate call:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">set_conflict_handler</span><span class="p">(</span><span class="n">handler</span><span class="p">)</span>
</pre></div>
</div>
<p>The available conflict handlers are:</p>
<blockquote>
<div><dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&quot;error&quot;</span></code> (default)</dt><dd><p>assume option conflicts are a programming error and raise
<a class="reference internal" href="#optparse.OptionConflictError" title="optparse.OptionConflictError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionConflictError</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&quot;resolve&quot;</span></code></dt><dd><p>resolve option conflicts intelligently (see below)</p>
</dd>
</dl>
</div></blockquote>
<p>As an example, let’s define an <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a> that resolves conflicts
intelligently and add conflicting options to it:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">conflict_handler</span><span class="o">=</span><span class="s2">&quot;resolve&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-n&quot;</span><span class="p">,</span> <span class="s2">&quot;--dry-run&quot;</span><span class="p">,</span> <span class="o">...</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;do no harm&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-n&quot;</span><span class="p">,</span> <span class="s2">&quot;--noisy&quot;</span><span class="p">,</span> <span class="o">...</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;be noisy&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>At this point, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> detects that a previously added option is already
using the <code class="docutils literal notranslate"><span class="pre">-n</span></code> option string.  Since <code class="docutils literal notranslate"><span class="pre">conflict_handler</span></code> is <code class="docutils literal notranslate"><span class="pre">&quot;resolve&quot;</span></code>,
it resolves the situation by removing <code class="docutils literal notranslate"><span class="pre">-n</span></code> from the earlier option’s list of
option strings.  Now <code class="docutils literal notranslate"><span class="pre">--dry-run</span></code> is the only way for the user to activate
that option.  If the user asks for help, the help message will reflect that:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Options</span><span class="p">:</span>
  <span class="o">--</span><span class="n">dry</span><span class="o">-</span><span class="n">run</span>     <span class="n">do</span> <span class="n">no</span> <span class="n">harm</span>
  <span class="o">...</span>
  <span class="o">-</span><span class="n">n</span><span class="p">,</span> <span class="o">--</span><span class="n">noisy</span>   <span class="n">be</span> <span class="n">noisy</span>
</pre></div>
</div>
<p>It’s possible to whittle away the option strings for a previously added option
until there are none left, and the user has no way of invoking that option from
the command-line.  In that case, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> removes that option completely,
so it doesn’t show up in help text or anywhere else. Carrying on with our
existing OptionParser:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--dry-run&quot;</span><span class="p">,</span> <span class="o">...</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;new dry-run option&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>At this point, the original <code class="docutils literal notranslate"><span class="pre">-n</span></code>/<code class="docutils literal notranslate"><span class="pre">--dry-run</span></code> option is no longer
accessible, so <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> removes it, leaving this help text:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Options</span><span class="p">:</span>
  <span class="o">...</span>
  <span class="o">-</span><span class="n">n</span><span class="p">,</span> <span class="o">--</span><span class="n">noisy</span>   <span class="n">be</span> <span class="n">noisy</span>
  <span class="o">--</span><span class="n">dry</span><span class="o">-</span><span class="n">run</span>     <span class="n">new</span> <span class="n">dry</span><span class="o">-</span><span class="n">run</span> <span class="n">option</span>
</pre></div>
</div>
</section>
<section id="cleanup">
<span id="optparse-cleanup"></span><h3>Cleanup<a class="headerlink" href="#cleanup" title="Link to this heading">¶</a></h3>
<p>OptionParser instances have several cyclic references.  This should not be a
problem for Python’s garbage collector, but you may wish to break the cyclic
references explicitly by calling <code class="xref py py-meth docutils literal notranslate"><span class="pre">destroy()</span></code> on your
OptionParser once you are done with it.  This is particularly useful in
long-running applications where large object graphs are reachable from your
OptionParser.</p>
</section>
<section id="other-methods">
<span id="optparse-other-methods"></span><h3>Other methods<a class="headerlink" href="#other-methods" title="Link to this heading">¶</a></h3>
<p>OptionParser supports several other public methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.set_usage">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">set_usage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">usage</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.set_usage" title="Link to this definition">¶</a></dt>
<dd><p>Set the usage string according to the rules described above for the <code class="docutils literal notranslate"><span class="pre">usage</span></code>
constructor keyword argument.  Passing <code class="docutils literal notranslate"><span class="pre">None</span></code> sets the default usage
string; use <code class="xref py py-const docutils literal notranslate"><span class="pre">optparse.SUPPRESS_USAGE</span></code> to suppress a usage message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.print_usage">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">print_usage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.print_usage" title="Link to this definition">¶</a></dt>
<dd><p>Print the usage message for the current program (<code class="docutils literal notranslate"><span class="pre">self.usage</span></code>) to <em>file</em>
(default stdout).  Any occurrence of the string <code class="docutils literal notranslate"><span class="pre">%prog</span></code> in <code class="docutils literal notranslate"><span class="pre">self.usage</span></code>
is replaced with the name of the current program.  Does nothing if
<code class="docutils literal notranslate"><span class="pre">self.usage</span></code> is empty or not defined.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.get_usage">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">get_usage</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.get_usage" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#optparse.OptionParser.print_usage" title="optparse.OptionParser.print_usage"><code class="xref py py-meth docutils literal notranslate"><span class="pre">print_usage()</span></code></a> but returns the usage string instead of
printing it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="optparse.OptionParser.set_defaults">
<span class="sig-prename descclassname"><span class="pre">OptionParser.</span></span><span class="sig-name descname"><span class="pre">set_defaults</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dest=value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#optparse.OptionParser.set_defaults" title="Link to this definition">¶</a></dt>
<dd><p>Set default values for several option destinations at once.  Using
<a class="reference internal" href="#optparse.OptionParser.set_defaults" title="optparse.OptionParser.set_defaults"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_defaults()</span></code></a> is the preferred way to set default values for options,
since multiple options can share the same destination.  For example, if
several “mode” options all set the same destination, any one of them can set
the default, and the last one wins:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--advanced&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span>
                  <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;mode&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="s2">&quot;advanced&quot;</span><span class="p">,</span>
                  <span class="n">default</span><span class="o">=</span><span class="s2">&quot;novice&quot;</span><span class="p">)</span>    <span class="c1"># overridden below</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--novice&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span>
                  <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;mode&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="s2">&quot;novice&quot;</span><span class="p">,</span>
                  <span class="n">default</span><span class="o">=</span><span class="s2">&quot;advanced&quot;</span><span class="p">)</span>  <span class="c1"># overrides above setting</span>
</pre></div>
</div>
<p>To avoid this confusion, use <a class="reference internal" href="#optparse.OptionParser.set_defaults" title="optparse.OptionParser.set_defaults"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_defaults()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">set_defaults</span><span class="p">(</span><span class="n">mode</span><span class="o">=</span><span class="s2">&quot;advanced&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--advanced&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span>
                  <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;mode&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="s2">&quot;advanced&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--novice&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_const&quot;</span><span class="p">,</span>
                  <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;mode&quot;</span><span class="p">,</span> <span class="n">const</span><span class="o">=</span><span class="s2">&quot;novice&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>
<section id="option-callbacks">
<span id="optparse-option-callbacks"></span><h2>Option Callbacks<a class="headerlink" href="#option-callbacks" title="Link to this heading">¶</a></h2>
<p>When <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s built-in actions and types aren’t quite enough for your
needs, you have two choices: extend <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> or define a callback option.
Extending <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> is more general, but overkill for a lot of simple
cases.  Quite often a simple callback is all you need.</p>
<p>There are two steps to defining a callback option:</p>
<ul class="simple">
<li><p>define the option itself using the <code class="docutils literal notranslate"><span class="pre">&quot;callback&quot;</span></code> action</p></li>
<li><p>write the callback; this is a function (or method) that takes at least four
arguments, as described below</p></li>
</ul>
<section id="defining-a-callback-option">
<span id="optparse-defining-callback-option"></span><h3>Defining a callback option<a class="headerlink" href="#defining-a-callback-option" title="Link to this heading">¶</a></h3>
<p>As always, the easiest way to define a callback option is by using the
<a class="reference internal" href="#optparse.OptionParser.add_option" title="optparse.OptionParser.add_option"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.add_option()</span></code></a> method.  Apart from <a class="reference internal" href="#optparse.Option.action" title="optparse.Option.action"><code class="xref py py-attr docutils literal notranslate"><span class="pre">action</span></code></a>, the
only option attribute you must specify is <code class="docutils literal notranslate"><span class="pre">callback</span></code>, the function to call:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-c&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">my_callback</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">callback</span></code> is a function (or other callable object), so you must have already
defined <code class="docutils literal notranslate"><span class="pre">my_callback()</span></code> when you create this callback option. In this simple
case, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> doesn’t even know if <code class="docutils literal notranslate"><span class="pre">-c</span></code> takes any arguments,
which usually means that the option takes no arguments—the mere presence of
<code class="docutils literal notranslate"><span class="pre">-c</span></code> on the command-line is all it needs to know.  In some
circumstances, though, you might want your callback to consume an arbitrary
number of command-line arguments.  This is where writing callbacks gets tricky;
it’s covered later in this section.</p>
<p><a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> always passes four particular arguments to your callback, and it
will only pass additional arguments if you specify them via
<a class="reference internal" href="#optparse.Option.callback_args" title="optparse.Option.callback_args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_args</span></code></a> and <a class="reference internal" href="#optparse.Option.callback_kwargs" title="optparse.Option.callback_kwargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_kwargs</span></code></a>.  Thus, the
minimal callback function signature is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">my_callback</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
</pre></div>
</div>
<p>The four arguments to a callback are described below.</p>
<p>There are several other option attributes that you can supply when you define a
callback option:</p>
<dl class="simple">
<dt><a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a></dt><dd><p>has its usual meaning: as with the <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code> actions, it
instructs <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> to consume one argument and convert it to
<a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.  Rather than storing the converted value(s) anywhere,
though, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> passes it to your callback function.</p>
</dd>
<dt><a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a></dt><dd><p>also has its usual meaning: if it is supplied and &gt; 1, <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will
consume <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a> arguments, each of which must be convertible to
<a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.  It then passes a tuple of converted values to your
callback.</p>
</dd>
<dt><a class="reference internal" href="#optparse.Option.callback_args" title="optparse.Option.callback_args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_args</span></code></a></dt><dd><p>a tuple of extra positional arguments to pass to the callback</p>
</dd>
<dt><a class="reference internal" href="#optparse.Option.callback_kwargs" title="optparse.Option.callback_kwargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_kwargs</span></code></a></dt><dd><p>a dictionary of extra keyword arguments to pass to the callback</p>
</dd>
</dl>
</section>
<section id="how-callbacks-are-called">
<span id="optparse-how-callbacks-called"></span><h3>How callbacks are called<a class="headerlink" href="#how-callbacks-are-called" title="Link to this heading">¶</a></h3>
<p>All callbacks are called as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
</pre></div>
</div>
<p>where</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">option</span></code></dt><dd><p>is the Option instance that’s calling the callback</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">opt_str</span></code></dt><dd><p>is the option string seen on the command-line that’s triggering the callback.
(If an abbreviated long option was used, <code class="docutils literal notranslate"><span class="pre">opt_str</span></code> will be the full,
canonical option string—e.g. if the user puts <code class="docutils literal notranslate"><span class="pre">--foo</span></code> on the
command-line as an abbreviation for <code class="docutils literal notranslate"><span class="pre">--foobar</span></code>, then <code class="docutils literal notranslate"><span class="pre">opt_str</span></code> will be
<code class="docutils literal notranslate"><span class="pre">&quot;--foobar&quot;</span></code>.)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">value</span></code></dt><dd><p>is the argument to this option seen on the command-line.  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> will
only expect an argument if <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> is set; the type of <code class="docutils literal notranslate"><span class="pre">value</span></code> will be
the type implied by the option’s type.  If <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> for this option is
<code class="docutils literal notranslate"><span class="pre">None</span></code> (no argument expected), then <code class="docutils literal notranslate"><span class="pre">value</span></code> will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.  If <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a>
&gt; 1, <code class="docutils literal notranslate"><span class="pre">value</span></code> will be a tuple of values of the appropriate type.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">parser</span></code></dt><dd><p>is the OptionParser instance driving the whole thing, mainly useful because
you can access some other interesting data through its instance attributes:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">parser.largs</span></code></dt><dd><p>the current list of leftover arguments, ie. arguments that have been
consumed but are neither options nor option arguments. Feel free to modify
<code class="docutils literal notranslate"><span class="pre">parser.largs</span></code>, e.g. by adding more arguments to it.  (This list will
become <code class="docutils literal notranslate"><span class="pre">args</span></code>, the second return value of <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_args()</span></code></a>.)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">parser.rargs</span></code></dt><dd><p>the current list of remaining arguments, ie. with <code class="docutils literal notranslate"><span class="pre">opt_str</span></code> and
<code class="docutils literal notranslate"><span class="pre">value</span></code> (if applicable) removed, and only the arguments following them
still there.  Feel free to modify <code class="docutils literal notranslate"><span class="pre">parser.rargs</span></code>, e.g. by consuming more
arguments.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">parser.values</span></code></dt><dd><p>the object where option values are by default stored (an instance of
optparse.OptionValues).  This lets callbacks use the same mechanism as the
rest of <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> for storing option values; you don’t need to mess
around with globals or closures.  You can also access or modify the
value(s) of any options already encountered on the command-line.</p>
</dd>
</dl>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">args</span></code></dt><dd><p>is a tuple of arbitrary positional arguments supplied via the
<a class="reference internal" href="#optparse.Option.callback_args" title="optparse.Option.callback_args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_args</span></code></a> option attribute.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">kwargs</span></code></dt><dd><p>is a dictionary of arbitrary keyword arguments supplied via
<a class="reference internal" href="#optparse.Option.callback_kwargs" title="optparse.Option.callback_kwargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">callback_kwargs</span></code></a>.</p>
</dd>
</dl>
</section>
<section id="raising-errors-in-a-callback">
<span id="optparse-raising-errors-in-callback"></span><h3>Raising errors in a callback<a class="headerlink" href="#raising-errors-in-a-callback" title="Link to this heading">¶</a></h3>
<p>The callback function should raise <a class="reference internal" href="#optparse.OptionValueError" title="optparse.OptionValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionValueError</span></code></a> if there are any
problems with the option or its argument(s).  <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> catches this and
terminates the program, printing the error message you supply to stderr.  Your
message should be clear, concise, accurate, and mention the option at fault.
Otherwise, the user will have a hard time figuring out what they did wrong.</p>
</section>
<section id="callback-example-1-trivial-callback">
<span id="optparse-callback-example-1"></span><h3>Callback example 1: trivial callback<a class="headerlink" href="#callback-example-1-trivial-callback" title="Link to this heading">¶</a></h3>
<p>Here’s an example of a callback option that takes no arguments, and simply
records that the option was seen:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">record_foo_seen</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">saw_foo</span> <span class="o">=</span> <span class="kc">True</span>

<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--foo&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">record_foo_seen</span><span class="p">)</span>
</pre></div>
</div>
<p>Of course, you could do that with the <code class="docutils literal notranslate"><span class="pre">&quot;store_true&quot;</span></code> action.</p>
</section>
<section id="callback-example-2-check-option-order">
<span id="optparse-callback-example-2"></span><h3>Callback example 2: check option order<a class="headerlink" href="#callback-example-2-check-option-order" title="Link to this heading">¶</a></h3>
<p>Here’s a slightly more interesting example: record the fact that <code class="docutils literal notranslate"><span class="pre">-a</span></code> is
seen, but blow up if it comes after <code class="docutils literal notranslate"><span class="pre">-b</span></code> in the command-line.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">check_order</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">b</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">OptionValueError</span><span class="p">(</span><span class="s2">&quot;can&#39;t use -a after -b&quot;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">a</span> <span class="o">=</span> <span class="mi">1</span>
<span class="o">...</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-a&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">check_order</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-b&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;b&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="callback-example-3-check-option-order-generalized">
<span id="optparse-callback-example-3"></span><h3>Callback example 3: check option order (generalized)<a class="headerlink" href="#callback-example-3-check-option-order-generalized" title="Link to this heading">¶</a></h3>
<p>If you want to re-use this callback for several similar options (set a flag, but
blow up if <code class="docutils literal notranslate"><span class="pre">-b</span></code> has already been seen), it needs a bit of work: the error
message and the flag that it sets must be generalized.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">check_order</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">b</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">OptionValueError</span><span class="p">(</span><span class="s2">&quot;can&#39;t use </span><span class="si">%s</span><span class="s2"> after -b&quot;</span> <span class="o">%</span> <span class="n">opt_str</span><span class="p">)</span>
    <span class="nb">setattr</span><span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="p">,</span> <span class="n">option</span><span class="o">.</span><span class="n">dest</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-a&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">check_order</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;a&#39;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-b&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;b&quot;</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-c&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">check_order</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;c&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="callback-example-4-check-arbitrary-condition">
<span id="optparse-callback-example-4"></span><h3>Callback example 4: check arbitrary condition<a class="headerlink" href="#callback-example-4-check-arbitrary-condition" title="Link to this heading">¶</a></h3>
<p>Of course, you could put any condition in there—you’re not limited to checking
the values of already-defined options.  For example, if you have options that
should not be called when the moon is full, all you have to do is this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">check_moon</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">is_moon_full</span><span class="p">():</span>
        <span class="k">raise</span> <span class="n">OptionValueError</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> option invalid when moon is full&quot;</span>
                               <span class="o">%</span> <span class="n">opt_str</span><span class="p">)</span>
    <span class="nb">setattr</span><span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="p">,</span> <span class="n">option</span><span class="o">.</span><span class="n">dest</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--foo&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">check_moon</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;foo&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>(The definition of <code class="docutils literal notranslate"><span class="pre">is_moon_full()</span></code> is left as an exercise for the reader.)</p>
</section>
<section id="callback-example-5-fixed-arguments">
<span id="optparse-callback-example-5"></span><h3>Callback example 5: fixed arguments<a class="headerlink" href="#callback-example-5-fixed-arguments" title="Link to this heading">¶</a></h3>
<p>Things get slightly more interesting when you define callback options that take
a fixed number of arguments.  Specifying that a callback option takes arguments
is similar to defining a <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code> option: if you define
<a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>, then the option takes one argument that must be
convertible to that type; if you further define <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a>, then the
option takes <a class="reference internal" href="#optparse.Option.nargs" title="optparse.Option.nargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nargs</span></code></a> arguments.</p>
<p>Here’s an example that just emulates the standard <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code> action:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">store_value</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
    <span class="nb">setattr</span><span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="p">,</span> <span class="n">option</span><span class="o">.</span><span class="n">dest</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;--foo&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">store_value</span><span class="p">,</span>
                  <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;int&quot;</span><span class="p">,</span> <span class="n">nargs</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;foo&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> takes care of consuming 3 arguments and converting
them to integers for you; all you have to do is store them.  (Or whatever;
obviously you don’t need a callback for this example.)</p>
</section>
<section id="callback-example-6-variable-arguments">
<span id="optparse-callback-example-6"></span><h3>Callback example 6: variable arguments<a class="headerlink" href="#callback-example-6-variable-arguments" title="Link to this heading">¶</a></h3>
<p>Things get hairy when you want an option to take a variable number of arguments.
For this case, you must write a callback, as <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> doesn’t provide any
built-in capabilities for it.  And you have to deal with certain intricacies of
conventional Unix command-line parsing that <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> normally handles for
you.  In particular, callbacks should implement the conventional rules for bare
<code class="docutils literal notranslate"><span class="pre">--</span></code> and <code class="docutils literal notranslate"><span class="pre">-</span></code> arguments:</p>
<ul class="simple">
<li><p>either <code class="docutils literal notranslate"><span class="pre">--</span></code> or <code class="docutils literal notranslate"><span class="pre">-</span></code> can be option arguments</p></li>
<li><p>bare <code class="docutils literal notranslate"><span class="pre">--</span></code> (if not the argument to some option): halt command-line
processing and discard the <code class="docutils literal notranslate"><span class="pre">--</span></code></p></li>
<li><p>bare <code class="docutils literal notranslate"><span class="pre">-</span></code> (if not the argument to some option): halt command-line
processing but keep the <code class="docutils literal notranslate"><span class="pre">-</span></code> (append it to <code class="docutils literal notranslate"><span class="pre">parser.largs</span></code>)</p></li>
</ul>
<p>If you want an option that takes a variable number of arguments, there are
several subtle, tricky issues to worry about.  The exact implementation you
choose will be based on which trade-offs you’re willing to make for your
application (which is why <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> doesn’t support this sort of thing
directly).</p>
<p>Nevertheless, here’s a stab at a callback for an option with variable
arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">vararg_callback</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt_str</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
    <span class="k">assert</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span>
    <span class="n">value</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="k">def</span> <span class="nf">floatable</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="nb">float</span><span class="p">(</span><span class="nb">str</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">True</span>
        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">for</span> <span class="n">arg</span> <span class="ow">in</span> <span class="n">parser</span><span class="o">.</span><span class="n">rargs</span><span class="p">:</span>
        <span class="c1"># stop on --foo like options</span>
        <span class="k">if</span> <span class="n">arg</span><span class="p">[:</span><span class="mi">2</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;--&quot;</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="n">arg</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">break</span>
        <span class="c1"># stop on -a, but not on -3 or -3.0</span>
        <span class="k">if</span> <span class="n">arg</span><span class="p">[:</span><span class="mi">1</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;-&quot;</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="n">arg</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">floatable</span><span class="p">(</span><span class="n">arg</span><span class="p">):</span>
            <span class="k">break</span>
        <span class="n">value</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">arg</span><span class="p">)</span>

    <span class="k">del</span> <span class="n">parser</span><span class="o">.</span><span class="n">rargs</span><span class="p">[:</span><span class="nb">len</span><span class="p">(</span><span class="n">value</span><span class="p">)]</span>
    <span class="nb">setattr</span><span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">values</span><span class="p">,</span> <span class="n">option</span><span class="o">.</span><span class="n">dest</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>

<span class="o">...</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-c&quot;</span><span class="p">,</span> <span class="s2">&quot;--callback&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;vararg_attr&quot;</span><span class="p">,</span>
                  <span class="n">action</span><span class="o">=</span><span class="s2">&quot;callback&quot;</span><span class="p">,</span> <span class="n">callback</span><span class="o">=</span><span class="n">vararg_callback</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="extending-optparse">
<span id="optparse-extending-optparse"></span><h2>Extending <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a><a class="headerlink" href="#extending-optparse" title="Link to this heading">¶</a></h2>
<p>Since the two major controlling factors in how <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> interprets
command-line options are the action and type of each option, the most likely
direction of extension is to add new actions and new types.</p>
<section id="adding-new-types">
<span id="optparse-adding-new-types"></span><h3>Adding new types<a class="headerlink" href="#adding-new-types" title="Link to this heading">¶</a></h3>
<p>To add new types, you need to define your own subclass of <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s
<a class="reference internal" href="#optparse.Option" title="optparse.Option"><code class="xref py py-class docutils literal notranslate"><span class="pre">Option</span></code></a> class.  This class has a couple of attributes that define
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s types: <a class="reference internal" href="#optparse.Option.TYPES" title="optparse.Option.TYPES"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TYPES</span></code></a> and <a class="reference internal" href="#optparse.Option.TYPE_CHECKER" title="optparse.Option.TYPE_CHECKER"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TYPE_CHECKER</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.TYPES">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">TYPES</span></span><a class="headerlink" href="#optparse.Option.TYPES" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of type names; in your subclass, simply define a new tuple
<a class="reference internal" href="#optparse.Option.TYPES" title="optparse.Option.TYPES"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TYPES</span></code></a> that builds on the standard one.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.TYPE_CHECKER">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">TYPE_CHECKER</span></span><a class="headerlink" href="#optparse.Option.TYPE_CHECKER" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping type names to type-checking functions.  A type-checking
function has the following signature:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">check_mytype</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">option</span></code> is an <a class="reference internal" href="#optparse.Option" title="optparse.Option"><code class="xref py py-class docutils literal notranslate"><span class="pre">Option</span></code></a> instance, <code class="docutils literal notranslate"><span class="pre">opt</span></code> is an option string
(e.g., <code class="docutils literal notranslate"><span class="pre">-f</span></code>), and <code class="docutils literal notranslate"><span class="pre">value</span></code> is the string from the command line that must
be checked and converted to your desired type.  <code class="docutils literal notranslate"><span class="pre">check_mytype()</span></code> should
return an object of the hypothetical type <code class="docutils literal notranslate"><span class="pre">mytype</span></code>.  The value returned by
a type-checking function will wind up in the OptionValues instance returned
by <a class="reference internal" href="#optparse.OptionParser.parse_args" title="optparse.OptionParser.parse_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OptionParser.parse_args()</span></code></a>, or be passed to a callback as the
<code class="docutils literal notranslate"><span class="pre">value</span></code> parameter.</p>
<p>Your type-checking function should raise <a class="reference internal" href="#optparse.OptionValueError" title="optparse.OptionValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionValueError</span></code></a> if it
encounters any problems.  <a class="reference internal" href="#optparse.OptionValueError" title="optparse.OptionValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OptionValueError</span></code></a> takes a single string
argument, which is passed as-is to <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">error()</span></code>
method, which in turn prepends the program name and the string <code class="docutils literal notranslate"><span class="pre">&quot;error:&quot;</span></code>
and prints everything to stderr before terminating the process.</p>
</dd></dl>

<p>Here’s a silly example that demonstrates adding a <code class="docutils literal notranslate"><span class="pre">&quot;complex&quot;</span></code> option type to
parse Python-style complex numbers on the command line.  (This is even sillier
than it used to be, because <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> 1.3 added built-in support for
complex numbers, but never mind.)</p>
<p>First, the necessary imports:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">copy</span> <span class="kn">import</span> <span class="n">copy</span>
<span class="kn">from</span> <span class="nn">optparse</span> <span class="kn">import</span> <span class="n">Option</span><span class="p">,</span> <span class="n">OptionValueError</span>
</pre></div>
</div>
<p>You need to define your type-checker first, since it’s referred to later (in the
<a class="reference internal" href="#optparse.Option.TYPE_CHECKER" title="optparse.Option.TYPE_CHECKER"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TYPE_CHECKER</span></code></a> class attribute of your Option subclass):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">check_complex</span><span class="p">(</span><span class="n">option</span><span class="p">,</span> <span class="n">opt</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">return</span> <span class="nb">complex</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">OptionValueError</span><span class="p">(</span>
            <span class="s2">&quot;option </span><span class="si">%s</span><span class="s2">: invalid complex value: </span><span class="si">%r</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">opt</span><span class="p">,</span> <span class="n">value</span><span class="p">))</span>
</pre></div>
</div>
<p>Finally, the Option subclass:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyOption</span> <span class="p">(</span><span class="n">Option</span><span class="p">):</span>
    <span class="n">TYPES</span> <span class="o">=</span> <span class="n">Option</span><span class="o">.</span><span class="n">TYPES</span> <span class="o">+</span> <span class="p">(</span><span class="s2">&quot;complex&quot;</span><span class="p">,)</span>
    <span class="n">TYPE_CHECKER</span> <span class="o">=</span> <span class="n">copy</span><span class="p">(</span><span class="n">Option</span><span class="o">.</span><span class="n">TYPE_CHECKER</span><span class="p">)</span>
    <span class="n">TYPE_CHECKER</span><span class="p">[</span><span class="s2">&quot;complex&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">check_complex</span>
</pre></div>
</div>
<p>(If we didn’t make a <a class="reference internal" href="copy.html#module-copy" title="copy: Shallow and deep copy operations."><code class="xref py py-func docutils literal notranslate"><span class="pre">copy()</span></code></a> of <a class="reference internal" href="#optparse.Option.TYPE_CHECKER" title="optparse.Option.TYPE_CHECKER"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Option.TYPE_CHECKER</span></code></a>, we would end
up modifying the <a class="reference internal" href="#optparse.Option.TYPE_CHECKER" title="optparse.Option.TYPE_CHECKER"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TYPE_CHECKER</span></code></a> attribute of <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>’s
Option class.  This being Python, nothing stops you from doing that except good
manners and common sense.)</p>
<p>That’s it!  Now you can write a script that uses the new option type just like
any other <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>-based script, except you have to instruct your
OptionParser to use MyOption instead of Option:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">option_class</span><span class="o">=</span><span class="n">MyOption</span><span class="p">)</span>
<span class="n">parser</span><span class="o">.</span><span class="n">add_option</span><span class="p">(</span><span class="s2">&quot;-c&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;complex&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Alternately, you can build your own option list and pass it to OptionParser; if
you don’t use <code class="xref py py-meth docutils literal notranslate"><span class="pre">add_option()</span></code> in the above way, you don’t need to tell
OptionParser which option class to use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">option_list</span> <span class="o">=</span> <span class="p">[</span><span class="n">MyOption</span><span class="p">(</span><span class="s2">&quot;-c&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;complex&quot;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s2">&quot;c&quot;</span><span class="p">)]</span>
<span class="n">parser</span> <span class="o">=</span> <span class="n">OptionParser</span><span class="p">(</span><span class="n">option_list</span><span class="o">=</span><span class="n">option_list</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="adding-new-actions">
<span id="optparse-adding-new-actions"></span><h3>Adding new actions<a class="headerlink" href="#adding-new-actions" title="Link to this heading">¶</a></h3>
<p>Adding new actions is a bit trickier, because you have to understand that
<a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> has a couple of classifications for actions:</p>
<dl class="simple">
<dt>“store” actions</dt><dd><p>actions that result in <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> storing a value to an attribute of the
current OptionValues instance; these options require a <a class="reference internal" href="#optparse.Option.dest" title="optparse.Option.dest"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dest</span></code></a>
attribute to be supplied to the Option constructor.</p>
</dd>
<dt>“typed” actions</dt><dd><p>actions that take a value from the command line and expect it to be of a
certain type; or rather, a string that can be converted to a certain type.
These options require a <a class="reference internal" href="#optparse.Option.type" title="optparse.Option.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a> attribute to the Option
constructor.</p>
</dd>
</dl>
<p>These are overlapping sets: some default “store” actions are <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;store_const&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&quot;count&quot;</span></code>, while the default “typed”
actions are <code class="docutils literal notranslate"><span class="pre">&quot;store&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&quot;callback&quot;</span></code>.</p>
<p>When you add an action, you need to categorize it by listing it in at least one
of the following class attributes of Option (all are lists of strings):</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.ACTIONS">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">ACTIONS</span></span><a class="headerlink" href="#optparse.Option.ACTIONS" title="Link to this definition">¶</a></dt>
<dd><p>All actions must be listed in ACTIONS.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.STORE_ACTIONS">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">STORE_ACTIONS</span></span><a class="headerlink" href="#optparse.Option.STORE_ACTIONS" title="Link to this definition">¶</a></dt>
<dd><p>“store” actions are additionally listed here.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.TYPED_ACTIONS">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">TYPED_ACTIONS</span></span><a class="headerlink" href="#optparse.Option.TYPED_ACTIONS" title="Link to this definition">¶</a></dt>
<dd><p>“typed” actions are additionally listed here.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="optparse.Option.ALWAYS_TYPED_ACTIONS">
<span class="sig-prename descclassname"><span class="pre">Option.</span></span><span class="sig-name descname"><span class="pre">ALWAYS_TYPED_ACTIONS</span></span><a class="headerlink" href="#optparse.Option.ALWAYS_TYPED_ACTIONS" title="Link to this definition">¶</a></dt>
<dd><p>Actions that always take a type (i.e. whose options always take a value) are
additionally listed here.  The only effect of this is that <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>
assigns the default type, <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code>, to options with no explicit type
whose action is listed in <a class="reference internal" href="#optparse.Option.ALWAYS_TYPED_ACTIONS" title="optparse.Option.ALWAYS_TYPED_ACTIONS"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ALWAYS_TYPED_ACTIONS</span></code></a>.</p>
</dd></dl>

<p>In order to actually implement your new action, you must override Option’s
<code class="xref py py-meth docutils literal notranslate"><span class="pre">take_action()</span></code> method and add a case that recognizes your action.</p>
<p>For example, let’s add an <code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code> action.  This is similar to the standard
<code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code> action, but instead of taking a single value from the command-line
and appending it to an existing list, <code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code> will take multiple values in
a single comma-delimited string, and extend an existing list with them.  That
is, if <code class="docutils literal notranslate"><span class="pre">--names</span></code> is an <code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code> option of type <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code>, the command
line</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">--</span><span class="n">names</span><span class="o">=</span><span class="n">foo</span><span class="p">,</span><span class="n">bar</span> <span class="o">--</span><span class="n">names</span> <span class="n">blah</span> <span class="o">--</span><span class="n">names</span> <span class="n">ding</span><span class="p">,</span><span class="n">dong</span>
</pre></div>
</div>
<p>would result in a list</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="s2">&quot;bar&quot;</span><span class="p">,</span> <span class="s2">&quot;blah&quot;</span><span class="p">,</span> <span class="s2">&quot;ding&quot;</span><span class="p">,</span> <span class="s2">&quot;dong&quot;</span><span class="p">]</span>
</pre></div>
</div>
<p>Again we define a subclass of Option:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyOption</span><span class="p">(</span><span class="n">Option</span><span class="p">):</span>

    <span class="n">ACTIONS</span> <span class="o">=</span> <span class="n">Option</span><span class="o">.</span><span class="n">ACTIONS</span> <span class="o">+</span> <span class="p">(</span><span class="s2">&quot;extend&quot;</span><span class="p">,)</span>
    <span class="n">STORE_ACTIONS</span> <span class="o">=</span> <span class="n">Option</span><span class="o">.</span><span class="n">STORE_ACTIONS</span> <span class="o">+</span> <span class="p">(</span><span class="s2">&quot;extend&quot;</span><span class="p">,)</span>
    <span class="n">TYPED_ACTIONS</span> <span class="o">=</span> <span class="n">Option</span><span class="o">.</span><span class="n">TYPED_ACTIONS</span> <span class="o">+</span> <span class="p">(</span><span class="s2">&quot;extend&quot;</span><span class="p">,)</span>
    <span class="n">ALWAYS_TYPED_ACTIONS</span> <span class="o">=</span> <span class="n">Option</span><span class="o">.</span><span class="n">ALWAYS_TYPED_ACTIONS</span> <span class="o">+</span> <span class="p">(</span><span class="s2">&quot;extend&quot;</span><span class="p">,)</span>

    <span class="k">def</span> <span class="nf">take_action</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">action</span><span class="p">,</span> <span class="n">dest</span><span class="p">,</span> <span class="n">opt</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">values</span><span class="p">,</span> <span class="n">parser</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">action</span> <span class="o">==</span> <span class="s2">&quot;extend&quot;</span><span class="p">:</span>
            <span class="n">lvalue</span> <span class="o">=</span> <span class="n">value</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;,&quot;</span><span class="p">)</span>
            <span class="n">values</span><span class="o">.</span><span class="n">ensure_value</span><span class="p">(</span><span class="n">dest</span><span class="p">,</span> <span class="p">[])</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">lvalue</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">Option</span><span class="o">.</span><span class="n">take_action</span><span class="p">(</span>
                <span class="bp">self</span><span class="p">,</span> <span class="n">action</span><span class="p">,</span> <span class="n">dest</span><span class="p">,</span> <span class="n">opt</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">values</span><span class="p">,</span> <span class="n">parser</span><span class="p">)</span>
</pre></div>
</div>
<p>Features of note:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code> both expects a value on the command-line and stores that value
somewhere, so it goes in both <a class="reference internal" href="#optparse.Option.STORE_ACTIONS" title="optparse.Option.STORE_ACTIONS"><code class="xref py py-attr docutils literal notranslate"><span class="pre">STORE_ACTIONS</span></code></a> and
<a class="reference internal" href="#optparse.Option.TYPED_ACTIONS" title="optparse.Option.TYPED_ACTIONS"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TYPED_ACTIONS</span></code></a>.</p></li>
<li><p>to ensure that <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a> assigns the default type of <code class="docutils literal notranslate"><span class="pre">&quot;string&quot;</span></code> to
<code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code> actions, we put the <code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code> action in
<a class="reference internal" href="#optparse.Option.ALWAYS_TYPED_ACTIONS" title="optparse.Option.ALWAYS_TYPED_ACTIONS"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ALWAYS_TYPED_ACTIONS</span></code></a> as well.</p></li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">MyOption.take_action()</span></code> implements just this one new action, and passes
control back to <code class="xref py py-meth docutils literal notranslate"><span class="pre">Option.take_action()</span></code> for the standard <a class="reference internal" href="#module-optparse" title="optparse: Command-line option parsing library. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a>
actions.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">values</span></code> is an instance of the optparse_parser.Values class, which provides
the very useful <code class="xref py py-meth docutils literal notranslate"><span class="pre">ensure_value()</span></code> method. <code class="xref py py-meth docutils literal notranslate"><span class="pre">ensure_value()</span></code> is
essentially <a class="reference internal" href="functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a> with a safety valve; it is called as</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">values</span><span class="o">.</span><span class="n">ensure_value</span><span class="p">(</span><span class="n">attr</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</pre></div>
</div>
<p>If the <code class="docutils literal notranslate"><span class="pre">attr</span></code> attribute of <code class="docutils literal notranslate"><span class="pre">values</span></code> doesn’t exist or is <code class="docutils literal notranslate"><span class="pre">None</span></code>, then
ensure_value() first sets it to <code class="docutils literal notranslate"><span class="pre">value</span></code>, and then returns <code class="docutils literal notranslate"><span class="pre">value</span></code>. This is
very handy for actions like <code class="docutils literal notranslate"><span class="pre">&quot;extend&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;append&quot;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&quot;count&quot;</span></code>, all
of which accumulate data in a variable and expect that variable to be of a
certain type (a list for the first two, an integer for the latter).  Using
<code class="xref py py-meth docutils literal notranslate"><span class="pre">ensure_value()</span></code> means that scripts using your action don’t have to worry
about setting a default value for the option destinations in question; they
can just leave the default as <code class="docutils literal notranslate"><span class="pre">None</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">ensure_value()</span></code> will take care of
getting it right when it’s needed.</p>
</li>
</ul>
</section>
</section>
<section id="exceptions">
<h2>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h2>
<dl class="py exception">
<dt class="sig sig-object py" id="optparse.OptionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">OptionError</span></span><a class="headerlink" href="#optparse.OptionError" title="Link to this definition">¶</a></dt>
<dd><p>Raised if an <a class="reference internal" href="#optparse.Option" title="optparse.Option"><code class="xref py py-class docutils literal notranslate"><span class="pre">Option</span></code></a> instance is created with invalid or
inconsistent arguments.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="optparse.OptionConflictError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">OptionConflictError</span></span><a class="headerlink" href="#optparse.OptionConflictError" title="Link to this definition">¶</a></dt>
<dd><p>Raised if conflicting options are added to an <a class="reference internal" href="#optparse.OptionParser" title="optparse.OptionParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">OptionParser</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="optparse.OptionValueError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">OptionValueError</span></span><a class="headerlink" href="#optparse.OptionValueError" title="Link to this definition">¶</a></dt>
<dd><p>Raised if an invalid option value is encountered on the command line.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="optparse.BadOptionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">BadOptionError</span></span><a class="headerlink" href="#optparse.BadOptionError" title="Link to this definition">¶</a></dt>
<dd><p>Raised if an invalid option is passed on the command line.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="optparse.AmbiguousOptionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">optparse.</span></span><span class="sig-name descname"><span class="pre">AmbiguousOptionError</span></span><a class="headerlink" href="#optparse.AmbiguousOptionError" title="Link to this definition">¶</a></dt>
<dd><p>Raised if an ambiguous option is passed on the command line.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a><ul>
<li><a class="reference internal" href="#background">Background</a><ul>
<li><a class="reference internal" href="#terminology">Terminology</a></li>
<li><a class="reference internal" href="#what-are-options-for">What are options for?</a></li>
<li><a class="reference internal" href="#what-are-positional-arguments-for">What are positional arguments for?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tutorial">Tutorial</a><ul>
<li><a class="reference internal" href="#understanding-option-actions">Understanding option actions</a></li>
<li><a class="reference internal" href="#the-store-action">The store action</a></li>
<li><a class="reference internal" href="#handling-boolean-flag-options">Handling boolean (flag) options</a></li>
<li><a class="reference internal" href="#other-actions">Other actions</a></li>
<li><a class="reference internal" href="#default-values">Default values</a></li>
<li><a class="reference internal" href="#generating-help">Generating help</a><ul>
<li><a class="reference internal" href="#grouping-options">Grouping Options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#printing-a-version-string">Printing a version string</a></li>
<li><a class="reference internal" href="#how-optparse-handles-errors">How <code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> handles errors</a></li>
<li><a class="reference internal" href="#putting-it-all-together">Putting it all together</a></li>
</ul>
</li>
<li><a class="reference internal" href="#reference-guide">Reference Guide</a><ul>
<li><a class="reference internal" href="#creating-the-parser">Creating the parser</a></li>
<li><a class="reference internal" href="#populating-the-parser">Populating the parser</a></li>
<li><a class="reference internal" href="#defining-options">Defining options</a></li>
<li><a class="reference internal" href="#option-attributes">Option attributes</a></li>
<li><a class="reference internal" href="#standard-option-actions">Standard option actions</a></li>
<li><a class="reference internal" href="#standard-option-types">Standard option types</a></li>
<li><a class="reference internal" href="#parsing-arguments">Parsing arguments</a></li>
<li><a class="reference internal" href="#querying-and-manipulating-your-option-parser">Querying and manipulating your option parser</a></li>
<li><a class="reference internal" href="#conflicts-between-options">Conflicts between options</a></li>
<li><a class="reference internal" href="#cleanup">Cleanup</a></li>
<li><a class="reference internal" href="#other-methods">Other methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#option-callbacks">Option Callbacks</a><ul>
<li><a class="reference internal" href="#defining-a-callback-option">Defining a callback option</a></li>
<li><a class="reference internal" href="#how-callbacks-are-called">How callbacks are called</a></li>
<li><a class="reference internal" href="#raising-errors-in-a-callback">Raising errors in a callback</a></li>
<li><a class="reference internal" href="#callback-example-1-trivial-callback">Callback example 1: trivial callback</a></li>
<li><a class="reference internal" href="#callback-example-2-check-option-order">Callback example 2: check option order</a></li>
<li><a class="reference internal" href="#callback-example-3-check-option-order-generalized">Callback example 3: check option order (generalized)</a></li>
<li><a class="reference internal" href="#callback-example-4-check-arbitrary-condition">Callback example 4: check arbitrary condition</a></li>
<li><a class="reference internal" href="#callback-example-5-fixed-arguments">Callback example 5: fixed arguments</a></li>
<li><a class="reference internal" href="#callback-example-6-variable-arguments">Callback example 6: variable arguments</a></li>
</ul>
</li>
<li><a class="reference internal" href="#extending-optparse">Extending <code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code></a><ul>
<li><a class="reference internal" href="#adding-new-types">Adding new types</a></li>
<li><a class="reference internal" href="#adding-new-actions">Adding new actions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="nntplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ossaudiodev.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ossaudiodev</span></code> — Access to OSS-compatible audio devices</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/optparse.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ossaudiodev.html" title="ossaudiodev — Access to OSS-compatible audio devices"
             >next</a> |</li>
        <li class="right" >
          <a href="nntplib.html" title="nntplib — NNTP protocol client"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">optparse</span></code> — Parser for command line options</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>