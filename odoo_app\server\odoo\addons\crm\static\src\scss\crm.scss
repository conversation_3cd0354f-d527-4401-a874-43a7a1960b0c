.o_lead_opportunity_form {
    // Used to add spacing between fields when placed inline without having
    // empty fields take extra space.
    div.o_lead_opportunity_form_inline_fields > :not(.o_field_empty) {
        margin-right: 1.5em;
    }
}

.o_opportunity_kanban .o_kanban_renderer {
    .oe_kanban_card_ribbon {
        min-height: 105px;
        .o_kanban_record_title {
            max-width: calc(100% - 65px);
        }
        .o_kanban_record_subtitle {
            max-width: calc(100% - 35px);
        }
    }
}

.crm_lead_merge_summary {
    blockquote {
        font-style: normal;
    }

    // Tag colors
    @for $size from 1 through length($o-colors) {
        .o_tag_color_#{$size - 1} {
            border: transparent;
            line-height: normal;
            $background-color: white;
            // no color selected
            @if $size == 1 {
                & {
                    color: black;
                    background-color: $background-color;
                    box-shadow: inset 0 0 0 1px nth($o-colors, $size);
                }
            } @else {
                $background-color: nth($o-colors, $size);
                & {
                    color: white;
                    background-color: $background-color;
                }
            }
            @at-root a#{&} {
                &:hover {
                    color: color-contrast($background-color);
                    background-color: darken($background-color, 10%);
                }
            }
        }
    }
}

