<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Internet Protocols and Support" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/internet.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The modules described in this chapter implement internet protocols and support for related technology. They are all implemented in Python. Most of these modules require the presence of the system-d..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The modules described in this chapter implement internet protocols and support for related technology. They are all implemented in Python. Most of these modules require the presence of the system-d..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Internet Protocols and Support &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="webbrowser — Convenient web-browser controller" href="webbrowser.html" />
    <link rel="prev" title="xml.parsers.expat — Fast XML parsing using Expat" href="pyexpat.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/internet.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pyexpat.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="webbrowser.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/internet.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="webbrowser.html" title="webbrowser — Convenient web-browser controller"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pyexpat.html" title="xml.parsers.expat — Fast XML parsing using Expat"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Internet Protocols and Support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="internet-protocols-and-support">
<span id="internet"></span><h1>Internet Protocols and Support<a class="headerlink" href="#internet-protocols-and-support" title="Link to this heading">¶</a></h1>
<p id="index-1"><span id="index-0"></span>The modules described in this chapter implement internet protocols and  support
for related technology.  They are all implemented in Python. Most of these
modules require the presence of the system-dependent module <a class="reference internal" href="socket.html#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a>, which
is currently supported on most popular platforms.  Here is an overview:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="webbrowser.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a><ul>
<li class="toctree-l2"><a class="reference internal" href="webbrowser.html#browser-controller-objects">Browser Controller Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="wsgiref.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#module-wsgiref.util"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code> – WSGI environment utilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#module-wsgiref.headers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.headers</span></code> – WSGI response header tools</a></li>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#module-wsgiref.simple_server"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.simple_server</span></code> – a simple WSGI HTTP server</a></li>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#module-wsgiref.validate"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.validate</span></code> — WSGI conformance checker</a></li>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#module-wsgiref.handlers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code> – server/gateway base classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#module-wsgiref.types"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.types</span></code> – WSGI types for static type checking</a></li>
<li class="toctree-l2"><a class="reference internal" href="wsgiref.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="urllib.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code> — URL handling modules</a></li>
<li class="toctree-l1"><a class="reference internal" href="urllib.request.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> — Extensible library for opening URLs</a><ul>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#request-objects">Request Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#openerdirector-objects">OpenerDirector Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#basehandler-objects">BaseHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httpredirecthandler-objects">HTTPRedirectHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httpcookieprocessor-objects">HTTPCookieProcessor Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#proxyhandler-objects">ProxyHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httppasswordmgr-objects">HTTPPasswordMgr Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httppasswordmgrwithpriorauth-objects">HTTPPasswordMgrWithPriorAuth Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#abstractbasicauthhandler-objects">AbstractBasicAuthHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httpbasicauthhandler-objects">HTTPBasicAuthHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#proxybasicauthhandler-objects">ProxyBasicAuthHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#abstractdigestauthhandler-objects">AbstractDigestAuthHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httpdigestauthhandler-objects">HTTPDigestAuthHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#proxydigestauthhandler-objects">ProxyDigestAuthHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httphandler-objects">HTTPHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httpshandler-objects">HTTPSHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#filehandler-objects">FileHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#datahandler-objects">DataHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#ftphandler-objects">FTPHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#cacheftphandler-objects">CacheFTPHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#unknownhandler-objects">UnknownHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#httperrorprocessor-objects">HTTPErrorProcessor Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#examples">Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#legacy-interface">Legacy interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.request.html#urllib-request-restrictions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> Restrictions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="urllib.request.html#module-urllib.response"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.response</span></code> — Response classes used by urllib</a></li>
<li class="toctree-l1"><a class="reference internal" href="urllib.parse.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.parse</span></code> — Parse URLs into components</a><ul>
<li class="toctree-l2"><a class="reference internal" href="urllib.parse.html#url-parsing">URL Parsing</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.parse.html#url-parsing-security">URL parsing security</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.parse.html#parsing-ascii-encoded-bytes">Parsing ASCII Encoded Bytes</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.parse.html#structured-parse-results">Structured Parse Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="urllib.parse.html#url-quoting">URL Quoting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="urllib.error.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.error</span></code> — Exception classes raised by urllib.request</a></li>
<li class="toctree-l1"><a class="reference internal" href="urllib.robotparser.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.robotparser</span></code> —  Parser for robots.txt</a></li>
<li class="toctree-l1"><a class="reference internal" href="http.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a><ul>
<li class="toctree-l2"><a class="reference internal" href="http.html#http-status-codes">HTTP status codes</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.html#http-status-category">HTTP status category</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.html#http-methods">HTTP methods</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="http.client.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a><ul>
<li class="toctree-l2"><a class="reference internal" href="http.client.html#httpconnection-objects">HTTPConnection Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.client.html#httpresponse-objects">HTTPResponse Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.client.html#examples">Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.client.html#httpmessage-objects">HTTPMessage Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ftplib.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ftplib.html#reference">Reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ftplib.html#ftp-objects">FTP objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="ftplib.html#ftp-tls-objects">FTP_TLS objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="ftplib.html#module-variables">Module variables</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="poplib.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a><ul>
<li class="toctree-l2"><a class="reference internal" href="poplib.html#pop3-objects">POP3 Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="poplib.html#pop3-example">POP3 Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="imaplib.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a><ul>
<li class="toctree-l2"><a class="reference internal" href="imaplib.html#imap4-objects">IMAP4 Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="imaplib.html#imap4-example">IMAP4 Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="smtplib.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a><ul>
<li class="toctree-l2"><a class="reference internal" href="smtplib.html#smtp-objects">SMTP Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="smtplib.html#smtp-example">SMTP Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="uuid.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a><ul>
<li class="toctree-l2"><a class="reference internal" href="uuid.html#command-line-usage">Command-Line Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="uuid.html#example">Example</a></li>
<li class="toctree-l2"><a class="reference internal" href="uuid.html#command-line-example">Command-Line Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="socketserver.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code> — A framework for network servers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="socketserver.html#server-creation-notes">Server Creation Notes</a></li>
<li class="toctree-l2"><a class="reference internal" href="socketserver.html#server-objects">Server Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="socketserver.html#request-handler-objects">Request Handler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="socketserver.html#examples">Examples</a><ul>
<li class="toctree-l3"><a class="reference internal" href="socketserver.html#socketserver-tcpserver-example"><code class="xref py py-class docutils literal notranslate"><span class="pre">socketserver.TCPServer</span></code> Example</a></li>
<li class="toctree-l3"><a class="reference internal" href="socketserver.html#socketserver-udpserver-example"><code class="xref py py-class docutils literal notranslate"><span class="pre">socketserver.UDPServer</span></code> Example</a></li>
<li class="toctree-l3"><a class="reference internal" href="socketserver.html#asynchronous-mixins">Asynchronous Mixins</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="http.server.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code> — HTTP servers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="http.server.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="http.cookies.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a><ul>
<li class="toctree-l2"><a class="reference internal" href="http.cookies.html#cookie-objects">Cookie Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookies.html#morsel-objects">Morsel Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookies.html#example">Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="http.cookiejar.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a><ul>
<li class="toctree-l2"><a class="reference internal" href="http.cookiejar.html#cookiejar-and-filecookiejar-objects">CookieJar and FileCookieJar Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookiejar.html#filecookiejar-subclasses-and-co-operation-with-web-browsers">FileCookieJar subclasses and co-operation with web browsers</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookiejar.html#cookiepolicy-objects">CookiePolicy Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookiejar.html#defaultcookiepolicy-objects">DefaultCookiePolicy Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookiejar.html#cookie-objects">Cookie Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="http.cookiejar.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xmlrpc.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc</span></code> — XMLRPC server and client modules</a></li>
<li class="toctree-l1"><a class="reference internal" href="xmlrpc.client.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#serverproxy-objects">ServerProxy Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#datetime-objects">DateTime Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#binary-objects">Binary Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#fault-objects">Fault Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#protocolerror-objects">ProtocolError Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#multicall-objects">MultiCall Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#convenience-functions">Convenience Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#example-of-client-usage">Example of Client Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.client.html#example-of-client-and-server-usage">Example of Client and Server Usage</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xmlrpc.server.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.server.html#simplexmlrpcserver-objects">SimpleXMLRPCServer Objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xmlrpc.server.html#simplexmlrpcserver-example">SimpleXMLRPCServer Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.server.html#cgixmlrpcrequesthandler">CGIXMLRPCRequestHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.server.html#documenting-xmlrpc-server">Documenting XMLRPC server</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.server.html#docxmlrpcserver-objects">DocXMLRPCServer Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.server.html#doccgixmlrpcrequesthandler">DocCGIXMLRPCRequestHandler</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ipaddress.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ipaddress.html#convenience-factory-functions">Convenience factory functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="ipaddress.html#ip-addresses">IP Addresses</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#address-objects">Address objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#conversion-to-strings-and-integers">Conversion to Strings and Integers</a></li>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#operators">Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="ipaddress.html#comparison-operators">Comparison operators</a></li>
<li class="toctree-l4"><a class="reference internal" href="ipaddress.html#arithmetic-operators">Arithmetic operators</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ipaddress.html#ip-network-definitions">IP Network definitions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#prefix-net-mask-and-host-mask">Prefix, net mask and host mask</a></li>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#network-objects">Network objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#id1">Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="ipaddress.html#logical-operators">Logical operators</a></li>
<li class="toctree-l4"><a class="reference internal" href="ipaddress.html#iteration">Iteration</a></li>
<li class="toctree-l4"><a class="reference internal" href="ipaddress.html#networks-as-containers-of-addresses">Networks as containers of addresses</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ipaddress.html#interface-objects">Interface objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ipaddress.html#id2">Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="ipaddress.html#id3">Logical operators</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ipaddress.html#other-module-level-functions">Other Module Level Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="ipaddress.html#custom-exceptions">Custom Exceptions</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pyexpat.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="webbrowser.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/internet.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="webbrowser.html" title="webbrowser — Convenient web-browser controller"
             >next</a> |</li>
        <li class="right" >
          <a href="pyexpat.html" title="xml.parsers.expat — Fast XML parsing using Expat"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Internet Protocols and Support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>