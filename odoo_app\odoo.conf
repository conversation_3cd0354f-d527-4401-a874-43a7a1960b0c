[options]
# Database settings
db_host = localhost
db_port = 5432
db_user = postgres
db_password =
db_name = False

# Server settings
http_port = 8069
http_interface = 0.0.0.0

# Addons path - include both standard and custom addons
addons_path = odoo_source/addons,addons/16.0

# Data directory
data_dir = .

# Log settings
log_level = info
log_handler = :INFO

# Security
admin_passwd = admin123

# Performance settings
workers = 0
max_cron_threads = 2

# File storage
filestore = filestore

# Session storage
session_dir = sessions

# Limit settings
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 60
limit_time_real = 120

# Email settings (optional)
# email_from = <EMAIL>
# smtp_server = localhost
# smtp_port = 25
# smtp_user = 
# smtp_password = 
# smtp_ssl = False

# Development settings
dev_mode = reload,qweb,werkzeug,xml

# Language and localization
# Default language
# lang = en_US

# Timezone
# timezone = UTC

# Currency (for jewelry business)
# Default currency will be set in the database

# Custom jewelry module settings
# These will be used by our custom jewelry management module
jewelry_default_metal = gold
jewelry_default_purity = 18k
jewelry_price_update_interval = 3600

# Backup settings
# db_backup_location = backups/
# auto_backup = True
# backup_retention_days = 30

# Security enhancements
# proxy_mode = True
# secure_cookie = True

# Multi-database settings
# dbfilter = ^jewelry_.*$
list_db = True

# Reporting
# report_url = http://localhost:8069

# Internationalization
# translate_modules = ['base', 'jewelry_management']

# Custom logging for jewelry operations
# log_db = True
# log_db_level = warning

# Performance monitoring
# enable_profiling = False
# profiling_output = profiles/

# Module auto-installation
# auto_reload = True
# test_enable = False
# test_file = 
# test_tags = 

# WebSocket settings for real-time updates
# websocket_port = 8072

# Longpolling for live updates
longpolling_port = 8072

# CORS settings for API access
# cors = *

# API settings
# api_enable = True
# api_rate_limit = 100

# Jewelry-specific configurations
# These will be used by our custom module
jewelry_weight_unit = gram
jewelry_dimension_unit = mm
jewelry_photo_max_size = 5242880
jewelry_certificate_storage = certificates/

# Inventory settings for jewelry
jewelry_auto_lot_tracking = True
jewelry_serial_tracking = True
jewelry_expiry_tracking = False

# Pricing settings
jewelry_multi_currency = True
jewelry_price_precision = 4
jewelry_cost_method = fifo

# Manufacturing settings for jewelry
jewelry_mrp_enable = True
jewelry_quality_control = True
jewelry_batch_tracking = True

# Customer settings
jewelry_customer_credit_limit = True
jewelry_loyalty_program = True
jewelry_warranty_tracking = True

# Reporting and analytics
jewelry_advanced_reports = True
jewelry_dashboard = True
jewelry_kpi_tracking = True

# Integration settings
jewelry_pos_integration = True
jewelry_ecommerce_integration = True
jewelry_accounting_integration = True

# Compliance and certification
jewelry_hallmark_tracking = True
jewelry_certification_required = True
jewelry_regulatory_compliance = True

# Notification settings
jewelry_low_stock_alert = True
jewelry_price_change_alert = True
jewelry_order_notifications = True

# Backup and security for jewelry data
jewelry_data_encryption = True
jewelry_audit_trail = True
jewelry_user_access_control = True

# Mobile app settings
jewelry_mobile_app = True
jewelry_barcode_scanning = True
jewelry_photo_capture = True

# Advanced features
jewelry_ai_recommendations = False
jewelry_blockchain_tracking = False
jewelry_iot_integration = False
