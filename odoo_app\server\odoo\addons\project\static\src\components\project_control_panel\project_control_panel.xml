<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="project.ProjectControlPanelContentBadge">
        <t t-tag="isProjectUser ? 'button' : 'span'" class="badge border d-flex p-2 ms-2 bg-view" data-hotkey="y">
            <span t-attf-class="o_status_bubble o_color_bubble_{{data.color}}"/>
            <span t-att-class="'fw-normal ms-1' + (data.color === 0 ? ' text-muted' : '')" t-esc="data.status"/>
        </t>
    </t>

    <t t-name="project.ProjectControlPanelContent">
        <t t-if="showProjectUpdate">
            <div t-if="isProjectUser" class="o_project_updates_breadcrumb z-index-1" t-on-click="onStatusClick">
                <t t-call="project.ProjectControlPanelContentBadge"></t>
            </div>
            <div t-else="" class="o_project_updates_breadcrumb z-index-1">
                <t t-call="project.ProjectControlPanelContentBadge"></t>
            </div>
        </t>
    </t>

    <t t-name="project.ProjectControlPanel" t-inherit="web.ControlPanel" t-inherit-mode="primary">
        <xpath expr="//t[@t-call='web.Breadcrumbs']" position="after">
            <t t-call="project.ProjectControlPanelContent"/>
        </xpath>
    </t>

</templates>
