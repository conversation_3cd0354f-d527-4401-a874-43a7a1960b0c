<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tracemalloc — Trace memory allocations" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tracemalloc.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tracemalloc.py The tracemalloc module is a debug tool to trace memory blocks allocated by Python. It provides the following information: Traceback where an object was allocated, St..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tracemalloc.py The tracemalloc module is a debug tool to trace memory blocks allocated by Python. It provides the following information: Traceback where an object was allocated, St..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tracemalloc — Trace memory allocations &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Software Packaging and Distribution" href="distribution.html" />
    <link rel="prev" title="trace — Trace or track Python statement execution" href="trace.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tracemalloc.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code> — Trace memory allocations</a><ul>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#display-the-top-10">Display the top 10</a></li>
<li><a class="reference internal" href="#compute-differences">Compute differences</a></li>
<li><a class="reference internal" href="#get-the-traceback-of-a-memory-block">Get the traceback of a memory block</a></li>
<li><a class="reference internal" href="#pretty-top">Pretty top</a><ul>
<li><a class="reference internal" href="#record-the-current-and-peak-size-of-all-traced-memory-blocks">Record the current and peak size of all traced memory blocks</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#api">API</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#domainfilter">DomainFilter</a></li>
<li><a class="reference internal" href="#filter">Filter</a></li>
<li><a class="reference internal" href="#frame">Frame</a></li>
<li><a class="reference internal" href="#snapshot">Snapshot</a></li>
<li><a class="reference internal" href="#statistic">Statistic</a></li>
<li><a class="reference internal" href="#statisticdiff">StatisticDiff</a></li>
<li><a class="reference internal" href="#trace">Trace</a></li>
<li><a class="reference internal" href="#traceback">Traceback</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="trace.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code> — Trace or track Python statement execution</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="distribution.html"
                          title="next chapter">Software Packaging and Distribution</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tracemalloc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="distribution.html" title="Software Packaging and Distribution"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="trace.html" title="trace — Trace or track Python statement execution"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="debug.html" accesskey="U">Debugging and Profiling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code> — Trace memory allocations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tracemalloc">
<span id="tracemalloc-trace-memory-allocations"></span><h1><a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> — Trace memory allocations<a class="headerlink" href="#module-tracemalloc" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tracemalloc.py">Lib/tracemalloc.py</a></p>
<hr class="docutils" />
<p>The tracemalloc module is a debug tool to trace memory blocks allocated by
Python. It provides the following information:</p>
<ul class="simple">
<li><p>Traceback where an object was allocated</p></li>
<li><p>Statistics on allocated memory blocks per filename and per line number:
total size, number and average size of allocated memory blocks</p></li>
<li><p>Compute the differences between two snapshots to detect memory leaks</p></li>
</ul>
<p>To trace most memory blocks allocated by Python, the module should be started
as early as possible by setting the <span class="target" id="index-0"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONTRACEMALLOC"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONTRACEMALLOC</span></code></a> environment
variable to <code class="docutils literal notranslate"><span class="pre">1</span></code>, or by using <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> <code class="docutils literal notranslate"><span class="pre">tracemalloc</span></code> command line
option. The <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">tracemalloc.start()</span></code></a> function can be called at runtime to
start tracing Python memory allocations.</p>
<p>By default, a trace of an allocated memory block only stores the most recent
frame (1 frame). To store 25 frames at startup: set the
<span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONTRACEMALLOC"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONTRACEMALLOC</span></code></a> environment variable to <code class="docutils literal notranslate"><span class="pre">25</span></code>, or use the
<a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> <code class="docutils literal notranslate"><span class="pre">tracemalloc=25</span></code> command line option.</p>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<section id="display-the-top-10">
<h3>Display the top 10<a class="headerlink" href="#display-the-top-10" title="Link to this heading">¶</a></h3>
<p>Display the 10 files allocating the most memory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tracemalloc</span>

<span class="n">tracemalloc</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

<span class="c1"># ... run your application ...</span>

<span class="n">snapshot</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">take_snapshot</span><span class="p">()</span>
<span class="n">top_stats</span> <span class="o">=</span> <span class="n">snapshot</span><span class="o">.</span><span class="n">statistics</span><span class="p">(</span><span class="s1">&#39;lineno&#39;</span><span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;[ Top 10 ]&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">stat</span> <span class="ow">in</span> <span class="n">top_stats</span><span class="p">[:</span><span class="mi">10</span><span class="p">]:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">stat</span><span class="p">)</span>
</pre></div>
</div>
<p>Example of output of the Python test suite:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span> <span class="n">Top</span> <span class="mi">10</span> <span class="p">]</span>
<span class="o">&lt;</span><span class="n">frozen</span> <span class="n">importlib</span><span class="o">.</span><span class="n">_bootstrap</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">716</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">4855</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">39328</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">126</span> <span class="n">B</span>
<span class="o">&lt;</span><span class="n">frozen</span> <span class="n">importlib</span><span class="o">.</span><span class="n">_bootstrap</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">284</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">521</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">3199</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">167</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">collections</span><span class="o">/</span><span class="fm">__init__</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">368</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">244</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">2315</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">108</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">unittest</span><span class="o">/</span><span class="n">case</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">381</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">185</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">779</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">243</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">unittest</span><span class="o">/</span><span class="n">case</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">402</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">154</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">378</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">416</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">abc</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">133</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">88.7</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">347</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">262</span> <span class="n">B</span>
<span class="o">&lt;</span><span class="n">frozen</span> <span class="n">importlib</span><span class="o">.</span><span class="n">_bootstrap</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">1446</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">70.4</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">911</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">79</span> <span class="n">B</span>
<span class="o">&lt;</span><span class="n">frozen</span> <span class="n">importlib</span><span class="o">.</span><span class="n">_bootstrap</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">1454</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">52.0</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">25</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">2131</span> <span class="n">B</span>
<span class="o">&lt;</span><span class="n">string</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">5</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">49.7</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">148</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mi">344</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">sysconfig</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">411</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">48.0</span> <span class="n">KiB</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="mf">48.0</span> <span class="n">KiB</span>
</pre></div>
</div>
<p>We can see that Python loaded <code class="docutils literal notranslate"><span class="pre">4855</span> <span class="pre">KiB</span></code> data (bytecode and constants) from
modules and that the <a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a> module allocated <code class="docutils literal notranslate"><span class="pre">244</span> <span class="pre">KiB</span></code> to build
<a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">namedtuple</span></code></a> types.</p>
<p>See <a class="reference internal" href="#tracemalloc.Snapshot.statistics" title="tracemalloc.Snapshot.statistics"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.statistics()</span></code></a> for more options.</p>
</section>
<section id="compute-differences">
<h3>Compute differences<a class="headerlink" href="#compute-differences" title="Link to this heading">¶</a></h3>
<p>Take two snapshots and display the differences:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tracemalloc</span>
<span class="n">tracemalloc</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
<span class="c1"># ... start your application ...</span>

<span class="n">snapshot1</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">take_snapshot</span><span class="p">()</span>
<span class="c1"># ... call the function leaking memory ...</span>
<span class="n">snapshot2</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">take_snapshot</span><span class="p">()</span>

<span class="n">top_stats</span> <span class="o">=</span> <span class="n">snapshot2</span><span class="o">.</span><span class="n">compare_to</span><span class="p">(</span><span class="n">snapshot1</span><span class="p">,</span> <span class="s1">&#39;lineno&#39;</span><span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;[ Top 10 differences ]&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">stat</span> <span class="ow">in</span> <span class="n">top_stats</span><span class="p">[:</span><span class="mi">10</span><span class="p">]:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">stat</span><span class="p">)</span>
</pre></div>
</div>
<p>Example of output before/after running some tests of the Python test suite:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span> <span class="n">Top</span> <span class="mi">10</span> <span class="n">differences</span> <span class="p">]</span>
<span class="o">&lt;</span><span class="n">frozen</span> <span class="n">importlib</span><span class="o">.</span><span class="n">_bootstrap</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">716</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">8173</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mi">4428</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">71332</span> <span class="p">(</span><span class="o">+</span><span class="mi">39369</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">117</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">linecache</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">127</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">940</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mi">940</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">8106</span> <span class="p">(</span><span class="o">+</span><span class="mi">8106</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">119</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">unittest</span><span class="o">/</span><span class="n">case</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">571</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">298</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mi">298</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">589</span> <span class="p">(</span><span class="o">+</span><span class="mi">589</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">519</span> <span class="n">B</span>
<span class="o">&lt;</span><span class="n">frozen</span> <span class="n">importlib</span><span class="o">.</span><span class="n">_bootstrap</span><span class="o">&gt;</span><span class="p">:</span><span class="mi">284</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">1005</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mi">166</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">7423</span> <span class="p">(</span><span class="o">+</span><span class="mi">1526</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">139</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">mimetypes</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">217</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mi">112</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mi">112</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">1334</span> <span class="p">(</span><span class="o">+</span><span class="mi">1334</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">86</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">http</span><span class="o">/</span><span class="n">server</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">848</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">96.0</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mf">96.0</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">1</span> <span class="p">(</span><span class="o">+</span><span class="mi">1</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mf">96.0</span> <span class="n">KiB</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">inspect</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">1465</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">83.5</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mf">83.5</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">109</span> <span class="p">(</span><span class="o">+</span><span class="mi">109</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">784</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">unittest</span><span class="o">/</span><span class="n">mock</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">491</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">77.7</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mf">77.7</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">143</span> <span class="p">(</span><span class="o">+</span><span class="mi">143</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">557</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">urllib</span><span class="o">/</span><span class="n">parse</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">476</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">71.8</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mf">71.8</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">969</span> <span class="p">(</span><span class="o">+</span><span class="mi">969</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">76</span> <span class="n">B</span>
<span class="o">/</span><span class="n">usr</span><span class="o">/</span><span class="n">lib</span><span class="o">/</span><span class="n">python3</span><span class="mf">.4</span><span class="o">/</span><span class="n">contextlib</span><span class="o">.</span><span class="n">py</span><span class="p">:</span><span class="mi">38</span><span class="p">:</span> <span class="n">size</span><span class="o">=</span><span class="mf">67.2</span> <span class="n">KiB</span> <span class="p">(</span><span class="o">+</span><span class="mf">67.2</span> <span class="n">KiB</span><span class="p">),</span> <span class="n">count</span><span class="o">=</span><span class="mi">126</span> <span class="p">(</span><span class="o">+</span><span class="mi">126</span><span class="p">),</span> <span class="n">average</span><span class="o">=</span><span class="mi">546</span> <span class="n">B</span>
</pre></div>
</div>
<p>We can see that Python has loaded <code class="docutils literal notranslate"><span class="pre">8173</span> <span class="pre">KiB</span></code> of module data (bytecode and
constants), and that this is <code class="docutils literal notranslate"><span class="pre">4428</span> <span class="pre">KiB</span></code> more than had been loaded before the
tests, when the previous snapshot was taken. Similarly, the <a class="reference internal" href="linecache.html#module-linecache" title="linecache: Provides random access to individual lines from text files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">linecache</span></code></a>
module has cached <code class="docutils literal notranslate"><span class="pre">940</span> <span class="pre">KiB</span></code> of Python source code to format tracebacks, all
of it since the previous snapshot.</p>
<p>If the system has little free memory, snapshots can be written on disk using
the <a class="reference internal" href="#tracemalloc.Snapshot.dump" title="tracemalloc.Snapshot.dump"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.dump()</span></code></a> method to analyze the snapshot offline. Then use the
<a class="reference internal" href="#tracemalloc.Snapshot.load" title="tracemalloc.Snapshot.load"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.load()</span></code></a> method reload the snapshot.</p>
</section>
<section id="get-the-traceback-of-a-memory-block">
<h3>Get the traceback of a memory block<a class="headerlink" href="#get-the-traceback-of-a-memory-block" title="Link to this heading">¶</a></h3>
<p>Code to display the traceback of the biggest memory block:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tracemalloc</span>

<span class="c1"># Store 25 frames</span>
<span class="n">tracemalloc</span><span class="o">.</span><span class="n">start</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>

<span class="c1"># ... run your application ...</span>

<span class="n">snapshot</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">take_snapshot</span><span class="p">()</span>
<span class="n">top_stats</span> <span class="o">=</span> <span class="n">snapshot</span><span class="o">.</span><span class="n">statistics</span><span class="p">(</span><span class="s1">&#39;traceback&#39;</span><span class="p">)</span>

<span class="c1"># pick the biggest memory block</span>
<span class="n">stat</span> <span class="o">=</span> <span class="n">top_stats</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> memory blocks: </span><span class="si">%.1f</span><span class="s2"> KiB&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">stat</span><span class="o">.</span><span class="n">count</span><span class="p">,</span> <span class="n">stat</span><span class="o">.</span><span class="n">size</span> <span class="o">/</span> <span class="mi">1024</span><span class="p">))</span>
<span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">stat</span><span class="o">.</span><span class="n">traceback</span><span class="o">.</span><span class="n">format</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</pre></div>
</div>
<p>Example of output of the Python test suite (traceback limited to 25 frames):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="mi">903</span> <span class="n">memory</span> <span class="n">blocks</span><span class="p">:</span> <span class="mf">870.1</span> <span class="n">KiB</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">716</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1036</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">934</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1068</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">619</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1581</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1614</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/doctest.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">101</span>
    <span class="kn">import</span> <span class="nn">pdb</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">284</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">938</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1068</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">619</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1581</span>
  <span class="n">File</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1614</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/support/__init__.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1728</span>
    <span class="kn">import</span> <span class="nn">doctest</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/test_pickletools.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">21</span>
    <span class="n">support</span><span class="o">.</span><span class="n">run_doctest</span><span class="p">(</span><span class="n">pickletools</span><span class="p">)</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/regrtest.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1276</span>
    <span class="n">test_runner</span><span class="p">()</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/regrtest.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">976</span>
    <span class="n">display_failure</span><span class="o">=</span><span class="ow">not</span> <span class="n">verbose</span><span class="p">)</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/regrtest.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">761</span>
    <span class="n">match_tests</span><span class="o">=</span><span class="n">ns</span><span class="o">.</span><span class="n">match_tests</span><span class="p">)</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/regrtest.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">1563</span>
    <span class="n">main</span><span class="p">()</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/test/__main__.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">3</span>
    <span class="n">regrtest</span><span class="o">.</span><span class="n">main_in_temp_cwd</span><span class="p">()</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/runpy.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">73</span>
    <span class="n">exec</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">run_globals</span><span class="p">)</span>
  <span class="n">File</span> <span class="s2">&quot;/usr/lib/python3.4/runpy.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">160</span>
    <span class="s2">&quot;__main__&quot;</span><span class="p">,</span> <span class="n">fname</span><span class="p">,</span> <span class="n">loader</span><span class="p">,</span> <span class="n">pkg_name</span><span class="p">)</span>
</pre></div>
</div>
<p>We can see that the most memory was allocated in the <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> module to
load data (bytecode and constants) from modules: <code class="docutils literal notranslate"><span class="pre">870.1</span> <span class="pre">KiB</span></code>. The traceback is
where the <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> loaded data most recently: on the <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">pdb</span></code>
line of the <a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a> module. The traceback may change if a new module is
loaded.</p>
</section>
<section id="pretty-top">
<h3>Pretty top<a class="headerlink" href="#pretty-top" title="Link to this heading">¶</a></h3>
<p>Code to display the 10 lines allocating the most memory with a pretty output,
ignoring <code class="docutils literal notranslate"><span class="pre">&lt;frozen</span> <span class="pre">importlib._bootstrap&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">&lt;unknown&gt;</span></code> files:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">linecache</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">tracemalloc</span>

<span class="k">def</span> <span class="nf">display_top</span><span class="p">(</span><span class="n">snapshot</span><span class="p">,</span> <span class="n">key_type</span><span class="o">=</span><span class="s1">&#39;lineno&#39;</span><span class="p">,</span> <span class="n">limit</span><span class="o">=</span><span class="mi">10</span><span class="p">):</span>
    <span class="n">snapshot</span> <span class="o">=</span> <span class="n">snapshot</span><span class="o">.</span><span class="n">filter_traces</span><span class="p">((</span>
        <span class="n">tracemalloc</span><span class="o">.</span><span class="n">Filter</span><span class="p">(</span><span class="kc">False</span><span class="p">,</span> <span class="s2">&quot;&lt;frozen importlib._bootstrap&gt;&quot;</span><span class="p">),</span>
        <span class="n">tracemalloc</span><span class="o">.</span><span class="n">Filter</span><span class="p">(</span><span class="kc">False</span><span class="p">,</span> <span class="s2">&quot;&lt;unknown&gt;&quot;</span><span class="p">),</span>
    <span class="p">))</span>
    <span class="n">top_stats</span> <span class="o">=</span> <span class="n">snapshot</span><span class="o">.</span><span class="n">statistics</span><span class="p">(</span><span class="n">key_type</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Top </span><span class="si">%s</span><span class="s2"> lines&quot;</span> <span class="o">%</span> <span class="n">limit</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">stat</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">top_stats</span><span class="p">[:</span><span class="n">limit</span><span class="p">],</span> <span class="mi">1</span><span class="p">):</span>
        <span class="n">frame</span> <span class="o">=</span> <span class="n">stat</span><span class="o">.</span><span class="n">traceback</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;#</span><span class="si">%s</span><span class="s2">: </span><span class="si">%s</span><span class="s2">:</span><span class="si">%s</span><span class="s2">: </span><span class="si">%.1f</span><span class="s2"> KiB&quot;</span>
              <span class="o">%</span> <span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="n">frame</span><span class="o">.</span><span class="n">filename</span><span class="p">,</span> <span class="n">frame</span><span class="o">.</span><span class="n">lineno</span><span class="p">,</span> <span class="n">stat</span><span class="o">.</span><span class="n">size</span> <span class="o">/</span> <span class="mi">1024</span><span class="p">))</span>
        <span class="n">line</span> <span class="o">=</span> <span class="n">linecache</span><span class="o">.</span><span class="n">getline</span><span class="p">(</span><span class="n">frame</span><span class="o">.</span><span class="n">filename</span><span class="p">,</span> <span class="n">frame</span><span class="o">.</span><span class="n">lineno</span><span class="p">)</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
        <span class="k">if</span> <span class="n">line</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;    </span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">line</span><span class="p">)</span>

    <span class="n">other</span> <span class="o">=</span> <span class="n">top_stats</span><span class="p">[</span><span class="n">limit</span><span class="p">:]</span>
    <span class="k">if</span> <span class="n">other</span><span class="p">:</span>
        <span class="n">size</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">stat</span><span class="o">.</span><span class="n">size</span> <span class="k">for</span> <span class="n">stat</span> <span class="ow">in</span> <span class="n">other</span><span class="p">)</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> other: </span><span class="si">%.1f</span><span class="s2"> KiB&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">other</span><span class="p">),</span> <span class="n">size</span> <span class="o">/</span> <span class="mi">1024</span><span class="p">))</span>
    <span class="n">total</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">stat</span><span class="o">.</span><span class="n">size</span> <span class="k">for</span> <span class="n">stat</span> <span class="ow">in</span> <span class="n">top_stats</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Total allocated size: </span><span class="si">%.1f</span><span class="s2"> KiB&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">total</span> <span class="o">/</span> <span class="mi">1024</span><span class="p">))</span>

<span class="n">tracemalloc</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

<span class="c1"># ... run your application ...</span>

<span class="n">snapshot</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">take_snapshot</span><span class="p">()</span>
<span class="n">display_top</span><span class="p">(</span><span class="n">snapshot</span><span class="p">)</span>
</pre></div>
</div>
<p>Example of output of the Python test suite:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Top</span> <span class="mi">10</span> <span class="n">lines</span>
<span class="c1">#1: Lib/base64.py:414: 419.8 KiB</span>
    <span class="n">_b85chars2</span> <span class="o">=</span> <span class="p">[(</span><span class="n">a</span> <span class="o">+</span> <span class="n">b</span><span class="p">)</span> <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">_b85chars</span> <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="n">_b85chars</span><span class="p">]</span>
<span class="c1">#2: Lib/base64.py:306: 419.8 KiB</span>
    <span class="n">_a85chars2</span> <span class="o">=</span> <span class="p">[(</span><span class="n">a</span> <span class="o">+</span> <span class="n">b</span><span class="p">)</span> <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">_a85chars</span> <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="n">_a85chars</span><span class="p">]</span>
<span class="c1">#3: collections/__init__.py:368: 293.6 KiB</span>
    <span class="n">exec</span><span class="p">(</span><span class="n">class_definition</span><span class="p">,</span> <span class="n">namespace</span><span class="p">)</span>
<span class="c1">#4: Lib/abc.py:133: 115.2 KiB</span>
    <span class="bp">cls</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="n">mcls</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">bases</span><span class="p">,</span> <span class="n">namespace</span><span class="p">)</span>
<span class="c1">#5: unittest/case.py:574: 103.1 KiB</span>
    <span class="n">testMethod</span><span class="p">()</span>
<span class="c1">#6: Lib/linecache.py:127: 95.4 KiB</span>
    <span class="n">lines</span> <span class="o">=</span> <span class="n">fp</span><span class="o">.</span><span class="n">readlines</span><span class="p">()</span>
<span class="c1">#7: urllib/parse.py:476: 71.8 KiB</span>
    <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">_hexdig</span> <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="n">_hexdig</span><span class="p">}</span>
<span class="c1">#8: &lt;string&gt;:5: 62.0 KiB</span>
<span class="c1">#9: Lib/_weakrefset.py:37: 60.0 KiB</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">data</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
<span class="c1">#10: Lib/base64.py:142: 59.8 KiB</span>
    <span class="n">_b32tab2</span> <span class="o">=</span> <span class="p">[</span><span class="n">a</span> <span class="o">+</span> <span class="n">b</span> <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">_b32tab</span> <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="n">_b32tab</span><span class="p">]</span>
<span class="mi">6220</span> <span class="n">other</span><span class="p">:</span> <span class="mf">3602.8</span> <span class="n">KiB</span>
<span class="n">Total</span> <span class="n">allocated</span> <span class="n">size</span><span class="p">:</span> <span class="mf">5303.1</span> <span class="n">KiB</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="#tracemalloc.Snapshot.statistics" title="tracemalloc.Snapshot.statistics"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.statistics()</span></code></a> for more options.</p>
<section id="record-the-current-and-peak-size-of-all-traced-memory-blocks">
<h4>Record the current and peak size of all traced memory blocks<a class="headerlink" href="#record-the-current-and-peak-size-of-all-traced-memory-blocks" title="Link to this heading">¶</a></h4>
<p>The following code computes two sums like <code class="docutils literal notranslate"><span class="pre">0</span> <span class="pre">+</span> <span class="pre">1</span> <span class="pre">+</span> <span class="pre">2</span> <span class="pre">+</span> <span class="pre">...</span></code> inefficiently, by
creating a list of those numbers. This list consumes a lot of memory
temporarily. We can use <a class="reference internal" href="#tracemalloc.get_traced_memory" title="tracemalloc.get_traced_memory"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traced_memory()</span></code></a> and <a class="reference internal" href="#tracemalloc.reset_peak" title="tracemalloc.reset_peak"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_peak()</span></code></a> to
observe the small memory usage after the sum is computed as well as the peak
memory usage during the computations:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tracemalloc</span>

<span class="n">tracemalloc</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

<span class="c1"># Example code: compute a sum with a large temporary list</span>
<span class="n">large_sum</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">100000</span><span class="p">)))</span>

<span class="n">first_size</span><span class="p">,</span> <span class="n">first_peak</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">get_traced_memory</span><span class="p">()</span>

<span class="n">tracemalloc</span><span class="o">.</span><span class="n">reset_peak</span><span class="p">()</span>

<span class="c1"># Example code: compute a sum with a small temporary list</span>
<span class="n">small_sum</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">1000</span><span class="p">)))</span>

<span class="n">second_size</span><span class="p">,</span> <span class="n">second_peak</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">get_traced_memory</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">first_size</span><span class="si">=}</span><span class="s2">, </span><span class="si">{</span><span class="n">first_peak</span><span class="si">=}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">second_size</span><span class="si">=}</span><span class="s2">, </span><span class="si">{</span><span class="n">second_peak</span><span class="si">=}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Output:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">first_size</span><span class="o">=</span><span class="mi">664</span><span class="p">,</span> <span class="n">first_peak</span><span class="o">=</span><span class="mi">3592984</span>
<span class="n">second_size</span><span class="o">=</span><span class="mi">804</span><span class="p">,</span> <span class="n">second_peak</span><span class="o">=</span><span class="mi">29704</span>
</pre></div>
</div>
<p>Using <a class="reference internal" href="#tracemalloc.reset_peak" title="tracemalloc.reset_peak"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_peak()</span></code></a> ensured we could accurately record the peak during the
computation of <code class="docutils literal notranslate"><span class="pre">small_sum</span></code>, even though it is much smaller than the overall
peak size of memory blocks since the <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">start()</span></code></a> call. Without the call to
<a class="reference internal" href="#tracemalloc.reset_peak" title="tracemalloc.reset_peak"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_peak()</span></code></a>, <code class="docutils literal notranslate"><span class="pre">second_peak</span></code> would still be the peak from the
computation <code class="docutils literal notranslate"><span class="pre">large_sum</span></code> (that is, equal to <code class="docutils literal notranslate"><span class="pre">first_peak</span></code>). In this case,
both peaks are much higher than the final memory usage, and which suggests we
could optimise (by removing the unnecessary call to <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>, and writing
<code class="docutils literal notranslate"><span class="pre">sum(range(...))</span></code>).</p>
</section>
</section>
</section>
<section id="api">
<h2>API<a class="headerlink" href="#api" title="Link to this heading">¶</a></h2>
<section id="functions">
<h3>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.clear_traces">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">clear_traces</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.clear_traces" title="Link to this definition">¶</a></dt>
<dd><p>Clear traces of memory blocks allocated by Python.</p>
<p>See also <a class="reference internal" href="#tracemalloc.stop" title="tracemalloc.stop"><code class="xref py py-func docutils literal notranslate"><span class="pre">stop()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.get_object_traceback">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">get_object_traceback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.get_object_traceback" title="Link to this definition">¶</a></dt>
<dd><p>Get the traceback where the Python object <em>obj</em> was allocated.
Return a <a class="reference internal" href="#tracemalloc.Traceback" title="tracemalloc.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a> instance, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a>
module is not tracing memory allocations or did not trace the allocation of
the object.</p>
<p>See also <a class="reference internal" href="gc.html#gc.get_referrers" title="gc.get_referrers"><code class="xref py py-func docutils literal notranslate"><span class="pre">gc.get_referrers()</span></code></a> and <a class="reference internal" href="sys.html#sys.getsizeof" title="sys.getsizeof"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getsizeof()</span></code></a> functions.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.get_traceback_limit">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">get_traceback_limit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.get_traceback_limit" title="Link to this definition">¶</a></dt>
<dd><p>Get the maximum number of frames stored in the traceback of a trace.</p>
<p>The <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module must be tracing memory allocations to
get the limit, otherwise an exception is raised.</p>
<p>The limit is set by the <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">start()</span></code></a> function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.get_traced_memory">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">get_traced_memory</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.get_traced_memory" title="Link to this definition">¶</a></dt>
<dd><p>Get the current size and peak size of memory blocks traced by the
<a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module as a tuple: <code class="docutils literal notranslate"><span class="pre">(current:</span> <span class="pre">int,</span> <span class="pre">peak:</span> <span class="pre">int)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.reset_peak">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">reset_peak</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.reset_peak" title="Link to this definition">¶</a></dt>
<dd><p>Set the peak size of memory blocks traced by the <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module
to the current size.</p>
<p>Do nothing if the <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module is not tracing memory
allocations.</p>
<p>This function only modifies the recorded peak size, and does not modify or
clear any traces, unlike <a class="reference internal" href="#tracemalloc.clear_traces" title="tracemalloc.clear_traces"><code class="xref py py-func docutils literal notranslate"><span class="pre">clear_traces()</span></code></a>. Snapshots taken with
<a class="reference internal" href="#tracemalloc.take_snapshot" title="tracemalloc.take_snapshot"><code class="xref py py-func docutils literal notranslate"><span class="pre">take_snapshot()</span></code></a> before a call to <a class="reference internal" href="#tracemalloc.reset_peak" title="tracemalloc.reset_peak"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset_peak()</span></code></a> can be
meaningfully compared to snapshots taken after the call.</p>
<p>See also <a class="reference internal" href="#tracemalloc.get_traced_memory" title="tracemalloc.get_traced_memory"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traced_memory()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.get_tracemalloc_memory">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">get_tracemalloc_memory</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.get_tracemalloc_memory" title="Link to this definition">¶</a></dt>
<dd><p>Get the memory usage in bytes of the <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module used to store
traces of memory blocks.
Return an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.is_tracing">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">is_tracing</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.is_tracing" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module is tracing Python memory
allocations, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<p>See also <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">start()</span></code></a> and <a class="reference internal" href="#tracemalloc.stop" title="tracemalloc.stop"><code class="xref py py-func docutils literal notranslate"><span class="pre">stop()</span></code></a> functions.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.start">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">start</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nframe</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.start" title="Link to this definition">¶</a></dt>
<dd><p>Start tracing Python memory allocations: install hooks on Python memory
allocators. Collected tracebacks of traces will be limited to <em>nframe</em>
frames. By default, a trace of a memory block only stores the most recent
frame: the limit is <code class="docutils literal notranslate"><span class="pre">1</span></code>. <em>nframe</em> must be greater or equal to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<p>You can still read the original number of total frames that composed the
traceback by looking at the <a class="reference internal" href="#tracemalloc.Traceback.total_nframe" title="tracemalloc.Traceback.total_nframe"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Traceback.total_nframe</span></code></a> attribute.</p>
<p>Storing more than <code class="docutils literal notranslate"><span class="pre">1</span></code> frame is only useful to compute statistics grouped
by <code class="docutils literal notranslate"><span class="pre">'traceback'</span></code> or to compute cumulative statistics: see the
<a class="reference internal" href="#tracemalloc.Snapshot.compare_to" title="tracemalloc.Snapshot.compare_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.compare_to()</span></code></a> and <a class="reference internal" href="#tracemalloc.Snapshot.statistics" title="tracemalloc.Snapshot.statistics"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.statistics()</span></code></a> methods.</p>
<p>Storing more frames increases the memory and CPU overhead of the
<a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module. Use the <a class="reference internal" href="#tracemalloc.get_tracemalloc_memory" title="tracemalloc.get_tracemalloc_memory"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_tracemalloc_memory()</span></code></a> function
to measure how much memory is used by the <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module.</p>
<p>The <span class="target" id="index-2"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONTRACEMALLOC"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONTRACEMALLOC</span></code></a> environment variable
(<code class="docutils literal notranslate"><span class="pre">PYTHONTRACEMALLOC=NFRAME</span></code>) and the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> <code class="docutils literal notranslate"><span class="pre">tracemalloc=NFRAME</span></code>
command line option can be used to start tracing at startup.</p>
<p>See also <a class="reference internal" href="#tracemalloc.stop" title="tracemalloc.stop"><code class="xref py py-func docutils literal notranslate"><span class="pre">stop()</span></code></a>, <a class="reference internal" href="#tracemalloc.is_tracing" title="tracemalloc.is_tracing"><code class="xref py py-func docutils literal notranslate"><span class="pre">is_tracing()</span></code></a> and <a class="reference internal" href="#tracemalloc.get_traceback_limit" title="tracemalloc.get_traceback_limit"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traceback_limit()</span></code></a>
functions.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.stop">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.stop" title="Link to this definition">¶</a></dt>
<dd><p>Stop tracing Python memory allocations: uninstall hooks on Python memory
allocators. Also clears all previously collected traces of memory blocks
allocated by Python.</p>
<p>Call <a class="reference internal" href="#tracemalloc.take_snapshot" title="tracemalloc.take_snapshot"><code class="xref py py-func docutils literal notranslate"><span class="pre">take_snapshot()</span></code></a> function to take a snapshot of traces before
clearing them.</p>
<p>See also <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">start()</span></code></a>, <a class="reference internal" href="#tracemalloc.is_tracing" title="tracemalloc.is_tracing"><code class="xref py py-func docutils literal notranslate"><span class="pre">is_tracing()</span></code></a> and <a class="reference internal" href="#tracemalloc.clear_traces" title="tracemalloc.clear_traces"><code class="xref py py-func docutils literal notranslate"><span class="pre">clear_traces()</span></code></a>
functions.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tracemalloc.take_snapshot">
<span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">take_snapshot</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.take_snapshot" title="Link to this definition">¶</a></dt>
<dd><p>Take a snapshot of traces of memory blocks allocated by Python. Return a new
<a class="reference internal" href="#tracemalloc.Snapshot" title="tracemalloc.Snapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">Snapshot</span></code></a> instance.</p>
<p>The snapshot does not include memory blocks allocated before the
<a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module started to trace memory allocations.</p>
<p>Tracebacks of traces are limited to <a class="reference internal" href="#tracemalloc.get_traceback_limit" title="tracemalloc.get_traceback_limit"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traceback_limit()</span></code></a> frames. Use
the <em>nframe</em> parameter of the <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">start()</span></code></a> function to store more frames.</p>
<p>The <a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module must be tracing memory allocations to take a
snapshot, see the <a class="reference internal" href="#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">start()</span></code></a> function.</p>
<p>See also the <a class="reference internal" href="#tracemalloc.get_object_traceback" title="tracemalloc.get_object_traceback"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_object_traceback()</span></code></a> function.</p>
</dd></dl>

</section>
<section id="domainfilter">
<h3>DomainFilter<a class="headerlink" href="#domainfilter" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.DomainFilter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">DomainFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">inclusive</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#bool" title="bool"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.DomainFilter" title="Link to this definition">¶</a></dt>
<dd><p>Filter traces of memory blocks by their address space (domain).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.DomainFilter.inclusive">
<span class="sig-name descname"><span class="pre">inclusive</span></span><a class="headerlink" href="#tracemalloc.DomainFilter.inclusive" title="Link to this definition">¶</a></dt>
<dd><p>If <em>inclusive</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (include), match memory blocks allocated
in the address space <a class="reference internal" href="#tracemalloc.DomainFilter.domain" title="tracemalloc.DomainFilter.domain"><code class="xref py py-attr docutils literal notranslate"><span class="pre">domain</span></code></a>.</p>
<p>If <em>inclusive</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code> (exclude), match memory blocks not allocated
in the address space <a class="reference internal" href="#tracemalloc.DomainFilter.domain" title="tracemalloc.DomainFilter.domain"><code class="xref py py-attr docutils literal notranslate"><span class="pre">domain</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.DomainFilter.domain">
<span class="sig-name descname"><span class="pre">domain</span></span><a class="headerlink" href="#tracemalloc.DomainFilter.domain" title="Link to this definition">¶</a></dt>
<dd><p>Address space of a memory block (<code class="docutils literal notranslate"><span class="pre">int</span></code>). Read-only property.</p>
</dd></dl>

</dd></dl>

</section>
<section id="filter">
<h3>Filter<a class="headerlink" href="#filter" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.Filter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">Filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">inclusive</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#bool" title="bool"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename_pattern</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">all_frames</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#bool" title="bool"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Filter" title="Link to this definition">¶</a></dt>
<dd><p>Filter on traces of memory blocks.</p>
<p>See the <a class="reference internal" href="fnmatch.html#fnmatch.fnmatch" title="fnmatch.fnmatch"><code class="xref py py-func docutils literal notranslate"><span class="pre">fnmatch.fnmatch()</span></code></a> function for the syntax of
<em>filename_pattern</em>. The <code class="docutils literal notranslate"><span class="pre">'.pyc'</span></code> file extension is
replaced with <code class="docutils literal notranslate"><span class="pre">'.py'</span></code>.</p>
<p>Examples:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">Filter(True,</span> <span class="pre">subprocess.__file__)</span></code> only includes traces of the
<a class="reference internal" href="subprocess.html#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Filter(False,</span> <span class="pre">tracemalloc.__file__)</span></code> excludes traces of the
<a class="reference internal" href="#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> module</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Filter(False,</span> <span class="pre">&quot;&lt;unknown&gt;&quot;)</span></code> excludes empty tracebacks</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <code class="docutils literal notranslate"><span class="pre">'.pyo'</span></code> file extension is no longer replaced with <code class="docutils literal notranslate"><span class="pre">'.py'</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the <a class="reference internal" href="#tracemalloc.Filter.domain" title="tracemalloc.Filter.domain"><code class="xref py py-attr docutils literal notranslate"><span class="pre">domain</span></code></a> attribute.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Filter.domain">
<span class="sig-name descname"><span class="pre">domain</span></span><a class="headerlink" href="#tracemalloc.Filter.domain" title="Link to this definition">¶</a></dt>
<dd><p>Address space of a memory block (<code class="docutils literal notranslate"><span class="pre">int</span></code> or <code class="docutils literal notranslate"><span class="pre">None</span></code>).</p>
<p>tracemalloc uses the domain <code class="docutils literal notranslate"><span class="pre">0</span></code> to trace memory allocations made by
Python. C extensions can use other domains to trace other resources.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Filter.inclusive">
<span class="sig-name descname"><span class="pre">inclusive</span></span><a class="headerlink" href="#tracemalloc.Filter.inclusive" title="Link to this definition">¶</a></dt>
<dd><p>If <em>inclusive</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (include), only match memory blocks allocated
in a file with a name matching <a class="reference internal" href="#tracemalloc.Filter.filename_pattern" title="tracemalloc.Filter.filename_pattern"><code class="xref py py-attr docutils literal notranslate"><span class="pre">filename_pattern</span></code></a> at line number
<a class="reference internal" href="#tracemalloc.Filter.lineno" title="tracemalloc.Filter.lineno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">lineno</span></code></a>.</p>
<p>If <em>inclusive</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code> (exclude), ignore memory blocks allocated in
a file with a name matching <a class="reference internal" href="#tracemalloc.Filter.filename_pattern" title="tracemalloc.Filter.filename_pattern"><code class="xref py py-attr docutils literal notranslate"><span class="pre">filename_pattern</span></code></a> at line number
<a class="reference internal" href="#tracemalloc.Filter.lineno" title="tracemalloc.Filter.lineno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">lineno</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Filter.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#tracemalloc.Filter.lineno" title="Link to this definition">¶</a></dt>
<dd><p>Line number (<code class="docutils literal notranslate"><span class="pre">int</span></code>) of the filter. If <em>lineno</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the filter
matches any line number.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Filter.filename_pattern">
<span class="sig-name descname"><span class="pre">filename_pattern</span></span><a class="headerlink" href="#tracemalloc.Filter.filename_pattern" title="Link to this definition">¶</a></dt>
<dd><p>Filename pattern of the filter (<code class="docutils literal notranslate"><span class="pre">str</span></code>). Read-only property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Filter.all_frames">
<span class="sig-name descname"><span class="pre">all_frames</span></span><a class="headerlink" href="#tracemalloc.Filter.all_frames" title="Link to this definition">¶</a></dt>
<dd><p>If <em>all_frames</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, all frames of the traceback are checked. If
<em>all_frames</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, only the most recent frame is checked.</p>
<p>This attribute has no effect if the traceback limit is <code class="docutils literal notranslate"><span class="pre">1</span></code>.  See the
<a class="reference internal" href="#tracemalloc.get_traceback_limit" title="tracemalloc.get_traceback_limit"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traceback_limit()</span></code></a> function and <a class="reference internal" href="#tracemalloc.Snapshot.traceback_limit" title="tracemalloc.Snapshot.traceback_limit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Snapshot.traceback_limit</span></code></a>
attribute.</p>
</dd></dl>

</dd></dl>

</section>
<section id="frame">
<h3>Frame<a class="headerlink" href="#frame" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.Frame">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">Frame</span></span><a class="headerlink" href="#tracemalloc.Frame" title="Link to this definition">¶</a></dt>
<dd><p>Frame of a traceback.</p>
<p>The <a class="reference internal" href="#tracemalloc.Traceback" title="tracemalloc.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a> class is a sequence of <a class="reference internal" href="#tracemalloc.Frame" title="tracemalloc.Frame"><code class="xref py py-class docutils literal notranslate"><span class="pre">Frame</span></code></a> instances.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Frame.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#tracemalloc.Frame.filename" title="Link to this definition">¶</a></dt>
<dd><p>Filename (<code class="docutils literal notranslate"><span class="pre">str</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Frame.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#tracemalloc.Frame.lineno" title="Link to this definition">¶</a></dt>
<dd><p>Line number (<code class="docutils literal notranslate"><span class="pre">int</span></code>).</p>
</dd></dl>

</dd></dl>

</section>
<section id="snapshot">
<h3>Snapshot<a class="headerlink" href="#snapshot" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.Snapshot">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">Snapshot</span></span><a class="headerlink" href="#tracemalloc.Snapshot" title="Link to this definition">¶</a></dt>
<dd><p>Snapshot of traces of memory blocks allocated by Python.</p>
<p>The <a class="reference internal" href="#tracemalloc.take_snapshot" title="tracemalloc.take_snapshot"><code class="xref py py-func docutils literal notranslate"><span class="pre">take_snapshot()</span></code></a> function creates a snapshot instance.</p>
<dl class="py method">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.compare_to">
<span class="sig-name descname"><span class="pre">compare_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">old_snapshot</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#tracemalloc.Snapshot" title="tracemalloc.Snapshot"><span class="pre">Snapshot</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">cumulative</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#bool" title="bool"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Snapshot.compare_to" title="Link to this definition">¶</a></dt>
<dd><p>Compute the differences with an old snapshot. Get statistics as a sorted
list of <a class="reference internal" href="#tracemalloc.StatisticDiff" title="tracemalloc.StatisticDiff"><code class="xref py py-class docutils literal notranslate"><span class="pre">StatisticDiff</span></code></a> instances grouped by <em>key_type</em>.</p>
<p>See the <a class="reference internal" href="#tracemalloc.Snapshot.statistics" title="tracemalloc.Snapshot.statistics"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.statistics()</span></code></a> method for <em>key_type</em> and <em>cumulative</em>
parameters.</p>
<p>The result is sorted from the biggest to the smallest by: absolute value
of <a class="reference internal" href="#tracemalloc.StatisticDiff.size_diff" title="tracemalloc.StatisticDiff.size_diff"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StatisticDiff.size_diff</span></code></a>, <a class="reference internal" href="#tracemalloc.StatisticDiff.size" title="tracemalloc.StatisticDiff.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StatisticDiff.size</span></code></a>, absolute
value of <a class="reference internal" href="#tracemalloc.StatisticDiff.count_diff" title="tracemalloc.StatisticDiff.count_diff"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StatisticDiff.count_diff</span></code></a>, <a class="reference internal" href="#tracemalloc.Statistic.count" title="tracemalloc.Statistic.count"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Statistic.count</span></code></a> and
then by <a class="reference internal" href="#tracemalloc.StatisticDiff.traceback" title="tracemalloc.StatisticDiff.traceback"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StatisticDiff.traceback</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.dump">
<span class="sig-name descname"><span class="pre">dump</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Snapshot.dump" title="Link to this definition">¶</a></dt>
<dd><p>Write the snapshot into a file.</p>
<p>Use <a class="reference internal" href="#tracemalloc.Snapshot.load" title="tracemalloc.Snapshot.load"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load()</span></code></a> to reload the snapshot.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.filter_traces">
<span class="sig-name descname"><span class="pre">filter_traces</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filters</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Snapshot.filter_traces" title="Link to this definition">¶</a></dt>
<dd><p>Create a new <a class="reference internal" href="#tracemalloc.Snapshot" title="tracemalloc.Snapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">Snapshot</span></code></a> instance with a filtered <a class="reference internal" href="#tracemalloc.Snapshot.traces" title="tracemalloc.Snapshot.traces"><code class="xref py py-attr docutils literal notranslate"><span class="pre">traces</span></code></a>
sequence, <em>filters</em> is a list of <a class="reference internal" href="#tracemalloc.DomainFilter" title="tracemalloc.DomainFilter"><code class="xref py py-class docutils literal notranslate"><span class="pre">DomainFilter</span></code></a> and
<a class="reference internal" href="#tracemalloc.Filter" title="tracemalloc.Filter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Filter</span></code></a> instances.  If <em>filters</em> is an empty list, return a new
<a class="reference internal" href="#tracemalloc.Snapshot" title="tracemalloc.Snapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">Snapshot</span></code></a> instance with a copy of the traces.</p>
<p>All inclusive filters are applied at once, a trace is ignored if no
inclusive filters match it. A trace is ignored if at least one exclusive
filter matches it.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#tracemalloc.DomainFilter" title="tracemalloc.DomainFilter"><code class="xref py py-class docutils literal notranslate"><span class="pre">DomainFilter</span></code></a> instances are now also accepted in <em>filters</em>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.load">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">load</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Snapshot.load" title="Link to this definition">¶</a></dt>
<dd><p>Load a snapshot from a file.</p>
<p>See also <a class="reference internal" href="#tracemalloc.Snapshot.dump" title="tracemalloc.Snapshot.dump"><code class="xref py py-meth docutils literal notranslate"><span class="pre">dump()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.statistics">
<span class="sig-name descname"><span class="pre">statistics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">cumulative</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#bool" title="bool"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Snapshot.statistics" title="Link to this definition">¶</a></dt>
<dd><p>Get statistics as a sorted list of <a class="reference internal" href="#tracemalloc.Statistic" title="tracemalloc.Statistic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Statistic</span></code></a> instances grouped
by <em>key_type</em>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>key_type</p></th>
<th class="head"><p>description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'filename'</span></code></p></td>
<td><p>filename</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'lineno'</span></code></p></td>
<td><p>filename and line number</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'traceback'</span></code></p></td>
<td><p>traceback</p></td>
</tr>
</tbody>
</table>
<p>If <em>cumulative</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, cumulate size and count of memory blocks of
all frames of the traceback of a trace, not only the most recent frame.
The cumulative mode can only be used with <em>key_type</em> equals to
<code class="docutils literal notranslate"><span class="pre">'filename'</span></code> and <code class="docutils literal notranslate"><span class="pre">'lineno'</span></code>.</p>
<p>The result is sorted from the biggest to the smallest by:
<a class="reference internal" href="#tracemalloc.Statistic.size" title="tracemalloc.Statistic.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Statistic.size</span></code></a>, <a class="reference internal" href="#tracemalloc.Statistic.count" title="tracemalloc.Statistic.count"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Statistic.count</span></code></a> and then by
<a class="reference internal" href="#tracemalloc.Statistic.traceback" title="tracemalloc.Statistic.traceback"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Statistic.traceback</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.traceback_limit">
<span class="sig-name descname"><span class="pre">traceback_limit</span></span><a class="headerlink" href="#tracemalloc.Snapshot.traceback_limit" title="Link to this definition">¶</a></dt>
<dd><p>Maximum number of frames stored in the traceback of <a class="reference internal" href="#tracemalloc.Snapshot.traces" title="tracemalloc.Snapshot.traces"><code class="xref py py-attr docutils literal notranslate"><span class="pre">traces</span></code></a>:
result of the <a class="reference internal" href="#tracemalloc.get_traceback_limit" title="tracemalloc.get_traceback_limit"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traceback_limit()</span></code></a> when the snapshot was taken.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Snapshot.traces">
<span class="sig-name descname"><span class="pre">traces</span></span><a class="headerlink" href="#tracemalloc.Snapshot.traces" title="Link to this definition">¶</a></dt>
<dd><p>Traces of all memory blocks allocated by Python: sequence of
<a class="reference internal" href="#tracemalloc.Trace" title="tracemalloc.Trace"><code class="xref py py-class docutils literal notranslate"><span class="pre">Trace</span></code></a> instances.</p>
<p>The sequence has an undefined order. Use the <a class="reference internal" href="#tracemalloc.Snapshot.statistics" title="tracemalloc.Snapshot.statistics"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Snapshot.statistics()</span></code></a>
method to get a sorted list of statistics.</p>
</dd></dl>

</dd></dl>

</section>
<section id="statistic">
<h3>Statistic<a class="headerlink" href="#statistic" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.Statistic">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">Statistic</span></span><a class="headerlink" href="#tracemalloc.Statistic" title="Link to this definition">¶</a></dt>
<dd><p>Statistic on memory allocations.</p>
<p><a class="reference internal" href="#tracemalloc.Snapshot.statistics" title="tracemalloc.Snapshot.statistics"><code class="xref py py-func docutils literal notranslate"><span class="pre">Snapshot.statistics()</span></code></a> returns a list of <a class="reference internal" href="#tracemalloc.Statistic" title="tracemalloc.Statistic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Statistic</span></code></a> instances.</p>
<p>See also the <a class="reference internal" href="#tracemalloc.StatisticDiff" title="tracemalloc.StatisticDiff"><code class="xref py py-class docutils literal notranslate"><span class="pre">StatisticDiff</span></code></a> class.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Statistic.count">
<span class="sig-name descname"><span class="pre">count</span></span><a class="headerlink" href="#tracemalloc.Statistic.count" title="Link to this definition">¶</a></dt>
<dd><p>Number of memory blocks (<code class="docutils literal notranslate"><span class="pre">int</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Statistic.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#tracemalloc.Statistic.size" title="Link to this definition">¶</a></dt>
<dd><p>Total size of memory blocks in bytes (<code class="docutils literal notranslate"><span class="pre">int</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Statistic.traceback">
<span class="sig-name descname"><span class="pre">traceback</span></span><a class="headerlink" href="#tracemalloc.Statistic.traceback" title="Link to this definition">¶</a></dt>
<dd><p>Traceback where the memory block was allocated, <a class="reference internal" href="#tracemalloc.Traceback" title="tracemalloc.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a>
instance.</p>
</dd></dl>

</dd></dl>

</section>
<section id="statisticdiff">
<h3>StatisticDiff<a class="headerlink" href="#statisticdiff" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.StatisticDiff">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">StatisticDiff</span></span><a class="headerlink" href="#tracemalloc.StatisticDiff" title="Link to this definition">¶</a></dt>
<dd><p>Statistic difference on memory allocations between an old and a new
<a class="reference internal" href="#tracemalloc.Snapshot" title="tracemalloc.Snapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">Snapshot</span></code></a> instance.</p>
<p><a class="reference internal" href="#tracemalloc.Snapshot.compare_to" title="tracemalloc.Snapshot.compare_to"><code class="xref py py-func docutils literal notranslate"><span class="pre">Snapshot.compare_to()</span></code></a> returns a list of <a class="reference internal" href="#tracemalloc.StatisticDiff" title="tracemalloc.StatisticDiff"><code class="xref py py-class docutils literal notranslate"><span class="pre">StatisticDiff</span></code></a>
instances. See also the <a class="reference internal" href="#tracemalloc.Statistic" title="tracemalloc.Statistic"><code class="xref py py-class docutils literal notranslate"><span class="pre">Statistic</span></code></a> class.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.StatisticDiff.count">
<span class="sig-name descname"><span class="pre">count</span></span><a class="headerlink" href="#tracemalloc.StatisticDiff.count" title="Link to this definition">¶</a></dt>
<dd><p>Number of memory blocks in the new snapshot (<code class="docutils literal notranslate"><span class="pre">int</span></code>): <code class="docutils literal notranslate"><span class="pre">0</span></code> if
the memory blocks have been released in the new snapshot.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.StatisticDiff.count_diff">
<span class="sig-name descname"><span class="pre">count_diff</span></span><a class="headerlink" href="#tracemalloc.StatisticDiff.count_diff" title="Link to this definition">¶</a></dt>
<dd><p>Difference of number of memory blocks between the old and the new
snapshots (<code class="docutils literal notranslate"><span class="pre">int</span></code>): <code class="docutils literal notranslate"><span class="pre">0</span></code> if the memory blocks have been allocated in
the new snapshot.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.StatisticDiff.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#tracemalloc.StatisticDiff.size" title="Link to this definition">¶</a></dt>
<dd><p>Total size of memory blocks in bytes in the new snapshot (<code class="docutils literal notranslate"><span class="pre">int</span></code>):
<code class="docutils literal notranslate"><span class="pre">0</span></code> if the memory blocks have been released in the new snapshot.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.StatisticDiff.size_diff">
<span class="sig-name descname"><span class="pre">size_diff</span></span><a class="headerlink" href="#tracemalloc.StatisticDiff.size_diff" title="Link to this definition">¶</a></dt>
<dd><p>Difference of total size of memory blocks in bytes between the old and
the new snapshots (<code class="docutils literal notranslate"><span class="pre">int</span></code>): <code class="docutils literal notranslate"><span class="pre">0</span></code> if the memory blocks have been
allocated in the new snapshot.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.StatisticDiff.traceback">
<span class="sig-name descname"><span class="pre">traceback</span></span><a class="headerlink" href="#tracemalloc.StatisticDiff.traceback" title="Link to this definition">¶</a></dt>
<dd><p>Traceback where the memory blocks were allocated, <a class="reference internal" href="#tracemalloc.Traceback" title="tracemalloc.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a>
instance.</p>
</dd></dl>

</dd></dl>

</section>
<section id="trace">
<h3>Trace<a class="headerlink" href="#trace" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.Trace">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">Trace</span></span><a class="headerlink" href="#tracemalloc.Trace" title="Link to this definition">¶</a></dt>
<dd><p>Trace of a memory block.</p>
<p>The <a class="reference internal" href="#tracemalloc.Snapshot.traces" title="tracemalloc.Snapshot.traces"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Snapshot.traces</span></code></a> attribute is a sequence of <a class="reference internal" href="#tracemalloc.Trace" title="tracemalloc.Trace"><code class="xref py py-class docutils literal notranslate"><span class="pre">Trace</span></code></a>
instances.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the <a class="reference internal" href="#tracemalloc.Trace.domain" title="tracemalloc.Trace.domain"><code class="xref py py-attr docutils literal notranslate"><span class="pre">domain</span></code></a> attribute.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Trace.domain">
<span class="sig-name descname"><span class="pre">domain</span></span><a class="headerlink" href="#tracemalloc.Trace.domain" title="Link to this definition">¶</a></dt>
<dd><p>Address space of a memory block (<code class="docutils literal notranslate"><span class="pre">int</span></code>). Read-only property.</p>
<p>tracemalloc uses the domain <code class="docutils literal notranslate"><span class="pre">0</span></code> to trace memory allocations made by
Python. C extensions can use other domains to trace other resources.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Trace.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#tracemalloc.Trace.size" title="Link to this definition">¶</a></dt>
<dd><p>Size of the memory block in bytes (<code class="docutils literal notranslate"><span class="pre">int</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Trace.traceback">
<span class="sig-name descname"><span class="pre">traceback</span></span><a class="headerlink" href="#tracemalloc.Trace.traceback" title="Link to this definition">¶</a></dt>
<dd><p>Traceback where the memory block was allocated, <a class="reference internal" href="#tracemalloc.Traceback" title="tracemalloc.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a>
instance.</p>
</dd></dl>

</dd></dl>

</section>
<section id="traceback">
<h3>Traceback<a class="headerlink" href="#traceback" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tracemalloc.Traceback">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tracemalloc.</span></span><span class="sig-name descname"><span class="pre">Traceback</span></span><a class="headerlink" href="#tracemalloc.Traceback" title="Link to this definition">¶</a></dt>
<dd><p>Sequence of <a class="reference internal" href="#tracemalloc.Frame" title="tracemalloc.Frame"><code class="xref py py-class docutils literal notranslate"><span class="pre">Frame</span></code></a> instances sorted from the oldest frame to the
most recent frame.</p>
<p>A traceback contains at least <code class="docutils literal notranslate"><span class="pre">1</span></code> frame. If the <code class="docutils literal notranslate"><span class="pre">tracemalloc</span></code> module
failed to get a frame, the filename <code class="docutils literal notranslate"><span class="pre">&quot;&lt;unknown&gt;&quot;</span></code> at line number <code class="docutils literal notranslate"><span class="pre">0</span></code> is
used.</p>
<p>When a snapshot is taken, tracebacks of traces are limited to
<a class="reference internal" href="#tracemalloc.get_traceback_limit" title="tracemalloc.get_traceback_limit"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_traceback_limit()</span></code></a> frames. See the <a class="reference internal" href="#tracemalloc.take_snapshot" title="tracemalloc.take_snapshot"><code class="xref py py-func docutils literal notranslate"><span class="pre">take_snapshot()</span></code></a> function.
The original number of frames of the traceback is stored in the
<a class="reference internal" href="#tracemalloc.Traceback.total_nframe" title="tracemalloc.Traceback.total_nframe"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Traceback.total_nframe</span></code></a> attribute. That allows to know if a traceback
has been truncated by the traceback limit.</p>
<p>The <a class="reference internal" href="#tracemalloc.Trace.traceback" title="tracemalloc.Trace.traceback"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Trace.traceback</span></code></a> attribute is an instance of <a class="reference internal" href="#tracemalloc.Traceback" title="tracemalloc.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a>
instance.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Frames are now sorted from the oldest to the most recent, instead of most recent to oldest.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="tracemalloc.Traceback.total_nframe">
<span class="sig-name descname"><span class="pre">total_nframe</span></span><a class="headerlink" href="#tracemalloc.Traceback.total_nframe" title="Link to this definition">¶</a></dt>
<dd><p>Total number of frames that composed the traceback before truncation.
This attribute can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> if the information is not
available.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <a class="reference internal" href="#tracemalloc.Traceback.total_nframe" title="tracemalloc.Traceback.total_nframe"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Traceback.total_nframe</span></code></a> attribute was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="tracemalloc.Traceback.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">most_recent_first</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tracemalloc.Traceback.format" title="Link to this definition">¶</a></dt>
<dd><p>Format the traceback as a list of lines. Use the <a class="reference internal" href="linecache.html#module-linecache" title="linecache: Provides random access to individual lines from text files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">linecache</span></code></a> module to
retrieve lines from the source code. If <em>limit</em> is set, format the <em>limit</em>
most recent frames if <em>limit</em> is positive. Otherwise, format the
<code class="docutils literal notranslate"><span class="pre">abs(limit)</span></code> oldest frames. If <em>most_recent_first</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the order
of the formatted frames is reversed, returning the most recent frame first
instead of last.</p>
<p>Similar to the <a class="reference internal" href="traceback.html#traceback.format_tb" title="traceback.format_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">traceback.format_tb()</span></code></a> function, except that
<a class="reference internal" href="#tracemalloc.Traceback.format" title="tracemalloc.Traceback.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> does not include newlines.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Traceback (most recent call first):&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">traceback</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</pre></div>
</div>
<p>Output:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Traceback</span> <span class="p">(</span><span class="n">most</span> <span class="n">recent</span> <span class="n">call</span> <span class="n">first</span><span class="p">):</span>
  <span class="n">File</span> <span class="s2">&quot;test.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">9</span>
    <span class="n">obj</span> <span class="o">=</span> <span class="n">Object</span><span class="p">()</span>
  <span class="n">File</span> <span class="s2">&quot;test.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">12</span>
    <span class="n">tb</span> <span class="o">=</span> <span class="n">tracemalloc</span><span class="o">.</span><span class="n">get_object_traceback</span><span class="p">(</span><span class="n">f</span><span class="p">())</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code> — Trace memory allocations</a><ul>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#display-the-top-10">Display the top 10</a></li>
<li><a class="reference internal" href="#compute-differences">Compute differences</a></li>
<li><a class="reference internal" href="#get-the-traceback-of-a-memory-block">Get the traceback of a memory block</a></li>
<li><a class="reference internal" href="#pretty-top">Pretty top</a><ul>
<li><a class="reference internal" href="#record-the-current-and-peak-size-of-all-traced-memory-blocks">Record the current and peak size of all traced memory blocks</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#api">API</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#domainfilter">DomainFilter</a></li>
<li><a class="reference internal" href="#filter">Filter</a></li>
<li><a class="reference internal" href="#frame">Frame</a></li>
<li><a class="reference internal" href="#snapshot">Snapshot</a></li>
<li><a class="reference internal" href="#statistic">Statistic</a></li>
<li><a class="reference internal" href="#statisticdiff">StatisticDiff</a></li>
<li><a class="reference internal" href="#trace">Trace</a></li>
<li><a class="reference internal" href="#traceback">Traceback</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="trace.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code> — Trace or track Python statement execution</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="distribution.html"
                          title="next chapter">Software Packaging and Distribution</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tracemalloc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="distribution.html" title="Software Packaging and Distribution"
             >next</a> |</li>
        <li class="right" >
          <a href="trace.html" title="trace — Trace or track Python statement execution"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="debug.html" >Debugging and Profiling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code> — Trace memory allocations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>