<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="errno — Standard errno system symbols" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/errno.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module makes available standard errno system symbols. The value of each symbol is the corresponding integer value. The names and descriptions are borrowed from linux/include/errno.h, which sho..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module makes available standard errno system symbols. The value of each symbol is the corresponding integer value. The names and descriptions are borrowed from linux/include/errno.h, which sho..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>errno — Standard errno system symbols &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="ctypes — A foreign function library for Python" href="ctypes.html" />
    <link rel="prev" title="platform — Access to underlying platform’s identifying data" href="platform.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/errno.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="platform.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ctypes.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ctypes</span></code> — A foreign function library for Python</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/errno.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ctypes.html" title="ctypes — A foreign function library for Python"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="platform.html" title="platform — Access to underlying platform’s identifying data"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" accesskey="U">Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code> — Standard errno system symbols</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-errno">
<span id="errno-standard-errno-system-symbols"></span><h1><a class="reference internal" href="#module-errno" title="errno: Standard errno system symbols."><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code></a> — Standard errno system symbols<a class="headerlink" href="#module-errno" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module makes available standard <code class="docutils literal notranslate"><span class="pre">errno</span></code> system symbols. The value of each
symbol is the corresponding integer value. The names and descriptions are
borrowed from <code class="file docutils literal notranslate"><span class="pre">linux/include/errno.h</span></code>, which should be
all-inclusive.</p>
<dl class="py data">
<dt class="sig sig-object py" id="errno.errorcode">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">errorcode</span></span><a class="headerlink" href="#errno.errorcode" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary providing a mapping from the errno value to the string name in the
underlying system.  For instance, <code class="docutils literal notranslate"><span class="pre">errno.errorcode[errno.EPERM]</span></code> maps to
<code class="docutils literal notranslate"><span class="pre">'EPERM'</span></code>.</p>
</dd></dl>

<p>To translate a numeric error code to an error message, use <a class="reference internal" href="os.html#os.strerror" title="os.strerror"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.strerror()</span></code></a>.</p>
<p>Of the following list, symbols that are not used on the current platform are not
defined by the module.  The specific list of defined symbols is available as
<code class="docutils literal notranslate"><span class="pre">errno.errorcode.keys()</span></code>.  Symbols available can include:</p>
<dl class="py data">
<dt class="sig sig-object py" id="errno.EPERM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EPERM</span></span><a class="headerlink" href="#errno.EPERM" title="Link to this definition">¶</a></dt>
<dd><p>Operation not permitted. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOENT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOENT</span></span><a class="headerlink" href="#errno.ENOENT" title="Link to this definition">¶</a></dt>
<dd><p>No such file or directory. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESRCH">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESRCH</span></span><a class="headerlink" href="#errno.ESRCH" title="Link to this definition">¶</a></dt>
<dd><p>No such process. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#ProcessLookupError" title="ProcessLookupError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProcessLookupError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EINTR">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EINTR</span></span><a class="headerlink" href="#errno.EINTR" title="Link to this definition">¶</a></dt>
<dd><p>Interrupted system call. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EIO">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EIO</span></span><a class="headerlink" href="#errno.EIO" title="Link to this definition">¶</a></dt>
<dd><p>I/O error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENXIO">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENXIO</span></span><a class="headerlink" href="#errno.ENXIO" title="Link to this definition">¶</a></dt>
<dd><p>No such device or address</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.E2BIG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">E2BIG</span></span><a class="headerlink" href="#errno.E2BIG" title="Link to this definition">¶</a></dt>
<dd><p>Arg list too long</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOEXEC">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOEXEC</span></span><a class="headerlink" href="#errno.ENOEXEC" title="Link to this definition">¶</a></dt>
<dd><p>Exec format error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADF">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADF</span></span><a class="headerlink" href="#errno.EBADF" title="Link to this definition">¶</a></dt>
<dd><p>Bad file number</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECHILD">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECHILD</span></span><a class="headerlink" href="#errno.ECHILD" title="Link to this definition">¶</a></dt>
<dd><p>No child processes. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#ChildProcessError" title="ChildProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ChildProcessError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EAGAIN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EAGAIN</span></span><a class="headerlink" href="#errno.EAGAIN" title="Link to this definition">¶</a></dt>
<dd><p>Try again. This error is mapped to the exception <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOMEM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOMEM</span></span><a class="headerlink" href="#errno.ENOMEM" title="Link to this definition">¶</a></dt>
<dd><p>Out of memory</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EACCES">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EACCES</span></span><a class="headerlink" href="#errno.EACCES" title="Link to this definition">¶</a></dt>
<dd><p>Permission denied.  This error is mapped to the exception
<a class="reference internal" href="exceptions.html#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EFAULT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EFAULT</span></span><a class="headerlink" href="#errno.EFAULT" title="Link to this definition">¶</a></dt>
<dd><p>Bad address</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTBLK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTBLK</span></span><a class="headerlink" href="#errno.ENOTBLK" title="Link to this definition">¶</a></dt>
<dd><p>Block device required</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBUSY">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBUSY</span></span><a class="headerlink" href="#errno.EBUSY" title="Link to this definition">¶</a></dt>
<dd><p>Device or resource busy</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EEXIST">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EEXIST</span></span><a class="headerlink" href="#errno.EEXIST" title="Link to this definition">¶</a></dt>
<dd><p>File exists. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EXDEV">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EXDEV</span></span><a class="headerlink" href="#errno.EXDEV" title="Link to this definition">¶</a></dt>
<dd><p>Cross-device link</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENODEV">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENODEV</span></span><a class="headerlink" href="#errno.ENODEV" title="Link to this definition">¶</a></dt>
<dd><p>No such device</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTDIR">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTDIR</span></span><a class="headerlink" href="#errno.ENOTDIR" title="Link to this definition">¶</a></dt>
<dd><p>Not a directory. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#NotADirectoryError" title="NotADirectoryError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotADirectoryError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EISDIR">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EISDIR</span></span><a class="headerlink" href="#errno.EISDIR" title="Link to this definition">¶</a></dt>
<dd><p>Is a directory. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#IsADirectoryError" title="IsADirectoryError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IsADirectoryError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EINVAL">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EINVAL</span></span><a class="headerlink" href="#errno.EINVAL" title="Link to this definition">¶</a></dt>
<dd><p>Invalid argument</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENFILE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENFILE</span></span><a class="headerlink" href="#errno.ENFILE" title="Link to this definition">¶</a></dt>
<dd><p>File table overflow</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EMFILE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EMFILE</span></span><a class="headerlink" href="#errno.EMFILE" title="Link to this definition">¶</a></dt>
<dd><p>Too many open files</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTTY">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTTY</span></span><a class="headerlink" href="#errno.ENOTTY" title="Link to this definition">¶</a></dt>
<dd><p>Not a typewriter</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ETXTBSY">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ETXTBSY</span></span><a class="headerlink" href="#errno.ETXTBSY" title="Link to this definition">¶</a></dt>
<dd><p>Text file busy</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EFBIG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EFBIG</span></span><a class="headerlink" href="#errno.EFBIG" title="Link to this definition">¶</a></dt>
<dd><p>File too large</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOSPC">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOSPC</span></span><a class="headerlink" href="#errno.ENOSPC" title="Link to this definition">¶</a></dt>
<dd><p>No space left on device</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESPIPE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESPIPE</span></span><a class="headerlink" href="#errno.ESPIPE" title="Link to this definition">¶</a></dt>
<dd><p>Illegal seek</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EROFS">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EROFS</span></span><a class="headerlink" href="#errno.EROFS" title="Link to this definition">¶</a></dt>
<dd><p>Read-only file system</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EMLINK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EMLINK</span></span><a class="headerlink" href="#errno.EMLINK" title="Link to this definition">¶</a></dt>
<dd><p>Too many links</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EPIPE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EPIPE</span></span><a class="headerlink" href="#errno.EPIPE" title="Link to this definition">¶</a></dt>
<dd><p>Broken pipe. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#BrokenPipeError" title="BrokenPipeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BrokenPipeError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EDOM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EDOM</span></span><a class="headerlink" href="#errno.EDOM" title="Link to this definition">¶</a></dt>
<dd><p>Math argument out of domain of func</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ERANGE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ERANGE</span></span><a class="headerlink" href="#errno.ERANGE" title="Link to this definition">¶</a></dt>
<dd><p>Math result not representable</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EDEADLK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EDEADLK</span></span><a class="headerlink" href="#errno.EDEADLK" title="Link to this definition">¶</a></dt>
<dd><p>Resource deadlock would occur</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENAMETOOLONG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENAMETOOLONG</span></span><a class="headerlink" href="#errno.ENAMETOOLONG" title="Link to this definition">¶</a></dt>
<dd><p>File name too long</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOLCK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOLCK</span></span><a class="headerlink" href="#errno.ENOLCK" title="Link to this definition">¶</a></dt>
<dd><p>No record locks available</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOSYS">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOSYS</span></span><a class="headerlink" href="#errno.ENOSYS" title="Link to this definition">¶</a></dt>
<dd><p>Function not implemented</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTEMPTY">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTEMPTY</span></span><a class="headerlink" href="#errno.ENOTEMPTY" title="Link to this definition">¶</a></dt>
<dd><p>Directory not empty</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELOOP">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELOOP</span></span><a class="headerlink" href="#errno.ELOOP" title="Link to this definition">¶</a></dt>
<dd><p>Too many symbolic links encountered</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EWOULDBLOCK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EWOULDBLOCK</span></span><a class="headerlink" href="#errno.EWOULDBLOCK" title="Link to this definition">¶</a></dt>
<dd><p>Operation would block. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOMSG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOMSG</span></span><a class="headerlink" href="#errno.ENOMSG" title="Link to this definition">¶</a></dt>
<dd><p>No message of desired type</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EIDRM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EIDRM</span></span><a class="headerlink" href="#errno.EIDRM" title="Link to this definition">¶</a></dt>
<dd><p>Identifier removed</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECHRNG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECHRNG</span></span><a class="headerlink" href="#errno.ECHRNG" title="Link to this definition">¶</a></dt>
<dd><p>Channel number out of range</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EL2NSYNC">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EL2NSYNC</span></span><a class="headerlink" href="#errno.EL2NSYNC" title="Link to this definition">¶</a></dt>
<dd><p>Level 2 not synchronized</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EL3HLT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EL3HLT</span></span><a class="headerlink" href="#errno.EL3HLT" title="Link to this definition">¶</a></dt>
<dd><p>Level 3 halted</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EL3RST">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EL3RST</span></span><a class="headerlink" href="#errno.EL3RST" title="Link to this definition">¶</a></dt>
<dd><p>Level 3 reset</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELNRNG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELNRNG</span></span><a class="headerlink" href="#errno.ELNRNG" title="Link to this definition">¶</a></dt>
<dd><p>Link number out of range</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EUNATCH">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EUNATCH</span></span><a class="headerlink" href="#errno.EUNATCH" title="Link to this definition">¶</a></dt>
<dd><p>Protocol driver not attached</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOCSI">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOCSI</span></span><a class="headerlink" href="#errno.ENOCSI" title="Link to this definition">¶</a></dt>
<dd><p>No CSI structure available</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EL2HLT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EL2HLT</span></span><a class="headerlink" href="#errno.EL2HLT" title="Link to this definition">¶</a></dt>
<dd><p>Level 2 halted</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADE</span></span><a class="headerlink" href="#errno.EBADE" title="Link to this definition">¶</a></dt>
<dd><p>Invalid exchange</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADR">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADR</span></span><a class="headerlink" href="#errno.EBADR" title="Link to this definition">¶</a></dt>
<dd><p>Invalid request descriptor</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EXFULL">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EXFULL</span></span><a class="headerlink" href="#errno.EXFULL" title="Link to this definition">¶</a></dt>
<dd><p>Exchange full</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOANO">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOANO</span></span><a class="headerlink" href="#errno.ENOANO" title="Link to this definition">¶</a></dt>
<dd><p>No anode</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADRQC">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADRQC</span></span><a class="headerlink" href="#errno.EBADRQC" title="Link to this definition">¶</a></dt>
<dd><p>Invalid request code</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADSLT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADSLT</span></span><a class="headerlink" href="#errno.EBADSLT" title="Link to this definition">¶</a></dt>
<dd><p>Invalid slot</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EDEADLOCK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EDEADLOCK</span></span><a class="headerlink" href="#errno.EDEADLOCK" title="Link to this definition">¶</a></dt>
<dd><p>File locking deadlock error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBFONT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBFONT</span></span><a class="headerlink" href="#errno.EBFONT" title="Link to this definition">¶</a></dt>
<dd><p>Bad font file format</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOSTR">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOSTR</span></span><a class="headerlink" href="#errno.ENOSTR" title="Link to this definition">¶</a></dt>
<dd><p>Device not a stream</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENODATA">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENODATA</span></span><a class="headerlink" href="#errno.ENODATA" title="Link to this definition">¶</a></dt>
<dd><p>No data available</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ETIME">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ETIME</span></span><a class="headerlink" href="#errno.ETIME" title="Link to this definition">¶</a></dt>
<dd><p>Timer expired</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOSR">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOSR</span></span><a class="headerlink" href="#errno.ENOSR" title="Link to this definition">¶</a></dt>
<dd><p>Out of streams resources</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENONET">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENONET</span></span><a class="headerlink" href="#errno.ENONET" title="Link to this definition">¶</a></dt>
<dd><p>Machine is not on the network</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOPKG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOPKG</span></span><a class="headerlink" href="#errno.ENOPKG" title="Link to this definition">¶</a></dt>
<dd><p>Package not installed</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EREMOTE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EREMOTE</span></span><a class="headerlink" href="#errno.EREMOTE" title="Link to this definition">¶</a></dt>
<dd><p>Object is remote</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOLINK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOLINK</span></span><a class="headerlink" href="#errno.ENOLINK" title="Link to this definition">¶</a></dt>
<dd><p>Link has been severed</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EADV">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EADV</span></span><a class="headerlink" href="#errno.EADV" title="Link to this definition">¶</a></dt>
<dd><p>Advertise error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESRMNT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESRMNT</span></span><a class="headerlink" href="#errno.ESRMNT" title="Link to this definition">¶</a></dt>
<dd><p>Srmount error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECOMM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECOMM</span></span><a class="headerlink" href="#errno.ECOMM" title="Link to this definition">¶</a></dt>
<dd><p>Communication error on send</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EPROTO">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EPROTO</span></span><a class="headerlink" href="#errno.EPROTO" title="Link to this definition">¶</a></dt>
<dd><p>Protocol error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EMULTIHOP">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EMULTIHOP</span></span><a class="headerlink" href="#errno.EMULTIHOP" title="Link to this definition">¶</a></dt>
<dd><p>Multihop attempted</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EDOTDOT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EDOTDOT</span></span><a class="headerlink" href="#errno.EDOTDOT" title="Link to this definition">¶</a></dt>
<dd><p>RFS specific error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADMSG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADMSG</span></span><a class="headerlink" href="#errno.EBADMSG" title="Link to this definition">¶</a></dt>
<dd><p>Not a data message</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EOVERFLOW">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EOVERFLOW</span></span><a class="headerlink" href="#errno.EOVERFLOW" title="Link to this definition">¶</a></dt>
<dd><p>Value too large for defined data type</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTUNIQ">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTUNIQ</span></span><a class="headerlink" href="#errno.ENOTUNIQ" title="Link to this definition">¶</a></dt>
<dd><p>Name not unique on network</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EBADFD">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EBADFD</span></span><a class="headerlink" href="#errno.EBADFD" title="Link to this definition">¶</a></dt>
<dd><p>File descriptor in bad state</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EREMCHG">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EREMCHG</span></span><a class="headerlink" href="#errno.EREMCHG" title="Link to this definition">¶</a></dt>
<dd><p>Remote address changed</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELIBACC">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELIBACC</span></span><a class="headerlink" href="#errno.ELIBACC" title="Link to this definition">¶</a></dt>
<dd><p>Can not access a needed shared library</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELIBBAD">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELIBBAD</span></span><a class="headerlink" href="#errno.ELIBBAD" title="Link to this definition">¶</a></dt>
<dd><p>Accessing a corrupted shared library</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELIBSCN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELIBSCN</span></span><a class="headerlink" href="#errno.ELIBSCN" title="Link to this definition">¶</a></dt>
<dd><p>.lib section in a.out corrupted</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELIBMAX">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELIBMAX</span></span><a class="headerlink" href="#errno.ELIBMAX" title="Link to this definition">¶</a></dt>
<dd><p>Attempting to link in too many shared libraries</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ELIBEXEC">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ELIBEXEC</span></span><a class="headerlink" href="#errno.ELIBEXEC" title="Link to this definition">¶</a></dt>
<dd><p>Cannot exec a shared library directly</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EILSEQ">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EILSEQ</span></span><a class="headerlink" href="#errno.EILSEQ" title="Link to this definition">¶</a></dt>
<dd><p>Illegal byte sequence</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ERESTART">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ERESTART</span></span><a class="headerlink" href="#errno.ERESTART" title="Link to this definition">¶</a></dt>
<dd><p>Interrupted system call should be restarted</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESTRPIPE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESTRPIPE</span></span><a class="headerlink" href="#errno.ESTRPIPE" title="Link to this definition">¶</a></dt>
<dd><p>Streams pipe error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EUSERS">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EUSERS</span></span><a class="headerlink" href="#errno.EUSERS" title="Link to this definition">¶</a></dt>
<dd><p>Too many users</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTSOCK">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTSOCK</span></span><a class="headerlink" href="#errno.ENOTSOCK" title="Link to this definition">¶</a></dt>
<dd><p>Socket operation on non-socket</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EDESTADDRREQ">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EDESTADDRREQ</span></span><a class="headerlink" href="#errno.EDESTADDRREQ" title="Link to this definition">¶</a></dt>
<dd><p>Destination address required</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EMSGSIZE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EMSGSIZE</span></span><a class="headerlink" href="#errno.EMSGSIZE" title="Link to this definition">¶</a></dt>
<dd><p>Message too long</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EPROTOTYPE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EPROTOTYPE</span></span><a class="headerlink" href="#errno.EPROTOTYPE" title="Link to this definition">¶</a></dt>
<dd><p>Protocol wrong type for socket</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOPROTOOPT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOPROTOOPT</span></span><a class="headerlink" href="#errno.ENOPROTOOPT" title="Link to this definition">¶</a></dt>
<dd><p>Protocol not available</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EPROTONOSUPPORT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EPROTONOSUPPORT</span></span><a class="headerlink" href="#errno.EPROTONOSUPPORT" title="Link to this definition">¶</a></dt>
<dd><p>Protocol not supported</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESOCKTNOSUPPORT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESOCKTNOSUPPORT</span></span><a class="headerlink" href="#errno.ESOCKTNOSUPPORT" title="Link to this definition">¶</a></dt>
<dd><p>Socket type not supported</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EOPNOTSUPP">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EOPNOTSUPP</span></span><a class="headerlink" href="#errno.EOPNOTSUPP" title="Link to this definition">¶</a></dt>
<dd><p>Operation not supported on transport endpoint</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTSUP">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTSUP</span></span><a class="headerlink" href="#errno.ENOTSUP" title="Link to this definition">¶</a></dt>
<dd><p>Operation not supported</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EPFNOSUPPORT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EPFNOSUPPORT</span></span><a class="headerlink" href="#errno.EPFNOSUPPORT" title="Link to this definition">¶</a></dt>
<dd><p>Protocol family not supported</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EAFNOSUPPORT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EAFNOSUPPORT</span></span><a class="headerlink" href="#errno.EAFNOSUPPORT" title="Link to this definition">¶</a></dt>
<dd><p>Address family not supported by protocol</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EADDRINUSE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EADDRINUSE</span></span><a class="headerlink" href="#errno.EADDRINUSE" title="Link to this definition">¶</a></dt>
<dd><p>Address already in use</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EADDRNOTAVAIL">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EADDRNOTAVAIL</span></span><a class="headerlink" href="#errno.EADDRNOTAVAIL" title="Link to this definition">¶</a></dt>
<dd><p>Cannot assign requested address</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENETDOWN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENETDOWN</span></span><a class="headerlink" href="#errno.ENETDOWN" title="Link to this definition">¶</a></dt>
<dd><p>Network is down</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENETUNREACH">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENETUNREACH</span></span><a class="headerlink" href="#errno.ENETUNREACH" title="Link to this definition">¶</a></dt>
<dd><p>Network is unreachable</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENETRESET">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENETRESET</span></span><a class="headerlink" href="#errno.ENETRESET" title="Link to this definition">¶</a></dt>
<dd><p>Network dropped connection because of reset</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECONNABORTED">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECONNABORTED</span></span><a class="headerlink" href="#errno.ECONNABORTED" title="Link to this definition">¶</a></dt>
<dd><p>Software caused connection abort. This error is mapped to the
exception <a class="reference internal" href="exceptions.html#ConnectionAbortedError" title="ConnectionAbortedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionAbortedError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECONNRESET">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECONNRESET</span></span><a class="headerlink" href="#errno.ECONNRESET" title="Link to this definition">¶</a></dt>
<dd><p>Connection reset by peer. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#ConnectionResetError" title="ConnectionResetError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionResetError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOBUFS">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOBUFS</span></span><a class="headerlink" href="#errno.ENOBUFS" title="Link to this definition">¶</a></dt>
<dd><p>No buffer space available</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EISCONN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EISCONN</span></span><a class="headerlink" href="#errno.EISCONN" title="Link to this definition">¶</a></dt>
<dd><p>Transport endpoint is already connected</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTCONN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTCONN</span></span><a class="headerlink" href="#errno.ENOTCONN" title="Link to this definition">¶</a></dt>
<dd><p>Transport endpoint is not connected</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESHUTDOWN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESHUTDOWN</span></span><a class="headerlink" href="#errno.ESHUTDOWN" title="Link to this definition">¶</a></dt>
<dd><p>Cannot send after transport endpoint shutdown. This error is mapped
to the exception <a class="reference internal" href="exceptions.html#BrokenPipeError" title="BrokenPipeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BrokenPipeError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ETOOMANYREFS">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ETOOMANYREFS</span></span><a class="headerlink" href="#errno.ETOOMANYREFS" title="Link to this definition">¶</a></dt>
<dd><p>Too many references: cannot splice</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ETIMEDOUT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ETIMEDOUT</span></span><a class="headerlink" href="#errno.ETIMEDOUT" title="Link to this definition">¶</a></dt>
<dd><p>Connection timed out. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#TimeoutError" title="TimeoutError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECONNREFUSED">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECONNREFUSED</span></span><a class="headerlink" href="#errno.ECONNREFUSED" title="Link to this definition">¶</a></dt>
<dd><p>Connection refused. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#ConnectionRefusedError" title="ConnectionRefusedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionRefusedError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EHOSTDOWN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EHOSTDOWN</span></span><a class="headerlink" href="#errno.EHOSTDOWN" title="Link to this definition">¶</a></dt>
<dd><p>Host is down</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EHOSTUNREACH">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EHOSTUNREACH</span></span><a class="headerlink" href="#errno.EHOSTUNREACH" title="Link to this definition">¶</a></dt>
<dd><p>No route to host</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EALREADY">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EALREADY</span></span><a class="headerlink" href="#errno.EALREADY" title="Link to this definition">¶</a></dt>
<dd><p>Operation already in progress. This error is mapped to the
exception <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EINPROGRESS">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EINPROGRESS</span></span><a class="headerlink" href="#errno.EINPROGRESS" title="Link to this definition">¶</a></dt>
<dd><p>Operation now in progress. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ESTALE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ESTALE</span></span><a class="headerlink" href="#errno.ESTALE" title="Link to this definition">¶</a></dt>
<dd><p>Stale NFS file handle</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EUCLEAN">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EUCLEAN</span></span><a class="headerlink" href="#errno.EUCLEAN" title="Link to this definition">¶</a></dt>
<dd><p>Structure needs cleaning</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTNAM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTNAM</span></span><a class="headerlink" href="#errno.ENOTNAM" title="Link to this definition">¶</a></dt>
<dd><p>Not a XENIX named type file</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENAVAIL">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENAVAIL</span></span><a class="headerlink" href="#errno.ENAVAIL" title="Link to this definition">¶</a></dt>
<dd><p>No XENIX semaphores available</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EISNAM">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EISNAM</span></span><a class="headerlink" href="#errno.EISNAM" title="Link to this definition">¶</a></dt>
<dd><p>Is a named type file</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EREMOTEIO">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EREMOTEIO</span></span><a class="headerlink" href="#errno.EREMOTEIO" title="Link to this definition">¶</a></dt>
<dd><p>Remote I/O error</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EDQUOT">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EDQUOT</span></span><a class="headerlink" href="#errno.EDQUOT" title="Link to this definition">¶</a></dt>
<dd><p>Quota exceeded</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EQFULL">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EQFULL</span></span><a class="headerlink" href="#errno.EQFULL" title="Link to this definition">¶</a></dt>
<dd><p>Interface output queue is full</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTCAPABLE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTCAPABLE</span></span><a class="headerlink" href="#errno.ENOTCAPABLE" title="Link to this definition">¶</a></dt>
<dd><p>Capabilities insufficient. This error is mapped to the exception
<a class="reference internal" href="exceptions.html#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: WASI, FreeBSD</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.1.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ECANCELED">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ECANCELED</span></span><a class="headerlink" href="#errno.ECANCELED" title="Link to this definition">¶</a></dt>
<dd><p>Operation canceled</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.EOWNERDEAD">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">EOWNERDEAD</span></span><a class="headerlink" href="#errno.EOWNERDEAD" title="Link to this definition">¶</a></dt>
<dd><p>Owner died</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="errno.ENOTRECOVERABLE">
<span class="sig-prename descclassname"><span class="pre">errno.</span></span><span class="sig-name descname"><span class="pre">ENOTRECOVERABLE</span></span><a class="headerlink" href="#errno.ENOTRECOVERABLE" title="Link to this definition">¶</a></dt>
<dd><p>State not recoverable</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="platform.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ctypes.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ctypes</span></code> — A foreign function library for Python</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/errno.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ctypes.html" title="ctypes — A foreign function library for Python"
             >next</a> |</li>
        <li class="right" >
          <a href="platform.html" title="platform — Access to underlying platform’s identifying data"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" >Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code> — Standard errno system symbols</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>