<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="email.utils: Miscellaneous utilities" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/email.utils.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/email/utils.py There are a couple of useful utilities provided in the email.utils module: The remaining functions are part of the legacy ( Compat32) email API. There is no need to ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/email/utils.py There are a couple of useful utilities provided in the email.utils module: The remaining functions are part of the legacy ( Compat32) email API. There is no need to ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>email.utils: Miscellaneous utilities &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="email.iterators: Iterators" href="email.iterators.html" />
    <link rel="prev" title="email.encoders: Encoders" href="email.encoders.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/email.utils.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.encoders.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.encoders</span></code>: Encoders</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.iterators.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.iterators</span></code>: Iterators</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/email.utils.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.iterators.html" title="email.iterators: Iterators"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="email.encoders.html" title="email.encoders: Encoders"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
          <li class="nav-item nav-item-3"><a href="email.html" accesskey="U"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.utils</span></code>: Miscellaneous utilities</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-email.utils">
<span id="email-utils-miscellaneous-utilities"></span><h1><a class="reference internal" href="#module-email.utils" title="email.utils: Miscellaneous email package utilities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.utils</span></code></a>: Miscellaneous utilities<a class="headerlink" href="#module-email.utils" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/email/utils.py">Lib/email/utils.py</a></p>
<hr class="docutils" />
<p>There are a couple of useful utilities provided in the <a class="reference internal" href="#module-email.utils" title="email.utils: Miscellaneous email package utilities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.utils</span></code></a>
module:</p>
<dl class="py function">
<dt class="sig sig-object py" id="email.utils.localtime">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">localtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.localtime" title="Link to this definition">¶</a></dt>
<dd><p>Return local time as an aware datetime object.  If called without
arguments, return current time.  Otherwise <em>dt</em> argument should be a
<a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime</span></code></a> instance, and it is converted to the local time
zone according to the system time zone database.  If <em>dt</em> is naive (that
is, <code class="docutils literal notranslate"><span class="pre">dt.tzinfo</span></code> is <code class="docutils literal notranslate"><span class="pre">None</span></code>), it is assumed to be in local time.  The
<em>isdst</em> parameter is ignored.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>The <em>isdst</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.make_msgid">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">make_msgid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">idstring</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.make_msgid" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string suitable for an <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>-compliant
<em class="mailheader">Message-ID</em> header.  Optional <em>idstring</em> if given, is a string
used to strengthen the uniqueness of the message id.  Optional <em>domain</em> if
given provides the portion of the msgid after the ‘&#64;’.  The default is the
local hostname.  It is not normally necessary to override this default, but
may be useful certain cases, such as a constructing distributed system that
uses a consistent domain name across multiple hosts.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>domain</em> keyword.</p>
</div>
</dd></dl>

<p>The remaining functions are part of the legacy (<code class="docutils literal notranslate"><span class="pre">Compat32</span></code>) email API.  There
is no need to directly use these with the new API, since the parsing and
formatting they provide is done automatically by the header parsing machinery
of the new API.</p>
<dl class="py function">
<dt class="sig sig-object py" id="email.utils.quote">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">quote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.quote" title="Link to this definition">¶</a></dt>
<dd><p>Return a new string with backslashes in <em>str</em> replaced by two backslashes, and
double quotes replaced by backslash-double quote.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.unquote">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">unquote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.unquote" title="Link to this definition">¶</a></dt>
<dd><p>Return a new string which is an <em>unquoted</em> version of <em>str</em>. If <em>str</em> ends and
begins with double quotes, they are stripped off.  Likewise if <em>str</em> ends and
begins with angle brackets, they are stripped off.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.parseaddr">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">parseaddr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.parseaddr" title="Link to this definition">¶</a></dt>
<dd><p>Parse address – which should be the value of some address-containing field such
as <em class="mailheader">To</em> or <em class="mailheader">Cc</em> – into its constituent <em>realname</em> and
<em>email address</em> parts.  Returns a tuple of that information, unless the parse
fails, in which case a 2-tuple of <code class="docutils literal notranslate"><span class="pre">('',</span> <span class="pre">'')</span></code> is returned.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.formataddr">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">formataddr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pair</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'utf-8'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.formataddr" title="Link to this definition">¶</a></dt>
<dd><p>The inverse of <a class="reference internal" href="#email.utils.parseaddr" title="email.utils.parseaddr"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parseaddr()</span></code></a>, this takes a 2-tuple of the form <code class="docutils literal notranslate"><span class="pre">(realname,</span>
<span class="pre">email_address)</span></code> and returns the string value suitable for a <em class="mailheader">To</em> or
<em class="mailheader">Cc</em> header.  If the first element of <em>pair</em> is false, then the
second element is returned unmodified.</p>
<p>Optional <em>charset</em> is the character set that will be used in the <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2047.html"><strong>RFC 2047</strong></a>
encoding of the <code class="docutils literal notranslate"><span class="pre">realname</span></code> if the <code class="docutils literal notranslate"><span class="pre">realname</span></code> contains non-ASCII
characters.  Can be an instance of <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> or a
<a class="reference internal" href="email.charset.html#email.charset.Charset" title="email.charset.Charset"><code class="xref py py-class docutils literal notranslate"><span class="pre">Charset</span></code></a>.  Defaults to <code class="docutils literal notranslate"><span class="pre">utf-8</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>charset</em> option.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.getaddresses">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">getaddresses</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fieldvalues</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.getaddresses" title="Link to this definition">¶</a></dt>
<dd><p>This method returns a list of 2-tuples of the form returned by <code class="docutils literal notranslate"><span class="pre">parseaddr()</span></code>.
<em>fieldvalues</em> is a sequence of header field values as might be returned by
<a class="reference internal" href="email.compat32-message.html#email.message.Message.get_all" title="email.message.Message.get_all"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Message.get_all</span></code></a>.  Here’s a simple
example that gets all the recipients of a message:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">email.utils</span> <span class="kn">import</span> <span class="n">getaddresses</span>

<span class="n">tos</span> <span class="o">=</span> <span class="n">msg</span><span class="o">.</span><span class="n">get_all</span><span class="p">(</span><span class="s1">&#39;to&#39;</span><span class="p">,</span> <span class="p">[])</span>
<span class="n">ccs</span> <span class="o">=</span> <span class="n">msg</span><span class="o">.</span><span class="n">get_all</span><span class="p">(</span><span class="s1">&#39;cc&#39;</span><span class="p">,</span> <span class="p">[])</span>
<span class="n">resent_tos</span> <span class="o">=</span> <span class="n">msg</span><span class="o">.</span><span class="n">get_all</span><span class="p">(</span><span class="s1">&#39;resent-to&#39;</span><span class="p">,</span> <span class="p">[])</span>
<span class="n">resent_ccs</span> <span class="o">=</span> <span class="n">msg</span><span class="o">.</span><span class="n">get_all</span><span class="p">(</span><span class="s1">&#39;resent-cc&#39;</span><span class="p">,</span> <span class="p">[])</span>
<span class="n">all_recipients</span> <span class="o">=</span> <span class="n">getaddresses</span><span class="p">(</span><span class="n">tos</span> <span class="o">+</span> <span class="n">ccs</span> <span class="o">+</span> <span class="n">resent_tos</span> <span class="o">+</span> <span class="n">resent_ccs</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.parsedate">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">parsedate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">date</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.parsedate" title="Link to this definition">¶</a></dt>
<dd><p>Attempts to parse a date according to the rules in <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>. however, some
mailers don’t follow that format as specified, so <a class="reference internal" href="#email.utils.parsedate" title="email.utils.parsedate"><code class="xref py py-func docutils literal notranslate"><span class="pre">parsedate()</span></code></a> tries to
guess correctly in such cases.  <em>date</em> is a string containing an <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>
date, such as  <code class="docutils literal notranslate"><span class="pre">&quot;Mon,</span> <span class="pre">20</span> <span class="pre">Nov</span> <span class="pre">1995</span> <span class="pre">19:12:08</span> <span class="pre">-0500&quot;</span></code>.  If it succeeds in parsing
the date, <a class="reference internal" href="#email.utils.parsedate" title="email.utils.parsedate"><code class="xref py py-func docutils literal notranslate"><span class="pre">parsedate()</span></code></a> returns a 9-tuple that can be passed directly to
<a class="reference internal" href="time.html#time.mktime" title="time.mktime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.mktime()</span></code></a>; otherwise <code class="docutils literal notranslate"><span class="pre">None</span></code> will be returned.  Note that indexes 6,
7, and 8 of the result tuple are not usable.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.parsedate_tz">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">parsedate_tz</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">date</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.parsedate_tz" title="Link to this definition">¶</a></dt>
<dd><p>Performs the same function as <a class="reference internal" href="#email.utils.parsedate" title="email.utils.parsedate"><code class="xref py py-func docutils literal notranslate"><span class="pre">parsedate()</span></code></a>, but returns either <code class="docutils literal notranslate"><span class="pre">None</span></code> or
a 10-tuple; the first 9 elements make up a tuple that can be passed directly to
<a class="reference internal" href="time.html#time.mktime" title="time.mktime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.mktime()</span></code></a>, and the tenth is the offset of the date’s timezone from UTC
(which is the official term for Greenwich Mean Time) <a class="footnote-reference brackets" href="#id2" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a>.  If the input string
has no timezone, the last element of the tuple returned is <code class="docutils literal notranslate"><span class="pre">0</span></code>, which represents
UTC. Note that indexes 6, 7, and 8 of the result tuple are not usable.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.parsedate_to_datetime">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">parsedate_to_datetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">date</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.parsedate_to_datetime" title="Link to this definition">¶</a></dt>
<dd><p>The inverse of <a class="reference internal" href="#email.utils.format_datetime" title="email.utils.format_datetime"><code class="xref py py-func docutils literal notranslate"><span class="pre">format_datetime()</span></code></a>.  Performs the same function as
<a class="reference internal" href="#email.utils.parsedate" title="email.utils.parsedate"><code class="xref py py-func docutils literal notranslate"><span class="pre">parsedate()</span></code></a>, but on success returns a <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code></a>;
otherwise <code class="docutils literal notranslate"><span class="pre">ValueError</span></code> is raised if <em>date</em> contains an invalid value such
as an hour greater than 23 or a timezone offset not between -24 and 24 hours.
If the input date has a timezone of <code class="docutils literal notranslate"><span class="pre">-0000</span></code>, the <code class="docutils literal notranslate"><span class="pre">datetime</span></code> will be a naive
<code class="docutils literal notranslate"><span class="pre">datetime</span></code>, and if the date is conforming to the RFCs it will represent a
time in UTC but with no indication of the actual source timezone of the
message the date comes from.  If the input date has any other valid timezone
offset, the <code class="docutils literal notranslate"><span class="pre">datetime</span></code> will be an aware <code class="docutils literal notranslate"><span class="pre">datetime</span></code> with the
corresponding a <a class="reference internal" href="datetime.html#datetime.timezone" title="datetime.timezone"><code class="xref py py-class docutils literal notranslate"><span class="pre">timezone</span></code></a> <a class="reference internal" href="datetime.html#datetime.tzinfo" title="datetime.tzinfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">tzinfo</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.mktime_tz">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">mktime_tz</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tuple</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.mktime_tz" title="Link to this definition">¶</a></dt>
<dd><p>Turn a 10-tuple as returned by <a class="reference internal" href="#email.utils.parsedate_tz" title="email.utils.parsedate_tz"><code class="xref py py-func docutils literal notranslate"><span class="pre">parsedate_tz()</span></code></a> into a UTC
timestamp (seconds since the Epoch).  If the timezone item in the
tuple is <code class="docutils literal notranslate"><span class="pre">None</span></code>, assume local time.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.formatdate">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">formatdate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localtime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usegmt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.formatdate" title="Link to this definition">¶</a></dt>
<dd><p>Returns a date string as per <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Fri</span><span class="p">,</span> <span class="mi">09</span> <span class="n">Nov</span> <span class="mi">2001</span> <span class="mi">01</span><span class="p">:</span><span class="mi">08</span><span class="p">:</span><span class="mi">47</span> <span class="o">-</span><span class="mi">0000</span>
</pre></div>
</div>
<p>Optional <em>timeval</em> if given is a floating point time value as accepted by
<a class="reference internal" href="time.html#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.gmtime()</span></code></a> and <a class="reference internal" href="time.html#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.localtime()</span></code></a>, otherwise the current time is
used.</p>
<p>Optional <em>localtime</em> is a flag that when <code class="docutils literal notranslate"><span class="pre">True</span></code>, interprets <em>timeval</em>, and
returns a date relative to the local timezone instead of UTC, properly taking
daylight savings time into account. The default is <code class="docutils literal notranslate"><span class="pre">False</span></code> meaning UTC is
used.</p>
<p>Optional <em>usegmt</em> is a flag that when <code class="docutils literal notranslate"><span class="pre">True</span></code>, outputs a  date string with the
timezone as an ascii string <code class="docutils literal notranslate"><span class="pre">GMT</span></code>, rather than a numeric <code class="docutils literal notranslate"><span class="pre">-0000</span></code>. This is
needed for some protocols (such as HTTP). This only applies when <em>localtime</em> is
<code class="docutils literal notranslate"><span class="pre">False</span></code>.  The default is <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.format_datetime">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">format_datetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dt</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usegmt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.format_datetime" title="Link to this definition">¶</a></dt>
<dd><p>Like <code class="docutils literal notranslate"><span class="pre">formatdate</span></code>, but the input is a <a class="reference internal" href="datetime.html#module-datetime" title="datetime: Basic date and time types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code></a> instance.  If it is
a naive datetime, it is assumed to be “UTC with no information about the
source timezone”, and the conventional <code class="docutils literal notranslate"><span class="pre">-0000</span></code> is used for the timezone.
If it is an aware <code class="docutils literal notranslate"><span class="pre">datetime</span></code>, then the numeric timezone offset is used.
If it is an aware timezone with offset zero, then <em>usegmt</em> may be set to
<code class="docutils literal notranslate"><span class="pre">True</span></code>, in which case the string <code class="docutils literal notranslate"><span class="pre">GMT</span></code> is used instead of the numeric
timezone offset.  This provides a way to generate standards conformant HTTP
date headers.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.decode_rfc2231">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">decode_rfc2231</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.decode_rfc2231" title="Link to this definition">¶</a></dt>
<dd><p>Decode the string <em>s</em> according to <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2231.html"><strong>RFC 2231</strong></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.encode_rfc2231">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">encode_rfc2231</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">language</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.encode_rfc2231" title="Link to this definition">¶</a></dt>
<dd><p>Encode the string <em>s</em> according to <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2231.html"><strong>RFC 2231</strong></a>.  Optional <em>charset</em> and
<em>language</em>, if given is the character set name and language name to use.  If
neither is given, <em>s</em> is returned as-is.  If <em>charset</em> is given but <em>language</em>
is not, the string is encoded using the empty string for <em>language</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.collapse_rfc2231_value">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">collapse_rfc2231_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'replace'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fallback_charset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'us-ascii'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.collapse_rfc2231_value" title="Link to this definition">¶</a></dt>
<dd><p>When a header parameter is encoded in <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2231.html"><strong>RFC 2231</strong></a> format,
<a class="reference internal" href="email.compat32-message.html#email.message.Message.get_param" title="email.message.Message.get_param"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Message.get_param</span></code></a> may return a
3-tuple containing the character set,
language, and value.  <a class="reference internal" href="#email.utils.collapse_rfc2231_value" title="email.utils.collapse_rfc2231_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">collapse_rfc2231_value()</span></code></a> turns this into a unicode
string.  Optional <em>errors</em> is passed to the <em>errors</em> argument of <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>’s
<a class="reference internal" href="stdtypes.html#str.encode" title="str.encode"><code class="xref py py-func docutils literal notranslate"><span class="pre">encode()</span></code></a> method; it defaults to <code class="docutils literal notranslate"><span class="pre">'replace'</span></code>.  Optional
<em>fallback_charset</em> specifies the character set to use if the one in the
<span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2231.html"><strong>RFC 2231</strong></a> header is not known by Python; it defaults to <code class="docutils literal notranslate"><span class="pre">'us-ascii'</span></code>.</p>
<p>For convenience, if the <em>value</em> passed to <a class="reference internal" href="#email.utils.collapse_rfc2231_value" title="email.utils.collapse_rfc2231_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">collapse_rfc2231_value()</span></code></a> is not
a tuple, it should be a string and it is returned unquoted.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.utils.decode_params">
<span class="sig-prename descclassname"><span class="pre">email.utils.</span></span><span class="sig-name descname"><span class="pre">decode_params</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">params</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.utils.decode_params" title="Link to this definition">¶</a></dt>
<dd><p>Decode parameters list according to <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2231.html"><strong>RFC 2231</strong></a>.  <em>params</em> is a sequence of
2-tuples containing elements of the form <code class="docutils literal notranslate"><span class="pre">(content-type,</span> <span class="pre">string-value)</span></code>.</p>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id2" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>Note that the sign of the timezone offset is the opposite of the sign of the
<code class="docutils literal notranslate"><span class="pre">time.timezone</span></code> variable for the same timezone; the latter variable follows
the POSIX standard while this module follows <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>.</p>
</aside>
</aside>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.encoders.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.encoders</span></code>: Encoders</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.iterators.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.iterators</span></code>: Iterators</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/email.utils.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.iterators.html" title="email.iterators: Iterators"
             >next</a> |</li>
        <li class="right" >
          <a href="email.encoders.html" title="email.encoders: Encoders"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
          <li class="nav-item nav-item-3"><a href="email.html" ><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.utils</span></code>: Miscellaneous utilities</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>