# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_profile
# 
# Translators:
# Wil O<PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"!<br/>\n"
"            Did not receive it?"
msgstr ""
"!<br/>\n"
"           受け取っていませんか？"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "(not verified)"
msgstr "(未検証)"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
". Collect points on the forum or on the eLearning platform. Those points "
"will make you reach new ranks."
msgstr "。フォーラムやeラーニング・プラットフォームでポイントを集めましょう。そのポイントで新しいランクに到達できます。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid ". Try another search."
msgstr "。また検索してみて下さい。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"fa fa-arrow-right\"/> Get Badges"
msgstr "<i class=\"fa fa-arrow-right\"/>バッジ獲得 "

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-close me-1\"/>Cancel"
msgstr "<i class=\"fa fa-close me-1\"/>取消"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"Edit\"/>"
msgstr "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"Edit\"/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-1\"/>EDIT"
msgstr "<i class=\"fa fa-pencil me-1\"/>編集"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>編集"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-2\"/>EDIT PROFILE"
msgstr "<i class=\"fa fa-pencil me-2\"/>プロフィールを編集"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Badges"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>全てのバッジ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right\"/> All Badges"
msgstr "<i class=\"oi oi-arrow-right\"/>全てのバッジ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> 受賞者</i>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">国...</option>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold me-2\">Current rank:</small>"
msgstr "<small class=\"fw-bold me-2\">現在のランク:</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Badges</small>"
msgstr "<small class=\"fw-bold\">バッジ</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Joined</small>"
msgstr "<small class=\"fw-bold\">参加済</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Biography</span>"
msgstr "<span class=\"fw-bold\">略歴</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">City</span>"
msgstr "<span class=\"fw-bold\">都市</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Country</span>"
msgstr "<span class=\"fw-bold\">国</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Email</span>"
msgstr "<span class=\"fw-bold\">Eメール</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Name</span>"
msgstr "<span class=\"fw-bold\">名前</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Public Profile</span>"
msgstr "<span class=\"fw-bold\">公開プロフィール</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Website</span>"
msgstr "<span class=\"fw-bold\">ウェブサイト</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">Badges</span>"
msgstr "<span class=\"text-muted small fw-bold\">バッジ</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">XP</span>"
msgstr "<span class=\"text-muted small fw-bold\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">Badges</span>"
msgstr "<span class=\"text-muted\">バッジ</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">XP</span>"
msgstr "<span class=\"text-muted\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"<span id=\"email_validated_message\">Congratulations! Your email has just "
"been validated.</span>"
msgstr "<span id=\"email_validated_message\">おめでとうございます。あなたのeメールが検証されました。</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "<strong class=\"mb-3 text-white me-2\">Rank by:</strong>"
msgstr "<strong class=\"mb-3 text-white me-2\">以下によるランク:</strong>"

#. module: website_profile
#: model:mail.template,body_html:website_profile.validation_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> Profile validation\n"
"                        </span>\n"
"                    </td>\n"
"                    <td t-if=\"not user.company_id.uses_default_logo\" valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hello <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                            You have been invited to validate your email in order to get access to \"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" website.\n"
"                            To validate your email, please click on the following link:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Validate my account\n"
"                                </a>\n"
"                            </div>\n"
"                            Thanks for your participation!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> プロフィール検証\n"
"                        </span>\n"
"                    </td>\n"
"                    <td t-if=\"not user.company_id.uses_default_logo\" valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            こんにちは、<t t-out=\"object.name or ''\">Marc Demo</t>様、<br><br>\n"
"                             eメールを検証して、\"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" ウェブサイトにアクセスできるようにして下さい。\n"
"                            eメールを検証するには、以下をクリックして下さい。\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    アカウントを検証する\n"
"                                </a>\n"
"                            </div>\n"
"                            ご参加ありがとうございます！\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Correct your email address</u>"
msgstr "<u>Eメールアドレスの訂正</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Send Again</u>"
msgstr "<u>再送する</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>here</u>"
msgstr "<u>ここ</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "About"
msgstr "Odooについて"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All Users"
msgstr "すべてのユーザ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All time"
msgstr "常に"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Badges"
msgstr "バッジ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "Badges are your collection of achievements. Wear them proudly! <br/>"
msgstr "バッジはあなたの功績のコレクションです。誇りを持って身につけましょう！<br/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"                        you receive badges for being especially helpful.<br class=\"d-none d-lg-inline-block\"/>Badges\n"
"                        appear on your profile page, and your posts."
msgstr ""
"質問や回答をすることで高い評価を得ることができます。\n"
"　　　　　　特に役に立った場合にはバッジを獲得できます。<br class=\"d-none d-lg-inline-block\"/>バッジ\n"
"はプロフィールページと投稿に表示されます。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Biography"
msgstr "略歴"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__can_publish
msgid "Can Publish"
msgstr "公開可"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Clear"
msgstr "クリア"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Close"
msgstr "閉じる"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit"
msgstr "編集"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit Profile"
msgstr "プロフィールを編集"

#. module: website_profile
#: model:mail.template,name:website_profile.validation_email
msgid "Forum: Email Verification"
msgstr "フォーラム: Eメール認証"

#. module: website_profile
#: model:ir.model,name:website_profile.model_gamification_badge
msgid "Gamification Badge"
msgstr "ゲーミフィケーションのバッジ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "Get"
msgstr "取得"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Home"
msgstr "ホーム"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I earn badges?"
msgstr "バッジを取得するには？"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I score more points?"
msgstr "より多くのポイントを獲得するには？"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__is_published
msgid "Is Published"
msgstr "公開済"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Keep learning with"
msgstr "以下で継続して学ぶ:"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_website__karma_profile_min
msgid "Minimal karma to see other user's profile"
msgstr "他のユーザのプロフィールを閲覧するための最小カルマ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Mobile sub-nav"
msgstr "モバイルサブナビゲーション"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "More info"
msgstr "詳しくはこちらへ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Nav"
msgstr "ナビゲーション"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Next rank:"
msgstr "次のランク:"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_main
msgid "No Leaderboard Yet :("
msgstr "現在リーダボードはありません :("

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "No badges yet!"
msgstr "まだバッジがありません！"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid "No user found for"
msgstr "以下用のユーザが見つかりません"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "Not have enough karma to view other users' profile."
msgstr "他のユーザのプロフィールを閲覧するためカルマが不足しています"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr "回答やコメントからの通知を受け取るには、有効なメールアドレスを入力してください。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Ranks"
msgstr "ランク"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_access_denied
msgid "Return to the website."
msgstr "ウェブサイトに戻る。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search"
msgstr "検索"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search users"
msgstr "ユーザを検索"

#. module: website_profile
#: model:mail.template,description:website_profile.validation_email
msgid "Sent to forum visitors to confirm their mail address"
msgstr "メールアドレスを確認するためにフォーラム訪問者に送信"

#. module: website_profile
#: model:ir.model.fields,help:website_profile.field_gamification_badge__website_url
msgid "The full URL to access the document through the website."
msgstr "サイト経由して、文書にアクセスする完全なURL。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This month"
msgstr "今月"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "This profile is private!"
msgstr "このプロフィールは非公開です。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This week"
msgstr "今週"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "Unpublished"
msgstr "未公開"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Update"
msgstr "更新"

#. module: website_profile
#: model:ir.model,name:website_profile.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "User rank"
msgstr "ユーザランク"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Users"
msgstr "ユーザ"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Verification Email sent to"
msgstr "認証メールを以下に送信:"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_published
msgid "Visible on current website"
msgstr "現在のサイトに表示"

#. module: website_profile
#: model:ir.model,name:website_profile.model_website
msgid "Website"
msgstr "ウェブサイト"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_url
msgid "Website URL"
msgstr "サイトURL"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "When you finish a course or reach milestones, you're awarded badges."
msgstr "コース終了やマイルストン達成で、バッジを獲得できます。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Write a few words about yourself..."
msgstr "あなた自身について一言書いて下さい"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "XP"
msgstr "XP"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
"You can score more points by answering quizzes at the end of each course "
"content. Points can also be earned on the forum. Follow this link to the "
"guidelines of the forum."
msgstr ""
"コースの各コンテンツの最後にあるクイズに答えることで、より多くのポイントを獲得できます。また、フォーラムでもポイントを獲得できます。このリンクからフォーラムのガイドラインをご覧下さい。"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"Your Account has not yet been verified.<br/>\n"
"            Click"
msgstr ""
"お客様のアカウントはまだ認証されていません。<br/>\n"
"　　　クリック"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Your account does not have an email set up. Please set it up on"
msgstr "アカウントにEメールが設定されていません。次のページで設定して下さい:"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "breadcrumb"
msgstr "パンくず"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "or"
msgstr "又は"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "point"
msgstr "ポイント"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this month"
msgstr "今月"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this week"
msgstr "今週"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "to receive a verification email"
msgstr "認証メールを受け取る"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "xp"
msgstr "xp"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"xp\n"
"                        to level up!"
msgstr ""
"xp\n"
"                        レベルアップする！"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "your account settings"
msgstr "アカウントセッティング"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"
msgstr ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"

#. module: website_profile
#: model:mail.template,subject:website_profile.validation_email
msgid "{{ object.company_id.name }} Profile validation"
msgstr "{{ object.company_id.name }} プロフィール検証"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "└ Users"
msgstr "└ ユーザ"
