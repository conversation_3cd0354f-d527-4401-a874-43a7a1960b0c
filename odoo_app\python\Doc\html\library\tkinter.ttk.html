<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tkinter.ttk — Tk themed widgets" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tkinter.ttk.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tkinter/ttk.py The tkinter.ttk module provides access to the Tk themed widget set, introduced in Tk 8.5. It provides additional benefits including anti-aliased font rendering under..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tkinter/ttk.py The tkinter.ttk module provides access to the Tk themed widget set, introduced in Tk 8.5. It provides additional benefits including anti-aliased font rendering under..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tkinter.ttk — Tk themed widgets &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tkinter.tix — Extension widgets for Tk" href="tkinter.tix.html" />
    <link rel="prev" title="tkinter.dnd — Drag and drop support" href="tkinter.dnd.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tkinter.ttk.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a><ul>
<li><a class="reference internal" href="#using-ttk">Using Ttk</a></li>
<li><a class="reference internal" href="#ttk-widgets">Ttk Widgets</a></li>
<li><a class="reference internal" href="#widget">Widget</a><ul>
<li><a class="reference internal" href="#standard-options">Standard Options</a></li>
<li><a class="reference internal" href="#scrollable-widget-options">Scrollable Widget Options</a></li>
<li><a class="reference internal" href="#label-options">Label Options</a></li>
<li><a class="reference internal" href="#compatibility-options">Compatibility Options</a></li>
<li><a class="reference internal" href="#widget-states">Widget States</a></li>
<li><a class="reference internal" href="#ttk-widget">ttk.Widget</a></li>
</ul>
</li>
<li><a class="reference internal" href="#combobox">Combobox</a><ul>
<li><a class="reference internal" href="#options">Options</a></li>
<li><a class="reference internal" href="#virtual-events">Virtual events</a></li>
<li><a class="reference internal" href="#ttk-combobox">ttk.Combobox</a></li>
</ul>
</li>
<li><a class="reference internal" href="#spinbox">Spinbox</a><ul>
<li><a class="reference internal" href="#id1">Options</a></li>
<li><a class="reference internal" href="#id2">Virtual events</a></li>
<li><a class="reference internal" href="#ttk-spinbox">ttk.Spinbox</a></li>
</ul>
</li>
<li><a class="reference internal" href="#notebook">Notebook</a><ul>
<li><a class="reference internal" href="#id3">Options</a></li>
<li><a class="reference internal" href="#tab-options">Tab Options</a></li>
<li><a class="reference internal" href="#tab-identifiers">Tab Identifiers</a></li>
<li><a class="reference internal" href="#id4">Virtual Events</a></li>
<li><a class="reference internal" href="#ttk-notebook">ttk.Notebook</a></li>
</ul>
</li>
<li><a class="reference internal" href="#progressbar">Progressbar</a><ul>
<li><a class="reference internal" href="#id5">Options</a></li>
<li><a class="reference internal" href="#ttk-progressbar">ttk.Progressbar</a></li>
</ul>
</li>
<li><a class="reference internal" href="#separator">Separator</a><ul>
<li><a class="reference internal" href="#id6">Options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#sizegrip">Sizegrip</a><ul>
<li><a class="reference internal" href="#platform-specific-notes">Platform-specific notes</a></li>
<li><a class="reference internal" href="#bugs">Bugs</a></li>
</ul>
</li>
<li><a class="reference internal" href="#treeview">Treeview</a><ul>
<li><a class="reference internal" href="#id7">Options</a></li>
<li><a class="reference internal" href="#item-options">Item Options</a></li>
<li><a class="reference internal" href="#tag-options">Tag Options</a></li>
<li><a class="reference internal" href="#column-identifiers">Column Identifiers</a></li>
<li><a class="reference internal" href="#id8">Virtual Events</a></li>
<li><a class="reference internal" href="#ttk-treeview">ttk.Treeview</a></li>
</ul>
</li>
<li><a class="reference internal" href="#ttk-styling">Ttk Styling</a><ul>
<li><a class="reference internal" href="#layouts">Layouts</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tkinter.dnd.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.dnd</span></code> — Drag and drop support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.tix.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.ttk.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.tix.html" title="tkinter.tix — Extension widgets for Tk"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tkinter.dnd.html" title="tkinter.dnd — Drag and drop support"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" accesskey="U">Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tkinter.ttk">
<span id="tkinter-ttk-tk-themed-widgets"></span><h1><a class="reference internal" href="#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> — Tk themed widgets<a class="headerlink" href="#module-tkinter.ttk" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tkinter/ttk.py">Lib/tkinter/ttk.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> module provides access to the Tk themed widget set,
introduced in Tk 8.5. It provides additional benefits including anti-aliased font
rendering under X11 and window transparency (requiring a composition
window manager on X11).</p>
<p>The basic idea for <a class="reference internal" href="#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> is to separate, to the extent possible,
the code implementing a widget’s behavior from the code implementing its
appearance.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://core.tcl.tk/tips/doc/trunk/tip/48.md">Tk Widget Styling Support</a></dt><dd><p>A document introducing theming support for Tk</p>
</dd>
</dl>
</div>
<section id="using-ttk">
<h2>Using Ttk<a class="headerlink" href="#using-ttk" title="Link to this heading">¶</a></h2>
<p>To start using Ttk, import its module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>
</pre></div>
</div>
<p>To override the basic Tk widgets, the import should follow the Tk import:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="o">*</span>
<span class="kn">from</span> <span class="nn">tkinter.ttk</span> <span class="kn">import</span> <span class="o">*</span>
</pre></div>
</div>
<p>That code causes several <a class="reference internal" href="#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> widgets (<code class="xref py py-class docutils literal notranslate"><span class="pre">Button</span></code>,
<code class="xref py py-class docutils literal notranslate"><span class="pre">Checkbutton</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Entry</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Frame</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Label</span></code>,
<code class="xref py py-class docutils literal notranslate"><span class="pre">LabelFrame</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Menubutton</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">PanedWindow</span></code>,
<code class="xref py py-class docutils literal notranslate"><span class="pre">Radiobutton</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Scale</span></code> and <code class="xref py py-class docutils literal notranslate"><span class="pre">Scrollbar</span></code>) to
automatically replace the Tk widgets.</p>
<p>This has the direct benefit of using the new widgets which gives a better
look and feel across platforms; however, the replacement widgets are not
completely compatible. The main difference is that widget options such as
“fg”, “bg” and others related to widget styling are no
longer present in Ttk widgets.  Instead, use  the <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Style</span></code> class
for improved styling effects.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://tktable.sourceforge.net/tile/doc/converting.txt">Converting existing applications to use Tile widgets</a></dt><dd><p>A monograph (using Tcl terminology) about differences typically
encountered when moving applications to use the new widgets.</p>
</dd>
</dl>
</div>
</section>
<section id="ttk-widgets">
<h2>Ttk Widgets<a class="headerlink" href="#ttk-widgets" title="Link to this heading">¶</a></h2>
<p>Ttk comes with 18 widgets, twelve of which already existed in tkinter:
<code class="xref py py-class docutils literal notranslate"><span class="pre">Button</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Checkbutton</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Entry</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Frame</span></code>,
<code class="xref py py-class docutils literal notranslate"><span class="pre">Label</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">LabelFrame</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Menubutton</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">PanedWindow</span></code>,
<code class="xref py py-class docutils literal notranslate"><span class="pre">Radiobutton</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Scale</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Scrollbar</span></code>, and <a class="reference internal" href="#tkinter.ttk.Spinbox" title="tkinter.ttk.Spinbox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Spinbox</span></code></a>.
The other six are new: <a class="reference internal" href="#tkinter.ttk.Combobox" title="tkinter.ttk.Combobox"><code class="xref py py-class docutils literal notranslate"><span class="pre">Combobox</span></code></a>, <a class="reference internal" href="#tkinter.ttk.Notebook" title="tkinter.ttk.Notebook"><code class="xref py py-class docutils literal notranslate"><span class="pre">Notebook</span></code></a>,
<a class="reference internal" href="#tkinter.ttk.Progressbar" title="tkinter.ttk.Progressbar"><code class="xref py py-class docutils literal notranslate"><span class="pre">Progressbar</span></code></a>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Separator</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Sizegrip</span></code> and
<a class="reference internal" href="#tkinter.ttk.Treeview" title="tkinter.ttk.Treeview"><code class="xref py py-class docutils literal notranslate"><span class="pre">Treeview</span></code></a>. And all them are subclasses of <a class="reference internal" href="#tkinter.ttk.Widget" title="tkinter.ttk.Widget"><code class="xref py py-class docutils literal notranslate"><span class="pre">Widget</span></code></a>.</p>
<p>Using the Ttk widgets gives the application an improved look and feel.
As discussed above, there are differences in how the styling is coded.</p>
<p>Tk code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">l1</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Label</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;Test&quot;</span><span class="p">,</span> <span class="n">fg</span><span class="o">=</span><span class="s2">&quot;black&quot;</span><span class="p">,</span> <span class="n">bg</span><span class="o">=</span><span class="s2">&quot;white&quot;</span><span class="p">)</span>
<span class="n">l2</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Label</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;Test&quot;</span><span class="p">,</span> <span class="n">fg</span><span class="o">=</span><span class="s2">&quot;black&quot;</span><span class="p">,</span> <span class="n">bg</span><span class="o">=</span><span class="s2">&quot;white&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Ttk code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">style</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">()</span>
<span class="n">style</span><span class="o">.</span><span class="n">configure</span><span class="p">(</span><span class="s2">&quot;BW.TLabel&quot;</span><span class="p">,</span> <span class="n">foreground</span><span class="o">=</span><span class="s2">&quot;black&quot;</span><span class="p">,</span> <span class="n">background</span><span class="o">=</span><span class="s2">&quot;white&quot;</span><span class="p">)</span>

<span class="n">l1</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Label</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;Test&quot;</span><span class="p">,</span> <span class="n">style</span><span class="o">=</span><span class="s2">&quot;BW.TLabel&quot;</span><span class="p">)</span>
<span class="n">l2</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Label</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;Test&quot;</span><span class="p">,</span> <span class="n">style</span><span class="o">=</span><span class="s2">&quot;BW.TLabel&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>For more information about <a class="reference internal" href="#ttkstyling">TtkStyling</a>, see the <a class="reference internal" href="#tkinter.ttk.Style" title="tkinter.ttk.Style"><code class="xref py py-class docutils literal notranslate"><span class="pre">Style</span></code></a> class
documentation.</p>
</section>
<section id="widget">
<h2>Widget<a class="headerlink" href="#widget" title="Link to this heading">¶</a></h2>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Widget</span></code> defines standard options and methods supported by Tk
themed widgets and is not supposed to be directly instantiated.</p>
<section id="standard-options">
<h3>Standard Options<a class="headerlink" href="#standard-options" title="Link to this heading">¶</a></h3>
<p>All the <code class="xref py py-mod docutils literal notranslate"><span class="pre">ttk</span></code> Widgets accept the following options:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>class</p></td>
<td><p>Specifies the window class. The class is used when querying
the option database for the window’s other options, to
determine the default bindtags for the window, and to select
the widget’s default layout and style. This option is
read-only, and may only be specified when the window is
created.</p></td>
</tr>
<tr class="row-odd"><td><p>cursor</p></td>
<td><p>Specifies the mouse cursor to be used for the widget. If set
to the empty string (the default), the cursor is inherited
for the parent widget.</p></td>
</tr>
<tr class="row-even"><td><p>takefocus</p></td>
<td><p>Determines whether the window accepts the focus during
keyboard traversal. 0, 1 or an empty string is returned.
If 0 is returned, it means that the window should be skipped
entirely during keyboard traversal. If 1, it means that the
window should receive the input focus as long as it is
viewable. And an empty string means that the traversal
scripts make the decision about whether or not to focus
on the window.</p></td>
</tr>
<tr class="row-odd"><td><p>style</p></td>
<td><p>May be used to specify a custom widget style.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="scrollable-widget-options">
<h3>Scrollable Widget Options<a class="headerlink" href="#scrollable-widget-options" title="Link to this heading">¶</a></h3>
<p>The following options are supported by widgets that are controlled by a
scrollbar.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>xscrollcommand</p></td>
<td><p>Used to communicate with horizontal scrollbars.</p>
<p>When the view in the widget’s window change, the widget
will generate a Tcl command based on the scrollcommand.</p>
<p>Usually this option consists of the method
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Scrollbar.set()</span></code> of some scrollbar. This will cause
the scrollbar to be updated whenever the view in the
window changes.</p>
</td>
</tr>
<tr class="row-odd"><td><p>yscrollcommand</p></td>
<td><p>Used to communicate with vertical scrollbars.
For some more information, see above.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="label-options">
<h3>Label Options<a class="headerlink" href="#label-options" title="Link to this heading">¶</a></h3>
<p>The following options are supported by labels, buttons and other button-like
widgets.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>text</p></td>
<td><p>Specifies a text string to be displayed inside the widget.</p></td>
</tr>
<tr class="row-odd"><td><p>textvariable</p></td>
<td><p>Specifies a name whose value will be used in place of the
text option resource.</p></td>
</tr>
<tr class="row-even"><td><p>underline</p></td>
<td><p>If set, specifies the index (0-based) of a character to
underline in the text string. The underline character is
used for mnemonic activation.</p></td>
</tr>
<tr class="row-odd"><td><p>image</p></td>
<td><p>Specifies an image to display. This is a list of 1 or more
elements. The first element is the default image name. The
rest of the list if a sequence of statespec/value pairs as
defined by <a class="reference internal" href="#tkinter.ttk.Style.map" title="tkinter.ttk.Style.map"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Style.map()</span></code></a>, specifying different images
to use when the widget is in a particular state or a
combination of states. All images in the list should have
the same size.</p></td>
</tr>
<tr class="row-even"><td><p>compound</p></td>
<td><p>Specifies how to display the image relative to the text,
in the case both text and images options are present.
Valid values are:</p>
<ul class="simple">
<li><p>text: display text only</p></li>
<li><p>image: display image only</p></li>
<li><p>top, bottom, left, right: display image above, below,
left of, or right of the text, respectively.</p></li>
<li><p>none: the default. display the image if present,
otherwise the text.</p></li>
</ul>
</td>
</tr>
<tr class="row-odd"><td><p>width</p></td>
<td><p>If greater than zero, specifies how much space, in
character widths, to allocate for the text label, if less
than zero, specifies a minimum width. If zero or
unspecified, the natural width of the text label is used.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="compatibility-options">
<h3>Compatibility Options<a class="headerlink" href="#compatibility-options" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>state</p></td>
<td><p>May be set to “normal” or “disabled” to control the “disabled”
state bit. This is a write-only option: setting it changes the
widget state, but the <a class="reference internal" href="#tkinter.ttk.Widget.state" title="tkinter.ttk.Widget.state"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.state()</span></code></a> method does not
affect this option.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="widget-states">
<h3>Widget States<a class="headerlink" href="#widget-states" title="Link to this heading">¶</a></h3>
<p>The widget state is a bitmap of independent state flags.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Flag</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>active</p></td>
<td><p>The mouse cursor is over the widget and pressing a mouse
button will cause some action to occur</p></td>
</tr>
<tr class="row-odd"><td><p>disabled</p></td>
<td><p>Widget is disabled under program control</p></td>
</tr>
<tr class="row-even"><td><p>focus</p></td>
<td><p>Widget has keyboard focus</p></td>
</tr>
<tr class="row-odd"><td><p>pressed</p></td>
<td><p>Widget is being pressed</p></td>
</tr>
<tr class="row-even"><td><p>selected</p></td>
<td><p>“On”, “true”, or “current” for things like Checkbuttons and
radiobuttons</p></td>
</tr>
<tr class="row-odd"><td><p>background</p></td>
<td><p>Windows and Mac have a notion of an “active” or foreground
window. The <em>background</em> state is set for widgets in a
background window, and cleared for those in the foreground
window</p></td>
</tr>
<tr class="row-even"><td><p>readonly</p></td>
<td><p>Widget should not allow user modification</p></td>
</tr>
<tr class="row-odd"><td><p>alternate</p></td>
<td><p>A widget-specific alternate display format</p></td>
</tr>
<tr class="row-even"><td><p>invalid</p></td>
<td><p>The widget’s value is invalid</p></td>
</tr>
</tbody>
</table>
<p>A state specification is a sequence of state names, optionally prefixed with
an exclamation point indicating that the bit is off.</p>
</section>
<section id="ttk-widget">
<h3>ttk.Widget<a class="headerlink" href="#ttk-widget" title="Link to this heading">¶</a></h3>
<p>Besides the methods described below, the <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Widget</span></code> supports the
methods <code class="xref py py-meth docutils literal notranslate"><span class="pre">tkinter.Widget.cget()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">tkinter.Widget.configure()</span></code>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Widget">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Widget</span></span><a class="headerlink" href="#tkinter.ttk.Widget" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Widget.identify">
<span class="sig-name descname"><span class="pre">identify</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Widget.identify" title="Link to this definition">¶</a></dt>
<dd><p>Returns the name of the element at position <em>x</em> <em>y</em>, or the empty string
if the point does not lie within any element.</p>
<p><em>x</em> and <em>y</em> are pixel coordinates relative to the widget.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Widget.instate">
<span class="sig-name descname"><span class="pre">instate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">statespec</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Widget.instate" title="Link to this definition">¶</a></dt>
<dd><p>Test the widget’s state. If a callback is not specified, returns <code class="docutils literal notranslate"><span class="pre">True</span></code>
if the widget state matches <em>statespec</em> and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise. If callback
is specified then it is called with args if widget state matches
<em>statespec</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Widget.state">
<span class="sig-name descname"><span class="pre">state</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">statespec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Widget.state" title="Link to this definition">¶</a></dt>
<dd><p>Modify or inquire widget state. If <em>statespec</em> is specified, sets the
widget state according to it and return a new <em>statespec</em> indicating
which flags were changed. If <em>statespec</em> is not specified, returns
the currently enabled state flags.</p>
</dd></dl>

<p><em>statespec</em> will usually be a list or a tuple.</p>
</dd></dl>

</section>
</section>
<section id="combobox">
<h2>Combobox<a class="headerlink" href="#combobox" title="Link to this heading">¶</a></h2>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Combobox</span></code> widget combines a text field with a pop-down list of
values. This widget is a subclass of <code class="xref py py-class docutils literal notranslate"><span class="pre">Entry</span></code>.</p>
<p>Besides the methods inherited from <a class="reference internal" href="#tkinter.ttk.Widget" title="tkinter.ttk.Widget"><code class="xref py py-class docutils literal notranslate"><span class="pre">Widget</span></code></a>: <code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.cget()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.configure()</span></code>, <a class="reference internal" href="#tkinter.ttk.Widget.identify" title="tkinter.ttk.Widget.identify"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.identify()</span></code></a>, <a class="reference internal" href="#tkinter.ttk.Widget.instate" title="tkinter.ttk.Widget.instate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.instate()</span></code></a>
and <a class="reference internal" href="#tkinter.ttk.Widget.state" title="tkinter.ttk.Widget.state"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.state()</span></code></a>, and the following inherited from <code class="xref py py-class docutils literal notranslate"><span class="pre">Entry</span></code>:
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.bbox()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.delete()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.icursor()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.index()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.insert()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.selection()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.xview()</span></code>, it has some other methods, described at
<code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Combobox</span></code>.</p>
<section id="options">
<h3>Options<a class="headerlink" href="#options" title="Link to this heading">¶</a></h3>
<p>This widget accepts the following specific options:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>exportselection</p></td>
<td><p>Boolean value. If set, the widget selection is linked
to the Window Manager selection (which can be returned
by invoking Misc.selection_get, for example).</p></td>
</tr>
<tr class="row-odd"><td><p>justify</p></td>
<td><p>Specifies how the text is aligned within the widget.
One of “left”, “center”, or “right”.</p></td>
</tr>
<tr class="row-even"><td><p>height</p></td>
<td><p>Specifies the height of the pop-down listbox, in rows.</p></td>
</tr>
<tr class="row-odd"><td><p>postcommand</p></td>
<td><p>A script (possibly registered with Misc.register) that
is called immediately before displaying the values. It
may specify which values to display.</p></td>
</tr>
<tr class="row-even"><td><p>state</p></td>
<td><p>One of “normal”, “readonly”, or “disabled”. In the
“readonly” state, the value may not be edited directly,
and the user can only selection of the values from the
dropdown list. In the “normal” state, the text field is
directly editable. In the “disabled” state, no
interaction is possible.</p></td>
</tr>
<tr class="row-odd"><td><p>textvariable</p></td>
<td><p>Specifies a name whose value is linked to the widget
value. Whenever the value associated with that name
changes, the widget value is updated, and vice versa.
See <code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.StringVar</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p>values</p></td>
<td><p>Specifies the list of values to display in the
drop-down listbox.</p></td>
</tr>
<tr class="row-odd"><td><p>width</p></td>
<td><p>Specifies an integer value indicating the desired width
of the entry window, in average-size characters of the
widget’s font.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="virtual-events">
<h3>Virtual events<a class="headerlink" href="#virtual-events" title="Link to this heading">¶</a></h3>
<p>The combobox widgets generates a <strong>&lt;&lt;ComboboxSelected&gt;&gt;</strong> virtual event
when the user selects an element from the list of values.</p>
</section>
<section id="ttk-combobox">
<h3>ttk.Combobox<a class="headerlink" href="#ttk-combobox" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Combobox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Combobox</span></span><a class="headerlink" href="#tkinter.ttk.Combobox" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Combobox.current">
<span class="sig-name descname"><span class="pre">current</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newindex</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Combobox.current" title="Link to this definition">¶</a></dt>
<dd><p>If <em>newindex</em> is specified, sets the combobox value to the element
position <em>newindex</em>. Otherwise, returns the index of the current value or
-1 if the current value is not in the values list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Combobox.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Combobox.get" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current value of the combobox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Combobox.set">
<span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Combobox.set" title="Link to this definition">¶</a></dt>
<dd><p>Sets the value of the combobox to <em>value</em>.</p>
</dd></dl>

</dd></dl>

</section>
</section>
<section id="spinbox">
<h2>Spinbox<a class="headerlink" href="#spinbox" title="Link to this heading">¶</a></h2>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Spinbox</span></code> widget is a <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Entry</span></code> enhanced with increment
and decrement arrows.  It can be used for numbers or lists of string values.
This widget is a subclass of <code class="xref py py-class docutils literal notranslate"><span class="pre">Entry</span></code>.</p>
<p>Besides the methods inherited from <a class="reference internal" href="#tkinter.ttk.Widget" title="tkinter.ttk.Widget"><code class="xref py py-class docutils literal notranslate"><span class="pre">Widget</span></code></a>: <code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.cget()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.configure()</span></code>, <a class="reference internal" href="#tkinter.ttk.Widget.identify" title="tkinter.ttk.Widget.identify"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.identify()</span></code></a>, <a class="reference internal" href="#tkinter.ttk.Widget.instate" title="tkinter.ttk.Widget.instate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.instate()</span></code></a>
and <a class="reference internal" href="#tkinter.ttk.Widget.state" title="tkinter.ttk.Widget.state"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.state()</span></code></a>, and the following inherited from <code class="xref py py-class docutils literal notranslate"><span class="pre">Entry</span></code>:
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.bbox()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.delete()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.icursor()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.index()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.insert()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">Entry.xview()</span></code>,
it has some other methods, described at <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Spinbox</span></code>.</p>
<section id="id1">
<h3>Options<a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<p>This widget accepts the following specific options:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>from</p></td>
<td><p>Float value.  If set, this is the minimum value to
which the decrement button will decrement.  Must be
spelled as <code class="docutils literal notranslate"><span class="pre">from_</span></code> when used as an argument, since
<code class="docutils literal notranslate"><span class="pre">from</span></code> is a Python keyword.</p></td>
</tr>
<tr class="row-odd"><td><p>to</p></td>
<td><p>Float value.  If set, this is the maximum value to
which the increment button will increment.</p></td>
</tr>
<tr class="row-even"><td><p>increment</p></td>
<td><p>Float value.  Specifies the amount which the
increment/decrement buttons change the
value. Defaults to 1.0.</p></td>
</tr>
<tr class="row-odd"><td><p>values</p></td>
<td><p>Sequence of string or float values.  If specified,
the increment/decrement buttons will cycle through
the items in this sequence rather than incrementing
or decrementing numbers.</p></td>
</tr>
<tr class="row-even"><td><p>wrap</p></td>
<td><p>Boolean value.  If <code class="docutils literal notranslate"><span class="pre">True</span></code>, increment and decrement
buttons will cycle from the <code class="docutils literal notranslate"><span class="pre">to</span></code> value to the
<code class="docutils literal notranslate"><span class="pre">from</span></code> value or the <code class="docutils literal notranslate"><span class="pre">from</span></code> value to the <code class="docutils literal notranslate"><span class="pre">to</span></code>
value, respectively.</p></td>
</tr>
<tr class="row-odd"><td><p>format</p></td>
<td><p>String value.  This specifies the format of numbers
set by the increment/decrement buttons.  It must be
in the form “%W.Pf”, where W is the padded width of
the value, P is the precision, and ‘%’ and ‘f’ are
literal.</p></td>
</tr>
<tr class="row-even"><td><p>command</p></td>
<td><p>Python callable.  Will be called with no arguments
whenever either of the increment or decrement buttons
are pressed.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="id2">
<h3>Virtual events<a class="headerlink" href="#id2" title="Link to this heading">¶</a></h3>
<p>The spinbox widget generates an <strong>&lt;&lt;Increment&gt;&gt;</strong> virtual event when the
user presses &lt;Up&gt;, and a <strong>&lt;&lt;Decrement&gt;&gt;</strong> virtual event when the user
presses &lt;Down&gt;.</p>
</section>
<section id="ttk-spinbox">
<h3>ttk.Spinbox<a class="headerlink" href="#ttk-spinbox" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Spinbox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Spinbox</span></span><a class="headerlink" href="#tkinter.ttk.Spinbox" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Spinbox.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Spinbox.get" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current value of the spinbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Spinbox.set">
<span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Spinbox.set" title="Link to this definition">¶</a></dt>
<dd><p>Sets the value of the spinbox to <em>value</em>.</p>
</dd></dl>

</dd></dl>

</section>
</section>
<section id="notebook">
<h2>Notebook<a class="headerlink" href="#notebook" title="Link to this heading">¶</a></h2>
<p>Ttk Notebook widget manages a collection of windows and displays a single
one at a time. Each child window is associated with a tab, which the user
may select to change the currently displayed window.</p>
<section id="id3">
<h3>Options<a class="headerlink" href="#id3" title="Link to this heading">¶</a></h3>
<p>This widget accepts the following specific options:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>height</p></td>
<td><p>If present and greater than zero, specifies the desired height
of the pane area (not including internal padding or tabs).
Otherwise, the maximum height of all panes is used.</p></td>
</tr>
<tr class="row-odd"><td><p>padding</p></td>
<td><p>Specifies the amount of extra space to add around the outside
of the notebook. The padding is a list up to four length
specifications left top right bottom. If fewer than four
elements are specified, bottom defaults to top, right defaults
to left, and top defaults to left.</p></td>
</tr>
<tr class="row-even"><td><p>width</p></td>
<td><p>If present and greater than zero, specified the desired width
of the pane area (not including internal padding). Otherwise,
the maximum width of all panes is used.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="tab-options">
<h3>Tab Options<a class="headerlink" href="#tab-options" title="Link to this heading">¶</a></h3>
<p>There are also specific options for tabs:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>state</p></td>
<td><p>Either “normal”, “disabled” or “hidden”. If “disabled”, then
the tab is not selectable. If “hidden”, then the tab is not
shown.</p></td>
</tr>
<tr class="row-odd"><td><p>sticky</p></td>
<td><p>Specifies how the child window is positioned within the pane
area. Value is a string containing zero or more of the
characters “n”, “s”, “e” or “w”. Each letter refers to a
side (north, south, east or west) that the child window will
stick to, as per the <code class="xref py py-meth docutils literal notranslate"><span class="pre">grid()</span></code> geometry manager.</p></td>
</tr>
<tr class="row-even"><td><p>padding</p></td>
<td><p>Specifies the amount of extra space to add between the
notebook and this pane. Syntax is the same as for the option
padding used by this widget.</p></td>
</tr>
<tr class="row-odd"><td><p>text</p></td>
<td><p>Specifies a text to be displayed in the tab.</p></td>
</tr>
<tr class="row-even"><td><p>image</p></td>
<td><p>Specifies an image to display in the tab. See the option
image described in <a class="reference internal" href="#tkinter.ttk.Widget" title="tkinter.ttk.Widget"><code class="xref py py-class docutils literal notranslate"><span class="pre">Widget</span></code></a>.</p></td>
</tr>
<tr class="row-odd"><td><p>compound</p></td>
<td><p>Specifies how to display the image relative to the text, in
the case both options text and image are present. See
<a class="reference internal" href="#label-options">Label Options</a> for legal values.</p></td>
</tr>
<tr class="row-even"><td><p>underline</p></td>
<td><p>Specifies the index (0-based) of a character to underline in
the text string. The underlined character is used for
mnemonic activation if <a class="reference internal" href="#tkinter.ttk.Notebook.enable_traversal" title="tkinter.ttk.Notebook.enable_traversal"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Notebook.enable_traversal()</span></code></a> is
called.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="tab-identifiers">
<h3>Tab Identifiers<a class="headerlink" href="#tab-identifiers" title="Link to this heading">¶</a></h3>
<p>The tab_id present in several methods of <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Notebook</span></code> may take any
of the following forms:</p>
<ul class="simple">
<li><p>An integer between zero and the number of tabs</p></li>
<li><p>The name of a child window</p></li>
<li><p>A positional specification of the form “&#64;x,y”, which identifies the tab</p></li>
<li><p>The literal string “current”, which identifies the currently selected tab</p></li>
<li><p>The literal string “end”, which returns the number of tabs (only valid for
<a class="reference internal" href="#tkinter.ttk.Notebook.index" title="tkinter.ttk.Notebook.index"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Notebook.index()</span></code></a>)</p></li>
</ul>
</section>
<section id="id4">
<h3>Virtual Events<a class="headerlink" href="#id4" title="Link to this heading">¶</a></h3>
<p>This widget generates a <strong>&lt;&lt;NotebookTabChanged&gt;&gt;</strong> virtual event after a new
tab is selected.</p>
</section>
<section id="ttk-notebook">
<h3>ttk.Notebook<a class="headerlink" href="#ttk-notebook" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Notebook</span></span><a class="headerlink" href="#tkinter.ttk.Notebook" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.add">
<span class="sig-name descname"><span class="pre">add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">child</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.add" title="Link to this definition">¶</a></dt>
<dd><p>Adds a new tab to the notebook.</p>
<p>If window is currently managed by the notebook but hidden, it is
restored to its previous position.</p>
<p>See <a class="reference internal" href="#tab-options">Tab Options</a> for the list of available options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.forget">
<span class="sig-name descname"><span class="pre">forget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tab_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.forget" title="Link to this definition">¶</a></dt>
<dd><p>Removes the tab specified by <em>tab_id</em>, unmaps and unmanages the
associated window.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.hide">
<span class="sig-name descname"><span class="pre">hide</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tab_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.hide" title="Link to this definition">¶</a></dt>
<dd><p>Hides the tab specified by <em>tab_id</em>.</p>
<p>The tab will not be displayed, but the associated window remains
managed by the notebook and its configuration remembered. Hidden tabs
may be restored with the <a class="reference internal" href="#tkinter.ttk.Notebook.add" title="tkinter.ttk.Notebook.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add()</span></code></a> command.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.identify">
<span class="sig-name descname"><span class="pre">identify</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.identify" title="Link to this definition">¶</a></dt>
<dd><p>Returns the name of the tab element at position <em>x</em>, <em>y</em>, or the empty
string if none.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.index">
<span class="sig-name descname"><span class="pre">index</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tab_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.index" title="Link to this definition">¶</a></dt>
<dd><p>Returns the numeric index of the tab specified by <em>tab_id</em>, or the total
number of tabs if <em>tab_id</em> is the string “end”.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.insert">
<span class="sig-name descname"><span class="pre">insert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">child</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.insert" title="Link to this definition">¶</a></dt>
<dd><p>Inserts a pane at the specified position.</p>
<p><em>pos</em> is either the string “end”, an integer index, or the name of a
managed child. If <em>child</em> is already managed by the notebook, moves it to
the specified position.</p>
<p>See <a class="reference internal" href="#tab-options">Tab Options</a> for the list of available options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.select">
<span class="sig-name descname"><span class="pre">select</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tab_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.select" title="Link to this definition">¶</a></dt>
<dd><p>Selects the specified <em>tab_id</em>.</p>
<p>The associated child window will be displayed, and the
previously selected window (if different) is unmapped. If <em>tab_id</em> is
omitted, returns the widget name of the currently selected pane.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.tab">
<span class="sig-name descname"><span class="pre">tab</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tab_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.tab" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify the options of the specific <em>tab_id</em>.</p>
<p>If <em>kw</em> is not given, returns a dictionary of the tab option values. If
<em>option</em> is specified, returns the value of that <em>option</em>. Otherwise,
sets the options to the corresponding values.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.tabs">
<span class="sig-name descname"><span class="pre">tabs</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.tabs" title="Link to this definition">¶</a></dt>
<dd><p>Returns a list of windows managed by the notebook.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Notebook.enable_traversal">
<span class="sig-name descname"><span class="pre">enable_traversal</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Notebook.enable_traversal" title="Link to this definition">¶</a></dt>
<dd><p>Enable keyboard traversal for a toplevel window containing this notebook.</p>
<p>This will extend the bindings for the toplevel window containing the
notebook as follows:</p>
<ul class="simple">
<li><p><kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>-<kbd class="kbd docutils literal notranslate">Tab</kbd></kbd>: selects the tab following the currently selected one.</p></li>
<li><p><kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Shift</kbd>-<kbd class="kbd docutils literal notranslate">Control</kbd>-<kbd class="kbd docutils literal notranslate">Tab</kbd></kbd>: selects the tab preceding the currently selected one.</p></li>
<li><p><kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Alt</kbd>-<kbd class="kbd docutils literal notranslate">K</kbd></kbd>: where <em>K</em> is the mnemonic (underlined) character of any tab, will
select that tab.</p></li>
</ul>
<p>Multiple notebooks in a single toplevel may be enabled for traversal,
including nested notebooks. However, notebook traversal only works
properly if all panes have the notebook they are in as master.</p>
</dd></dl>

</dd></dl>

</section>
</section>
<section id="progressbar">
<h2>Progressbar<a class="headerlink" href="#progressbar" title="Link to this heading">¶</a></h2>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Progressbar</span></code> widget shows the status of a long-running
operation. It can operate in two modes:  1) the determinate mode which shows the
amount completed relative to the total amount of work to be done and 2) the
indeterminate mode which provides an animated display to let the user know that
work is progressing.</p>
<section id="id5">
<h3>Options<a class="headerlink" href="#id5" title="Link to this heading">¶</a></h3>
<p>This widget accepts the following specific options:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>orient</p></td>
<td><p>One of “horizontal” or “vertical”. Specifies the orientation
of the progress bar.</p></td>
</tr>
<tr class="row-odd"><td><p>length</p></td>
<td><p>Specifies the length of the long axis of the progress bar
(width if horizontal, height if vertical).</p></td>
</tr>
<tr class="row-even"><td><p>mode</p></td>
<td><p>One of “determinate” or “indeterminate”.</p></td>
</tr>
<tr class="row-odd"><td><p>maximum</p></td>
<td><p>A number specifying the maximum value. Defaults to 100.</p></td>
</tr>
<tr class="row-even"><td><p>value</p></td>
<td><p>The current value of the progress bar. In “determinate” mode,
this represents the amount of work completed. In
“indeterminate” mode, it is interpreted as modulo <em>maximum</em>;
that is, the progress bar completes one “cycle” when its value
increases by <em>maximum</em>.</p></td>
</tr>
<tr class="row-odd"><td><p>variable</p></td>
<td><p>A name which is linked to the option value. If specified, the
value of the progress bar is automatically set to the value of
this name whenever the latter is modified.</p></td>
</tr>
<tr class="row-even"><td><p>phase</p></td>
<td><p>Read-only option. The widget periodically increments the value
of this option whenever its value is greater than 0 and, in
determinate mode, less than maximum. This option may be used
by the current theme to provide additional animation effects.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="ttk-progressbar">
<h3>ttk.Progressbar<a class="headerlink" href="#ttk-progressbar" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Progressbar">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Progressbar</span></span><a class="headerlink" href="#tkinter.ttk.Progressbar" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Progressbar.start">
<span class="sig-name descname"><span class="pre">start</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">interval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Progressbar.start" title="Link to this definition">¶</a></dt>
<dd><p>Begin autoincrement mode: schedules a recurring timer event that calls
<a class="reference internal" href="#tkinter.ttk.Progressbar.step" title="tkinter.ttk.Progressbar.step"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Progressbar.step()</span></code></a> every <em>interval</em> milliseconds. If omitted,
<em>interval</em> defaults to 50 milliseconds.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Progressbar.step">
<span class="sig-name descname"><span class="pre">step</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">amount</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Progressbar.step" title="Link to this definition">¶</a></dt>
<dd><p>Increments the progress bar’s value by <em>amount</em>.</p>
<p><em>amount</em> defaults to 1.0 if omitted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Progressbar.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Progressbar.stop" title="Link to this definition">¶</a></dt>
<dd><p>Stop autoincrement mode: cancels any recurring timer event initiated by
<a class="reference internal" href="#tkinter.ttk.Progressbar.start" title="tkinter.ttk.Progressbar.start"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Progressbar.start()</span></code></a> for this progress bar.</p>
</dd></dl>

</dd></dl>

</section>
</section>
<section id="separator">
<h2>Separator<a class="headerlink" href="#separator" title="Link to this heading">¶</a></h2>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Separator</span></code> widget displays a horizontal or vertical separator
bar.</p>
<p>It has no other methods besides the ones inherited from <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Widget</span></code>.</p>
<section id="id6">
<h3>Options<a class="headerlink" href="#id6" title="Link to this heading">¶</a></h3>
<p>This widget accepts the following specific option:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>orient</p></td>
<td><p>One of “horizontal” or “vertical”. Specifies the orientation of
the separator.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="sizegrip">
<h2>Sizegrip<a class="headerlink" href="#sizegrip" title="Link to this heading">¶</a></h2>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Sizegrip</span></code> widget (also known as a grow box) allows the user to
resize the containing toplevel window by pressing and dragging the grip.</p>
<p>This widget has neither specific options nor specific methods, besides the
ones inherited from <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Widget</span></code>.</p>
<section id="platform-specific-notes">
<h3>Platform-specific notes<a class="headerlink" href="#platform-specific-notes" title="Link to this heading">¶</a></h3>
<ul class="simple">
<li><p>On macOS, toplevel windows automatically include a built-in size grip
by default. Adding a <code class="xref py py-class docutils literal notranslate"><span class="pre">Sizegrip</span></code> is harmless, since the built-in
grip will just mask the widget.</p></li>
</ul>
</section>
<section id="bugs">
<h3>Bugs<a class="headerlink" href="#bugs" title="Link to this heading">¶</a></h3>
<ul class="simple">
<li><p>If the containing toplevel’s position was specified relative to the right
or bottom of the screen (e.g. ….), the <code class="xref py py-class docutils literal notranslate"><span class="pre">Sizegrip</span></code> widget will
not resize the window.</p></li>
<li><p>This widget supports only “southeast” resizing.</p></li>
</ul>
</section>
</section>
<section id="treeview">
<h2>Treeview<a class="headerlink" href="#treeview" title="Link to this heading">¶</a></h2>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Treeview</span></code> widget displays a hierarchical collection of items.
Each item has a textual label, an optional image, and an optional list of data
values. The data values are displayed in successive columns after the tree
label.</p>
<p>The order in which data values are displayed may be controlled by setting
the widget option <code class="docutils literal notranslate"><span class="pre">displaycolumns</span></code>. The tree widget can also display column
headings. Columns may be accessed by number or symbolic names listed in the
widget option columns. See <a class="reference internal" href="#column-identifiers">Column Identifiers</a>.</p>
<p>Each item is identified by a unique name. The widget will generate item IDs
if they are not supplied by the caller. There is a distinguished root item,
named <code class="docutils literal notranslate"><span class="pre">{}</span></code>. The root item itself is not displayed; its children appear at the
top level of the hierarchy.</p>
<p>Each item also has a list of tags, which can be used to associate event bindings
with individual items and control the appearance of the item.</p>
<p>The Treeview widget supports horizontal and vertical scrolling, according to
the options described in <a class="reference internal" href="#scrollable-widget-options">Scrollable Widget Options</a> and the methods
<a class="reference internal" href="#tkinter.ttk.Treeview.xview" title="tkinter.ttk.Treeview.xview"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Treeview.xview()</span></code></a> and <a class="reference internal" href="#tkinter.ttk.Treeview.yview" title="tkinter.ttk.Treeview.yview"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Treeview.yview()</span></code></a>.</p>
<section id="id7">
<h3>Options<a class="headerlink" href="#id7" title="Link to this heading">¶</a></h3>
<p>This widget accepts the following specific options:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>columns</p></td>
<td><p>A list of column identifiers, specifying the number of
columns and their names.</p></td>
</tr>
<tr class="row-odd"><td><p>displaycolumns</p></td>
<td><p>A list of column identifiers (either symbolic or
integer indices) specifying which data columns are
displayed and the order in which they appear, or the
string “#all”.</p></td>
</tr>
<tr class="row-even"><td><p>height</p></td>
<td><p>Specifies the number of rows which should be visible.
Note: the requested width is determined from the sum
of the column widths.</p></td>
</tr>
<tr class="row-odd"><td><p>padding</p></td>
<td><p>Specifies the internal padding for the widget. The
padding is a list of up to four length specifications.</p></td>
</tr>
<tr class="row-even"><td><p>selectmode</p></td>
<td><p>Controls how the built-in class bindings manage the
selection. One of “extended”, “browse” or “none”.
If set to “extended” (the default), multiple items may
be selected. If “browse”, only a single item will be
selected at a time. If “none”, the selection will not
be changed.</p>
<p>Note that the application code and tag bindings can set
the selection however they wish, regardless of the
value  of this option.</p>
</td>
</tr>
<tr class="row-odd"><td><p>show</p></td>
<td><p>A list containing zero or more of the following values,
specifying which elements of the tree to display.</p>
<ul class="simple">
<li><p>tree: display tree labels in column #0.</p></li>
<li><p>headings: display the heading row.</p></li>
</ul>
<p>The default is “tree headings”, i.e., show all
elements.</p>
<p><strong>Note</strong>: Column #0 always refers to the tree column,
even if show=”tree” is not specified.</p>
</td>
</tr>
</tbody>
</table>
</section>
<section id="item-options">
<h3>Item Options<a class="headerlink" href="#item-options" title="Link to this heading">¶</a></h3>
<p>The following item options may be specified for items in the insert and item
widget commands.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>text</p></td>
<td><p>The textual label to display for the item.</p></td>
</tr>
<tr class="row-odd"><td><p>image</p></td>
<td><p>A Tk Image, displayed to the left of the label.</p></td>
</tr>
<tr class="row-even"><td><p>values</p></td>
<td><p>The list of values associated with the item.</p>
<p>Each item should have the same number of values as the widget
option columns. If there are fewer values than columns, the
remaining values are assumed empty. If there are more values
than columns, the extra values are ignored.</p>
</td>
</tr>
<tr class="row-odd"><td><p>open</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">True</span></code>/<code class="docutils literal notranslate"><span class="pre">False</span></code> value indicating whether the item’s
children should be displayed or hidden.</p></td>
</tr>
<tr class="row-even"><td><p>tags</p></td>
<td><p>A list of tags associated with this item.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="tag-options">
<h3>Tag Options<a class="headerlink" href="#tag-options" title="Link to this heading">¶</a></h3>
<p>The following options may be specified on tags:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>foreground</p></td>
<td><p>Specifies the text foreground color.</p></td>
</tr>
<tr class="row-odd"><td><p>background</p></td>
<td><p>Specifies the cell or item background color.</p></td>
</tr>
<tr class="row-even"><td><p>font</p></td>
<td><p>Specifies the font to use when drawing text.</p></td>
</tr>
<tr class="row-odd"><td><p>image</p></td>
<td><p>Specifies the item image, in case the item’s image option
is empty.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="column-identifiers">
<h3>Column Identifiers<a class="headerlink" href="#column-identifiers" title="Link to this heading">¶</a></h3>
<p>Column identifiers take any of the following forms:</p>
<ul class="simple">
<li><p>A symbolic name from the list of columns option.</p></li>
<li><p>An integer n, specifying the nth data column.</p></li>
<li><p>A string of the form #n, where n is an integer, specifying the nth display
column.</p></li>
</ul>
<p>Notes:</p>
<ul class="simple">
<li><p>Item’s option values may be displayed in a different order than the order
in which they are stored.</p></li>
<li><p>Column #0 always refers to the tree column, even if show=”tree” is not
specified.</p></li>
</ul>
<p>A data column number is an index into an item’s option values list; a display
column number is the column number in the tree where the values are displayed.
Tree labels are displayed in column #0. If option displaycolumns is not set,
then data column n is displayed in column #n+1. Again, <strong>column #0 always
refers to the tree column</strong>.</p>
</section>
<section id="id8">
<h3>Virtual Events<a class="headerlink" href="#id8" title="Link to this heading">¶</a></h3>
<p>The Treeview widget generates the following virtual events.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Event</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>&lt;&lt;TreeviewSelect&gt;&gt;</p></td>
<td><p>Generated whenever the selection changes.</p></td>
</tr>
<tr class="row-odd"><td><p>&lt;&lt;TreeviewOpen&gt;&gt;</p></td>
<td><p>Generated just before settings the focus item to
open=True.</p></td>
</tr>
<tr class="row-even"><td><p>&lt;&lt;TreeviewClose&gt;&gt;</p></td>
<td><p>Generated just after setting the focus item to
open=False.</p></td>
</tr>
</tbody>
</table>
<p>The <a class="reference internal" href="#tkinter.ttk.Treeview.focus" title="tkinter.ttk.Treeview.focus"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Treeview.focus()</span></code></a> and <a class="reference internal" href="#tkinter.ttk.Treeview.selection" title="tkinter.ttk.Treeview.selection"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Treeview.selection()</span></code></a> methods can be used
to determine the affected item or items.</p>
</section>
<section id="ttk-treeview">
<h3>ttk.Treeview<a class="headerlink" href="#ttk-treeview" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Treeview</span></span><a class="headerlink" href="#tkinter.ttk.Treeview" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.bbox">
<span class="sig-name descname"><span class="pre">bbox</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.bbox" title="Link to this definition">¶</a></dt>
<dd><p>Returns the bounding box (relative to the treeview widget’s window) of
the specified <em>item</em> in the form (x, y, width, height).</p>
<p>If <em>column</em> is specified, returns the bounding box of that cell. If the
<em>item</em> is not visible (i.e., if it is a descendant of a closed item or is
scrolled offscreen), returns an empty string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.get_children">
<span class="sig-name descname"><span class="pre">get_children</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.get_children" title="Link to this definition">¶</a></dt>
<dd><p>Returns the list of children belonging to <em>item</em>.</p>
<p>If <em>item</em> is not specified, returns root children.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.set_children">
<span class="sig-name descname"><span class="pre">set_children</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">newchildren</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.set_children" title="Link to this definition">¶</a></dt>
<dd><p>Replaces <em>item</em>’s child with <em>newchildren</em>.</p>
<p>Children present in <em>item</em> that are not present in <em>newchildren</em> are
detached from the tree. No items in <em>newchildren</em> may be an ancestor of
<em>item</em>. Note that not specifying <em>newchildren</em> results in detaching
<em>item</em>’s children.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.column">
<span class="sig-name descname"><span class="pre">column</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.column" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify the options for the specified <em>column</em>.</p>
<p>If <em>kw</em> is not given, returns a dict of the column option values. If
<em>option</em> is specified then the value for that <em>option</em> is returned.
Otherwise, sets the options to the corresponding values.</p>
<p>The valid options/values are:</p>
<dl class="simple">
<dt><em>id</em></dt><dd><p>Returns the column name. This is a read-only option.</p>
</dd>
<dt><em>anchor</em>: One of the standard Tk anchor values.</dt><dd><p>Specifies how the text in this column should be aligned with respect
to the cell.</p>
</dd>
<dt><em>minwidth</em>: width</dt><dd><p>The minimum width of the column in pixels. The treeview widget will
not make the column any smaller than specified by this option when
the widget is resized or the user drags a column.</p>
</dd>
<dt><em>stretch</em>: <code class="docutils literal notranslate"><span class="pre">True</span></code>/<code class="docutils literal notranslate"><span class="pre">False</span></code></dt><dd><p>Specifies whether the column’s width should be adjusted when
the widget is resized.</p>
</dd>
<dt><em>width</em>: width</dt><dd><p>The width of the column in pixels.</p>
</dd>
</dl>
<p>To configure the tree column, call this with column = “#0”</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.delete">
<span class="sig-name descname"><span class="pre">delete</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.delete" title="Link to this definition">¶</a></dt>
<dd><p>Delete all specified <em>items</em> and all their descendants.</p>
<p>The root item may not be deleted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.detach">
<span class="sig-name descname"><span class="pre">detach</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.detach" title="Link to this definition">¶</a></dt>
<dd><p>Unlinks all of the specified <em>items</em> from the tree.</p>
<p>The items and all of their descendants are still present, and may be
reinserted at another point in the tree, but will not be displayed.</p>
<p>The root item may not be detached.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.exists">
<span class="sig-name descname"><span class="pre">exists</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.exists" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the specified <em>item</em> is present in the tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.focus">
<span class="sig-name descname"><span class="pre">focus</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.focus" title="Link to this definition">¶</a></dt>
<dd><p>If <em>item</em> is specified, sets the focus item to <em>item</em>. Otherwise, returns
the current focus item, or ‘’ if there is none.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.heading">
<span class="sig-name descname"><span class="pre">heading</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.heading" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify the heading options for the specified <em>column</em>.</p>
<p>If <em>kw</em> is not given, returns a dict of the heading option values. If
<em>option</em> is specified then the value for that <em>option</em> is returned.
Otherwise, sets the options to the corresponding values.</p>
<p>The valid options/values are:</p>
<dl class="simple">
<dt><em>text</em>: text</dt><dd><p>The text to display in the column heading.</p>
</dd>
<dt><em>image</em>: imageName</dt><dd><p>Specifies an image to display to the right of the column heading.</p>
</dd>
<dt><em>anchor</em>: anchor</dt><dd><p>Specifies how the heading text should be aligned. One of the standard
Tk anchor values.</p>
</dd>
<dt><em>command</em>: callback</dt><dd><p>A callback to be invoked when the heading label is pressed.</p>
</dd>
</dl>
<p>To configure the tree column heading, call this with column = “#0”.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.identify">
<span class="sig-name descname"><span class="pre">identify</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">component</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.identify" title="Link to this definition">¶</a></dt>
<dd><p>Returns a description of the specified <em>component</em> under the point given
by <em>x</em> and <em>y</em>, or the empty string if no such <em>component</em> is present at
that position.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.identify_row">
<span class="sig-name descname"><span class="pre">identify_row</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.identify_row" title="Link to this definition">¶</a></dt>
<dd><p>Returns the item ID of the item at position <em>y</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.identify_column">
<span class="sig-name descname"><span class="pre">identify_column</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.identify_column" title="Link to this definition">¶</a></dt>
<dd><p>Returns the data column identifier of the cell at position <em>x</em>.</p>
<p>The tree column has ID #0.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.identify_region">
<span class="sig-name descname"><span class="pre">identify_region</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.identify_region" title="Link to this definition">¶</a></dt>
<dd><p>Returns one of:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>region</p></th>
<th class="head"><p>meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>heading</p></td>
<td><p>Tree heading area.</p></td>
</tr>
<tr class="row-odd"><td><p>separator</p></td>
<td><p>Space between two columns headings.</p></td>
</tr>
<tr class="row-even"><td><p>tree</p></td>
<td><p>The tree area.</p></td>
</tr>
<tr class="row-odd"><td><p>cell</p></td>
<td><p>A data cell.</p></td>
</tr>
</tbody>
</table>
<p>Availability: Tk 8.6.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.identify_element">
<span class="sig-name descname"><span class="pre">identify_element</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.identify_element" title="Link to this definition">¶</a></dt>
<dd><p>Returns the element at position <em>x</em>, <em>y</em>.</p>
<p>Availability: Tk 8.6.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.index">
<span class="sig-name descname"><span class="pre">index</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.index" title="Link to this definition">¶</a></dt>
<dd><p>Returns the integer index of <em>item</em> within its parent’s list of children.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.insert">
<span class="sig-name descname"><span class="pre">insert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.insert" title="Link to this definition">¶</a></dt>
<dd><p>Creates a new item and returns the item identifier of the newly created
item.</p>
<p><em>parent</em> is the item ID of the parent item, or the empty string to create
a new top-level item. <em>index</em> is an integer, or the value “end”,
specifying where in the list of parent’s children to insert the new item.
If <em>index</em> is less than or equal to zero, the new node is inserted at
the beginning; if <em>index</em> is greater than or equal to the current number
of children, it is inserted at the end. If <em>iid</em> is specified, it is used
as the item identifier; <em>iid</em> must not already exist in the tree.
Otherwise, a new unique identifier is generated.</p>
<p>See <a class="reference internal" href="#item-options">Item Options</a> for the list of available options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.item">
<span class="sig-name descname"><span class="pre">item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.item" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify the options for the specified <em>item</em>.</p>
<p>If no options are given, a dict with options/values for the item is
returned.
If <em>option</em> is specified then the value for that option is returned.
Otherwise, sets the options to the corresponding values as given by <em>kw</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.move">
<span class="sig-name descname"><span class="pre">move</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.move" title="Link to this definition">¶</a></dt>
<dd><p>Moves <em>item</em> to position <em>index</em> in <em>parent</em>’s list of children.</p>
<p>It is illegal to move an item under one of its descendants. If <em>index</em> is
less than or equal to zero, <em>item</em> is moved to the beginning; if greater
than or equal to the number of children, it is moved to the end. If <em>item</em>
was detached it is reattached.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.next">
<span class="sig-name descname"><span class="pre">next</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.next" title="Link to this definition">¶</a></dt>
<dd><p>Returns the identifier of <em>item</em>’s next sibling, or ‘’ if <em>item</em> is the
last child of its parent.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.parent">
<span class="sig-name descname"><span class="pre">parent</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.parent" title="Link to this definition">¶</a></dt>
<dd><p>Returns the ID of the parent of <em>item</em>, or ‘’ if <em>item</em> is at the top
level of the hierarchy.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.prev">
<span class="sig-name descname"><span class="pre">prev</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.prev" title="Link to this definition">¶</a></dt>
<dd><p>Returns the identifier of <em>item</em>’s previous sibling, or ‘’ if <em>item</em> is
the first child of its parent.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.reattach">
<span class="sig-name descname"><span class="pre">reattach</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.reattach" title="Link to this definition">¶</a></dt>
<dd><p>An alias for <a class="reference internal" href="#tkinter.ttk.Treeview.move" title="tkinter.ttk.Treeview.move"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Treeview.move()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.see">
<span class="sig-name descname"><span class="pre">see</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.see" title="Link to this definition">¶</a></dt>
<dd><p>Ensure that <em>item</em> is visible.</p>
<p>Sets all of <em>item</em>’s ancestors open option to <code class="docutils literal notranslate"><span class="pre">True</span></code>, and scrolls the
widget if necessary so that <em>item</em> is within the visible portion of
the tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.selection">
<span class="sig-name descname"><span class="pre">selection</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.selection" title="Link to this definition">¶</a></dt>
<dd><p>Returns a tuple of selected items.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span><code class="docutils literal notranslate"><span class="pre">selection()</span></code> no longer takes arguments.  For changing the selection
state use the following selection methods.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.selection_set">
<span class="sig-name descname"><span class="pre">selection_set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.selection_set" title="Link to this definition">¶</a></dt>
<dd><p><em>items</em> becomes the new selection.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>items</em> can be passed as separate arguments, not just as a single tuple.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.selection_add">
<span class="sig-name descname"><span class="pre">selection_add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.selection_add" title="Link to this definition">¶</a></dt>
<dd><p>Add <em>items</em> to the selection.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>items</em> can be passed as separate arguments, not just as a single tuple.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.selection_remove">
<span class="sig-name descname"><span class="pre">selection_remove</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.selection_remove" title="Link to this definition">¶</a></dt>
<dd><p>Remove <em>items</em> from the selection.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>items</em> can be passed as separate arguments, not just as a single tuple.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.selection_toggle">
<span class="sig-name descname"><span class="pre">selection_toggle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.selection_toggle" title="Link to this definition">¶</a></dt>
<dd><p>Toggle the selection state of each item in <em>items</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>items</em> can be passed as separate arguments, not just as a single tuple.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.set">
<span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">item</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.set" title="Link to this definition">¶</a></dt>
<dd><p>With one argument, returns a dictionary of column/value pairs for the
specified <em>item</em>. With two arguments, returns the current value of the
specified <em>column</em>. With three arguments, sets the value of given
<em>column</em> in given <em>item</em> to the specified <em>value</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.tag_bind">
<span class="sig-name descname"><span class="pre">tag_bind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tagname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sequence</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.tag_bind" title="Link to this definition">¶</a></dt>
<dd><p>Bind a callback for the given event <em>sequence</em> to the tag <em>tagname</em>.
When an event is delivered to an item, the callbacks for each of the
item’s tags option are called.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.tag_configure">
<span class="sig-name descname"><span class="pre">tag_configure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tagname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.tag_configure" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify the options for the specified <em>tagname</em>.</p>
<p>If <em>kw</em> is not given, returns a dict of the option settings for
<em>tagname</em>. If <em>option</em> is specified, returns the value for that <em>option</em>
for the specified <em>tagname</em>. Otherwise, sets the options to the
corresponding values for the given <em>tagname</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.tag_has">
<span class="sig-name descname"><span class="pre">tag_has</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tagname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">item</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.tag_has" title="Link to this definition">¶</a></dt>
<dd><p>If <em>item</em> is specified, returns 1 or 0 depending on whether the specified
<em>item</em> has the given <em>tagname</em>. Otherwise, returns a list of all items
that have the specified tag.</p>
<p>Availability: Tk 8.6</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.xview">
<span class="sig-name descname"><span class="pre">xview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.xview" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify horizontal position of the treeview.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Treeview.yview">
<span class="sig-name descname"><span class="pre">yview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Treeview.yview" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify vertical position of the treeview.</p>
</dd></dl>

</dd></dl>

</section>
</section>
<section id="ttk-styling">
<span id="ttkstyling"></span><h2>Ttk Styling<a class="headerlink" href="#ttk-styling" title="Link to this heading">¶</a></h2>
<p>Each widget in <code class="xref py py-mod docutils literal notranslate"><span class="pre">ttk</span></code> is assigned a style, which specifies the set of
elements making up the widget and how they are arranged, along with dynamic
and default settings for element options. By default the style name is the
same as the widget’s class name, but it may be overridden by the widget’s style
option. If you don’t know the class name of a widget, use the method
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Misc.winfo_class()</span></code> (somewidget.winfo_class()).</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://tktable.sourceforge.net/tile/tile-tcl2004.pdf">Tcl’2004 conference presentation</a></dt><dd><p>This document explains how the theme engine works</p>
</dd>
</dl>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.ttk.Style">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.ttk.</span></span><span class="sig-name descname"><span class="pre">Style</span></span><a class="headerlink" href="#tkinter.ttk.Style" title="Link to this definition">¶</a></dt>
<dd><p>This class is used to manipulate the style database.</p>
<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.configure">
<span class="sig-name descname"><span class="pre">configure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">query_opt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.configure" title="Link to this definition">¶</a></dt>
<dd><p>Query or set the default value of the specified option(s) in <em>style</em>.</p>
<p>Each key in <em>kw</em> is an option and each value is a string identifying
the value for that option.</p>
<p>For example, to change every default button to be a flat button with
some padding and a different background color:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>
<span class="kn">import</span> <span class="nn">tkinter</span>

<span class="n">root</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>

<span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">()</span><span class="o">.</span><span class="n">configure</span><span class="p">(</span><span class="s2">&quot;TButton&quot;</span><span class="p">,</span> <span class="n">padding</span><span class="o">=</span><span class="mi">6</span><span class="p">,</span> <span class="n">relief</span><span class="o">=</span><span class="s2">&quot;flat&quot;</span><span class="p">,</span>
   <span class="n">background</span><span class="o">=</span><span class="s2">&quot;#ccc&quot;</span><span class="p">)</span>

<span class="n">btn</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Button</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;Sample&quot;</span><span class="p">)</span>
<span class="n">btn</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>

<span class="n">root</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.map">
<span class="sig-name descname"><span class="pre">map</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">query_opt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.map" title="Link to this definition">¶</a></dt>
<dd><p>Query or sets dynamic values of the specified option(s) in <em>style</em>.</p>
<p>Each key in <em>kw</em> is an option and each value should be a list or a
tuple (usually) containing statespecs grouped in tuples, lists, or
some other preference. A statespec is a compound of one
or more states and then a value.</p>
<p>An example may make it more understandable:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tkinter</span>
<span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>

<span class="n">root</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>

<span class="n">style</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">()</span>
<span class="n">style</span><span class="o">.</span><span class="n">map</span><span class="p">(</span><span class="s2">&quot;C.TButton&quot;</span><span class="p">,</span>
    <span class="n">foreground</span><span class="o">=</span><span class="p">[(</span><span class="s1">&#39;pressed&#39;</span><span class="p">,</span> <span class="s1">&#39;red&#39;</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;active&#39;</span><span class="p">,</span> <span class="s1">&#39;blue&#39;</span><span class="p">)],</span>
    <span class="n">background</span><span class="o">=</span><span class="p">[(</span><span class="s1">&#39;pressed&#39;</span><span class="p">,</span> <span class="s1">&#39;!disabled&#39;</span><span class="p">,</span> <span class="s1">&#39;black&#39;</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;active&#39;</span><span class="p">,</span> <span class="s1">&#39;white&#39;</span><span class="p">)]</span>
    <span class="p">)</span>

<span class="n">colored_btn</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Button</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;Test&quot;</span><span class="p">,</span> <span class="n">style</span><span class="o">=</span><span class="s2">&quot;C.TButton&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>

<span class="n">root</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
<p>Note that the order of the (states, value) sequences for an option does
matter, if the order is changed to <code class="docutils literal notranslate"><span class="pre">[('active',</span> <span class="pre">'blue'),</span> <span class="pre">('pressed',</span>
<span class="pre">'red')]</span></code> in the foreground option, for example, the result would be a
blue foreground when the widget were in active or pressed states.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.lookup">
<span class="sig-name descname"><span class="pre">lookup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">state</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.lookup" title="Link to this definition">¶</a></dt>
<dd><p>Returns the value specified for <em>option</em> in <em>style</em>.</p>
<p>If <em>state</em> is specified, it is expected to be a sequence of one or more
states. If the <em>default</em> argument is set, it is used as a fallback value
in case no specification for option is found.</p>
<p>To check what font a Button uses by default:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>

<span class="nb">print</span><span class="p">(</span><span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">()</span><span class="o">.</span><span class="n">lookup</span><span class="p">(</span><span class="s2">&quot;TButton&quot;</span><span class="p">,</span> <span class="s2">&quot;font&quot;</span><span class="p">))</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.layout">
<span class="sig-name descname"><span class="pre">layout</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">layoutspec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.layout" title="Link to this definition">¶</a></dt>
<dd><p>Define the widget layout for given <em>style</em>. If <em>layoutspec</em> is omitted,
return the layout specification for given style.</p>
<p><em>layoutspec</em>, if specified, is expected to be a list or some other
sequence type (excluding strings), where each item should be a tuple and
the first item is the layout name and the second item should have the
format described in <a class="reference internal" href="#layouts">Layouts</a>.</p>
<p>To understand the format, see the following example (it is not
intended to do anything useful):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>
<span class="kn">import</span> <span class="nn">tkinter</span>

<span class="n">root</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>

<span class="n">style</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">()</span>
<span class="n">style</span><span class="o">.</span><span class="n">layout</span><span class="p">(</span><span class="s2">&quot;TMenubutton&quot;</span><span class="p">,</span> <span class="p">[</span>
   <span class="p">(</span><span class="s2">&quot;Menubutton.background&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">),</span>
   <span class="p">(</span><span class="s2">&quot;Menubutton.button&quot;</span><span class="p">,</span> <span class="p">{</span><span class="s2">&quot;children&quot;</span><span class="p">:</span>
       <span class="p">[(</span><span class="s2">&quot;Menubutton.focus&quot;</span><span class="p">,</span> <span class="p">{</span><span class="s2">&quot;children&quot;</span><span class="p">:</span>
           <span class="p">[(</span><span class="s2">&quot;Menubutton.padding&quot;</span><span class="p">,</span> <span class="p">{</span><span class="s2">&quot;children&quot;</span><span class="p">:</span>
               <span class="p">[(</span><span class="s2">&quot;Menubutton.label&quot;</span><span class="p">,</span> <span class="p">{</span><span class="s2">&quot;side&quot;</span><span class="p">:</span> <span class="s2">&quot;left&quot;</span><span class="p">,</span> <span class="s2">&quot;expand&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">})]</span>
           <span class="p">})]</span>
       <span class="p">})]</span>
   <span class="p">}),</span>
<span class="p">])</span>

<span class="n">mbtn</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Menubutton</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s1">&#39;Text&#39;</span><span class="p">)</span>
<span class="n">mbtn</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>
<span class="n">root</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.element_create">
<span class="sig-name descname"><span class="pre">element_create</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elementname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">etype</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.element_create" title="Link to this definition">¶</a></dt>
<dd><p>Create a new element in the current theme, of the given <em>etype</em> which is
expected to be either “image” or “from”.</p>
<p>If “image” is used, <em>args</em> should contain the default image name followed
by statespec/value pairs (this is the imagespec), and <em>kw</em> may have the
following options:</p>
<dl class="simple">
<dt>border=padding</dt><dd><p>padding is a list of up to four integers, specifying the left, top,
right, and bottom borders, respectively.</p>
</dd>
<dt>height=height</dt><dd><p>Specifies a minimum height for the element. If less than zero, the
base image’s height is used as a default.</p>
</dd>
<dt>padding=padding</dt><dd><p>Specifies the element’s interior padding. Defaults to border’s value
if not specified.</p>
</dd>
<dt>sticky=spec</dt><dd><p>Specifies how the image is placed within the final parcel. spec
contains zero or more characters “n”, “s”, “w”, or “e”.</p>
</dd>
<dt>width=width</dt><dd><p>Specifies a minimum width for the element. If less than zero, the
base image’s width is used as a default.</p>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">img1</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">PhotoImage</span><span class="p">(</span><span class="n">master</span><span class="o">=</span><span class="n">root</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="s1">&#39;button.png&#39;</span><span class="p">)</span>
<span class="n">img1</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">PhotoImage</span><span class="p">(</span><span class="n">master</span><span class="o">=</span><span class="n">root</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="s1">&#39;button-pressed.png&#39;</span><span class="p">)</span>
<span class="n">img1</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">PhotoImage</span><span class="p">(</span><span class="n">master</span><span class="o">=</span><span class="n">root</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="s1">&#39;button-active.png&#39;</span><span class="p">)</span>
<span class="n">style</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">(</span><span class="n">root</span><span class="p">)</span>
<span class="n">style</span><span class="o">.</span><span class="n">element_create</span><span class="p">(</span><span class="s1">&#39;Button.button&#39;</span><span class="p">,</span> <span class="s1">&#39;image&#39;</span><span class="p">,</span>
                     <span class="n">img1</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;pressed&#39;</span><span class="p">,</span> <span class="n">img2</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;active&#39;</span><span class="p">,</span> <span class="n">img3</span><span class="p">),</span>
                     <span class="n">border</span><span class="o">=</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span> <span class="n">sticky</span><span class="o">=</span><span class="s1">&#39;we&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>If “from” is used as the value of <em>etype</em>,
<a class="reference internal" href="#tkinter.ttk.Style.element_create" title="tkinter.ttk.Style.element_create"><code class="xref py py-meth docutils literal notranslate"><span class="pre">element_create()</span></code></a> will clone an existing
element. <em>args</em> is expected to contain a themename, from which
the element will be cloned, and optionally an element to clone from.
If this element to clone from is not specified, an empty element will
be used. <em>kw</em> is discarded.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">style</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">(</span><span class="n">root</span><span class="p">)</span>
<span class="n">style</span><span class="o">.</span><span class="n">element_create</span><span class="p">(</span><span class="s1">&#39;plain.background&#39;</span><span class="p">,</span> <span class="s1">&#39;from&#39;</span><span class="p">,</span> <span class="s1">&#39;default&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.element_names">
<span class="sig-name descname"><span class="pre">element_names</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.element_names" title="Link to this definition">¶</a></dt>
<dd><p>Returns the list of elements defined in the current theme.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.element_options">
<span class="sig-name descname"><span class="pre">element_options</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elementname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.element_options" title="Link to this definition">¶</a></dt>
<dd><p>Returns the list of <em>elementname</em>’s options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.theme_create">
<span class="sig-name descname"><span class="pre">theme_create</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">themename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">settings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.theme_create" title="Link to this definition">¶</a></dt>
<dd><p>Create a new theme.</p>
<p>It is an error if <em>themename</em> already exists. If <em>parent</em> is specified,
the new theme will inherit styles, elements and layouts from the parent
theme. If <em>settings</em> are present they are expected to have the same
syntax used for <a class="reference internal" href="#tkinter.ttk.Style.theme_settings" title="tkinter.ttk.Style.theme_settings"><code class="xref py py-meth docutils literal notranslate"><span class="pre">theme_settings()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.theme_settings">
<span class="sig-name descname"><span class="pre">theme_settings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">themename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">settings</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.theme_settings" title="Link to this definition">¶</a></dt>
<dd><p>Temporarily sets the current theme to <em>themename</em>, apply specified
<em>settings</em> and then restore the previous theme.</p>
<p>Each key in <em>settings</em> is a style and each value may contain the keys
‘configure’, ‘map’, ‘layout’ and ‘element create’ and they are expected
to have the same format as specified by the methods
<a class="reference internal" href="#tkinter.ttk.Style.configure" title="tkinter.ttk.Style.configure"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Style.configure()</span></code></a>, <a class="reference internal" href="#tkinter.ttk.Style.map" title="tkinter.ttk.Style.map"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Style.map()</span></code></a>, <a class="reference internal" href="#tkinter.ttk.Style.layout" title="tkinter.ttk.Style.layout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Style.layout()</span></code></a> and
<a class="reference internal" href="#tkinter.ttk.Style.element_create" title="tkinter.ttk.Style.element_create"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Style.element_create()</span></code></a> respectively.</p>
<p>As an example, let’s change the Combobox for the default theme a bit:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>
<span class="kn">import</span> <span class="nn">tkinter</span>

<span class="n">root</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>

<span class="n">style</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Style</span><span class="p">()</span>
<span class="n">style</span><span class="o">.</span><span class="n">theme_settings</span><span class="p">(</span><span class="s2">&quot;default&quot;</span><span class="p">,</span> <span class="p">{</span>
   <span class="s2">&quot;TCombobox&quot;</span><span class="p">:</span> <span class="p">{</span>
       <span class="s2">&quot;configure&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;padding&quot;</span><span class="p">:</span> <span class="mi">5</span><span class="p">},</span>
       <span class="s2">&quot;map&quot;</span><span class="p">:</span> <span class="p">{</span>
           <span class="s2">&quot;background&quot;</span><span class="p">:</span> <span class="p">[(</span><span class="s2">&quot;active&quot;</span><span class="p">,</span> <span class="s2">&quot;green2&quot;</span><span class="p">),</span>
                          <span class="p">(</span><span class="s2">&quot;!disabled&quot;</span><span class="p">,</span> <span class="s2">&quot;green4&quot;</span><span class="p">)],</span>
           <span class="s2">&quot;fieldbackground&quot;</span><span class="p">:</span> <span class="p">[(</span><span class="s2">&quot;!disabled&quot;</span><span class="p">,</span> <span class="s2">&quot;green3&quot;</span><span class="p">)],</span>
           <span class="s2">&quot;foreground&quot;</span><span class="p">:</span> <span class="p">[(</span><span class="s2">&quot;focus&quot;</span><span class="p">,</span> <span class="s2">&quot;OliveDrab1&quot;</span><span class="p">),</span>
                          <span class="p">(</span><span class="s2">&quot;!disabled&quot;</span><span class="p">,</span> <span class="s2">&quot;OliveDrab2&quot;</span><span class="p">)]</span>
       <span class="p">}</span>
   <span class="p">}</span>
<span class="p">})</span>

<span class="n">combo</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Combobox</span><span class="p">()</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>

<span class="n">root</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.theme_names">
<span class="sig-name descname"><span class="pre">theme_names</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.theme_names" title="Link to this definition">¶</a></dt>
<dd><p>Returns a list of all known themes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.ttk.Style.theme_use">
<span class="sig-name descname"><span class="pre">theme_use</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">themename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.ttk.Style.theme_use" title="Link to this definition">¶</a></dt>
<dd><p>If <em>themename</em> is not given, returns the theme in use.  Otherwise, sets
the current theme to <em>themename</em>, refreshes all widgets and emits a
&lt;&lt;ThemeChanged&gt;&gt; event.</p>
</dd></dl>

</dd></dl>

<section id="layouts">
<h3>Layouts<a class="headerlink" href="#layouts" title="Link to this heading">¶</a></h3>
<p>A layout can be just <code class="docutils literal notranslate"><span class="pre">None</span></code>, if it takes no options, or a dict of
options specifying how to arrange the element. The layout mechanism
uses a simplified version of the pack geometry manager: given an
initial cavity, each element is allocated a parcel.</p>
<p>The valid options/values are:</p>
<dl class="simple">
<dt><em>side</em>: whichside</dt><dd><p>Specifies which side of the cavity to place the element; one of
top, right, bottom or left. If omitted, the element occupies the
entire cavity.</p>
</dd>
<dt><em>sticky</em>: nswe</dt><dd><p>Specifies where the element is placed inside its allocated parcel.</p>
</dd>
<dt><em>unit</em>: 0 or 1</dt><dd><p>If set to 1, causes the element and all of its descendants to be treated as
a single element for the purposes of <a class="reference internal" href="#tkinter.ttk.Widget.identify" title="tkinter.ttk.Widget.identify"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Widget.identify()</span></code></a> et al. It’s
used for things like scrollbar thumbs with grips.</p>
</dd>
<dt><em>children</em>: [sublayout… ]</dt><dd><p>Specifies a list of elements to place inside the element. Each
element is a tuple (or other sequence type) where the first item is
the layout name, and the other is a <a class="reference internal" href="#layouts">Layout</a>.</p>
</dd>
</dl>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a><ul>
<li><a class="reference internal" href="#using-ttk">Using Ttk</a></li>
<li><a class="reference internal" href="#ttk-widgets">Ttk Widgets</a></li>
<li><a class="reference internal" href="#widget">Widget</a><ul>
<li><a class="reference internal" href="#standard-options">Standard Options</a></li>
<li><a class="reference internal" href="#scrollable-widget-options">Scrollable Widget Options</a></li>
<li><a class="reference internal" href="#label-options">Label Options</a></li>
<li><a class="reference internal" href="#compatibility-options">Compatibility Options</a></li>
<li><a class="reference internal" href="#widget-states">Widget States</a></li>
<li><a class="reference internal" href="#ttk-widget">ttk.Widget</a></li>
</ul>
</li>
<li><a class="reference internal" href="#combobox">Combobox</a><ul>
<li><a class="reference internal" href="#options">Options</a></li>
<li><a class="reference internal" href="#virtual-events">Virtual events</a></li>
<li><a class="reference internal" href="#ttk-combobox">ttk.Combobox</a></li>
</ul>
</li>
<li><a class="reference internal" href="#spinbox">Spinbox</a><ul>
<li><a class="reference internal" href="#id1">Options</a></li>
<li><a class="reference internal" href="#id2">Virtual events</a></li>
<li><a class="reference internal" href="#ttk-spinbox">ttk.Spinbox</a></li>
</ul>
</li>
<li><a class="reference internal" href="#notebook">Notebook</a><ul>
<li><a class="reference internal" href="#id3">Options</a></li>
<li><a class="reference internal" href="#tab-options">Tab Options</a></li>
<li><a class="reference internal" href="#tab-identifiers">Tab Identifiers</a></li>
<li><a class="reference internal" href="#id4">Virtual Events</a></li>
<li><a class="reference internal" href="#ttk-notebook">ttk.Notebook</a></li>
</ul>
</li>
<li><a class="reference internal" href="#progressbar">Progressbar</a><ul>
<li><a class="reference internal" href="#id5">Options</a></li>
<li><a class="reference internal" href="#ttk-progressbar">ttk.Progressbar</a></li>
</ul>
</li>
<li><a class="reference internal" href="#separator">Separator</a><ul>
<li><a class="reference internal" href="#id6">Options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#sizegrip">Sizegrip</a><ul>
<li><a class="reference internal" href="#platform-specific-notes">Platform-specific notes</a></li>
<li><a class="reference internal" href="#bugs">Bugs</a></li>
</ul>
</li>
<li><a class="reference internal" href="#treeview">Treeview</a><ul>
<li><a class="reference internal" href="#id7">Options</a></li>
<li><a class="reference internal" href="#item-options">Item Options</a></li>
<li><a class="reference internal" href="#tag-options">Tag Options</a></li>
<li><a class="reference internal" href="#column-identifiers">Column Identifiers</a></li>
<li><a class="reference internal" href="#id8">Virtual Events</a></li>
<li><a class="reference internal" href="#ttk-treeview">ttk.Treeview</a></li>
</ul>
</li>
<li><a class="reference internal" href="#ttk-styling">Ttk Styling</a><ul>
<li><a class="reference internal" href="#layouts">Layouts</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tkinter.dnd.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.dnd</span></code> — Drag and drop support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.tix.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.ttk.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.tix.html" title="tkinter.tix — Extension widgets for Tk"
             >next</a> |</li>
        <li class="right" >
          <a href="tkinter.dnd.html" title="tkinter.dnd — Drag and drop support"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" >Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>