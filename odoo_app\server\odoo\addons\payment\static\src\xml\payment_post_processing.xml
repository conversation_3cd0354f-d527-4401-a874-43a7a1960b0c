<?xml version="1.0" encoding="UTF-8"?>
<templates id="payment" xml:space="preserve">

    <t t-name="payment.transactionDetails">
        <div>  <!-- Single parent element to allow using renderToElement. -->
            <t t-set="alert_style" t-value="'info'"/>
            <t t-set="alert_header" t-value="'Please wait...'"/>
            <t t-if="state == 'draft'">
                <t t-set="alert_message"
                   t-value="display_message ? display_message
                   : 'Your payment has not been processed yet.'"
                />
            </t>
            <t t-elif="state == 'pending'">
                <t t-set="alert_style" t-value="'warning'"/>
                <t t-set="alert_message" t-value="display_message"/>
            </t>
            <t t-elif="state == 'authorized'">
                <t t-set="alert_style" t-value="'success'"/>
                <t t-set="alert_message" t-value="display_message"/>
            </t>
            <t t-elif="state == 'done'">
                <t t-set="alert_style" t-value="'success'"/>
                <t t-set="alert_header"
                   t-value="operation !== 'validation' ? 'Your payment has been processed' :
                            'Your payment method has been saved'"
                />
            </t>
            <t t-elif="state == 'cancel'">
                <t t-set="alert_style" t-value="'danger'"/>
                <t t-set="alert_header" t-value="'This payment has been canceled'"/>
                <t t-set="alert_message" t-value="state_message ? 'Reason: ' + state_message : 'No payment has been processed.'"/>
            </t>
            <t t-elif="state == 'error'">
                <t t-set="alert_style" t-value="'danger'"/>
                <t t-set="alert_header" t-value="'Error'"/>
                <t t-set="alert_message" t-value="state_message"/>
            </t>

            <div name="o_payment_status_alert"
                 t-attf-class="alert alert-{{alert_style}} d-flex gap-3"
            >
                <div>
                    <i t-attf-class="fa fa-{{alert_style === 'danger' ? 'exclamation-triangle'
                                     : 'cog fa-spin'}}"
                    />
                </div>
                <div>
                    <h5 class="alert-heading mb-0" t-out="alert_header"/>
                    <t t-if="alert_message" t-out="alert_message"/>
                </div>
                <a t-att-href="landing_route" class="alert-link ms-auto text-nowrap">
                    Skip <i class="oi oi-arrow-right ms-1 small"/>
                </a>
            </div>

            <div class="o_cc o_cc2 row row-cols-1 row-cols-md-2 mx-0 mb-3 py-2 rounded">
                <div class="col py-3">
                    <label for="o_payment_summary_amount" class="d-block small text-muted">
                        Amount
                    </label>
                    <span id="o_payment_summary_amount"
                          t-out="formatCurrency(amount, currency_id)"
                          class="fs-5 fw-bold"
                    />
                </div>
                <hr class="d-md-none m-0 text-300 opacity-100"/>
                <div class="o_payment_summary_separator col py-3 text-break">
                    <label for="o_payment_summary_reference" class="d-block small text-muted">
                        Reference
                    </label>
                    <span id="o_payment_summary_reference"
                          t-out="reference"
                          class="fs-5 fw-bold"
                    />
                </div>
            </div>
        </div>
    </t>

    <t t-name="payment.tx_not_found">
        <div class="text-center">  <!-- Single parent element to allow using renderToElement. -->
            <p>We are not able to find your payment, but don't worry.</p>
            <p>You should receive an email confirming your payment in a few minutes.</p>
            <p>If the payment hasn't been confirmed you can contact us.</p>
        </div>
    </t>

    <t t-name="payment.rpc_error">
        <div class="text-center">  <!-- Single parent element to allow using renderToElement. -->
            <p>Unable to contact the server. Please wait. <i class="fa fa-refresh fa-spin"/></p>
        </div>
    </t>

    <t t-name="payment.exception">
        <div class="text-center">  <!-- Single parent element to allow using renderToElement. -->
            <h2>Internal server error</h2>
            <pre><t t-out="error_message"/></pre>
        </div>
    </t>

</templates>
