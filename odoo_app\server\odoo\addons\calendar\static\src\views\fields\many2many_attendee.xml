<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="calendar.Many2ManyAttendee" t-inherit="web.Many2ManyTagsAvatarField" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('many2many_tags_avatar_field_container')]" position="attributes">
            <attribute name="class" add="flex-column" separator=" "/>
        </xpath>
        <xpath expr="//Many2XAutocomplete" position="attributes">
            <attribute name="placeholder">props.placeholder</attribute>
        </xpath>
    </t>
</templates>
