# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:35+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " days"
msgstr " dni"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " hours"
msgstr " ur"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(employee_name)s - from %(date_from)s to %(date_to)s - %(state)s"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%(first)s, %(second)s and %(amount)s others"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(holiday_name)s has been refused."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"%(leave_name)s has been cancelled with the justification: <br/> %(reason)s."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s: %(duration).2f days (%(start)s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s: %(duration).2f hours on %(date)s"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%(name)s (%(duration)s day(s))"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%(name)s (%(duration)s hour(s))"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f days (%(start)s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f hours on %(date)s"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s: %(duration).2f days (%(start)s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s: %(duration).2f hours on %(date)s"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid "%g remaining out of %g"
msgstr "%g preostalih od %g"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to %s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to No Limit)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f day(s)"
msgstr "%s na : %.2f dan(dni)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f hour(s)"
msgstr "%s na : %.2f ur(ure)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: %.2f days"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: %.2f hours"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "(valid until"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10
msgid "10:00 AM"
msgstr "10:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22
msgid "10:00 PM"
msgstr "10:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10_5
msgid "10:30 AM"
msgstr "10:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22_5
msgid "10:30 PM"
msgstr "10:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11
msgid "11:00 AM"
msgstr "11:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23
msgid "11:00 PM"
msgstr "11:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11_5
msgid "11:30 AM"
msgstr "11:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23_5
msgid "11:30 PM"
msgstr "11:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0
msgid "12:00 AM"
msgstr "12:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12
msgid "12:00 PM"
msgstr "12:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0_5
msgid "12:30 AM"
msgstr "12:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12_5
msgid "12:30 PM"
msgstr "12:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1
msgid "1:00 AM"
msgstr "1:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13
msgid "1:00 PM"
msgstr "1:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1_5
msgid "1:30 AM"
msgstr "1:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13_5
msgid "1:30 PM"
msgstr "1:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2
msgid "2:00 AM"
msgstr "2:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14
msgid "2:00 PM"
msgstr "2:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2_5
msgid "2:30 AM"
msgstr "2:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14_5
msgid "2:30 PM"
msgstr "2:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3
msgid "3:00 AM"
msgstr "3:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15
msgid "3:00 PM"
msgstr "3:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3_5
msgid "3:30 AM"
msgstr "3:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15_5
msgid "3:30 PM"
msgstr "3:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4
msgid "4:00 AM"
msgstr "4:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16
msgid "4:00 PM"
msgstr "4:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4_5
msgid "4:30 AM"
msgstr "4:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16_5
msgid "4:30 PM"
msgstr "4:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5
msgid "5:00 AM"
msgstr "5:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17
msgid "5:00 PM"
msgstr "5:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5_5
msgid "5:30 AM"
msgstr "5:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17_5
msgid "5:30 PM"
msgstr "5:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6
msgid "6:00 AM"
msgstr "6:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18
msgid "6:00 PM"
msgstr "6:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6_5
msgid "6:30 AM"
msgstr "6:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18_5
msgid "6:30 PM"
msgstr "6:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7
msgid "7:00 AM"
msgstr "7:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19
msgid "7:00 PM"
msgstr "7:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7_5
msgid "7:30 AM"
msgstr "7:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19_5
msgid "7:30 PM"
msgstr "7:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8
msgid "8:00 AM"
msgstr "8:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20
msgid "8:00 PM"
msgstr "8:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8_5
msgid "8:30 AM"
msgstr "8:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20_5
msgid "8:30 PM"
msgstr "8:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9
msgid "9:00 AM"
msgstr "9:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21
msgid "9:00 PM"
msgstr "9:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9_5
msgid "9:30 AM"
msgstr "9:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21_5
msgid "9:30 PM"
msgstr "9:30 PM"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Days</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Hours</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "<span class=\"ms-3\">Days</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Off Till\n"
"                        </span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Dopust\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                           Time Off\n"
"                        </span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\"> to </span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">od </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span invisible=\"holiday_type != 'employee'\">\n"
"                        The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone\n"
"                    </span>\n"
"                    <span invisible=\"holiday_type != 'department'\">\n"
"                        The department's company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    <span invisible=\"holiday_type != 'company'\">\n"
"                        The company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    ("
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>You can only take this time off in whole days, so if your schedule has"
" half days, it won't be used efficiently.</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>Oddelki in kadri</strong>"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "A time off cannot be duplicated."
msgstr "Odsotnost ne more biti podvojena."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "Lahko vidi preostali dopust"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "Odsotnost"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "Odsotnost do danes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "Odsotni zaposleni"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
msgid "Absent Today"
msgstr "Danes odsotni"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Accrual (Future):"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "Ponavljajoča se dodelitev"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_accrual_plan
msgid "Accrual Plan Available"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "Accrual Plan's Employees"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
msgid "Accrual Time Off: Updates the number of time off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrued_gain_time
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__accrued_gain_time
msgid "Accrued Gain Time"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "Potreben je ukrep"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Active"
msgstr "Aktivno"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "Aktivne dodelitve"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Employee"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Active Time Off"
msgstr "Aktivna odsotnost"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Types"
msgstr "Aktivni tipi"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Izjema pri oznaki aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "Stanje aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona vrste aktivnosti"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipi aktivnosti"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Add a description..."
msgstr "Dodajanje opisa..."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "Dodaj razlog..."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Add some description for the people that will validate it"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__added_value_type
msgid "Added Value Type"
msgstr ""

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "Popoldne"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "Vse dodelitve"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__all_employee_ids
msgid "All Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "Vse odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__all
msgid "All accrued time carried over"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__last_several_days
msgid "All day"
msgstr "Cel dan"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Allocated ("
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation"
msgstr "Dodelitev"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
msgid "Allocation Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__private_name
msgid "Allocation Description"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_type
msgid "Allocation Mode"
msgstr "Metoda dodelitve"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_remaining_display
msgid "Allocation Remaining Display"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#, python-format
msgid "Allocation Request"
msgstr "Zahtevek po dodelitvi"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "Zahtevki po dodelitvi"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "Vrsta dodelitve"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Allocation of %s: %.2f %s to %s"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request must be confirmed or validated in order to refuse it."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_count
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Allocations"
msgstr "Dodelitve"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allows_negative
msgid "Allow Negative Cap"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Attach Supporting Document"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__holiday_type
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__already_accrued
msgid "Already Accrued"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Amount"
msgstr "Znesek"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid "Amount in Negative"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "An employee already booked time off which overlaps with this period:%s"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "Analiziraj od"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "Analiza ocenjevanja"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "Odobritev"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Approve"
msgstr "Potrdi"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#, python-format
msgid "Approved"
msgstr "Odobreno"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__officer
msgid "Approved by Time Off Officer"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Approved:"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__apr
msgid "April"
msgstr "April"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Archive Employee Allocations"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Archived"
msgstr "Arhivirano"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Archived Time Off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_search_my
msgid "Archived Time Off Type"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Ali ste prepričani, da želite izbrisati ta zapis?"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__allocation
msgid "At the allocation date"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__end
msgid "At the end of the accrual period"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__start
msgid "At the start of the accrual period"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__year_start
msgid "At the start of the year"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "At work"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "Število prilog"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "Priponke"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__aug
msgid "August"
msgstr "Avgust"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Available"
msgstr "Razpoložljivo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Available:"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
#, python-format
msgid "Away"
msgstr "Odsoten"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "Balance at the"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid "Based on worked time"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "Overjeno in potrjeno"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__company
msgid "By Company"
msgstr "Po podjetju"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__department
msgid "By Department"
msgstr "Po oddelkih"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__employee
msgid "By Employee"
msgstr "Po kadru"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__category
msgid "By Employee Tag"
msgstr "Po oznaki kadra"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__holiday_type
msgid ""
"By Employee: Allocation/Request for individual Employee, By Employee Tag: "
"Allocation/Request for group of employees in category"
msgstr ""
"Po kadru: dodelitev/zahtevek za posamezni kader. Po kadrovski oznaki: "
"dodelitev/zahtevek za skupino kadrov določene kategorije"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "Uradnik odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_cancel
msgid "Can Cancel"
msgstr "Lahko prekličem"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__can_modify_value_type
msgid "Can Modify Value Type"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
msgid "Can reset"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Prekliči"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel Future Time Off"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
#, python-format
msgid "Cancel Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_cancel_leave
msgid "Cancel Time Off Wizard"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel all time off after this date."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Canceled"
msgstr "Preklicano"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Canceled Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Cancelled"
msgstr "Preklicano"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid "Cap accrued time"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Cap:"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry over"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__maximum
msgid "Carry over with a maximum"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry over:"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry-Over Date"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_date
msgid "Carry-Over Time"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day
msgid "Carryover Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day_display
msgid "Carryover Day Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_month
msgid "Carryover Month"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__category_id
msgid "Category of Employee"
msgstr "Kategorija kadra"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
#, python-format
msgid ""
"Changing this working schedule results in the affected employee(s) not "
"having enough leaves allocated to accomodate for their leaves already taken "
"in the future. Please review this employee's leaves and adjust their "
"allocation accordingly."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_ids
msgid ""
"Choose the Time Off Officers who will be notified to approve allocation or "
"Time Off Request. If empty, nobody will be notified"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Click on any date or on this button to request a time-off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "Barva"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "Podjetje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__mode_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__mode_company_id
msgid "Company Mode"
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "Nadomestni dnevi"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Configuration"
msgstr "Nastavitve"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Confirm"
msgstr "Potrdi"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Confirmation"
msgstr "Potrditev"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
#, python-format
msgid "Confirmed"
msgstr "Potrjeno"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#, python-format
msgid "Confirmed and Approved"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Congrats, we can see that your request has been validated."
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "Stik"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of allocations for this time off type (approved or waiting for "
"approbation) with a validity period starting this year."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Count of plans linked to this time off type."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of time off requests for this time off type (approved or waiting for "
"approbation) with a start date in the current year."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "Naslovna slika"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "Ustvari novo dodelitev za odsotnost"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "Ustvari zahtevek za novo dodelitev"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__current_leave_state
msgid "Current Time Off Status"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "Trenutni tip dopusta"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Current Year"
msgstr "Tekoče leto"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Currently Valid"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "Ure po meri"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "Dnevno"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "Nadzorna plošča"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Date"
msgstr "Datum"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "Datum obdobja začetka"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "Datumi"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "Dan"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__day
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
#, python-format
msgid "Days"
msgstr "Dni"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__dec
msgid "December"
msgstr "December"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid ""
"Define the maximum level of negative days this kind of time off can reach. "
"Value must be at least 1."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "Delete"
msgstr "Izbriši"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#, python-format
msgid "Delete Confirmation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Delete Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "Oddelek"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__department_ids
msgid "Departments"
msgstr "Oddelki"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
msgid "Description"
msgstr "Opis"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Discard"
msgstr "Opusti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "Prikaži odsotnost v koledaju"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Due to a change in global time offs, %s extra day(s) have been taken from "
"your allocation. Please review this leave if you need it to be changed."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Due to a change in global time offs, this leave no longer has the required "
"amount of available allocation and has been set to refused. Please review "
"this leave."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Due to a change in global time offs, you have been granted %s day(s) back."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "Trajanje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Days)"
msgstr "Trajanje (Dnevi)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours
msgid "Duration (Hours)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "Trajanje (dnevi)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "Trajanje (ure)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days_display
msgid "Duration in days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_display
msgid "Duration in hours"
msgstr "Trajanje v urah"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "Uredi odsotnost"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "Kader"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
msgid "Employee Active"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
msgid "Employee Company"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__category_id
msgid "Employee Tag"
msgstr "Oznaka kadra"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Employee accrue"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "Kader(i)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_ids
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Employees"
msgstr "Kadri"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Employees Off Today"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__end_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "Končni datum"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__employee_requests
msgid ""
"Extra Days Requests Allowed: User can request an allocation for himself.\n"
"\n"
"        Not Allowed: User cannot request an allocation."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__feb
msgid "February"
msgstr "Februar"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid ""
"Filters only on allocations that belong to a time off type that is 'active' "
"(active field is True)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "First Approval"
msgstr "Prva odobritev"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "Sledilci"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledilci (partnerji)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font izjemna ikona npr. fa-tasks"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"For an Accrual Allocation, this field contains the theorical amount of time "
"given to the employee, due to a previous start date, on the first run of the"
" plan. This can be manually edited."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "Pogostost"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "Petek"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "Od"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "Od datuma"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "Bodoče aktivnosti"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "Grant Time"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "Združi po"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__report_note
msgid "HR Comments"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__half_day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "Pol dneva"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_mandatory_day
msgid "Has Mandatory Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "Ima sporočilo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Holds whether this allocation concerns more than 1 employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_home_working
#: model:mail.message.subtype,name:hr_holidays.mt_leave_home_working
msgid "Home Working"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "Ura od"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "Ura do"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__hourly
msgid "Hourly"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
#, python-format
msgid "Hours"
msgstr "Ure"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona za označevanje izjemne aktivnosti."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "Idle"
msgstr "Nedejaven"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Če je označeno, zahtevajo nova sporočila vašo pozornost."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Če je označeno, nekatera sporočila vsebujejo napako pri dostavi."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid ""
"If checked, the accrual period will be calculated according to the work "
"days, not calendar days."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allows_negative
msgid ""
"If checked, users request can exceed the allocated days and balance can go "
"in negative."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Če je aktivno polje nastavljeno, bo doboljeno skriti zapis vira, ne da bi ga"
" brisali."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "Takoj"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Incorrect state for new allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "Je sledilec"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "Je neplačan"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_user_only_responsible
msgid "Is User Only Responsible"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jan
msgid "January"
msgstr "Januar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "Zaposlitev"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "Delovno mesto"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jul
msgid "July"
msgstr "Julij"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jun
msgid "June"
msgstr "Junij"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "Aktivnosti z zamudo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_increases_duration
msgid "Leave Type Increases Duration"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "Levo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Legend"
msgstr "Legenda"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's approve it"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's discover the Time Off application"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's go validate it"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's try to create a Sick Time Off, select it in the list"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "Ravni"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__linked_request_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__linked_request_ids
msgid "Linked Requests"
msgstr "Povezan zahtevek"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna priponka"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_management
msgid "Management"
msgstr "Upravljanje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
msgid "Manager"
msgstr "Upravitelj"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_mandatory_day
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
msgid "Mandatory Day"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_mandatory_day_action
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_mandatory_day_menu_configuration
#, python-format
msgid "Mandatory Days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__mar
msgid "March"
msgstr "Marec"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Mark as Draft"
msgstr "Označi kot osnutek"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "Največ dovoljeno"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Maximum amount of accruals to transfer"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__may
msgid "May"
msgstr "Maj"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "Sestanek"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "Napaka pri dostavi sporočila"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Podtipi sporočil"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "Sporočila"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
msgid "Milestone"
msgstr "Mejnik"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Milestone Transition"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Milestone reached"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Mode"
msgstr "Način"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "Ponedeljek"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "Mesec"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "Mesečno"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "Months"
msgstr "Meseci"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "Jutro"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Multi Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Rok dejavnosti"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "Moje dodelitve"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "Moji zahtevki"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "Moja ekipa"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
msgid "My Time"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "Moje odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "Naziv"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Negative Cap"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__draft
#, python-format
msgid "New"
msgstr "Novo"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "New %(leave_type)s Request created by %(user)s"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#, python-format
msgid "New Allocation"
msgstr "Nova dodelitev"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "New Allocation Request"
msgstr "Zahtevek za novo dodelitev"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "New Milestone"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "New Time Off"
msgstr "Nova odsotnost"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Naslednji dogodek v koledarju aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Rok naslednje aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "Povzetek naslednje aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "Tip naslednje aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "Brez omejitev"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "Brez validacije"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.act_hr_employee_holiday_type
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid "No data to display"
msgstr "Ni podatkov za prikaz"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_department
msgid "No data yet!"
msgstr "Podatkov še ni!"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "Brez omejitev"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rule has been set up for this accrual plan."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no
msgid "No validation needed"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Nobody will be notified"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#, python-format
msgid "None"
msgstr "Brez"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "None. Accrued time reset to 0"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_ids
msgid "Notified Time Off Officer"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__nov
msgid "November"
msgstr "November"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_text
msgid "Number Of Hours Text"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "Število aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
msgid "Number of Days"
msgstr "Število dni"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days_display
msgid ""
"Number of days of the time off request according to your working schedule. "
"Used for interface."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid "Number of days of the time off request. Used in the calculation."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "Število napak"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours_display
msgid ""
"Number of hours of the time off request according to your working schedule. "
"Used for interface."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours
msgid "Number of hours of the time off request. Used in the calculation."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Število sporočil, ki zahtevajo ukrepanje"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Število sporočil, ki niso bila pravilno dostavljena."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__oct
msgid "October"
msgstr "Oktober"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Off Till"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr ""

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer: Manage all requests"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "On Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
#, python-format
msgid "Online"
msgstr "Spletno"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Only"
msgstr "Samo"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a refused leave."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a started leave."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset other people leaves."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Only a Time Off Officer or Manager can approve/refuse its own requests."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Only a time off Manager can approve its own requests."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only a time off Officer/Responsible or Manager can approve or refuse time "
"off requests."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operacija ni podprta"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__other
msgid "Other"
msgstr "Drugo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
#, python-format
msgid "Out of office"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/persona_service_patch.js:0
#, python-format
msgid "Out of office until %s"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "Pregled"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__parent_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__parent_id
msgid "Parent"
msgstr "Nadrejeni"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "Obdobje"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "Planirano"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Planned:"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Tiskanje"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Provide a reason to delete an approved time off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public"
msgstr "Javno"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
#, python-format
msgid "Public Holidays"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "Tečaj"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__rating_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__reason
msgid "Reason"
msgstr "Razlog"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
msgid "Reasons"
msgstr "Razlogi"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Refuse"
msgstr "Zavrni"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#, python-format
msgid "Refused"
msgstr "Zavrnjeno"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Refused Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
msgid "Regular Allocation"
msgstr "Redna dodelitev"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Povezano uporabniško ime za vir za upravljanje njegovega dostopa."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "Preostali dnevi"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Remaining Paid Time Off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "Preostale odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Remove employee from existing accrual plans."
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Poročanje"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "Zahtevan končni datum"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "Zahtevan datum začetka"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "Tip zahtevka"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "Zahtevano (dnevi/ure)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__resource_calendar_id
msgid "Resource Calendar"
msgstr "Koledar virov"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "Čas odsotnosti virov"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni uporabnik"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "Pravila"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "Zaženi dokler"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Napaka pri dostavi SMS "

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "Sobota"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#, python-format
msgid "Save"
msgstr "Shrani"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "Išči po tipu odsotnosti"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Second Approval"
msgstr "Druga overitev"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Second approval request for %(leave_type)s"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Select Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "Izberite tip odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__validation_type
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allocation_validation_type
msgid ""
"Select the level of approval needed in case of request by employee\n"
"        - No validation needed: The employee's request is automatically approved.\n"
"        - Approved by Time Off Officer: The employee's request need to be manually approved by the Time Off Officer."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Select the request you just created"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__sep
msgid "September"
msgstr "September"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Set a maximum of accruals an allocation keeps at the end of the year."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__show_transition_mode
msgid "Show Transition Mode"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Prikaži vse zapise z datumom naslednje aktivnosti pred današnjim datumom."

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Some leaves cannot be linked to any allocation. To see those leaves,"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid ""
"Some of the accrual plans you're trying to delete are linked to an existing "
"allocation. Delete or cancel them first."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Specify what occurs if a level transition takes place in the middle of a pay period.\n"
"\n"
"                'Immediately' will switch the employee to the new accrual level on the exact date during the ongoing pay period.\n"
"\n"
"                'After this accrual's period' will keep the employee on the same accrual level until the ongoing pay period is complete.\n"
"                After it is complete, the new level will take effect when the next pay period begins."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__start_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Start Date"
msgstr "Začetni datum"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "Stanje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "Status"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na osnovi aktivnosti\n"
"Zapadel: Rok je že prekoračen\n"
"Danes: Datum aktivnosti je danes\n"
"Planirano: Bodoče aktivnosti."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Submit your request"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "Vsota"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "Nedelja"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Supporting Documents"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "Koriščenje odsotnosti v"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
#, python-format
msgid "Taken"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The Start Date of the Validity Period must be anterior to the End Date."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid ""
"The allocation requirement of a time off type cannot be changed once leaves "
"of that type have been taken. You should create a new time off type instead."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"The distinction between working time (ex. Attendance) and absence (ex. "
"Training) will be used in the computation of Accrual's plan rate."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_type_value
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_value
msgid ""
"The employee, department, company or employee category of this request is "
"missing. Please make sure that your user login is linked to an employee."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid ""
"The leaves planned in the future are exceeding the maximum value of the allocation.\n"
"                It will not be possible to take all of them."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_check_negative
msgid ""
"The negative amount must be greater than 0. If you want to set 0, disable "
"the negative cap instead."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check3
msgid ""
"The request start date must be before or equal to the request end date."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_mandatory_day_date_from_after_day_to
msgid "The start date must be anterior than the end date."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be before or equal to the end date."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is set to 'To Submit', when an allocation request is created.\n"
"The status is 'To Approve', when an allocation request is confirmed by user.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "The time off has been automatically approved"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "The time off has been canceled: %s"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "There is no valid allocation to cover that request."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, delete and create a new one."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "This modification is not allowed in the current state."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "This time off cannot be canceled."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "Četrtek"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#, python-format
msgid "Time Off"
msgstr "Odsotnost"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.act_hr_employee_holiday_type
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_department
#, python-format
msgid "Time Off Analysis"
msgstr "Analiza dopustov"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
msgid "Time Off Approval"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Koledar odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar__associated_leaves_count
msgid "Time Off Count"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee.py:0
#, python-format
msgid "Time Off Dashboard"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "Opis odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#, python-format
msgid "Time Off Request"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "Zahteva za odsotnost"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr ""

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "Povzetek dopustov"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "Povzetek odsotnosti / Poročilo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "Tip dopusta"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "Tipi odsotnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Time Off Validation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "Odsotnost."

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_cron_cancel_invalid_ir_actions_server
msgid "Time Off: Cancel invalid leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Time off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_pivot
msgid "Time off Summary"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Time off Taken"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed in order to approve it."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be in Draft state (\"To Submit\") in order to confirm "
"it."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request state must be \"Refused\" or \"To Approve\" in order to be "
"reset to draft."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "Časovni pas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "Do"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#, python-format
msgid "To Approve"
msgstr "Za potrditi"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "To Approve or Approved Allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "Do datuma"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__draft
msgid "To Submit"
msgstr "Za predložitev"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "Today"
msgstr "Danes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_training
msgid "Training Time Off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "Torek"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Two public holidays cannot overlap each other for the same working hours."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "Tip"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
msgid "Type Request Unit"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Vrsta dejavnosti izjeme na zapisu. "

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Unlimited"
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "Neplačano"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "Neplačana odsotnost"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "Neprebrana sporočila"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Up to"
msgstr "Do"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "Uporabnik"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "User is online"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "User is out of office"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Validate"
msgstr "Potrditev"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Validated"
msgstr "Potrjeno"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "Čaka odobritev"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Waiting For Me"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "Čakanje na drugo potrditev"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website Messages"
msgstr "Sporočila iz spletne strani"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website communication history"
msgstr "Kronologija komunikacij spletne strani"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "Sreda"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/year/calendar_year_renderer.js:0
#, python-format
msgid "Week"
msgstr "Teden"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "Tedensko"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Worked Time"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__resource_calendar_id
msgid "Working Hours"
msgstr "Delovne ure"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "Letno"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "Years"
msgstr "Leta"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "Da"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__requires_allocation
msgid ""
"Yes: Time off requests need to have a valid allocation.\n"
"\n"
"              No Limit: Time Off requests can be taken without any prior allocation."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You are not allowed to request a time off on a Mandatory Day."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You can not have 2 time off that overlaps on the same day."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid ""
"You can select the period you need to take off, from start date to end date"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You can't manually archive/unarchive a time off."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot archive an allocation which is in confirm or validate state."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off assigned to several employees"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in %s state"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in the past"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "You cannot delete an allocation request which is in %s state."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
#, python-format
msgid ""
"You cannot have a cap on accrued time without setting a maximum amount."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot reduce the duration below the duration of leaves already taken by"
" the employee."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot refuse this allocation request since the employee has already "
"taken leaves for it. Please refuse or delete those leaves first."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You must be %s's Manager to approve this leave"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must either be a Time off Officer or Time off Manager to approve this "
"leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You've already booked time off which overlaps with this period:\n"
"%s\n"
"Attempting to double-book your time off won't magically make your vacation 2x better!\n"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your Time Off"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_holidays_cancel_leave.py:0
#, python-format
msgid "Your time off has been canceled."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation start date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "all"
msgstr "vse"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "and"
msgstr "in"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "available"
msgstr "Razpoložljivo"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "Po kadru"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "Po tipu"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "can be used before the allocation expires."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "click here"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#, python-format
msgid "day(s)"
msgstr "dan(dni)"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "days"
msgstr "dni"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "days)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "deleted by %s (uid=%d)."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "from %(date_from)s to %(date_to)s - %(state)s"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "hours"
msgstr "ure"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#, python-format
msgid "in"
msgstr "v"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "initially"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
#, python-format
msgid "last day"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "new request"
msgstr "Nov zahtevek"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "no"
msgstr "ne"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "of"
msgstr "od"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "od"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "na"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "taken"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "the accruated amount is insufficient for that duration."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "do"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "to refuse"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "up to"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "valid until"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr ""
