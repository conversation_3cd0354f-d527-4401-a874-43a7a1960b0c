# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
".\n"
"                        <br/>\n"
"                        感謝你支持我們公司！\n"
"                        <br/>\n"
"                        順祝 安康"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>回應：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>捐贈日期：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>捐贈人信箱：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>捐贈人姓名：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>付款編號:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>付款方式:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">國家/地區⋯</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>找不到合適的付款方式.</strong><br/>\n"
"                                如果您認為這是一個錯誤，請聯繫網站管理員"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>警告:</strong>貨別未填或不正確。"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "一筆捐贈款已在您的網站上完成"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "文化覺醒的一年。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Payments"
msgstr "啟動付款功能"

#. module: website_payment
#: model:ir.actions.server,name:website_payment.action_activate_stripe
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Stripe"
msgstr "啟用 Stripe"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "在這兒增加說明"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "加入新的預填選項"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Amount"
msgstr "金額"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Amount ("
msgstr "金額 ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "金額("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "照顧寶寶1個月。"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "選擇您的金額"

#. module: website_payment
#: model:ir.model,name:website_payment.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/res_config_settings.py:0
#, python-format
msgid "Configure %s"
msgstr "%s 配置"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#: model:ir.model,name:website_payment.model_res_country
#, python-format
msgid "Country"
msgstr "國家"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Country\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"國家/地區\n"
"                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "必須填寫國家。"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "自訂金額"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "親愛的"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "預設金額"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Descriptions"
msgstr "說明"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "顯示選項"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donate"
msgstr "捐助"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "馬上捐款"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation"
msgstr "捐款"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation Button"
msgstr "捐款按鈕"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "捐款金額必須高於 %.2f。"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "捐贈確認"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "捐贈通知"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email"
msgstr "電郵"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"電子郵件\n"
"                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "電郵地址無效"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "請填入電子郵件"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "欄位“%s”必須填入"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__first_provider_label
msgid "First Provider Label"
msgstr "首名服務商標籤"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "流入"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "為捐贈"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__is_stripe_supported_country
#: model:ir.model.fields,field_description:website_payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "是支援 Stripe 國家/地區"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "為捐贈"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "建立一筆捐贈"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Maximum"
msgstr "最高數量"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Minimum"
msgstr "最低"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Name"
msgstr "名稱"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Name\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"名稱\n"
"                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "必須填入名稱"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__none
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "無"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "小學一年級。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "高中一年級。"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__other_than_paypal
msgid "Other than Paypal"
msgstr "除 PayPal 以外"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Payment Details"
msgstr "付款詳情"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "付款處理失敗"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "收到的捐贈款項，詳情如下："

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "付款"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__paypal_only
msgid "Paypal Only"
msgstr "只用 PayPal"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "請選擇或輸入金額"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "預填選項"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__providers_state
msgid "Providers State"
msgstr "服務商狀態"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "受款人電郵地址"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Shop - Payment"
msgstr "網店 － 付款"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "滑動選擇器"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "無論金額大小，我們都萬分感謝您的協助。"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "還缺了一些資訊來處理您的付款。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Step"
msgstr "步驟"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr "你所在的國家/地區未有 Stripe Connect 服務。請使用其他付款服務商。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple "
"Pay, etc. as well as recurring charges."
msgstr "支援大多數付款方法； Visa、Mastercard、Maestro、Google Pay、Apple Pay 等，以及經常性費用。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "感謝您的捐贈"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "最低捐款金額為 %s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "沒有須付款項目。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View Alternatives"
msgstr "查看替代方案"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View other providers"
msgstr "查看其他服務商"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_provider__website_id
msgid "Website"
msgstr "網站"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
msgid "Website: Donation"
msgstr "網站：捐款"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Write us a comment"
msgstr "留下回應給我們"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Your comment"
msgstr "你的回應"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "獲得於"
