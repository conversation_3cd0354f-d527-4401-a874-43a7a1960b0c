# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_mine
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "%d credits will be consumed to find %d companies."
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "<b>Contacts</b>"
msgstr ""

#. module: crm_iap_mine
#: model:mail.template,body_html:crm_iap_mine.lead_generation_no_credits
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear,</p>\n"
"    <p>There are no more credits on your IAP Lead Generation account.<br>\n"
"    You can charge your IAP Lead Generation account in the settings of the CRM app.<br></p>\n"
"    <p>Best regards,</p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "<span class=\"col-md-6\">Extra contacts per Company</span>"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "<span class=\"o_stat_text\">Leads</span>"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "<span class=\"o_stat_text\">Opportunities</span>"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid ""
"<span invisible=\"error_type != 'no_result'\">Your request did not return "
"any result (no credits were used). Try removing some filters.</span>"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_238
msgid "Automobiles & Components"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__available_state_ids
msgid "Available State"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_69_157
msgid "Banks & Insurance"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Buy credits."
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_1
msgid "CEO"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_industry
msgid "CRM IAP Lead Industry"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_mining_request
msgid "CRM Lead Mining Request"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Cancel"
msgstr "Ακύρωση"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_162
msgid "Capital Goods"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__color
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__color
msgid "Color Index"
msgstr "Χρωματισμός Ευρετήριου"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_163
msgid "Commercial & Professional Services"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_2
msgid "Communications"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__search_type__companies
msgid "Companies"
msgstr "Εταιρίες"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__search_type__people
msgid "Companies and their Contacts"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__company_size_max
msgid "Company Size Max"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_167
msgid "Construction Materials"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_3
msgid "Consulting"
msgstr "Συμβουλευτική"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_30_155
msgid "Consumer Discretionary"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_239
msgid "Consumer Durables & Apparel"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_150_151
msgid "Consumer Services"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_33
msgid "Consumer Staples"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__country_ids
msgid "Countries"
msgstr "Χώρες"

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Create a Lead Mining Request"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_4
msgid "Customer Service"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Default Tags"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__display_lead_label
msgid "Display Lead Label"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_158_159
msgid "Diversified Financials & Financial Services"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__state__done
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__state__draft
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Draft"
msgstr "Προσχέδιο"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_5
msgid "Education"
msgstr "Εκπαίδευση"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "Email"
msgstr "Email"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_138_156
msgid "Energy & Utilities "
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_6
msgid "Engineering"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__state__error
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Error"
msgstr "Σφάλμα"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__error_type
msgid "Error Type"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__contact_filter_type
msgid "Filter on"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__filter_on_size
msgid "Filter on Size"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_7
msgid "Finance"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_153_154
msgid "Food, Beverage & Tobacco"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_8
msgid "Founder"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "From"
msgstr "Από"

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_case_kanban_view_leads
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_lead_view_tree_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_lead_view_tree_opportunity
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.view_crm_lead_kanban
#, python-format
msgid "Generate Leads"
msgstr ""

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Generate new leads based on their country, industry, size, etc."
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_ids
msgid "Generated Lead / Opportunity"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_160
msgid "Health Care Equipment & Services"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_9
msgid "Health Professional"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_helpers
msgid "Helper methods for crm_iap_mine modules"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "How many leads would you like?"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_10
msgid "Human Resources"
msgstr "Ανθρώπινοι Πόροι"

#. module: crm_iap_mine
#: model:mail.template,name:crm_iap_mine.lead_generation_no_credits
#: model:mail.template,subject:crm_iap_mine.lead_generation_no_credits
msgid "IAP Lead Generation Notification"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__id
msgid "ID"
msgstr "Κωδικός"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_168
msgid "Independent Power and Renewable Electricity Producers"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__industry_ids
msgid "Industries"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__name
msgid "Industry"
msgstr "Κλάδος"

#. module: crm_iap_mine
#: model:ir.model.constraint,message:crm_iap_mine.constraint_crm_iap_lead_industry_name_uniq
msgid "Industry name already exists!"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_11
msgid "Information Technology"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__error_type__credits
msgid "Insufficient Credits"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_contacts_credits
msgid "Lead Contacts Credits"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_credits
msgid "Lead Credits"
msgstr ""

#. module: crm_iap_mine
#: model:ir.ui.menu,name:crm_iap_mine.crm_menu_lead_generation
msgid "Lead Generation"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_lead__lead_mining_request_id
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Lead Mining Request"
msgstr ""

#. module: crm_iap_mine
#: model:ir.actions.act_window,name:crm_iap_mine.crm_iap_lead_mining_request_action
#: model:ir.ui.menu,name:crm_iap_mine.crm_iap_lead_mining_request_menu_action
msgid "Lead Mining Requests"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_total_credits
msgid "Lead Total Credits"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Σύσταση/Ευκαιρία"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__lead_type__lead
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Leads"
msgstr "Συστάσεις"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_12
msgid "Legal"
msgstr ""

#. module: crm_iap_mine
#. odoo-javascript
#: code:addons/crm_iap_mine/static/src/js/tours/crm_iap_lead.js:0
#, python-format
msgid ""
"Looking for more opportunities?<br>Try the <b>Lead Generation</b> tool."
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_13
msgid "Marketing"
msgstr "Marketing"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_148
msgid "Materials"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_86
msgid "Media"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__name
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "Name"
msgstr "Περιγραφή"

#. module: crm_iap_mine
#: model:ir.model.constraint,message:crm_iap_mine.constraint_crm_iap_lead_seniority_name_uniq
msgid "Name already exists!"
msgstr ""

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_lead.py:0
#, python-format
msgid "Need help reaching your target?"
msgstr ""

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "New"
msgstr "Νέα"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__error_type__no_result
msgid "No Result"
msgstr ""

#. module: crm_iap_mine
#. odoo-javascript
#: code:addons/crm_iap_mine/static/src/js/tours/crm_iap_lead.js:0
#, python-format
msgid "Now, just let the magic happen!"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__contact_number
msgid "Number of Contacts"
msgstr "Αριθμός Επαφών"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_count
msgid "Number of Generated Leads"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_number
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_tree
msgid "Number of Leads"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_14
msgid "Operations"
msgstr "Λειτουργίες"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__lead_type__opportunity
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Opportunities"
msgstr "Ευκαιρίες"

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Opportunity created by Odoo Lead Generation"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__role_ids
msgid "Other Roles"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_15
msgid "Owner"
msgstr "Ιδιοκτήτης"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_role
msgid "People Role"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_seniority
msgid "People Seniority"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_161
msgid "Pharmaceuticals, Biotechnology & Life Sciences"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "Phone"
msgstr "Τηλέφωνο"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Pick States..."
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__preferred_role_id
msgid "Preferred Role"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_16
msgid "President"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_17
msgid "Product"
msgstr "Είδος"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_18
msgid "Public Relations"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_114
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_19
msgid "Real Estate"
msgstr "Κτηματομεσιτικά"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_20
msgid "Recruiting"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__name
msgid "Request Number"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_21
msgid "Research"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_152
msgid "Retailing"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Retry"
msgstr "Επανάληψη"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__reveal_ids
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__reveal_id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__reveal_id
msgid "Reveal"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__contact_filter_type__role
msgid "Role"
msgstr "Ρόλος"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__name
msgid "Role Name"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.constraint,message:crm_iap_mine.constraint_crm_iap_lead_role_name_uniq
msgid "Role name already exists!"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_22
msgid "Sale"
msgstr "Πώληση"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__team_id
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Sales Team"
msgstr "Ομάδα Πώλησης"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__user_id
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Salesperson"
msgstr "Πωλητής"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__contact_filter_type__seniority
msgid "Seniority"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__company_size_min
msgid "Size"
msgstr "Μέγεθος"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_165
msgid "Software & Services"
msgstr ""

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__state_ids
msgid "States"
msgstr "Νομοί/Πολιτείες"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__state
msgid "Status"
msgstr "Κατάσταση"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Submit"
msgstr "Υποβολή"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__tag_ids
msgid "Tags"
msgstr "Ετικέτες"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__search_type
msgid "Target"
msgstr "Στόχος"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_166
msgid "Technology Hardware & Equipment"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_149
msgid "Telecommunication Services"
msgstr ""

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "This makes a total of %d credits for this request."
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_136
msgid "Transportation"
msgstr "Μεταφορές"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_type
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Type"
msgstr "Τύπος"

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"Up to %d additional credits will be consumed to identify %d contacts per "
"company."
msgstr ""

#. module: crm_iap_mine
#. odoo-javascript
#: code:addons/crm_iap_mine/static/src/js/tours/crm_iap_lead.js:0
#, python-format
msgid "Which Industry do you want to target?"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "You do not have enough credits to submit this request."
msgstr ""

#. module: crm_iap_mine
#. odoo-python
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Your request could not be executed: %s"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.seniority,name:crm_iap_mine.crm_iap_mine_seniority_1
msgid "director"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "employees"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.seniority,name:crm_iap_mine.crm_iap_mine_seniority_2
msgid "executive"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.seniority,name:crm_iap_mine.crm_iap_mine_seniority_3
msgid "manager"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "to"
msgstr "σε"
