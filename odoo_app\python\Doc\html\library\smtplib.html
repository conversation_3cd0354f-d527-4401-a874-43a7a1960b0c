<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="smtplib — SMTP protocol client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/smtplib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/smtplib.py The smtplib module defines an SMTP client session object that can be used to send mail to any internet machine with an SMTP or ESMTP listener daemon. For details of SMTP..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/smtplib.py The smtplib module defines an SMTP client session object that can be used to send mail to any internet machine with an SMTP or ESMTP listener daemon. For details of SMTP..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>smtplib — SMTP protocol client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="uuid — UUID objects according to RFC 4122" href="uuid.html" />
    <link rel="prev" title="imaplib — IMAP4 protocol client" href="imaplib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/smtplib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a><ul>
<li><a class="reference internal" href="#smtp-objects">SMTP Objects</a></li>
<li><a class="reference internal" href="#smtp-example">SMTP Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="imaplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="uuid.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/smtplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="uuid.html" title="uuid — UUID objects according to RFC 4122"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="imaplib.html" title="imaplib — IMAP4 protocol client"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-smtplib">
<span id="smtplib-smtp-protocol-client"></span><h1><a class="reference internal" href="#module-smtplib" title="smtplib: SMTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code></a> — SMTP protocol client<a class="headerlink" href="#module-smtplib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/smtplib.py">Lib/smtplib.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-smtplib" title="smtplib: SMTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code></a> module defines an SMTP client session object that can be used
to send mail to any internet machine with an SMTP or ESMTP listener daemon.  For
details of SMTP and ESMTP operation, consult <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc821.html"><strong>RFC 821</strong></a> (Simple Mail Transfer
Protocol) and <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1869.html"><strong>RFC 1869</strong></a> (SMTP Service Extensions).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="smtplib.SMTP">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTP</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host=''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">local_hostname=None</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">source_address=None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a> instance encapsulates an SMTP connection.  It has methods
that support a full repertoire of SMTP and ESMTP operations. If the optional
<em>host</em> and <em>port</em> parameters are given, the SMTP <a class="reference internal" href="#smtplib.SMTP.connect" title="smtplib.SMTP.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a> method is
called with those parameters during initialization.  If specified,
<em>local_hostname</em> is used as the FQDN of the local host in the HELO/EHLO
command.  Otherwise, the local hostname is found using
<a class="reference internal" href="socket.html#socket.getfqdn" title="socket.getfqdn"><code class="xref py py-func docutils literal notranslate"><span class="pre">socket.getfqdn()</span></code></a>.  If the <a class="reference internal" href="#smtplib.SMTP.connect" title="smtplib.SMTP.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a> call returns anything other
than a success code, an <a class="reference internal" href="#smtplib.SMTPConnectError" title="smtplib.SMTPConnectError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPConnectError</span></code></a> is raised. The optional
<em>timeout</em> parameter specifies a timeout in seconds for blocking operations
like the connection attempt (if not specified, the global default timeout
setting will be used).  If the timeout expires, <a class="reference internal" href="exceptions.html#TimeoutError" title="TimeoutError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutError</span></code></a> is
raised.  The optional <em>source_address</em> parameter allows binding
to some specific source address in a machine with multiple network
interfaces, and/or to some specific source TCP port. It takes a 2-tuple
<code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code>, for the socket to bind to as its source address before
connecting. If omitted (or if <em>host</em> or <em>port</em> are <code class="docutils literal notranslate"><span class="pre">''</span></code> and/or <code class="docutils literal notranslate"><span class="pre">0</span></code>
respectively) the OS default behavior will be used.</p>
<p>For normal use, you should only require the initialization/connect,
<a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmail()</span></code></a>, and <a class="reference internal" href="#smtplib.SMTP.quit" title="smtplib.SMTP.quit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SMTP.quit()</span></code></a> methods.
An example is included below.</p>
<p>The <a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a> class supports the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  When used
like this, the SMTP <code class="docutils literal notranslate"><span class="pre">QUIT</span></code> command is issued automatically when the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement exits.  E.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">smtplib</span> <span class="kn">import</span> <span class="n">SMTP</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">SMTP</span><span class="p">(</span><span class="s2">&quot;domain.org&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">smtp</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">smtp</span><span class="o">.</span><span class="n">noop</span><span class="p">()</span>
<span class="gp">...</span>
<span class="go">(250, b&#39;Ok&#39;)</span>
<span class="gp">&gt;&gt;&gt;</span>
</pre></div>
</div>
<p class="audit-hook"><p>All commands will raise an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">smtplib.SMTP.send</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code> and <code class="docutils literal notranslate"><span class="pre">data</span></code>,
where <code class="docutils literal notranslate"><span class="pre">data</span></code> is the bytes about to be sent to the remote host.</p>
</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Support for the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>source_address</em> argument was added.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>The SMTPUTF8 extension (<span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6531.html"><strong>RFC 6531</strong></a>) is now supported.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="smtplib.SMTP_SSL">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTP_SSL</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host=''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">local_hostname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">context=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_address=None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP_SSL" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="#smtplib.SMTP_SSL" title="smtplib.SMTP_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP_SSL</span></code></a> instance behaves exactly the same as instances of
<a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a>. <a class="reference internal" href="#smtplib.SMTP_SSL" title="smtplib.SMTP_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP_SSL</span></code></a> should be used for situations where SSL is
required from the beginning of the connection and using <code class="xref py py-meth docutils literal notranslate"><span class="pre">starttls()</span></code> is
not appropriate. If <em>host</em> is not specified, the local host is used. If
<em>port</em> is zero, the standard SMTP-over-SSL port (465) is used.  The optional
arguments <em>local_hostname</em>, <em>timeout</em> and <em>source_address</em> have the same
meaning as they do in the <a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a> class.  <em>context</em>, also optional,
can contain a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">SSLContext</span></code></a> and allows configuring various
aspects of the secure connection.  Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for
best practices.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>context</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>source_address</em> argument was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The class now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The deprecated <em>keyfile</em> and <em>certfile</em> parameters have been removed.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="smtplib.LMTP">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">LMTP</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host=''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=LMTP_PORT</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">local_hostname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_address=None</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.LMTP" title="Link to this definition">¶</a></dt>
<dd><p>The LMTP protocol, which is very similar to ESMTP, is heavily based on the
standard SMTP client. It’s common to use Unix sockets for LMTP, so our
<code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code> method must support that as well as a regular host:port
server. The optional arguments <em>local_hostname</em> and <em>source_address</em> have the
same meaning as they do in the <a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a> class. To specify a Unix
socket, you must use an absolute path for <em>host</em>, starting with a ‘/’.</p>
<p>Authentication is supported, using the regular SMTP mechanism. When using a
Unix socket, LMTP generally don’t support or require any authentication, but
your mileage might vary.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The optional <em>timeout</em> parameter was added.</p>
</div>
</dd></dl>

<p>A nice selection of exceptions is defined as well:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPException">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPException</span></span><a class="headerlink" href="#smtplib.SMTPException" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> that is the base exception class for all
the other exceptions provided by this module.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>SMTPException became subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPServerDisconnected">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPServerDisconnected</span></span><a class="headerlink" href="#smtplib.SMTPServerDisconnected" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised when the server unexpectedly disconnects, or when an
attempt is made to use the <a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a> instance before connecting it to a
server.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPResponseException">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPResponseException</span></span><a class="headerlink" href="#smtplib.SMTPResponseException" title="Link to this definition">¶</a></dt>
<dd><p>Base class for all exceptions that include an SMTP error code. These exceptions
are generated in some instances when the SMTP server returns an error code.  The
error code is stored in the <code class="xref py py-attr docutils literal notranslate"><span class="pre">smtp_code</span></code> attribute of the error, and the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">smtp_error</span></code> attribute is set to the error message.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPSenderRefused">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPSenderRefused</span></span><a class="headerlink" href="#smtplib.SMTPSenderRefused" title="Link to this definition">¶</a></dt>
<dd><p>Sender address refused.  In addition to the attributes set by on all
<a class="reference internal" href="#smtplib.SMTPResponseException" title="smtplib.SMTPResponseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPResponseException</span></code></a> exceptions, this sets ‘sender’ to the string that
the SMTP server refused.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPRecipientsRefused">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPRecipientsRefused</span></span><a class="headerlink" href="#smtplib.SMTPRecipientsRefused" title="Link to this definition">¶</a></dt>
<dd><p>All recipient addresses refused.  The errors for each recipient are accessible
through the attribute <code class="xref py py-attr docutils literal notranslate"><span class="pre">recipients</span></code>, which is a dictionary of exactly the
same sort as <a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SMTP.sendmail()</span></code></a> returns.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPDataError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPDataError</span></span><a class="headerlink" href="#smtplib.SMTPDataError" title="Link to this definition">¶</a></dt>
<dd><p>The SMTP server refused to accept the message data.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPConnectError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPConnectError</span></span><a class="headerlink" href="#smtplib.SMTPConnectError" title="Link to this definition">¶</a></dt>
<dd><p>Error occurred during establishment of a connection  with the server.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPHeloError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPHeloError</span></span><a class="headerlink" href="#smtplib.SMTPHeloError" title="Link to this definition">¶</a></dt>
<dd><p>The server refused our <code class="docutils literal notranslate"><span class="pre">HELO</span></code> message.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPNotSupportedError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPNotSupportedError</span></span><a class="headerlink" href="#smtplib.SMTPNotSupportedError" title="Link to this definition">¶</a></dt>
<dd><p>The command or option attempted is not supported by the server.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="smtplib.SMTPAuthenticationError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">smtplib.</span></span><span class="sig-name descname"><span class="pre">SMTPAuthenticationError</span></span><a class="headerlink" href="#smtplib.SMTPAuthenticationError" title="Link to this definition">¶</a></dt>
<dd><p>SMTP authentication went wrong.  Most probably the server didn’t accept the
username/password combination provided.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc821.html"><strong>RFC 821</strong></a> - Simple Mail Transfer Protocol</dt><dd><p>Protocol definition for SMTP.  This document covers the model, operating
procedure, and protocol details for SMTP.</p>
</dd>
<dt><span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1869.html"><strong>RFC 1869</strong></a> - SMTP Service Extensions</dt><dd><p>Definition of the ESMTP extensions for SMTP.  This describes a framework for
extending SMTP with new commands, supporting dynamic discovery of the commands
provided by the server, and defines a few additional commands.</p>
</dd>
</dl>
</div>
<section id="smtp-objects">
<span id="id1"></span><h2>SMTP Objects<a class="headerlink" href="#smtp-objects" title="Link to this heading">¶</a></h2>
<p>An <a class="reference internal" href="#smtplib.SMTP" title="smtplib.SMTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTP</span></code></a> instance has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.set_debuglevel">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">set_debuglevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.set_debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>Set the debug output level.  A value of 1 or <code class="docutils literal notranslate"><span class="pre">True</span></code> for <em>level</em> results in
debug messages for connection and for all messages sent to and received from
the server.  A value of 2 for <em>level</em> results in these messages being
timestamped.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added debuglevel 2.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.docmd">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">docmd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.docmd" title="Link to this definition">¶</a></dt>
<dd><p>Send a command <em>cmd</em> to the server.  The optional argument <em>args</em> is simply
concatenated to the command, separated by a space.</p>
<p>This returns a 2-tuple composed of a numeric response code and the actual
response line (multiline responses are joined into one long line.)</p>
<p>In normal operation it should not be necessary to call this method explicitly.
It is used to implement other methods and may be useful for testing private
extensions.</p>
<p>If the connection to the server is lost while waiting for the reply,
<a class="reference internal" href="#smtplib.SMTPServerDisconnected" title="smtplib.SMTPServerDisconnected"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPServerDisconnected</span></code></a> will be raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.connect">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'localhost'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.connect" title="Link to this definition">¶</a></dt>
<dd><p>Connect to a host on a given port.  The defaults are to connect to the local
host at the standard SMTP port (25). If the hostname ends with a colon (<code class="docutils literal notranslate"><span class="pre">':'</span></code>)
followed by a number, that suffix will be stripped off and the number
interpreted as the port number to use. This method is automatically invoked by
the constructor if a host is specified during instantiation.  Returns a
2-tuple of the response code and message sent by the server in its
connection response.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">smtplib.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.helo">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">helo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.helo" title="Link to this definition">¶</a></dt>
<dd><p>Identify yourself to the SMTP server using <code class="docutils literal notranslate"><span class="pre">HELO</span></code>.  The hostname argument
defaults to the fully qualified domain name of the local host.
The message returned by the server is stored as the <code class="xref py py-attr docutils literal notranslate"><span class="pre">helo_resp</span></code> attribute
of the object.</p>
<p>In normal operation it should not be necessary to call this method explicitly.
It will be implicitly called by the <a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmail()</span></code></a> when necessary.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.ehlo">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">ehlo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.ehlo" title="Link to this definition">¶</a></dt>
<dd><p>Identify yourself to an ESMTP server using <code class="docutils literal notranslate"><span class="pre">EHLO</span></code>.  The hostname argument
defaults to the fully qualified domain name of the local host.  Examine the
response for ESMTP option and store them for use by <a class="reference internal" href="#smtplib.SMTP.has_extn" title="smtplib.SMTP.has_extn"><code class="xref py py-meth docutils literal notranslate"><span class="pre">has_extn()</span></code></a>.
Also sets several informational attributes: the message returned by
the server is stored as the <code class="xref py py-attr docutils literal notranslate"><span class="pre">ehlo_resp</span></code> attribute, <code class="xref py py-attr docutils literal notranslate"><span class="pre">does_esmtp</span></code>
is set to <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> depending on whether the server supports
ESMTP, and <code class="xref py py-attr docutils literal notranslate"><span class="pre">esmtp_features</span></code> will be a dictionary containing the names
of the SMTP service extensions this server supports, and their parameters
(if any).</p>
<p>Unless you wish to use <a class="reference internal" href="#smtplib.SMTP.has_extn" title="smtplib.SMTP.has_extn"><code class="xref py py-meth docutils literal notranslate"><span class="pre">has_extn()</span></code></a> before sending mail, it should not be
necessary to call this method explicitly.  It will be implicitly called by
<a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmail()</span></code></a> when necessary.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.ehlo_or_helo_if_needed">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">ehlo_or_helo_if_needed</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.ehlo_or_helo_if_needed" title="Link to this definition">¶</a></dt>
<dd><p>This method calls <a class="reference internal" href="#smtplib.SMTP.ehlo" title="smtplib.SMTP.ehlo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ehlo()</span></code></a> and/or <a class="reference internal" href="#smtplib.SMTP.helo" title="smtplib.SMTP.helo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">helo()</span></code></a> if there has been no
previous <code class="docutils literal notranslate"><span class="pre">EHLO</span></code> or <code class="docutils literal notranslate"><span class="pre">HELO</span></code> command this session.  It tries ESMTP <code class="docutils literal notranslate"><span class="pre">EHLO</span></code>
first.</p>
<dl class="simple">
<dt><a class="reference internal" href="#smtplib.SMTPHeloError" title="smtplib.SMTPHeloError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPHeloError</span></code></a></dt><dd><p>The server didn’t reply properly to the <code class="docutils literal notranslate"><span class="pre">HELO</span></code> greeting.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.has_extn">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">has_extn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.has_extn" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if <em>name</em> is in the set of SMTP service extensions returned
by the server, <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> otherwise. Case is ignored.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.verify">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">verify</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.verify" title="Link to this definition">¶</a></dt>
<dd><p>Check the validity of an address on this server using SMTP <code class="docutils literal notranslate"><span class="pre">VRFY</span></code>. Returns a
tuple consisting of code 250 and a full <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a> address (including human
name) if the user address is valid. Otherwise returns an SMTP error code of 400
or greater and an error string.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Many sites disable SMTP <code class="docutils literal notranslate"><span class="pre">VRFY</span></code> in order to foil spammers.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.login">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">login</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">initial_response_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.login" title="Link to this definition">¶</a></dt>
<dd><p>Log in on an SMTP server that requires authentication. The arguments are the
username and the password to authenticate with. If there has been no previous
<code class="docutils literal notranslate"><span class="pre">EHLO</span></code> or <code class="docutils literal notranslate"><span class="pre">HELO</span></code> command this session, this method tries ESMTP <code class="docutils literal notranslate"><span class="pre">EHLO</span></code>
first. This method will return normally if the authentication was successful, or
may raise the following exceptions:</p>
<dl class="simple">
<dt><a class="reference internal" href="#smtplib.SMTPHeloError" title="smtplib.SMTPHeloError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPHeloError</span></code></a></dt><dd><p>The server didn’t reply properly to the <code class="docutils literal notranslate"><span class="pre">HELO</span></code> greeting.</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPAuthenticationError" title="smtplib.SMTPAuthenticationError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPAuthenticationError</span></code></a></dt><dd><p>The server didn’t accept the username/password combination.</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPNotSupportedError" title="smtplib.SMTPNotSupportedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupportedError</span></code></a></dt><dd><p>The <code class="docutils literal notranslate"><span class="pre">AUTH</span></code> command is not supported by the server.</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPException" title="smtplib.SMTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPException</span></code></a></dt><dd><p>No suitable authentication method was found.</p>
</dd>
</dl>
<p>Each of the authentication methods supported by <a class="reference internal" href="#module-smtplib" title="smtplib: SMTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code></a> are tried in
turn if they are advertised as supported by the server.  See <a class="reference internal" href="#smtplib.SMTP.auth" title="smtplib.SMTP.auth"><code class="xref py py-meth docutils literal notranslate"><span class="pre">auth()</span></code></a>
for a list of supported authentication methods.  <em>initial_response_ok</em> is
passed through to <a class="reference internal" href="#smtplib.SMTP.auth" title="smtplib.SMTP.auth"><code class="xref py py-meth docutils literal notranslate"><span class="pre">auth()</span></code></a>.</p>
<p>Optional keyword argument <em>initial_response_ok</em> specifies whether, for
authentication methods that support it, an “initial response” as specified
in <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4954.html"><strong>RFC 4954</strong></a> can be sent along with the <code class="docutils literal notranslate"><span class="pre">AUTH</span></code> command, rather than
requiring a challenge/response.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><a class="reference internal" href="#smtplib.SMTPNotSupportedError" title="smtplib.SMTPNotSupportedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupportedError</span></code></a> may be raised, and the
<em>initial_response_ok</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.auth">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">auth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mechanism</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">authobject</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">initial_response_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.auth" title="Link to this definition">¶</a></dt>
<dd><p>Issue an <code class="docutils literal notranslate"><span class="pre">SMTP</span></code> <code class="docutils literal notranslate"><span class="pre">AUTH</span></code> command for the specified authentication
<em>mechanism</em>, and handle the challenge response via <em>authobject</em>.</p>
<p><em>mechanism</em> specifies which authentication mechanism is to
be used as argument to the <code class="docutils literal notranslate"><span class="pre">AUTH</span></code> command; the valid values are
those listed in the <code class="docutils literal notranslate"><span class="pre">auth</span></code> element of <code class="xref py py-attr docutils literal notranslate"><span class="pre">esmtp_features</span></code>.</p>
<p><em>authobject</em> must be a callable object taking an optional single argument:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="n">authobject</span><span class="p">(</span><span class="n">challenge</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span>
</pre></div>
</div>
<p>If optional keyword argument <em>initial_response_ok</em> is true,
<code class="docutils literal notranslate"><span class="pre">authobject()</span></code> will be called first with no argument.  It can return the
<span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4954.html"><strong>RFC 4954</strong></a> “initial response” ASCII <code class="docutils literal notranslate"><span class="pre">str</span></code> which will be encoded and sent with
the <code class="docutils literal notranslate"><span class="pre">AUTH</span></code> command as below.  If the <code class="docutils literal notranslate"><span class="pre">authobject()</span></code> does not support an
initial response (e.g. because it requires a challenge), it should return
<code class="docutils literal notranslate"><span class="pre">None</span></code> when called with <code class="docutils literal notranslate"><span class="pre">challenge=None</span></code>.  If <em>initial_response_ok</em> is
false, then <code class="docutils literal notranslate"><span class="pre">authobject()</span></code> will not be called first with <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>If the initial response check returns <code class="docutils literal notranslate"><span class="pre">None</span></code>, or if <em>initial_response_ok</em> is
false, <code class="docutils literal notranslate"><span class="pre">authobject()</span></code> will be called to process the server’s challenge
response; the <em>challenge</em> argument it is passed will be a <code class="docutils literal notranslate"><span class="pre">bytes</span></code>.  It
should return ASCII <code class="docutils literal notranslate"><span class="pre">str</span></code> <em>data</em> that will be base64 encoded and sent to the
server.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">SMTP</span></code> class provides <code class="docutils literal notranslate"><span class="pre">authobjects</span></code> for the <code class="docutils literal notranslate"><span class="pre">CRAM-MD5</span></code>, <code class="docutils literal notranslate"><span class="pre">PLAIN</span></code>,
and <code class="docutils literal notranslate"><span class="pre">LOGIN</span></code> mechanisms; they are named <code class="docutils literal notranslate"><span class="pre">SMTP.auth_cram_md5</span></code>,
<code class="docutils literal notranslate"><span class="pre">SMTP.auth_plain</span></code>, and <code class="docutils literal notranslate"><span class="pre">SMTP.auth_login</span></code> respectively.  They all require
that the <code class="docutils literal notranslate"><span class="pre">user</span></code> and <code class="docutils literal notranslate"><span class="pre">password</span></code> properties of the <code class="docutils literal notranslate"><span class="pre">SMTP</span></code> instance are
set to appropriate values.</p>
<p>User code does not normally need to call <code class="docutils literal notranslate"><span class="pre">auth</span></code> directly, but can instead
call the <a class="reference internal" href="#smtplib.SMTP.login" title="smtplib.SMTP.login"><code class="xref py py-meth docutils literal notranslate"><span class="pre">login()</span></code></a> method, which will try each of the above mechanisms
in turn, in the order listed.  <code class="docutils literal notranslate"><span class="pre">auth</span></code> is exposed to facilitate the
implementation of authentication methods not (or not yet) supported
directly by <a class="reference internal" href="#module-smtplib" title="smtplib: SMTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.starttls">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">starttls</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.starttls" title="Link to this definition">¶</a></dt>
<dd><p>Put the SMTP connection in TLS (Transport Layer Security) mode.  All SMTP
commands that follow will be encrypted.  You should then call <a class="reference internal" href="#smtplib.SMTP.ehlo" title="smtplib.SMTP.ehlo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ehlo()</span></code></a>
again.</p>
<p>If <em>keyfile</em> and <em>certfile</em> are provided, they are used to create an
<a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a>.</p>
<p>Optional <em>context</em> parameter is an <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> object; This is
an alternative to using a keyfile and a certfile and if specified both
<em>keyfile</em> and <em>certfile</em> should be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>If there has been no previous <code class="docutils literal notranslate"><span class="pre">EHLO</span></code> or <code class="docutils literal notranslate"><span class="pre">HELO</span></code> command this session,
this method tries ESMTP <code class="docutils literal notranslate"><span class="pre">EHLO</span></code> first.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The deprecated <em>keyfile</em> and <em>certfile</em> parameters have been removed.</p>
</div>
<dl class="simple">
<dt><a class="reference internal" href="#smtplib.SMTPHeloError" title="smtplib.SMTPHeloError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPHeloError</span></code></a></dt><dd><p>The server didn’t reply properly to the <code class="docutils literal notranslate"><span class="pre">HELO</span></code> greeting.</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPNotSupportedError" title="smtplib.SMTPNotSupportedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupportedError</span></code></a></dt><dd><p>The server does not support the STARTTLS extension.</p>
</dd>
<dt><a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a></dt><dd><p>SSL/TLS support is not available to your Python interpreter.</p>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>context</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The method now supports hostname check with
<code class="xref py py-attr docutils literal notranslate"><span class="pre">SSLContext.check_hostname</span></code> and <em>Server Name Indicator</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">HAS_SNI</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The error raised for lack of STARTTLS support is now the
<a class="reference internal" href="#smtplib.SMTPNotSupportedError" title="smtplib.SMTPNotSupportedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupportedError</span></code></a> subclass instead of the base
<a class="reference internal" href="#smtplib.SMTPException" title="smtplib.SMTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPException</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.sendmail">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">sendmail</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">from_addr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">to_addrs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mail_options</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rcpt_options</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.sendmail" title="Link to this definition">¶</a></dt>
<dd><p>Send mail.  The required arguments are an <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a> from-address string, a list
of <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a> to-address strings (a bare string will be treated as a list with 1
address), and a message string.  The caller may pass a list of ESMTP options
(such as <code class="docutils literal notranslate"><span class="pre">8bitmime</span></code>) to be used in <code class="docutils literal notranslate"><span class="pre">MAIL</span> <span class="pre">FROM</span></code> commands as <em>mail_options</em>.
ESMTP options (such as <code class="docutils literal notranslate"><span class="pre">DSN</span></code> commands) that should be used with all <code class="docutils literal notranslate"><span class="pre">RCPT</span></code>
commands can be passed as <em>rcpt_options</em>.  (If you need to use different ESMTP
options to different recipients you have to use the low-level methods such as
<code class="xref py py-meth docutils literal notranslate"><span class="pre">mail()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">rcpt()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">data()</span></code> to send the message.)</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <em>from_addr</em> and <em>to_addrs</em> parameters are used to construct the message
envelope used by the transport agents.  <code class="docutils literal notranslate"><span class="pre">sendmail</span></code> does not modify the
message headers in any way.</p>
</div>
<p><em>msg</em> may be a string containing characters in the ASCII range, or a byte
string.  A string is encoded to bytes using the ascii codec, and lone <code class="docutils literal notranslate"><span class="pre">\r</span></code>
and <code class="docutils literal notranslate"><span class="pre">\n</span></code> characters are converted to <code class="docutils literal notranslate"><span class="pre">\r\n</span></code> characters.  A byte string is
not modified.</p>
<p>If there has been no previous <code class="docutils literal notranslate"><span class="pre">EHLO</span></code> or <code class="docutils literal notranslate"><span class="pre">HELO</span></code> command this session, this
method tries ESMTP <code class="docutils literal notranslate"><span class="pre">EHLO</span></code> first. If the server does ESMTP, message size and
each of the specified options will be passed to it (if the option is in the
feature set the server advertises).  If <code class="docutils literal notranslate"><span class="pre">EHLO</span></code> fails, <code class="docutils literal notranslate"><span class="pre">HELO</span></code> will be tried
and ESMTP options suppressed.</p>
<p>This method will return normally if the mail is accepted for at least one
recipient. Otherwise it will raise an exception.  That is, if this method does
not raise an exception, then someone should get your mail. If this method does
not raise an exception, it returns a dictionary, with one entry for each
recipient that was refused.  Each entry contains a tuple of the SMTP error code
and the accompanying error message sent by the server.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> is included in <em>mail_options</em>, and the server supports it,
<em>from_addr</em> and <em>to_addrs</em> may contain non-ASCII characters.</p>
<p>This method may raise the following exceptions:</p>
<dl class="simple">
<dt><a class="reference internal" href="#smtplib.SMTPRecipientsRefused" title="smtplib.SMTPRecipientsRefused"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPRecipientsRefused</span></code></a></dt><dd><p>All recipients were refused.  Nobody got the mail.  The <code class="xref py py-attr docutils literal notranslate"><span class="pre">recipients</span></code>
attribute of the exception object is a dictionary with information about the
refused recipients (like the one returned when at least one recipient was
accepted).</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPHeloError" title="smtplib.SMTPHeloError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPHeloError</span></code></a></dt><dd><p>The server didn’t reply properly to the <code class="docutils literal notranslate"><span class="pre">HELO</span></code> greeting.</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPSenderRefused" title="smtplib.SMTPSenderRefused"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPSenderRefused</span></code></a></dt><dd><p>The server didn’t accept the <em>from_addr</em>.</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPDataError" title="smtplib.SMTPDataError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPDataError</span></code></a></dt><dd><p>The server replied with an unexpected error code (other than a refusal of a
recipient).</p>
</dd>
<dt><a class="reference internal" href="#smtplib.SMTPNotSupportedError" title="smtplib.SMTPNotSupportedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupportedError</span></code></a></dt><dd><p><code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> was given in the <em>mail_options</em> but is not supported by the
server.</p>
</dd>
</dl>
<p>Unless otherwise noted, the connection will be open even after an exception is
raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>msg</em> may be a byte string.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> support added, and <a class="reference internal" href="#smtplib.SMTPNotSupportedError" title="smtplib.SMTPNotSupportedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupportedError</span></code></a> may be
raised if <code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> is specified but the server does not support it.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.send_message">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">send_message</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">from_addr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">to_addrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mail_options</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rcpt_options</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.send_message" title="Link to this definition">¶</a></dt>
<dd><p>This is a convenience method for calling <a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmail()</span></code></a> with the message
represented by an <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> object.  The arguments have
the same meaning as for <a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmail()</span></code></a>, except that <em>msg</em> is a <code class="docutils literal notranslate"><span class="pre">Message</span></code>
object.</p>
<p>If <em>from_addr</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> or <em>to_addrs</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <code class="docutils literal notranslate"><span class="pre">send_message</span></code> fills
those arguments with addresses extracted from the headers of <em>msg</em> as
specified in <span class="target" id="index-11"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>: <em>from_addr</em> is set to the <em class="mailheader">Sender</em>
field if it is present, and otherwise to the <em class="mailheader">From</em> field.
<em>to_addrs</em> combines the values (if any) of the <em class="mailheader">To</em>,
<em class="mailheader">Cc</em>, and <em class="mailheader">Bcc</em> fields from <em>msg</em>.  If exactly one
set of <em class="mailheader">Resent-*</em> headers appear in the message, the regular
headers are ignored and the <em class="mailheader">Resent-*</em> headers are used instead.
If the message contains more than one set of <em class="mailheader">Resent-*</em> headers,
a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised, since there is no way to unambiguously detect
the most recent set of <em class="mailheader">Resent-</em> headers.</p>
<p><code class="docutils literal notranslate"><span class="pre">send_message</span></code> serializes <em>msg</em> using
<a class="reference internal" href="email.generator.html#email.generator.BytesGenerator" title="email.generator.BytesGenerator"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesGenerator</span></code></a> with <code class="docutils literal notranslate"><span class="pre">\r\n</span></code> as the <em>linesep</em>, and
calls <a class="reference internal" href="#smtplib.SMTP.sendmail" title="smtplib.SMTP.sendmail"><code class="xref py py-meth docutils literal notranslate"><span class="pre">sendmail()</span></code></a> to transmit the resulting message.  Regardless of the
values of <em>from_addr</em> and <em>to_addrs</em>, <code class="docutils literal notranslate"><span class="pre">send_message</span></code> does not transmit any
<em class="mailheader">Bcc</em> or <em class="mailheader">Resent-Bcc</em> headers that may appear
in <em>msg</em>.  If any of the addresses in <em>from_addr</em> and <em>to_addrs</em> contain
non-ASCII characters and the server does not advertise <code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> support,
an <code class="xref py py-exc docutils literal notranslate"><span class="pre">SMTPNotSupported</span></code> error is raised.  Otherwise the <code class="docutils literal notranslate"><span class="pre">Message</span></code> is
serialized with a clone of its <a class="reference internal" href="email.policy.html#module-email.policy" title="email.policy: Controlling the parsing and generating of messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">policy</span></code></a> with the
<a class="reference internal" href="email.policy.html#email.policy.EmailPolicy.utf8" title="email.policy.EmailPolicy.utf8"><code class="xref py py-attr docutils literal notranslate"><span class="pre">utf8</span></code></a> attribute set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, and
<code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> and <code class="docutils literal notranslate"><span class="pre">BODY=8BITMIME</span></code> are added to <em>mail_options</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>Support for internationalized addresses (<code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code>).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="smtplib.SMTP.quit">
<span class="sig-prename descclassname"><span class="pre">SMTP.</span></span><span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#smtplib.SMTP.quit" title="Link to this definition">¶</a></dt>
<dd><p>Terminate the SMTP session and close the connection.  Return the result of
the SMTP <code class="docutils literal notranslate"><span class="pre">QUIT</span></code> command.</p>
</dd></dl>

<p>Low-level methods corresponding to the standard SMTP/ESMTP commands <code class="docutils literal notranslate"><span class="pre">HELP</span></code>,
<code class="docutils literal notranslate"><span class="pre">RSET</span></code>, <code class="docutils literal notranslate"><span class="pre">NOOP</span></code>, <code class="docutils literal notranslate"><span class="pre">MAIL</span></code>, <code class="docutils literal notranslate"><span class="pre">RCPT</span></code>, and <code class="docutils literal notranslate"><span class="pre">DATA</span></code> are also supported.
Normally these do not need to be called directly, so they are not documented
here.  For details, consult the module code.</p>
</section>
<section id="smtp-example">
<span id="id2"></span><h2>SMTP Example<a class="headerlink" href="#smtp-example" title="Link to this heading">¶</a></h2>
<p>This example prompts the user for addresses needed in the message envelope (‘To’
and ‘From’ addresses), and the message to be delivered.  Note that the headers
to be included with the message must be included in the message as entered; this
example doesn’t do any processing of the <span class="target" id="index-12"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a> headers.  In particular, the
‘To’ and ‘From’ addresses must be included in the message headers explicitly.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">smtplib</span>

<span class="k">def</span> <span class="nf">prompt</span><span class="p">(</span><span class="n">prompt</span><span class="p">):</span>
    <span class="k">return</span> <span class="nb">input</span><span class="p">(</span><span class="n">prompt</span><span class="p">)</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>

<span class="n">fromaddr</span> <span class="o">=</span> <span class="n">prompt</span><span class="p">(</span><span class="s2">&quot;From: &quot;</span><span class="p">)</span>
<span class="n">toaddrs</span>  <span class="o">=</span> <span class="n">prompt</span><span class="p">(</span><span class="s2">&quot;To: &quot;</span><span class="p">)</span><span class="o">.</span><span class="n">split</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Enter message, end with ^D (Unix) or ^Z (Windows):&quot;</span><span class="p">)</span>

<span class="c1"># Add the From: and To: headers at the start!</span>
<span class="n">msg</span> <span class="o">=</span> <span class="p">(</span><span class="s2">&quot;From: </span><span class="si">%s</span><span class="se">\r\n</span><span class="s2">To: </span><span class="si">%s</span><span class="se">\r\n\r\n</span><span class="s2">&quot;</span>
       <span class="o">%</span> <span class="p">(</span><span class="n">fromaddr</span><span class="p">,</span> <span class="s2">&quot;, &quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">toaddrs</span><span class="p">)))</span>
<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">line</span> <span class="o">=</span> <span class="nb">input</span><span class="p">()</span>
    <span class="k">except</span> <span class="ne">EOFError</span><span class="p">:</span>
        <span class="k">break</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">line</span><span class="p">:</span>
        <span class="k">break</span>
    <span class="n">msg</span> <span class="o">=</span> <span class="n">msg</span> <span class="o">+</span> <span class="n">line</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Message length is&quot;</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">msg</span><span class="p">))</span>

<span class="n">server</span> <span class="o">=</span> <span class="n">smtplib</span><span class="o">.</span><span class="n">SMTP</span><span class="p">(</span><span class="s1">&#39;localhost&#39;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">set_debuglevel</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">sendmail</span><span class="p">(</span><span class="n">fromaddr</span><span class="p">,</span> <span class="n">toaddrs</span><span class="p">,</span> <span class="n">msg</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">quit</span><span class="p">()</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In general, you will want to use the <a class="reference internal" href="email.html#module-email" title="email: Package supporting the parsing, manipulating, and generating email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code></a> package’s features to
construct an email message, which you can then send
via <a class="reference internal" href="#smtplib.SMTP.send_message" title="smtplib.SMTP.send_message"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send_message()</span></code></a>; see <a class="reference internal" href="email.examples.html#email-examples"><span class="std std-ref">email: Examples</span></a>.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a><ul>
<li><a class="reference internal" href="#smtp-objects">SMTP Objects</a></li>
<li><a class="reference internal" href="#smtp-example">SMTP Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="imaplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="uuid.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/smtplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="uuid.html" title="uuid — UUID objects according to RFC 4122"
             >next</a> |</li>
        <li class="right" >
          <a href="imaplib.html" title="imaplib — IMAP4 protocol client"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>