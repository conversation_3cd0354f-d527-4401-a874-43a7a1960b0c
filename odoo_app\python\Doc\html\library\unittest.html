<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="unittest — Unit testing framework" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/unittest.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/unittest/__init__.py(If you are already familiar with the basic concepts of testing, you might want to skip to the list of assert methods.) The unittest unit testing framework was ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/unittest/__init__.py(If you are already familiar with the basic concepts of testing, you might want to skip to the list of assert methods.) The unittest unit testing framework was ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>unittest — Unit testing framework &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="unittest.mock — mock object library" href="unittest.mock.html" />
    <link rel="prev" title="doctest — Test interactive Python examples" href="doctest.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/unittest.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code> — Unit testing framework</a><ul>
<li><a class="reference internal" href="#basic-example">Basic example</a></li>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command-line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#test-discovery">Test Discovery</a></li>
<li><a class="reference internal" href="#organizing-test-code">Organizing test code</a></li>
<li><a class="reference internal" href="#re-using-old-test-code">Re-using old test code</a></li>
<li><a class="reference internal" href="#skipping-tests-and-expected-failures">Skipping tests and expected failures</a></li>
<li><a class="reference internal" href="#distinguishing-test-iterations-using-subtests">Distinguishing test iterations using subtests</a></li>
<li><a class="reference internal" href="#classes-and-functions">Classes and functions</a><ul>
<li><a class="reference internal" href="#test-cases">Test cases</a></li>
<li><a class="reference internal" href="#grouping-tests">Grouping tests</a></li>
<li><a class="reference internal" href="#loading-and-running-tests">Loading and running tests</a><ul>
<li><a class="reference internal" href="#load-tests-protocol">load_tests Protocol</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#class-and-module-fixtures">Class and Module Fixtures</a><ul>
<li><a class="reference internal" href="#setupclass-and-teardownclass">setUpClass and tearDownClass</a></li>
<li><a class="reference internal" href="#setupmodule-and-teardownmodule">setUpModule and tearDownModule</a></li>
</ul>
</li>
<li><a class="reference internal" href="#signal-handling">Signal Handling</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="doctest.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code> — Test interactive Python examples</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unittest.mock.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest.mock</span></code> — mock object library</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/unittest.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="unittest.mock.html" title="unittest.mock — mock object library"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="doctest.html" title="doctest — Test interactive Python examples"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" accesskey="U">Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code> — Unit testing framework</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-unittest">
<span id="unittest-unit-testing-framework"></span><h1><a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> — Unit testing framework<a class="headerlink" href="#module-unittest" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/unittest/__init__.py">Lib/unittest/__init__.py</a></p>
<hr class="docutils" />
<p>(If you are already familiar with the basic concepts of testing, you might want
to skip to <a class="reference internal" href="#assert-methods"><span class="std std-ref">the list of assert methods</span></a>.)</p>
<p>The <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> unit testing framework was originally inspired by JUnit
and has a similar flavor as major unit testing frameworks in other
languages.  It supports test automation, sharing of setup and shutdown code
for tests, aggregation of tests into collections, and independence of the
tests from the reporting framework.</p>
<p>To achieve this, <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> supports some important concepts in an
object-oriented way:</p>
<dl class="simple">
<dt>test fixture</dt><dd><p>A <em class="dfn">test fixture</em> represents the preparation needed to perform one or more
tests, and any associated cleanup actions.  This may involve, for example,
creating temporary or proxy databases, directories, or starting a server
process.</p>
</dd>
<dt>test case</dt><dd><p>A <em class="dfn">test case</em> is the individual unit of testing.  It checks for a specific
response to a particular set of inputs.  <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> provides a base class,
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>, which may be used to create new test cases.</p>
</dd>
<dt>test suite</dt><dd><p>A <em class="dfn">test suite</em> is a collection of test cases, test suites, or both.  It is
used to aggregate tests that should be executed together.</p>
</dd>
<dt>test runner</dt><dd><p>A <em class="dfn">test runner</em> is a component which orchestrates the execution of tests
and provides the outcome to the user.  The runner may use a graphical interface,
a textual interface, or return a special value to indicate the results of
executing the tests.</p>
</dd>
</dl>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a></dt><dd><p>Another test-support module with a very different flavor.</p>
</dd>
<dt><a class="reference external" href="https://web.archive.org/web/20150315073817/http://www.xprogramming.com/testfram.htm">Simple Smalltalk Testing: With Patterns</a></dt><dd><p>Kent Beck’s original paper on testing frameworks using the pattern shared
by <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>.</p>
</dd>
<dt><a class="reference external" href="https://docs.pytest.org/">pytest</a></dt><dd><p>Third-party unittest framework with a lighter-weight syntax for writing
tests.  For example, <code class="docutils literal notranslate"><span class="pre">assert</span> <span class="pre">func(10)</span> <span class="pre">==</span> <span class="pre">42</span></code>.</p>
</dd>
<dt><a class="reference external" href="https://wiki.python.org/moin/PythonTestingToolsTaxonomy">The Python Testing Tools Taxonomy</a></dt><dd><p>An extensive list of Python testing tools including functional testing
frameworks and mock object libraries.</p>
</dd>
<dt><a class="reference external" href="http://lists.idyll.org/listinfo/testing-in-python">Testing in Python Mailing List</a></dt><dd><p>A special-interest-group for discussion of testing, and testing tools,
in Python.</p>
</dd>
</dl>
<p>The script <code class="file docutils literal notranslate"><span class="pre">Tools/unittestgui/unittestgui.py</span></code> in the Python source distribution is
a GUI tool for test discovery and execution.  This is intended largely for ease of use
for those new to unit testing.  For production environments it is
recommended that tests be driven by a continuous integration system such as
<a class="reference external" href="https://buildbot.net/">Buildbot</a>, <a class="reference external" href="https://www.jenkins.io/">Jenkins</a>,
<a class="reference external" href="https://github.com/features/actions">GitHub Actions</a>, or
<a class="reference external" href="https://www.appveyor.com/">AppVeyor</a>.</p>
</div>
<section id="basic-example">
<span id="unittest-minimal-example"></span><h2>Basic example<a class="headerlink" href="#basic-example" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> module provides a rich set of tools for constructing and
running tests.  This section demonstrates that a small subset of the tools
suffice to meet the needs of most users.</p>
<p>Here is a short script to test three string methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">unittest</span>

<span class="k">class</span> <span class="nc">TestStringMethods</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">test_upper</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="o">.</span><span class="n">upper</span><span class="p">(),</span> <span class="s1">&#39;FOO&#39;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">test_isupper</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertTrue</span><span class="p">(</span><span class="s1">&#39;FOO&#39;</span><span class="o">.</span><span class="n">isupper</span><span class="p">())</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertFalse</span><span class="p">(</span><span class="s1">&#39;Foo&#39;</span><span class="o">.</span><span class="n">isupper</span><span class="p">())</span>

    <span class="k">def</span> <span class="nf">test_split</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">s</span> <span class="o">=</span> <span class="s1">&#39;hello world&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">split</span><span class="p">(),</span> <span class="p">[</span><span class="s1">&#39;hello&#39;</span><span class="p">,</span> <span class="s1">&#39;world&#39;</span><span class="p">])</span>
        <span class="c1"># check that s.split fails when the separator is not a string</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertRaises</span><span class="p">(</span><span class="ne">TypeError</span><span class="p">):</span>
            <span class="n">s</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">unittest</span><span class="o">.</span><span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
<p>A testcase is created by subclassing <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">unittest.TestCase</span></code></a>.  The three
individual tests are defined with methods whose names start with the letters
<code class="docutils literal notranslate"><span class="pre">test</span></code>.  This naming convention informs the test runner about which methods
represent tests.</p>
<p>The crux of each test is a call to <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a> to check for an
expected result; <a class="reference internal" href="#unittest.TestCase.assertTrue" title="unittest.TestCase.assertTrue"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertTrue()</span></code></a> or <a class="reference internal" href="#unittest.TestCase.assertFalse" title="unittest.TestCase.assertFalse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertFalse()</span></code></a>
to verify a condition; or <a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises()</span></code></a> to verify that a
specific exception gets raised.  These methods are used instead of the
<a class="reference internal" href="../reference/simple_stmts.html#assert"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">assert</span></code></a> statement so the test runner can accumulate all test results
and produce a report.</p>
<p>The <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> and <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> methods allow you
to define instructions that will be executed before and after each test method.
They are covered in more detail in the section <a class="reference internal" href="#organizing-tests"><span class="std std-ref">Organizing test code</span></a>.</p>
<p>The final block shows a simple way to run the tests. <a class="reference internal" href="#unittest.main" title="unittest.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">unittest.main()</span></code></a>
provides a command-line interface to the test script.  When run from the command
line, the above script produces an output that looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">...</span>
<span class="o">----------------------------------------------------------------------</span>
<span class="n">Ran</span> <span class="mi">3</span> <span class="n">tests</span> <span class="ow">in</span> <span class="mf">0.000</span><span class="n">s</span>

<span class="n">OK</span>
</pre></div>
</div>
<p>Passing the <code class="docutils literal notranslate"><span class="pre">-v</span></code> option to your test script will instruct <a class="reference internal" href="#unittest.main" title="unittest.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">unittest.main()</span></code></a>
to enable a higher level of verbosity, and produce the following output:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">test_isupper</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">TestStringMethods</span><span class="o">.</span><span class="n">test_isupper</span><span class="p">)</span> <span class="o">...</span> <span class="n">ok</span>
<span class="n">test_split</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">TestStringMethods</span><span class="o">.</span><span class="n">test_split</span><span class="p">)</span> <span class="o">...</span> <span class="n">ok</span>
<span class="n">test_upper</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">TestStringMethods</span><span class="o">.</span><span class="n">test_upper</span><span class="p">)</span> <span class="o">...</span> <span class="n">ok</span>

<span class="o">----------------------------------------------------------------------</span>
<span class="n">Ran</span> <span class="mi">3</span> <span class="n">tests</span> <span class="ow">in</span> <span class="mf">0.001</span><span class="n">s</span>

<span class="n">OK</span>
</pre></div>
</div>
<p>The above examples show the most commonly used <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> features which
are sufficient to meet many everyday testing needs.  The remainder of the
documentation explores the full feature set from first principles.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The behavior of returning a value from a test method (other than the default
<code class="docutils literal notranslate"><span class="pre">None</span></code> value), is now deprecated.</p>
</div>
</section>
<section id="command-line-interface">
<span id="unittest-command-line-interface"></span><h2>Command-Line Interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<p>The unittest module can be used from the command line to run tests from
modules, classes or even individual test methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">test_module1</span> <span class="n">test_module2</span>
<span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">test_module</span><span class="o">.</span><span class="n">TestClass</span>
<span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">test_module</span><span class="o">.</span><span class="n">TestClass</span><span class="o">.</span><span class="n">test_method</span>
</pre></div>
</div>
<p>You can pass in a list with any combination of module names, and fully
qualified class or method names.</p>
<p>Test modules can be specified by file path as well:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">tests</span><span class="o">/</span><span class="n">test_something</span><span class="o">.</span><span class="n">py</span>
</pre></div>
</div>
<p>This allows you to use the shell filename completion to specify the test module.
The file specified must still be importable as a module. The path is converted
to a module name by removing the ‘.py’ and converting path separators into ‘.’.
If you want to execute a test file that isn’t importable as a module you should
execute the file directly instead.</p>
<p>You can run tests with more detail (higher verbosity) by passing in the -v flag:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="o">-</span><span class="n">v</span> <span class="n">test_module</span>
</pre></div>
</div>
<p>When executed without arguments <a class="reference internal" href="#unittest-test-discovery"><span class="std std-ref">Test Discovery</span></a> is started:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span>
</pre></div>
</div>
<p>For a list of all the command-line options:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="o">-</span><span class="n">h</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>In earlier versions it was only possible to run individual test methods and
not modules or classes.</p>
</div>
<section id="command-line-options">
<h3>Command-line options<a class="headerlink" href="#command-line-options" title="Link to this heading">¶</a></h3>
<p><strong class="program">unittest</strong> supports these command-line options:</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-b">
<span id="cmdoption-unittest-buffer"></span><span class="sig-name descname"><span class="pre">-b</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--buffer</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-unittest-b" title="Link to this definition">¶</a></dt>
<dd><p>The standard output and standard error streams are buffered during the test
run. Output during a passing test is discarded. Output is echoed normally
on test fail or error and is added to the failure messages.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-c">
<span id="cmdoption-unittest-catch"></span><span class="sig-name descname"><span class="pre">-c</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--catch</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-unittest-c" title="Link to this definition">¶</a></dt>
<dd><p><kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>-<kbd class="kbd docutils literal notranslate">C</kbd></kbd> during the test run waits for the current test to end and then
reports all the results so far. A second <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>-<kbd class="kbd docutils literal notranslate">C</kbd></kbd> raises the normal
<a class="reference internal" href="exceptions.html#KeyboardInterrupt" title="KeyboardInterrupt"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyboardInterrupt</span></code></a> exception.</p>
<p>See <a class="reference internal" href="#signal-handling">Signal Handling</a> for the functions that provide this functionality.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-f">
<span id="cmdoption-unittest-failfast"></span><span class="sig-name descname"><span class="pre">-f</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--failfast</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-unittest-f" title="Link to this definition">¶</a></dt>
<dd><p>Stop the test run on the first error or failure.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-k">
<span class="sig-name descname"><span class="pre">-k</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-unittest-k" title="Link to this definition">¶</a></dt>
<dd><p>Only run test methods and classes that match the pattern or substring.
This option may be used multiple times, in which case all test cases that
match any of the given patterns are included.</p>
<p>Patterns that contain a wildcard character (<code class="docutils literal notranslate"><span class="pre">*</span></code>) are matched against the
test name using <a class="reference internal" href="fnmatch.html#fnmatch.fnmatchcase" title="fnmatch.fnmatchcase"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fnmatch.fnmatchcase()</span></code></a>; otherwise simple case-sensitive
substring matching is used.</p>
<p>Patterns are matched against the fully qualified test method name as
imported by the test loader.</p>
<p>For example, <code class="docutils literal notranslate"><span class="pre">-k</span> <span class="pre">foo</span></code> matches <code class="docutils literal notranslate"><span class="pre">foo_tests.SomeTest.test_something</span></code>,
<code class="docutils literal notranslate"><span class="pre">bar_tests.SomeTest.test_foo</span></code>, but not <code class="docutils literal notranslate"><span class="pre">bar_tests.FooTest.test_something</span></code>.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-locals">
<span class="sig-name descname"><span class="pre">--locals</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-unittest-locals" title="Link to this definition">¶</a></dt>
<dd><p>Show local variables in tracebacks.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-durations">
<span class="sig-name descname"><span class="pre">--durations</span></span><span class="sig-prename descclassname"> <span class="pre">N</span></span><a class="headerlink" href="#cmdoption-unittest-durations" title="Link to this definition">¶</a></dt>
<dd><p>Show the N slowest test cases (N=0 for all).</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span>The command-line options <code class="docutils literal notranslate"><span class="pre">-b</span></code>, <code class="docutils literal notranslate"><span class="pre">-c</span></code> and <code class="docutils literal notranslate"><span class="pre">-f</span></code> were added.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>The command-line option <code class="docutils literal notranslate"><span class="pre">--locals</span></code>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>The command-line option <code class="docutils literal notranslate"><span class="pre">-k</span></code>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12: </span>The command-line option <code class="docutils literal notranslate"><span class="pre">--durations</span></code>.</p>
</div>
<p>The command line can also be used for test discovery, for running all of the
tests in a project or just a subset.</p>
</section>
</section>
<section id="test-discovery">
<span id="unittest-test-discovery"></span><h2>Test Discovery<a class="headerlink" href="#test-discovery" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>Unittest supports simple test discovery. In order to be compatible with test
discovery, all of the test files must be <a class="reference internal" href="../tutorial/modules.html#tut-modules"><span class="std std-ref">modules</span></a> or
<a class="reference internal" href="../tutorial/modules.html#tut-packages"><span class="std std-ref">packages</span></a> importable from the top-level directory of
the project (this means that their filenames must be valid <a class="reference internal" href="../reference/lexical_analysis.html#identifiers"><span class="std std-ref">identifiers</span></a>).</p>
<p>Test discovery is implemented in <a class="reference internal" href="#unittest.TestLoader.discover" title="unittest.TestLoader.discover"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestLoader.discover()</span></code></a>, but can also be
used from the command line. The basic command-line usage is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">cd</span> <span class="n">project_directory</span>
<span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">discover</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>As a shortcut, <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">unittest</span></code> is the equivalent of
<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">unittest</span> <span class="pre">discover</span></code>. If you want to pass arguments to test
discovery the <code class="docutils literal notranslate"><span class="pre">discover</span></code> sub-command must be used explicitly.</p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">discover</span></code> sub-command has the following options:</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-discover-v">
<span id="cmdoption-unittest-discover-verbose"></span><span class="sig-name descname"><span class="pre">-v</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--verbose</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-unittest-discover-v" title="Link to this definition">¶</a></dt>
<dd><p>Verbose output</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-discover-s">
<span id="cmdoption-unittest-discover-start-directory"></span><span class="sig-name descname"><span class="pre">-s</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--start-directory</span></span><span class="sig-prename descclassname"> <span class="pre">directory</span></span><a class="headerlink" href="#cmdoption-unittest-discover-s" title="Link to this definition">¶</a></dt>
<dd><p>Directory to start discovery (<code class="docutils literal notranslate"><span class="pre">.</span></code> default)</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-discover-p">
<span id="cmdoption-unittest-discover-pattern"></span><span class="sig-name descname"><span class="pre">-p</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--pattern</span></span><span class="sig-prename descclassname"> <span class="pre">pattern</span></span><a class="headerlink" href="#cmdoption-unittest-discover-p" title="Link to this definition">¶</a></dt>
<dd><p>Pattern to match test files (<code class="docutils literal notranslate"><span class="pre">test*.py</span></code> default)</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-unittest-discover-t">
<span id="cmdoption-unittest-discover-top-level-directory"></span><span class="sig-name descname"><span class="pre">-t</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--top-level-directory</span></span><span class="sig-prename descclassname"> <span class="pre">directory</span></span><a class="headerlink" href="#cmdoption-unittest-discover-t" title="Link to this definition">¶</a></dt>
<dd><p>Top level directory of project (defaults to start directory)</p>
</dd></dl>

<p>The <a class="reference internal" href="#cmdoption-unittest-discover-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a>, <a class="reference internal" href="#cmdoption-unittest-discover-p"><code class="xref std std-option docutils literal notranslate"><span class="pre">-p</span></code></a>, and <a class="reference internal" href="#cmdoption-unittest-discover-t"><code class="xref std std-option docutils literal notranslate"><span class="pre">-t</span></code></a> options can be passed in
as positional arguments in that order. The following two command lines
are equivalent:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">discover</span> <span class="o">-</span><span class="n">s</span> <span class="n">project_directory</span> <span class="o">-</span><span class="n">p</span> <span class="s2">&quot;*_test.py&quot;</span>
<span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">discover</span> <span class="n">project_directory</span> <span class="s2">&quot;*_test.py&quot;</span>
</pre></div>
</div>
<p>As well as being a path it is possible to pass a package name, for example
<code class="docutils literal notranslate"><span class="pre">myproject.subpackage.test</span></code>, as the start directory. The package name you
supply will then be imported and its location on the filesystem will be used
as the start directory.</p>
<div class="admonition caution">
<p class="admonition-title">Caution</p>
<p>Test discovery loads tests by importing them. Once test discovery has found
all the test files from the start directory you specify it turns the paths
into package names to import. For example <code class="file docutils literal notranslate"><span class="pre">foo/bar/baz.py</span></code> will be
imported as <code class="docutils literal notranslate"><span class="pre">foo.bar.baz</span></code>.</p>
<p>If you have a package installed globally and attempt test discovery on
a different copy of the package then the import <em>could</em> happen from the
wrong place. If this happens test discovery will warn you and exit.</p>
<p>If you supply the start directory as a package name rather than a
path to a directory then discover assumes that whichever location it
imports from is the location you intended, so you will not get the
warning.</p>
</div>
<p>Test modules and packages can customize test loading and discovery by through
the <a class="reference internal" href="#id1">load_tests protocol</a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Test discovery supports <a class="reference internal" href="../glossary.html#term-namespace-package"><span class="xref std std-term">namespace packages</span></a>
for the start directory. Note that you need to specify the top level
directory too (e.g.
<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">unittest</span> <span class="pre">discover</span> <span class="pre">-s</span> <span class="pre">root/namespace</span> <span class="pre">-t</span> <span class="pre">root</span></code>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> dropped the <a class="reference internal" href="../glossary.html#term-namespace-package"><span class="xref std std-term">namespace packages</span></a>
support in Python 3.11. It has been broken since Python 3.7. Start directory and
subdirectories containing tests must be regular package that have
<code class="docutils literal notranslate"><span class="pre">__init__.py</span></code> file.</p>
<p>Directories containing start directory still can be a namespace package.
In this case, you need to specify start directory as dotted package name,
and target directory explicitly. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># proj/  &lt;-- current directory</span>
<span class="c1">#   namespace/</span>
<span class="c1">#     mypkg/</span>
<span class="c1">#       __init__.py</span>
<span class="c1">#       test_mypkg.py</span>

<span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">unittest</span> <span class="n">discover</span> <span class="o">-</span><span class="n">s</span> <span class="n">namespace</span><span class="o">.</span><span class="n">mypkg</span> <span class="o">-</span><span class="n">t</span> <span class="o">.</span>
</pre></div>
</div>
</div>
</section>
<section id="organizing-test-code">
<span id="organizing-tests"></span><h2>Organizing test code<a class="headerlink" href="#organizing-test-code" title="Link to this heading">¶</a></h2>
<p>The basic building blocks of unit testing are <em class="dfn">test cases</em> — single
scenarios that must be set up and checked for correctness.  In <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>,
test cases are represented by <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">unittest.TestCase</span></code></a> instances.
To make your own test cases you must write subclasses of
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> or use <a class="reference internal" href="#unittest.FunctionTestCase" title="unittest.FunctionTestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">FunctionTestCase</span></code></a>.</p>
<p>The testing code of a <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instance should be entirely self
contained, such that it can be run either in isolation or in arbitrary
combination with any number of other test cases.</p>
<p>The simplest <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> subclass will simply implement a test method
(i.e. a method whose name starts with <code class="docutils literal notranslate"><span class="pre">test</span></code>) in order to perform specific
testing code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">unittest</span>

<span class="k">class</span> <span class="nc">DefaultWidgetSizeTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">test_default_widget_size</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">widget</span> <span class="o">=</span> <span class="n">Widget</span><span class="p">(</span><span class="s1">&#39;The widget&#39;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">widget</span><span class="o">.</span><span class="n">size</span><span class="p">(),</span> <span class="p">(</span><span class="mi">50</span><span class="p">,</span> <span class="mi">50</span><span class="p">))</span>
</pre></div>
</div>
<p>Note that in order to test something, we use one of the <a class="reference internal" href="#assert-methods"><span class="std std-ref">assert* methods</span></a>
provided by the <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> base class.  If the test fails, an
exception will be raised with an explanatory message, and <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>
will identify the test case as a <em class="dfn">failure</em>.  Any other exceptions will be
treated as <em class="dfn">errors</em>.</p>
<p>Tests can be numerous, and their set-up can be repetitive.  Luckily, we
can factor out set-up code by implementing a method called
<a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a>, which the testing framework will automatically
call for every single test we run:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">unittest</span>

<span class="k">class</span> <span class="nc">WidgetTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">setUp</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">widget</span> <span class="o">=</span> <span class="n">Widget</span><span class="p">(</span><span class="s1">&#39;The widget&#39;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">test_default_widget_size</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">widget</span><span class="o">.</span><span class="n">size</span><span class="p">(),</span> <span class="p">(</span><span class="mi">50</span><span class="p">,</span><span class="mi">50</span><span class="p">),</span>
                         <span class="s1">&#39;incorrect default size&#39;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">test_widget_resize</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">widget</span><span class="o">.</span><span class="n">resize</span><span class="p">(</span><span class="mi">100</span><span class="p">,</span><span class="mi">150</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">widget</span><span class="o">.</span><span class="n">size</span><span class="p">(),</span> <span class="p">(</span><span class="mi">100</span><span class="p">,</span><span class="mi">150</span><span class="p">),</span>
                         <span class="s1">&#39;wrong size after resize&#39;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The order in which the various tests will be run is determined
by sorting the test method names with respect to the built-in
ordering for strings.</p>
</div>
<p>If the <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> method raises an exception while the test is
running, the framework will consider the test to have suffered an error, and
the test method will not be executed.</p>
<p>Similarly, we can provide a <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> method that tidies up
after the test method has been run:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">unittest</span>

<span class="k">class</span> <span class="nc">WidgetTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">setUp</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">widget</span> <span class="o">=</span> <span class="n">Widget</span><span class="p">(</span><span class="s1">&#39;The widget&#39;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">tearDown</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">widget</span><span class="o">.</span><span class="n">dispose</span><span class="p">()</span>
</pre></div>
</div>
<p>If <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> succeeded, <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> will be
run whether the test method succeeded or not.</p>
<p>Such a working environment for the testing code is called a
<em class="dfn">test fixture</em>.  A new TestCase instance is created as a unique
test fixture used to execute each individual test method.  Thus
<a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a>, <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a>, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code>
will be called once per test.</p>
<p>It is recommended that you use TestCase implementations to group tests together
according to the features they test.  <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> provides a mechanism for
this: the <em class="dfn">test suite</em>, represented by <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>’s
<a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> class.  In most cases, calling <a class="reference internal" href="#unittest.main" title="unittest.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">unittest.main()</span></code></a> will do
the right thing and collect all the module’s test cases for you and execute
them.</p>
<p>However, should you want to customize the building of your test suite,
you can do it yourself:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">suite</span><span class="p">():</span>
    <span class="n">suite</span> <span class="o">=</span> <span class="n">unittest</span><span class="o">.</span><span class="n">TestSuite</span><span class="p">()</span>
    <span class="n">suite</span><span class="o">.</span><span class="n">addTest</span><span class="p">(</span><span class="n">WidgetTestCase</span><span class="p">(</span><span class="s1">&#39;test_default_widget_size&#39;</span><span class="p">))</span>
    <span class="n">suite</span><span class="o">.</span><span class="n">addTest</span><span class="p">(</span><span class="n">WidgetTestCase</span><span class="p">(</span><span class="s1">&#39;test_widget_resize&#39;</span><span class="p">))</span>
    <span class="k">return</span> <span class="n">suite</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">runner</span> <span class="o">=</span> <span class="n">unittest</span><span class="o">.</span><span class="n">TextTestRunner</span><span class="p">()</span>
    <span class="n">runner</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">suite</span><span class="p">())</span>
</pre></div>
</div>
<p>You can place the definitions of test cases and test suites in the same modules
as the code they are to test (such as <code class="file docutils literal notranslate"><span class="pre">widget.py</span></code>), but there are several
advantages to placing the test code in a separate module, such as
<code class="file docutils literal notranslate"><span class="pre">test_widget.py</span></code>:</p>
<ul class="simple">
<li><p>The test module can be run standalone from the command line.</p></li>
<li><p>The test code can more easily be separated from shipped code.</p></li>
<li><p>There is less temptation to change test code to fit the code it tests without
a good reason.</p></li>
<li><p>Test code should be modified much less frequently than the code it tests.</p></li>
<li><p>Tested code can be refactored more easily.</p></li>
<li><p>Tests for modules written in C must be in separate modules anyway, so why not
be consistent?</p></li>
<li><p>If the testing strategy changes, there is no need to change the source code.</p></li>
</ul>
</section>
<section id="re-using-old-test-code">
<span id="legacy-unit-tests"></span><h2>Re-using old test code<a class="headerlink" href="#re-using-old-test-code" title="Link to this heading">¶</a></h2>
<p>Some users will find that they have existing test code that they would like to
run from <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>, without converting every old test function to a
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> subclass.</p>
<p>For this reason, <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> provides a <a class="reference internal" href="#unittest.FunctionTestCase" title="unittest.FunctionTestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">FunctionTestCase</span></code></a> class.
This subclass of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> can be used to wrap an existing test
function.  Set-up and tear-down functions can also be provided.</p>
<p>Given the following test function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">testSomething</span><span class="p">():</span>
    <span class="n">something</span> <span class="o">=</span> <span class="n">makeSomething</span><span class="p">()</span>
    <span class="k">assert</span> <span class="n">something</span><span class="o">.</span><span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
    <span class="c1"># ...</span>
</pre></div>
</div>
<p>one can create an equivalent test case instance as follows, with optional
set-up and tear-down methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">testcase</span> <span class="o">=</span> <span class="n">unittest</span><span class="o">.</span><span class="n">FunctionTestCase</span><span class="p">(</span><span class="n">testSomething</span><span class="p">,</span>
                                     <span class="n">setUp</span><span class="o">=</span><span class="n">makeSomethingDB</span><span class="p">,</span>
                                     <span class="n">tearDown</span><span class="o">=</span><span class="n">deleteSomethingDB</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Even though <a class="reference internal" href="#unittest.FunctionTestCase" title="unittest.FunctionTestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">FunctionTestCase</span></code></a> can be used to quickly convert an
existing test base over to a <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>-based system, this approach is
not recommended.  Taking the time to set up proper <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>
subclasses will make future test refactorings infinitely easier.</p>
</div>
<p>In some cases, the existing tests may have been written using the <a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a>
module.  If so, <a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a> provides a <code class="xref py py-class docutils literal notranslate"><span class="pre">DocTestSuite</span></code> class that can
automatically build <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">unittest.TestSuite</span></code></a> instances from the existing
<a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a>-based tests.</p>
</section>
<section id="skipping-tests-and-expected-failures">
<span id="unittest-skipping"></span><h2>Skipping tests and expected failures<a class="headerlink" href="#skipping-tests-and-expected-failures" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Unittest supports skipping individual test methods and even whole classes of
tests.  In addition, it supports marking a test as an “expected failure,” a test
that is broken and will fail, but shouldn’t be counted as a failure on a
<a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a>.</p>
<p>Skipping a test is simply a matter of using the <a class="reference internal" href="#unittest.skip" title="unittest.skip"><code class="xref py py-func docutils literal notranslate"><span class="pre">skip()</span></code></a> <a class="reference internal" href="../glossary.html#term-decorator"><span class="xref std std-term">decorator</span></a>
or one of its conditional variants, calling <a class="reference internal" href="#unittest.TestCase.skipTest" title="unittest.TestCase.skipTest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestCase.skipTest()</span></code></a> within a
<a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> or test method, or raising <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a> directly.</p>
<p>Basic skipping looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>

    <span class="nd">@unittest</span><span class="o">.</span><span class="n">skip</span><span class="p">(</span><span class="s2">&quot;demonstrating skipping&quot;</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">test_nothing</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fail</span><span class="p">(</span><span class="s2">&quot;shouldn&#39;t happen&quot;</span><span class="p">)</span>

    <span class="nd">@unittest</span><span class="o">.</span><span class="n">skipIf</span><span class="p">(</span><span class="n">mylib</span><span class="o">.</span><span class="n">__version__</span> <span class="o">&lt;</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>
                     <span class="s2">&quot;not supported in this library version&quot;</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">test_format</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># Tests that work for only a certain version of the library.</span>
        <span class="k">pass</span>

    <span class="nd">@unittest</span><span class="o">.</span><span class="n">skipUnless</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">platform</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;win&quot;</span><span class="p">),</span> <span class="s2">&quot;requires Windows&quot;</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">test_windows_support</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># windows specific testing code</span>
        <span class="k">pass</span>

    <span class="k">def</span> <span class="nf">test_maybe_skipped</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">external_resource_available</span><span class="p">():</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">skipTest</span><span class="p">(</span><span class="s2">&quot;external resource not available&quot;</span><span class="p">)</span>
        <span class="c1"># test code that depends on the external resource</span>
        <span class="k">pass</span>
</pre></div>
</div>
<p>This is the output of running the example above in verbose mode:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">test_format</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">MyTestCase</span><span class="o">.</span><span class="n">test_format</span><span class="p">)</span> <span class="o">...</span> <span class="n">skipped</span> <span class="s1">&#39;not supported in this library version&#39;</span>
<span class="n">test_nothing</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">MyTestCase</span><span class="o">.</span><span class="n">test_nothing</span><span class="p">)</span> <span class="o">...</span> <span class="n">skipped</span> <span class="s1">&#39;demonstrating skipping&#39;</span>
<span class="n">test_maybe_skipped</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">MyTestCase</span><span class="o">.</span><span class="n">test_maybe_skipped</span><span class="p">)</span> <span class="o">...</span> <span class="n">skipped</span> <span class="s1">&#39;external resource not available&#39;</span>
<span class="n">test_windows_support</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">MyTestCase</span><span class="o">.</span><span class="n">test_windows_support</span><span class="p">)</span> <span class="o">...</span> <span class="n">skipped</span> <span class="s1">&#39;requires Windows&#39;</span>

<span class="o">----------------------------------------------------------------------</span>
<span class="n">Ran</span> <span class="mi">4</span> <span class="n">tests</span> <span class="ow">in</span> <span class="mf">0.005</span><span class="n">s</span>

<span class="n">OK</span> <span class="p">(</span><span class="n">skipped</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
</pre></div>
</div>
<p>Classes can be skipped just like methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@unittest</span><span class="o">.</span><span class="n">skip</span><span class="p">(</span><span class="s2">&quot;showing class skipping&quot;</span><span class="p">)</span>
<span class="k">class</span> <span class="nc">MySkippedTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">test_not_run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">pass</span>
</pre></div>
</div>
<p><a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestCase.setUp()</span></code></a> can also skip the test.  This is useful when a resource
that needs to be set up is not available.</p>
<p>Expected failures use the <a class="reference internal" href="#unittest.expectedFailure" title="unittest.expectedFailure"><code class="xref py py-func docutils literal notranslate"><span class="pre">expectedFailure()</span></code></a> decorator.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">ExpectedFailureTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="nd">@unittest</span><span class="o">.</span><span class="n">expectedFailure</span>
    <span class="k">def</span> <span class="nf">test_fail</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;broken&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>It’s easy to roll your own skipping decorators by making a decorator that calls
<a class="reference internal" href="#unittest.skip" title="unittest.skip"><code class="xref py py-func docutils literal notranslate"><span class="pre">skip()</span></code></a> on the test when it wants it to be skipped.  This decorator skips
the test unless the passed object has a certain attribute:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">skipUnlessHasattr</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="n">attr</span><span class="p">):</span>
    <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="n">attr</span><span class="p">):</span>
        <span class="k">return</span> <span class="k">lambda</span> <span class="n">func</span><span class="p">:</span> <span class="n">func</span>
    <span class="k">return</span> <span class="n">unittest</span><span class="o">.</span><span class="n">skip</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">{!r}</span><span class="s2"> doesn&#39;t have </span><span class="si">{!r}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="n">attr</span><span class="p">))</span>
</pre></div>
</div>
<p>The following decorators and exception implement test skipping and expected failures:</p>
<dl class="py function">
<dt class="sig sig-object py" id="unittest.skip">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">skip</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reason</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.skip" title="Link to this definition">¶</a></dt>
<dd><p>Unconditionally skip the decorated test.  <em>reason</em> should describe why the
test is being skipped.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.skipIf">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">skipIf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reason</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.skipIf" title="Link to this definition">¶</a></dt>
<dd><p>Skip the decorated test if <em>condition</em> is true.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.skipUnless">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">skipUnless</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reason</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.skipUnless" title="Link to this definition">¶</a></dt>
<dd><p>Skip the decorated test unless <em>condition</em> is true.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.expectedFailure">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">expectedFailure</span></span><a class="headerlink" href="#unittest.expectedFailure" title="Link to this definition">¶</a></dt>
<dd><p>Mark the test as an expected failure or error.  If the test fails or errors
in the test function itself (rather than in one of the <em class="dfn">test fixture</em>
methods) then it will be considered a success.  If the test passes, it will
be considered a failure.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="unittest.SkipTest">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">SkipTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reason</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.SkipTest" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised to skip a test.</p>
<p>Usually you can use <a class="reference internal" href="#unittest.TestCase.skipTest" title="unittest.TestCase.skipTest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestCase.skipTest()</span></code></a> or one of the skipping
decorators instead of raising this directly.</p>
</dd></dl>

<p>Skipped tests will not have <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> or <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> run around them.
Skipped classes will not have <a class="reference internal" href="#unittest.TestCase.setUpClass" title="unittest.TestCase.setUpClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUpClass()</span></code></a> or <a class="reference internal" href="#unittest.TestCase.tearDownClass" title="unittest.TestCase.tearDownClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDownClass()</span></code></a> run.
Skipped modules will not have <code class="xref py py-func docutils literal notranslate"><span class="pre">setUpModule()</span></code> or <code class="xref py py-func docutils literal notranslate"><span class="pre">tearDownModule()</span></code> run.</p>
</section>
<section id="distinguishing-test-iterations-using-subtests">
<span id="subtests"></span><h2>Distinguishing test iterations using subtests<a class="headerlink" href="#distinguishing-test-iterations-using-subtests" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>When there are very small differences among your tests, for
instance some parameters, unittest allows you to distinguish them inside
the body of a test method using the <a class="reference internal" href="#unittest.TestCase.subTest" title="unittest.TestCase.subTest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">subTest()</span></code></a> context manager.</p>
<p>For example, the following test:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">NumbersTest</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">test_even</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Test that numbers between 0 and 5 are all even.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">6</span><span class="p">):</span>
            <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">subTest</span><span class="p">(</span><span class="n">i</span><span class="o">=</span><span class="n">i</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">i</span> <span class="o">%</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>will produce the following output:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">======================================================================</span>
<span class="n">FAIL</span><span class="p">:</span> <span class="n">test_even</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">NumbersTest</span><span class="o">.</span><span class="n">test_even</span><span class="p">)</span> <span class="p">(</span><span class="n">i</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="n">Test</span> <span class="n">that</span> <span class="n">numbers</span> <span class="n">between</span> <span class="mi">0</span> <span class="ow">and</span> <span class="mi">5</span> <span class="n">are</span> <span class="nb">all</span> <span class="n">even</span><span class="o">.</span>
<span class="o">----------------------------------------------------------------------</span>
<span class="n">Traceback</span> <span class="p">(</span><span class="n">most</span> <span class="n">recent</span> <span class="n">call</span> <span class="n">last</span><span class="p">):</span>
  <span class="n">File</span> <span class="s2">&quot;subtests.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">11</span><span class="p">,</span> <span class="ow">in</span> <span class="n">test_even</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">i</span> <span class="o">%</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="o">^^^^^^^^^^^^^^^^^^^^^^^^^^</span>
<span class="ne">AssertionError</span><span class="p">:</span> <span class="mi">1</span> <span class="o">!=</span> <span class="mi">0</span>

<span class="o">======================================================================</span>
<span class="n">FAIL</span><span class="p">:</span> <span class="n">test_even</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">NumbersTest</span><span class="o">.</span><span class="n">test_even</span><span class="p">)</span> <span class="p">(</span><span class="n">i</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>
<span class="n">Test</span> <span class="n">that</span> <span class="n">numbers</span> <span class="n">between</span> <span class="mi">0</span> <span class="ow">and</span> <span class="mi">5</span> <span class="n">are</span> <span class="nb">all</span> <span class="n">even</span><span class="o">.</span>
<span class="o">----------------------------------------------------------------------</span>
<span class="n">Traceback</span> <span class="p">(</span><span class="n">most</span> <span class="n">recent</span> <span class="n">call</span> <span class="n">last</span><span class="p">):</span>
  <span class="n">File</span> <span class="s2">&quot;subtests.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">11</span><span class="p">,</span> <span class="ow">in</span> <span class="n">test_even</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">i</span> <span class="o">%</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="o">^^^^^^^^^^^^^^^^^^^^^^^^^^</span>
<span class="ne">AssertionError</span><span class="p">:</span> <span class="mi">1</span> <span class="o">!=</span> <span class="mi">0</span>

<span class="o">======================================================================</span>
<span class="n">FAIL</span><span class="p">:</span> <span class="n">test_even</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">NumbersTest</span><span class="o">.</span><span class="n">test_even</span><span class="p">)</span> <span class="p">(</span><span class="n">i</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>
<span class="n">Test</span> <span class="n">that</span> <span class="n">numbers</span> <span class="n">between</span> <span class="mi">0</span> <span class="ow">and</span> <span class="mi">5</span> <span class="n">are</span> <span class="nb">all</span> <span class="n">even</span><span class="o">.</span>
<span class="o">----------------------------------------------------------------------</span>
<span class="n">Traceback</span> <span class="p">(</span><span class="n">most</span> <span class="n">recent</span> <span class="n">call</span> <span class="n">last</span><span class="p">):</span>
  <span class="n">File</span> <span class="s2">&quot;subtests.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">11</span><span class="p">,</span> <span class="ow">in</span> <span class="n">test_even</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">i</span> <span class="o">%</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="o">^^^^^^^^^^^^^^^^^^^^^^^^^^</span>
<span class="ne">AssertionError</span><span class="p">:</span> <span class="mi">1</span> <span class="o">!=</span> <span class="mi">0</span>
</pre></div>
</div>
<p>Without using a subtest, execution would stop after the first failure,
and the error would be less easy to diagnose because the value of <code class="docutils literal notranslate"><span class="pre">i</span></code>
wouldn’t be displayed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">======================================================================</span>
<span class="n">FAIL</span><span class="p">:</span> <span class="n">test_even</span> <span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">NumbersTest</span><span class="o">.</span><span class="n">test_even</span><span class="p">)</span>
<span class="o">----------------------------------------------------------------------</span>
<span class="n">Traceback</span> <span class="p">(</span><span class="n">most</span> <span class="n">recent</span> <span class="n">call</span> <span class="n">last</span><span class="p">):</span>
  <span class="n">File</span> <span class="s2">&quot;subtests.py&quot;</span><span class="p">,</span> <span class="n">line</span> <span class="mi">32</span><span class="p">,</span> <span class="ow">in</span> <span class="n">test_even</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">i</span> <span class="o">%</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="ne">AssertionError</span><span class="p">:</span> <span class="mi">1</span> <span class="o">!=</span> <span class="mi">0</span>
</pre></div>
</div>
</section>
<section id="classes-and-functions">
<span id="unittest-contents"></span><h2>Classes and functions<a class="headerlink" href="#classes-and-functions" title="Link to this heading">¶</a></h2>
<p>This section describes in depth the API of <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>.</p>
<section id="test-cases">
<span id="testcase-objects"></span><h3>Test cases<a class="headerlink" href="#test-cases" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="unittest.TestCase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">TestCase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase" title="Link to this definition">¶</a></dt>
<dd><p>Instances of the <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> class represent the logical test units
in the <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> universe.  This class is intended to be used as a base
class, with specific tests being implemented by concrete subclasses.  This class
implements the interface needed by the test runner to allow it to drive the
tests, and methods that the test code can use to check for and report various
kinds of failure.</p>
<p>Each instance of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> will run a single base method: the method
named <em>methodName</em>.
In most uses of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>, you will neither change
the <em>methodName</em> nor reimplement the default <code class="docutils literal notranslate"><span class="pre">runTest()</span></code> method.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> can be instantiated successfully without providing a
<em>methodName</em>. This makes it easier to experiment with <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>
from the interactive interpreter.</p>
</div>
<p><a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances provide three groups of methods: one group used
to run the test, another used by the test implementation to check conditions
and report failures, and some inquiry methods allowing information about the
test itself to be gathered.</p>
<p>Methods in the first group (running the test) are:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.setUp" title="Link to this definition">¶</a></dt>
<dd><p>Method called to prepare the test fixture.  This is called immediately
before calling the test method; other than <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> or <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a>,
any exception raised by this method will be considered an error rather than
a test failure. The default implementation does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.tearDown">
<span class="sig-name descname"><span class="pre">tearDown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.tearDown" title="Link to this definition">¶</a></dt>
<dd><p>Method called immediately after the test method has been called and the
result recorded.  This is called even if the test method raised an
exception, so the implementation in subclasses may need to be particularly
careful about checking internal state.  Any exception, other than
<a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> or <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a>, raised by this method will be
considered an additional error rather than a test failure (thus increasing
the total number of reported errors). This method will only be called if
the <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> succeeds, regardless of the outcome of the test method.
The default implementation does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.setUpClass">
<span class="sig-name descname"><span class="pre">setUpClass</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.setUpClass" title="Link to this definition">¶</a></dt>
<dd><p>A class method called before tests in an individual class are run.
<code class="docutils literal notranslate"><span class="pre">setUpClass</span></code> is called with the class as the only argument
and must be decorated as a <a class="reference internal" href="functions.html#classmethod" title="classmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">classmethod()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@classmethod</span>
<span class="k">def</span> <span class="nf">setUpClass</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="#class-and-module-fixtures">Class and Module Fixtures</a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.tearDownClass">
<span class="sig-name descname"><span class="pre">tearDownClass</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.tearDownClass" title="Link to this definition">¶</a></dt>
<dd><p>A class method called after tests in an individual class have run.
<code class="docutils literal notranslate"><span class="pre">tearDownClass</span></code> is called with the class as the only argument
and must be decorated as a <a class="reference internal" href="functions.html#classmethod" title="classmethod"><code class="xref py py-meth docutils literal notranslate"><span class="pre">classmethod()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@classmethod</span>
<span class="k">def</span> <span class="nf">tearDownClass</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="#class-and-module-fixtures">Class and Module Fixtures</a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.run" title="Link to this definition">¶</a></dt>
<dd><p>Run the test, collecting the result into the <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> object
passed as <em>result</em>.  If <em>result</em> is omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, a temporary
result object is created (by calling the <a class="reference internal" href="#unittest.TestCase.defaultTestResult" title="unittest.TestCase.defaultTestResult"><code class="xref py py-meth docutils literal notranslate"><span class="pre">defaultTestResult()</span></code></a>
method) and used. The result object is returned to <a class="reference internal" href="#unittest.TestCase.run" title="unittest.TestCase.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run()</span></code></a>’s
caller.</p>
<p>The same effect may be had by simply calling the <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>
instance.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Previous versions of <code class="docutils literal notranslate"><span class="pre">run</span></code> did not return the result. Neither did
calling an instance.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.skipTest">
<span class="sig-name descname"><span class="pre">skipTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reason</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.skipTest" title="Link to this definition">¶</a></dt>
<dd><p>Calling this during a test method or <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> skips the current
test.  See <a class="reference internal" href="#unittest-skipping"><span class="std std-ref">Skipping tests and expected failures</span></a> for more information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.subTest">
<span class="sig-name descname"><span class="pre">subTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">params</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.subTest" title="Link to this definition">¶</a></dt>
<dd><p>Return a context manager which executes the enclosed code block as a
subtest.  <em>msg</em> and <em>params</em> are optional, arbitrary values which are
displayed whenever a subtest fails, allowing you to identify them
clearly.</p>
<p>A test case can contain any number of subtest declarations, and
they can be arbitrarily nested.</p>
<p>See <a class="reference internal" href="#subtests"><span class="std std-ref">Distinguishing test iterations using subtests</span></a> for more information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.debug">
<span class="sig-name descname"><span class="pre">debug</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.debug" title="Link to this definition">¶</a></dt>
<dd><p>Run the test without collecting the result.  This allows exceptions raised
by the test to be propagated to the caller, and can be used to support
running tests under a debugger.</p>
</dd></dl>

<p id="assert-methods">The <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> class provides several assert methods to check for and
report failures.  The following table lists the most commonly used methods
(see the tables below for more assert methods):</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Method</p></th>
<th class="head"><p>Checks that</p></th>
<th class="head"><p>New in</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">==</span> <span class="pre">b</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertNotEqual" title="unittest.TestCase.assertNotEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">!=</span> <span class="pre">b</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertTrue" title="unittest.TestCase.assertTrue"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertTrue(x)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bool(x)</span> <span class="pre">is</span> <span class="pre">True</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertFalse" title="unittest.TestCase.assertFalse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertFalse(x)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bool(x)</span> <span class="pre">is</span> <span class="pre">False</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertIs" title="unittest.TestCase.assertIs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertIs(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">is</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertIsNot" title="unittest.TestCase.assertIsNot"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertIsNot(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">is</span> <span class="pre">not</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertIsNone" title="unittest.TestCase.assertIsNone"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertIsNone(x)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">is</span> <span class="pre">None</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertIsNotNone" title="unittest.TestCase.assertIsNotNone"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertIsNotNone(x)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">is</span> <span class="pre">not</span> <span class="pre">None</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertIn" title="unittest.TestCase.assertIn"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertIn(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">in</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertNotIn" title="unittest.TestCase.assertNotIn"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotIn(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">not</span> <span class="pre">in</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertIsInstance" title="unittest.TestCase.assertIsInstance"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertIsInstance(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">isinstance(a,</span> <span class="pre">b)</span></code></p></td>
<td><p>3.2</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertNotIsInstance" title="unittest.TestCase.assertNotIsInstance"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotIsInstance(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">not</span> <span class="pre">isinstance(a,</span> <span class="pre">b)</span></code></p></td>
<td><p>3.2</p></td>
</tr>
</tbody>
</table>
<p>All the assert methods accept a <em>msg</em> argument that, if specified, is used
as the error message on failure (see also <a class="reference internal" href="#unittest.TestCase.longMessage" title="unittest.TestCase.longMessage"><code class="xref py py-data docutils literal notranslate"><span class="pre">longMessage</span></code></a>).
Note that the <em>msg</em> keyword argument can be passed to <a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises()</span></code></a>,
<a class="reference internal" href="#unittest.TestCase.assertRaisesRegex" title="unittest.TestCase.assertRaisesRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaisesRegex()</span></code></a>, <a class="reference internal" href="#unittest.TestCase.assertWarns" title="unittest.TestCase.assertWarns"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarns()</span></code></a>, <a class="reference internal" href="#unittest.TestCase.assertWarnsRegex" title="unittest.TestCase.assertWarnsRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarnsRegex()</span></code></a>
only when they are used as a context manager.</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertEqual">
<span class="sig-name descname"><span class="pre">assertEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>first</em> and <em>second</em> are equal.  If the values do not
compare equal, the test will fail.</p>
<p>In addition, if <em>first</em> and <em>second</em> are the exact same type and one of
list, tuple, dict, set, frozenset or str or any type that a subclass
registers with <a class="reference internal" href="#unittest.TestCase.addTypeEqualityFunc" title="unittest.TestCase.addTypeEqualityFunc"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addTypeEqualityFunc()</span></code></a> the type-specific equality
function will be called in order to generate a more useful default
error message (see also the <a class="reference internal" href="#type-specific-methods"><span class="std std-ref">list of type-specific methods</span></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the automatic calling of type-specific equality function.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><a class="reference internal" href="#unittest.TestCase.assertMultiLineEqual" title="unittest.TestCase.assertMultiLineEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertMultiLineEqual()</span></code></a> added as the default type equality
function for comparing strings.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertNotEqual">
<span class="sig-name descname"><span class="pre">assertNotEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertNotEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>first</em> and <em>second</em> are not equal.  If the values do
compare equal, the test will fail.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertTrue">
<span class="sig-name descname"><span class="pre">assertTrue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertTrue" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertFalse">
<span class="sig-name descname"><span class="pre">assertFalse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertFalse" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>expr</em> is true (or false).</p>
<p>Note that this is equivalent to <code class="docutils literal notranslate"><span class="pre">bool(expr)</span> <span class="pre">is</span> <span class="pre">True</span></code> and not to <code class="docutils literal notranslate"><span class="pre">expr</span>
<span class="pre">is</span> <span class="pre">True</span></code> (use <code class="docutils literal notranslate"><span class="pre">assertIs(expr,</span> <span class="pre">True)</span></code> for the latter).  This method
should also be avoided when more specific methods are available (e.g.
<code class="docutils literal notranslate"><span class="pre">assertEqual(a,</span> <span class="pre">b)</span></code> instead of <code class="docutils literal notranslate"><span class="pre">assertTrue(a</span> <span class="pre">==</span> <span class="pre">b)</span></code>), because they
provide a better error message in case of failure.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertIs">
<span class="sig-name descname"><span class="pre">assertIs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertIs" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertIsNot">
<span class="sig-name descname"><span class="pre">assertIsNot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertIsNot" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>first</em> and <em>second</em> are (or are not) the same object.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertIsNone">
<span class="sig-name descname"><span class="pre">assertIsNone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertIsNone" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertIsNotNone">
<span class="sig-name descname"><span class="pre">assertIsNotNone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertIsNotNone" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>expr</em> is (or is not) <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertIn">
<span class="sig-name descname"><span class="pre">assertIn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">container</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertIn" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertNotIn">
<span class="sig-name descname"><span class="pre">assertNotIn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">container</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertNotIn" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>member</em> is (or is not) in <em>container</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertIsInstance">
<span class="sig-name descname"><span class="pre">assertIsInstance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertIsInstance" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertNotIsInstance">
<span class="sig-name descname"><span class="pre">assertNotIsInstance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertNotIsInstance" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>obj</em> is (or is not) an instance of <em>cls</em> (which can be a
class or a tuple of classes, as supported by <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a>).
To check for the exact type, use <a class="reference internal" href="#unittest.TestCase.assertIs" title="unittest.TestCase.assertIs"><code class="xref py py-func docutils literal notranslate"><span class="pre">assertIs(type(obj),</span> <span class="pre">cls)</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<p>It is also possible to check the production of exceptions, warnings, and
log messages using the following methods:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Method</p></th>
<th class="head"><p>Checks that</p></th>
<th class="head"><p>New in</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises(exc,</span> <span class="pre">fun,</span> <span class="pre">*args,</span> <span class="pre">**kwds)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">fun(*args,</span> <span class="pre">**kwds)</span></code> raises <em>exc</em></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertRaisesRegex" title="unittest.TestCase.assertRaisesRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaisesRegex(exc,</span> <span class="pre">r,</span> <span class="pre">fun,</span> <span class="pre">*args,</span> <span class="pre">**kwds)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">fun(*args,</span> <span class="pre">**kwds)</span></code> raises <em>exc</em>
and the message matches regex <em>r</em></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertWarns" title="unittest.TestCase.assertWarns"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarns(warn,</span> <span class="pre">fun,</span> <span class="pre">*args,</span> <span class="pre">**kwds)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">fun(*args,</span> <span class="pre">**kwds)</span></code> raises <em>warn</em></p></td>
<td><p>3.2</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertWarnsRegex" title="unittest.TestCase.assertWarnsRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarnsRegex(warn,</span> <span class="pre">r,</span> <span class="pre">fun,</span> <span class="pre">*args,</span> <span class="pre">**kwds)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">fun(*args,</span> <span class="pre">**kwds)</span></code> raises <em>warn</em>
and the message matches regex <em>r</em></p></td>
<td><p>3.2</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertLogs" title="unittest.TestCase.assertLogs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertLogs(logger,</span> <span class="pre">level)</span></code></a></p></td>
<td><p>The <code class="docutils literal notranslate"><span class="pre">with</span></code> block logs on <em>logger</em>
with minimum <em>level</em></p></td>
<td><p>3.4</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertNoLogs" title="unittest.TestCase.assertNoLogs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNoLogs(logger,</span> <span class="pre">level)</span></code></a></p></td>
<td><dl class="simple">
<dt>The <code class="docutils literal notranslate"><span class="pre">with</span></code> block does not log on</dt><dd><p><em>logger</em> with minimum <em>level</em></p>
</dd>
</dl>
</td>
<td><p>3.10</p></td>
</tr>
</tbody>
</table>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertRaises">
<span class="sig-name descname"><span class="pre">assertRaises</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertRaises" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">assertRaises</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Test that an exception is raised when <em>callable</em> is called with any
positional or keyword arguments that are also passed to
<a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises()</span></code></a>.  The test passes if <em>exception</em> is raised, is an
error if another exception is raised, or fails if no exception is raised.
To catch any of a group of exceptions, a tuple containing the exception
classes may be passed as <em>exception</em>.</p>
<p>If only the <em>exception</em> and possibly the <em>msg</em> arguments are given,
return a context manager so that the code under test can be written
inline rather than as a function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertRaises</span><span class="p">(</span><span class="n">SomeException</span><span class="p">):</span>
    <span class="n">do_something</span><span class="p">()</span>
</pre></div>
</div>
<p>When used as a context manager, <a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises()</span></code></a> accepts the
additional keyword argument <em>msg</em>.</p>
<p>The context manager will store the caught exception object in its
<code class="xref py py-attr docutils literal notranslate"><span class="pre">exception</span></code> attribute.  This can be useful if the intention
is to perform additional checks on the exception raised:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertRaises</span><span class="p">(</span><span class="n">SomeException</span><span class="p">)</span> <span class="k">as</span> <span class="n">cm</span><span class="p">:</span>
    <span class="n">do_something</span><span class="p">()</span>

<span class="n">the_exception</span> <span class="o">=</span> <span class="n">cm</span><span class="o">.</span><span class="n">exception</span>
<span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">the_exception</span><span class="o">.</span><span class="n">error_code</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the ability to use <a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises()</span></code></a> as a context manager.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <code class="xref py py-attr docutils literal notranslate"><span class="pre">exception</span></code> attribute.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>msg</em> keyword argument when used as a context manager.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertRaisesRegex">
<span class="sig-name descname"><span class="pre">assertRaisesRegex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">regex</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertRaisesRegex" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">assertRaisesRegex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">regex</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Like <a class="reference internal" href="#unittest.TestCase.assertRaises" title="unittest.TestCase.assertRaises"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaises()</span></code></a> but also tests that <em>regex</em> matches
on the string representation of the raised exception.  <em>regex</em> may be
a regular expression object or a string containing a regular expression
suitable for use by <a class="reference internal" href="re.html#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.search()</span></code></a>.  Examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="bp">self</span><span class="o">.</span><span class="n">assertRaisesRegex</span><span class="p">(</span><span class="ne">ValueError</span><span class="p">,</span> <span class="s2">&quot;invalid literal for.*XYZ&#39;$&quot;</span><span class="p">,</span>
                       <span class="nb">int</span><span class="p">,</span> <span class="s1">&#39;XYZ&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>or:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertRaisesRegex</span><span class="p">(</span><span class="ne">ValueError</span><span class="p">,</span> <span class="s1">&#39;literal&#39;</span><span class="p">):</span>
   <span class="nb">int</span><span class="p">(</span><span class="s1">&#39;XYZ&#39;</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>Added under the name <code class="docutils literal notranslate"><span class="pre">assertRaisesRegexp</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Renamed to <a class="reference internal" href="#unittest.TestCase.assertRaisesRegex" title="unittest.TestCase.assertRaisesRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRaisesRegex()</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>msg</em> keyword argument when used as a context manager.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertWarns">
<span class="sig-name descname"><span class="pre">assertWarns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">warning</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertWarns" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">assertWarns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">warning</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Test that a warning is triggered when <em>callable</em> is called with any
positional or keyword arguments that are also passed to
<a class="reference internal" href="#unittest.TestCase.assertWarns" title="unittest.TestCase.assertWarns"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarns()</span></code></a>.  The test passes if <em>warning</em> is triggered and
fails if it isn’t.  Any exception is an error.
To catch any of a group of warnings, a tuple containing the warning
classes may be passed as <em>warnings</em>.</p>
<p>If only the <em>warning</em> and possibly the <em>msg</em> arguments are given,
return a context manager so that the code under test can be written
inline rather than as a function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertWarns</span><span class="p">(</span><span class="n">SomeWarning</span><span class="p">):</span>
    <span class="n">do_something</span><span class="p">()</span>
</pre></div>
</div>
<p>When used as a context manager, <a class="reference internal" href="#unittest.TestCase.assertWarns" title="unittest.TestCase.assertWarns"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarns()</span></code></a> accepts the
additional keyword argument <em>msg</em>.</p>
<p>The context manager will store the caught warning object in its
<code class="xref py py-attr docutils literal notranslate"><span class="pre">warning</span></code> attribute, and the source line which triggered the
warnings in the <code class="xref py py-attr docutils literal notranslate"><span class="pre">filename</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">lineno</span></code> attributes.
This can be useful if the intention is to perform additional checks
on the warning caught:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertWarns</span><span class="p">(</span><span class="n">SomeWarning</span><span class="p">)</span> <span class="k">as</span> <span class="n">cm</span><span class="p">:</span>
    <span class="n">do_something</span><span class="p">()</span>

<span class="bp">self</span><span class="o">.</span><span class="n">assertIn</span><span class="p">(</span><span class="s1">&#39;myfile.py&#39;</span><span class="p">,</span> <span class="n">cm</span><span class="o">.</span><span class="n">filename</span><span class="p">)</span>
<span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="mi">320</span><span class="p">,</span> <span class="n">cm</span><span class="o">.</span><span class="n">lineno</span><span class="p">)</span>
</pre></div>
</div>
<p>This method works regardless of the warning filters in place when it
is called.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>msg</em> keyword argument when used as a context manager.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertWarnsRegex">
<span class="sig-name descname"><span class="pre">assertWarnsRegex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">warning</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">regex</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertWarnsRegex" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">assertWarnsRegex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">warning</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">regex</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Like <a class="reference internal" href="#unittest.TestCase.assertWarns" title="unittest.TestCase.assertWarns"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertWarns()</span></code></a> but also tests that <em>regex</em> matches on the
message of the triggered warning.  <em>regex</em> may be a regular expression
object or a string containing a regular expression suitable for use
by <a class="reference internal" href="re.html#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.search()</span></code></a>.  Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="bp">self</span><span class="o">.</span><span class="n">assertWarnsRegex</span><span class="p">(</span><span class="ne">DeprecationWarning</span><span class="p">,</span>
                      <span class="sa">r</span><span class="s1">&#39;legacy_function\(\) is deprecated&#39;</span><span class="p">,</span>
                      <span class="n">legacy_function</span><span class="p">,</span> <span class="s1">&#39;XYZ&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>or:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertWarnsRegex</span><span class="p">(</span><span class="ne">RuntimeWarning</span><span class="p">,</span> <span class="s1">&#39;unsafe frobnicating&#39;</span><span class="p">):</span>
    <span class="n">frobnicate</span><span class="p">(</span><span class="s1">&#39;/etc/passwd&#39;</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>msg</em> keyword argument when used as a context manager.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertLogs">
<span class="sig-name descname"><span class="pre">assertLogs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">logger</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertLogs" title="Link to this definition">¶</a></dt>
<dd><p>A context manager to test that at least one message is logged on
the <em>logger</em> or one of its children, with at least the given
<em>level</em>.</p>
<p>If given, <em>logger</em> should be a <a class="reference internal" href="logging.html#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">logging.Logger</span></code></a> object or a
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> giving the name of a logger.  The default is the root
logger, which will catch all messages that were not blocked by a
non-propagating descendent logger.</p>
<p>If given, <em>level</em> should be either a numeric logging level or
its string equivalent (for example either <code class="docutils literal notranslate"><span class="pre">&quot;ERROR&quot;</span></code> or
<a class="reference internal" href="logging.html#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">logging.ERROR</span></code></a>).  The default is <a class="reference internal" href="logging.html#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">logging.INFO</span></code></a>.</p>
<p>The test passes if at least one message emitted inside the <code class="docutils literal notranslate"><span class="pre">with</span></code>
block matches the <em>logger</em> and <em>level</em> conditions, otherwise it fails.</p>
<p>The object returned by the context manager is a recording helper
which keeps tracks of the matching log messages.  It has two
attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestCase.records">
<span class="sig-name descname"><span class="pre">records</span></span><a class="headerlink" href="#unittest.TestCase.records" title="Link to this definition">¶</a></dt>
<dd><p>A list of <a class="reference internal" href="logging.html#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">logging.LogRecord</span></code></a> objects of the matching
log messages.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestCase.output">
<span class="sig-name descname"><span class="pre">output</span></span><a class="headerlink" href="#unittest.TestCase.output" title="Link to this definition">¶</a></dt>
<dd><p>A list of <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> objects with the formatted output of
matching messages.</p>
</dd></dl>

<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertLogs</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">,</span> <span class="n">level</span><span class="o">=</span><span class="s1">&#39;INFO&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">cm</span><span class="p">:</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s1">&#39;first message&#39;</span><span class="p">)</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;foo.bar&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s1">&#39;second message&#39;</span><span class="p">)</span>
<span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">cm</span><span class="o">.</span><span class="n">output</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;INFO:foo:first message&#39;</span><span class="p">,</span>
                             <span class="s1">&#39;ERROR:foo.bar:second message&#39;</span><span class="p">])</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertNoLogs">
<span class="sig-name descname"><span class="pre">assertNoLogs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">logger</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertNoLogs" title="Link to this definition">¶</a></dt>
<dd><p>A context manager to test that no messages are logged on
the <em>logger</em> or one of its children, with at least the given
<em>level</em>.</p>
<p>If given, <em>logger</em> should be a <a class="reference internal" href="logging.html#logging.Logger" title="logging.Logger"><code class="xref py py-class docutils literal notranslate"><span class="pre">logging.Logger</span></code></a> object or a
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> giving the name of a logger.  The default is the root
logger, which will catch all messages.</p>
<p>If given, <em>level</em> should be either a numeric logging level or
its string equivalent (for example either <code class="docutils literal notranslate"><span class="pre">&quot;ERROR&quot;</span></code> or
<a class="reference internal" href="logging.html#logging.ERROR" title="logging.ERROR"><code class="xref py py-const docutils literal notranslate"><span class="pre">logging.ERROR</span></code></a>).  The default is <a class="reference internal" href="logging.html#logging.INFO" title="logging.INFO"><code class="xref py py-const docutils literal notranslate"><span class="pre">logging.INFO</span></code></a>.</p>
<p>Unlike <a class="reference internal" href="#unittest.TestCase.assertLogs" title="unittest.TestCase.assertLogs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertLogs()</span></code></a>, nothing will be returned by the context
manager.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<p>There are also other methods used to perform more specific checks, such as:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Method</p></th>
<th class="head"><p>Checks that</p></th>
<th class="head"><p>New in</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertAlmostEqual" title="unittest.TestCase.assertAlmostEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertAlmostEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">round(a-b,</span> <span class="pre">7)</span> <span class="pre">==</span> <span class="pre">0</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertNotAlmostEqual" title="unittest.TestCase.assertNotAlmostEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotAlmostEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">round(a-b,</span> <span class="pre">7)</span> <span class="pre">!=</span> <span class="pre">0</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertGreater" title="unittest.TestCase.assertGreater"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertGreater(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">&gt;</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertGreaterEqual" title="unittest.TestCase.assertGreaterEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertGreaterEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">&gt;=</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertLess" title="unittest.TestCase.assertLess"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertLess(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">&lt;</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertLessEqual" title="unittest.TestCase.assertLessEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertLessEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">&lt;=</span> <span class="pre">b</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertRegex" title="unittest.TestCase.assertRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRegex(s,</span> <span class="pre">r)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">r.search(s)</span></code></p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertNotRegex" title="unittest.TestCase.assertNotRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotRegex(s,</span> <span class="pre">r)</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">not</span> <span class="pre">r.search(s)</span></code></p></td>
<td><p>3.2</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertCountEqual" title="unittest.TestCase.assertCountEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertCountEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p><em>a</em> and <em>b</em> have the same
elements in the same number,
regardless of their order.</p></td>
<td><p>3.2</p></td>
</tr>
</tbody>
</table>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertAlmostEqual">
<span class="sig-name descname"><span class="pre">assertAlmostEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">places</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">7</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delta</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertAlmostEqual" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertNotAlmostEqual">
<span class="sig-name descname"><span class="pre">assertNotAlmostEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">places</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">7</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delta</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertNotAlmostEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>first</em> and <em>second</em> are approximately (or not approximately)
equal by computing the difference, rounding to the given number of
decimal <em>places</em> (default 7), and comparing to zero.  Note that these
methods round the values to the given number of <em>decimal places</em> (i.e.
like the <a class="reference internal" href="functions.html#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a> function) and not <em>significant digits</em>.</p>
<p>If <em>delta</em> is supplied instead of <em>places</em> then the difference
between <em>first</em> and <em>second</em> must be less or equal to (or greater than) <em>delta</em>.</p>
<p>Supplying both <em>delta</em> and <em>places</em> raises a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><a class="reference internal" href="#unittest.TestCase.assertAlmostEqual" title="unittest.TestCase.assertAlmostEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertAlmostEqual()</span></code></a> automatically considers almost equal objects
that compare equal.  <a class="reference internal" href="#unittest.TestCase.assertNotAlmostEqual" title="unittest.TestCase.assertNotAlmostEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotAlmostEqual()</span></code></a> automatically fails
if the objects compare equal.  Added the <em>delta</em> keyword argument.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertGreater">
<span class="sig-name descname"><span class="pre">assertGreater</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertGreater" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertGreaterEqual">
<span class="sig-name descname"><span class="pre">assertGreaterEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertGreaterEqual" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertLess">
<span class="sig-name descname"><span class="pre">assertLess</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertLess" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertLessEqual">
<span class="sig-name descname"><span class="pre">assertLessEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertLessEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that <em>first</em> is respectively &gt;, &gt;=, &lt; or &lt;= than <em>second</em> depending
on the method name.  If not, the test will fail:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="bp">self</span><span class="o">.</span><span class="n">assertGreaterEqual</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">)</span>
<span class="go">AssertionError: &quot;3&quot; unexpectedly not greater than or equal to &quot;4&quot;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertRegex">
<span class="sig-name descname"><span class="pre">assertRegex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">regex</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertRegex" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertNotRegex">
<span class="sig-name descname"><span class="pre">assertNotRegex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">regex</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertNotRegex" title="Link to this definition">¶</a></dt>
<dd><p>Test that a <em>regex</em> search matches (or does not match) <em>text</em>.  In case
of failure, the error message will include the pattern and the <em>text</em> (or
the pattern and the part of <em>text</em> that unexpectedly matched).  <em>regex</em>
may be a regular expression object or a string containing a regular
expression suitable for use by <a class="reference internal" href="re.html#re.search" title="re.search"><code class="xref py py-func docutils literal notranslate"><span class="pre">re.search()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>Added under the name <code class="docutils literal notranslate"><span class="pre">assertRegexpMatches</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The method <code class="docutils literal notranslate"><span class="pre">assertRegexpMatches()</span></code> has been renamed to
<a class="reference internal" href="#unittest.TestCase.assertRegex" title="unittest.TestCase.assertRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertRegex()</span></code></a>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span><a class="reference internal" href="#unittest.TestCase.assertNotRegex" title="unittest.TestCase.assertNotRegex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertNotRegex()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertCountEqual">
<span class="sig-name descname"><span class="pre">assertCountEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertCountEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that sequence <em>first</em> contains the same elements as <em>second</em>,
regardless of their order. When they don’t, an error message listing the
differences between the sequences will be generated.</p>
<p>Duplicate elements are <em>not</em> ignored when comparing <em>first</em> and
<em>second</em>. It verifies whether each element has the same count in both
sequences. Equivalent to:
<code class="docutils literal notranslate"><span class="pre">assertEqual(Counter(list(first)),</span> <span class="pre">Counter(list(second)))</span></code>
but works with sequences of unhashable objects as well.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<p id="type-specific-methods">The <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a> method dispatches the equality check for objects of
the same type to different type-specific methods.  These methods are already
implemented for most of the built-in types, but it’s also possible to
register new methods using <a class="reference internal" href="#unittest.TestCase.addTypeEqualityFunc" title="unittest.TestCase.addTypeEqualityFunc"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addTypeEqualityFunc()</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.addTypeEqualityFunc">
<span class="sig-name descname"><span class="pre">addTypeEqualityFunc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">typeobj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.addTypeEqualityFunc" title="Link to this definition">¶</a></dt>
<dd><p>Registers a type-specific method called by <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a> to check
if two objects of exactly the same <em>typeobj</em> (not subclasses) compare
equal.  <em>function</em> must take two positional arguments and a third msg=None
keyword argument just as <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a> does.  It must raise
<a class="reference internal" href="#unittest.TestCase.failureException" title="unittest.TestCase.failureException"><code class="xref py py-data docutils literal notranslate"><span class="pre">self.failureException(msg)</span></code></a> when inequality
between the first two parameters is detected – possibly providing useful
information and explaining the inequalities in details in the error
message.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<p>The list of type-specific methods automatically used by
<a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a> are summarized in the following table.  Note
that it’s usually not necessary to invoke these methods directly.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Method</p></th>
<th class="head"><p>Used to compare</p></th>
<th class="head"><p>New in</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertMultiLineEqual" title="unittest.TestCase.assertMultiLineEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertMultiLineEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p>strings</p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertSequenceEqual" title="unittest.TestCase.assertSequenceEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertSequenceEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p>sequences</p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertListEqual" title="unittest.TestCase.assertListEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertListEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p>lists</p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertTupleEqual" title="unittest.TestCase.assertTupleEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertTupleEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p>tuples</p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#unittest.TestCase.assertSetEqual" title="unittest.TestCase.assertSetEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertSetEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p>sets or frozensets</p></td>
<td><p>3.1</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#unittest.TestCase.assertDictEqual" title="unittest.TestCase.assertDictEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertDictEqual(a,</span> <span class="pre">b)</span></code></a></p></td>
<td><p>dicts</p></td>
<td><p>3.1</p></td>
</tr>
</tbody>
</table>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertMultiLineEqual">
<span class="sig-name descname"><span class="pre">assertMultiLineEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertMultiLineEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that the multiline string <em>first</em> is equal to the string <em>second</em>.
When not equal a diff of the two strings highlighting the differences
will be included in the error message. This method is used by default
when comparing strings with <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertSequenceEqual">
<span class="sig-name descname"><span class="pre">assertSequenceEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">seq_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertSequenceEqual" title="Link to this definition">¶</a></dt>
<dd><p>Tests that two sequences are equal.  If a <em>seq_type</em> is supplied, both
<em>first</em> and <em>second</em> must be instances of <em>seq_type</em> or a failure will
be raised.  If the sequences are different an error message is
constructed that shows the difference between the two.</p>
<p>This method is not called directly by <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a>, but
it’s used to implement <a class="reference internal" href="#unittest.TestCase.assertListEqual" title="unittest.TestCase.assertListEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertListEqual()</span></code></a> and
<a class="reference internal" href="#unittest.TestCase.assertTupleEqual" title="unittest.TestCase.assertTupleEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertTupleEqual()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertListEqual">
<span class="sig-name descname"><span class="pre">assertListEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertListEqual" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="unittest.TestCase.assertTupleEqual">
<span class="sig-name descname"><span class="pre">assertTupleEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertTupleEqual" title="Link to this definition">¶</a></dt>
<dd><p>Tests that two lists or tuples are equal.  If not, an error message is
constructed that shows only the differences between the two.  An error
is also raised if either of the parameters are of the wrong type.
These methods are used by default when comparing lists or tuples with
<a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertSetEqual">
<span class="sig-name descname"><span class="pre">assertSetEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertSetEqual" title="Link to this definition">¶</a></dt>
<dd><p>Tests that two sets are equal.  If not, an error message is constructed
that lists the differences between the sets.  This method is used by
default when comparing sets or frozensets with <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a>.</p>
<p>Fails if either of <em>first</em> or <em>second</em> does not have a <code class="xref py py-meth docutils literal notranslate"><span class="pre">set.difference()</span></code>
method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.assertDictEqual">
<span class="sig-name descname"><span class="pre">assertDictEqual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.assertDictEqual" title="Link to this definition">¶</a></dt>
<dd><p>Test that two dictionaries are equal.  If not, an error message is
constructed that shows the differences in the dictionaries. This
method will be used by default to compare dictionaries in
calls to <a class="reference internal" href="#unittest.TestCase.assertEqual" title="unittest.TestCase.assertEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertEqual()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<p id="other-methods-and-attrs">Finally the <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> provides the following methods and attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.fail">
<span class="sig-name descname"><span class="pre">fail</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.fail" title="Link to this definition">¶</a></dt>
<dd><p>Signals a test failure unconditionally, with <em>msg</em> or <code class="docutils literal notranslate"><span class="pre">None</span></code> for
the error message.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestCase.failureException">
<span class="sig-name descname"><span class="pre">failureException</span></span><a class="headerlink" href="#unittest.TestCase.failureException" title="Link to this definition">¶</a></dt>
<dd><p>This class attribute gives the exception raised by the test method.  If a
test framework needs to use a specialized exception, possibly to carry
additional information, it must subclass this exception in order to “play
fair” with the framework.  The initial value of this attribute is
<a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestCase.longMessage">
<span class="sig-name descname"><span class="pre">longMessage</span></span><a class="headerlink" href="#unittest.TestCase.longMessage" title="Link to this definition">¶</a></dt>
<dd><p>This class attribute determines what happens when a custom failure message
is passed as the msg argument to an assertXYY call that fails.
<code class="docutils literal notranslate"><span class="pre">True</span></code> is the default value. In this case, the custom message is appended
to the end of the standard failure message.
When set to <code class="docutils literal notranslate"><span class="pre">False</span></code>, the custom message replaces the standard message.</p>
<p>The class setting can be overridden in individual test methods by assigning
an instance attribute, self.longMessage, to <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> before
calling the assert methods.</p>
<p>The class setting gets reset before each test call.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestCase.maxDiff">
<span class="sig-name descname"><span class="pre">maxDiff</span></span><a class="headerlink" href="#unittest.TestCase.maxDiff" title="Link to this definition">¶</a></dt>
<dd><p>This attribute controls the maximum length of diffs output by assert
methods that report diffs on failure. It defaults to 80*8 characters.
Assert methods affected by this attribute are
<a class="reference internal" href="#unittest.TestCase.assertSequenceEqual" title="unittest.TestCase.assertSequenceEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertSequenceEqual()</span></code></a> (including all the sequence comparison
methods that delegate to it), <a class="reference internal" href="#unittest.TestCase.assertDictEqual" title="unittest.TestCase.assertDictEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertDictEqual()</span></code></a> and
<a class="reference internal" href="#unittest.TestCase.assertMultiLineEqual" title="unittest.TestCase.assertMultiLineEqual"><code class="xref py py-meth docutils literal notranslate"><span class="pre">assertMultiLineEqual()</span></code></a>.</p>
<p>Setting <code class="docutils literal notranslate"><span class="pre">maxDiff</span></code> to <code class="docutils literal notranslate"><span class="pre">None</span></code> means that there is no maximum length of
diffs.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<p>Testing frameworks can use the following methods to collect information on
the test:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.countTestCases">
<span class="sig-name descname"><span class="pre">countTestCases</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.countTestCases" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of tests represented by this test object.  For
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances, this will always be <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.defaultTestResult">
<span class="sig-name descname"><span class="pre">defaultTestResult</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.defaultTestResult" title="Link to this definition">¶</a></dt>
<dd><p>Return an instance of the test result class that should be used for this
test case class (if no other result instance is provided to the
<a class="reference internal" href="#unittest.TestCase.run" title="unittest.TestCase.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run()</span></code></a> method).</p>
<p>For <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances, this will always be an instance of
<a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a>; subclasses of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> should override this
as necessary.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.id">
<span class="sig-name descname"><span class="pre">id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.id" title="Link to this definition">¶</a></dt>
<dd><p>Return a string identifying the specific test case.  This is usually the
full name of the test method, including the module and class name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.shortDescription">
<span class="sig-name descname"><span class="pre">shortDescription</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.shortDescription" title="Link to this definition">¶</a></dt>
<dd><p>Returns a description of the test, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no description
has been provided.  The default implementation of this method
returns the first line of the test method’s docstring, if available,
or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>In 3.1 this was changed to add the test name to the short description
even in the presence of a docstring.  This caused compatibility issues
with unittest extensions and adding the test name was moved to the
<a class="reference internal" href="#unittest.TextTestResult" title="unittest.TextTestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextTestResult</span></code></a> in Python 3.2.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.addCleanup">
<span class="sig-name descname"><span class="pre">addCleanup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.addCleanup" title="Link to this definition">¶</a></dt>
<dd><p>Add a function to be called after <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> to cleanup resources
used during the test. Functions will be called in reverse order to the
order they are added (<abbr title="last-in, first-out">LIFO</abbr>).  They
are called with any arguments and keyword arguments passed into
<a class="reference internal" href="#unittest.TestCase.addCleanup" title="unittest.TestCase.addCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addCleanup()</span></code></a> when they are added.</p>
<p>If <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> fails, meaning that <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> is not called,
then any cleanup functions added will still be called.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.enterContext">
<span class="sig-name descname"><span class="pre">enterContext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cm</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.enterContext" title="Link to this definition">¶</a></dt>
<dd><p>Enter the supplied <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a>.  If successful, also
add its <a class="reference internal" href="../reference/datamodel.html#object.__exit__" title="object.__exit__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__exit__()</span></code></a> method as a cleanup function by
<a class="reference internal" href="#unittest.TestCase.addCleanup" title="unittest.TestCase.addCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addCleanup()</span></code></a> and return the result of the
<a class="reference internal" href="../reference/datamodel.html#object.__enter__" title="object.__enter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__enter__()</span></code></a> method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.doCleanups">
<span class="sig-name descname"><span class="pre">doCleanups</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.doCleanups" title="Link to this definition">¶</a></dt>
<dd><p>This method is called unconditionally after <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a>, or
after <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> if <a class="reference internal" href="#unittest.TestCase.setUp" title="unittest.TestCase.setUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code></a> raises an exception.</p>
<p>It is responsible for calling all the cleanup functions added by
<a class="reference internal" href="#unittest.TestCase.addCleanup" title="unittest.TestCase.addCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addCleanup()</span></code></a>. If you need cleanup functions to be called
<em>prior</em> to <a class="reference internal" href="#unittest.TestCase.tearDown" title="unittest.TestCase.tearDown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code></a> then you can call <a class="reference internal" href="#unittest.TestCase.doCleanups" title="unittest.TestCase.doCleanups"><code class="xref py py-meth docutils literal notranslate"><span class="pre">doCleanups()</span></code></a>
yourself.</p>
<p><a class="reference internal" href="#unittest.TestCase.doCleanups" title="unittest.TestCase.doCleanups"><code class="xref py py-meth docutils literal notranslate"><span class="pre">doCleanups()</span></code></a> pops methods off the stack of cleanup
functions one at a time, so it can be called at any time.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.addClassCleanup">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">addClassCleanup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.addClassCleanup" title="Link to this definition">¶</a></dt>
<dd><p>Add a function to be called after <a class="reference internal" href="#unittest.TestCase.tearDownClass" title="unittest.TestCase.tearDownClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDownClass()</span></code></a> to cleanup
resources used during the test class. Functions will be called in reverse
order to the order they are added (<abbr title="last-in, first-out">LIFO</abbr>).
They are called with any arguments and keyword arguments passed into
<a class="reference internal" href="#unittest.TestCase.addClassCleanup" title="unittest.TestCase.addClassCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addClassCleanup()</span></code></a> when they are added.</p>
<p>If <a class="reference internal" href="#unittest.TestCase.setUpClass" title="unittest.TestCase.setUpClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUpClass()</span></code></a> fails, meaning that <a class="reference internal" href="#unittest.TestCase.tearDownClass" title="unittest.TestCase.tearDownClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDownClass()</span></code></a> is not
called, then any cleanup functions added will still be called.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.enterClassContext">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">enterClassContext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cm</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.enterClassContext" title="Link to this definition">¶</a></dt>
<dd><p>Enter the supplied <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a>.  If successful, also
add its <a class="reference internal" href="../reference/datamodel.html#object.__exit__" title="object.__exit__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__exit__()</span></code></a> method as a cleanup function by
<a class="reference internal" href="#unittest.TestCase.addClassCleanup" title="unittest.TestCase.addClassCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addClassCleanup()</span></code></a> and return the result of the
<a class="reference internal" href="../reference/datamodel.html#object.__enter__" title="object.__enter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__enter__()</span></code></a> method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestCase.doClassCleanups">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">doClassCleanups</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestCase.doClassCleanups" title="Link to this definition">¶</a></dt>
<dd><p>This method is called unconditionally after <a class="reference internal" href="#unittest.TestCase.tearDownClass" title="unittest.TestCase.tearDownClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDownClass()</span></code></a>, or
after <a class="reference internal" href="#unittest.TestCase.setUpClass" title="unittest.TestCase.setUpClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUpClass()</span></code></a> if <a class="reference internal" href="#unittest.TestCase.setUpClass" title="unittest.TestCase.setUpClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setUpClass()</span></code></a> raises an exception.</p>
<p>It is responsible for calling all the cleanup functions added by
<a class="reference internal" href="#unittest.TestCase.addClassCleanup" title="unittest.TestCase.addClassCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addClassCleanup()</span></code></a>. If you need cleanup functions to be called
<em>prior</em> to <a class="reference internal" href="#unittest.TestCase.tearDownClass" title="unittest.TestCase.tearDownClass"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDownClass()</span></code></a> then you can call
<a class="reference internal" href="#unittest.TestCase.doClassCleanups" title="unittest.TestCase.doClassCleanups"><code class="xref py py-meth docutils literal notranslate"><span class="pre">doClassCleanups()</span></code></a> yourself.</p>
<p><a class="reference internal" href="#unittest.TestCase.doClassCleanups" title="unittest.TestCase.doClassCleanups"><code class="xref py py-meth docutils literal notranslate"><span class="pre">doClassCleanups()</span></code></a> pops methods off the stack of cleanup
functions one at a time, so it can be called at any time.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="unittest.IsolatedAsyncioTestCase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">IsolatedAsyncioTestCase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.IsolatedAsyncioTestCase" title="Link to this definition">¶</a></dt>
<dd><p>This class provides an API similar to <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> and also accepts
coroutines as test functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.IsolatedAsyncioTestCase.asyncSetUp">
<em class="property"><span class="pre">coroutine</span> </em><span class="sig-name descname"><span class="pre">asyncSetUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.IsolatedAsyncioTestCase.asyncSetUp" title="Link to this definition">¶</a></dt>
<dd><p>Method called to prepare the test fixture. This is called after <code class="xref py py-meth docutils literal notranslate"><span class="pre">setUp()</span></code>.
This is called immediately before calling the test method; other than
<a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> or <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a>, any exception raised by this method
will be considered an error rather than a test failure. The default implementation
does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.IsolatedAsyncioTestCase.asyncTearDown">
<em class="property"><span class="pre">coroutine</span> </em><span class="sig-name descname"><span class="pre">asyncTearDown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.IsolatedAsyncioTestCase.asyncTearDown" title="Link to this definition">¶</a></dt>
<dd><p>Method called immediately after the test method has been called and the
result recorded.  This is called before <code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDown()</span></code>. This is called even if
the test method raised an exception, so the implementation in subclasses may need
to be particularly careful about checking internal state.  Any exception, other than
<a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> or <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a>, raised by this method will be
considered an additional error rather than a test failure (thus increasing
the total number of reported errors). This method will only be called if
the <a class="reference internal" href="#unittest.IsolatedAsyncioTestCase.asyncSetUp" title="unittest.IsolatedAsyncioTestCase.asyncSetUp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">asyncSetUp()</span></code></a> succeeds, regardless of the outcome of the test method.
The default implementation does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.IsolatedAsyncioTestCase.addAsyncCleanup">
<span class="sig-name descname"><span class="pre">addAsyncCleanup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.IsolatedAsyncioTestCase.addAsyncCleanup" title="Link to this definition">¶</a></dt>
<dd><p>This method accepts a coroutine that can be used as a cleanup function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.IsolatedAsyncioTestCase.enterAsyncContext">
<em class="property"><span class="pre">coroutine</span> </em><span class="sig-name descname"><span class="pre">enterAsyncContext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cm</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.IsolatedAsyncioTestCase.enterAsyncContext" title="Link to this definition">¶</a></dt>
<dd><p>Enter the supplied <a class="reference internal" href="../glossary.html#term-asynchronous-context-manager"><span class="xref std std-term">asynchronous context manager</span></a>.  If successful,
also add its <a class="reference internal" href="../reference/datamodel.html#object.__aexit__" title="object.__aexit__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__aexit__()</span></code></a> method as a cleanup function by
<a class="reference internal" href="#unittest.IsolatedAsyncioTestCase.addAsyncCleanup" title="unittest.IsolatedAsyncioTestCase.addAsyncCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addAsyncCleanup()</span></code></a> and return the result of the
<a class="reference internal" href="../reference/datamodel.html#object.__aenter__" title="object.__aenter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__aenter__()</span></code></a> method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.IsolatedAsyncioTestCase.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.IsolatedAsyncioTestCase.run" title="Link to this definition">¶</a></dt>
<dd><p>Sets up a new event loop to run the test, collecting the result into
the <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> object passed as <em>result</em>.  If <em>result</em> is
omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, a temporary result object is created (by calling
the <code class="xref py py-meth docutils literal notranslate"><span class="pre">defaultTestResult()</span></code> method) and used. The result object is
returned to <a class="reference internal" href="#unittest.IsolatedAsyncioTestCase.run" title="unittest.IsolatedAsyncioTestCase.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run()</span></code></a>’s caller. At the end of the test all the tasks
in the event loop are cancelled.</p>
</dd></dl>

<p>An example illustrating the order:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">unittest</span> <span class="kn">import</span> <span class="n">IsolatedAsyncioTestCase</span>

<span class="n">events</span> <span class="o">=</span> <span class="p">[]</span>


<span class="k">class</span> <span class="nc">Test</span><span class="p">(</span><span class="n">IsolatedAsyncioTestCase</span><span class="p">):</span>


    <span class="k">def</span> <span class="nf">setUp</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">events</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;setUp&quot;</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">asyncSetUp</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_async_connection</span> <span class="o">=</span> <span class="k">await</span> <span class="n">AsyncConnection</span><span class="p">()</span>
        <span class="n">events</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;asyncSetUp&quot;</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">test_response</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">events</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;test_response&quot;</span><span class="p">)</span>
        <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_async_connection</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;https://example.com&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span> <span class="mi">200</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">addAsyncCleanup</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">on_cleanup</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">tearDown</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">events</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;tearDown&quot;</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">asyncTearDown</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_async_connection</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
        <span class="n">events</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;asyncTearDown&quot;</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">on_cleanup</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">events</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;cleanup&quot;</span><span class="p">)</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">unittest</span><span class="o">.</span><span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
<p>After running the test, <code class="docutils literal notranslate"><span class="pre">events</span></code> would contain <code class="docutils literal notranslate"><span class="pre">[&quot;setUp&quot;,</span> <span class="pre">&quot;asyncSetUp&quot;,</span> <span class="pre">&quot;test_response&quot;,</span> <span class="pre">&quot;asyncTearDown&quot;,</span> <span class="pre">&quot;tearDown&quot;,</span> <span class="pre">&quot;cleanup&quot;]</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="unittest.FunctionTestCase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">FunctionTestCase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">testFunc</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">setUp</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tearDown</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.FunctionTestCase" title="Link to this definition">¶</a></dt>
<dd><p>This class implements the portion of the <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> interface which
allows the test runner to drive the test, but does not provide the methods
which test code can use to check and report errors.  This is used to create
test cases using legacy test code, allowing it to be integrated into a
<a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>-based test framework.</p>
</dd></dl>

</section>
<section id="grouping-tests">
<span id="testsuite-objects"></span><h3>Grouping tests<a class="headerlink" href="#grouping-tests" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="unittest.TestSuite">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">TestSuite</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tests</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite" title="Link to this definition">¶</a></dt>
<dd><p>This class represents an aggregation of individual test cases and test suites.
The class presents the interface needed by the test runner to allow it to be run
as any other test case.  Running a <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> instance is the same as
iterating over the suite, running each test individually.</p>
<p>If <em>tests</em> is given, it must be an iterable of individual test cases or other
test suites that will be used to build the suite initially. Additional methods
are provided to add test cases and suites to the collection later on.</p>
<p><a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> objects behave much like <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> objects, except
they do not actually implement a test.  Instead, they are used to aggregate
tests into groups of tests that should be run together. Some additional
methods are available to add tests to <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> instances:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestSuite.addTest">
<span class="sig-name descname"><span class="pre">addTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite.addTest" title="Link to this definition">¶</a></dt>
<dd><p>Add a <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> or <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> to the suite.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestSuite.addTests">
<span class="sig-name descname"><span class="pre">addTests</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tests</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite.addTests" title="Link to this definition">¶</a></dt>
<dd><p>Add all the tests from an iterable of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> and <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a>
instances to this test suite.</p>
<p>This is equivalent to iterating over <em>tests</em>, calling <a class="reference internal" href="#unittest.TestSuite.addTest" title="unittest.TestSuite.addTest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addTest()</span></code></a> for
each element.</p>
</dd></dl>

<p><a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> shares the following methods with <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestSuite.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite.run" title="Link to this definition">¶</a></dt>
<dd><p>Run the tests associated with this suite, collecting the result into the
test result object passed as <em>result</em>.  Note that unlike
<a class="reference internal" href="#unittest.TestCase.run" title="unittest.TestCase.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestCase.run()</span></code></a>, <a class="reference internal" href="#unittest.TestSuite.run" title="unittest.TestSuite.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestSuite.run()</span></code></a> requires the result object to
be passed in.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestSuite.debug">
<span class="sig-name descname"><span class="pre">debug</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite.debug" title="Link to this definition">¶</a></dt>
<dd><p>Run the tests associated with this suite without collecting the
result. This allows exceptions raised by the test to be propagated to the
caller and can be used to support running tests under a debugger.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestSuite.countTestCases">
<span class="sig-name descname"><span class="pre">countTestCases</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite.countTestCases" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of tests represented by this test object, including all
individual tests and sub-suites.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestSuite.__iter__">
<span class="sig-name descname"><span class="pre">__iter__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestSuite.__iter__" title="Link to this definition">¶</a></dt>
<dd><p>Tests grouped by a <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> are always accessed by iteration.
Subclasses can lazily provide tests by overriding <code class="xref py py-meth docutils literal notranslate"><span class="pre">__iter__()</span></code>. Note
that this method may be called several times on a single suite (for
example when counting tests or comparing for equality) so the tests
returned by repeated iterations before <a class="reference internal" href="#unittest.TestSuite.run" title="unittest.TestSuite.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestSuite.run()</span></code></a> must be the
same for each call iteration. After <a class="reference internal" href="#unittest.TestSuite.run" title="unittest.TestSuite.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestSuite.run()</span></code></a>, callers should
not rely on the tests returned by this method unless the caller uses a
subclass that overrides <code class="xref py py-meth docutils literal notranslate"><span class="pre">TestSuite._removeTestAtIndex()</span></code> to preserve
test references.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>In earlier versions the <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> accessed tests directly rather
than through iteration, so overriding <code class="xref py py-meth docutils literal notranslate"><span class="pre">__iter__()</span></code> wasn’t sufficient
for providing tests.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>In earlier versions the <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> held references to each
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> after <a class="reference internal" href="#unittest.TestSuite.run" title="unittest.TestSuite.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestSuite.run()</span></code></a>. Subclasses can restore
that behavior by overriding <code class="xref py py-meth docutils literal notranslate"><span class="pre">TestSuite._removeTestAtIndex()</span></code>.</p>
</div>
</dd></dl>

<p>In the typical usage of a <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> object, the <a class="reference internal" href="#unittest.TestSuite.run" title="unittest.TestSuite.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run()</span></code></a> method
is invoked by a <code class="xref py py-class docutils literal notranslate"><span class="pre">TestRunner</span></code> rather than by the end-user test harness.</p>
</dd></dl>

</section>
<section id="loading-and-running-tests">
<h3>Loading and running tests<a class="headerlink" href="#loading-and-running-tests" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="unittest.TestLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">TestLoader</span></span><a class="headerlink" href="#unittest.TestLoader" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> class is used to create test suites from classes and
modules.  Normally, there is no need to create an instance of this class; the
<a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> module provides an instance that can be shared as
<a class="reference internal" href="#unittest.defaultTestLoader" title="unittest.defaultTestLoader"><code class="xref py py-data docutils literal notranslate"><span class="pre">unittest.defaultTestLoader</span></code></a>.  Using a subclass or instance, however,
allows customization of some configurable properties.</p>
<p><a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> objects have the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestLoader.errors">
<span class="sig-name descname"><span class="pre">errors</span></span><a class="headerlink" href="#unittest.TestLoader.errors" title="Link to this definition">¶</a></dt>
<dd><p>A list of the non-fatal errors encountered while loading tests. Not reset
by the loader at any point. Fatal errors are signalled by the relevant
method raising an exception to the caller. Non-fatal errors are also
indicated by a synthetic test that will raise the original error when
run.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<p><a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> objects have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestLoader.loadTestsFromTestCase">
<span class="sig-name descname"><span class="pre">loadTestsFromTestCase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">testCaseClass</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestLoader.loadTestsFromTestCase" title="Link to this definition">¶</a></dt>
<dd><p>Return a suite of all test cases contained in the <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>-derived
<code class="xref py py-class docutils literal notranslate"><span class="pre">testCaseClass</span></code>.</p>
<p>A test case instance is created for each method named by
<a class="reference internal" href="#unittest.TestLoader.getTestCaseNames" title="unittest.TestLoader.getTestCaseNames"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getTestCaseNames()</span></code></a>. By default these are the method names
beginning with <code class="docutils literal notranslate"><span class="pre">test</span></code>. If <a class="reference internal" href="#unittest.TestLoader.getTestCaseNames" title="unittest.TestLoader.getTestCaseNames"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getTestCaseNames()</span></code></a> returns no
methods, but the <code class="xref py py-meth docutils literal notranslate"><span class="pre">runTest()</span></code> method is implemented, a single test
case is created for that method instead.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestLoader.loadTestsFromModule">
<span class="sig-name descname"><span class="pre">loadTestsFromModule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestLoader.loadTestsFromModule" title="Link to this definition">¶</a></dt>
<dd><p>Return a suite of all test cases contained in the given module. This
method searches <em>module</em> for classes derived from <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> and
creates an instance of the class for each test method defined for the
class.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>While using a hierarchy of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>-derived classes can be
convenient in sharing fixtures and helper functions, defining test
methods on base classes that are not intended to be instantiated
directly does not play well with this method.  Doing so, however, can
be useful when the fixtures are different and defined in subclasses.</p>
</div>
<p>If a module provides a <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> function it will be called to
load the tests. This allows modules to customize test loading.
This is the <a class="reference internal" href="#id1">load_tests protocol</a>.  The <em>pattern</em> argument is passed as
the third argument to <code class="docutils literal notranslate"><span class="pre">load_tests</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Support for <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Support for a keyword-only argument <em>pattern</em> has been added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The undocumented and unofficial <em>use_load_tests</em> parameter has been
removed.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestLoader.loadTestsFromName">
<span class="sig-name descname"><span class="pre">loadTestsFromName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestLoader.loadTestsFromName" title="Link to this definition">¶</a></dt>
<dd><p>Return a suite of all test cases given a string specifier.</p>
<p>The specifier <em>name</em> is a “dotted name” that may resolve either to a
module, a test case class, a test method within a test case class, a
<a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> instance, or a callable object which returns a
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> or <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> instance.  These checks are
applied in the order listed here; that is, a method on a possible test
case class will be picked up as “a test method within a test case class”,
rather than “a callable object”.</p>
<p>For example, if you have a module <code class="xref py py-mod docutils literal notranslate"><span class="pre">SampleTests</span></code> containing a
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>-derived class <code class="xref py py-class docutils literal notranslate"><span class="pre">SampleTestCase</span></code> with three test
methods (<code class="xref py py-meth docutils literal notranslate"><span class="pre">test_one()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">test_two()</span></code>, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">test_three()</span></code>), the
specifier <code class="docutils literal notranslate"><span class="pre">'SampleTests.SampleTestCase'</span></code> would cause this method to
return a suite which will run all three test methods. Using the specifier
<code class="docutils literal notranslate"><span class="pre">'SampleTests.SampleTestCase.test_two'</span></code> would cause it to return a test
suite which will run only the <code class="xref py py-meth docutils literal notranslate"><span class="pre">test_two()</span></code> test method. The specifier
can refer to modules and packages which have not been imported; they will
be imported as a side-effect.</p>
<p>The method optionally resolves <em>name</em> relative to the given <em>module</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If an <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> or <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a> occurs while traversing
<em>name</em> then a synthetic test that raises that error when run will be
returned. These errors are included in the errors accumulated by
self.errors.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestLoader.loadTestsFromNames">
<span class="sig-name descname"><span class="pre">loadTestsFromNames</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">names</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestLoader.loadTestsFromNames" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#unittest.TestLoader.loadTestsFromName" title="unittest.TestLoader.loadTestsFromName"><code class="xref py py-meth docutils literal notranslate"><span class="pre">loadTestsFromName()</span></code></a>, but takes a sequence of names rather
than a single name.  The return value is a test suite which supports all
the tests defined for each name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestLoader.getTestCaseNames">
<span class="sig-name descname"><span class="pre">getTestCaseNames</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">testCaseClass</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestLoader.getTestCaseNames" title="Link to this definition">¶</a></dt>
<dd><p>Return a sorted sequence of method names found within <em>testCaseClass</em>;
this should be a subclass of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestLoader.discover">
<span class="sig-name descname"><span class="pre">discover</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'test*.py'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">top_level_dir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestLoader.discover" title="Link to this definition">¶</a></dt>
<dd><p>Find all the test modules by recursing into subdirectories from the
specified start directory, and return a TestSuite object containing them.
Only test files that match <em>pattern</em> will be loaded. (Using shell style
pattern matching.) Only module names that are importable (i.e. are valid
Python identifiers) will be loaded.</p>
<p>All test modules must be importable from the top level of the project. If
the start directory is not the top level directory then the top level
directory must be specified separately.</p>
<p>If importing a module fails, for example due to a syntax error, then
this will be recorded as a single error and discovery will continue.  If
the import failure is due to <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a> being raised, it will be
recorded as a skip instead of an error.</p>
<p>If a package (a directory containing a file named <code class="file docutils literal notranslate"><span class="pre">__init__.py</span></code>) is
found, the package will be checked for a <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> function. If this
exists then it will be called
<code class="docutils literal notranslate"><span class="pre">package.load_tests(loader,</span> <span class="pre">tests,</span> <span class="pre">pattern)</span></code>. Test discovery takes care
to ensure that a package is only checked for tests once during an
invocation, even if the load_tests function itself calls
<code class="docutils literal notranslate"><span class="pre">loader.discover</span></code>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> exists then discovery does <em>not</em> recurse into the
package, <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> is responsible for loading all tests in the
package.</p>
<p>The pattern is deliberately not stored as a loader attribute so that
packages can continue discovery themselves. <em>top_level_dir</em> is stored so
<code class="docutils literal notranslate"><span class="pre">load_tests</span></code> does not need to pass this argument in to
<code class="docutils literal notranslate"><span class="pre">loader.discover()</span></code>.</p>
<p><em>start_dir</em> can be a dotted module name as well as a directory.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Modules that raise <a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a> on import are recorded as skips,
not errors.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><em>start_dir</em> can be a <a class="reference internal" href="../glossary.html#term-namespace-package"><span class="xref std std-term">namespace packages</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Paths are sorted before being imported so that execution order is the
same even if the underlying file system’s ordering is not dependent
on file name.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Found packages are now checked for <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> regardless of
whether their path matches <em>pattern</em>, because it is impossible for
a package name to match the default pattern.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><em>start_dir</em> can not be a <a class="reference internal" href="../glossary.html#term-namespace-package"><span class="xref std std-term">namespace packages</span></a>.
It has been broken since Python 3.7 and Python 3.11 officially remove it.</p>
</div>
</dd></dl>

<p>The following attributes of a <a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> can be configured either by
subclassing or assignment on an instance:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestLoader.testMethodPrefix">
<span class="sig-name descname"><span class="pre">testMethodPrefix</span></span><a class="headerlink" href="#unittest.TestLoader.testMethodPrefix" title="Link to this definition">¶</a></dt>
<dd><p>String giving the prefix of method names which will be interpreted as test
methods.  The default value is <code class="docutils literal notranslate"><span class="pre">'test'</span></code>.</p>
<p>This affects <a class="reference internal" href="#unittest.TestLoader.getTestCaseNames" title="unittest.TestLoader.getTestCaseNames"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getTestCaseNames()</span></code></a> and all the <code class="docutils literal notranslate"><span class="pre">loadTestsFrom*</span></code>
methods.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestLoader.sortTestMethodsUsing">
<span class="sig-name descname"><span class="pre">sortTestMethodsUsing</span></span><a class="headerlink" href="#unittest.TestLoader.sortTestMethodsUsing" title="Link to this definition">¶</a></dt>
<dd><p>Function to be used to compare method names when sorting them in
<a class="reference internal" href="#unittest.TestLoader.getTestCaseNames" title="unittest.TestLoader.getTestCaseNames"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getTestCaseNames()</span></code></a> and all the <code class="docutils literal notranslate"><span class="pre">loadTestsFrom*</span></code> methods.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestLoader.suiteClass">
<span class="sig-name descname"><span class="pre">suiteClass</span></span><a class="headerlink" href="#unittest.TestLoader.suiteClass" title="Link to this definition">¶</a></dt>
<dd><p>Callable object that constructs a test suite from a list of tests. No
methods on the resulting object are needed.  The default value is the
<a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> class.</p>
<p>This affects all the <code class="docutils literal notranslate"><span class="pre">loadTestsFrom*</span></code> methods.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestLoader.testNamePatterns">
<span class="sig-name descname"><span class="pre">testNamePatterns</span></span><a class="headerlink" href="#unittest.TestLoader.testNamePatterns" title="Link to this definition">¶</a></dt>
<dd><p>List of Unix shell-style wildcard test name patterns that test methods
have to match to be included in test suites (see <code class="docutils literal notranslate"><span class="pre">-k</span></code> option).</p>
<p>If this attribute is not <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default), all test methods to be
included in test suites must match one of the patterns in this list.
Note that matches are always performed using <a class="reference internal" href="fnmatch.html#fnmatch.fnmatchcase" title="fnmatch.fnmatchcase"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fnmatch.fnmatchcase()</span></code></a>,
so unlike patterns passed to the <code class="docutils literal notranslate"><span class="pre">-k</span></code> option, simple substring patterns
will have to be converted using <code class="docutils literal notranslate"><span class="pre">*</span></code> wildcards.</p>
<p>This affects all the <code class="docutils literal notranslate"><span class="pre">loadTestsFrom*</span></code> methods.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="unittest.TestResult">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">TestResult</span></span><a class="headerlink" href="#unittest.TestResult" title="Link to this definition">¶</a></dt>
<dd><p>This class is used to compile information about which tests have succeeded
and which have failed.</p>
<p>A <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> object stores the results of a set of tests.  The
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> and <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> classes ensure that results are
properly recorded; test authors do not need to worry about recording the
outcome of tests.</p>
<p>Testing frameworks built on top of <a class="reference internal" href="#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> may want access to the
<a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> object generated by running a set of tests for reporting
purposes; a <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> instance is returned by the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">TestRunner.run()</span></code> method for this purpose.</p>
<p><a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> instances have the following attributes that will be of
interest when inspecting the results of running a set of tests:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.errors">
<span class="sig-name descname"><span class="pre">errors</span></span><a class="headerlink" href="#unittest.TestResult.errors" title="Link to this definition">¶</a></dt>
<dd><p>A list containing 2-tuples of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances and strings
holding formatted tracebacks. Each tuple represents a test which raised an
unexpected exception.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.failures">
<span class="sig-name descname"><span class="pre">failures</span></span><a class="headerlink" href="#unittest.TestResult.failures" title="Link to this definition">¶</a></dt>
<dd><p>A list containing 2-tuples of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances and strings
holding formatted tracebacks. Each tuple represents a test where a failure
was explicitly signalled using the <a class="reference internal" href="#assert-methods"><span class="std std-ref">assert* methods</span></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.skipped">
<span class="sig-name descname"><span class="pre">skipped</span></span><a class="headerlink" href="#unittest.TestResult.skipped" title="Link to this definition">¶</a></dt>
<dd><p>A list containing 2-tuples of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances and strings
holding the reason for skipping the test.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.expectedFailures">
<span class="sig-name descname"><span class="pre">expectedFailures</span></span><a class="headerlink" href="#unittest.TestResult.expectedFailures" title="Link to this definition">¶</a></dt>
<dd><p>A list containing 2-tuples of <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances and strings
holding formatted tracebacks.  Each tuple represents an expected failure
or error of the test case.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.unexpectedSuccesses">
<span class="sig-name descname"><span class="pre">unexpectedSuccesses</span></span><a class="headerlink" href="#unittest.TestResult.unexpectedSuccesses" title="Link to this definition">¶</a></dt>
<dd><p>A list containing <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instances that were marked as expected
failures, but succeeded.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.collectedDurations">
<span class="sig-name descname"><span class="pre">collectedDurations</span></span><a class="headerlink" href="#unittest.TestResult.collectedDurations" title="Link to this definition">¶</a></dt>
<dd><p>A list containing 2-tuples of test case names and floats
representing the elapsed time of each test which was run.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.shouldStop">
<span class="sig-name descname"><span class="pre">shouldStop</span></span><a class="headerlink" href="#unittest.TestResult.shouldStop" title="Link to this definition">¶</a></dt>
<dd><p>Set to <code class="docutils literal notranslate"><span class="pre">True</span></code> when the execution of tests should stop by <a class="reference internal" href="#unittest.TestResult.stop" title="unittest.TestResult.stop"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stop()</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.testsRun">
<span class="sig-name descname"><span class="pre">testsRun</span></span><a class="headerlink" href="#unittest.TestResult.testsRun" title="Link to this definition">¶</a></dt>
<dd><p>The total number of tests run so far.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.buffer">
<span class="sig-name descname"><span class="pre">buffer</span></span><a class="headerlink" href="#unittest.TestResult.buffer" title="Link to this definition">¶</a></dt>
<dd><p>If set to true, <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> will be buffered in between
<a class="reference internal" href="#unittest.TestResult.startTest" title="unittest.TestResult.startTest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">startTest()</span></code></a> and <a class="reference internal" href="#unittest.TestResult.stopTest" title="unittest.TestResult.stopTest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stopTest()</span></code></a> being called. Collected output will
only be echoed onto the real <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> if the test
fails or errors. Any output is also attached to the failure / error message.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.failfast">
<span class="sig-name descname"><span class="pre">failfast</span></span><a class="headerlink" href="#unittest.TestResult.failfast" title="Link to this definition">¶</a></dt>
<dd><p>If set to true <a class="reference internal" href="#unittest.TestResult.stop" title="unittest.TestResult.stop"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stop()</span></code></a> will be called on the first failure or error,
halting the test run.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="unittest.TestResult.tb_locals">
<span class="sig-name descname"><span class="pre">tb_locals</span></span><a class="headerlink" href="#unittest.TestResult.tb_locals" title="Link to this definition">¶</a></dt>
<dd><p>If set to true then local variables will be shown in tracebacks.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.wasSuccessful">
<span class="sig-name descname"><span class="pre">wasSuccessful</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.wasSuccessful" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if all tests run so far have passed, otherwise returns
<code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Returns <code class="docutils literal notranslate"><span class="pre">False</span></code> if there were any <a class="reference internal" href="#unittest.TestResult.unexpectedSuccesses" title="unittest.TestResult.unexpectedSuccesses"><code class="xref py py-attr docutils literal notranslate"><span class="pre">unexpectedSuccesses</span></code></a>
from tests marked with the <a class="reference internal" href="#unittest.expectedFailure" title="unittest.expectedFailure"><code class="xref py py-func docutils literal notranslate"><span class="pre">expectedFailure()</span></code></a> decorator.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.stop" title="Link to this definition">¶</a></dt>
<dd><p>This method can be called to signal that the set of tests being run should
be aborted by setting the <a class="reference internal" href="#unittest.TestResult.shouldStop" title="unittest.TestResult.shouldStop"><code class="xref py py-attr docutils literal notranslate"><span class="pre">shouldStop</span></code></a> attribute to <code class="docutils literal notranslate"><span class="pre">True</span></code>.
<code class="xref py py-class docutils literal notranslate"><span class="pre">TestRunner</span></code> objects should respect this flag and return without
running any additional tests.</p>
<p>For example, this feature is used by the <a class="reference internal" href="#unittest.TextTestRunner" title="unittest.TextTestRunner"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextTestRunner</span></code></a> class to
stop the test framework when the user signals an interrupt from the
keyboard.  Interactive tools which provide <code class="xref py py-class docutils literal notranslate"><span class="pre">TestRunner</span></code>
implementations can use this in a similar manner.</p>
</dd></dl>

<p>The following methods of the <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> class are used to maintain
the internal data structures, and may be extended in subclasses to support
additional reporting requirements.  This is particularly useful in building
tools which support interactive reporting while tests are being run.</p>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.startTest">
<span class="sig-name descname"><span class="pre">startTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.startTest" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> is about to be run.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.stopTest">
<span class="sig-name descname"><span class="pre">stopTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.stopTest" title="Link to this definition">¶</a></dt>
<dd><p>Called after the test case <em>test</em> has been executed, regardless of the
outcome.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.startTestRun">
<span class="sig-name descname"><span class="pre">startTestRun</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.startTestRun" title="Link to this definition">¶</a></dt>
<dd><p>Called once before any tests are executed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.stopTestRun">
<span class="sig-name descname"><span class="pre">stopTestRun</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.stopTestRun" title="Link to this definition">¶</a></dt>
<dd><p>Called once after all tests are executed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addError">
<span class="sig-name descname"><span class="pre">addError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">err</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addError" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> raises an unexpected exception. <em>err</em> is a
tuple of the form returned by <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>: <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">value,</span>
<span class="pre">traceback)</span></code>.</p>
<p>The default implementation appends a tuple <code class="docutils literal notranslate"><span class="pre">(test,</span> <span class="pre">formatted_err)</span></code> to
the instance’s <a class="reference internal" href="#unittest.TestResult.errors" title="unittest.TestResult.errors"><code class="xref py py-attr docutils literal notranslate"><span class="pre">errors</span></code></a> attribute, where <em>formatted_err</em> is a
formatted traceback derived from <em>err</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addFailure">
<span class="sig-name descname"><span class="pre">addFailure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">err</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addFailure" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> signals a failure. <em>err</em> is a tuple of
the form returned by <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>: <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">value,</span> <span class="pre">traceback)</span></code>.</p>
<p>The default implementation appends a tuple <code class="docutils literal notranslate"><span class="pre">(test,</span> <span class="pre">formatted_err)</span></code> to
the instance’s <a class="reference internal" href="#unittest.TestResult.failures" title="unittest.TestResult.failures"><code class="xref py py-attr docutils literal notranslate"><span class="pre">failures</span></code></a> attribute, where <em>formatted_err</em> is a
formatted traceback derived from <em>err</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addSuccess">
<span class="sig-name descname"><span class="pre">addSuccess</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addSuccess" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> succeeds.</p>
<p>The default implementation does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addSkip">
<span class="sig-name descname"><span class="pre">addSkip</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reason</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addSkip" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> is skipped.  <em>reason</em> is the reason the
test gave for skipping.</p>
<p>The default implementation appends a tuple <code class="docutils literal notranslate"><span class="pre">(test,</span> <span class="pre">reason)</span></code> to the
instance’s <a class="reference internal" href="#unittest.TestResult.skipped" title="unittest.TestResult.skipped"><code class="xref py py-attr docutils literal notranslate"><span class="pre">skipped</span></code></a> attribute.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addExpectedFailure">
<span class="sig-name descname"><span class="pre">addExpectedFailure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">err</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addExpectedFailure" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> fails or errors, but was marked with
the <a class="reference internal" href="#unittest.expectedFailure" title="unittest.expectedFailure"><code class="xref py py-func docutils literal notranslate"><span class="pre">expectedFailure()</span></code></a> decorator.</p>
<p>The default implementation appends a tuple <code class="docutils literal notranslate"><span class="pre">(test,</span> <span class="pre">formatted_err)</span></code> to
the instance’s <a class="reference internal" href="#unittest.TestResult.expectedFailures" title="unittest.TestResult.expectedFailures"><code class="xref py py-attr docutils literal notranslate"><span class="pre">expectedFailures</span></code></a> attribute, where <em>formatted_err</em>
is a formatted traceback derived from <em>err</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addUnexpectedSuccess">
<span class="sig-name descname"><span class="pre">addUnexpectedSuccess</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addUnexpectedSuccess" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case <em>test</em> was marked with the
<a class="reference internal" href="#unittest.expectedFailure" title="unittest.expectedFailure"><code class="xref py py-func docutils literal notranslate"><span class="pre">expectedFailure()</span></code></a> decorator, but succeeded.</p>
<p>The default implementation appends the test to the instance’s
<a class="reference internal" href="#unittest.TestResult.unexpectedSuccesses" title="unittest.TestResult.unexpectedSuccesses"><code class="xref py py-attr docutils literal notranslate"><span class="pre">unexpectedSuccesses</span></code></a> attribute.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addSubTest">
<span class="sig-name descname"><span class="pre">addSubTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subtest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outcome</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addSubTest" title="Link to this definition">¶</a></dt>
<dd><p>Called when a subtest finishes.  <em>test</em> is the test case
corresponding to the test method.  <em>subtest</em> is a custom
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instance describing the subtest.</p>
<p>If <em>outcome</em> is <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the subtest succeeded.  Otherwise,
it failed with an exception where <em>outcome</em> is a tuple of the form
returned by <a class="reference internal" href="sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>: <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">value,</span> <span class="pre">traceback)</span></code>.</p>
<p>The default implementation does nothing when the outcome is a
success, and records subtest failures as normal failures.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TestResult.addDuration">
<span class="sig-name descname"><span class="pre">addDuration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">elapsed</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TestResult.addDuration" title="Link to this definition">¶</a></dt>
<dd><p>Called when the test case finishes.  <em>elapsed</em> is the time represented in
seconds, and it includes the execution of cleanup functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="unittest.TextTestResult">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">TextTestResult</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">descriptions</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbosity</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">durations</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TextTestResult" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> used by the
<a class="reference internal" href="#unittest.TextTestRunner" title="unittest.TextTestRunner"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextTestRunner</span></code></a>. Subclasses should accept <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> to ensure
compatibility as the interface changes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>durations</em> keyword parameter.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="unittest.defaultTestLoader">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">defaultTestLoader</span></span><a class="headerlink" href="#unittest.defaultTestLoader" title="Link to this definition">¶</a></dt>
<dd><p>Instance of the <a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> class intended to be shared.  If no
customization of the <a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> is needed, this instance can be used
instead of repeatedly creating new instances.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="unittest.TextTestRunner">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">TextTestRunner</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">descriptions</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbosity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">failfast</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resultclass</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">warnings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tb_locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">durations</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TextTestRunner" title="Link to this definition">¶</a></dt>
<dd><p>A basic test runner implementation that outputs results to a stream. If <em>stream</em>
is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the default, <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a> is used as the output stream. This class
has a few configurable parameters, but is essentially very simple.  Graphical
applications which run test suites should provide alternate implementations. Such
implementations should accept <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> as the interface to construct runners
changes when features are added to unittest.</p>
<p>By default this runner shows <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a>,
<a class="reference internal" href="exceptions.html#PendingDeprecationWarning" title="PendingDeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PendingDeprecationWarning</span></code></a>, <a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a> and
<a class="reference internal" href="exceptions.html#ImportWarning" title="ImportWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportWarning</span></code></a> even if they are <a class="reference internal" href="warnings.html#warning-ignored"><span class="std std-ref">ignored by default</span></a>.  This behavior can
be overridden using Python’s <code class="xref std std-option docutils literal notranslate"><span class="pre">-Wd</span></code> or <code class="xref std std-option docutils literal notranslate"><span class="pre">-Wa</span></code> options
(see <a class="reference internal" href="../using/cmdline.html#using-on-warnings"><span class="std std-ref">Warning control</span></a>) and leaving
<em>warnings</em> to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>warnings</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The default stream is set to <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a> at instantiation time rather
than import time.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>tb_locals</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>durations</em> parameter.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="unittest.TextTestRunner._makeResult">
<span class="sig-name descname"><span class="pre">_makeResult</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TextTestRunner._makeResult" title="Link to this definition">¶</a></dt>
<dd><p>This method returns the instance of <code class="docutils literal notranslate"><span class="pre">TestResult</span></code> used by <a class="reference internal" href="#unittest.TextTestRunner.run" title="unittest.TextTestRunner.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run()</span></code></a>.
It is not intended to be called directly, but can be overridden in
subclasses to provide a custom <code class="docutils literal notranslate"><span class="pre">TestResult</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">_makeResult()</span></code> instantiates the class or callable passed in the
<code class="docutils literal notranslate"><span class="pre">TextTestRunner</span></code> constructor as the <code class="docutils literal notranslate"><span class="pre">resultclass</span></code> argument. It
defaults to <a class="reference internal" href="#unittest.TextTestResult" title="unittest.TextTestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextTestResult</span></code></a> if no <code class="docutils literal notranslate"><span class="pre">resultclass</span></code> is provided.
The result class is instantiated with the following arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">stream</span><span class="p">,</span> <span class="n">descriptions</span><span class="p">,</span> <span class="n">verbosity</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.TextTestRunner.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.TextTestRunner.run" title="Link to this definition">¶</a></dt>
<dd><p>This method is the main public interface to the <code class="docutils literal notranslate"><span class="pre">TextTestRunner</span></code>. This
method takes a <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> or <a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> instance. A
<a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> is created by calling
<a class="reference internal" href="#unittest.TextTestRunner._makeResult" title="unittest.TextTestRunner._makeResult"><code class="xref py py-func docutils literal notranslate"><span class="pre">_makeResult()</span></code></a> and the test(s) are run and the
results printed to stdout.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.main">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'__main__'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">defaultTest</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">argv</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">testRunner</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">testLoader</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">unittest.defaultTestLoader</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbosity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">failfast</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">catchbreak</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">warnings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.main" title="Link to this definition">¶</a></dt>
<dd><p>A command-line program that loads a set of tests from <em>module</em> and runs them;
this is primarily for making test modules conveniently executable.
The simplest use for this function is to include the following line at the
end of a test script:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">unittest</span><span class="o">.</span><span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
<p>You can run tests with more detailed information by passing in the verbosity
argument:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">unittest</span><span class="o">.</span><span class="n">main</span><span class="p">(</span><span class="n">verbosity</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>The <em>defaultTest</em> argument is either the name of a single test or an
iterable of test names to run if no test names are specified via <em>argv</em>.  If
not specified or <code class="docutils literal notranslate"><span class="pre">None</span></code> and no test names are provided via <em>argv</em>, all
tests found in <em>module</em> are run.</p>
<p>The <em>argv</em> argument can be a list of options passed to the program, with the
first element being the program name.  If not specified or <code class="docutils literal notranslate"><span class="pre">None</span></code>,
the values of <a class="reference internal" href="sys.html#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a> are used.</p>
<p>The <em>testRunner</em> argument can either be a test runner class or an already
created instance of it. By default <code class="docutils literal notranslate"><span class="pre">main</span></code> calls <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a> with
an exit code indicating success (0) or failure (1) of the tests run.
An exit code of 5 indicates that no tests were run or skipped.</p>
<p>The <em>testLoader</em> argument has to be a <a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> instance,
and defaults to <a class="reference internal" href="#unittest.defaultTestLoader" title="unittest.defaultTestLoader"><code class="xref py py-data docutils literal notranslate"><span class="pre">defaultTestLoader</span></code></a>.</p>
<p><code class="docutils literal notranslate"><span class="pre">main</span></code> supports being used from the interactive interpreter by passing in the
argument <code class="docutils literal notranslate"><span class="pre">exit=False</span></code>. This displays the result on standard output without
calling <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">unittest</span> <span class="kn">import</span> <span class="n">main</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">main</span><span class="p">(</span><span class="n">module</span><span class="o">=</span><span class="s1">&#39;test_module&#39;</span><span class="p">,</span> <span class="n">exit</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</pre></div>
</div>
<p>The <em>failfast</em>, <em>catchbreak</em> and <em>buffer</em> parameters have the same
effect as the same-name <a class="reference internal" href="#command-line-options">command-line options</a>.</p>
<p>The <em>warnings</em> argument specifies the <a class="reference internal" href="warnings.html#warning-filter"><span class="std std-ref">warning filter</span></a>
that should be used while running the tests.  If it’s not specified, it will
remain <code class="docutils literal notranslate"><span class="pre">None</span></code> if a <code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code> option is passed to <strong class="program">python</strong>
(see <a class="reference internal" href="../using/cmdline.html#using-on-warnings"><span class="std std-ref">Warning control</span></a>),
otherwise it will be set to <code class="docutils literal notranslate"><span class="pre">'default'</span></code>.</p>
<p>Calling <code class="docutils literal notranslate"><span class="pre">main</span></code> actually returns an instance of the <code class="docutils literal notranslate"><span class="pre">TestProgram</span></code> class.
This stores the result of the tests run as the <code class="docutils literal notranslate"><span class="pre">result</span></code> attribute.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>The <em>exit</em> parameter was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>verbosity</em>, <em>failfast</em>, <em>catchbreak</em>, <em>buffer</em>
and <em>warnings</em> parameters were added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The <em>defaultTest</em> parameter was changed to also accept an iterable of
test names.</p>
</div>
</dd></dl>

<section id="load-tests-protocol">
<span id="id1"></span><h4>load_tests Protocol<a class="headerlink" href="#load-tests-protocol" title="Link to this heading">¶</a></h4>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>Modules or packages can customize how tests are loaded from them during normal
test runs or test discovery by implementing a function called <code class="docutils literal notranslate"><span class="pre">load_tests</span></code>.</p>
<p>If a test module defines <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> it will be called by
<a class="reference internal" href="#unittest.TestLoader.loadTestsFromModule" title="unittest.TestLoader.loadTestsFromModule"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestLoader.loadTestsFromModule()</span></code></a> with the following arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">load_tests</span><span class="p">(</span><span class="n">loader</span><span class="p">,</span> <span class="n">standard_tests</span><span class="p">,</span> <span class="n">pattern</span><span class="p">)</span>
</pre></div>
</div>
<p>where <em>pattern</em> is passed straight through from <code class="docutils literal notranslate"><span class="pre">loadTestsFromModule</span></code>.  It
defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>It should return a <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a>.</p>
<p><em>loader</em> is the instance of <a class="reference internal" href="#unittest.TestLoader" title="unittest.TestLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestLoader</span></code></a> doing the loading.
<em>standard_tests</em> are the tests that would be loaded by default from the
module. It is common for test modules to only want to add or remove tests
from the standard set of tests.
The third argument is used when loading packages as part of test discovery.</p>
<p>A typical <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> function that loads tests from a specific set of
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> classes may look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">test_cases</span> <span class="o">=</span> <span class="p">(</span><span class="n">TestCase1</span><span class="p">,</span> <span class="n">TestCase2</span><span class="p">,</span> <span class="n">TestCase3</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">load_tests</span><span class="p">(</span><span class="n">loader</span><span class="p">,</span> <span class="n">tests</span><span class="p">,</span> <span class="n">pattern</span><span class="p">):</span>
    <span class="n">suite</span> <span class="o">=</span> <span class="n">TestSuite</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">test_class</span> <span class="ow">in</span> <span class="n">test_cases</span><span class="p">:</span>
        <span class="n">tests</span> <span class="o">=</span> <span class="n">loader</span><span class="o">.</span><span class="n">loadTestsFromTestCase</span><span class="p">(</span><span class="n">test_class</span><span class="p">)</span>
        <span class="n">suite</span><span class="o">.</span><span class="n">addTests</span><span class="p">(</span><span class="n">tests</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">suite</span>
</pre></div>
</div>
<p>If discovery is started in a directory containing a package, either from the
command line or by calling <a class="reference internal" href="#unittest.TestLoader.discover" title="unittest.TestLoader.discover"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TestLoader.discover()</span></code></a>, then the package
<code class="file docutils literal notranslate"><span class="pre">__init__.py</span></code> will be checked for <code class="docutils literal notranslate"><span class="pre">load_tests</span></code>.  If that function does
not exist, discovery will recurse into the package as though it were just
another directory.  Otherwise, discovery of the package’s tests will be left up
to <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> which is called with the following arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">load_tests</span><span class="p">(</span><span class="n">loader</span><span class="p">,</span> <span class="n">standard_tests</span><span class="p">,</span> <span class="n">pattern</span><span class="p">)</span>
</pre></div>
</div>
<p>This should return a <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a> representing all the tests
from the package. (<code class="docutils literal notranslate"><span class="pre">standard_tests</span></code> will only contain tests
collected from <code class="file docutils literal notranslate"><span class="pre">__init__.py</span></code>.)</p>
<p>Because the pattern is passed into <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> the package is free to
continue (and potentially modify) test discovery. A ‘do nothing’
<code class="docutils literal notranslate"><span class="pre">load_tests</span></code> function for a test package would look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">load_tests</span><span class="p">(</span><span class="n">loader</span><span class="p">,</span> <span class="n">standard_tests</span><span class="p">,</span> <span class="n">pattern</span><span class="p">):</span>
    <span class="c1"># top level directory cached on loader instance</span>
    <span class="n">this_dir</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">)</span>
    <span class="n">package_tests</span> <span class="o">=</span> <span class="n">loader</span><span class="o">.</span><span class="n">discover</span><span class="p">(</span><span class="n">start_dir</span><span class="o">=</span><span class="n">this_dir</span><span class="p">,</span> <span class="n">pattern</span><span class="o">=</span><span class="n">pattern</span><span class="p">)</span>
    <span class="n">standard_tests</span><span class="o">.</span><span class="n">addTests</span><span class="p">(</span><span class="n">package_tests</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">standard_tests</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Discovery no longer checks package names for matching <em>pattern</em> due to the
impossibility of package names matching the default pattern.</p>
</div>
</section>
</section>
</section>
<section id="class-and-module-fixtures">
<h2>Class and Module Fixtures<a class="headerlink" href="#class-and-module-fixtures" title="Link to this heading">¶</a></h2>
<p>Class and module level fixtures are implemented in <a class="reference internal" href="#unittest.TestSuite" title="unittest.TestSuite"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestSuite</span></code></a>. When
the test suite encounters a test from a new class then <code class="xref py py-meth docutils literal notranslate"><span class="pre">tearDownClass()</span></code>
from the previous class (if there is one) is called, followed by
<code class="xref py py-meth docutils literal notranslate"><span class="pre">setUpClass()</span></code> from the new class.</p>
<p>Similarly if a test is from a different module from the previous test then
<code class="docutils literal notranslate"><span class="pre">tearDownModule</span></code> from the previous module is run, followed by
<code class="docutils literal notranslate"><span class="pre">setUpModule</span></code> from the new module.</p>
<p>After all the tests have run the final <code class="docutils literal notranslate"><span class="pre">tearDownClass</span></code> and
<code class="docutils literal notranslate"><span class="pre">tearDownModule</span></code> are run.</p>
<p>Note that shared fixtures do not play well with [potential] features like test
parallelization and they break test isolation. They should be used with care.</p>
<p>The default ordering of tests created by the unittest test loaders is to group
all tests from the same modules and classes together. This will lead to
<code class="docutils literal notranslate"><span class="pre">setUpClass</span></code> / <code class="docutils literal notranslate"><span class="pre">setUpModule</span></code> (etc) being called exactly once per class and
module. If you randomize the order, so that tests from different modules and
classes are adjacent to each other, then these shared fixture functions may be
called multiple times in a single test run.</p>
<p>Shared fixtures are not intended to work with suites with non-standard
ordering. A <code class="docutils literal notranslate"><span class="pre">BaseTestSuite</span></code> still exists for frameworks that don’t want to
support shared fixtures.</p>
<p>If there are any exceptions raised during one of the shared fixture functions
the test is reported as an error. Because there is no corresponding test
instance an <code class="docutils literal notranslate"><span class="pre">_ErrorHolder</span></code> object (that has the same interface as a
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a>) is created to represent the error. If you are just using
the standard unittest test runner then this detail doesn’t matter, but if you
are a framework author it may be relevant.</p>
<section id="setupclass-and-teardownclass">
<h3>setUpClass and tearDownClass<a class="headerlink" href="#setupclass-and-teardownclass" title="Link to this heading">¶</a></h3>
<p>These must be implemented as class methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">unittest</span>

<span class="k">class</span> <span class="nc">Test</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">setUpClass</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
        <span class="bp">cls</span><span class="o">.</span><span class="n">_connection</span> <span class="o">=</span> <span class="n">createExpensiveConnectionObject</span><span class="p">()</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">tearDownClass</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
        <span class="bp">cls</span><span class="o">.</span><span class="n">_connection</span><span class="o">.</span><span class="n">destroy</span><span class="p">()</span>
</pre></div>
</div>
<p>If you want the <code class="docutils literal notranslate"><span class="pre">setUpClass</span></code> and <code class="docutils literal notranslate"><span class="pre">tearDownClass</span></code> on base classes called
then you must call up to them yourself. The implementations in
<a class="reference internal" href="#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></a> are empty.</p>
<p>If an exception is raised during a <code class="docutils literal notranslate"><span class="pre">setUpClass</span></code> then the tests in the class
are not run and the <code class="docutils literal notranslate"><span class="pre">tearDownClass</span></code> is not run. Skipped classes will not
have <code class="docutils literal notranslate"><span class="pre">setUpClass</span></code> or <code class="docutils literal notranslate"><span class="pre">tearDownClass</span></code> run. If the exception is a
<a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a> exception then the class will be reported as having been skipped
instead of as an error.</p>
</section>
<section id="setupmodule-and-teardownmodule">
<h3>setUpModule and tearDownModule<a class="headerlink" href="#setupmodule-and-teardownmodule" title="Link to this heading">¶</a></h3>
<p>These should be implemented as functions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">setUpModule</span><span class="p">():</span>
    <span class="n">createConnection</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">tearDownModule</span><span class="p">():</span>
    <span class="n">closeConnection</span><span class="p">()</span>
</pre></div>
</div>
<p>If an exception is raised in a <code class="docutils literal notranslate"><span class="pre">setUpModule</span></code> then none of the tests in the
module will be run and the <code class="docutils literal notranslate"><span class="pre">tearDownModule</span></code> will not be run. If the exception is a
<a class="reference internal" href="#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SkipTest</span></code></a> exception then the module will be reported as having been skipped
instead of as an error.</p>
<p>To add cleanup code that must be run even in the case of an exception, use
<code class="docutils literal notranslate"><span class="pre">addModuleCleanup</span></code>:</p>
<dl class="py function">
<dt class="sig sig-object py" id="unittest.addModuleCleanup">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">addModuleCleanup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.addModuleCleanup" title="Link to this definition">¶</a></dt>
<dd><p>Add a function to be called after <code class="xref py py-func docutils literal notranslate"><span class="pre">tearDownModule()</span></code> to cleanup
resources used during the test class. Functions will be called in reverse
order to the order they are added (<abbr title="last-in, first-out">LIFO</abbr>).
They are called with any arguments and keyword arguments passed into
<a class="reference internal" href="#unittest.addModuleCleanup" title="unittest.addModuleCleanup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addModuleCleanup()</span></code></a> when they are added.</p>
<p>If <code class="xref py py-meth docutils literal notranslate"><span class="pre">setUpModule()</span></code> fails, meaning that <code class="xref py py-func docutils literal notranslate"><span class="pre">tearDownModule()</span></code> is not
called, then any cleanup functions added will still be called.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="unittest.enterModuleContext">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">enterModuleContext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cm</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.enterModuleContext" title="Link to this definition">¶</a></dt>
<dd><p>Enter the supplied <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a>.  If successful, also
add its <a class="reference internal" href="../reference/datamodel.html#object.__exit__" title="object.__exit__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__exit__()</span></code></a> method as a cleanup function by
<a class="reference internal" href="#unittest.addModuleCleanup" title="unittest.addModuleCleanup"><code class="xref py py-func docutils literal notranslate"><span class="pre">addModuleCleanup()</span></code></a> and return the result of the
<a class="reference internal" href="../reference/datamodel.html#object.__enter__" title="object.__enter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__enter__()</span></code></a> method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.doModuleCleanups">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">doModuleCleanups</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.doModuleCleanups" title="Link to this definition">¶</a></dt>
<dd><p>This function is called unconditionally after <code class="xref py py-func docutils literal notranslate"><span class="pre">tearDownModule()</span></code>, or
after <code class="xref py py-func docutils literal notranslate"><span class="pre">setUpModule()</span></code> if <code class="xref py py-func docutils literal notranslate"><span class="pre">setUpModule()</span></code> raises an exception.</p>
<p>It is responsible for calling all the cleanup functions added by
<a class="reference internal" href="#unittest.addModuleCleanup" title="unittest.addModuleCleanup"><code class="xref py py-func docutils literal notranslate"><span class="pre">addModuleCleanup()</span></code></a>. If you need cleanup functions to be called
<em>prior</em> to <code class="xref py py-func docutils literal notranslate"><span class="pre">tearDownModule()</span></code> then you can call
<a class="reference internal" href="#unittest.doModuleCleanups" title="unittest.doModuleCleanups"><code class="xref py py-func docutils literal notranslate"><span class="pre">doModuleCleanups()</span></code></a> yourself.</p>
<p><a class="reference internal" href="#unittest.doModuleCleanups" title="unittest.doModuleCleanups"><code class="xref py py-func docutils literal notranslate"><span class="pre">doModuleCleanups()</span></code></a> pops methods off the stack of cleanup
functions one at a time, so it can be called at any time.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="signal-handling">
<h2>Signal Handling<a class="headerlink" href="#signal-handling" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>The <a class="reference internal" href="#cmdoption-unittest-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c/--catch</span></code></a> command-line option to unittest,
along with the <code class="docutils literal notranslate"><span class="pre">catchbreak</span></code> parameter to <a class="reference internal" href="#unittest.main" title="unittest.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">unittest.main()</span></code></a>, provide
more friendly handling of control-C during a test run. With catch break
behavior enabled control-C will allow the currently running test to complete,
and the test run will then end and report all the results so far. A second
control-c will raise a <a class="reference internal" href="exceptions.html#KeyboardInterrupt" title="KeyboardInterrupt"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyboardInterrupt</span></code></a> in the usual way.</p>
<p>The control-c handling signal handler attempts to remain compatible with code or
tests that install their own <a class="reference internal" href="signal.html#signal.SIGINT" title="signal.SIGINT"><code class="xref py py-const docutils literal notranslate"><span class="pre">signal.SIGINT</span></code></a> handler. If the <code class="docutils literal notranslate"><span class="pre">unittest</span></code>
handler is called but <em>isn’t</em> the installed <a class="reference internal" href="signal.html#signal.SIGINT" title="signal.SIGINT"><code class="xref py py-const docutils literal notranslate"><span class="pre">signal.SIGINT</span></code></a> handler,
i.e. it has been replaced by the system under test and delegated to, then it
calls the default handler. This will normally be the expected behavior by code
that replaces an installed handler and delegates to it. For individual tests
that need <code class="docutils literal notranslate"><span class="pre">unittest</span></code> control-c handling disabled the <a class="reference internal" href="#unittest.removeHandler" title="unittest.removeHandler"><code class="xref py py-func docutils literal notranslate"><span class="pre">removeHandler()</span></code></a>
decorator can be used.</p>
<p>There are a few utility functions for framework authors to enable control-c
handling functionality within test frameworks.</p>
<dl class="py function">
<dt class="sig sig-object py" id="unittest.installHandler">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">installHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#unittest.installHandler" title="Link to this definition">¶</a></dt>
<dd><p>Install the control-c handler. When a <a class="reference internal" href="signal.html#signal.SIGINT" title="signal.SIGINT"><code class="xref py py-const docutils literal notranslate"><span class="pre">signal.SIGINT</span></code></a> is received
(usually in response to the user pressing control-c) all registered results
have <a class="reference internal" href="#unittest.TestResult.stop" title="unittest.TestResult.stop"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stop()</span></code></a> called.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.registerResult">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">registerResult</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.registerResult" title="Link to this definition">¶</a></dt>
<dd><p>Register a <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> object for control-c handling. Registering a
result stores a weak reference to it, so it doesn’t prevent the result from
being garbage collected.</p>
<p>Registering a <a class="reference internal" href="#unittest.TestResult" title="unittest.TestResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">TestResult</span></code></a> object has no side-effects if control-c
handling is not enabled, so test frameworks can unconditionally register
all results they create independently of whether or not handling is enabled.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.removeResult">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">removeResult</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.removeResult" title="Link to this definition">¶</a></dt>
<dd><p>Remove a registered result. Once a result has been removed then
<a class="reference internal" href="#unittest.TestResult.stop" title="unittest.TestResult.stop"><code class="xref py py-meth docutils literal notranslate"><span class="pre">stop()</span></code></a> will no longer be called on that result object in
response to a control-c.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unittest.removeHandler">
<span class="sig-prename descclassname"><span class="pre">unittest.</span></span><span class="sig-name descname"><span class="pre">removeHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unittest.removeHandler" title="Link to this definition">¶</a></dt>
<dd><p>When called without arguments this function removes the control-c handler
if it has been installed. This function can also be used as a test decorator
to temporarily remove the handler while the test is being executed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@unittest</span><span class="o">.</span><span class="n">removeHandler</span>
<span class="k">def</span> <span class="nf">test_signal_handling</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code> — Unit testing framework</a><ul>
<li><a class="reference internal" href="#basic-example">Basic example</a></li>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command-line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#test-discovery">Test Discovery</a></li>
<li><a class="reference internal" href="#organizing-test-code">Organizing test code</a></li>
<li><a class="reference internal" href="#re-using-old-test-code">Re-using old test code</a></li>
<li><a class="reference internal" href="#skipping-tests-and-expected-failures">Skipping tests and expected failures</a></li>
<li><a class="reference internal" href="#distinguishing-test-iterations-using-subtests">Distinguishing test iterations using subtests</a></li>
<li><a class="reference internal" href="#classes-and-functions">Classes and functions</a><ul>
<li><a class="reference internal" href="#test-cases">Test cases</a></li>
<li><a class="reference internal" href="#grouping-tests">Grouping tests</a></li>
<li><a class="reference internal" href="#loading-and-running-tests">Loading and running tests</a><ul>
<li><a class="reference internal" href="#load-tests-protocol">load_tests Protocol</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#class-and-module-fixtures">Class and Module Fixtures</a><ul>
<li><a class="reference internal" href="#setupclass-and-teardownclass">setUpClass and tearDownClass</a></li>
<li><a class="reference internal" href="#setupmodule-and-teardownmodule">setUpModule and tearDownModule</a></li>
</ul>
</li>
<li><a class="reference internal" href="#signal-handling">Signal Handling</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="doctest.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code> — Test interactive Python examples</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unittest.mock.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest.mock</span></code> — mock object library</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/unittest.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="unittest.mock.html" title="unittest.mock — mock object library"
             >next</a> |</li>
        <li class="right" >
          <a href="doctest.html" title="doctest — Test interactive Python examples"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" >Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code> — Unit testing framework</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>