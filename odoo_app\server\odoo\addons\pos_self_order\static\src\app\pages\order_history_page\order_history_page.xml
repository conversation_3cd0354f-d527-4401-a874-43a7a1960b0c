<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.OrdersHistoryPage">
        <div class="overflow-auto h-100">
            <t t-if="state.loadingProgress">
                <div class="d-flex align-items-center h-100 justify-content-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </t>
            <div t-else="" class="d-flex flex-column h-100">
                <div class="overflow-y-auto flex-grow-1 flex-shrink-1">
                    <t t-foreach="orders" t-as="order" t-key="order.access_token">
                        <div class="o_so_order d-flex flex-column flex-grow-1 mb-2 bg-white">
                            <div class="o_so_order_header p-3" t-on-click="() => this.editOrder(order)">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex flex-column">
                                        <h6 class="m-0" t-esc="order.pos_reference"/>
                                        <span class="text-muted">Order number: <t t-esc="order.trackingNumber" /></span>
                                    </div>
                                    <span
                                        class="badge py-2 rounded-pill text-capitalize"
                                        t-att-class="{
                                            'text-bg-success': order.state == 'paid',
                                            'text-bg-primary': order.state != 'paid'
                                        }"
                                        t-esc="getOrderState(order.state)"/>
                                </div>
                                <p class="small m-0 fst-italic text-muted"
                                    t-esc="order.date"/>
                            </div>
                            <div class="o_so_order_body pt-2 border-top">
                                <div
                                    t-foreach="order.lines"
                                    t-as="line"
                                    t-key="line.uuid"
                                    t-attf-class="o_self_order_item_card position-relative d-flex align-items-start w-100 px-3 overflow-hidden"
                                    >
                                    <div class="d-flex w-100 py-1 justify-content-between">
                                        <div t-attf-class="d-flex {{ line.qty ? 'flex-column align-items-start' : 'flex-row align-items-center' }} text-900 fw-bold fs-6">
                                            <t t-set="lineName" t-value="getNameAndDescription(line)" />
                                            <h4 class="mb-0 o_self_product_name" t-esc="lineName.productName" />
                                            <div t-if="line.qty">
                                                <span class="text-primary fw-bolder small" t-esc="`${line.qty}x `" />
                                                <span
                                                    class="flex-grow-1 me-3 small text-muted"
                                                    t-esc="selfOrder.formatMonetary(getPrice(line) / line.qty)"
                                                    />
                                            </div>
                                            <span
                                                t-if="lineName.attributes"
                                                class="m-0 text-muted small break-line"
                                                t-esc="lineName.attributes"
                                                />
                                            <t t-set="comboParent" t-value="line.combo_parent_uuid and order.lines.find(l => l.uuid === line.combo_parent_uuid)" />
                                            <div t-if="comboParent" class="info ms-2 combo-parent-name">
                                                <i class="fa fa-th-large me-2" role="img" aria-label="Combo" title="Combo"/>
                                                <t t-esc="selfOrder.productByIds[comboParent.product_id].name" />
                                            </div>
                                            <div t-if="line.customer_note" class="d-inline-block m-0 text-muted small break-line">
                                                <i class="fa fa-pencil-square-o" aria-hidden="true" />
                                                <span t-esc="line.customer_note" class="customer_note ms-1" />
                                            </div>
                                        </div>
                                        <span t-attf-class="card-text line_price"
                                            t-esc="selfOrder.formatMonetary(getPrice(line))"/>
                                    </div>
                                </div>
                                <div class="d-flex mt-2 px-3">
                                    <div class="ms-auto border-top">
                                        <table class="table table-sm table-borderless mb-0 py-3">
                                            <tbody>
                                                <tr class="text-end text-muted">
                                                    <th class="pt-2 pb-0">Tax:</th>
                                                    <th class="pt-2 pb-0 pe-0" t-if="!selfOrder.priceLoading" t-esc="selfOrder.formatMonetary(order.amount_tax)"/>
                                                    <span t-else="" class="spinner-border"/>
                                                </tr>
                                                <tr class="text-end">
                                                    <th class="pt-0">Total:</th>
                                                    <th class="pt-0 pe-0" t-if="!selfOrder.priceLoading" t-esc="selfOrder.formatMonetary(order.amount_total)"/>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                    <div t-if="orders.length === 0" class="d-flex justify-content-center mt-3">
                        <div>No order found</div>
                    </div>
                </div>
                <div class="bg-view p-3 border-top">
                    <button class="btn btn-secondary btn-lg" t-on-click="back">
                        <i class="oi oi-chevron-left"/>
                        Back
                    </button>
                </div>
            </div>

        </div>
    </t>
</templates>
