# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_viva_wallet
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2024-02-07 08:49+0000\n"
"Last-Translator: Deniz <PERSON>_<PERSON>do<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
msgid "API Key"
msgstr "API Anahtarı"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Cannot process transactions with negative amount."
msgstr "Negatif tutardaki işlemler işlenemiyor."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid "Client ID"
msgstr "Müşteri ID"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_secret
msgid "Client secret"
msgstr "İstemci gizli anahtarı"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Odoo sunucusuna bağlanılamadı, lütfen internet bağlantınızı kontrol edin ve "
"tekrar deneyin."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "It is essential to provide API key for the use of viva wallet"
msgstr "Viva Wallet kullanımı için API anahtarının sağlanması zorunludur"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid "Merchant ID"
msgstr "Ticari ID"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Message from Viva Wallet: %s"
msgstr "Viva Wallet'tan gelen mesaj: %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to cancel a Viva Wallet payment"
msgstr ""
"Yalnızca 'group_pos_user' grubundakiler bir Viva Wallet ödemesini iptal "
"edebilir"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to get latest transaction status"
msgstr ""
"Yalnızca 'group_pos_user' grubu en son işlem durumunu görüntüleyebilir"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to get the payment status from Viva Wallet"
msgstr ""
"Yalnızca 'group_pos_user' grubundaki kullanıcılar bir Viva Wallet ödemesini "
"iptal edebilir."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to send a Viva Wallet payment request"
msgstr "Yalnızca 'group_pos_user' grubu Viva Wallet ödeme isteği gönderebilir"

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Satış Noktası Ödeme Yöntemleri"

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_session
msgid "Point of Sale Session"
msgstr "Satış Noktası Oturumu"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Run transactions in the test environment."
msgstr "İşlemleri test ortamında çalıştırın."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "Terminal ID"
msgstr "Terminal Kimliği"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Test mode"
msgstr "Test Modu"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later. %s"
msgstr ""
"Viva Cüzdan ile aramızda bazı sorunlar var, daha sonra tekrar deneyin. %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later.%s)"
msgstr ""
"Viva Wallet ile aramızda bazı sorunlar yaşanıyor, lütfen daha sonra tekrar "
"deneyin.%s)"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Unable to retrieve Viva Wallet Bearer Token: Please verify that the Client "
"ID and Client Secret are correct"
msgstr ""
"Viva Wallet Bearer Token alınamadı: Lütfen Müşteri Kimliği (Client ID) ve "
"Müşteri Sırrı (Client Secret) bilgilerinin doğru olduğunu kontrol edin."

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"
msgstr ""
"Viva Wallet bağlantısı sırasında kullanılır: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"
msgstr ""
"Viva Wallet bağlantısı sırasında kullanılır: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_bearer_token
msgid "Viva Wallet Bearer Token"
msgstr "Viva Wallet Bearer Token"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Viva Wallet Error"
msgstr "Viva Wallet Hatası"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_latest_response
msgid "Viva Wallet Latest Response"
msgstr "Viva Wallet Son Yanıt"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_endpoint
msgid "Viva Wallet Webhook Endpoint"
msgstr "Viva Wallet Webhook Uç Noktası"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_verification_key
msgid "Viva Wallet Webhook Verification Key"
msgstr "Viva Wallet Webhook Doğrulama Anahtarı"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Your transaction with Viva Wallet failed. Please try again later."
msgstr ""
"Viva Wallet ile işleminiz başarısız oldu. Lütfen daha sonra tekrar deneyin."

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "[Terminal ID of the Viva Wallet terminal], for example: 16002169"
msgstr "[Viva Wallet terminalinin Terminal Kimliği], örneğin: 16002169"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a pos payment provider not registered."
msgstr "kayıtlı olmayan bir POS ödeme sağlayıcısı için bir mesaj alındı."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a terminal not registered in Odoo: %s"
msgstr "Odoo'da kayıtlı olmayan bir terminal için bir mesaj alındı:%s"
