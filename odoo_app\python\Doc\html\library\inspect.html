<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="inspect — Inspect live objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/inspect.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/inspect.py The inspect module provides several useful functions to help get information about live objects such as modules, classes, methods, functions, tracebacks, frame objects, ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/inspect.py The inspect module provides several useful functions to help get information about live objects such as modules, classes, methods, functions, tracebacks, frame objects, ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>inspect — Inspect live objects &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="site — Site-specific configuration hook" href="site.html" />
    <link rel="prev" title="gc — Garbage Collector interface" href="gc.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/inspect.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a><ul>
<li><a class="reference internal" href="#types-and-members">Types and members</a></li>
<li><a class="reference internal" href="#retrieving-source-code">Retrieving source code</a></li>
<li><a class="reference internal" href="#introspecting-callables-with-the-signature-object">Introspecting callables with the Signature object</a></li>
<li><a class="reference internal" href="#classes-and-functions">Classes and functions</a></li>
<li><a class="reference internal" href="#the-interpreter-stack">The interpreter stack</a></li>
<li><a class="reference internal" href="#fetching-attributes-statically">Fetching attributes statically</a></li>
<li><a class="reference internal" href="#current-state-of-generators-coroutines-and-asynchronous-generators">Current State of Generators, Coroutines, and Asynchronous Generators</a></li>
<li><a class="reference internal" href="#code-objects-bit-flags">Code Objects Bit Flags</a></li>
<li><a class="reference internal" href="#buffer-flags">Buffer flags</a></li>
<li><a class="reference internal" href="#command-line-interface">Command Line Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gc.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="site.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/inspect.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="site.html" title="site — Site-specific configuration hook"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="gc.html" title="gc — Garbage Collector interface"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="inspect-inspect-live-objects">
<h1><a class="reference internal" href="#module-inspect" title="inspect: Extract information and source code from live objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code></a> — Inspect live objects<a class="headerlink" href="#inspect-inspect-live-objects" title="Link to this heading">¶</a></h1>
<p id="module-inspect"><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/inspect.py">Lib/inspect.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-inspect" title="inspect: Extract information and source code from live objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code></a> module provides several useful functions to help get
information about live objects such as modules, classes, methods, functions,
tracebacks, frame objects, and code objects.  For example, it can help you
examine the contents of a class, retrieve the source code of a method, extract
and format the argument list for a function, or get all the information you need
to display a detailed traceback.</p>
<p>There are four main kinds of services provided by this module: type checking,
getting source code, inspecting classes and functions, and examining the
interpreter stack.</p>
<section id="types-and-members">
<span id="inspect-types"></span><h2>Types and members<a class="headerlink" href="#types-and-members" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#inspect.getmembers" title="inspect.getmembers"><code class="xref py py-func docutils literal notranslate"><span class="pre">getmembers()</span></code></a> function retrieves the members of an object such as a
class or module. The functions whose names begin with “is” are mainly
provided as convenient choices for the second argument to <a class="reference internal" href="#inspect.getmembers" title="inspect.getmembers"><code class="xref py py-func docutils literal notranslate"><span class="pre">getmembers()</span></code></a>.
They also help you determine when you can expect to find the following special
attributes (see <a class="reference internal" href="../reference/import.html#import-mod-attrs"><span class="std std-ref">Import-related module attributes</span></a> for module attributes):</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Type</p></th>
<th class="head"><p>Attribute</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>class</p></td>
<td><p>__doc__</p></td>
<td><p>documentation string</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__name__</p></td>
<td><p>name with which this
class was defined</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__qualname__</p></td>
<td><p>qualified name</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__module__</p></td>
<td><p>name of module in which
this class was defined</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__type_params__</p></td>
<td><p>A tuple containing the
<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameters</span></a> of
a generic class</p></td>
</tr>
<tr class="row-odd"><td><p>method</p></td>
<td><p>__doc__</p></td>
<td><p>documentation string</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__name__</p></td>
<td><p>name with which this
method was defined</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__qualname__</p></td>
<td><p>qualified name</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__func__</p></td>
<td><p>function object
containing implementation
of method</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__self__</p></td>
<td><p>instance to which this
method is bound, or
<code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__module__</p></td>
<td><p>name of module in which
this method was defined</p></td>
</tr>
<tr class="row-odd"><td><p>function</p></td>
<td><p>__doc__</p></td>
<td><p>documentation string</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__name__</p></td>
<td><p>name with which this
function was defined</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__qualname__</p></td>
<td><p>qualified name</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__code__</p></td>
<td><p>code object containing
compiled function
<a class="reference internal" href="../glossary.html#term-bytecode"><span class="xref std std-term">bytecode</span></a></p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__defaults__</p></td>
<td><p>tuple of any default
values for positional or
keyword parameters</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__kwdefaults__</p></td>
<td><p>mapping of any default
values for keyword-only
parameters</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__globals__</p></td>
<td><p>global namespace in which
this function was defined</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__builtins__</p></td>
<td><p>builtins namespace</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__annotations__</p></td>
<td><p>mapping of parameters
names to annotations;
<code class="docutils literal notranslate"><span class="pre">&quot;return&quot;</span></code> key is
reserved for return
annotations.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__type_params__</p></td>
<td><p>A tuple containing the
<a class="reference internal" href="../reference/compound_stmts.html#type-params"><span class="std std-ref">type parameters</span></a> of
a generic function</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__module__</p></td>
<td><p>name of module in which
this function was defined</p></td>
</tr>
<tr class="row-even"><td><p>traceback</p></td>
<td><p>tb_frame</p></td>
<td><p>frame object at this
level</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>tb_lasti</p></td>
<td><p>index of last attempted
instruction in bytecode</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>tb_lineno</p></td>
<td><p>current line number in
Python source code</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>tb_next</p></td>
<td><p>next inner traceback
object (called by this
level)</p></td>
</tr>
<tr class="row-even"><td><p>frame</p></td>
<td><p>f_back</p></td>
<td><p>next outer frame object
(this frame’s caller)</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>f_builtins</p></td>
<td><p>builtins namespace seen
by this frame</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>f_code</p></td>
<td><p>code object being
executed in this frame</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>f_globals</p></td>
<td><p>global namespace seen by
this frame</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>f_lasti</p></td>
<td><p>index of last attempted
instruction in bytecode</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>f_lineno</p></td>
<td><p>current line number in
Python source code</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>f_locals</p></td>
<td><p>local namespace seen by
this frame</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>f_trace</p></td>
<td><p>tracing function for this
frame, or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
<tr class="row-even"><td><p>code</p></td>
<td><p>co_argcount</p></td>
<td><p>number of arguments (not
including keyword only
arguments, * or **
args)</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_code</p></td>
<td><p>string of raw compiled
bytecode</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_cellvars</p></td>
<td><p>tuple of names of cell
variables (referenced by
containing scopes)</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_consts</p></td>
<td><p>tuple of constants used
in the bytecode</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_filename</p></td>
<td><p>name of file in which
this code object was
created</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_firstlineno</p></td>
<td><p>number of first line in
Python source code</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_flags</p></td>
<td><p>bitmap of <code class="docutils literal notranslate"><span class="pre">CO_*</span></code> flags,
read more <a class="reference internal" href="#inspect-module-co-flags"><span class="std std-ref">here</span></a></p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_lnotab</p></td>
<td><p>encoded mapping of line
numbers to bytecode
indices</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_freevars</p></td>
<td><p>tuple of names of free
variables (referenced via
a function’s closure)</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_posonlyargcount</p></td>
<td><p>number of positional only
arguments</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_kwonlyargcount</p></td>
<td><p>number of keyword only
arguments (not including
** arg)</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_name</p></td>
<td><p>name with which this code
object was defined</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_qualname</p></td>
<td><p>fully qualified name with
which this code object
was defined</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_names</p></td>
<td><p>tuple of names other
than arguments and
function locals</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_nlocals</p></td>
<td><p>number of local variables</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>co_stacksize</p></td>
<td><p>virtual machine stack
space required</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>co_varnames</p></td>
<td><p>tuple of names of
arguments and local
variables</p></td>
</tr>
<tr class="row-odd"><td><p>generator</p></td>
<td><p>__name__</p></td>
<td><p>name</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__qualname__</p></td>
<td><p>qualified name</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>gi_frame</p></td>
<td><p>frame</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>gi_running</p></td>
<td><p>is the generator running?</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>gi_code</p></td>
<td><p>code</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>gi_yieldfrom</p></td>
<td><p>object being iterated by
<code class="docutils literal notranslate"><span class="pre">yield</span> <span class="pre">from</span></code>, or
<code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>coroutine</p></td>
<td><p>__name__</p></td>
<td><p>name</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__qualname__</p></td>
<td><p>qualified name</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>cr_await</p></td>
<td><p>object being awaited on,
or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>cr_frame</p></td>
<td><p>frame</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>cr_running</p></td>
<td><p>is the coroutine running?</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>cr_code</p></td>
<td><p>code</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>cr_origin</p></td>
<td><p>where coroutine was
created, or <code class="docutils literal notranslate"><span class="pre">None</span></code>. See
<a class="reference internal" href="sys.html#sys.set_coroutine_origin_tracking_depth" title="sys.set_coroutine_origin_tracking_depth"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.set_coroutine_origin_tracking_depth()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p>builtin</p></td>
<td><p>__doc__</p></td>
<td><p>documentation string</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__name__</p></td>
<td><p>original name of this
function or method</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p>__qualname__</p></td>
<td><p>qualified name</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p>__self__</p></td>
<td><p>instance to which a
method is bound, or
<code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Add <code class="docutils literal notranslate"><span class="pre">__qualname__</span></code> and <code class="docutils literal notranslate"><span class="pre">gi_yieldfrom</span></code> attributes to generators.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">__name__</span></code> attribute of generators is now set from the function
name, instead of the code name, and it can now be modified.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Add <code class="docutils literal notranslate"><span class="pre">cr_origin</span></code> attribute to coroutines.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Add <code class="docutils literal notranslate"><span class="pre">__builtins__</span></code> attribute to functions.</p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getmembers">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getmembers</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">predicate</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getmembers" title="Link to this definition">¶</a></dt>
<dd><p>Return all the members of an object in a list of <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code>
pairs sorted by name. If the optional <em>predicate</em> argument—which will be
called with the <code class="docutils literal notranslate"><span class="pre">value</span></code> object of each member—is supplied, only members
for which the predicate returns a true value are included.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#inspect.getmembers" title="inspect.getmembers"><code class="xref py py-func docutils literal notranslate"><span class="pre">getmembers()</span></code></a> will only return class attributes defined in the
metaclass when the argument is a class and those attributes have been
listed in the metaclass’ custom <a class="reference internal" href="../reference/datamodel.html#object.__dir__" title="object.__dir__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__dir__()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getmembers_static">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getmembers_static</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">predicate</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getmembers_static" title="Link to this definition">¶</a></dt>
<dd><p>Return all the members of an object in a list of <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code>
pairs sorted by name without triggering dynamic lookup via the descriptor
protocol, __getattr__ or __getattribute__. Optionally, only return members
that satisfy a given predicate.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#inspect.getmembers_static" title="inspect.getmembers_static"><code class="xref py py-func docutils literal notranslate"><span class="pre">getmembers_static()</span></code></a> may not be able to retrieve all members
that getmembers can fetch (like dynamically created attributes)
and may find members that getmembers can’t (like descriptors
that raise AttributeError). It can also return descriptor objects
instead of instance members in some cases.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getmodulename">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getmodulename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getmodulename" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the module named by the file <em>path</em>, without including the
names of enclosing packages. The file extension is checked against all of
the entries in <a class="reference internal" href="importlib.html#importlib.machinery.all_suffixes" title="importlib.machinery.all_suffixes"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.machinery.all_suffixes()</span></code></a>. If it matches,
the final path component is returned with the extension removed.
Otherwise, <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
<p>Note that this function <em>only</em> returns a meaningful name for actual
Python modules - paths that potentially refer to Python packages will
still return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The function is based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.ismodule">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">ismodule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.ismodule" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a module.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isclass">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isclass</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isclass" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a class, whether built-in or created in Python
code.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.ismethod">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">ismethod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.ismethod" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a bound method written in Python.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isfunction">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isfunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isfunction" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a Python function, which includes functions
created by a <a class="reference internal" href="../glossary.html#term-lambda"><span class="xref std std-term">lambda</span></a> expression.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isgeneratorfunction">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isgeneratorfunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isgeneratorfunction" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a Python generator function.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Functions wrapped in <a class="reference internal" href="functools.html#functools.partial" title="functools.partial"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.partial()</span></code></a> now return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the
wrapped function is a Python generator function.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isgenerator">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isgenerator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isgenerator" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a generator.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.iscoroutinefunction">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">iscoroutinefunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.iscoroutinefunction" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a <a class="reference internal" href="../glossary.html#term-coroutine-function"><span class="xref std std-term">coroutine function</span></a> (a function
defined with an <a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> syntax), a <a class="reference internal" href="functools.html#functools.partial" title="functools.partial"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.partial()</span></code></a>
wrapping a <a class="reference internal" href="../glossary.html#term-coroutine-function"><span class="xref std std-term">coroutine function</span></a>, or a sync function marked with
<a class="reference internal" href="#inspect.markcoroutinefunction" title="inspect.markcoroutinefunction"><code class="xref py py-func docutils literal notranslate"><span class="pre">markcoroutinefunction()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Functions wrapped in <a class="reference internal" href="functools.html#functools.partial" title="functools.partial"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.partial()</span></code></a> now return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the
wrapped function is a <a class="reference internal" href="../glossary.html#term-coroutine-function"><span class="xref std std-term">coroutine function</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Sync functions marked with <a class="reference internal" href="#inspect.markcoroutinefunction" title="inspect.markcoroutinefunction"><code class="xref py py-func docutils literal notranslate"><span class="pre">markcoroutinefunction()</span></code></a> now return
<code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.markcoroutinefunction">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">markcoroutinefunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.markcoroutinefunction" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to mark a callable as a <a class="reference internal" href="../glossary.html#term-coroutine-function"><span class="xref std std-term">coroutine function</span></a> if it would not
otherwise be detected by <a class="reference internal" href="#inspect.iscoroutinefunction" title="inspect.iscoroutinefunction"><code class="xref py py-func docutils literal notranslate"><span class="pre">iscoroutinefunction()</span></code></a>.</p>
<p>This may be of use for sync functions that return a <a class="reference internal" href="../glossary.html#term-coroutine"><span class="xref std std-term">coroutine</span></a>, if
the function is passed to an API that requires <a class="reference internal" href="#inspect.iscoroutinefunction" title="inspect.iscoroutinefunction"><code class="xref py py-func docutils literal notranslate"><span class="pre">iscoroutinefunction()</span></code></a>.</p>
<p>When possible, using an <a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> function is preferred. Also
acceptable is calling the function and testing the return with
<a class="reference internal" href="#inspect.iscoroutine" title="inspect.iscoroutine"><code class="xref py py-func docutils literal notranslate"><span class="pre">iscoroutine()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.iscoroutine">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">iscoroutine</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.iscoroutine" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a <a class="reference internal" href="../glossary.html#term-coroutine"><span class="xref std std-term">coroutine</span></a> created by an
<a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> function.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isawaitable">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isawaitable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isawaitable" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object can be used in <a class="reference internal" href="../reference/expressions.html#await"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">await</span></code></a> expression.</p>
<p>Can also be used to distinguish generator-based coroutines from regular
generators:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">types</span>

<span class="k">def</span> <span class="nf">gen</span><span class="p">():</span>
    <span class="k">yield</span>
<span class="nd">@types</span><span class="o">.</span><span class="n">coroutine</span>
<span class="k">def</span> <span class="nf">gen_coro</span><span class="p">():</span>
    <span class="k">yield</span>

<span class="k">assert</span> <span class="ow">not</span> <span class="n">isawaitable</span><span class="p">(</span><span class="n">gen</span><span class="p">())</span>
<span class="k">assert</span> <span class="n">isawaitable</span><span class="p">(</span><span class="n">gen_coro</span><span class="p">())</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isasyncgenfunction">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isasyncgenfunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isasyncgenfunction" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is an <a class="reference internal" href="../glossary.html#term-asynchronous-generator"><span class="xref std std-term">asynchronous generator</span></a> function,
for example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">async</span> <span class="k">def</span> <span class="nf">agen</span><span class="p">():</span>
<span class="gp">... </span>    <span class="k">yield</span> <span class="mi">1</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">inspect</span><span class="o">.</span><span class="n">isasyncgenfunction</span><span class="p">(</span><span class="n">agen</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Functions wrapped in <a class="reference internal" href="functools.html#functools.partial" title="functools.partial"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.partial()</span></code></a> now return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the
wrapped function is a <a class="reference internal" href="../glossary.html#term-asynchronous-generator"><span class="xref std std-term">asynchronous generator</span></a> function.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isasyncgen">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isasyncgen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isasyncgen" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is an <a class="reference internal" href="../glossary.html#term-asynchronous-generator-iterator"><span class="xref std std-term">asynchronous generator iterator</span></a>
created by an <a class="reference internal" href="../glossary.html#term-asynchronous-generator"><span class="xref std std-term">asynchronous generator</span></a> function.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.istraceback">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">istraceback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.istraceback" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a traceback.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isframe">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isframe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isframe" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a frame.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.iscode">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">iscode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.iscode" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a code.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isbuiltin">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isbuiltin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isbuiltin" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a built-in function or a bound built-in method.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.ismethodwrapper">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">ismethodwrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.ismethodwrapper" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the type of object is a <a class="reference internal" href="types.html#types.MethodWrapperType" title="types.MethodWrapperType"><code class="xref py py-class docutils literal notranslate"><span class="pre">MethodWrapperType</span></code></a>.</p>
<p>These are instances of <a class="reference internal" href="types.html#types.MethodWrapperType" title="types.MethodWrapperType"><code class="xref py py-class docutils literal notranslate"><span class="pre">MethodWrapperType</span></code></a>, such as <a class="reference internal" href="../reference/datamodel.html#object.__str__" title="object.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code></a>,
<a class="reference internal" href="../reference/datamodel.html#object.__eq__" title="object.__eq__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__eq__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__repr__" title="object.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isroutine">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isroutine</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isroutine" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a user-defined or built-in function or method.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isabstract">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isabstract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isabstract" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is an abstract base class.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.ismethoddescriptor">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">ismethoddescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.ismethoddescriptor" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a method descriptor, but not if
<a class="reference internal" href="#inspect.ismethod" title="inspect.ismethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">ismethod()</span></code></a>, <a class="reference internal" href="#inspect.isclass" title="inspect.isclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">isclass()</span></code></a>, <a class="reference internal" href="#inspect.isfunction" title="inspect.isfunction"><code class="xref py py-func docutils literal notranslate"><span class="pre">isfunction()</span></code></a> or <a class="reference internal" href="#inspect.isbuiltin" title="inspect.isbuiltin"><code class="xref py py-func docutils literal notranslate"><span class="pre">isbuiltin()</span></code></a>
are true.</p>
<p>This, for example, is true of <code class="docutils literal notranslate"><span class="pre">int.__add__</span></code>.  An object passing this test
has a <a class="reference internal" href="../reference/datamodel.html#object.__get__" title="object.__get__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__get__()</span></code></a> method but not a <a class="reference internal" href="../reference/datamodel.html#object.__set__" title="object.__set__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__set__()</span></code></a>
method, but beyond that the set of attributes varies.  A
<a class="reference internal" href="stdtypes.html#definition.__name__" title="definition.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> attribute is usually
sensible, and <code class="xref py py-attr docutils literal notranslate"><span class="pre">__doc__</span></code> often is.</p>
<p>Methods implemented via descriptors that also pass one of the other tests
return <code class="docutils literal notranslate"><span class="pre">False</span></code> from the <a class="reference internal" href="#inspect.ismethoddescriptor" title="inspect.ismethoddescriptor"><code class="xref py py-func docutils literal notranslate"><span class="pre">ismethoddescriptor()</span></code></a> test, simply because the
other tests promise more – you can, e.g., count on having the
<a class="reference internal" href="../reference/datamodel.html#method.__func__" title="method.__func__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__func__</span></code></a> attribute (etc) when an object passes
<a class="reference internal" href="#inspect.ismethod" title="inspect.ismethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">ismethod()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isdatadescriptor">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isdatadescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isdatadescriptor" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a data descriptor.</p>
<p>Data descriptors have a <a class="reference internal" href="../reference/datamodel.html#object.__set__" title="object.__set__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__set__</span></code></a> or a <a class="reference internal" href="../reference/datamodel.html#object.__delete__" title="object.__delete__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__delete__</span></code></a> method.
Examples are properties (defined in Python), getsets, and members.  The
latter two are defined in C and there are more specific tests available for
those types, which is robust across Python implementations.  Typically, data
descriptors will also have <a class="reference internal" href="stdtypes.html#definition.__name__" title="definition.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">__doc__</span></code> attributes
(properties, getsets, and members have both of these attributes), but this is
not guaranteed.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.isgetsetdescriptor">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">isgetsetdescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.isgetsetdescriptor" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a getset descriptor.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> getsets are attributes defined in extension modules via
<a class="reference internal" href="../c-api/structures.html#c.PyGetSetDef" title="PyGetSetDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyGetSetDef</span></code></a> structures.  For Python implementations without such
types, this method will always return <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.ismemberdescriptor">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">ismemberdescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.ismemberdescriptor" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is a member descriptor.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> Member descriptors are attributes defined in extension modules via
<a class="reference internal" href="../c-api/structures.html#c.PyMemberDef" title="PyMemberDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMemberDef</span></code></a> structures.  For Python implementations without such
types, this method will always return <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</div>
</dd></dl>

</section>
<section id="retrieving-source-code">
<span id="inspect-source"></span><h2>Retrieving source code<a class="headerlink" href="#retrieving-source-code" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getdoc">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getdoc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getdoc" title="Link to this definition">¶</a></dt>
<dd><p>Get the documentation string for an object, cleaned up with <a class="reference internal" href="#inspect.cleandoc" title="inspect.cleandoc"><code class="xref py py-func docutils literal notranslate"><span class="pre">cleandoc()</span></code></a>.
If the documentation string for an object is not provided and the object is
a class, a method, a property or a descriptor, retrieve the documentation
string from the inheritance hierarchy.
Return <code class="docutils literal notranslate"><span class="pre">None</span></code> if the documentation string is invalid or missing.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Documentation strings are now inherited if not overridden.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getcomments">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getcomments</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getcomments" title="Link to this definition">¶</a></dt>
<dd><p>Return in a single string any lines of comments immediately preceding the
object’s source code (for a class, function, or method), or at the top of the
Python source file (if the object is a module).  If the object’s source code
is unavailable, return <code class="docutils literal notranslate"><span class="pre">None</span></code>.  This could happen if the object has been
defined in C or the interactive shell.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getfile">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getfile" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the (text or binary) file in which an object was defined.
This will fail with a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if the object is a built-in module,
class, or function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getmodule">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getmodule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getmodule" title="Link to this definition">¶</a></dt>
<dd><p>Try to guess which module an object was defined in. Return <code class="docutils literal notranslate"><span class="pre">None</span></code>
if the module cannot be determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getsourcefile">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getsourcefile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getsourcefile" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the Python source file in which an object was defined
or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no way can be identified to get the source.  This
will fail with a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if the object is a built-in module, class, or
function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getsourcelines">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getsourcelines</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getsourcelines" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of source lines and starting line number for an object. The
argument may be a module, class, method, function, traceback, frame, or code
object.  The source code is returned as a list of the lines corresponding to the
object and the line number indicates where in the original source file the first
line of code was found.  An <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised if the source code cannot
be retrieved.
A <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised if the object is a built-in module, class, or
function.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised instead of <a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a>, now an alias of the
former.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getsource">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getsource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getsource" title="Link to this definition">¶</a></dt>
<dd><p>Return the text of the source code for an object. The argument may be a module,
class, method, function, traceback, frame, or code object.  The source code is
returned as a single string.  An <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised if the source code
cannot be retrieved.
A <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised if the object is a built-in module, class, or
function.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised instead of <a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a>, now an alias of the
former.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.cleandoc">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">cleandoc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">doc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.cleandoc" title="Link to this definition">¶</a></dt>
<dd><p>Clean up indentation from docstrings that are indented to line up with blocks
of code.</p>
<p>All leading whitespace is removed from the first line.  Any leading whitespace
that can be uniformly removed from the second line onwards is removed.  Empty
lines at the beginning and end are subsequently removed.  Also, all tabs are
expanded to spaces.</p>
</dd></dl>

</section>
<section id="introspecting-callables-with-the-signature-object">
<span id="inspect-signature-object"></span><h2>Introspecting callables with the Signature object<a class="headerlink" href="#introspecting-callables-with-the-signature-object" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>The <a class="reference internal" href="#inspect.Signature" title="inspect.Signature"><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code></a> object represents the call signature of a callable object
and its return annotation. To retrieve a <code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code> object,
use the <code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code>
function.</p>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.signature">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">signature</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_wrapped</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">eval_str</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.signature" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#inspect.Signature" title="inspect.Signature"><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code></a> object for the given <em>callable</em>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">inspect</span> <span class="kn">import</span> <span class="n">signature</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">b</span><span class="p">:</span><span class="nb">int</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">pass</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">sig</span> <span class="o">=</span> <span class="n">signature</span><span class="p">(</span><span class="n">foo</span><span class="p">)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">sig</span><span class="p">)</span>
<span class="go">&#39;(a, *, b: int, **kwargs)&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">sig</span><span class="o">.</span><span class="n">parameters</span><span class="p">[</span><span class="s1">&#39;b&#39;</span><span class="p">])</span>
<span class="go">&#39;b: int&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">sig</span><span class="o">.</span><span class="n">parameters</span><span class="p">[</span><span class="s1">&#39;b&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">annotation</span>
<span class="go">&lt;class &#39;int&#39;&gt;</span>
</pre></div>
</div>
<p>Accepts a wide range of Python callables, from plain functions and classes to
<a class="reference internal" href="functools.html#functools.partial" title="functools.partial"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.partial()</span></code></a> objects.</p>
<p>For objects defined in modules using stringized annotations
(<code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">annotations</span></code>), <a class="reference internal" href="#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code></a> will
attempt to automatically un-stringize the annotations using
<a class="reference internal" href="#inspect.get_annotations" title="inspect.get_annotations"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_annotations()</span></code></a>.  The
<em>globals</em>, <em>locals</em>, and <em>eval_str</em> parameters are passed
into <a class="reference internal" href="#inspect.get_annotations" title="inspect.get_annotations"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_annotations()</span></code></a> when resolving the
annotations; see the documentation for <a class="reference internal" href="#inspect.get_annotations" title="inspect.get_annotations"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_annotations()</span></code></a>
for instructions on how to use these parameters.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if no signature can be provided, and
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if that type of object is not supported.  Also,
if the annotations are stringized, and <em>eval_str</em> is not false,
the <code class="docutils literal notranslate"><span class="pre">eval()</span></code> call(s) to un-stringize the annotations in <a class="reference internal" href="#inspect.get_annotations" title="inspect.get_annotations"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_annotations()</span></code></a>
could potentially raise any kind of exception.</p>
<p>A slash(/) in the signature of a function denotes that the parameters prior
to it are positional-only. For more info, see
<a class="reference internal" href="../faq/programming.html#faq-positional-only-arguments"><span class="std std-ref">the FAQ entry on positional-only parameters</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>follow_wrapped</em> parameter was added.
Pass <code class="docutils literal notranslate"><span class="pre">False</span></code> to get a signature of
<em>callable</em> specifically (<code class="docutils literal notranslate"><span class="pre">callable.__wrapped__</span></code> will not be used to
unwrap decorated callables.)</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>globals</em>, <em>locals</em>, and <em>eval_str</em> parameters were added.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Some callables may not be introspectable in certain implementations of
Python.  For example, in CPython, some built-in functions defined in
C provide no metadata about their arguments.</p>
</div>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> If the passed object has a <code class="xref py py-attr docutils literal notranslate"><span class="pre">__signature__</span></code> attribute,
we may use it to create the signature.
The exact semantics are an implementation detail and are subject to
unannounced changes. Consult the source code for current semantics.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="inspect.Signature">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">Signature</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parameters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_annotation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Signature.empty</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Signature" title="Link to this definition">¶</a></dt>
<dd><p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code> object represents the call signature of a function
and its return
annotation.  For each parameter accepted by the function it stores a
<a class="reference internal" href="#inspect.Parameter" title="inspect.Parameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code></a> object in its <a class="reference internal" href="#inspect.Signature.parameters" title="inspect.Signature.parameters"><code class="xref py py-attr docutils literal notranslate"><span class="pre">parameters</span></code></a> collection.</p>
<p>The optional <em>parameters</em> argument is a sequence of <a class="reference internal" href="#inspect.Parameter" title="inspect.Parameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code></a>
objects, which is validated to check that there are no parameters with
duplicate names, and that the parameters are in the right order, i.e.
positional-only first, then positional-or-keyword, and that parameters with
defaults follow parameters without defaults.</p>
<p>The optional <em>return_annotation</em> argument can be an arbitrary Python object.
It represents the “return” annotation of the callable.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code> objects are <em>immutable</em>.  Use <a class="reference internal" href="#inspect.Signature.replace" title="inspect.Signature.replace"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.replace()</span></code></a> to make a
modified copy.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code> objects are now picklable and <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Signature.empty">
<span class="sig-name descname"><span class="pre">empty</span></span><a class="headerlink" href="#inspect.Signature.empty" title="Link to this definition">¶</a></dt>
<dd><p>A special class-level marker to specify absence of a return annotation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Signature.parameters">
<span class="sig-name descname"><span class="pre">parameters</span></span><a class="headerlink" href="#inspect.Signature.parameters" title="Link to this definition">¶</a></dt>
<dd><p>An ordered mapping of parameters’ names to the corresponding
<a class="reference internal" href="#inspect.Parameter" title="inspect.Parameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code></a> objects.  Parameters appear in strict definition
order, including keyword-only parameters.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Python only explicitly guaranteed that it preserved the declaration
order of keyword-only parameters as of version 3.7, although in practice
this order had always been preserved in Python 3.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Signature.return_annotation">
<span class="sig-name descname"><span class="pre">return_annotation</span></span><a class="headerlink" href="#inspect.Signature.return_annotation" title="Link to this definition">¶</a></dt>
<dd><p>The “return” annotation for the callable.  If the callable has no “return”
annotation, this attribute is set to <a class="reference internal" href="#inspect.Signature.empty" title="inspect.Signature.empty"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Signature.empty</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="inspect.Signature.bind">
<span class="sig-name descname"><span class="pre">bind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Signature.bind" title="Link to this definition">¶</a></dt>
<dd><p>Create a mapping from positional and keyword arguments to parameters.
Returns <a class="reference internal" href="#inspect.BoundArguments" title="inspect.BoundArguments"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoundArguments</span></code></a> if <code class="docutils literal notranslate"><span class="pre">*args</span></code> and <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> match the
signature, or raises a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="inspect.Signature.bind_partial">
<span class="sig-name descname"><span class="pre">bind_partial</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Signature.bind_partial" title="Link to this definition">¶</a></dt>
<dd><p>Works the same way as <a class="reference internal" href="#inspect.Signature.bind" title="inspect.Signature.bind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind()</span></code></a>, but allows the omission of
some required arguments (mimics <a class="reference internal" href="functools.html#functools.partial" title="functools.partial"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.partial()</span></code></a> behavior.)
Returns <a class="reference internal" href="#inspect.BoundArguments" title="inspect.BoundArguments"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoundArguments</span></code></a>, or raises a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if the
passed arguments do not match the signature.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="inspect.Signature.replace">
<span class="sig-name descname"><span class="pre">replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">*[,</span> <span class="pre">parameters][,</span> <span class="pre">return_annotation]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Signature.replace" title="Link to this definition">¶</a></dt>
<dd><p>Create a new <a class="reference internal" href="#inspect.Signature" title="inspect.Signature"><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code></a> instance based on the instance
<a class="reference internal" href="#inspect.Signature.replace" title="inspect.Signature.replace"><code class="xref py py-meth docutils literal notranslate"><span class="pre">replace()</span></code></a> was invoked on.
It is possible to pass different <em>parameters</em> and/or
<em>return_annotation</em> to override the corresponding properties of the base
signature.  To remove <code class="docutils literal notranslate"><span class="pre">return_annotation</span></code> from the copied
<code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code>, pass in
<a class="reference internal" href="#inspect.Signature.empty" title="inspect.Signature.empty"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Signature.empty</span></code></a>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">test</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">pass</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sig</span> <span class="o">=</span> <span class="n">signature</span><span class="p">(</span><span class="n">test</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">new_sig</span> <span class="o">=</span> <span class="n">sig</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="n">return_annotation</span><span class="o">=</span><span class="s2">&quot;new return anno&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">new_sig</span><span class="p">)</span>
<span class="go">&quot;(a, b) -&gt; &#39;new return anno&#39;&quot;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="inspect.Signature.from_callable">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_callable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_wrapped</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">eval_str</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Signature.from_callable" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#inspect.Signature" title="inspect.Signature"><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code></a> (or its subclass) object for a given callable
<em>obj</em>.</p>
<p>This method simplifies subclassing of <a class="reference internal" href="#inspect.Signature" title="inspect.Signature"><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MySignature</span><span class="p">(</span><span class="n">Signature</span><span class="p">):</span>
    <span class="k">pass</span>
<span class="n">sig</span> <span class="o">=</span> <span class="n">MySignature</span><span class="o">.</span><span class="n">from_callable</span><span class="p">(</span><span class="nb">sum</span><span class="p">)</span>
<span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">sig</span><span class="p">,</span> <span class="n">MySignature</span><span class="p">)</span>
</pre></div>
</div>
<p>Its behavior is otherwise identical to that of <a class="reference internal" href="#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>globals</em>, <em>locals</em>, and <em>eval_str</em> parameters were added.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="inspect.Parameter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">Parameter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kind</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Parameter.empty</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">annotation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Parameter.empty</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Parameter" title="Link to this definition">¶</a></dt>
<dd><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code> objects are <em>immutable</em>.
Instead of modifying a <code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code> object,
you can use <a class="reference internal" href="#inspect.Parameter.replace" title="inspect.Parameter.replace"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Parameter.replace()</span></code></a> to create a modified copy.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Parameter objects are now picklable and <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Parameter.empty">
<span class="sig-name descname"><span class="pre">empty</span></span><a class="headerlink" href="#inspect.Parameter.empty" title="Link to this definition">¶</a></dt>
<dd><p>A special class-level marker to specify absence of default values and
annotations.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Parameter.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#inspect.Parameter.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the parameter as a string.  The name must be a valid
Python identifier.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> CPython generates implicit parameter names of the form <code class="docutils literal notranslate"><span class="pre">.0</span></code> on the
code objects used to implement comprehensions and generator
expressions.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>These parameter names are now exposed by this module as names like
<code class="docutils literal notranslate"><span class="pre">implicit0</span></code>.</p>
</div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Parameter.default">
<span class="sig-name descname"><span class="pre">default</span></span><a class="headerlink" href="#inspect.Parameter.default" title="Link to this definition">¶</a></dt>
<dd><p>The default value for the parameter.  If the parameter has no default
value, this attribute is set to <a class="reference internal" href="#inspect.Parameter.empty" title="inspect.Parameter.empty"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Parameter.empty</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Parameter.annotation">
<span class="sig-name descname"><span class="pre">annotation</span></span><a class="headerlink" href="#inspect.Parameter.annotation" title="Link to this definition">¶</a></dt>
<dd><p>The annotation for the parameter.  If the parameter has no annotation,
this attribute is set to <a class="reference internal" href="#inspect.Parameter.empty" title="inspect.Parameter.empty"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Parameter.empty</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Parameter.kind">
<span class="sig-name descname"><span class="pre">kind</span></span><a class="headerlink" href="#inspect.Parameter.kind" title="Link to this definition">¶</a></dt>
<dd><p>Describes how argument values are bound to the parameter.  The possible
values are accessible via <a class="reference internal" href="#inspect.Parameter" title="inspect.Parameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code></a> (like <code class="docutils literal notranslate"><span class="pre">Parameter.KEYWORD_ONLY</span></code>),
and support comparison and ordering, in the following order:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>POSITIONAL_ONLY</em></p></td>
<td><p>Value must be supplied as a positional
argument. Positional only parameters are
those which appear before a <code class="docutils literal notranslate"><span class="pre">/</span></code> entry (if
present) in a Python function definition.</p></td>
</tr>
<tr class="row-odd"><td><p><em>POSITIONAL_OR_KEYWORD</em></p></td>
<td><p>Value may be supplied as either a keyword or
positional argument (this is the standard
binding behaviour for functions implemented
in Python.)</p></td>
</tr>
<tr class="row-even"><td><p><em>VAR_POSITIONAL</em></p></td>
<td><p>A tuple of positional arguments that aren’t
bound to any other parameter. This
corresponds to a <code class="docutils literal notranslate"><span class="pre">*args</span></code> parameter in a
Python function definition.</p></td>
</tr>
<tr class="row-odd"><td><p><em>KEYWORD_ONLY</em></p></td>
<td><p>Value must be supplied as a keyword argument.
Keyword only parameters are those which
appear after a <code class="docutils literal notranslate"><span class="pre">*</span></code> or <code class="docutils literal notranslate"><span class="pre">*args</span></code> entry in a
Python function definition.</p></td>
</tr>
<tr class="row-even"><td><p><em>VAR_KEYWORD</em></p></td>
<td><p>A dict of keyword arguments that aren’t bound
to any other parameter. This corresponds to a
<code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> parameter in a Python function
definition.</p></td>
</tr>
</tbody>
</table>
<p>Example: print all keyword-only arguments without default values:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">d</span><span class="o">=</span><span class="mi">10</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">pass</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">sig</span> <span class="o">=</span> <span class="n">signature</span><span class="p">(</span><span class="n">foo</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">param</span> <span class="ow">in</span> <span class="n">sig</span><span class="o">.</span><span class="n">parameters</span><span class="o">.</span><span class="n">values</span><span class="p">():</span>
<span class="gp">... </span>    <span class="k">if</span> <span class="p">(</span><span class="n">param</span><span class="o">.</span><span class="n">kind</span> <span class="o">==</span> <span class="n">param</span><span class="o">.</span><span class="n">KEYWORD_ONLY</span> <span class="ow">and</span>
<span class="gp">... </span>                       <span class="n">param</span><span class="o">.</span><span class="n">default</span> <span class="ow">is</span> <span class="n">param</span><span class="o">.</span><span class="n">empty</span><span class="p">):</span>
<span class="gp">... </span>        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Parameter:&#39;</span><span class="p">,</span> <span class="n">param</span><span class="p">)</span>
<span class="go">Parameter: c</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Parameter.kind.description">
<span class="sig-prename descclassname"><span class="pre">kind.</span></span><span class="sig-name descname"><span class="pre">description</span></span><a class="headerlink" href="#inspect.Parameter.kind.description" title="Link to this definition">¶</a></dt>
<dd><p>Describes a enum value of <a class="reference internal" href="#inspect.Parameter.kind" title="inspect.Parameter.kind"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Parameter.kind</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Example: print all descriptions of arguments:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">d</span><span class="o">=</span><span class="mi">10</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">pass</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">sig</span> <span class="o">=</span> <span class="n">signature</span><span class="p">(</span><span class="n">foo</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">param</span> <span class="ow">in</span> <span class="n">sig</span><span class="o">.</span><span class="n">parameters</span><span class="o">.</span><span class="n">values</span><span class="p">():</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">param</span><span class="o">.</span><span class="n">kind</span><span class="o">.</span><span class="n">description</span><span class="p">)</span>
<span class="go">positional or keyword</span>
<span class="go">positional or keyword</span>
<span class="go">keyword-only</span>
<span class="go">keyword-only</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="inspect.Parameter.replace">
<span class="sig-name descname"><span class="pre">replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">*[,</span> <span class="pre">name][,</span> <span class="pre">kind][,</span> <span class="pre">default][,</span> <span class="pre">annotation]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.Parameter.replace" title="Link to this definition">¶</a></dt>
<dd><p>Create a new <a class="reference internal" href="#inspect.Parameter" title="inspect.Parameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code></a> instance based on the instance replaced was invoked
on.  To override a <code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code> attribute, pass the corresponding
argument.  To remove a default value or/and an annotation from a
<code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code>, pass <a class="reference internal" href="#inspect.Parameter.empty" title="inspect.Parameter.empty"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Parameter.empty</span></code></a>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">inspect</span> <span class="kn">import</span> <span class="n">Parameter</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">param</span> <span class="o">=</span> <span class="n">Parameter</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">,</span> <span class="n">Parameter</span><span class="o">.</span><span class="n">KEYWORD_ONLY</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="mi">42</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">param</span><span class="p">)</span>
<span class="go">&#39;foo=42&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">param</span><span class="o">.</span><span class="n">replace</span><span class="p">())</span> <span class="c1"># Will create a shallow copy of &#39;param&#39;</span>
<span class="go">&#39;foo=42&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">param</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="n">default</span><span class="o">=</span><span class="n">Parameter</span><span class="o">.</span><span class="n">empty</span><span class="p">,</span> <span class="n">annotation</span><span class="o">=</span><span class="s1">&#39;spam&#39;</span><span class="p">))</span>
<span class="go">&quot;foo: &#39;spam&#39;&quot;</span>
</pre></div>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>In Python 3.3 <a class="reference internal" href="#inspect.Parameter" title="inspect.Parameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parameter</span></code></a> objects were allowed to have <code class="docutils literal notranslate"><span class="pre">name</span></code> set
to <code class="docutils literal notranslate"><span class="pre">None</span></code> if their <code class="docutils literal notranslate"><span class="pre">kind</span></code> was set to <code class="docutils literal notranslate"><span class="pre">POSITIONAL_ONLY</span></code>.
This is no longer permitted.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="inspect.BoundArguments">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">BoundArguments</span></span><a class="headerlink" href="#inspect.BoundArguments" title="Link to this definition">¶</a></dt>
<dd><p>Result of a <a class="reference internal" href="#inspect.Signature.bind" title="inspect.Signature.bind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind()</span></code></a> or <a class="reference internal" href="#inspect.Signature.bind_partial" title="inspect.Signature.bind_partial"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind_partial()</span></code></a> call.
Holds the mapping of arguments to the function’s parameters.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BoundArguments.arguments">
<span class="sig-name descname"><span class="pre">arguments</span></span><a class="headerlink" href="#inspect.BoundArguments.arguments" title="Link to this definition">¶</a></dt>
<dd><p>A mutable mapping of parameters’ names to arguments’ values.
Contains only explicitly bound arguments.  Changes in <a class="reference internal" href="#inspect.BoundArguments.arguments" title="inspect.BoundArguments.arguments"><code class="xref py py-attr docutils literal notranslate"><span class="pre">arguments</span></code></a>
will reflect in <a class="reference internal" href="#inspect.BoundArguments.args" title="inspect.BoundArguments.args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">args</span></code></a> and <a class="reference internal" href="#inspect.BoundArguments.kwargs" title="inspect.BoundArguments.kwargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">kwargs</span></code></a>.</p>
<p>Should be used in conjunction with <a class="reference internal" href="#inspect.Signature.parameters" title="inspect.Signature.parameters"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Signature.parameters</span></code></a> for any
argument processing purposes.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Arguments for which <a class="reference internal" href="#inspect.Signature.bind" title="inspect.Signature.bind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind()</span></code></a> or
<a class="reference internal" href="#inspect.Signature.bind_partial" title="inspect.Signature.bind_partial"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind_partial()</span></code></a> relied on a default value are skipped.
However, if needed, use <a class="reference internal" href="#inspect.BoundArguments.apply_defaults" title="inspect.BoundArguments.apply_defaults"><code class="xref py py-meth docutils literal notranslate"><span class="pre">BoundArguments.apply_defaults()</span></code></a> to add
them.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span><a class="reference internal" href="#inspect.BoundArguments.arguments" title="inspect.BoundArguments.arguments"><code class="xref py py-attr docutils literal notranslate"><span class="pre">arguments</span></code></a> is now of type <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>. Formerly, it was of
type <a class="reference internal" href="collections.html#collections.OrderedDict" title="collections.OrderedDict"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.OrderedDict</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BoundArguments.args">
<span class="sig-name descname"><span class="pre">args</span></span><a class="headerlink" href="#inspect.BoundArguments.args" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of positional arguments values.  Dynamically computed from the
<a class="reference internal" href="#inspect.BoundArguments.arguments" title="inspect.BoundArguments.arguments"><code class="xref py py-attr docutils literal notranslate"><span class="pre">arguments</span></code></a> attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BoundArguments.kwargs">
<span class="sig-name descname"><span class="pre">kwargs</span></span><a class="headerlink" href="#inspect.BoundArguments.kwargs" title="Link to this definition">¶</a></dt>
<dd><p>A dict of keyword arguments values.  Dynamically computed from the
<a class="reference internal" href="#inspect.BoundArguments.arguments" title="inspect.BoundArguments.arguments"><code class="xref py py-attr docutils literal notranslate"><span class="pre">arguments</span></code></a> attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BoundArguments.signature">
<span class="sig-name descname"><span class="pre">signature</span></span><a class="headerlink" href="#inspect.BoundArguments.signature" title="Link to this definition">¶</a></dt>
<dd><p>A reference to the parent <a class="reference internal" href="#inspect.Signature" title="inspect.Signature"><code class="xref py py-class docutils literal notranslate"><span class="pre">Signature</span></code></a> object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="inspect.BoundArguments.apply_defaults">
<span class="sig-name descname"><span class="pre">apply_defaults</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#inspect.BoundArguments.apply_defaults" title="Link to this definition">¶</a></dt>
<dd><p>Set default values for missing arguments.</p>
<p>For variable-positional arguments (<code class="docutils literal notranslate"><span class="pre">*args</span></code>) the default is an
empty tuple.</p>
<p>For variable-keyword arguments (<code class="docutils literal notranslate"><span class="pre">**kwargs</span></code>) the default is an
empty dict.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="o">=</span><span class="s1">&#39;ham&#39;</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">):</span> <span class="k">pass</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ba</span> <span class="o">=</span> <span class="n">inspect</span><span class="o">.</span><span class="n">signature</span><span class="p">(</span><span class="n">foo</span><span class="p">)</span><span class="o">.</span><span class="n">bind</span><span class="p">(</span><span class="s1">&#39;spam&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ba</span><span class="o">.</span><span class="n">apply_defaults</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ba</span><span class="o">.</span><span class="n">arguments</span>
<span class="go">{&#39;a&#39;: &#39;spam&#39;, &#39;b&#39;: &#39;ham&#39;, &#39;args&#39;: ()}</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#inspect.BoundArguments.args" title="inspect.BoundArguments.args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">args</span></code></a> and <a class="reference internal" href="#inspect.BoundArguments.kwargs" title="inspect.BoundArguments.kwargs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">kwargs</span></code></a> properties can be used to invoke
functions:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
    <span class="o">...</span>

<span class="n">sig</span> <span class="o">=</span> <span class="n">signature</span><span class="p">(</span><span class="n">test</span><span class="p">)</span>
<span class="n">ba</span> <span class="o">=</span> <span class="n">sig</span><span class="o">.</span><span class="n">bind</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="n">b</span><span class="o">=</span><span class="mi">20</span><span class="p">)</span>
<span class="n">test</span><span class="p">(</span><span class="o">*</span><span class="n">ba</span><span class="o">.</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">ba</span><span class="o">.</span><span class="n">kwargs</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0362/"><strong>PEP 362</strong></a> - Function Signature Object.</dt><dd><p>The detailed specification, implementation details and examples.</p>
</dd>
</dl>
</div>
</section>
<section id="classes-and-functions">
<span id="inspect-classes-functions"></span><h2>Classes and functions<a class="headerlink" href="#classes-and-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getclasstree">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getclasstree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">classes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getclasstree" title="Link to this definition">¶</a></dt>
<dd><p>Arrange the given list of classes into a hierarchy of nested lists. Where a
nested list appears, it contains classes derived from the class whose entry
immediately precedes the list.  Each entry is a 2-tuple containing a class and a
tuple of its base classes.  If the <em>unique</em> argument is true, exactly one entry
appears in the returned structure for each class in the given list.  Otherwise,
classes using multiple inheritance and their descendants will appear multiple
times.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getfullargspec">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getfullargspec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getfullargspec" title="Link to this definition">¶</a></dt>
<dd><p>Get the names and default values of a Python function’s parameters.  A
<a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> is returned:</p>
<p><code class="docutils literal notranslate"><span class="pre">FullArgSpec(args,</span> <span class="pre">varargs,</span> <span class="pre">varkw,</span> <span class="pre">defaults,</span> <span class="pre">kwonlyargs,</span> <span class="pre">kwonlydefaults,</span>
<span class="pre">annotations)</span></code></p>
<p><em>args</em> is a list of the positional parameter names.
<em>varargs</em> is the name of the <code class="docutils literal notranslate"><span class="pre">*</span></code> parameter or <code class="docutils literal notranslate"><span class="pre">None</span></code> if arbitrary
positional arguments are not accepted.
<em>varkw</em> is the name of the <code class="docutils literal notranslate"><span class="pre">**</span></code> parameter or <code class="docutils literal notranslate"><span class="pre">None</span></code> if arbitrary
keyword arguments are not accepted.
<em>defaults</em> is an <em>n</em>-tuple of default argument values corresponding to the
last <em>n</em> positional parameters, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if there are no such defaults
defined.
<em>kwonlyargs</em> is a list of keyword-only parameter names in declaration order.
<em>kwonlydefaults</em> is a dictionary mapping parameter names from <em>kwonlyargs</em>
to the default values used if no argument is supplied.
<em>annotations</em> is a dictionary mapping parameter names to annotations.
The special key <code class="docutils literal notranslate"><span class="pre">&quot;return&quot;</span></code> is used to report the function return value
annotation (if any).</p>
<p>Note that <a class="reference internal" href="#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code></a> and
<a class="reference internal" href="#inspect-signature-object"><span class="std std-ref">Signature Object</span></a> provide the recommended
API for callable introspection, and support additional behaviours (like
positional-only arguments) that are sometimes encountered in extension module
APIs. This function is retained primarily for use in code that needs to
maintain compatibility with the Python 2 <code class="docutils literal notranslate"><span class="pre">inspect</span></code> module API.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>This function is now based on <a class="reference internal" href="#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code></a>, but still ignores
<code class="docutils literal notranslate"><span class="pre">__wrapped__</span></code> attributes and includes the already bound first
parameter in the signature output for bound methods.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>This method was previously documented as deprecated in favour of
<a class="reference internal" href="#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code></a> in Python 3.5, but that decision has been reversed
in order to restore a clearly supported standard interface for
single-source Python 2/3 code migrating away from the legacy
<code class="xref py py-func docutils literal notranslate"><span class="pre">getargspec()</span></code> API.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Python only explicitly guaranteed that it preserved the declaration
order of keyword-only parameters as of version 3.7, although in practice
this order had always been preserved in Python 3.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getargvalues">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getargvalues</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getargvalues" title="Link to this definition">¶</a></dt>
<dd><p>Get information about arguments passed into a particular frame.  A
<a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> <code class="docutils literal notranslate"><span class="pre">ArgInfo(args,</span> <span class="pre">varargs,</span> <span class="pre">keywords,</span> <span class="pre">locals)</span></code> is
returned. <em>args</em> is a list of the argument names.  <em>varargs</em> and <em>keywords</em>
are the names of the <code class="docutils literal notranslate"><span class="pre">*</span></code> and <code class="docutils literal notranslate"><span class="pre">**</span></code> arguments or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  <em>locals</em> is the
locals dictionary of the given frame.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function was inadvertently marked as deprecated in Python 3.5.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.formatargvalues">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">formatargvalues</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">varargs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">varkw</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">formatarg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">formatvarargs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">formatvarkw</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">formatvalue</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#inspect.formatargvalues" title="Link to this definition">¶</a></dt>
<dd><p>Format a pretty argument spec from the four values returned by
<a class="reference internal" href="#inspect.getargvalues" title="inspect.getargvalues"><code class="xref py py-func docutils literal notranslate"><span class="pre">getargvalues()</span></code></a>.  The format* arguments are the corresponding optional
formatting functions that are called to turn names and values into strings.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function was inadvertently marked as deprecated in Python 3.5.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getmro">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getmro</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getmro" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple of class cls’s base classes, including cls, in method resolution
order.  No class appears more than once in this tuple. Note that the method
resolution order depends on cls’s type.  Unless a very peculiar user-defined
metatype is in use, cls will be the first element of the tuple.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getcallargs">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getcallargs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getcallargs" title="Link to this definition">¶</a></dt>
<dd><p>Bind the <em>args</em> and <em>kwds</em> to the argument names of the Python function or
method <em>func</em>, as if it was called with them. For bound methods, bind also the
first argument (typically named <code class="docutils literal notranslate"><span class="pre">self</span></code>) to the associated instance. A dict
is returned, mapping the argument names (including the names of the <code class="docutils literal notranslate"><span class="pre">*</span></code> and
<code class="docutils literal notranslate"><span class="pre">**</span></code> arguments, if any) to their values from <em>args</em> and <em>kwds</em>. In case of
invoking <em>func</em> incorrectly, i.e. whenever <code class="docutils literal notranslate"><span class="pre">func(*args,</span> <span class="pre">**kwds)</span></code> would raise
an exception because of incompatible signature, an exception of the same type
and the same or similar message is raised. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">inspect</span> <span class="kn">import</span> <span class="n">getcallargs</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">f</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="o">*</span><span class="n">pos</span><span class="p">,</span> <span class="o">**</span><span class="n">named</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">pass</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">getcallargs</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span> <span class="o">==</span> <span class="p">{</span><span class="s1">&#39;a&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;named&#39;</span><span class="p">:</span> <span class="p">{},</span> <span class="s1">&#39;b&#39;</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;pos&#39;</span><span class="p">:</span> <span class="p">(</span><span class="mi">3</span><span class="p">,)}</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">getcallargs</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="n">a</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">x</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span> <span class="o">==</span> <span class="p">{</span><span class="s1">&#39;a&#39;</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;named&#39;</span><span class="p">:</span> <span class="p">{</span><span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="mi">4</span><span class="p">},</span> <span class="s1">&#39;b&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;pos&#39;</span><span class="p">:</span> <span class="p">()}</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">getcallargs</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">f() missing 1 required positional argument: &#39;a&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.5: </span>Use <a class="reference internal" href="#inspect.Signature.bind" title="inspect.Signature.bind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind()</span></code></a> and <a class="reference internal" href="#inspect.Signature.bind_partial" title="inspect.Signature.bind_partial"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Signature.bind_partial()</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getclosurevars">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getclosurevars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getclosurevars" title="Link to this definition">¶</a></dt>
<dd><p>Get the mapping of external name references in a Python function or
method <em>func</em> to their current values. A
<a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> <code class="docutils literal notranslate"><span class="pre">ClosureVars(nonlocals,</span> <span class="pre">globals,</span> <span class="pre">builtins,</span> <span class="pre">unbound)</span></code>
is returned. <em>nonlocals</em> maps referenced names to lexical closure
variables, <em>globals</em> to the function’s module globals and <em>builtins</em> to
the builtins visible from the function body. <em>unbound</em> is the set of names
referenced in the function that could not be resolved at all given the
current module globals and builtins.</p>
<p><a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised if <em>func</em> is not a Python function or method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.unwrap">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">unwrap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.unwrap" title="Link to this definition">¶</a></dt>
<dd><p>Get the object wrapped by <em>func</em>. It follows the chain of <code class="xref py py-attr docutils literal notranslate"><span class="pre">__wrapped__</span></code>
attributes returning the last object in the chain.</p>
<p><em>stop</em> is an optional callback accepting an object in the wrapper chain
as its sole argument that allows the unwrapping to be terminated early if
the callback returns a true value. If the callback never returns a true
value, the last object in the chain is returned as usual. For example,
<a class="reference internal" href="#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">signature()</span></code></a> uses this to stop unwrapping if any object in the
chain has a <code class="docutils literal notranslate"><span class="pre">__signature__</span></code> attribute defined.</p>
<p><a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if a cycle is encountered.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.get_annotations">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">get_annotations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">eval_str</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.get_annotations" title="Link to this definition">¶</a></dt>
<dd><p>Compute the annotations dict for an object.</p>
<p><code class="docutils literal notranslate"><span class="pre">obj</span></code> may be a callable, class, or module.
Passing in an object of any other type raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.</p>
<p>Returns a dict.  <code class="docutils literal notranslate"><span class="pre">get_annotations()</span></code> returns a new dict every time
it’s called; calling it twice on the same object will return two
different but equivalent dicts.</p>
<p>This function handles several details for you:</p>
<ul class="simple">
<li><p>If <code class="docutils literal notranslate"><span class="pre">eval_str</span></code> is true, values of type <code class="docutils literal notranslate"><span class="pre">str</span></code> will
be un-stringized using <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>.  This is intended
for use with stringized annotations
(<code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">annotations</span></code>).</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">obj</span></code> doesn’t have an annotations dict, returns an
empty dict.  (Functions and methods always have an
annotations dict; classes, modules, and other types of
callables may not.)</p></li>
<li><p>Ignores inherited annotations on classes.  If a class
doesn’t have its own annotations dict, returns an empty dict.</p></li>
<li><p>All accesses to object members and dict values are done
using <code class="docutils literal notranslate"><span class="pre">getattr()</span></code> and <code class="docutils literal notranslate"><span class="pre">dict.get()</span></code> for safety.</p></li>
<li><p>Always, always, always returns a freshly created dict.</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">eval_str</span></code> controls whether or not values of type <code class="docutils literal notranslate"><span class="pre">str</span></code> are replaced
with the result of calling <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> on those values:</p>
<ul class="simple">
<li><p>If eval_str is true, <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> is called on values of type <code class="docutils literal notranslate"><span class="pre">str</span></code>.
(Note that <code class="docutils literal notranslate"><span class="pre">get_annotations</span></code> doesn’t catch exceptions; if <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>
raises an exception, it will unwind the stack past the <code class="docutils literal notranslate"><span class="pre">get_annotations</span></code>
call.)</p></li>
<li><p>If eval_str is false (the default), values of type <code class="docutils literal notranslate"><span class="pre">str</span></code> are unchanged.</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">globals</span></code> and <code class="docutils literal notranslate"><span class="pre">locals</span></code> are passed in to <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>; see the documentation
for <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> for more information.  If <code class="docutils literal notranslate"><span class="pre">globals</span></code> or <code class="docutils literal notranslate"><span class="pre">locals</span></code>
is <code class="docutils literal notranslate"><span class="pre">None</span></code>, this function may replace that value with a context-specific
default, contingent on <code class="docutils literal notranslate"><span class="pre">type(obj)</span></code>:</p>
<ul class="simple">
<li><p>If <code class="docutils literal notranslate"><span class="pre">obj</span></code> is a module, <code class="docutils literal notranslate"><span class="pre">globals</span></code> defaults to <code class="docutils literal notranslate"><span class="pre">obj.__dict__</span></code>.</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">obj</span></code> is a class, <code class="docutils literal notranslate"><span class="pre">globals</span></code> defaults to
<code class="docutils literal notranslate"><span class="pre">sys.modules[obj.__module__].__dict__</span></code> and <code class="docutils literal notranslate"><span class="pre">locals</span></code> defaults
to the <code class="docutils literal notranslate"><span class="pre">obj</span></code> class namespace.</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">obj</span></code> is a callable, <code class="docutils literal notranslate"><span class="pre">globals</span></code> defaults to
<a class="reference internal" href="../reference/datamodel.html#function.__globals__" title="function.__globals__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">obj.__globals__</span></code></a>,
although if <code class="docutils literal notranslate"><span class="pre">obj</span></code> is a wrapped function (using
<a class="reference internal" href="functools.html#functools.update_wrapper" title="functools.update_wrapper"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.update_wrapper()</span></code></a>) it is first unwrapped.</p></li>
</ul>
<p>Calling <code class="docutils literal notranslate"><span class="pre">get_annotations</span></code> is best practice for accessing the
annotations dict of any object.  See <a class="reference internal" href="../howto/annotations.html#annotations-howto"><span class="std std-ref">Annotations Best Practices</span></a> for
more information on annotations best practices.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

</section>
<section id="the-interpreter-stack">
<span id="inspect-stack"></span><h2>The interpreter stack<a class="headerlink" href="#the-interpreter-stack" title="Link to this heading">¶</a></h2>
<p>Some of the following functions return
<a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects. For backwards compatibility these objects allow
tuple-like operations on all attributes except <code class="docutils literal notranslate"><span class="pre">positions</span></code>. This behavior
is considered deprecated and may be removed in the future.</p>
<dl class="py class">
<dt class="sig sig-object py" id="inspect.FrameInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">FrameInfo</span></span><a class="headerlink" href="#inspect.FrameInfo" title="Link to this definition">¶</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.frame">
<span class="sig-name descname"><span class="pre">frame</span></span><a class="headerlink" href="#inspect.FrameInfo.frame" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame object</span></a> that the record corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#inspect.FrameInfo.filename" title="Link to this definition">¶</a></dt>
<dd><p>The file name associated with the code being executed by the frame this record
corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#inspect.FrameInfo.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line number of the current line associated with the code being
executed by the frame this record corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.function">
<span class="sig-name descname"><span class="pre">function</span></span><a class="headerlink" href="#inspect.FrameInfo.function" title="Link to this definition">¶</a></dt>
<dd><p>The function name that is being executed by the frame this record corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.code_context">
<span class="sig-name descname"><span class="pre">code_context</span></span><a class="headerlink" href="#inspect.FrameInfo.code_context" title="Link to this definition">¶</a></dt>
<dd><p>A list of lines of context from the source code that’s being executed by the frame
this record corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.index">
<span class="sig-name descname"><span class="pre">index</span></span><a class="headerlink" href="#inspect.FrameInfo.index" title="Link to this definition">¶</a></dt>
<dd><p>The index of the current line being executed in the <a class="reference internal" href="#inspect.FrameInfo.code_context" title="inspect.FrameInfo.code_context"><code class="xref py py-attr docutils literal notranslate"><span class="pre">code_context</span></code></a> list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.FrameInfo.positions">
<span class="sig-name descname"><span class="pre">positions</span></span><a class="headerlink" href="#inspect.FrameInfo.positions" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="dis.html#dis.Positions" title="dis.Positions"><code class="xref py py-class docutils literal notranslate"><span class="pre">dis.Positions</span></code></a> object containing the start line number, end line
number, start column offset, and end column offset associated with the
instruction being executed by the frame this record corresponds to.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Return a <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> instead of a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code> is now a class instance
(that is backwards compatible with the previous <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a>).</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="inspect.Traceback">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">Traceback</span></span><a class="headerlink" href="#inspect.Traceback" title="Link to this definition">¶</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Traceback.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#inspect.Traceback.filename" title="Link to this definition">¶</a></dt>
<dd><p>The file name associated with the code being executed by the frame this traceback
corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Traceback.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#inspect.Traceback.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line number of the current line associated with the code being
executed by the frame this traceback corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Traceback.function">
<span class="sig-name descname"><span class="pre">function</span></span><a class="headerlink" href="#inspect.Traceback.function" title="Link to this definition">¶</a></dt>
<dd><p>The function name that is being executed by the frame this traceback corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Traceback.code_context">
<span class="sig-name descname"><span class="pre">code_context</span></span><a class="headerlink" href="#inspect.Traceback.code_context" title="Link to this definition">¶</a></dt>
<dd><p>A list of lines of context from the source code that’s being executed by the frame
this traceback corresponds to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Traceback.index">
<span class="sig-name descname"><span class="pre">index</span></span><a class="headerlink" href="#inspect.Traceback.index" title="Link to this definition">¶</a></dt>
<dd><p>The index of the current line being executed in the <a class="reference internal" href="#inspect.Traceback.code_context" title="inspect.Traceback.code_context"><code class="xref py py-attr docutils literal notranslate"><span class="pre">code_context</span></code></a> list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.Traceback.positions">
<span class="sig-name descname"><span class="pre">positions</span></span><a class="headerlink" href="#inspect.Traceback.positions" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="dis.html#dis.Positions" title="dis.Positions"><code class="xref py py-class docutils literal notranslate"><span class="pre">dis.Positions</span></code></a> object containing the start line number, end
line number, start column offset, and end column offset associated with
the instruction being executed by the frame this traceback corresponds
to.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code> is now a class instance
(that is backwards compatible with the previous <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a>).</p>
</div>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Keeping references to frame objects, as found in the first element of the frame
records these functions return, can cause your program to create reference
cycles.  Once a reference cycle has been created, the lifespan of all objects
which can be accessed from the objects which form the cycle can become much
longer even if Python’s optional cycle detector is enabled.  If such cycles must
be created, it is important to ensure they are explicitly broken to avoid the
delayed destruction of objects and increased memory consumption which occurs.</p>
<p>Though the cycle detector will catch these, destruction of the frames (and local
variables) can be made deterministic by removing the cycle in a
<a class="reference internal" href="../reference/compound_stmts.html#finally"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">finally</span></code></a> clause.  This is also important if the cycle detector was
disabled when Python was compiled or using <a class="reference internal" href="gc.html#gc.disable" title="gc.disable"><code class="xref py py-func docutils literal notranslate"><span class="pre">gc.disable()</span></code></a>.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">handle_stackframe_without_leak</span><span class="p">():</span>
    <span class="n">frame</span> <span class="o">=</span> <span class="n">inspect</span><span class="o">.</span><span class="n">currentframe</span><span class="p">()</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># do something with the frame</span>
    <span class="k">finally</span><span class="p">:</span>
        <span class="k">del</span> <span class="n">frame</span>
</pre></div>
</div>
<p>If you want to keep the frame around (for example to print a traceback
later), you can also break reference cycles by using the
<a class="reference internal" href="../reference/datamodel.html#frame.clear" title="frame.clear"><code class="xref py py-meth docutils literal notranslate"><span class="pre">frame.clear()</span></code></a> method.</p>
</div>
<p>The optional <em>context</em> argument supported by most of these functions specifies
the number of lines of context to return, which are centered around the current
line.</p>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getframeinfo">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getframeinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getframeinfo" title="Link to this definition">¶</a></dt>
<dd><p>Get information about a frame or traceback object.  A <a class="reference internal" href="#inspect.Traceback" title="inspect.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a> object
is returned.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>A <a class="reference internal" href="#inspect.Traceback" title="inspect.Traceback"><code class="xref py py-class docutils literal notranslate"><span class="pre">Traceback</span></code></a> object is returned instead of a named tuple.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getouterframes">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getouterframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getouterframes" title="Link to this definition">¶</a></dt>
<dd><p>Get a list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects for a frame and all outer frames.
These frames represent the calls that lead to the creation of <em>frame</em>. The
first entry in the returned list represents <em>frame</em>; the last entry
represents the outermost call on <em>frame</em>’s stack.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>A list of <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuples</span></a>
<code class="docutils literal notranslate"><span class="pre">FrameInfo(frame,</span> <span class="pre">filename,</span> <span class="pre">lineno,</span> <span class="pre">function,</span> <span class="pre">code_context,</span> <span class="pre">index)</span></code>
is returned.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>A list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects is returned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getinnerframes">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getinnerframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">traceback</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getinnerframes" title="Link to this definition">¶</a></dt>
<dd><p>Get a list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects for a traceback’s frame and all
inner frames.  These frames represent calls made as a consequence of <em>frame</em>.
The first entry in the list represents <em>traceback</em>; the last entry represents
where the exception was raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>A list of <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuples</span></a>
<code class="docutils literal notranslate"><span class="pre">FrameInfo(frame,</span> <span class="pre">filename,</span> <span class="pre">lineno,</span> <span class="pre">function,</span> <span class="pre">code_context,</span> <span class="pre">index)</span></code>
is returned.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>A list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects is returned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.currentframe">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">currentframe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#inspect.currentframe" title="Link to this definition">¶</a></dt>
<dd><p>Return the frame object for the caller’s stack frame.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This function relies on Python stack frame support in the interpreter,
which isn’t guaranteed to exist in all implementations of Python.  If
running in an implementation without Python stack frame support this
function returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.stack">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">stack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.stack" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects for the caller’s stack.  The
first entry in the returned list represents the caller; the last entry
represents the outermost call on the stack.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>A list of <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuples</span></a>
<code class="docutils literal notranslate"><span class="pre">FrameInfo(frame,</span> <span class="pre">filename,</span> <span class="pre">lineno,</span> <span class="pre">function,</span> <span class="pre">code_context,</span> <span class="pre">index)</span></code>
is returned.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>A list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects is returned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.trace">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">trace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.trace" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects for the stack between the current
frame and the frame in which an exception currently being handled was raised
in.  The first entry in the list represents the caller; the last entry
represents where the exception was raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>A list of <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuples</span></a>
<code class="docutils literal notranslate"><span class="pre">FrameInfo(frame,</span> <span class="pre">filename,</span> <span class="pre">lineno,</span> <span class="pre">function,</span> <span class="pre">code_context,</span> <span class="pre">index)</span></code>
is returned.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>A list of <a class="reference internal" href="#inspect.FrameInfo" title="inspect.FrameInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameInfo</span></code></a> objects is returned.</p>
</div>
</dd></dl>

</section>
<section id="fetching-attributes-statically">
<h2>Fetching attributes statically<a class="headerlink" href="#fetching-attributes-statically" title="Link to this heading">¶</a></h2>
<p>Both <a class="reference internal" href="functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a> and <a class="reference internal" href="functions.html#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a> can trigger code execution when
fetching or checking for the existence of attributes. Descriptors, like
properties, will be invoked and <a class="reference internal" href="../reference/datamodel.html#object.__getattr__" title="object.__getattr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattr__()</span></code></a> and
<a class="reference internal" href="../reference/datamodel.html#object.__getattribute__" title="object.__getattribute__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattribute__()</span></code></a>
may be called.</p>
<p>For cases where you want passive introspection, like documentation tools, this
can be inconvenient. <a class="reference internal" href="#inspect.getattr_static" title="inspect.getattr_static"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr_static()</span></code></a> has the same signature as <a class="reference internal" href="functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a>
but avoids executing code when it fetches attributes.</p>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getattr_static">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getattr_static</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getattr_static" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve attributes without triggering dynamic lookup via the
descriptor protocol, <a class="reference internal" href="../reference/datamodel.html#object.__getattr__" title="object.__getattr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattr__()</span></code></a>
or <a class="reference internal" href="../reference/datamodel.html#object.__getattribute__" title="object.__getattribute__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattribute__()</span></code></a>.</p>
<p>Note: this function may not be able to retrieve all attributes
that getattr can fetch (like dynamically created attributes)
and may find attributes that getattr can’t (like descriptors
that raise AttributeError). It can also return descriptors objects
instead of instance members.</p>
<p>If the instance <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> is shadowed by another member (for
example a property) then this function will be unable to find instance
members.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<p><a class="reference internal" href="#inspect.getattr_static" title="inspect.getattr_static"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr_static()</span></code></a> does not resolve descriptors, for example slot descriptors or
getset descriptors on objects implemented in C. The descriptor object
is returned instead of the underlying attribute.</p>
<p>You can handle these with code like the following. Note that
for arbitrary getset descriptors invoking these may trigger
code execution:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># example code for resolving the builtin descriptor types</span>
<span class="k">class</span> <span class="nc">_foo</span><span class="p">:</span>
    <span class="vm">__slots__</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;foo&#39;</span><span class="p">]</span>

<span class="n">slot_descriptor</span> <span class="o">=</span> <span class="nb">type</span><span class="p">(</span><span class="n">_foo</span><span class="o">.</span><span class="n">foo</span><span class="p">)</span>
<span class="n">getset_descriptor</span> <span class="o">=</span> <span class="nb">type</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="nb">open</span><span class="p">(</span><span class="vm">__file__</span><span class="p">))</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
<span class="n">wrapper_descriptor</span> <span class="o">=</span> <span class="nb">type</span><span class="p">(</span><span class="nb">str</span><span class="o">.</span><span class="vm">__dict__</span><span class="p">[</span><span class="s1">&#39;__add__&#39;</span><span class="p">])</span>
<span class="n">descriptor_types</span> <span class="o">=</span> <span class="p">(</span><span class="n">slot_descriptor</span><span class="p">,</span> <span class="n">getset_descriptor</span><span class="p">,</span> <span class="n">wrapper_descriptor</span><span class="p">)</span>

<span class="n">result</span> <span class="o">=</span> <span class="n">getattr_static</span><span class="p">(</span><span class="n">some_object</span><span class="p">,</span> <span class="s1">&#39;foo&#39;</span><span class="p">)</span>
<span class="k">if</span> <span class="nb">type</span><span class="p">(</span><span class="n">result</span><span class="p">)</span> <span class="ow">in</span> <span class="n">descriptor_types</span><span class="p">:</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">result</span><span class="o">.</span><span class="fm">__get__</span><span class="p">()</span>
    <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
        <span class="c1"># descriptors can raise AttributeError to</span>
        <span class="c1"># indicate there is no underlying value</span>
        <span class="c1"># in which case the descriptor itself will</span>
        <span class="c1"># have to do</span>
        <span class="k">pass</span>
</pre></div>
</div>
</section>
<section id="current-state-of-generators-coroutines-and-asynchronous-generators">
<h2>Current State of Generators, Coroutines, and Asynchronous Generators<a class="headerlink" href="#current-state-of-generators-coroutines-and-asynchronous-generators" title="Link to this heading">¶</a></h2>
<p>When implementing coroutine schedulers and for other advanced uses of
generators, it is useful to determine whether a generator is currently
executing, is waiting to start or resume or execution, or has already
terminated. <a class="reference internal" href="#inspect.getgeneratorstate" title="inspect.getgeneratorstate"><code class="xref py py-func docutils literal notranslate"><span class="pre">getgeneratorstate()</span></code></a> allows the current state of a
generator to be determined easily.</p>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getgeneratorstate">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getgeneratorstate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">generator</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getgeneratorstate" title="Link to this definition">¶</a></dt>
<dd><p>Get current state of a generator-iterator.</p>
<p>Possible states are:</p>
<ul class="simple">
<li><p>GEN_CREATED: Waiting to start execution.</p></li>
<li><p>GEN_RUNNING: Currently being executed by the interpreter.</p></li>
<li><p>GEN_SUSPENDED: Currently suspended at a yield expression.</p></li>
<li><p>GEN_CLOSED: Execution has completed.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getcoroutinestate">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getcoroutinestate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coroutine</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getcoroutinestate" title="Link to this definition">¶</a></dt>
<dd><p>Get current state of a coroutine object.  The function is intended to be
used with coroutine objects created by <a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> functions, but
will accept any coroutine-like object that has <code class="docutils literal notranslate"><span class="pre">cr_running</span></code> and
<code class="docutils literal notranslate"><span class="pre">cr_frame</span></code> attributes.</p>
<p>Possible states are:</p>
<ul class="simple">
<li><p>CORO_CREATED: Waiting to start execution.</p></li>
<li><p>CORO_RUNNING: Currently being executed by the interpreter.</p></li>
<li><p>CORO_SUSPENDED: Currently suspended at an await expression.</p></li>
<li><p>CORO_CLOSED: Execution has completed.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getasyncgenstate">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getasyncgenstate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">agen</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getasyncgenstate" title="Link to this definition">¶</a></dt>
<dd><p>Get current state of an asynchronous generator object.  The function is
intended to be used with asynchronous iterator objects created by
<a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> functions which use the <a class="reference internal" href="../reference/simple_stmts.html#yield"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">yield</span></code></a> statement,
but will accept any asynchronous generator-like object that has
<code class="docutils literal notranslate"><span class="pre">ag_running</span></code> and <code class="docutils literal notranslate"><span class="pre">ag_frame</span></code> attributes.</p>
<p>Possible states are:</p>
<ul class="simple">
<li><p>AGEN_CREATED: Waiting to start execution.</p></li>
<li><p>AGEN_RUNNING: Currently being executed by the interpreter.</p></li>
<li><p>AGEN_SUSPENDED: Currently suspended at a yield expression.</p></li>
<li><p>AGEN_CLOSED: Execution has completed.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<p>The current internal state of the generator can also be queried. This is
mostly useful for testing purposes, to ensure that internal state is being
updated as expected:</p>
<dl class="py function">
<dt class="sig sig-object py" id="inspect.getgeneratorlocals">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getgeneratorlocals</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">generator</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getgeneratorlocals" title="Link to this definition">¶</a></dt>
<dd><p>Get the mapping of live local variables in <em>generator</em> to their current
values.  A dictionary is returned that maps from variable names to values.
This is the equivalent of calling <a class="reference internal" href="functions.html#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a> in the body of the
generator, and all the same caveats apply.</p>
<p>If <em>generator</em> is a <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generator</span></a> with no currently associated frame,
then an empty dictionary is returned.  <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised if
<em>generator</em> is not a Python generator object.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This function relies on the generator exposing a Python stack frame
for introspection, which isn’t guaranteed to be the case in all
implementations of Python. In such cases, this function will always
return an empty dictionary.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getcoroutinelocals">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getcoroutinelocals</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coroutine</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getcoroutinelocals" title="Link to this definition">¶</a></dt>
<dd><p>This function is analogous to <a class="reference internal" href="#inspect.getgeneratorlocals" title="inspect.getgeneratorlocals"><code class="xref py py-func docutils literal notranslate"><span class="pre">getgeneratorlocals()</span></code></a>, but
works for coroutine objects created by <a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="inspect.getasyncgenlocals">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">getasyncgenlocals</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">agen</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#inspect.getasyncgenlocals" title="Link to this definition">¶</a></dt>
<dd><p>This function is analogous to <a class="reference internal" href="#inspect.getgeneratorlocals" title="inspect.getgeneratorlocals"><code class="xref py py-func docutils literal notranslate"><span class="pre">getgeneratorlocals()</span></code></a>, but
works for asynchronous generator objects created by <a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a>
functions which use the <a class="reference internal" href="../reference/simple_stmts.html#yield"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">yield</span></code></a> statement.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</section>
<section id="code-objects-bit-flags">
<span id="inspect-module-co-flags"></span><h2>Code Objects Bit Flags<a class="headerlink" href="#code-objects-bit-flags" title="Link to this heading">¶</a></h2>
<p>Python code objects have a <a class="reference internal" href="../reference/datamodel.html#codeobject.co_flags" title="codeobject.co_flags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">co_flags</span></code></a> attribute,
which is a bitmap of the following flags:</p>
<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_OPTIMIZED">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_OPTIMIZED</span></span><a class="headerlink" href="#inspect.CO_OPTIMIZED" title="Link to this definition">¶</a></dt>
<dd><p>The code object is optimized, using fast locals.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_NEWLOCALS">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_NEWLOCALS</span></span><a class="headerlink" href="#inspect.CO_NEWLOCALS" title="Link to this definition">¶</a></dt>
<dd><p>If set, a new dict will be created for the frame’s <a class="reference internal" href="../reference/datamodel.html#frame.f_locals" title="frame.f_locals"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_locals</span></code></a>
when the code object is executed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_VARARGS">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_VARARGS</span></span><a class="headerlink" href="#inspect.CO_VARARGS" title="Link to this definition">¶</a></dt>
<dd><p>The code object has a variable positional parameter (<code class="docutils literal notranslate"><span class="pre">*args</span></code>-like).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_VARKEYWORDS">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_VARKEYWORDS</span></span><a class="headerlink" href="#inspect.CO_VARKEYWORDS" title="Link to this definition">¶</a></dt>
<dd><p>The code object has a variable keyword parameter (<code class="docutils literal notranslate"><span class="pre">**kwargs</span></code>-like).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_NESTED">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_NESTED</span></span><a class="headerlink" href="#inspect.CO_NESTED" title="Link to this definition">¶</a></dt>
<dd><p>The flag is set when the code object is a nested function.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_GENERATOR">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_GENERATOR</span></span><a class="headerlink" href="#inspect.CO_GENERATOR" title="Link to this definition">¶</a></dt>
<dd><p>The flag is set when the code object is a generator function, i.e.
a generator object is returned when the code object is executed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_COROUTINE">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_COROUTINE</span></span><a class="headerlink" href="#inspect.CO_COROUTINE" title="Link to this definition">¶</a></dt>
<dd><p>The flag is set when the code object is a coroutine function.
When the code object is executed it returns a coroutine object.
See <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0492/"><strong>PEP 492</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_ITERABLE_COROUTINE">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_ITERABLE_COROUTINE</span></span><a class="headerlink" href="#inspect.CO_ITERABLE_COROUTINE" title="Link to this definition">¶</a></dt>
<dd><p>The flag is used to transform generators into generator-based
coroutines.  Generator objects with this flag can be used in
<code class="docutils literal notranslate"><span class="pre">await</span></code> expression, and can <code class="docutils literal notranslate"><span class="pre">yield</span> <span class="pre">from</span></code> coroutine objects.
See <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0492/"><strong>PEP 492</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="inspect.CO_ASYNC_GENERATOR">
<span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">CO_ASYNC_GENERATOR</span></span><a class="headerlink" href="#inspect.CO_ASYNC_GENERATOR" title="Link to this definition">¶</a></dt>
<dd><p>The flag is set when the code object is an asynchronous generator
function.  When the code object is executed it returns an
asynchronous generator object.  See <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0525/"><strong>PEP 525</strong></a> for more details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The flags are specific to CPython, and may not be defined in other
Python implementations.  Furthermore, the flags are an implementation
detail, and can be removed or deprecated in future Python releases.
It’s recommended to use public APIs from the <a class="reference internal" href="#module-inspect" title="inspect: Extract information and source code from live objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code></a> module
for any introspection needs.</p>
</div>
</section>
<section id="buffer-flags">
<h2>Buffer flags<a class="headerlink" href="#buffer-flags" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="inspect.BufferFlags">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">inspect.</span></span><span class="sig-name descname"><span class="pre">BufferFlags</span></span><a class="headerlink" href="#inspect.BufferFlags" title="Link to this definition">¶</a></dt>
<dd><p>This is an <a class="reference internal" href="enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.IntFlag</span></code></a> that represents the flags that
can be passed to the <a class="reference internal" href="../reference/datamodel.html#object.__buffer__" title="object.__buffer__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__buffer__()</span></code></a> method of objects
implementing the <a class="reference internal" href="../c-api/buffer.html#bufferobjects"><span class="std std-ref">buffer protocol</span></a>.</p>
<p>The meaning of the flags is explained at <a class="reference internal" href="../c-api/buffer.html#buffer-request-types"><span class="std std-ref">Buffer request types</span></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.SIMPLE">
<span class="sig-name descname"><span class="pre">SIMPLE</span></span><a class="headerlink" href="#inspect.BufferFlags.SIMPLE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.WRITABLE">
<span class="sig-name descname"><span class="pre">WRITABLE</span></span><a class="headerlink" href="#inspect.BufferFlags.WRITABLE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.FORMAT">
<span class="sig-name descname"><span class="pre">FORMAT</span></span><a class="headerlink" href="#inspect.BufferFlags.FORMAT" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.ND">
<span class="sig-name descname"><span class="pre">ND</span></span><a class="headerlink" href="#inspect.BufferFlags.ND" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.STRIDES">
<span class="sig-name descname"><span class="pre">STRIDES</span></span><a class="headerlink" href="#inspect.BufferFlags.STRIDES" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.C_CONTIGUOUS">
<span class="sig-name descname"><span class="pre">C_CONTIGUOUS</span></span><a class="headerlink" href="#inspect.BufferFlags.C_CONTIGUOUS" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.F_CONTIGUOUS">
<span class="sig-name descname"><span class="pre">F_CONTIGUOUS</span></span><a class="headerlink" href="#inspect.BufferFlags.F_CONTIGUOUS" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.ANY_CONTIGUOUS">
<span class="sig-name descname"><span class="pre">ANY_CONTIGUOUS</span></span><a class="headerlink" href="#inspect.BufferFlags.ANY_CONTIGUOUS" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.INDIRECT">
<span class="sig-name descname"><span class="pre">INDIRECT</span></span><a class="headerlink" href="#inspect.BufferFlags.INDIRECT" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.CONTIG">
<span class="sig-name descname"><span class="pre">CONTIG</span></span><a class="headerlink" href="#inspect.BufferFlags.CONTIG" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.CONTIG_RO">
<span class="sig-name descname"><span class="pre">CONTIG_RO</span></span><a class="headerlink" href="#inspect.BufferFlags.CONTIG_RO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.STRIDED">
<span class="sig-name descname"><span class="pre">STRIDED</span></span><a class="headerlink" href="#inspect.BufferFlags.STRIDED" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.STRIDED_RO">
<span class="sig-name descname"><span class="pre">STRIDED_RO</span></span><a class="headerlink" href="#inspect.BufferFlags.STRIDED_RO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.RECORDS">
<span class="sig-name descname"><span class="pre">RECORDS</span></span><a class="headerlink" href="#inspect.BufferFlags.RECORDS" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.RECORDS_RO">
<span class="sig-name descname"><span class="pre">RECORDS_RO</span></span><a class="headerlink" href="#inspect.BufferFlags.RECORDS_RO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.FULL">
<span class="sig-name descname"><span class="pre">FULL</span></span><a class="headerlink" href="#inspect.BufferFlags.FULL" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.FULL_RO">
<span class="sig-name descname"><span class="pre">FULL_RO</span></span><a class="headerlink" href="#inspect.BufferFlags.FULL_RO" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.READ">
<span class="sig-name descname"><span class="pre">READ</span></span><a class="headerlink" href="#inspect.BufferFlags.READ" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="inspect.BufferFlags.WRITE">
<span class="sig-name descname"><span class="pre">WRITE</span></span><a class="headerlink" href="#inspect.BufferFlags.WRITE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</section>
<section id="command-line-interface">
<span id="inspect-module-cli"></span><h2>Command Line Interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-inspect" title="inspect: Extract information and source code from live objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code></a> module also provides a basic introspection capability
from the command line.</p>
<p>By default, accepts the name of a module and prints the source of that
module. A class or function within the module can be printed instead by
appended a colon and the qualified name of the target object.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-inspect-details">
<span class="sig-name descname"><span class="pre">--details</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-inspect-details" title="Link to this definition">¶</a></dt>
<dd><p>Print information about the specified object rather than the source code</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a><ul>
<li><a class="reference internal" href="#types-and-members">Types and members</a></li>
<li><a class="reference internal" href="#retrieving-source-code">Retrieving source code</a></li>
<li><a class="reference internal" href="#introspecting-callables-with-the-signature-object">Introspecting callables with the Signature object</a></li>
<li><a class="reference internal" href="#classes-and-functions">Classes and functions</a></li>
<li><a class="reference internal" href="#the-interpreter-stack">The interpreter stack</a></li>
<li><a class="reference internal" href="#fetching-attributes-statically">Fetching attributes statically</a></li>
<li><a class="reference internal" href="#current-state-of-generators-coroutines-and-asynchronous-generators">Current State of Generators, Coroutines, and Asynchronous Generators</a></li>
<li><a class="reference internal" href="#code-objects-bit-flags">Code Objects Bit Flags</a></li>
<li><a class="reference internal" href="#buffer-flags">Buffer flags</a></li>
<li><a class="reference internal" href="#command-line-interface">Command Line Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gc.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="site.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/inspect.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="site.html" title="site — Site-specific configuration hook"
             >next</a> |</li>
        <li class="right" >
          <a href="gc.html" title="gc — Garbage Collector interface"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>