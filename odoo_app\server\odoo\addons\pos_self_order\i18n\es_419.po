# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_self_order
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_self_order
#: model:ir.actions.report,print_report_name:pos_self_order.report_self_order_qr_codes_page
msgid "\"QR codes\""
msgstr "\"Códigos QR\""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid ""
"%s is not available anymore, it has thus been removed from your order. "
"Please review your order and validate it again."
msgstr ""
"%s ya no está disponible y se eliminó de su orden. Revise su orden y vuelva "
"a validarla."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "/ Tracker number:"
msgstr "/ número de seguimiento:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"<br/>\n"
"                    URL:"
msgstr ""
"<br/>\n"
"                    URL:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"<span>Please note that the kiosk only works with Adyen &amp; Stripe "
"terminals</span>"
msgstr ""
"<span>Tenga en cuenta que el quiosco solo funciona con terminales de Adyen y"
" Stripe</span>"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
msgid ""
"Access rights of this user will be used when visiting self order website "
"when no session is open."
msgstr ""
"Los permisos de acceso de este usuario se utilizarán al visitar el sitio web"
" de autopedido cuando no haya ninguna sesión abierta."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__active
msgid "Active"
msgstr "Activar"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add Languages"
msgstr "Agregar idiomas"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add an image to brand your header."
msgstr "Agregue una imagen de marca a su encabezado."

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Add images"
msgstr "Agregar imágenes"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Add to Cart"
msgstr "Agregar al carrito"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Add to cart"
msgstr "Agregar al carrito"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr ""
"Ajuste la tasa tributaria según si los clientes comen en el establecimiento "
"o se llevan su comida."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "All the items will be removed from the cart."
msgstr "Se eliminarán todos los artículos del carrito."

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Alternative Fiscal Position"
msgstr "Posición fiscal alterna"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "An error has occurred"
msgstr "Ocurrió un error"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Any items already sent will not be canceled"
msgstr "No se cancelarán los artículos ya enviados"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Are you sure you want to cancel this order?"
msgstr "¿Está seguro de que desea cancelar esta orden? "

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Available Languages"
msgstr "Idiomas disponibles"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Available in Self"
msgstr "Disponible en auto"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__self_order_available
msgid "Available in Self Order"
msgstr "Disponible en autopedido"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Available interface languages"
msgstr "Idiomas disponibles para la interfaz"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Back"
msgstr "Regresar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Cancel Order"
msgstr "Cancelar orden"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.js:0
#, python-format
msgid "Cancel order"
msgstr "Cancelar orden"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Choose the kiosk mode"
msgstr "Seleccione el modo quiosco"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Choose the self ordering mode"
msgstr "Seleccione el modo de autopedido"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Choose when the customer will pay"
msgstr "Elija cuándo pagará el cliente"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#, python-format
msgid "Choose your"
msgstr "Elija su"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Choose your eating location"
msgstr "Elija el lugar donde comerá"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Close Session"
msgstr "Cerrar sesión"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Combo"
msgstr "Combo"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "Líneas del combo"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "Combo principal"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_id
msgid "Combo line reference"
msgstr "Referencia de la línea del combo"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Connection lost, please try again later"
msgstr "Se perdió la conexión, inténtelo de nuevo más tarde."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Could you please confirm your table number?"
msgstr "¿Podría confirmar su número de mesa?"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_date
msgid "Created on"
msgstr "Creado el"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.js:0
#, python-format
msgid "Current"
msgstr "Actual"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "Custom Links"
msgstr "Enlaces personalizados"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_self_order_custom_link
msgid ""
"Custom links that the restaurant can configure to be displayed on the self "
"order screen"
msgstr ""
"Enlaces personalizados que el restaurante puede configurar para que "
"aparezcan en la pantalla de autopedido"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Customize Header"
msgstr "Personalizar encabezado"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__danger
msgid "Danger"
msgstr "Peligro"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__dark
msgid "Dark"
msgstr "Oscuro"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default"
msgstr "Predeterminado"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default Language"
msgstr "Idioma predeterminado"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default User"
msgstr "Usuario predeterminado"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default language for the kiosk mode"
msgstr "Idioma predeterminado para el modo quiosco"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__nothing
msgid "Disable"
msgstr "Deshabilitar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#: code:addons/pos_self_order/static/src/app/components/language_popup/language_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Download QR Codes"
msgstr "Descargar códigos QR"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Each Order"
msgstr "Cada orden"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Each table in your floor plan is assigned a unique QR code based on your configuration. For security reasons,\n"
"                    both the point of sale and table names are encrypted in the generated URL, as shown in the example below:."
msgstr ""
"A cada mesa de su piso se le asignará un código QR único según su configuración. Por motivos de seguridad,\n"
"                    tanto el punto de venta como los nombres de las mesas se cifran en la URL generada, tal y como se muestra en el siguiente ejemplo:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Eat In"
msgstr "Comer en el local"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Eat in / Take out"
msgstr "Comer en el local/para llevar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/attribute_selection/attribute_selection.xml:0
#, python-format
msgid "Enter your custom value"
msgstr "Introduzca su valor personalizado"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Feel free to use and print this QR code as many times as needed according to"
" your requirements."
msgstr ""
"No dude en utilizar e imprimir este código QR tantas veces como necesite."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Fiscal Positions"
msgstr "Posiciones fiscales"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Follow instructions on the terminal"
msgstr "Siga las instrucciones de la terminal"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "From your Kiosk, open this URL:"
msgstr "Abra la siguiente URL desde su quiosco:"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Generic"
msgstr "Genérico"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Get a tracker and enter its number here"
msgstr "Consiga un rastreador y escriba su número aquí"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid ""
"Hey, looks like you forgot to create products or add them to pos_config. "
"Please add them before using the Self Order"
msgstr ""
"Disculpe, parece que olvidó crear productos o agregarlos a pos_config. "
"Agréguelos antes de utilizar Autopedido."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Home buttons"
msgstr "Botones de inicio"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Hope you enjoyed your meal!"
msgstr "Ojalá haya disfrutado su comida."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to customize"
msgstr "Cómo personalizar"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to use"
msgstr "Cómo usar"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__id
msgid "ID"
msgstr "ID"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,help:pos_self_order.field_product_template__self_order_available
msgid "If this product is available in the Self Order screens"
msgstr "Si este producto está disponible en las pantallas de autopedido"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"If you need customized QR codes, start by scanning the relevant QR code to acquire the URL. Then, make\n"
"                use of a QR code generator like https://www.qrcode-monkey.com or https://www.qr-code-generator.com"
msgstr ""
"Si necesita códigos QR personalizados, primero escanee el código QR correspondiente para obtener la URL. Después,\n"
"                utilice un generador de códigos QR como https://www.qrcode-monkey.com o https://www.qr-code-generator.com."

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Image to display on the self order screen"
msgstr "Imagen para mostrar en la pantalla de autopedido"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid ""
"In Self-Order mode, you must have at least one table to generate QR codes"
msgstr ""
"En el modo Autopedido, deberá disponer de al menos una mesa para generar "
"códigos QR"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__inactive
msgid "Inactive"
msgstr "Inactivo"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__info
msgid "Info"
msgstr "Información"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Information about your product for Self Order and Kiosk"
msgstr "Información sobre su producto para el modo Autopedido y Quiosco"

#. module: pos_self_order
#: model:ir.actions.act_window,name:pos_self_order.action_pos_self_order_search_view
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__kiosk
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_search_view
msgid "Kiosk"
msgstr "Quiosco"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__name
msgid "Label"
msgstr "Etiqueta"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Language"
msgstr "Idioma"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Languages available for the kiosk mode"
msgstr "Idiomas disponibles para el modo quiosco"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Let your customers order using their mobile or a kiosk."
msgstr ""
"Permita que sus clientes hagan sus pedidos desde su celular o en un quiosco"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__light
msgid "Light"
msgstr "Claro"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online or order with the QR codes on your tables"
msgstr ""
"Gracias a los códigos QR de sus mesas, sus clientes podrán\n"
"                consultar su menú en línea con facilidad o hacer pedidos"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online with the QR codes on your tables"
msgstr ""
"Gracias a los códigos QR de sus mesas, sus clientes podrán\n"
"                consultar su menú en línea con facilidad"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Meal"
msgstr "Comida"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Mobile Menu"
msgstr "Menú para celular"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Mobile self-order & Kiosk"
msgstr "Autopedidos por celular y quioscos"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Order"
msgstr "Mi orden"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Orders"
msgstr "Mis órdenes"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "NUEVO"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "NOTA"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Name of the image to display on the self order screen"
msgstr "Nombre de la imagen para mostrar en la pantalla de autopedido"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Next"
msgstr "Siguiente"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "No"
msgstr "No"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "No order found"
msgstr "No se encontró ninguna orden"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "No products found"
msgstr "No se encontraron productos"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Not available"
msgstr "No disponible"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Not available in Self"
msgstr "No disponible en auto"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Ok"
msgstr "De acuerdo"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "Only pay after each is available with kiosk mode."
msgstr "Solo se puede pagar después de cada uno con el modo quiosco."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Open Kiosk"
msgstr "Abrir quiosco"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "Open in New Tab"
msgstr "Abrir en una nueva pestaña"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Order"
msgstr "Orden"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: model:pos_self_order.custom_link,name:pos_self_order.default_custom_link
#, python-format
msgid "Order Now"
msgstr "Ordenar ahora"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Order number:"
msgstr "Número de orden:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Order to pick-up at the counter"
msgstr "Pedido para recoger en el mostrador"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Orders not found on server"
msgstr "No se han encontrado pedidos en el servidor"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Out of stock"
msgstr "Agotado"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Pay"
msgstr "Pagar"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Pay After:"
msgstr "Pagar después de:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Pay after"
msgstr "Pagar después de"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Pay at the cashier"
msgstr "Pagar en caja"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Personalize your splash screen by adding one or multiple images to create a "
"slideshow"
msgstr ""
"Personalice su pantalla de inicio con una o varias imágenes para crear una "
"presentación"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Pickup At Counter"
msgstr "Recoger en mostrador"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__counter
msgid "Pickup zone"
msgstr "Zona de recolección"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Please wait until the price is loaded"
msgstr "Espere hasta que cargue el precio"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_session.py:0
#, python-format
msgid "PoS Order by Session"
msgstr "Orden de PdV por sesión"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuración del punto de venta"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Líneas de la orden del punto de venta"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order
msgid "Point of Sale Orders"
msgstr "Órdenes del punto de venta"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Métodos de pago del punto de venta "

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesión de punto de venta"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Point of sale:"
msgstr "Punto de venta:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid "Points of Sale"
msgstr "Puntos de venta"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__link_html
msgid "Preview"
msgstr "Vista previa"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Preview Web interface"
msgstr "Vista previa de la interfaz web"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__primary
msgid "Primary"
msgstr "Primario"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Print QR Codes"
msgstr "Imprimir códigos QR"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_template
msgid "Product"
msgstr "Producto"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__description_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__description_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Product Description for Self Order"
msgstr "Descripción del producto del autopedido"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "Información del producto"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Product image"
msgstr "Imagen del producto"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product is not available"
msgstr "El producto no está disponible"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product not found"
msgstr "No se encontró el producto"

#. module: pos_self_order
#: model:ir.actions.report,name:pos_self_order.report_self_order_qr_codes_page
msgid "QR Codes"
msgstr "Códigos QR"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "QR codes can only be generated in mobile or consultation mode."
msgstr ""
"Los códigos QR solo se pueden generar en el celular o en modo consulta."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__consultation
msgid "QR menu"
msgstr "Menú QR"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "QR menu & Kiosk activation"
msgstr "Menú QR y activación del quiosco"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__mobile
msgid "QR menu + Ordering"
msgstr "Menú QR + Pedidos"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Quantity select"
msgstr "Selección de cantidad"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Reset QR Codes"
msgstr "Restablecer códigos QR"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_restaurant_table
msgid "Restaurant Table"
msgstr "Mesa del restaurante"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Retry"
msgstr "Volver a intentar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "Search product"
msgstr "Buscar producto"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__secondary
msgid "Secondary"
msgstr "Secundario"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__access_token
#: model:ir.model.fields,field_description:pos_self_order.field_restaurant_table__identifier
msgid "Security Token"
msgstr "Token de seguridad"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid ""
"Select for which points of sale you want to display this link. Leave empty "
"to display it for all points of sale. You have to select among the points of"
" sale that have the 'QR Code Menu' feature enabled."
msgstr ""
"Seleccione los puntos de venta donde desea mostrar este enlace, deje el "
"campo vacío para mostrarlo en todos. Debe seleccionar entre los puntos de "
"venta que tienen habilitado el menú en código QR."

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Self Kiosk"
msgstr "Quiosco de autopedido"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
msgid "Self Order Kiosk Image Brand"
msgstr "Imagen de marca del quiosco de autopedidos"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Self Order Kiosk Image Brand Name"
msgstr "Nombre de la imagen de marca del quiosco de autopedidos"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Self Order:"
msgstr "Autopedido:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Self Ordering"
msgstr "Autopedido"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Self Ordering Enabled"
msgstr "Autopedido habilitado"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Self Ordering Mode"
msgstr "Modo de autopedido"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_url
msgid "Self Ordering Url"
msgstr "URL para hacer el autopedido"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Self-ordering availability:"
msgstr "Disponibilidad de autopedidos:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Service"
msgstr "Servicio"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Service at"
msgstr "Servicio en"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Service at Table"
msgstr "Servicio a la mesa"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Service at table"
msgstr "Servicio a la mesa"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Splash screens"
msgstr "Pantallas de inicio"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Start Kiosk"
msgstr "Iniciar quiosco"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__status
msgid "Status"
msgstr "Estado"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__style
msgid "Style"
msgstr "Estilo"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__success
msgid "Success"
msgstr "Aprobada"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__table
msgid "Table"
msgstr "Mesa"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__table_stand_number
msgid "Table Stand Number"
msgstr "Número de mesa"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Table Tracker:"
msgstr "Rastreador de mesas:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Table detective time!"
msgstr "¡Hora de ser detective!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Table:"
msgstr "Mesa:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__take_away
msgid "Take Away"
msgstr "Para llevar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Take Out"
msgstr "Comida para llevar"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_takeaway
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_takeaway
msgid "Takeaway"
msgstr "Para llevar"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Tax:"
msgstr "Impuesto:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Taxes:"
msgstr "Impuestos:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Thanks a lot!"
msgstr "¡Muchas gracias!"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "The Self-Order default user must be a POS user"
msgstr ""
"El usuario predeterminado de autopedido debe ser un usuario del punto de "
"venta"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "The user must be a POS user"
msgstr "El usuario debe ser un usuario del punto de venta"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Esto es útil para los restaurantes que cuentan con servicio para llevar y "
"para consumir allí, y que suponen tasas de impuestos específicos."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__url
msgid "URL"
msgstr "URL"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "URL:"
msgstr "URL:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Uncategorised"
msgstr "Sin categoría"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__warning
msgid "Warning"
msgstr "Advertencia"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "We're currently closed"
msgstr "Está cerrado por el momento"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid "We're currently closed."
msgstr "Estamos cerrados por el momento."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "We're preparing your order!"
msgstr "Estamos preparando su orden"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Yes"
msgstr "Sí"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.js:0
#, python-format
msgid "You cannot edit a posted orderline !"
msgstr "No puede editar una línea de orden publicada."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "You're not authorized to perform this action"
msgstr "No tiene los permisos necesarios para realizar esta acción"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Your Order"
msgstr "Su orden"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Your Selection"
msgstr "Su selección"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been canceled"
msgstr "Se canceló su orden"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been paid"
msgstr "Su orden ha sido pagada."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Your order number"
msgstr "Su número de orden"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order status has been changed"
msgstr "El estado de su orden cambió"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "empty = all points of sale"
msgstr "vacío = todos los puntos de venta"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "https://odoo.com"
msgstr "https://odoo.com"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "odoo"
msgstr "odoo"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "options"
msgstr "opciones"
