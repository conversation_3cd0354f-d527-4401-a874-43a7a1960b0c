<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Built-in Exceptions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/exceptions.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="In Python, all exceptions must be instances of a class that derives from BaseException. In a try statement with an except clause that mentions a particular class, that clause also handles any excep..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="In Python, all exceptions must be instances of a class that derives from BaseException. In a try statement with an except clause that mentions a particular class, that clause also handles any excep..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Built-in Exceptions &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Text Processing Services" href="text.html" />
    <link rel="prev" title="Built-in Types" href="stdtypes.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/exceptions.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Built-in Exceptions</a><ul>
<li><a class="reference internal" href="#exception-context">Exception context</a></li>
<li><a class="reference internal" href="#inheriting-from-built-in-exceptions">Inheriting from built-in exceptions</a></li>
<li><a class="reference internal" href="#base-classes">Base classes</a></li>
<li><a class="reference internal" href="#concrete-exceptions">Concrete exceptions</a><ul>
<li><a class="reference internal" href="#os-exceptions">OS exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#warnings">Warnings</a></li>
<li><a class="reference internal" href="#exception-groups">Exception groups</a></li>
<li><a class="reference internal" href="#exception-hierarchy">Exception hierarchy</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stdtypes.html"
                          title="previous chapter">Built-in Types</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="text.html"
                          title="next chapter">Text Processing Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/exceptions.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="text.html" title="Text Processing Services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="stdtypes.html" title="Built-in Types"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Built-in Exceptions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="built-in-exceptions">
<span id="bltin-exceptions"></span><h1>Built-in Exceptions<a class="headerlink" href="#built-in-exceptions" title="Link to this heading">¶</a></h1>
<p id="index-0">In Python, all exceptions must be instances of a class that derives from
<a class="reference internal" href="#BaseException" title="BaseException"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseException</span></code></a>.  In a <a class="reference internal" href="../reference/compound_stmts.html#try"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">try</span></code></a> statement with an <a class="reference internal" href="../reference/compound_stmts.html#except"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">except</span></code></a>
clause that mentions a particular class, that clause also handles any exception
classes derived from that class (but not exception classes from which <em>it</em> is
derived).  Two exception classes that are not related via subclassing are never
equivalent, even if they have the same name.</p>
<p id="index-1">The built-in exceptions listed in this chapter can be generated by the interpreter or
built-in functions.  Except where mentioned, they have an “associated value”
indicating the detailed cause of the error.  This may be a string or a tuple of
several items of information (e.g., an error code and a string explaining the
code).  The associated value is usually passed as arguments to the exception
class’s constructor.</p>
<p>User code can raise built-in exceptions.  This can be used to test an exception
handler or to report an error condition “just like” the situation in which the
interpreter raises the same exception; but beware that there is nothing to
prevent user code from raising an inappropriate error.</p>
<p>The built-in exception classes can be subclassed to define new exceptions;
programmers are encouraged to derive new exceptions from the <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>
class or one of its subclasses, and not from <a class="reference internal" href="#BaseException" title="BaseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseException</span></code></a>.  More
information on defining exceptions is available in the Python Tutorial under
<a class="reference internal" href="../tutorial/errors.html#tut-userexceptions"><span class="std std-ref">User-defined Exceptions</span></a>.</p>
<section id="exception-context">
<h2>Exception context<a class="headerlink" href="#exception-context" title="Link to this heading">¶</a></h2>
<p id="index-2">Three attributes on exception objects provide information about the context in
which the exception was raised:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="BaseException.__context__">
<span class="sig-prename descclassname"><span class="pre">BaseException.</span></span><span class="sig-name descname"><span class="pre">__context__</span></span><a class="headerlink" href="#BaseException.__context__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="BaseException.__cause__">
<span class="sig-prename descclassname"><span class="pre">BaseException.</span></span><span class="sig-name descname"><span class="pre">__cause__</span></span><a class="headerlink" href="#BaseException.__cause__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="BaseException.__suppress_context__">
<span class="sig-prename descclassname"><span class="pre">BaseException.</span></span><span class="sig-name descname"><span class="pre">__suppress_context__</span></span><a class="headerlink" href="#BaseException.__suppress_context__" title="Link to this definition">¶</a></dt>
<dd><p>When raising a new exception while another exception
is already being handled, the new exception’s
<code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code> attribute is automatically set to the handled
exception.  An exception may be handled when an <a class="reference internal" href="../reference/compound_stmts.html#except"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">except</span></code></a> or
<a class="reference internal" href="../reference/compound_stmts.html#finally"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">finally</span></code></a> clause, or a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement, is used.</p>
<p>This implicit exception context can be
supplemented with an explicit cause by using <code class="xref std std-keyword docutils literal notranslate"><span class="pre">from</span></code> with
<a class="reference internal" href="../reference/simple_stmts.html#raise"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">raise</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">raise</span> <span class="n">new_exc</span> <span class="kn">from</span> <span class="nn">original_exc</span>
</pre></div>
</div>
<p>The expression following <a class="reference internal" href="../reference/simple_stmts.html#raise"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">from</span></code></a> must be an exception or <code class="docutils literal notranslate"><span class="pre">None</span></code>. It
will be set as <code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code> on the raised exception. Setting
<code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code> also implicitly sets the <code class="xref py py-attr docutils literal notranslate"><span class="pre">__suppress_context__</span></code>
attribute to <code class="docutils literal notranslate"><span class="pre">True</span></code>, so that using <code class="docutils literal notranslate"><span class="pre">raise</span> <span class="pre">new_exc</span> <span class="pre">from</span> <span class="pre">None</span></code>
effectively replaces the old exception with the new one for display
purposes (e.g. converting <a class="reference internal" href="#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> to <a class="reference internal" href="#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a>), while
leaving the old exception available in <code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code> for introspection
when debugging.</p>
<p>The default traceback display code shows these chained exceptions in
addition to the traceback for the exception itself. An explicitly chained
exception in <code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code> is always shown when present. An implicitly
chained exception in <code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code> is shown only if <code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code>
is <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">__suppress_context__</span></code> is false.</p>
<p>In either case, the exception itself is always shown after any chained
exceptions so that the final line of the traceback always shows the last
exception that was raised.</p>
</dd></dl>

</section>
<section id="inheriting-from-built-in-exceptions">
<h2>Inheriting from built-in exceptions<a class="headerlink" href="#inheriting-from-built-in-exceptions" title="Link to this heading">¶</a></h2>
<p>User code can create subclasses that inherit from an exception type.
It’s recommended to only subclass one exception type at a time to avoid
any possible conflicts between how the bases handle the <code class="docutils literal notranslate"><span class="pre">args</span></code>
attribute, as well as due to possible memory layout incompatibilities.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> Most built-in exceptions are implemented in C for efficiency, see:
<a class="reference external" href="https://github.com/python/cpython/tree/3.12/Objects/exceptions.c">Objects/exceptions.c</a>.  Some have custom memory layouts
which makes it impossible to create a subclass that inherits from
multiple exception types. The memory layout of a type is an implementation
detail and might change between Python versions, leading to new
conflicts in the future.  Therefore, it’s recommended to avoid
subclassing multiple exception types altogether.</p>
</div>
</section>
<section id="base-classes">
<h2>Base classes<a class="headerlink" href="#base-classes" title="Link to this heading">¶</a></h2>
<p>The following exceptions are used mostly as base classes for other exceptions.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="BaseException">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BaseException</span></span><a class="headerlink" href="#BaseException" title="Link to this definition">¶</a></dt>
<dd><p>The base class for all built-in exceptions.  It is not meant to be directly
inherited by user-defined classes (for that, use <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>).  If
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> is called on an instance of this class, the representation of
the argument(s) to the instance are returned, or the empty string when
there were no arguments.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="BaseException.args">
<span class="sig-name descname"><span class="pre">args</span></span><a class="headerlink" href="#BaseException.args" title="Link to this definition">¶</a></dt>
<dd><p>The tuple of arguments given to the exception constructor.  Some built-in
exceptions (like <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>) expect a certain number of arguments and
assign a special meaning to the elements of this tuple, while others are
usually called only with a single string giving an error message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="BaseException.with_traceback">
<span class="sig-name descname"><span class="pre">with_traceback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#BaseException.with_traceback" title="Link to this definition">¶</a></dt>
<dd><p>This method sets <em>tb</em> as the new traceback for the exception and returns
the exception object.  It was more commonly used before the exception
chaining features of <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-3134/"><strong>PEP 3134</strong></a> became available.  The following example
shows how we can convert an instance of <code class="docutils literal notranslate"><span class="pre">SomeException</span></code> into an
instance of <code class="docutils literal notranslate"><span class="pre">OtherException</span></code> while preserving the traceback.  Once
raised, the current frame is pushed onto the traceback of the
<code class="docutils literal notranslate"><span class="pre">OtherException</span></code>, as would have happened to the traceback of the
original <code class="docutils literal notranslate"><span class="pre">SomeException</span></code> had we allowed it to propagate to the caller.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="p">:</span>
    <span class="o">...</span>
<span class="k">except</span> <span class="n">SomeException</span><span class="p">:</span>
    <span class="n">tb</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">exception</span><span class="p">()</span><span class="o">.</span><span class="n">__traceback__</span>
    <span class="k">raise</span> <span class="n">OtherException</span><span class="p">(</span><span class="o">...</span><span class="p">)</span><span class="o">.</span><span class="n">with_traceback</span><span class="p">(</span><span class="n">tb</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="BaseException.__traceback__">
<span class="sig-name descname"><span class="pre">__traceback__</span></span><a class="headerlink" href="#BaseException.__traceback__" title="Link to this definition">¶</a></dt>
<dd><p>A writable field that holds the
<a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback object</span></a> associated with this
exception. See also: <a class="reference internal" href="../reference/simple_stmts.html#raise"><span class="std std-ref">The raise statement</span></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="BaseException.add_note">
<span class="sig-name descname"><span class="pre">add_note</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">note</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#BaseException.add_note" title="Link to this definition">¶</a></dt>
<dd><p>Add the string <code class="docutils literal notranslate"><span class="pre">note</span></code> to the exception’s notes which appear in the standard
traceback after the exception string. A <a class="reference internal" href="#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised if <code class="docutils literal notranslate"><span class="pre">note</span></code>
is not a string.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="BaseException.__notes__">
<span class="sig-name descname"><span class="pre">__notes__</span></span><a class="headerlink" href="#BaseException.__notes__" title="Link to this definition">¶</a></dt>
<dd><p>A list of the notes of this exception, which were added with <a class="reference internal" href="#BaseException.add_note" title="BaseException.add_note"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_note()</span></code></a>.
This attribute is created when <a class="reference internal" href="#BaseException.add_note" title="BaseException.add_note"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_note()</span></code></a> is called.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="Exception">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Exception</span></span><a class="headerlink" href="#Exception" title="Link to this definition">¶</a></dt>
<dd><p>All built-in, non-system-exiting exceptions are derived from this class.  All
user-defined exceptions should also be derived from this class.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ArithmeticError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ArithmeticError</span></span><a class="headerlink" href="#ArithmeticError" title="Link to this definition">¶</a></dt>
<dd><p>The base class for those built-in exceptions that are raised for various
arithmetic errors: <a class="reference internal" href="#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a>, <a class="reference internal" href="#ZeroDivisionError" title="ZeroDivisionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ZeroDivisionError</span></code></a>,
<a class="reference internal" href="#FloatingPointError" title="FloatingPointError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FloatingPointError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="BufferError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BufferError</span></span><a class="headerlink" href="#BufferError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a <a class="reference internal" href="../c-api/buffer.html#bufferobjects"><span class="std std-ref">buffer</span></a> related operation cannot be
performed.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="LookupError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LookupError</span></span><a class="headerlink" href="#LookupError" title="Link to this definition">¶</a></dt>
<dd><p>The base class for the exceptions that are raised when a key or index used on
a mapping or sequence is invalid: <a class="reference internal" href="#IndexError" title="IndexError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexError</span></code></a>, <a class="reference internal" href="#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>.  This
can be raised directly by <a class="reference internal" href="codecs.html#codecs.lookup" title="codecs.lookup"><code class="xref py py-func docutils literal notranslate"><span class="pre">codecs.lookup()</span></code></a>.</p>
</dd></dl>

</section>
<section id="concrete-exceptions">
<h2>Concrete exceptions<a class="headerlink" href="#concrete-exceptions" title="Link to this heading">¶</a></h2>
<p>The following exceptions are the exceptions that are usually raised.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="AssertionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AssertionError</span></span><a class="headerlink" href="#AssertionError" title="Link to this definition">¶</a></dt>
<dd><p id="index-4">Raised when an <a class="reference internal" href="../reference/simple_stmts.html#assert"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">assert</span></code></a> statement fails.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="AttributeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributeError</span></span><a class="headerlink" href="#AttributeError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an attribute reference (see <a class="reference internal" href="../reference/expressions.html#attribute-references"><span class="std std-ref">Attribute references</span></a>) or
assignment fails.  (When an object does not support attribute references or
attribute assignments at all, <a class="reference internal" href="#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised.)</p>
<p>The <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">obj</span></code> attributes can be set using keyword-only
arguments to the constructor. When set they represent the name of the attribute
that was attempted to be accessed and the object that was accessed for said
attribute, respectively.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">obj</span></code> attributes.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="EOFError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">EOFError</span></span><a class="headerlink" href="#EOFError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the <a class="reference internal" href="functions.html#input" title="input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a> function hits an end-of-file condition (EOF)
without reading any data. (N.B.: the <code class="xref py py-meth docutils literal notranslate"><span class="pre">io.IOBase.read()</span></code> and
<a class="reference internal" href="io.html#io.IOBase.readline" title="io.IOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">io.IOBase.readline()</span></code></a> methods return an empty string when they hit EOF.)</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="FloatingPointError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FloatingPointError</span></span><a class="headerlink" href="#FloatingPointError" title="Link to this definition">¶</a></dt>
<dd><p>Not currently used.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="GeneratorExit">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">GeneratorExit</span></span><a class="headerlink" href="#GeneratorExit" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generator</span></a> or <a class="reference internal" href="../glossary.html#term-coroutine"><span class="xref std std-term">coroutine</span></a> is closed;
see <a class="reference internal" href="../reference/expressions.html#generator.close" title="generator.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">generator.close()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#coroutine.close" title="coroutine.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">coroutine.close()</span></code></a>.  It
directly inherits from <a class="reference internal" href="#BaseException" title="BaseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseException</span></code></a> instead of <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a> since
it is technically not an error.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ImportError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ImportError</span></span><a class="headerlink" href="#ImportError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement has troubles trying to
load a module.  Also raised when the “from list” in <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">...</span> <span class="pre">import</span></code>
has a name that cannot be found.</p>
<p>The optional <em>name</em> and <em>path</em> keyword-only arguments
set the corresponding attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ImportError.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#ImportError.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module that was attempted to be imported.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ImportError.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#ImportError.path" title="Link to this definition">¶</a></dt>
<dd><p>The path to any file which triggered the exception.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <a class="reference internal" href="#ImportError.name" title="ImportError.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a> and <a class="reference internal" href="#ImportError.path" title="ImportError.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a> attributes.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ModuleNotFoundError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ModuleNotFoundError</span></span><a class="headerlink" href="#ModuleNotFoundError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> which is raised by <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a>
when a module could not be located.  It is also raised when <code class="docutils literal notranslate"><span class="pre">None</span></code>
is found in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="IndexError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IndexError</span></span><a class="headerlink" href="#IndexError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a sequence subscript is out of range.  (Slice indices are
silently truncated to fall in the allowed range; if an index is not an
integer, <a class="reference internal" href="#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised.)</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="KeyError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">KeyError</span></span><a class="headerlink" href="#KeyError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a mapping (dictionary) key is not found in the set of existing keys.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="KeyboardInterrupt">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">KeyboardInterrupt</span></span><a class="headerlink" href="#KeyboardInterrupt" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the user hits the interrupt key (normally <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>-<kbd class="kbd docutils literal notranslate">C</kbd></kbd> or
<kbd class="kbd docutils literal notranslate">Delete</kbd>).  During execution, a check for interrupts is made
regularly. The exception inherits from <a class="reference internal" href="#BaseException" title="BaseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseException</span></code></a> so as to not be
accidentally caught by code that catches <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a> and thus prevent
the interpreter from exiting.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Catching a <a class="reference internal" href="#KeyboardInterrupt" title="KeyboardInterrupt"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyboardInterrupt</span></code></a> requires special consideration.
Because it can be raised at unpredictable points, it may, in some
circumstances, leave the running program in an inconsistent state. It is
generally best to allow <a class="reference internal" href="#KeyboardInterrupt" title="KeyboardInterrupt"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyboardInterrupt</span></code></a> to end the program as
quickly as possible or avoid raising it entirely. (See
<a class="reference internal" href="signal.html#handlers-and-exceptions"><span class="std std-ref">Note on Signal Handlers and Exceptions</span></a>.)</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="MemoryError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">MemoryError</span></span><a class="headerlink" href="#MemoryError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an operation runs out of memory but the situation may still be
rescued (by deleting some objects).  The associated value is a string indicating
what kind of (internal) operation ran out of memory. Note that because of the
underlying memory management architecture (C’s <code class="xref c c-func docutils literal notranslate"><span class="pre">malloc()</span></code> function), the
interpreter may not always be able to completely recover from this situation; it
nevertheless raises an exception so that a stack traceback can be printed, in
case a run-away program was the cause.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="NameError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NameError</span></span><a class="headerlink" href="#NameError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a local or global name is not found.  This applies only to
unqualified names.  The associated value is an error message that includes the
name that could not be found.</p>
<p>The <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> attribute can be set using a keyword-only argument to the
constructor. When set it represent the name of the variable that was attempted
to be accessed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> attribute.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="NotImplementedError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NotImplementedError</span></span><a class="headerlink" href="#NotImplementedError" title="Link to this definition">¶</a></dt>
<dd><p>This exception is derived from <a class="reference internal" href="#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.  In user defined base
classes, abstract methods should raise this exception when they require
derived classes to override the method, or while the class is being
developed to indicate that the real implementation still needs to be added.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It should not be used to indicate that an operator or method is not
meant to be supported at all – in that case either leave the operator /
method undefined or, if a subclass, set it to <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-data docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="docutils literal notranslate"><span class="pre">NotImplementedError</span></code> and <a class="reference internal" href="constants.html#NotImplemented" title="NotImplemented"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotImplemented</span></code></a> are not interchangeable,
even though they have similar names and purposes.  See
<code class="xref py py-data docutils literal notranslate"><span class="pre">NotImplemented</span></code> for details on when to use it.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="OSError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">OSError</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#OSError" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">OSError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">errno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strerror</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">winerror</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">filename2</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span></dt>
<dd><p id="index-5">This exception is raised when a system function returns a system-related
error, including I/O failures such as “file not found” or “disk full”
(not for illegal argument types or other incidental errors).</p>
<p>The second form of the constructor sets the corresponding attributes,
described below.  The attributes default to <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if not
specified.  For backwards compatibility, if three arguments are passed,
the <a class="reference internal" href="#BaseException.args" title="BaseException.args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">args</span></code></a> attribute contains only a 2-tuple
of the first two constructor arguments.</p>
<p>The constructor often actually returns a subclass of <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, as
described in <a class="reference internal" href="#os-exceptions">OS exceptions</a> below.  The particular subclass depends on
the final <a class="reference internal" href="#OSError.errno" title="OSError.errno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">errno</span></code></a> value.  This behaviour only occurs when
constructing <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> directly or via an alias, and is not
inherited when subclassing.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="OSError.errno">
<span class="sig-name descname"><span class="pre">errno</span></span><a class="headerlink" href="#OSError.errno" title="Link to this definition">¶</a></dt>
<dd><p>A numeric error code from the C variable <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="OSError.winerror">
<span class="sig-name descname"><span class="pre">winerror</span></span><a class="headerlink" href="#OSError.winerror" title="Link to this definition">¶</a></dt>
<dd><p>Under Windows, this gives you the native
Windows error code.  The <a class="reference internal" href="#OSError.errno" title="OSError.errno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">errno</span></code></a> attribute is then an approximate
translation, in POSIX terms, of that native error code.</p>
<p>Under Windows, if the <em>winerror</em> constructor argument is an integer,
the <a class="reference internal" href="#OSError.errno" title="OSError.errno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">errno</span></code></a> attribute is determined from the Windows error code,
and the <em>errno</em> argument is ignored.  On other platforms, the
<em>winerror</em> argument is ignored, and the <a class="reference internal" href="#OSError.winerror" title="OSError.winerror"><code class="xref py py-attr docutils literal notranslate"><span class="pre">winerror</span></code></a> attribute
does not exist.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="OSError.strerror">
<span class="sig-name descname"><span class="pre">strerror</span></span><a class="headerlink" href="#OSError.strerror" title="Link to this definition">¶</a></dt>
<dd><p>The corresponding error message, as provided by
the operating system.  It is formatted by the C
functions <code class="xref c c-func docutils literal notranslate"><span class="pre">perror()</span></code> under POSIX, and <code class="xref c c-func docutils literal notranslate"><span class="pre">FormatMessage()</span></code>
under Windows.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="OSError.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#OSError.filename" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="OSError.filename2">
<span class="sig-name descname"><span class="pre">filename2</span></span><a class="headerlink" href="#OSError.filename2" title="Link to this definition">¶</a></dt>
<dd><p>For exceptions that involve a file system path (such as <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> or
<a class="reference internal" href="os.html#os.unlink" title="os.unlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.unlink()</span></code></a>), <a class="reference internal" href="#OSError.filename" title="OSError.filename"><code class="xref py py-attr docutils literal notranslate"><span class="pre">filename</span></code></a> is the file name passed to the function.
For functions that involve two file system paths (such as
<a class="reference internal" href="os.html#os.rename" title="os.rename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rename()</span></code></a>), <a class="reference internal" href="#OSError.filename2" title="OSError.filename2"><code class="xref py py-attr docutils literal notranslate"><span class="pre">filename2</span></code></a> corresponds to the second
file name passed to the function.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="#EnvironmentError" title="EnvironmentError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EnvironmentError</span></code></a>, <a class="reference internal" href="#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a>, <a class="reference internal" href="#WindowsError" title="WindowsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">WindowsError</span></code></a>,
<a class="reference internal" href="socket.html#socket.error" title="socket.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">socket.error</span></code></a>, <a class="reference internal" href="select.html#select.error" title="select.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">select.error</span></code></a> and
<code class="xref py py-exc docutils literal notranslate"><span class="pre">mmap.error</span></code> have been merged into <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, and the
constructor may return a subclass.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The <a class="reference internal" href="#OSError.filename" title="OSError.filename"><code class="xref py py-attr docutils literal notranslate"><span class="pre">filename</span></code></a> attribute is now the original file name passed to
the function, instead of the name encoded to or decoded from the
<a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>. Also, the <em>filename2</em>
constructor argument and attribute was added.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="OverflowError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">OverflowError</span></span><a class="headerlink" href="#OverflowError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the result of an arithmetic operation is too large to be
represented.  This cannot occur for integers (which would rather raise
<a class="reference internal" href="#MemoryError" title="MemoryError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">MemoryError</span></code></a> than give up).  However, for historical reasons,
OverflowError is sometimes raised for integers that are outside a required
range.   Because of the lack of standardization of floating point exception
handling in C, most floating point operations are not checked.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="RecursionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RecursionError</span></span><a class="headerlink" href="#RecursionError" title="Link to this definition">¶</a></dt>
<dd><p>This exception is derived from <a class="reference internal" href="#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.  It is raised when the
interpreter detects that the maximum recursion depth (see
<a class="reference internal" href="sys.html#sys.getrecursionlimit" title="sys.getrecursionlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getrecursionlimit()</span></code></a>) is exceeded.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>Previously, a plain <a class="reference internal" href="#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ReferenceError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ReferenceError</span></span><a class="headerlink" href="#ReferenceError" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised when a weak reference proxy, created by the
<a class="reference internal" href="weakref.html#weakref.proxy" title="weakref.proxy"><code class="xref py py-func docutils literal notranslate"><span class="pre">weakref.proxy()</span></code></a> function, is used to access an attribute of the referent
after it has been garbage collected. For more information on weak references,
see the <a class="reference internal" href="weakref.html#module-weakref" title="weakref: Support for weak references and weak dictionaries."><code class="xref py py-mod docutils literal notranslate"><span class="pre">weakref</span></code></a> module.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="RuntimeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RuntimeError</span></span><a class="headerlink" href="#RuntimeError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an error is detected that doesn’t fall in any of the other
categories.  The associated value is a string indicating what precisely went
wrong.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="StopIteration">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StopIteration</span></span><a class="headerlink" href="#StopIteration" title="Link to this definition">¶</a></dt>
<dd><p>Raised by built-in function <a class="reference internal" href="functions.html#next" title="next"><code class="xref py py-func docutils literal notranslate"><span class="pre">next()</span></code></a> and an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>'s
<a class="reference internal" href="stdtypes.html#iterator.__next__" title="iterator.__next__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__next__()</span></code></a> method to signal that there are no further
items produced by the iterator.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="StopIteration.value">
<span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#StopIteration.value" title="Link to this definition">¶</a></dt>
<dd><p>The exception object has a single attribute <code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code>, which is
given as an argument when constructing the exception, and defaults
to <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</dd></dl>

<p>When a <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generator</span></a> or <a class="reference internal" href="../glossary.html#term-coroutine"><span class="xref std std-term">coroutine</span></a> function
returns, a new <a class="reference internal" href="#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> instance is
raised, and the value returned by the function is used as the
<a class="reference internal" href="#StopIteration.value" title="StopIteration.value"><code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code></a> parameter to the constructor of the exception.</p>
<p>If a generator code directly or indirectly raises <a class="reference internal" href="#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a>,
it is converted into a <a class="reference internal" href="#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> (retaining the
<a class="reference internal" href="#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> as the new exception’s cause).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added <code class="docutils literal notranslate"><span class="pre">value</span></code> attribute and the ability for generator functions to
use it to return a value.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Introduced the RuntimeError transformation via
<code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">generator_stop</span></code>, see <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0479/"><strong>PEP 479</strong></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Enable <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0479/"><strong>PEP 479</strong></a> for all code by default: a <a class="reference internal" href="#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a>
error raised in a generator is transformed into a <a class="reference internal" href="#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="StopAsyncIteration">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StopAsyncIteration</span></span><a class="headerlink" href="#StopAsyncIteration" title="Link to this definition">¶</a></dt>
<dd><p>Must be raised by <a class="reference internal" href="../reference/datamodel.html#object.__anext__" title="object.__anext__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__anext__()</span></code></a> method of an
<a class="reference internal" href="../glossary.html#term-asynchronous-iterator"><span class="xref std std-term">asynchronous iterator</span></a> object to stop the iteration.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="SyntaxError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SyntaxError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">details</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SyntaxError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the parser encounters a syntax error.  This may occur in an
<a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement, in a call to the built-in functions
<a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>, <a class="reference internal" href="functions.html#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a>,
or <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>, or when reading the initial script or standard input
(also interactively).</p>
<p>The <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> of the exception instance returns only the error message.
Details is a tuple whose members are also available as separate attributes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="SyntaxError.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#SyntaxError.filename" title="Link to this definition">¶</a></dt>
<dd><p>The name of the file the syntax error occurred in.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="SyntaxError.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#SyntaxError.lineno" title="Link to this definition">¶</a></dt>
<dd><p>Which line number in the file the error occurred in. This is
1-indexed: the first line in the file has a <code class="docutils literal notranslate"><span class="pre">lineno</span></code> of 1.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="SyntaxError.offset">
<span class="sig-name descname"><span class="pre">offset</span></span><a class="headerlink" href="#SyntaxError.offset" title="Link to this definition">¶</a></dt>
<dd><p>The column in the line where the error occurred. This is
1-indexed: the first character in the line has an <code class="docutils literal notranslate"><span class="pre">offset</span></code> of 1.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="SyntaxError.text">
<span class="sig-name descname"><span class="pre">text</span></span><a class="headerlink" href="#SyntaxError.text" title="Link to this definition">¶</a></dt>
<dd><p>The source code text involved in the error.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="SyntaxError.end_lineno">
<span class="sig-name descname"><span class="pre">end_lineno</span></span><a class="headerlink" href="#SyntaxError.end_lineno" title="Link to this definition">¶</a></dt>
<dd><p>Which line number in the file the error occurred ends in. This is
1-indexed: the first line in the file has a <code class="docutils literal notranslate"><span class="pre">lineno</span></code> of 1.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="SyntaxError.end_offset">
<span class="sig-name descname"><span class="pre">end_offset</span></span><a class="headerlink" href="#SyntaxError.end_offset" title="Link to this definition">¶</a></dt>
<dd><p>The column in the end line where the error occurred finishes. This is
1-indexed: the first character in the line has an <code class="docutils literal notranslate"><span class="pre">offset</span></code> of 1.</p>
</dd></dl>

<p>For errors in f-string fields, the message is prefixed by “f-string: ”
and the offsets are offsets in a text constructed from the replacement
expression.  For example, compiling f’Bad {a b} field’ results in this
args attribute: (‘f-string: …’, (‘’, 1, 2, ‘(a b)n’, 1, 5)).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <a class="reference internal" href="#SyntaxError.end_lineno" title="SyntaxError.end_lineno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">end_lineno</span></code></a> and <a class="reference internal" href="#SyntaxError.end_offset" title="SyntaxError.end_offset"><code class="xref py py-attr docutils literal notranslate"><span class="pre">end_offset</span></code></a> attributes.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="IndentationError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IndentationError</span></span><a class="headerlink" href="#IndentationError" title="Link to this definition">¶</a></dt>
<dd><p>Base class for syntax errors related to incorrect indentation.  This is a
subclass of <a class="reference internal" href="#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="TabError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TabError</span></span><a class="headerlink" href="#TabError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when indentation contains an inconsistent use of tabs and spaces.
This is a subclass of <a class="reference internal" href="#IndentationError" title="IndentationError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndentationError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="SystemError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SystemError</span></span><a class="headerlink" href="#SystemError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the interpreter finds an internal error, but the situation does not
look so serious to cause it to abandon all hope. The associated value is a
string indicating what went wrong (in low-level terms).</p>
<p>You should report this to the author or maintainer of your Python interpreter.
Be sure to report the version of the Python interpreter (<code class="docutils literal notranslate"><span class="pre">sys.version</span></code>; it is
also printed at the start of an interactive Python session), the exact error
message (the exception’s associated value) and if possible the source of the
program that triggered the error.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="SystemExit">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SystemExit</span></span><a class="headerlink" href="#SystemExit" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised by the <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a> function.  It inherits from
<a class="reference internal" href="#BaseException" title="BaseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseException</span></code></a> instead of <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a> so that it is not accidentally
caught by code that catches <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>.  This allows the exception to
properly propagate up and cause the interpreter to exit.  When it is not
handled, the Python interpreter exits; no stack traceback is printed.  The
constructor accepts the same optional argument passed to <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a>.
If the value is an integer, it specifies the system exit status (passed to
C’s <code class="xref c c-func docutils literal notranslate"><span class="pre">exit()</span></code> function); if it is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the exit status is zero; if
it has another type (such as a string), the object’s value is printed and
the exit status is one.</p>
<p>A call to <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a> is translated into an exception so that clean-up
handlers (<a class="reference internal" href="../reference/compound_stmts.html#finally"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">finally</span></code></a> clauses of <a class="reference internal" href="../reference/compound_stmts.html#try"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">try</span></code></a> statements) can be
executed, and so that a debugger can execute a script without running the risk
of losing control.  The <a class="reference internal" href="os.html#os._exit" title="os._exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">os._exit()</span></code></a> function can be used if it is
absolutely positively necessary to exit immediately (for example, in the child
process after a call to <a class="reference internal" href="os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fork()</span></code></a>).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="SystemExit.code">
<span class="sig-name descname"><span class="pre">code</span></span><a class="headerlink" href="#SystemExit.code" title="Link to this definition">¶</a></dt>
<dd><p>The exit status or error message that is passed to the constructor.
(Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.)</p>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="TypeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TypeError</span></span><a class="headerlink" href="#TypeError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an operation or function is applied to an object of inappropriate
type.  The associated value is a string giving details about the type mismatch.</p>
<p>This exception may be raised by user code to indicate that an attempted
operation on an object is not supported, and is not meant to be. If an object
is meant to support a given operation but has not yet provided an
implementation, <a class="reference internal" href="#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> is the proper exception to raise.</p>
<p>Passing arguments of the wrong type (e.g. passing a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> when an
<a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> is expected) should result in a <a class="reference internal" href="#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>, but passing
arguments with the wrong value (e.g. a number outside expected boundaries)
should result in a <a class="reference internal" href="#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UnboundLocalError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnboundLocalError</span></span><a class="headerlink" href="#UnboundLocalError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a reference is made to a local variable in a function or method, but
no value has been bound to that variable.  This is a subclass of
<a class="reference internal" href="#NameError" title="NameError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NameError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UnicodeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnicodeError</span></span><a class="headerlink" href="#UnicodeError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a Unicode-related encoding or decoding error occurs.  It is a
subclass of <a class="reference internal" href="#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p><a class="reference internal" href="#UnicodeError" title="UnicodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeError</span></code></a> has attributes that describe the encoding or decoding
error.  For example, <code class="docutils literal notranslate"><span class="pre">err.object[err.start:err.end]</span></code> gives the particular
invalid input that the codec failed on.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="UnicodeError.encoding">
<span class="sig-name descname"><span class="pre">encoding</span></span><a class="headerlink" href="#UnicodeError.encoding" title="Link to this definition">¶</a></dt>
<dd><p>The name of the encoding that raised the error.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="UnicodeError.reason">
<span class="sig-name descname"><span class="pre">reason</span></span><a class="headerlink" href="#UnicodeError.reason" title="Link to this definition">¶</a></dt>
<dd><p>A string describing the specific codec error.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="UnicodeError.object">
<span class="sig-name descname"><span class="pre">object</span></span><a class="headerlink" href="#UnicodeError.object" title="Link to this definition">¶</a></dt>
<dd><p>The object the codec was attempting to encode or decode.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="UnicodeError.start">
<span class="sig-name descname"><span class="pre">start</span></span><a class="headerlink" href="#UnicodeError.start" title="Link to this definition">¶</a></dt>
<dd><p>The first index of invalid data in <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-attr docutils literal notranslate"><span class="pre">object</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="UnicodeError.end">
<span class="sig-name descname"><span class="pre">end</span></span><a class="headerlink" href="#UnicodeError.end" title="Link to this definition">¶</a></dt>
<dd><p>The index after the last invalid data in <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-attr docutils literal notranslate"><span class="pre">object</span></code></a>.</p>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UnicodeEncodeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnicodeEncodeError</span></span><a class="headerlink" href="#UnicodeEncodeError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a Unicode-related error occurs during encoding.  It is a subclass of
<a class="reference internal" href="#UnicodeError" title="UnicodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UnicodeDecodeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnicodeDecodeError</span></span><a class="headerlink" href="#UnicodeDecodeError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a Unicode-related error occurs during decoding.  It is a subclass of
<a class="reference internal" href="#UnicodeError" title="UnicodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UnicodeTranslateError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnicodeTranslateError</span></span><a class="headerlink" href="#UnicodeTranslateError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a Unicode-related error occurs during translating.  It is a subclass
of <a class="reference internal" href="#UnicodeError" title="UnicodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ValueError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ValueError</span></span><a class="headerlink" href="#ValueError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an operation or function receives an argument that has the
right type but an inappropriate value, and the situation is not described by a
more precise exception such as <a class="reference internal" href="#IndexError" title="IndexError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ZeroDivisionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ZeroDivisionError</span></span><a class="headerlink" href="#ZeroDivisionError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the second argument of a division or modulo operation is zero.  The
associated value is a string indicating the type of the operands and the
operation.</p>
</dd></dl>

<p>The following exceptions are kept for compatibility with previous versions;
starting from Python 3.3, they are aliases of <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="EnvironmentError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">EnvironmentError</span></span><a class="headerlink" href="#EnvironmentError" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="IOError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IOError</span></span><a class="headerlink" href="#IOError" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="WindowsError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">WindowsError</span></span><a class="headerlink" href="#WindowsError" title="Link to this definition">¶</a></dt>
<dd><p>Only available on Windows.</p>
</dd></dl>

<section id="os-exceptions">
<h3>OS exceptions<a class="headerlink" href="#os-exceptions" title="Link to this heading">¶</a></h3>
<p>The following exceptions are subclasses of <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, they get raised
depending on the system error code.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="BlockingIOError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BlockingIOError</span></span><a class="headerlink" href="#BlockingIOError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an operation would block on an object (e.g. socket) set
for non-blocking operation.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.EAGAIN" title="errno.EAGAIN"><code class="xref py py-const docutils literal notranslate"><span class="pre">EAGAIN</span></code></a>, <a class="reference internal" href="errno.html#errno.EALREADY" title="errno.EALREADY"><code class="xref py py-const docutils literal notranslate"><span class="pre">EALREADY</span></code></a>,
<a class="reference internal" href="errno.html#errno.EWOULDBLOCK" title="errno.EWOULDBLOCK"><code class="xref py py-const docutils literal notranslate"><span class="pre">EWOULDBLOCK</span></code></a> and <a class="reference internal" href="errno.html#errno.EINPROGRESS" title="errno.EINPROGRESS"><code class="xref py py-const docutils literal notranslate"><span class="pre">EINPROGRESS</span></code></a>.</p>
<p>In addition to those of <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, <a class="reference internal" href="#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> can have
one more attribute:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="BlockingIOError.characters_written">
<span class="sig-name descname"><span class="pre">characters_written</span></span><a class="headerlink" href="#BlockingIOError.characters_written" title="Link to this definition">¶</a></dt>
<dd><p>An integer containing the number of characters written to the stream
before it blocked.  This attribute is available when using the
buffered I/O classes from the <a class="reference internal" href="io.html#module-io" title="io: Core tools for working with streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code></a> module.</p>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ChildProcessError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ChildProcessError</span></span><a class="headerlink" href="#ChildProcessError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an operation on a child process failed.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ECHILD" title="errno.ECHILD"><code class="xref py py-const docutils literal notranslate"><span class="pre">ECHILD</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ConnectionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ConnectionError</span></span><a class="headerlink" href="#ConnectionError" title="Link to this definition">¶</a></dt>
<dd><p>A base class for connection-related issues.</p>
<p>Subclasses are <a class="reference internal" href="#BrokenPipeError" title="BrokenPipeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BrokenPipeError</span></code></a>, <a class="reference internal" href="#ConnectionAbortedError" title="ConnectionAbortedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionAbortedError</span></code></a>,
<a class="reference internal" href="#ConnectionRefusedError" title="ConnectionRefusedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionRefusedError</span></code></a> and <a class="reference internal" href="#ConnectionResetError" title="ConnectionResetError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionResetError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="BrokenPipeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BrokenPipeError</span></span><a class="headerlink" href="#BrokenPipeError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ConnectionError" title="ConnectionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionError</span></code></a>, raised when trying to write on a
pipe while the other end has been closed, or trying to write on a socket
which has been shutdown for writing.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.EPIPE" title="errno.EPIPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">EPIPE</span></code></a> and <a class="reference internal" href="errno.html#errno.ESHUTDOWN" title="errno.ESHUTDOWN"><code class="xref py py-const docutils literal notranslate"><span class="pre">ESHUTDOWN</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ConnectionAbortedError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ConnectionAbortedError</span></span><a class="headerlink" href="#ConnectionAbortedError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ConnectionError" title="ConnectionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionError</span></code></a>, raised when a connection attempt
is aborted by the peer.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ECONNABORTED" title="errno.ECONNABORTED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ECONNABORTED</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ConnectionRefusedError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ConnectionRefusedError</span></span><a class="headerlink" href="#ConnectionRefusedError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ConnectionError" title="ConnectionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionError</span></code></a>, raised when a connection attempt
is refused by the peer.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ECONNREFUSED" title="errno.ECONNREFUSED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ECONNREFUSED</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ConnectionResetError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ConnectionResetError</span></span><a class="headerlink" href="#ConnectionResetError" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#ConnectionError" title="ConnectionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionError</span></code></a>, raised when a connection is
reset by the peer.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ECONNRESET" title="errno.ECONNRESET"><code class="xref py py-const docutils literal notranslate"><span class="pre">ECONNRESET</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="FileExistsError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FileExistsError</span></span><a class="headerlink" href="#FileExistsError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when trying to create a file or directory which already exists.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.EEXIST" title="errno.EEXIST"><code class="xref py py-const docutils literal notranslate"><span class="pre">EEXIST</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="FileNotFoundError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FileNotFoundError</span></span><a class="headerlink" href="#FileNotFoundError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a file or directory is requested but doesn’t exist.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ENOENT" title="errno.ENOENT"><code class="xref py py-const docutils literal notranslate"><span class="pre">ENOENT</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="InterruptedError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterruptedError</span></span><a class="headerlink" href="#InterruptedError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a system call is interrupted by an incoming signal.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.EINTR" title="errno.EINTR"><code class="xref py py-const docutils literal notranslate"><span class="pre">EINTR</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Python now retries system calls when a syscall is interrupted by a
signal, except if the signal handler raises an exception (see <span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a>
for the rationale), instead of raising <a class="reference internal" href="#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="IsADirectoryError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IsADirectoryError</span></span><a class="headerlink" href="#IsADirectoryError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a file operation (such as <a class="reference internal" href="os.html#os.remove" title="os.remove"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.remove()</span></code></a>) is requested
on a directory.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.EISDIR" title="errno.EISDIR"><code class="xref py py-const docutils literal notranslate"><span class="pre">EISDIR</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="NotADirectoryError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NotADirectoryError</span></span><a class="headerlink" href="#NotADirectoryError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a directory operation (such as <a class="reference internal" href="os.html#os.listdir" title="os.listdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.listdir()</span></code></a>) is requested on
something which is not a directory.  On most POSIX platforms, it may also be
raised if an operation attempts to open or traverse a non-directory file as if
it were a directory.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ENOTDIR" title="errno.ENOTDIR"><code class="xref py py-const docutils literal notranslate"><span class="pre">ENOTDIR</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="PermissionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PermissionError</span></span><a class="headerlink" href="#PermissionError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when trying to run an operation without the adequate access
rights - for example filesystem permissions.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.EACCES" title="errno.EACCES"><code class="xref py py-const docutils literal notranslate"><span class="pre">EACCES</span></code></a>,
<a class="reference internal" href="errno.html#errno.EPERM" title="errno.EPERM"><code class="xref py py-const docutils literal notranslate"><span class="pre">EPERM</span></code></a>, and <a class="reference internal" href="errno.html#errno.ENOTCAPABLE" title="errno.ENOTCAPABLE"><code class="xref py py-const docutils literal notranslate"><span class="pre">ENOTCAPABLE</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.1: </span>WASI’s <a class="reference internal" href="errno.html#errno.ENOTCAPABLE" title="errno.ENOTCAPABLE"><code class="xref py py-const docutils literal notranslate"><span class="pre">ENOTCAPABLE</span></code></a> is now mapped to
<a class="reference internal" href="#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ProcessLookupError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ProcessLookupError</span></span><a class="headerlink" href="#ProcessLookupError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a given process doesn’t exist.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ESRCH" title="errno.ESRCH"><code class="xref py py-const docutils literal notranslate"><span class="pre">ESRCH</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="TimeoutError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TimeoutError</span></span><a class="headerlink" href="#TimeoutError" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a system function timed out at the system level.
Corresponds to <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> <a class="reference internal" href="errno.html#errno.ETIMEDOUT" title="errno.ETIMEDOUT"><code class="xref py py-const docutils literal notranslate"><span class="pre">ETIMEDOUT</span></code></a>.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3: </span>All the above <a class="reference internal" href="#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> subclasses were added.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-3151/"><strong>PEP 3151</strong></a> - Reworking the OS and IO exception hierarchy</p>
</div>
</section>
</section>
<section id="warnings">
<span id="warning-categories-as-exceptions"></span><h2>Warnings<a class="headerlink" href="#warnings" title="Link to this heading">¶</a></h2>
<p>The following exceptions are used as warning categories; see the
<a class="reference internal" href="warnings.html#warning-categories"><span class="std std-ref">Warning Categories</span></a> documentation for more details.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="Warning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Warning</span></span><a class="headerlink" href="#Warning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warning categories.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UserWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UserWarning</span></span><a class="headerlink" href="#UserWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings generated by user code.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="DeprecationWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DeprecationWarning</span></span><a class="headerlink" href="#DeprecationWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings about deprecated features when those warnings are
intended for other Python developers.</p>
<p>Ignored by the default warning filters, except in the <code class="docutils literal notranslate"><span class="pre">__main__</span></code> module
(<span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0565/"><strong>PEP 565</strong></a>). Enabling the <a class="reference internal" href="devmode.html#devmode"><span class="std std-ref">Python Development Mode</span></a> shows
this warning.</p>
<p>The deprecation policy is described in <span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-0387/"><strong>PEP 387</strong></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="PendingDeprecationWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PendingDeprecationWarning</span></span><a class="headerlink" href="#PendingDeprecationWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings about features which are obsolete and
expected to be deprecated in the future, but are not deprecated
at the moment.</p>
<p>This class is rarely used as emitting a warning about a possible
upcoming deprecation is unusual, and <a class="reference internal" href="#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a>
is preferred for already active deprecations.</p>
<p>Ignored by the default warning filters. Enabling the <a class="reference internal" href="devmode.html#devmode"><span class="std std-ref">Python
Development Mode</span></a> shows this warning.</p>
<p>The deprecation policy is described in <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0387/"><strong>PEP 387</strong></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="SyntaxWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SyntaxWarning</span></span><a class="headerlink" href="#SyntaxWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings about dubious syntax.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="RuntimeWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RuntimeWarning</span></span><a class="headerlink" href="#RuntimeWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings about dubious runtime behavior.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="FutureWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FutureWarning</span></span><a class="headerlink" href="#FutureWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings about deprecated features when those warnings are
intended for end users of applications that are written in Python.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ImportWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ImportWarning</span></span><a class="headerlink" href="#ImportWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings about probable mistakes in module imports.</p>
<p>Ignored by the default warning filters. Enabling the <a class="reference internal" href="devmode.html#devmode"><span class="std std-ref">Python
Development Mode</span></a> shows this warning.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="UnicodeWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnicodeWarning</span></span><a class="headerlink" href="#UnicodeWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings related to Unicode.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="EncodingWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">EncodingWarning</span></span><a class="headerlink" href="#EncodingWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings related to encodings.</p>
<p>See <a class="reference internal" href="io.html#io-encoding-warning"><span class="std std-ref">Opt-in EncodingWarning</span></a> for details.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="BytesWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BytesWarning</span></span><a class="headerlink" href="#BytesWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings related to <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ResourceWarning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ResourceWarning</span></span><a class="headerlink" href="#ResourceWarning" title="Link to this definition">¶</a></dt>
<dd><p>Base class for warnings related to resource usage.</p>
<p>Ignored by the default warning filters. Enabling the <a class="reference internal" href="devmode.html#devmode"><span class="std std-ref">Python
Development Mode</span></a> shows this warning.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
<section id="exception-groups">
<span id="lib-exception-groups"></span><h2>Exception groups<a class="headerlink" href="#exception-groups" title="Link to this heading">¶</a></h2>
<p>The following are used when it is necessary to raise multiple unrelated
exceptions. They are part of the exception hierarchy so they can be
handled with <a class="reference internal" href="../reference/compound_stmts.html#except"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">except</span></code></a> like all other exceptions. In addition,
they are recognised by <a class="reference internal" href="../reference/compound_stmts.html#except-star"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">except*</span></code></a>, which matches
their subgroups based on the types of the contained exceptions.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="ExceptionGroup">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExceptionGroup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">excs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ExceptionGroup" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="BaseExceptionGroup">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BaseExceptionGroup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">excs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#BaseExceptionGroup" title="Link to this definition">¶</a></dt>
<dd><p>Both of these exception types wrap the exceptions in the sequence <code class="docutils literal notranslate"><span class="pre">excs</span></code>.
The <code class="docutils literal notranslate"><span class="pre">msg</span></code> parameter must be a string. The difference between the two
classes is that <a class="reference internal" href="#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a> extends <a class="reference internal" href="#BaseException" title="BaseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseException</span></code></a> and
it can wrap any exception, while <a class="reference internal" href="#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a> extends <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>
and it can only wrap subclasses of <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>. This design is so that
<code class="docutils literal notranslate"><span class="pre">except</span> <span class="pre">Exception</span></code> catches an <a class="reference internal" href="#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a> but not
<a class="reference internal" href="#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a>.</p>
<p>The <a class="reference internal" href="#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a> constructor returns an <a class="reference internal" href="#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a>
rather than a <a class="reference internal" href="#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a> if all contained exceptions are
<a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a> instances, so it can be used to make the selection
automatic. The <a class="reference internal" href="#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a> constructor, on the other hand,
raises a <a class="reference internal" href="#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if any contained exception is not an
<a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a> subclass.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="BaseExceptionGroup.message">
<span class="sig-name descname"><span class="pre">message</span></span><a class="headerlink" href="#BaseExceptionGroup.message" title="Link to this definition">¶</a></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">msg</span></code> argument to the constructor. This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="BaseExceptionGroup.exceptions">
<span class="sig-name descname"><span class="pre">exceptions</span></span><a class="headerlink" href="#BaseExceptionGroup.exceptions" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of the exceptions in the <code class="docutils literal notranslate"><span class="pre">excs</span></code> sequence given to the
constructor. This is a read-only attribute.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="BaseExceptionGroup.subgroup">
<span class="sig-name descname"><span class="pre">subgroup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#BaseExceptionGroup.subgroup" title="Link to this definition">¶</a></dt>
<dd><p>Returns an exception group that contains only the exceptions from the
current group that match <em>condition</em>, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the result is empty.</p>
<p>The condition can be either a function that accepts an exception and returns
true for those that should be in the subgroup, or it can be an exception type
or a tuple of exception types, which is used to check for a match using the
same check that is used in an <code class="docutils literal notranslate"><span class="pre">except</span></code> clause.</p>
<p>The nesting structure of the current exception is preserved in the result,
as are the values of its <a class="reference internal" href="#BaseExceptionGroup.message" title="BaseExceptionGroup.message"><code class="xref py py-attr docutils literal notranslate"><span class="pre">message</span></code></a>,
<a class="reference internal" href="#BaseException.__traceback__" title="BaseException.__traceback__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__traceback__</span></code></a>, <a class="reference internal" href="#BaseException.__cause__" title="BaseException.__cause__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code></a>,
<a class="reference internal" href="#BaseException.__context__" title="BaseException.__context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code></a> and
<a class="reference internal" href="#BaseException.__notes__" title="BaseException.__notes__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__notes__</span></code></a> fields.
Empty nested groups are omitted from the result.</p>
<p>The condition is checked for all exceptions in the nested exception group,
including the top-level and any nested exception groups. If the condition is
true for such an exception group, it is included in the result in full.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="BaseExceptionGroup.split">
<span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#BaseExceptionGroup.split" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#BaseExceptionGroup.subgroup" title="BaseExceptionGroup.subgroup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">subgroup()</span></code></a>, but returns the pair <code class="docutils literal notranslate"><span class="pre">(match,</span> <span class="pre">rest)</span></code> where <code class="docutils literal notranslate"><span class="pre">match</span></code>
is <code class="docutils literal notranslate"><span class="pre">subgroup(condition)</span></code> and <code class="docutils literal notranslate"><span class="pre">rest</span></code> is the remaining non-matching
part.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="BaseExceptionGroup.derive">
<span class="sig-name descname"><span class="pre">derive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">excs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#BaseExceptionGroup.derive" title="Link to this definition">¶</a></dt>
<dd><p>Returns an exception group with the same <a class="reference internal" href="#BaseExceptionGroup.message" title="BaseExceptionGroup.message"><code class="xref py py-attr docutils literal notranslate"><span class="pre">message</span></code></a>, but which
wraps the exceptions in <code class="docutils literal notranslate"><span class="pre">excs</span></code>.</p>
<p>This method is used by <a class="reference internal" href="#BaseExceptionGroup.subgroup" title="BaseExceptionGroup.subgroup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">subgroup()</span></code></a> and <a class="reference internal" href="#BaseExceptionGroup.split" title="BaseExceptionGroup.split"><code class="xref py py-meth docutils literal notranslate"><span class="pre">split()</span></code></a>. A
subclass needs to override it in order to make <a class="reference internal" href="#BaseExceptionGroup.subgroup" title="BaseExceptionGroup.subgroup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">subgroup()</span></code></a>
and <a class="reference internal" href="#BaseExceptionGroup.split" title="BaseExceptionGroup.split"><code class="xref py py-meth docutils literal notranslate"><span class="pre">split()</span></code></a> return instances of the subclass rather
than <a class="reference internal" href="#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a>.</p>
<p><a class="reference internal" href="#BaseExceptionGroup.subgroup" title="BaseExceptionGroup.subgroup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">subgroup()</span></code></a> and <a class="reference internal" href="#BaseExceptionGroup.split" title="BaseExceptionGroup.split"><code class="xref py py-meth docutils literal notranslate"><span class="pre">split()</span></code></a> copy the
<a class="reference internal" href="#BaseException.__traceback__" title="BaseException.__traceback__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__traceback__</span></code></a>,
<a class="reference internal" href="#BaseException.__cause__" title="BaseException.__cause__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code></a>, <a class="reference internal" href="#BaseException.__context__" title="BaseException.__context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code></a> and
<a class="reference internal" href="#BaseException.__notes__" title="BaseException.__notes__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__notes__</span></code></a> fields from
the original exception group to the one returned by <a class="reference internal" href="#BaseExceptionGroup.derive" title="BaseExceptionGroup.derive"><code class="xref py py-meth docutils literal notranslate"><span class="pre">derive()</span></code></a>, so
these fields do not need to be updated by <a class="reference internal" href="#BaseExceptionGroup.derive" title="BaseExceptionGroup.derive"><code class="xref py py-meth docutils literal notranslate"><span class="pre">derive()</span></code></a>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyGroup</span><span class="p">(</span><span class="n">ExceptionGroup</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">derive</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">excs</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="n">MyGroup</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">message</span><span class="p">,</span> <span class="n">excs</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">e</span> <span class="o">=</span> <span class="n">MyGroup</span><span class="p">(</span><span class="s2">&quot;eg&quot;</span><span class="p">,</span> <span class="p">[</span><span class="ne">ValueError</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="ne">TypeError</span><span class="p">(</span><span class="mi">2</span><span class="p">)])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">e</span><span class="o">.</span><span class="n">add_note</span><span class="p">(</span><span class="s2">&quot;a note&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">e</span><span class="o">.</span><span class="n">__context__</span> <span class="o">=</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;context&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">e</span><span class="o">.</span><span class="n">__cause__</span> <span class="o">=</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;cause&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">try</span><span class="p">:</span>
<span class="gp">... </span>   <span class="k">raise</span> <span class="n">e</span>
<span class="gp">... </span><span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
<span class="gp">... </span>   <span class="n">exc</span> <span class="o">=</span> <span class="n">e</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">match</span><span class="p">,</span> <span class="n">rest</span> <span class="o">=</span> <span class="n">exc</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="ne">ValueError</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">exc</span><span class="p">,</span> <span class="n">exc</span><span class="o">.</span><span class="n">__context__</span><span class="p">,</span> <span class="n">exc</span><span class="o">.</span><span class="n">__cause__</span><span class="p">,</span> <span class="n">exc</span><span class="o">.</span><span class="n">__notes__</span>
<span class="go">(MyGroup(&#39;eg&#39;, [ValueError(1), TypeError(2)]), Exception(&#39;context&#39;), Exception(&#39;cause&#39;), [&#39;a note&#39;])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">match</span><span class="p">,</span> <span class="n">match</span><span class="o">.</span><span class="n">__context__</span><span class="p">,</span> <span class="n">match</span><span class="o">.</span><span class="n">__cause__</span><span class="p">,</span> <span class="n">match</span><span class="o">.</span><span class="n">__notes__</span>
<span class="go">(MyGroup(&#39;eg&#39;, [ValueError(1)]), Exception(&#39;context&#39;), Exception(&#39;cause&#39;), [&#39;a note&#39;])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">rest</span><span class="p">,</span> <span class="n">rest</span><span class="o">.</span><span class="n">__context__</span><span class="p">,</span> <span class="n">rest</span><span class="o">.</span><span class="n">__cause__</span><span class="p">,</span> <span class="n">rest</span><span class="o">.</span><span class="n">__notes__</span>
<span class="go">(MyGroup(&#39;eg&#39;, [TypeError(2)]), Exception(&#39;context&#39;), Exception(&#39;cause&#39;), [&#39;a note&#39;])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">exc</span><span class="o">.</span><span class="n">__traceback__</span> <span class="ow">is</span> <span class="n">match</span><span class="o">.</span><span class="n">__traceback__</span> <span class="ow">is</span> <span class="n">rest</span><span class="o">.</span><span class="n">__traceback__</span>
<span class="go">True</span>
</pre></div>
</div>
</dd></dl>

<p>Note that <a class="reference internal" href="#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a> defines <a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a>, so
subclasses that need a different constructor signature need to
override that rather than <a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a>. For example, the following
defines an exception group subclass which accepts an exit_code and
and constructs the group’s message from it.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Errors</span><span class="p">(</span><span class="n">ExceptionGroup</span><span class="p">):</span>
   <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">errors</span><span class="p">,</span> <span class="n">exit_code</span><span class="p">):</span>
      <span class="bp">self</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="n">Errors</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;exit code: </span><span class="si">{</span><span class="n">exit_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">errors</span><span class="p">)</span>
      <span class="bp">self</span><span class="o">.</span><span class="n">exit_code</span> <span class="o">=</span> <span class="n">exit_code</span>
      <span class="k">return</span> <span class="bp">self</span>

   <span class="k">def</span> <span class="nf">derive</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">excs</span><span class="p">):</span>
      <span class="k">return</span> <span class="n">Errors</span><span class="p">(</span><span class="n">excs</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">exit_code</span><span class="p">)</span>
</pre></div>
</div>
<p>Like <a class="reference internal" href="#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a>, any subclass of <a class="reference internal" href="#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a> which
is also a subclass of <a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a> can only wrap instances of
<a class="reference internal" href="#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</section>
<section id="exception-hierarchy">
<h2>Exception hierarchy<a class="headerlink" href="#exception-hierarchy" title="Link to this heading">¶</a></h2>
<p>The class hierarchy for built-in exceptions is:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>BaseException
 ├── BaseExceptionGroup
 ├── GeneratorExit
 ├── KeyboardInterrupt
 ├── SystemExit
 └── Exception
      ├── ArithmeticError
      │    ├── FloatingPointError
      │    ├── OverflowError
      │    └── ZeroDivisionError
      ├── AssertionError
      ├── AttributeError
      ├── BufferError
      ├── EOFError
      ├── ExceptionGroup [BaseExceptionGroup]
      ├── ImportError
      │    └── ModuleNotFoundError
      ├── LookupError
      │    ├── IndexError
      │    └── KeyError
      ├── MemoryError
      ├── NameError
      │    └── UnboundLocalError
      ├── OSError
      │    ├── BlockingIOError
      │    ├── ChildProcessError
      │    ├── ConnectionError
      │    │    ├── BrokenPipeError
      │    │    ├── ConnectionAbortedError
      │    │    ├── ConnectionRefusedError
      │    │    └── ConnectionResetError
      │    ├── FileExistsError
      │    ├── FileNotFoundError
      │    ├── InterruptedError
      │    ├── IsADirectoryError
      │    ├── NotADirectoryError
      │    ├── PermissionError
      │    ├── ProcessLookupError
      │    └── TimeoutError
      ├── ReferenceError
      ├── RuntimeError
      │    ├── NotImplementedError
      │    └── RecursionError
      ├── StopAsyncIteration
      ├── StopIteration
      ├── SyntaxError
      │    └── IndentationError
      │         └── TabError
      ├── SystemError
      ├── TypeError
      ├── ValueError
      │    └── UnicodeError
      │         ├── UnicodeDecodeError
      │         ├── UnicodeEncodeError
      │         └── UnicodeTranslateError
      └── Warning
           ├── BytesWarning
           ├── DeprecationWarning
           ├── EncodingWarning
           ├── FutureWarning
           ├── ImportWarning
           ├── PendingDeprecationWarning
           ├── ResourceWarning
           ├── RuntimeWarning
           ├── SyntaxWarning
           ├── UnicodeWarning
           └── UserWarning
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Built-in Exceptions</a><ul>
<li><a class="reference internal" href="#exception-context">Exception context</a></li>
<li><a class="reference internal" href="#inheriting-from-built-in-exceptions">Inheriting from built-in exceptions</a></li>
<li><a class="reference internal" href="#base-classes">Base classes</a></li>
<li><a class="reference internal" href="#concrete-exceptions">Concrete exceptions</a><ul>
<li><a class="reference internal" href="#os-exceptions">OS exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#warnings">Warnings</a></li>
<li><a class="reference internal" href="#exception-groups">Exception groups</a></li>
<li><a class="reference internal" href="#exception-hierarchy">Exception hierarchy</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stdtypes.html"
                          title="previous chapter">Built-in Types</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="text.html"
                          title="next chapter">Text Processing Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/exceptions.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="text.html" title="Text Processing Services"
             >next</a> |</li>
        <li class="right" >
          <a href="stdtypes.html" title="Built-in Types"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Built-in Exceptions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>