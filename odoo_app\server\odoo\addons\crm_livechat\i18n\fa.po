# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_livechat
# 
# Translators:
# <PERSON> <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> Bar<PERSON>hor<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s's New Lead"
msgstr "%s سرنخ جدید"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "چت‌بات اسکریپٹ"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "مرحله اسکریپت چت‌بات"

#. module: crm_livechat
#: model:ir.model.fields.selection,name:crm_livechat.selection__chatbot_script_step__step_type__create_lead
msgid "Create Lead"
msgstr "ایجاد سرنخ"

#. module: crm_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
#: code:addons/crm_livechat/static/src/core/channel_commands.js:0
#, python-format
msgid "Create a new lead (/lead lead title)"
msgstr "ایجاد یک سرنخ جدید (/lead عنوان سرنخ)"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
#, python-format
msgid "Created a new lead: %s"
msgstr "ایجاد یک سرنخ جدید: %s"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "کانال گفتگو"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script__lead_count
msgid "Generated Lead Count"
msgstr "تعداد سرنخ‌های تولید شده"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_welcome
msgid "Hi there, what brings you to our website today? 👋"
msgstr "سلام، چه چیزی شما را امروز به وب‌سایت ما آورده است؟ 👋"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "هوهو، به نظر می‌رسد که هیچ‌یک از اپراتورهای ما در دسترس نیستند 🙁"

#. module: crm_livechat
#: model:chatbot.script,title:crm_livechat.chatbot_script_lead_generation_bot
msgid "Lead Generation Bot"
msgstr "ربات تولید سرنخ"

#. module: crm_livechat
#: model_terms:ir.ui.view,arch_db:crm_livechat.chatbot_script_view_form
msgid "Leads"
msgstr "سرنخ ها"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__crm_team_id
msgid "Sales Team"
msgstr "تیم فروش"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "نوع مرحله"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_just_looking
msgid "Thank you, you should hear back from us very soon!"
msgstr "بسیار سپاسگزاریم، به زودی پاسخی از ما دریافت خواهید کرد!"

#. module: crm_livechat
#: model:ir.model.fields,help:crm_livechat.field_chatbot_script_step__crm_team_id
msgid ""
"Used in combination with 'create_lead' step type in order to automatically "
"assign the created lead/opportunity to the defined team"
msgstr ""
"برای استفاده در ترکیب با نوع گام 'create_lead' به منظور اختصاص خودکار "
"سرنخ/فرصت ایجاد شده به تیم تعریف شده"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr "آیا مایلید آدرس ایمیل خود را بگذارید تا بتوانیم با شما تماس بگیریم؟"
