<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.Notification">
        <div class="o_notification o-fade o-fade-enter-active p-3 m-2 text-bg-dark bg-opacity-75 text-center pe-none" t-att-class="props.className" t-on-click="props.close">
            <span t-esc="props.message"/>
        </div>
    </t>

    <t t-name="point_of_sale.NotificationContainer">
        <div class="o_pos_notification_manager fixed-bottom d-flex flex-column align-items-center justify-content-end p-4 pe-none">
            <t t-foreach="props.notifications" t-as="notification" t-key="notification">
                <Transition name="'o-fade'" visible="notification_value.visible" leaveDuration="leaveDuration" t-slot-scope="transition" onLeave="notification_value.delete">
                    <Notification message="notification_value.message" close="notification_value.close" className="transition.className"/>
                </Transition>
            </t>
        </div>
    </t>

</templates>
