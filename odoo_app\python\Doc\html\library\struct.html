<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="struct — Interpret bytes as packed binary data" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/struct.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/struct.py This module converts between Python values and C structs represented as Python bytes objects. Compact format strings describe the intended conversions to/from Python valu..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/struct.py This module converts between Python values and C structs represented as Python bytes objects. Compact format strings describe the intended conversions to/from Python valu..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>struct — Interpret bytes as packed binary data &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="codecs — Codec registry and base classes" href="codecs.html" />
    <link rel="prev" title="Binary Data Services" href="binary.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/struct.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code> — Interpret bytes as packed binary data</a><ul>
<li><a class="reference internal" href="#functions-and-exceptions">Functions and Exceptions</a></li>
<li><a class="reference internal" href="#format-strings">Format Strings</a><ul>
<li><a class="reference internal" href="#byte-order-size-and-alignment">Byte Order, Size, and Alignment</a></li>
<li><a class="reference internal" href="#format-characters">Format Characters</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
<li><a class="reference internal" href="#applications">Applications</a><ul>
<li><a class="reference internal" href="#native-formats">Native Formats</a></li>
<li><a class="reference internal" href="#standard-formats">Standard Formats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#classes">Classes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="binary.html"
                          title="previous chapter">Binary Data Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="codecs.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">codecs</span></code> — Codec registry and base classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/struct.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="codecs.html" title="codecs — Codec registry and base classes"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="binary.html" title="Binary Data Services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="binary.html" accesskey="U">Binary Data Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code> — Interpret bytes as packed binary data</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-struct">
<span id="struct-interpret-bytes-as-packed-binary-data"></span><h1><a class="reference internal" href="#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> — Interpret bytes as packed binary data<a class="headerlink" href="#module-struct" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/struct.py">Lib/struct.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module converts between Python values and C structs represented
as Python <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects.  Compact <a class="reference internal" href="#struct-format-strings"><span class="std std-ref">format strings</span></a>
describe the intended conversions to/from Python values.
The module’s functions and objects can be used for two largely
distinct applications, data exchange with external sources (files or
network connections), or data transfer between the Python application
and the C layer.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When no prefix character is given, native mode is the default. It
packs or unpacks data based on the platform and compiler on which
the Python interpreter was built.
The result of packing a given C struct includes pad bytes which
maintain proper alignment for the C types involved; similarly,
alignment is taken into account when unpacking.  In contrast, when
communicating data between external sources, the programmer is
responsible for defining byte ordering and padding between elements.
See <a class="reference internal" href="#struct-alignment"><span class="std std-ref">Byte Order, Size, and Alignment</span></a> for details.</p>
</div>
<p>Several <a class="reference internal" href="#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> functions (and methods of <a class="reference internal" href="#struct.Struct" title="struct.Struct"><code class="xref py py-class docutils literal notranslate"><span class="pre">Struct</span></code></a>) take a <em>buffer</em>
argument.  This refers to objects that implement the <a class="reference internal" href="../c-api/buffer.html#bufferobjects"><span class="std std-ref">Buffer Protocol</span></a> and
provide either a readable or read-writable buffer.  The most common types used
for that purpose are <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>, but many other types
that can be viewed as an array of bytes implement the buffer protocol, so that
they can be read/filled without additional copying from a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
<section id="functions-and-exceptions">
<h2>Functions and Exceptions<a class="headerlink" href="#functions-and-exceptions" title="Link to this heading">¶</a></h2>
<p>The module defines the following exception and functions:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="struct.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#struct.error" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised on various occasions; argument is a string describing what
is wrong.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="struct.pack">
<span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">pack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.pack" title="Link to this definition">¶</a></dt>
<dd><p>Return a bytes object containing the values <em>v1</em>, <em>v2</em>, … packed according
to the format string <em>format</em>.  The arguments must match the values required by
the format exactly.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="struct.pack_into">
<span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">pack_into</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.pack_into" title="Link to this definition">¶</a></dt>
<dd><p>Pack the values <em>v1</em>, <em>v2</em>, … according to the format string <em>format</em> and
write the packed bytes into the writable buffer <em>buffer</em> starting at
position <em>offset</em>.  Note that <em>offset</em> is a required argument.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="struct.unpack">
<span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">unpack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.unpack" title="Link to this definition">¶</a></dt>
<dd><p>Unpack from the buffer <em>buffer</em> (presumably packed by <code class="docutils literal notranslate"><span class="pre">pack(format,</span> <span class="pre">...)</span></code>)
according to the format string <em>format</em>.  The result is a tuple even if it
contains exactly one item.  The buffer’s size in bytes must match the
size required by the format, as reflected by <a class="reference internal" href="#struct.calcsize" title="struct.calcsize"><code class="xref py py-func docutils literal notranslate"><span class="pre">calcsize()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="struct.unpack_from">
<span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">unpack_from</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.unpack_from" title="Link to this definition">¶</a></dt>
<dd><p>Unpack from <em>buffer</em> starting at position <em>offset</em>, according to the format
string <em>format</em>.  The result is a tuple even if it contains exactly one
item.  The buffer’s size in bytes, starting at position <em>offset</em>, must be at
least the size required by the format, as reflected by <a class="reference internal" href="#struct.calcsize" title="struct.calcsize"><code class="xref py py-func docutils literal notranslate"><span class="pre">calcsize()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="struct.iter_unpack">
<span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">iter_unpack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.iter_unpack" title="Link to this definition">¶</a></dt>
<dd><p>Iteratively unpack from the buffer <em>buffer</em> according to the format
string <em>format</em>.  This function returns an iterator which will read
equally sized chunks from the buffer until all its contents have been
consumed.  The buffer’s size in bytes must be a multiple of the size
required by the format, as reflected by <a class="reference internal" href="#struct.calcsize" title="struct.calcsize"><code class="xref py py-func docutils literal notranslate"><span class="pre">calcsize()</span></code></a>.</p>
<p>Each iteration yields a tuple as specified by the format string.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="struct.calcsize">
<span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">calcsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.calcsize" title="Link to this definition">¶</a></dt>
<dd><p>Return the size of the struct (and hence of the bytes object produced by
<code class="docutils literal notranslate"><span class="pre">pack(format,</span> <span class="pre">...)</span></code>) corresponding to the format string <em>format</em>.</p>
</dd></dl>

</section>
<section id="format-strings">
<span id="struct-format-strings"></span><h2>Format Strings<a class="headerlink" href="#format-strings" title="Link to this heading">¶</a></h2>
<p>Format strings describe the data layout when
packing and unpacking data.  They are built up from <a class="reference internal" href="#format-characters"><span class="std std-ref">format characters</span></a>,
which specify the type of data being packed/unpacked.  In addition,
special characters control the <a class="reference internal" href="#struct-alignment"><span class="std std-ref">byte order, size and alignment</span></a>.
Each format string consists of an optional prefix character which
describes the overall properties of the data and one or more format
characters which describe the actual data values and padding.</p>
<section id="byte-order-size-and-alignment">
<span id="struct-alignment"></span><h3>Byte Order, Size, and Alignment<a class="headerlink" href="#byte-order-size-and-alignment" title="Link to this heading">¶</a></h3>
<p>By default, C types are represented in the machine’s native format and byte
order, and properly aligned by skipping pad bytes if necessary (according to the
rules used by the C compiler).
This behavior is chosen so
that the bytes of a packed struct correspond exactly to the memory layout
of the corresponding C struct.
Whether to use native byte ordering
and padding or standard formats depends on the application.</p>
<p id="index-1">Alternatively, the first character of the format string can be used to indicate
the byte order, size and alignment of the packed data, according to the
following table:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Character</p></th>
<th class="head"><p>Byte order</p></th>
<th class="head"><p>Size</p></th>
<th class="head"><p>Alignment</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&#64;</span></code></p></td>
<td><p>native</p></td>
<td><p>native</p></td>
<td><p>native</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">=</span></code></p></td>
<td><p>native</p></td>
<td><p>standard</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&lt;</span></code></p></td>
<td><p>little-endian</p></td>
<td><p>standard</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&gt;</span></code></p></td>
<td><p>big-endian</p></td>
<td><p>standard</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">!</span></code></p></td>
<td><p>network (= big-endian)</p></td>
<td><p>standard</p></td>
<td><p>none</p></td>
</tr>
</tbody>
</table>
<p>If the first character is not one of these, <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code> is assumed.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The number 1023 (<code class="docutils literal notranslate"><span class="pre">0x3ff</span></code> in hexadecimal) has the following byte representations:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">03</span> <span class="pre">ff</span></code> in big-endian (<code class="docutils literal notranslate"><span class="pre">&gt;</span></code>)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ff</span> <span class="pre">03</span></code> in little-endian (<code class="docutils literal notranslate"><span class="pre">&lt;</span></code>)</p></li>
</ul>
<p>Python example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">struct</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">struct</span><span class="o">.</span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&gt;h&#39;</span><span class="p">,</span> <span class="mi">1023</span><span class="p">)</span>
<span class="go">b&#39;\x03\xff&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">struct</span><span class="o">.</span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&lt;h&#39;</span><span class="p">,</span> <span class="mi">1023</span><span class="p">)</span>
<span class="go">b&#39;\xff\x03&#39;</span>
</pre></div>
</div>
</div>
<p>Native byte order is big-endian or little-endian, depending on the
host system. For example, Intel x86, AMD64 (x86-64), and Apple M1 are
little-endian; IBM z and many legacy architectures are big-endian.
Use <a class="reference internal" href="sys.html#sys.byteorder" title="sys.byteorder"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.byteorder</span></code></a> to check the endianness of your system.</p>
<p>Native size and alignment are determined using the C compiler’s
<code class="docutils literal notranslate"><span class="pre">sizeof</span></code> expression.  This is always combined with native byte order.</p>
<p>Standard size depends only on the format character;  see the table in
the <a class="reference internal" href="#format-characters"><span class="std std-ref">Format Characters</span></a> section.</p>
<p>Note the difference between <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code> and <code class="docutils literal notranslate"><span class="pre">'='</span></code>: both use native byte order, but
the size and alignment of the latter is standardized.</p>
<p>The form <code class="docutils literal notranslate"><span class="pre">'!'</span></code> represents the network byte order which is always big-endian
as defined in <a class="reference external" href="https://datatracker.ietf.org/doc/html/rfc1700">IETF RFC 1700</a>.</p>
<p>There is no way to indicate non-native byte order (force byte-swapping); use the
appropriate choice of <code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code> or <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code>.</p>
<p>Notes:</p>
<ol class="arabic simple">
<li><p>Padding is only automatically added between successive structure members.
No padding is added at the beginning or the end of the encoded struct.</p></li>
<li><p>No padding is added when using non-native size and alignment, e.g.
with ‘&lt;’, ‘&gt;’, ‘=’, and ‘!’.</p></li>
<li><p>To align the end of a structure to the alignment requirement of a
particular type, end the format with the code for that type with a repeat
count of zero.  See <a class="reference internal" href="#struct-examples"><span class="std std-ref">Examples</span></a>.</p></li>
</ol>
</section>
<section id="format-characters">
<span id="id1"></span><h3>Format Characters<a class="headerlink" href="#format-characters" title="Link to this heading">¶</a></h3>
<p>Format characters have the following meaning; the conversion between C and
Python values should be obvious given their types.  The ‘Standard size’ column
refers to the size of the packed value in bytes when using standard size; that
is, when the format string starts with one of <code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code>, <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code>, <code class="docutils literal notranslate"><span class="pre">'!'</span></code> or
<code class="docutils literal notranslate"><span class="pre">'='</span></code>.  When using native size, the size of the packed value is
platform-dependent.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Format</p></th>
<th class="head"><p>C Type</p></th>
<th class="head"><p>Python type</p></th>
<th class="head"><p>Standard size</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">x</span></code></p></td>
<td><p>pad byte</p></td>
<td><p>no value</p></td>
<td></td>
<td><p>(7)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">c</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">char</span></span></p></td>
<td><p>bytes of length 1</p></td>
<td><p>1</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">b</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">signed</span><span class="w"> </span><span class="kt">char</span></span></p></td>
<td><p>integer</p></td>
<td><p>1</p></td>
<td><p>(1), (2)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">B</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">char</span></span></p></td>
<td><p>integer</p></td>
<td><p>1</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">?</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">_Bool</span></span></p></td>
<td><p>bool</p></td>
<td><p>1</p></td>
<td><p>(1)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">h</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">short</span></span></p></td>
<td><p>integer</p></td>
<td><p>2</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">H</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">short</span></span></p></td>
<td><p>integer</p></td>
<td><p>2</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">i</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">int</span></span></p></td>
<td><p>integer</p></td>
<td><p>4</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">I</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">int</span></span></p></td>
<td><p>integer</p></td>
<td><p>4</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">l</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">long</span></span></p></td>
<td><p>integer</p></td>
<td><p>4</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">L</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span></span></p></td>
<td><p>integer</p></td>
<td><p>4</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">q</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">long</span><span class="w"> </span><span class="kt">long</span></span></p></td>
<td><p>integer</p></td>
<td><p>8</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">Q</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="kt">long</span></span></p></td>
<td><p>integer</p></td>
<td><p>8</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">n</span></code></p></td>
<td><p><code class="xref c c-type docutils literal notranslate"><span class="pre">ssize_t</span></code></p></td>
<td><p>integer</p></td>
<td></td>
<td><p>(3)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">N</span></code></p></td>
<td><p><code class="xref c c-type docutils literal notranslate"><span class="pre">size_t</span></code></p></td>
<td><p>integer</p></td>
<td></td>
<td><p>(3)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">e</span></code></p></td>
<td><p>(6)</p></td>
<td><p>float</p></td>
<td><p>2</p></td>
<td><p>(4)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">f</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">float</span></span></p></td>
<td><p>float</p></td>
<td><p>4</p></td>
<td><p>(4)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">d</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">double</span></span></p></td>
<td><p>float</p></td>
<td><p>8</p></td>
<td><p>(4)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">char</span><span class="p">[</span><span class="p">]</span></span></p></td>
<td><p>bytes</p></td>
<td></td>
<td><p>(9)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">p</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">char</span><span class="p">[</span><span class="p">]</span></span></p></td>
<td><p>bytes</p></td>
<td></td>
<td><p>(8)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">P</span></code></p></td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span></p></td>
<td><p>integer</p></td>
<td></td>
<td><p>(5)</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added support for the <code class="docutils literal notranslate"><span class="pre">'n'</span></code> and <code class="docutils literal notranslate"><span class="pre">'N'</span></code> formats.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added support for the <code class="docutils literal notranslate"><span class="pre">'e'</span></code> format.</p>
</div>
<p>Notes:</p>
<ol class="arabic">
<li><p id="index-2">The <code class="docutils literal notranslate"><span class="pre">'?'</span></code> conversion code corresponds to the <span class="c-expr sig sig-inline c"><span class="kt">_Bool</span></span> type defined by
C99. If this type is not available, it is simulated using a <span class="c-expr sig sig-inline c"><span class="kt">char</span></span>. In
standard mode, it is always represented by one byte.</p>
</li>
<li><p>When attempting to pack a non-integer using any of the integer conversion
codes, if the non-integer has a <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> method then that method is
called to convert the argument to an integer before packing.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added use of the <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> method for non-integers.</p>
</div>
</li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">'n'</span></code> and <code class="docutils literal notranslate"><span class="pre">'N'</span></code> conversion codes are only available for the native
size (selected as the default or with the <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code> byte order character).
For the standard size, you can use whichever of the other integer formats
fits your application.</p></li>
<li><p>For the <code class="docutils literal notranslate"><span class="pre">'f'</span></code>, <code class="docutils literal notranslate"><span class="pre">'d'</span></code> and <code class="docutils literal notranslate"><span class="pre">'e'</span></code> conversion codes, the packed
representation uses the IEEE 754 binary32, binary64 or binary16 format (for
<code class="docutils literal notranslate"><span class="pre">'f'</span></code>, <code class="docutils literal notranslate"><span class="pre">'d'</span></code> or <code class="docutils literal notranslate"><span class="pre">'e'</span></code> respectively), regardless of the floating-point
format used by the platform.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">'P'</span></code> format character is only available for the native byte ordering
(selected as the default or with the <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code> byte order character). The byte
order character <code class="docutils literal notranslate"><span class="pre">'='</span></code> chooses to use little- or big-endian ordering based
on the host system. The struct module does not interpret this as native
ordering, so the <code class="docutils literal notranslate"><span class="pre">'P'</span></code> format is not available.</p></li>
<li><p>The IEEE 754 binary16 “half precision” type was introduced in the 2008
revision of the <a class="reference external" href="https://en.wikipedia.org/wiki/IEEE_754-2008_revision">IEEE 754 standard</a>. It has a sign
bit, a 5-bit exponent and 11-bit precision (with 10 bits explicitly stored),
and can represent numbers between approximately <code class="docutils literal notranslate"><span class="pre">6.1e-05</span></code> and <code class="docutils literal notranslate"><span class="pre">6.5e+04</span></code>
at full precision. This type is not widely supported by C compilers: on a
typical machine, an unsigned short can be used for storage, but not for math
operations. See the Wikipedia page on the <a class="reference external" href="https://en.wikipedia.org/wiki/Half-precision_floating-point_format">half-precision floating-point
format</a> for more information.</p></li>
<li><p>When packing, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> inserts one NUL byte.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">'p'</span></code> format character encodes a “Pascal string”, meaning a short
variable-length string stored in a <em>fixed number of bytes</em>, given by the count.
The first byte stored is the length of the string, or 255, whichever is
smaller.  The bytes of the string follow.  If the string passed in to
<a class="reference internal" href="#struct.pack" title="struct.pack"><code class="xref py py-func docutils literal notranslate"><span class="pre">pack()</span></code></a> is too long (longer than the count minus 1), only the leading
<code class="docutils literal notranslate"><span class="pre">count-1</span></code> bytes of the string are stored.  If the string is shorter than
<code class="docutils literal notranslate"><span class="pre">count-1</span></code>, it is padded with null bytes so that exactly count bytes in all
are used.  Note that for <a class="reference internal" href="#struct.unpack" title="struct.unpack"><code class="xref py py-func docutils literal notranslate"><span class="pre">unpack()</span></code></a>, the <code class="docutils literal notranslate"><span class="pre">'p'</span></code> format character consumes
<code class="docutils literal notranslate"><span class="pre">count</span></code> bytes, but that the string returned can never contain more than 255
bytes.</p></li>
<li><p>For the <code class="docutils literal notranslate"><span class="pre">'s'</span></code> format character, the count is interpreted as the length of the
bytes, not a repeat count like for the other format characters; for example,
<code class="docutils literal notranslate"><span class="pre">'10s'</span></code> means a single 10-byte string mapping to or from a single
Python byte string, while <code class="docutils literal notranslate"><span class="pre">'10c'</span></code> means 10
separate one byte character elements (e.g., <code class="docutils literal notranslate"><span class="pre">cccccccccc</span></code>) mapping
to or from ten different Python byte objects. (See <a class="reference internal" href="#struct-examples"><span class="std std-ref">Examples</span></a>
for a concrete demonstration of the difference.)
If a count is not given, it defaults to 1.  For packing, the string is
truncated or padded with null bytes as appropriate to make it fit. For
unpacking, the resulting bytes object always has exactly the specified number
of bytes.  As a special case, <code class="docutils literal notranslate"><span class="pre">'0s'</span></code> means a single, empty string (while
<code class="docutils literal notranslate"><span class="pre">'0c'</span></code> means 0 characters).</p></li>
</ol>
<p>A format character may be preceded by an integral repeat count.  For example,
the format string <code class="docutils literal notranslate"><span class="pre">'4h'</span></code> means exactly the same as <code class="docutils literal notranslate"><span class="pre">'hhhh'</span></code>.</p>
<p>Whitespace characters between formats are ignored; a count and its format must
not contain whitespace though.</p>
<p>When packing a value <code class="docutils literal notranslate"><span class="pre">x</span></code> using one of the integer formats (<code class="docutils literal notranslate"><span class="pre">'b'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'B'</span></code>, <code class="docutils literal notranslate"><span class="pre">'h'</span></code>, <code class="docutils literal notranslate"><span class="pre">'H'</span></code>, <code class="docutils literal notranslate"><span class="pre">'i'</span></code>, <code class="docutils literal notranslate"><span class="pre">'I'</span></code>, <code class="docutils literal notranslate"><span class="pre">'l'</span></code>, <code class="docutils literal notranslate"><span class="pre">'L'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'q'</span></code>, <code class="docutils literal notranslate"><span class="pre">'Q'</span></code>), if <code class="docutils literal notranslate"><span class="pre">x</span></code> is outside the valid range for that format
then <a class="reference internal" href="#struct.error" title="struct.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">struct.error</span></code></a> is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Previously, some of the integer formats wrapped out-of-range values and
raised <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> instead of <a class="reference internal" href="#struct.error" title="struct.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">struct.error</span></code></a>.</p>
</div>
<p id="index-3">For the <code class="docutils literal notranslate"><span class="pre">'?'</span></code> format character, the return value is either <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> or
<a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>. When packing, the truth value of the argument object is used.
Either 0 or 1 in the native or standard bool representation will be packed, and
any non-zero value will be <code class="docutils literal notranslate"><span class="pre">True</span></code> when unpacking.</p>
</section>
<section id="examples">
<span id="struct-examples"></span><h3>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h3>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Native byte order examples (designated by the <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code> format prefix or
lack of any prefix character) may not match what the reader’s
machine produces as
that depends on the platform and compiler.</p>
</div>
<p>Pack and unpack integers of three different sizes, using big endian
ordering:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">struct</span> <span class="kn">import</span> <span class="o">*</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s2">&quot;&gt;bhl&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">b&#39;\x01\x00\x02\x00\x00\x00\x03&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unpack</span><span class="p">(</span><span class="s1">&#39;&gt;bhl&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x01\x00\x02\x00\x00\x00\x03</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="go">(1, 2, 3)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;&gt;bhl&#39;</span><span class="p">)</span>
<span class="go">7</span>
</pre></div>
</div>
<p>Attempt to pack an integer which is too large for the defined field:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s2">&quot;&gt;h&quot;</span><span class="p">,</span> <span class="mi">99999</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="gr">struct.error</span>: <span class="n">&#39;h&#39; format requires -32768 &lt;= number &lt;= 32767</span>
</pre></div>
</div>
<p>Demonstrate the difference between <code class="docutils literal notranslate"><span class="pre">'s'</span></code> and <code class="docutils literal notranslate"><span class="pre">'c'</span></code> format
characters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s2">&quot;@ccc&quot;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;1&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;2&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;3&#39;</span><span class="p">)</span>
<span class="go">b&#39;123&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s2">&quot;@3s&quot;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;123&#39;</span><span class="p">)</span>
<span class="go">b&#39;123&#39;</span>
</pre></div>
</div>
<p>Unpacked fields can be named by assigning them to variables or by wrapping
the result in a named tuple:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">record</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;raymond   </span><span class="se">\x32\x12\x08\x01\x08</span><span class="s1">&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">name</span><span class="p">,</span> <span class="n">serialnum</span><span class="p">,</span> <span class="n">school</span><span class="p">,</span> <span class="n">gradelevel</span> <span class="o">=</span> <span class="n">unpack</span><span class="p">(</span><span class="s1">&#39;&lt;10sHHb&#39;</span><span class="p">,</span> <span class="n">record</span><span class="p">)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">collections</span> <span class="kn">import</span> <span class="n">namedtuple</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Student</span> <span class="o">=</span> <span class="n">namedtuple</span><span class="p">(</span><span class="s1">&#39;Student&#39;</span><span class="p">,</span> <span class="s1">&#39;name serialnum school gradelevel&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Student</span><span class="o">.</span><span class="n">_make</span><span class="p">(</span><span class="n">unpack</span><span class="p">(</span><span class="s1">&#39;&lt;10sHHb&#39;</span><span class="p">,</span> <span class="n">record</span><span class="p">))</span>
<span class="go">Student(name=b&#39;raymond   &#39;, serialnum=4658, school=264, gradelevel=8)</span>
</pre></div>
</div>
<p>The ordering of format characters may have an impact on size in native
mode since padding is implicit. In standard mode, the user is
responsible for inserting any desired padding.
Note in
the first <code class="docutils literal notranslate"><span class="pre">pack</span></code> call below that three NUL bytes were added after the
packed <code class="docutils literal notranslate"><span class="pre">'#'</span></code> to align the following integer on a four-byte boundary.
In this example, the output was produced on a little endian machine:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@ci&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;#&#39;</span><span class="p">,</span> <span class="mh">0x12131415</span><span class="p">)</span>
<span class="go">b&#39;#\x00\x00\x00\x15\x14\x13\x12&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@ic&#39;</span><span class="p">,</span> <span class="mh">0x12131415</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;#&#39;</span><span class="p">)</span>
<span class="go">b&#39;\x15\x14\x13\x12#&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@ci&#39;</span><span class="p">)</span>
<span class="go">8</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@ic&#39;</span><span class="p">)</span>
<span class="go">5</span>
</pre></div>
</div>
<p>The following format <code class="docutils literal notranslate"><span class="pre">'llh0l'</span></code> results in two pad bytes being added
at the end, assuming the platform’s longs are aligned on 4-byte boundaries:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@llh0l&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">b&#39;\x00\x00\x00\x01\x00\x00\x00\x02\x00\x03\x00\x00&#39;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="array.html#module-array" title="array: Space efficient arrays of uniformly typed numeric values."><code class="xref py py-mod docutils literal notranslate"><span class="pre">array</span></code></a></dt><dd><p>Packed binary storage of homogeneous data.</p>
</dd>
<dt>Module <a class="reference internal" href="json.html#module-json" title="json: Encode and decode the JSON format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code></a></dt><dd><p>JSON encoder and decoder.</p>
</dd>
<dt>Module <a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a></dt><dd><p>Python object serialization.</p>
</dd>
</dl>
</div>
</section>
</section>
<section id="applications">
<span id="id2"></span><h2>Applications<a class="headerlink" href="#applications" title="Link to this heading">¶</a></h2>
<p>Two main applications for the <a class="reference internal" href="#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> module exist, data
interchange between Python and C code within an application or another
application compiled using the same compiler (<a class="reference internal" href="#struct-native-formats"><span class="std std-ref">native formats</span></a>), and
data interchange between applications using agreed upon data layout
(<a class="reference internal" href="#struct-standard-formats"><span class="std std-ref">standard formats</span></a>).  Generally speaking, the format strings
constructed for these two domains are distinct.</p>
<section id="native-formats">
<span id="struct-native-formats"></span><h3>Native Formats<a class="headerlink" href="#native-formats" title="Link to this heading">¶</a></h3>
<p>When constructing format strings which mimic native layouts, the
compiler and machine architecture determine byte ordering and padding.
In such cases, the <code class="docutils literal notranslate"><span class="pre">&#64;</span></code> format character should be used to specify
native byte ordering and data sizes.  Internal pad bytes are normally inserted
automatically.  It is possible that a zero-repeat format code will be
needed at the end of a format string to round up to the correct
byte boundary for proper alignment of consecutive chunks of data.</p>
<p>Consider these two simple examples (on a 64-bit, little-endian
machine):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@lhl&#39;</span><span class="p">)</span>
<span class="go">24</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@llh&#39;</span><span class="p">)</span>
<span class="go">18</span>
</pre></div>
</div>
<p>Data is not padded to an 8-byte boundary at the end of the second
format string without the use of extra padding.  A zero-repeat format
code solves that problem:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@llh0l&#39;</span><span class="p">)</span>
<span class="go">24</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">'x'</span></code> format code can be used to specify the repeat, but for
native formats it is better to use a zero-repeat format like <code class="docutils literal notranslate"><span class="pre">'0l'</span></code>.</p>
<p>By default, native byte ordering and alignment is used, but it is
better to be explicit and use the <code class="docutils literal notranslate"><span class="pre">'&#64;'</span></code> prefix character.</p>
</section>
<section id="standard-formats">
<span id="struct-standard-formats"></span><h3>Standard Formats<a class="headerlink" href="#standard-formats" title="Link to this heading">¶</a></h3>
<p>When exchanging data beyond your process such as networking or storage,
be precise.  Specify the exact byte order, size, and alignment.  Do
not assume they match the native order of a particular machine.
For example, network byte order is big-endian, while many popular CPUs
are little-endian.  By defining this explicitly, the user need not
care about the specifics of the platform their code is running on.
The first character should typically be <code class="docutils literal notranslate"><span class="pre">&lt;</span></code> or <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>
(or <code class="docutils literal notranslate"><span class="pre">!</span></code>).  Padding is the responsibility of the programmer.  The
zero-repeat format character won’t work.  Instead, the user must
explicitly add <code class="docutils literal notranslate"><span class="pre">'x'</span></code> pad bytes where needed.  Revisiting the
examples from the previous section, we have:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;&lt;qh6xq&#39;</span><span class="p">)</span>
<span class="go">24</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&lt;qh6xq&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span> <span class="o">==</span> <span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@lhl&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@llh&#39;</span><span class="p">)</span>
<span class="go">18</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@llh&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span> <span class="o">==</span> <span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&lt;qqh&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;&lt;qqh6x&#39;</span><span class="p">)</span>
<span class="go">24</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@llh0l&#39;</span><span class="p">)</span>
<span class="go">24</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@llh0l&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span> <span class="o">==</span> <span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&lt;qqh6x&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<p>The above results (executed on a 64-bit machine) aren’t guaranteed to
match when executed on different machines.  For example, the examples
below were executed on a 32-bit machine:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;&lt;qqh6x&#39;</span><span class="p">)</span>
<span class="go">24</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">calcsize</span><span class="p">(</span><span class="s1">&#39;@llh0l&#39;</span><span class="p">)</span>
<span class="go">12</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;@llh0l&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span> <span class="o">==</span> <span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&lt;qqh6x&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
</section>
</section>
<section id="classes">
<span id="struct-objects"></span><h2>Classes<a class="headerlink" href="#classes" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> module also defines the following type:</p>
<dl class="py class">
<dt class="sig sig-object py" id="struct.Struct">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">struct.</span></span><span class="sig-name descname"><span class="pre">Struct</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.Struct" title="Link to this definition">¶</a></dt>
<dd><p>Return a new Struct object which writes and reads binary data according to
the format string <em>format</em>.  Creating a <code class="docutils literal notranslate"><span class="pre">Struct</span></code> object once and calling its
methods is more efficient than calling module-level functions with the
same format since the format string is only compiled once.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The compiled versions of the most recent format strings passed to
the module-level functions are cached, so programs that use only a few
format strings needn’t worry about reusing a single <a class="reference internal" href="#struct.Struct" title="struct.Struct"><code class="xref py py-class docutils literal notranslate"><span class="pre">Struct</span></code></a>
instance.</p>
</div>
<p>Compiled Struct objects support the following methods and attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="struct.Struct.pack">
<span class="sig-name descname"><span class="pre">pack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">v1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.Struct.pack" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#struct.pack" title="struct.pack"><code class="xref py py-func docutils literal notranslate"><span class="pre">pack()</span></code></a> function, using the compiled format.
(<code class="docutils literal notranslate"><span class="pre">len(result)</span></code> will equal <a class="reference internal" href="#struct.Struct.size" title="struct.Struct.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a>.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="struct.Struct.pack_into">
<span class="sig-name descname"><span class="pre">pack_into</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.Struct.pack_into" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#struct.pack_into" title="struct.pack_into"><code class="xref py py-func docutils literal notranslate"><span class="pre">pack_into()</span></code></a> function, using the compiled format.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="struct.Struct.unpack">
<span class="sig-name descname"><span class="pre">unpack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.Struct.unpack" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#struct.unpack" title="struct.unpack"><code class="xref py py-func docutils literal notranslate"><span class="pre">unpack()</span></code></a> function, using the compiled format.
The buffer’s size in bytes must equal <a class="reference internal" href="#struct.Struct.size" title="struct.Struct.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="struct.Struct.unpack_from">
<span class="sig-name descname"><span class="pre">unpack_from</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.Struct.unpack_from" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#struct.unpack_from" title="struct.unpack_from"><code class="xref py py-func docutils literal notranslate"><span class="pre">unpack_from()</span></code></a> function, using the compiled format.
The buffer’s size in bytes, starting at position <em>offset</em>, must be at least
<a class="reference internal" href="#struct.Struct.size" title="struct.Struct.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="struct.Struct.iter_unpack">
<span class="sig-name descname"><span class="pre">iter_unpack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#struct.Struct.iter_unpack" title="Link to this definition">¶</a></dt>
<dd><p>Identical to the <a class="reference internal" href="#struct.iter_unpack" title="struct.iter_unpack"><code class="xref py py-func docutils literal notranslate"><span class="pre">iter_unpack()</span></code></a> function, using the compiled format.
The buffer’s size in bytes must be a multiple of <a class="reference internal" href="#struct.Struct.size" title="struct.Struct.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="struct.Struct.format">
<span class="sig-name descname"><span class="pre">format</span></span><a class="headerlink" href="#struct.Struct.format" title="Link to this definition">¶</a></dt>
<dd><p>The format string used to construct this Struct object.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The format string type is now <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> instead of <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="struct.Struct.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#struct.Struct.size" title="Link to this definition">¶</a></dt>
<dd><p>The calculated size of the struct (and hence of the bytes object produced
by the <a class="reference internal" href="#struct.pack" title="struct.pack"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pack()</span></code></a> method) corresponding to <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-attr docutils literal notranslate"><span class="pre">format</span></code></a>.</p>
</dd></dl>

</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code> — Interpret bytes as packed binary data</a><ul>
<li><a class="reference internal" href="#functions-and-exceptions">Functions and Exceptions</a></li>
<li><a class="reference internal" href="#format-strings">Format Strings</a><ul>
<li><a class="reference internal" href="#byte-order-size-and-alignment">Byte Order, Size, and Alignment</a></li>
<li><a class="reference internal" href="#format-characters">Format Characters</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
<li><a class="reference internal" href="#applications">Applications</a><ul>
<li><a class="reference internal" href="#native-formats">Native Formats</a></li>
<li><a class="reference internal" href="#standard-formats">Standard Formats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#classes">Classes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="binary.html"
                          title="previous chapter">Binary Data Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="codecs.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">codecs</span></code> — Codec registry and base classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/struct.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="codecs.html" title="codecs — Codec registry and base classes"
             >next</a> |</li>
        <li class="right" >
          <a href="binary.html" title="Binary Data Services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="binary.html" >Binary Data Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code> — Interpret bytes as packed binary data</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>