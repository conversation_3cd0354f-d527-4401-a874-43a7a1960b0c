# PowerShell script to start Odoo with local Python environment

Write-Host "========================================" -ForegroundColor Green
Write-Host "Starting Odoo with Local Python Environment" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set the local Python path
$PythonPath = Join-Path $PSScriptRoot "python"
$env:PATH = "$PythonPath;$PythonPath\Scripts;$env:PATH"

# Display Python version
Write-Host "Using Python:" -ForegroundColor Yellow
& "$PythonPath\python.exe" --version

Write-Host ""
Write-Host "Starting Odoo server..." -ForegroundColor Cyan
Write-Host "Database: jewelry_db" -ForegroundColor White
Write-Host "Port: 8069" -ForegroundColor White
Write-Host "URL: http://127.0.0.1:8069" -ForegroundColor White
Write-Host ""

# Start Odoo with the local Python
try {
    & "$PythonPath\python.exe" start_odoo.py -c odoo.conf
}
catch {
    Write-Host "Error starting Odoo: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
