<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="ftplib — FTP protocol client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/ftplib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/ftplib.py This module defines the class FTP and a few related items. The FTP class implements the client side of the FTP protocol. You can use this to write Python programs that pe..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/ftplib.py This module defines the class FTP and a few related items. The FTP class implements the client side of the FTP protocol. You can use this to write Python programs that pe..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>ftplib — FTP protocol client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="poplib — POP3 protocol client" href="poplib.html" />
    <link rel="prev" title="http.client — HTTP protocol client" href="http.client.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/ftplib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a><ul>
<li><a class="reference internal" href="#reference">Reference</a><ul>
<li><a class="reference internal" href="#ftp-objects">FTP objects</a></li>
<li><a class="reference internal" href="#ftp-tls-objects">FTP_TLS objects</a></li>
<li><a class="reference internal" href="#module-variables">Module variables</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.client.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="poplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ftplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="poplib.html" title="poplib — POP3 protocol client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="http.client.html" title="http.client — HTTP protocol client"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-ftplib">
<span id="ftplib-ftp-protocol-client"></span><h1><a class="reference internal" href="#module-ftplib" title="ftplib: FTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code></a> — FTP protocol client<a class="headerlink" href="#module-ftplib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/ftplib.py">Lib/ftplib.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module defines the class <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> and a few related items. The
<a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> class implements the client side of the FTP protocol.  You can use
this to write Python programs that perform a variety of automated FTP jobs, such
as mirroring other FTP servers.  It is also used by the module
<a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> to handle URLs that use FTP.  For more information on FTP
(File Transfer Protocol), see internet <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc959.html"><strong>RFC 959</strong></a>.</p>
<p>The default encoding is UTF-8, following <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2640.html"><strong>RFC 2640</strong></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>Here’s a sample session using the <a class="reference internal" href="#module-ftplib" title="ftplib: FTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code></a> module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">ftplib</span> <span class="kn">import</span> <span class="n">FTP</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftp</span> <span class="o">=</span> <span class="n">FTP</span><span class="p">(</span><span class="s1">&#39;ftp.us.debian.org&#39;</span><span class="p">)</span>  <span class="c1"># connect to host, default port</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftp</span><span class="o">.</span><span class="n">login</span><span class="p">()</span>                     <span class="c1"># user anonymous, passwd anonymous@</span>
<span class="go">&#39;230 Login successful.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftp</span><span class="o">.</span><span class="n">cwd</span><span class="p">(</span><span class="s1">&#39;debian&#39;</span><span class="p">)</span>               <span class="c1"># change into &quot;debian&quot; directory</span>
<span class="go">&#39;250 Directory successfully changed.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftp</span><span class="o">.</span><span class="n">retrlines</span><span class="p">(</span><span class="s1">&#39;LIST&#39;</span><span class="p">)</span>           <span class="c1"># list directory contents</span>
<span class="go">-rw-rw-r--    1 <USER>     <GROUP>         1063 Jun 15 10:18 README</span>
<span class="go">...</span>
<span class="go">drwxr-sr-x    5 1176     1176         4096 Dec 19  2000 pool</span>
<span class="go">drwxr-sr-x    4 1176     1176         4096 Nov 17  2008 project</span>
<span class="go">drwxr-xr-x    3 <USER>     <GROUP>         4096 Oct 10  2012 tools</span>
<span class="go">&#39;226 Directory send OK.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;README&#39;</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">fp</span><span class="p">:</span>
<span class="gp">&gt;&gt;&gt; </span>    <span class="n">ftp</span><span class="o">.</span><span class="n">retrbinary</span><span class="p">(</span><span class="s1">&#39;RETR README&#39;</span><span class="p">,</span> <span class="n">fp</span><span class="o">.</span><span class="n">write</span><span class="p">)</span>
<span class="go">&#39;226 Transfer complete.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftp</span><span class="o">.</span><span class="n">quit</span><span class="p">()</span>
<span class="go">&#39;221 Goodbye.&#39;</span>
</pre></div>
</div>
<section id="reference">
<span id="ftplib-reference"></span><h2>Reference<a class="headerlink" href="#reference" title="Link to this heading">¶</a></h2>
<section id="ftp-objects">
<span id="id1"></span><h3>FTP objects<a class="headerlink" href="#ftp-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="ftplib.FTP">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">FTP</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">passwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">acct</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_address</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'utf-8'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP" title="Link to this definition">¶</a></dt>
<dd><p>Return a new instance of the <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> class.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>host</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The hostname to connect to.
If given, <code class="code docutils literal notranslate"><span class="pre">connect(host)</span></code> is implicitly called by the constructor.</p></li>
<li><p><strong>user</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The username to log in with (default: <code class="docutils literal notranslate"><span class="pre">'anonymous'</span></code>).
If given, <code class="code docutils literal notranslate"><span class="pre">login(host,</span> <span class="pre">passwd,</span> <span class="pre">acct)</span></code> is implicitly called
by the constructor.</p></li>
<li><p><strong>passwd</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The password to use when logging in.
If not given, and if <em>passwd</em> is the empty string or <code class="docutils literal notranslate"><span class="pre">&quot;-&quot;</span></code>,
a password will be automatically generated.</p></li>
<li><p><strong>acct</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – Account information to be used for the <code class="docutils literal notranslate"><span class="pre">ACCT</span></code> FTP command.
Few systems implement this.
See <a class="reference external" href="https://datatracker.ietf.org/doc/html/rfc959.html">RFC-959</a>
for more details.</p></li>
<li><p><strong>timeout</strong> (<a class="reference internal" href="functions.html#float" title="float"><em>float</em></a><em> | </em><em>None</em>) – A timeout in seconds for blocking operations like <a class="reference internal" href="#ftplib.FTP.connect" title="ftplib.FTP.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a>
(default: the global default timeout setting).</p></li>
<li><p><strong>source_address</strong> (<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><em>tuple</em></a><em> | </em><em>None</em>) – A 2-tuple <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> for the socket to bind to as its
source address before connecting.</p></li>
<li><p><strong>encoding</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The encoding for directories and filenames (default: <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code>).</p></li>
</ul>
</dd>
</dl>
<p>The <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> class supports the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement, e.g.:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">ftplib</span> <span class="kn">import</span> <span class="n">FTP</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">FTP</span><span class="p">(</span><span class="s2">&quot;ftp1.at.proftpd.org&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">ftp</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">ftp</span><span class="o">.</span><span class="n">login</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">ftp</span><span class="o">.</span><span class="n">dir</span><span class="p">()</span>
<span class="gp">... </span>
<span class="go">&#39;230 Anonymous login ok, restrictions apply.&#39;</span>
<span class="go">dr-xr-xr-x   9 <USER>      <GROUP>           154 May  6 10:43 .</span>
<span class="go">dr-xr-xr-x   9 <USER>      <GROUP>           154 May  6 10:43 ..</span>
<span class="go">dr-xr-xr-x   5 <USER>      <GROUP>          4096 May  6 10:43 CentOS</span>
<span class="go">dr-xr-xr-x   3 <USER>      <GROUP>            18 Jul 10  2008 Fedora</span>
<span class="gp">&gt;&gt;&gt;</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Support for the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>source_address</em> parameter was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.
The <em>encoding</em> parameter was added, and the default was changed from
Latin-1 to UTF-8 to follow <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2640.html"><strong>RFC 2640</strong></a>.</p>
</div>
<p>Several <code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code> methods are available in two flavors:
one for handling text files and another for binary files.
The methods are named for the command which is used followed by
<code class="docutils literal notranslate"><span class="pre">lines</span></code> for the text version or <code class="docutils literal notranslate"><span class="pre">binary</span></code> for the binary version.</p>
<p><a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.set_debuglevel">
<span class="sig-name descname"><span class="pre">set_debuglevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.set_debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>Set the instance’s debugging level as an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.
This controls the amount of debugging output printed.
The debug levels are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">0</span></code> (default): No debug output.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">1</span></code>: Produce a moderate amount of debug output,
generally a single line per request.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">2</span></code> or higher: Produce the maximum amount of debugging output,
logging each line sent and received on the control connection.</p></li>
</ul>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.connect">
<span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_address</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.connect" title="Link to this definition">¶</a></dt>
<dd><p>Connect to the given host and port.
This function should be called only once for each instance;
it should not be called if a <em>host</em> argument was given
when the <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> instance was created.
All other <code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code> methods can only be called
after a connection has successfully been made.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>host</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The host to connect to.</p></li>
<li><p><strong>port</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The TCP port to connect to (default: <code class="docutils literal notranslate"><span class="pre">21</span></code>,
as specified by the FTP protocol specification).
It is rarely needed to specify a different port number.</p></li>
<li><p><strong>timeout</strong> (<a class="reference internal" href="functions.html#float" title="float"><em>float</em></a><em> | </em><em>None</em>) – A timeout in seconds for the connection attempt
(default: the global default timeout setting).</p></li>
<li><p><strong>source_address</strong> (<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><em>tuple</em></a><em> | </em><em>None</em>) – A 2-tuple <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> for the socket to bind to as its
source address before connecting.</p></li>
</ul>
</dd>
</dl>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">ftplib.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>source_address</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.getwelcome">
<span class="sig-name descname"><span class="pre">getwelcome</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.getwelcome" title="Link to this definition">¶</a></dt>
<dd><p>Return the welcome message sent by the server in reply to the initial
connection.  (This message sometimes contains disclaimers or help information
that may be relevant to the user.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.login">
<span class="sig-name descname"><span class="pre">login</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'anonymous'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">passwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">acct</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.login" title="Link to this definition">¶</a></dt>
<dd><p>Log on to the connected FTP server.
This function should be called only once for each instance,
after a connection has been established;
it should not be called if the <em>host</em> and <em>user</em> arguments were given
when the <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> instance was created.
Most FTP commands are only allowed after the client has logged in.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>user</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The username to log in with (default: <code class="docutils literal notranslate"><span class="pre">'anonymous'</span></code>).</p></li>
<li><p><strong>passwd</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The password to use when logging in.
If not given, and if <em>passwd</em> is the empty string or <code class="docutils literal notranslate"><span class="pre">&quot;-&quot;</span></code>,
a password will be automatically generated.</p></li>
<li><p><strong>acct</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – Account information to be used for the <code class="docutils literal notranslate"><span class="pre">ACCT</span></code> FTP command.
Few systems implement this.
See <a class="reference external" href="https://datatracker.ietf.org/doc/html/rfc959.html">RFC-959</a>
for more details.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.abort">
<span class="sig-name descname"><span class="pre">abort</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.abort" title="Link to this definition">¶</a></dt>
<dd><p>Abort a file transfer that is in progress.  Using this does not always work, but
it’s worth a try.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.sendcmd">
<span class="sig-name descname"><span class="pre">sendcmd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.sendcmd" title="Link to this definition">¶</a></dt>
<dd><p>Send a simple command string to the server and return the response string.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">ftplib.sendcmd</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">cmd</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.voidcmd">
<span class="sig-name descname"><span class="pre">voidcmd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.voidcmd" title="Link to this definition">¶</a></dt>
<dd><p>Send a simple command string to the server and handle the response.  Return
the response string if the response code corresponds to success (codes in
the range 200–299).  Raise <a class="reference internal" href="#ftplib.error_reply" title="ftplib.error_reply"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error_reply</span></code></a> otherwise.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">ftplib.sendcmd</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">cmd</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.retrbinary">
<span class="sig-name descname"><span class="pre">retrbinary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callback</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blocksize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">8192</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rest</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.retrbinary" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve a file in binary transfer mode.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cmd</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – An appropriate <code class="docutils literal notranslate"><span class="pre">STOR</span></code> command: <code class="samp docutils literal notranslate"><span class="pre">&quot;STOR</span> <em><span class="pre">filename</span></em><span class="pre">&quot;</span></code>.</p></li>
<li><p><strong>callback</strong> (<a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a>) – A single parameter callable that is called
for each block of data received,
with its single argument being the data as <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.</p></li>
<li><p><strong>blocksize</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The maximum chunk size to read on the low-level
<a class="reference internal" href="socket.html#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket</span></code></a> object created to do the actual transfer.
This also corresponds to the largest size of data
that will be passed to <em>callback</em>.
Defaults to <code class="docutils literal notranslate"><span class="pre">8192</span></code>.</p></li>
<li><p><strong>rest</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – A <code class="docutils literal notranslate"><span class="pre">REST</span></code> command to be sent to the server.
See the documentation for the <em>rest</em> parameter of the <a class="reference internal" href="#ftplib.FTP.transfercmd" title="ftplib.FTP.transfercmd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">transfercmd()</span></code></a> method.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.retrlines">
<span class="sig-name descname"><span class="pre">retrlines</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.retrlines" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve a file or directory listing in the encoding specified by the
<em>encoding</em> parameter at initialization.
<em>cmd</em> should be an appropriate <code class="docutils literal notranslate"><span class="pre">RETR</span></code> command (see <a class="reference internal" href="#ftplib.FTP.retrbinary" title="ftplib.FTP.retrbinary"><code class="xref py py-meth docutils literal notranslate"><span class="pre">retrbinary()</span></code></a>) or
a command such as <code class="docutils literal notranslate"><span class="pre">LIST</span></code> or <code class="docutils literal notranslate"><span class="pre">NLST</span></code> (usually just the string <code class="docutils literal notranslate"><span class="pre">'LIST'</span></code>).
<code class="docutils literal notranslate"><span class="pre">LIST</span></code> retrieves a list of files and information about those files.
<code class="docutils literal notranslate"><span class="pre">NLST</span></code> retrieves a list of file names.
The <em>callback</em> function is called for each line with a string argument
containing the line with the trailing CRLF stripped.  The default <em>callback</em>
prints the line to <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.set_pasv">
<span class="sig-name descname"><span class="pre">set_pasv</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.set_pasv" title="Link to this definition">¶</a></dt>
<dd><p>Enable “passive” mode if <em>val</em> is true, otherwise disable passive mode.
Passive mode is on by default.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.storbinary">
<span class="sig-name descname"><span class="pre">storbinary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blocksize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">8192</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rest</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.storbinary" title="Link to this definition">¶</a></dt>
<dd><p>Store a file in binary transfer mode.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cmd</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – An appropriate <code class="docutils literal notranslate"><span class="pre">STOR</span></code> command: <code class="samp docutils literal notranslate"><span class="pre">&quot;STOR</span> <em><span class="pre">filename</span></em><span class="pre">&quot;</span></code>.</p></li>
<li><p><strong>fp</strong> (<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>) – A file object (opened in binary mode) which is read until EOF,
using its <a class="reference internal" href="io.html#io.RawIOBase.read" title="io.RawIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> method in blocks of size <em>blocksize</em>
to provide the data to be stored.</p></li>
<li><p><strong>blocksize</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The read block size.
Defaults to <code class="docutils literal notranslate"><span class="pre">8192</span></code>.</p></li>
<li><p><strong>callback</strong> (<a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a>) – A single parameter callable that is called
for each block of data sent,
with its single argument being the data as <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.</p></li>
<li><p><strong>rest</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – A <code class="docutils literal notranslate"><span class="pre">REST</span></code> command to be sent to the server.
See the documentation for the <em>rest</em> parameter of the <a class="reference internal" href="#ftplib.FTP.transfercmd" title="ftplib.FTP.transfercmd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">transfercmd()</span></code></a> method.</p></li>
</ul>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>rest</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.storlines">
<span class="sig-name descname"><span class="pre">storlines</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.storlines" title="Link to this definition">¶</a></dt>
<dd><p>Store a file in line mode.  <em>cmd</em> should be an appropriate
<code class="docutils literal notranslate"><span class="pre">STOR</span></code> command (see <a class="reference internal" href="#ftplib.FTP.storbinary" title="ftplib.FTP.storbinary"><code class="xref py py-meth docutils literal notranslate"><span class="pre">storbinary()</span></code></a>).  Lines are read until EOF from the
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> <em>fp</em> (opened in binary mode) using its <a class="reference internal" href="io.html#io.IOBase.readline" title="io.IOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a>
method to provide the data to be stored.  <em>callback</em> is an optional single
parameter callable that is called on each line after it is sent.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.transfercmd">
<span class="sig-name descname"><span class="pre">transfercmd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rest</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.transfercmd" title="Link to this definition">¶</a></dt>
<dd><p>Initiate a transfer over the data connection.  If the transfer is active, send an
<code class="docutils literal notranslate"><span class="pre">EPRT</span></code> or  <code class="docutils literal notranslate"><span class="pre">PORT</span></code> command and the transfer command specified by <em>cmd</em>, and
accept the connection.  If the server is passive, send an <code class="docutils literal notranslate"><span class="pre">EPSV</span></code> or <code class="docutils literal notranslate"><span class="pre">PASV</span></code>
command, connect to it, and start the transfer command.  Either way, return the
socket for the connection.</p>
<p>If optional <em>rest</em> is given, a <code class="docutils literal notranslate"><span class="pre">REST</span></code> command is sent to the server, passing
<em>rest</em> as an argument.  <em>rest</em> is usually a byte offset into the requested file,
telling the server to restart sending the file’s bytes at the requested offset,
skipping over the initial bytes.  Note however that the <a class="reference internal" href="#ftplib.FTP.transfercmd" title="ftplib.FTP.transfercmd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">transfercmd()</span></code></a>
method converts <em>rest</em> to a string with the <em>encoding</em> parameter specified
at initialization, but no check is performed on the string’s contents.  If the
server does not recognize the <code class="docutils literal notranslate"><span class="pre">REST</span></code> command, an <a class="reference internal" href="#ftplib.error_reply" title="ftplib.error_reply"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error_reply</span></code></a> exception
will be raised.  If this happens, simply call <a class="reference internal" href="#ftplib.FTP.transfercmd" title="ftplib.FTP.transfercmd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">transfercmd()</span></code></a> without a
<em>rest</em> argument.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.ntransfercmd">
<span class="sig-name descname"><span class="pre">ntransfercmd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rest</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.ntransfercmd" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#ftplib.FTP.transfercmd" title="ftplib.FTP.transfercmd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">transfercmd()</span></code></a>, but returns a tuple of the data connection and the
expected size of the data.  If the expected size could not be computed, <code class="docutils literal notranslate"><span class="pre">None</span></code>
will be returned as the expected size.  <em>cmd</em> and <em>rest</em> means the same thing as
in <a class="reference internal" href="#ftplib.FTP.transfercmd" title="ftplib.FTP.transfercmd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">transfercmd()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.mlsd">
<span class="sig-name descname"><span class="pre">mlsd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">facts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">[]</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.mlsd" title="Link to this definition">¶</a></dt>
<dd><p>List a directory in a standardized format by using <code class="docutils literal notranslate"><span class="pre">MLSD</span></code> command
(<span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3659.html"><strong>RFC 3659</strong></a>).  If <em>path</em> is omitted the current directory is assumed.
<em>facts</em> is a list of strings representing the type of information desired
(e.g. <code class="docutils literal notranslate"><span class="pre">[&quot;type&quot;,</span> <span class="pre">&quot;size&quot;,</span> <span class="pre">&quot;perm&quot;]</span></code>).  Return a generator object yielding a
tuple of two elements for every file found in path.  First element is the
file name, the second one is a dictionary containing facts about the file
name.  Content of this dictionary might be limited by the <em>facts</em> argument
but server is not guaranteed to return all requested facts.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.nlst">
<span class="sig-name descname"><span class="pre">nlst</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">argument</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.nlst" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of file names as returned by the <code class="docutils literal notranslate"><span class="pre">NLST</span></code> command.  The
optional <em>argument</em> is a directory to list (default is the current server
directory).  Multiple arguments can be used to pass non-standard options to
the <code class="docutils literal notranslate"><span class="pre">NLST</span></code> command.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If your server supports the command, <a class="reference internal" href="#ftplib.FTP.mlsd" title="ftplib.FTP.mlsd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">mlsd()</span></code></a> offers a better API.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.dir">
<span class="sig-name descname"><span class="pre">dir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">argument</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.dir" title="Link to this definition">¶</a></dt>
<dd><p>Produce a directory listing as returned by the <code class="docutils literal notranslate"><span class="pre">LIST</span></code> command, printing it to
standard output.  The optional <em>argument</em> is a directory to list (default is the
current server directory).  Multiple arguments can be used to pass non-standard
options to the <code class="docutils literal notranslate"><span class="pre">LIST</span></code> command.  If the last argument is a function, it is used
as a <em>callback</em> function as for <a class="reference internal" href="#ftplib.FTP.retrlines" title="ftplib.FTP.retrlines"><code class="xref py py-meth docutils literal notranslate"><span class="pre">retrlines()</span></code></a>; the default prints to
<a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>.  This method returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If your server supports the command, <a class="reference internal" href="#ftplib.FTP.mlsd" title="ftplib.FTP.mlsd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">mlsd()</span></code></a> offers a better API.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.rename">
<span class="sig-name descname"><span class="pre">rename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fromname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">toname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.rename" title="Link to this definition">¶</a></dt>
<dd><p>Rename file <em>fromname</em> on the server to <em>toname</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.delete">
<span class="sig-name descname"><span class="pre">delete</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.delete" title="Link to this definition">¶</a></dt>
<dd><p>Remove the file named <em>filename</em> from the server.  If successful, returns the
text of the response, otherwise raises <a class="reference internal" href="#ftplib.error_perm" title="ftplib.error_perm"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error_perm</span></code></a> on permission errors or
<a class="reference internal" href="#ftplib.error_reply" title="ftplib.error_reply"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error_reply</span></code></a> on other errors.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.cwd">
<span class="sig-name descname"><span class="pre">cwd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pathname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.cwd" title="Link to this definition">¶</a></dt>
<dd><p>Set the current directory on the server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.mkd">
<span class="sig-name descname"><span class="pre">mkd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pathname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.mkd" title="Link to this definition">¶</a></dt>
<dd><p>Create a new directory on the server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.pwd">
<span class="sig-name descname"><span class="pre">pwd</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.pwd" title="Link to this definition">¶</a></dt>
<dd><p>Return the pathname of the current directory on the server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.rmd">
<span class="sig-name descname"><span class="pre">rmd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dirname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.rmd" title="Link to this definition">¶</a></dt>
<dd><p>Remove the directory named <em>dirname</em> on the server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.size">
<span class="sig-name descname"><span class="pre">size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.size" title="Link to this definition">¶</a></dt>
<dd><p>Request the size of the file named <em>filename</em> on the server.  On success, the
size of the file is returned as an integer, otherwise <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.
Note that the <code class="docutils literal notranslate"><span class="pre">SIZE</span></code> command is not  standardized, but is supported by many
common server implementations.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.quit">
<span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.quit" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">QUIT</span></code> command to the server and close the connection. This is the
“polite” way to close a connection, but it may raise an exception if the server
responds with an error to the <code class="docutils literal notranslate"><span class="pre">QUIT</span></code> command.  This implies a call to the
<a class="reference internal" href="#ftplib.FTP.close" title="ftplib.FTP.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method which renders the <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> instance useless for
subsequent calls (see below).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the connection unilaterally.  This should not be applied to an already
closed connection such as after a successful call to <a class="reference internal" href="#ftplib.FTP.quit" title="ftplib.FTP.quit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">quit()</span></code></a>.
After this call the <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> instance should not be used any more (after
a call to <a class="reference internal" href="#ftplib.FTP.close" title="ftplib.FTP.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> or <a class="reference internal" href="#ftplib.FTP.quit" title="ftplib.FTP.quit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">quit()</span></code></a> you cannot reopen the
connection by issuing another <a class="reference internal" href="#ftplib.FTP.login" title="ftplib.FTP.login"><code class="xref py py-meth docutils literal notranslate"><span class="pre">login()</span></code></a> method).</p>
</dd></dl>

</dd></dl>

</section>
<section id="ftp-tls-objects">
<h3>FTP_TLS objects<a class="headerlink" href="#ftp-tls-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="ftplib.FTP_TLS">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">FTP_TLS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">passwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">acct</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_address</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'utf-8'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP_TLS" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a> subclass which adds TLS support to FTP as described in
<span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4217.html"><strong>RFC 4217</strong></a>.
Connect to port 21 implicitly securing the FTP control connection
before authenticating.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The user must explicitly secure the data connection
by calling the <a class="reference internal" href="#ftplib.FTP_TLS.prot_p" title="ftplib.FTP_TLS.prot_p"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prot_p()</span></code></a> method.</p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>host</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The hostname to connect to.
If given, <code class="code docutils literal notranslate"><span class="pre">connect(host)</span></code> is implicitly called by the constructor.</p></li>
<li><p><strong>user</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The username to log in with (default: <code class="docutils literal notranslate"><span class="pre">'anonymous'</span></code>).
If given, <code class="code docutils literal notranslate"><span class="pre">login(host,</span> <span class="pre">passwd,</span> <span class="pre">acct)</span></code> is implicitly called
by the constructor.</p></li>
<li><p><strong>passwd</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The password to use when logging in.
If not given, and if <em>passwd</em> is the empty string or <code class="docutils literal notranslate"><span class="pre">&quot;-&quot;</span></code>,
a password will be automatically generated.</p></li>
<li><p><strong>acct</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – Account information to be used for the <code class="docutils literal notranslate"><span class="pre">ACCT</span></code> FTP command.
Few systems implement this.
See <a class="reference external" href="https://datatracker.ietf.org/doc/html/rfc959.html">RFC-959</a>
for more details.</p></li>
<li><p><strong>context</strong> (<a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a>) – An SSL context object which allows bundling SSL configuration options,
certificates and private keys into a single, potentially long-lived,
structure.
Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for best practices.</p></li>
<li><p><strong>timeout</strong> (<a class="reference internal" href="functions.html#float" title="float"><em>float</em></a><em> | </em><em>None</em>) – A timeout in seconds for blocking operations like <a class="reference internal" href="#ftplib.FTP.connect" title="ftplib.FTP.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a>
(default: the global default timeout setting).</p></li>
<li><p><strong>source_address</strong> (<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><em>tuple</em></a><em> | </em><em>None</em>) – A 2-tuple <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> for the socket to bind to as its
source address before connecting.</p></li>
<li><p><strong>encoding</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The encoding for directories and filenames (default: <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code>).</p></li>
</ul>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>source_address</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The class now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.
The <em>encoding</em> parameter was added, and the default was changed from
Latin-1 to UTF-8 to follow <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2640.html"><strong>RFC 2640</strong></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The deprecated <em>keyfile</em> and <em>certfile</em> parameters have been removed.</p>
</div>
<p>Here’s a sample session using the <a class="reference internal" href="#ftplib.FTP_TLS" title="ftplib.FTP_TLS"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP_TLS</span></code></a> class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ftps</span> <span class="o">=</span> <span class="n">FTP_TLS</span><span class="p">(</span><span class="s1">&#39;ftp.pureftpd.org&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftps</span><span class="o">.</span><span class="n">login</span><span class="p">()</span>
<span class="go">&#39;230 Anonymous user logged in&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftps</span><span class="o">.</span><span class="n">prot_p</span><span class="p">()</span>
<span class="go">&#39;200 Data protection level set to &quot;private&quot;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ftps</span><span class="o">.</span><span class="n">nlst</span><span class="p">()</span>
<span class="go">[&#39;6jack&#39;, &#39;OpenBSD&#39;, &#39;antilink&#39;, &#39;blogbench&#39;, &#39;bsdcam&#39;, &#39;clockspeed&#39;, &#39;djbdns-jedi&#39;, &#39;docs&#39;, &#39;eaccelerator-jedi&#39;, &#39;favicon.ico&#39;, &#39;francotone&#39;, &#39;fugu&#39;, &#39;ignore&#39;, &#39;libpuzzle&#39;, &#39;metalog&#39;, &#39;minidentd&#39;, &#39;misc&#39;, &#39;mysql-udf-global-user-variables&#39;, &#39;php-jenkins-hash&#39;, &#39;php-skein-hash&#39;, &#39;php-webdav&#39;, &#39;phpaudit&#39;, &#39;phpbench&#39;, &#39;pincaster&#39;, &#39;ping&#39;, &#39;posto&#39;, &#39;pub&#39;, &#39;public&#39;, &#39;public_keys&#39;, &#39;pure-ftpd&#39;, &#39;qscan&#39;, &#39;qtc&#39;, &#39;sharedance&#39;, &#39;skycache&#39;, &#39;sound&#39;, &#39;tmp&#39;, &#39;ucarp&#39;]</span>
</pre></div>
</div>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP_TLS</span></code> class inherits from <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a>,
defining these additional methods and attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ftplib.FTP_TLS.ssl_version">
<span class="sig-name descname"><span class="pre">ssl_version</span></span><a class="headerlink" href="#ftplib.FTP_TLS.ssl_version" title="Link to this definition">¶</a></dt>
<dd><p>The SSL version to use (defaults to <a class="reference internal" href="ssl.html#ssl.PROTOCOL_SSLv23" title="ssl.PROTOCOL_SSLv23"><code class="xref py py-data docutils literal notranslate"><span class="pre">ssl.PROTOCOL_SSLv23</span></code></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP_TLS.auth">
<span class="sig-name descname"><span class="pre">auth</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP_TLS.auth" title="Link to this definition">¶</a></dt>
<dd><p>Set up a secure control connection by using TLS or SSL, depending on what
is specified in the <a class="reference internal" href="#ftplib.FTP_TLS.ssl_version" title="ftplib.FTP_TLS.ssl_version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl_version</span></code></a> attribute.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The method now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP_TLS.ccc">
<span class="sig-name descname"><span class="pre">ccc</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP_TLS.ccc" title="Link to this definition">¶</a></dt>
<dd><p>Revert control channel back to plaintext.  This can be useful to take
advantage of firewalls that know how to handle NAT with non-secure FTP
without opening fixed ports.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP_TLS.prot_p">
<span class="sig-name descname"><span class="pre">prot_p</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP_TLS.prot_p" title="Link to this definition">¶</a></dt>
<dd><p>Set up secure data connection.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ftplib.FTP_TLS.prot_c">
<span class="sig-name descname"><span class="pre">prot_c</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ftplib.FTP_TLS.prot_c" title="Link to this definition">¶</a></dt>
<dd><p>Set up clear text data connection.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-variables">
<h3>Module variables<a class="headerlink" href="#module-variables" title="Link to this heading">¶</a></h3>
<dl class="py exception">
<dt class="sig sig-object py" id="ftplib.error_reply">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">error_reply</span></span><a class="headerlink" href="#ftplib.error_reply" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when an unexpected reply is received from the server.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ftplib.error_temp">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">error_temp</span></span><a class="headerlink" href="#ftplib.error_temp" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when an error code signifying a temporary error (response
codes in the range 400–499) is received.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ftplib.error_perm">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">error_perm</span></span><a class="headerlink" href="#ftplib.error_perm" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when an error code signifying a permanent error (response
codes in the range 500–599) is received.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ftplib.error_proto">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">error_proto</span></span><a class="headerlink" href="#ftplib.error_proto" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when a reply is received from the server that does not fit
the response specifications of the File Transfer Protocol, i.e. begin with a
digit in the range 1–5.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="ftplib.all_errors">
<span class="sig-prename descclassname"><span class="pre">ftplib.</span></span><span class="sig-name descname"><span class="pre">all_errors</span></span><a class="headerlink" href="#ftplib.all_errors" title="Link to this definition">¶</a></dt>
<dd><p>The set of all exceptions (as a tuple) that methods of <a class="reference internal" href="#ftplib.FTP" title="ftplib.FTP"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTP</span></code></a>
instances may raise as a result of problems with the FTP connection (as
opposed to programming errors made by the caller).  This set includes the
four exceptions listed above as well as <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> and <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a>.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="netrc.html#module-netrc" title="netrc: Loading of .netrc files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">netrc</span></code></a></dt><dd><p>Parser for the <code class="file docutils literal notranslate"><span class="pre">.netrc</span></code> file format.  The file <code class="file docutils literal notranslate"><span class="pre">.netrc</span></code> is
typically used by FTP clients to load user authentication information
before prompting the user.</p>
</dd>
</dl>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a><ul>
<li><a class="reference internal" href="#reference">Reference</a><ul>
<li><a class="reference internal" href="#ftp-objects">FTP objects</a></li>
<li><a class="reference internal" href="#ftp-tls-objects">FTP_TLS objects</a></li>
<li><a class="reference internal" href="#module-variables">Module variables</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.client.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="poplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ftplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="poplib.html" title="poplib — POP3 protocol client"
             >next</a> |</li>
        <li class="right" >
          <a href="http.client.html" title="http.client — HTTP protocol client"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>