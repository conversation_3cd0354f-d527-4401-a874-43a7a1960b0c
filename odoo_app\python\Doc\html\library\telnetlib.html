<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="telnetlib — Telnet client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/telnetlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/telnetlib.py The telnetlib module provides a Telnet class that implements the Telnet protocol. See RFC 854 for details about the protocol. In addition, it provides symbolic constan..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/telnetlib.py The telnetlib module provides a Telnet class that implements the Telnet protocol. See RFC 854 for details about the protocol. In addition, it provides symbolic constan..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>telnetlib — Telnet client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="uu — Encode and decode uuencode files" href="uu.html" />
    <link rel="prev" title="sunau — Read and write Sun AU files" href="sunau.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/telnetlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code> — Telnet client</a><ul>
<li><a class="reference internal" href="#telnet-objects">Telnet Objects</a></li>
<li><a class="reference internal" href="#telnet-example">Telnet Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="sunau.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code> — Read and write Sun AU files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="uu.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uu</span></code> — Encode and decode uuencode files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/telnetlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="uu.html" title="uu — Encode and decode uuencode files"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="sunau.html" title="sunau — Read and write Sun AU files"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code> — Telnet client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-telnetlib">
<span id="telnetlib-telnet-client"></span><h1><a class="reference internal" href="#module-telnetlib" title="telnetlib: Telnet client class. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code></a> — Telnet client<a class="headerlink" href="#module-telnetlib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/telnetlib.py">Lib/telnetlib.py</a></p>
<div class="deprecated-removed" id="index-0">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-telnetlib" title="telnetlib: Telnet client class. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code></a> module is deprecated
(see <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#telnetlib"><strong>PEP 594</strong></a> for details and alternatives).</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-telnetlib" title="telnetlib: Telnet client class. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code></a> module provides a <a class="reference internal" href="#telnetlib.Telnet" title="telnetlib.Telnet"><code class="xref py py-class docutils literal notranslate"><span class="pre">Telnet</span></code></a> class that implements the
Telnet protocol.  See <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc854.html"><strong>RFC 854</strong></a> for details about the protocol. In addition, it
provides symbolic constants for the protocol characters (see below), and for the
telnet options. The symbolic names of the telnet options follow the definitions
in <code class="docutils literal notranslate"><span class="pre">arpa/telnet.h</span></code>, with the leading <code class="docutils literal notranslate"><span class="pre">TELOPT_</span></code> removed. For symbolic names
of options which are traditionally not included in <code class="docutils literal notranslate"><span class="pre">arpa/telnet.h</span></code>, see the
module source itself.</p>
<p>The symbolic constants for the telnet commands are: IAC, DONT, DO, WONT, WILL,
SE (Subnegotiation End), NOP (No Operation), DM (Data Mark), BRK (Break), IP
(Interrupt process), AO (Abort output), AYT (Are You There), EC (Erase
Character), EL (Erase Line), GA (Go Ahead), SB (Subnegotiation Begin).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="telnetlib.Telnet">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">telnetlib.</span></span><span class="sig-name descname"><span class="pre">Telnet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=0</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="#telnetlib.Telnet" title="telnetlib.Telnet"><code class="xref py py-class docutils literal notranslate"><span class="pre">Telnet</span></code></a> represents a connection to a Telnet server. The instance is
initially not connected by default; the <a class="reference internal" href="#telnetlib.Telnet.open" title="telnetlib.Telnet.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a> method must be used to
establish a connection.  Alternatively, the host name and optional port
number can be passed to the constructor too, in which case the connection to
the server will be established before the constructor returns.  The optional
<em>timeout</em> parameter specifies a timeout in seconds for blocking operations
like the connection attempt (if not specified, the global default timeout
setting will be used).</p>
<p>Do not reopen an already connected instance.</p>
<p>This class has many <code class="xref py py-meth docutils literal notranslate"><span class="pre">read_*()</span></code> methods.  Note that some of them  raise
<a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> when the end of the connection is read, because they can return
an empty string for other reasons.  See the individual descriptions below.</p>
<p>A <a class="reference internal" href="#telnetlib.Telnet" title="telnetlib.Telnet"><code class="xref py py-class docutils literal notranslate"><span class="pre">Telnet</span></code></a> object is a context manager and can be used in a
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  When the <code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> block ends, the
<a class="reference internal" href="#telnetlib.Telnet.close" title="telnetlib.Telnet.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method is called:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">telnetlib</span> <span class="kn">import</span> <span class="n">Telnet</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">Telnet</span><span class="p">(</span><span class="s1">&#39;localhost&#39;</span><span class="p">,</span> <span class="mi">23</span><span class="p">)</span> <span class="k">as</span> <span class="n">tn</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">tn</span><span class="o">.</span><span class="n">interact</span><span class="p">()</span>
<span class="gp">...</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Context manager support added</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc854.html"><strong>RFC 854</strong></a> - Telnet Protocol Specification</dt><dd><p>Definition of the Telnet protocol.</p>
</dd>
</dl>
</div>
<section id="telnet-objects">
<span id="id1"></span><h2>Telnet Objects<a class="headerlink" href="#telnet-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#telnetlib.Telnet" title="telnetlib.Telnet"><code class="xref py py-class docutils literal notranslate"><span class="pre">Telnet</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_until">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_until</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expected</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_until" title="Link to this definition">¶</a></dt>
<dd><p>Read until a given byte string, <em>expected</em>, is encountered or until <em>timeout</em>
seconds have passed.</p>
<p>When no match is found, return whatever is available instead, possibly empty
bytes.  Raise <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> if the connection is closed and no cooked data
is available.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_all">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_all</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_all" title="Link to this definition">¶</a></dt>
<dd><p>Read all data until EOF as bytes; block until connection closed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_some">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_some</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_some" title="Link to this definition">¶</a></dt>
<dd><p>Read at least one byte of cooked data unless EOF is hit. Return <code class="docutils literal notranslate"><span class="pre">b''</span></code> if
EOF is hit.  Block if no data is immediately available.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_very_eager">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_very_eager</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_very_eager" title="Link to this definition">¶</a></dt>
<dd><p>Read everything that can be without blocking in I/O (eager).</p>
<p>Raise <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> if connection closed and no cooked data available.
Return <code class="docutils literal notranslate"><span class="pre">b''</span></code> if no cooked data available otherwise. Do not block unless in
the midst of an IAC sequence.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_eager">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_eager</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_eager" title="Link to this definition">¶</a></dt>
<dd><p>Read readily available data.</p>
<p>Raise <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> if connection closed and no cooked data available.
Return <code class="docutils literal notranslate"><span class="pre">b''</span></code> if no cooked data available otherwise. Do not block unless in
the midst of an IAC sequence.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_lazy">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_lazy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_lazy" title="Link to this definition">¶</a></dt>
<dd><p>Process and return data already in the queues (lazy).</p>
<p>Raise <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> if connection closed and no data available. Return
<code class="docutils literal notranslate"><span class="pre">b''</span></code> if no cooked data available otherwise.  Do not block unless in the
midst of an IAC sequence.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_very_lazy">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_very_lazy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_very_lazy" title="Link to this definition">¶</a></dt>
<dd><p>Return any data available in the cooked queue (very lazy).</p>
<p>Raise <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> if connection closed and no data available. Return
<code class="docutils literal notranslate"><span class="pre">b''</span></code> if no cooked data available otherwise.  This method never blocks.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.read_sb_data">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">read_sb_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.read_sb_data" title="Link to this definition">¶</a></dt>
<dd><p>Return the data collected between a SB/SE pair (suboption begin/end). The
callback should access these data when it was invoked with a <code class="docutils literal notranslate"><span class="pre">SE</span></code> command.
This method never blocks.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.open">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=0</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.open" title="Link to this definition">¶</a></dt>
<dd><p>Connect to a host. The optional second argument is the port number, which
defaults to the standard Telnet port (23). The optional <em>timeout</em> parameter
specifies a timeout in seconds for blocking operations like the connection
attempt (if not specified, the global default timeout setting will be used).</p>
<p>Do not try to reopen an already connected instance.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">telnetlib.Telnet.open</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.msg">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">msg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.msg" title="Link to this definition">¶</a></dt>
<dd><p>Print a debug message when the debug level is <code class="docutils literal notranslate"><span class="pre">&gt;</span></code> 0. If extra arguments are
present, they are substituted in the message using the standard string
formatting operator.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.set_debuglevel">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">set_debuglevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">debuglevel</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.set_debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>Set the debug level.  The higher the value of <em>debuglevel</em>, the more debug
output you get (on <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.close">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the connection.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.get_socket">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">get_socket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.get_socket" title="Link to this definition">¶</a></dt>
<dd><p>Return the socket object used internally.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.fileno">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Return the file descriptor of the socket object used internally.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.write">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.write" title="Link to this definition">¶</a></dt>
<dd><p>Write a byte string to the socket, doubling any IAC characters. This can
block if the connection is blocked.  May raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the
connection is closed.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">telnetlib.Telnet.write</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">buffer</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This method used to raise <a class="reference internal" href="socket.html#socket.error" title="socket.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">socket.error</span></code></a>, which is now an alias
of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.interact">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">interact</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.interact" title="Link to this definition">¶</a></dt>
<dd><p>Interaction function, emulates a very dumb Telnet client.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.mt_interact">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">mt_interact</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.mt_interact" title="Link to this definition">¶</a></dt>
<dd><p>Multithreaded version of <a class="reference internal" href="#telnetlib.Telnet.interact" title="telnetlib.Telnet.interact"><code class="xref py py-meth docutils literal notranslate"><span class="pre">interact()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.expect">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">expect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">list</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.expect" title="Link to this definition">¶</a></dt>
<dd><p>Read until one from a list of a regular expressions matches.</p>
<p>The first argument is a list of regular expressions, either compiled
(<a class="reference internal" href="re.html#re-objects"><span class="std std-ref">regex objects</span></a>) or uncompiled (byte strings). The
optional second argument is a timeout, in seconds; the default is to block
indefinitely.</p>
<p>Return a tuple of three items: the index in the list of the first regular
expression that matches; the match object returned; and the bytes read up
till and including the match.</p>
<p>If end of file is found and no bytes were read, raise <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a>.
Otherwise, when nothing matches, return <code class="docutils literal notranslate"><span class="pre">(-1,</span> <span class="pre">None,</span> <span class="pre">data)</span></code> where <em>data</em> is
the bytes received so far (may be empty bytes if a timeout happened).</p>
<p>If a regular expression ends with a greedy match (such as <code class="docutils literal notranslate"><span class="pre">.*</span></code>) or if more
than one expression can match the same input, the results are
non-deterministic, and may depend on the I/O timing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="telnetlib.Telnet.set_option_negotiation_callback">
<span class="sig-prename descclassname"><span class="pre">Telnet.</span></span><span class="sig-name descname"><span class="pre">set_option_negotiation_callback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#telnetlib.Telnet.set_option_negotiation_callback" title="Link to this definition">¶</a></dt>
<dd><p>Each time a telnet option is read on the input flow, this <em>callback</em> (if set) is
called with the following parameters: callback(telnet socket, command
(DO/DONT/WILL/WONT), option).  No other action is done afterwards by telnetlib.</p>
</dd></dl>

</section>
<section id="telnet-example">
<span id="id2"></span><h2>Telnet Example<a class="headerlink" href="#telnet-example" title="Link to this heading">¶</a></h2>
<p>A simple example illustrating typical use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">getpass</span>
<span class="kn">import</span> <span class="nn">telnetlib</span>

<span class="n">HOST</span> <span class="o">=</span> <span class="s2">&quot;localhost&quot;</span>
<span class="n">user</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;Enter your remote account: &quot;</span><span class="p">)</span>
<span class="n">password</span> <span class="o">=</span> <span class="n">getpass</span><span class="o">.</span><span class="n">getpass</span><span class="p">()</span>

<span class="n">tn</span> <span class="o">=</span> <span class="n">telnetlib</span><span class="o">.</span><span class="n">Telnet</span><span class="p">(</span><span class="n">HOST</span><span class="p">)</span>

<span class="n">tn</span><span class="o">.</span><span class="n">read_until</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;login: &quot;</span><span class="p">)</span>
<span class="n">tn</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">user</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s1">&#39;ascii&#39;</span><span class="p">)</span> <span class="o">+</span> <span class="sa">b</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">if</span> <span class="n">password</span><span class="p">:</span>
    <span class="n">tn</span><span class="o">.</span><span class="n">read_until</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Password: &quot;</span><span class="p">)</span>
    <span class="n">tn</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">password</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s1">&#39;ascii&#39;</span><span class="p">)</span> <span class="o">+</span> <span class="sa">b</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="n">tn</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;ls</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">tn</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;exit</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="n">tn</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;ascii&#39;</span><span class="p">))</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code> — Telnet client</a><ul>
<li><a class="reference internal" href="#telnet-objects">Telnet Objects</a></li>
<li><a class="reference internal" href="#telnet-example">Telnet Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="sunau.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code> — Read and write Sun AU files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="uu.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uu</span></code> — Encode and decode uuencode files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/telnetlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="uu.html" title="uu — Encode and decode uuencode files"
             >next</a> |</li>
        <li class="right" >
          <a href="sunau.html" title="sunau — Read and write Sun AU files"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code> — Telnet client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>