<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record model="pos.config" id="pos_config_main">
        <field name="iface_start_categ_id" ref="pos_category_desks" />
        <field name="start_category">True</field>
    </record>

    <function model="ir.model.data" name="_update_xmlids">
        <value model="base" eval="[{
                'xml_id': 'point_of_sale.payment_method',
                'record': obj().env.ref('point_of_sale.pos_config_main')._get_payment_method('cash'),
                'noupdate': True,
                }]" />
    </function>
    <!-- Closed Session 1 -->

    <record id="pos_closed_session_1" model="pos.session" forcecreate="False" context="{'onboarding_creation': True}">
        <field name="config_id" ref="pos_config_main" />
        <field name="user_id" ref="base.user_admin" />
        <field name="start_at" eval="(DateTime.today() + relativedelta(days=-1)).strftime('%Y-%m-%d %H:%M:%S')" />
        <field name="stop_at"
            eval="(DateTime.today() + relativedelta(days=-1, hours=1)).strftime('%Y-%m-%d %H:%M:%S')" />
    </record>

    <record id="pos_closed_order_1_1" model="pos.order" forcecreate="False">
        <field name="session_id" ref="pos_closed_session_1" />
        <field name="company_id" ref="base.main_company" />
        <field name="name">ClosedDemo/0001</field>
        <field name="state">paid</field>
        <field name="amount_total">4.81</field>
        <field name="amount_tax">0.0</field>
        <field name="amount_paid">4.81</field>
        <field name="amount_return">0.0</field>
        <field name="pos_reference">Order 00000-001-1001</field>
    </record>

    <record id="pos_closed_orderline_1_1_1" model="pos.order.line" forcecreate="False">
        <field name="product_id" ref="wall_shelf" />
        <field name="price_subtotal">1.98</field>
        <field name="price_subtotal_incl">1.98</field>
        <field name="price_unit">1.98</field>
        <field name="order_id" ref="pos_closed_order_1_1" />
        <field name="full_product_name">Wall Shelf</field>
    </record>

    <record id="pos_closed_orderline_1_1_2" model="pos.order.line" forcecreate="False">
        <field name="product_id" ref="small_shelf" />
        <field name="price_subtotal">2.83</field>
        <field name="price_subtotal_incl">2.83</field>
        <field name="price_unit">2.83</field>
        <field name="order_id" ref="pos_closed_order_1_1" />
        <field name="full_product_name">Small Shelf</field>
    </record>

    <record id="pos_payment_1" model="pos.payment" forcecreate="False">
        <field name="payment_method_id" ref="point_of_sale.payment_method" />
        <field name="pos_order_id" ref="pos_closed_order_1_1" />
        <field name="amount">4.81</field>
    </record>

    <record id="pos_closed_order_1_2" model="pos.order" forcecreate="False">
        <field name="session_id" ref="pos_closed_session_1" />
        <field name="company_id" ref="base.main_company" />
        <field name="name">ClosedDemo/0002</field>
        <field name="state">paid</field>
        <field name="amount_total">2220.50</field>
        <field name="amount_tax">0.0</field>
        <field name="amount_paid">2220.50</field>
        <field name="amount_return">0.0</field>
        <field name="pos_reference">Order 00000-001-1002</field>
    </record>

    <record id="pos_closed_orderline_1_2_1" model="pos.order.line" forcecreate="False">
        <field name="product_id" ref="product_product_12" />
        <field name="price_subtotal">120.50</field>
        <field name="price_subtotal_incl">120.5</field>
        <field name="price_unit">120.50</field>
        <field name="order_id" ref="pos_closed_order_1_2" />
        <field name="full_product_name">Office Chair Black</field>
    </record>

    <record id="pos_closed_orderline_1_2_2" model="pos.order.line" forcecreate="False">
        <field name="product_id" ref="product_product_22" />
        <field name="price_subtotal">2100.0</field>
        <field name="price_subtotal_incl">2100.0</field>
        <field name="price_unit">2100.0</field>
        <field name="order_id" ref="pos_closed_order_1_2" />
        <field name="full_product_name">Desk Stand with Screen</field>
    </record>

    <record id="pos_payment_2" model="pos.payment" forcecreate="False">
        <field name="payment_method_id" ref="point_of_sale.payment_method" />
        <field name="pos_order_id" ref="pos_closed_order_1_2" />
        <field name="amount">2220.50</field>
    </record>

    <function model="pos.session" name="post_closing_cash_details" eval="[[ref('pos_closed_session_1')], 2225.31]" />

    <function model="pos.session" name="update_closing_control_state_session"
        eval="[[ref('pos_closed_session_1')], '']" />

    <function model="pos.session" name="action_pos_session_closing_control"
        eval="[[ref('pos_closed_session_1')]]" />

    <!-- Closed Session 2 -->

    <record id="pos_closed_session_2" model="pos.session" forcecreate="False" context="{'onboarding_creation': True}">
        <field name="config_id" ref="pos_config_main" />
        <field name="user_id" ref="base.user_admin" />
        <field name="start_at" eval="(DateTime.today() + relativedelta(hours=-3)).strftime('%Y-%m-%d %H:%M:%S')" />
        <field name="stop_at" eval="(DateTime.today() + relativedelta(hours=-2)).strftime('%Y-%m-%d %H:%M:%S')" />
    </record>

    <record id="pos_closed_order_2_1" model="pos.order" forcecreate="False">
        <field name="session_id" ref="pos_closed_session_2" />
        <field name="company_id" ref="base.main_company" />
        <field name="name">ClosedDemo/0003</field>
        <field name="state">paid</field>
        <field name="amount_total">9.90</field>
        <field name="amount_tax">0.0</field>
        <field name="amount_paid">9.90</field>
        <field name="amount_return">0.0</field>
        <field name="pos_reference">Order 00000-002-1001</field>
    </record>

    <record id="pos_closed_orderline_2_1_1" model="pos.order.line" forcecreate="False">
        <field name="name">Closed Orderline 2.1.1</field>
        <field name="product_id" ref="letter_tray" />
        <field name="price_subtotal">4.80</field>
        <field name="price_subtotal_incl">4.80</field>
        <field name="price_unit">4.80</field>
        <field name="order_id" ref="pos_closed_order_2_1" />
        <field name="full_product_name">Letter Tray</field>
    </record>

    <record id="pos_closed_orderline_2_1_2" model="pos.order.line" forcecreate="False">
        <field name="name">Closed Orderline 2.1.2</field>
        <field name="product_id" ref="desk_organizer" />
        <field name="price_subtotal">5.10</field>
        <field name="price_subtotal_incl">5.10</field>
        <field name="price_unit">5.10</field>
        <field name="order_id" ref="pos_closed_order_2_1" />
        <field name="full_product_name">Desk Organizer</field>
    </record>

    <record id="pos_payment_3" model="pos.payment" forcecreate="False">
        <field name="payment_method_id" ref="point_of_sale.payment_method" />
        <field name="pos_order_id" ref="pos_closed_order_2_1" />
        <field name="amount">9.90</field>
    </record>

    <record id="pos_closed_order_2_2" model="pos.order" forcecreate="False">
        <field name="session_id" ref="pos_closed_session_2" />
        <field name="company_id" ref="base.main_company" />
        <field name="name">ClosedDemo/0004</field>
        <field name="state">paid</field>
        <field name="amount_total">8.36</field>
        <field name="amount_tax">0.0</field>
        <field name="amount_paid">8.36</field>
        <field name="amount_return">0.0</field>
        <field name="pos_reference">Order 00000-002-1002</field>
    </record>

    <record id="pos_closed_orderline_2_2_1" model="pos.order.line" forcecreate="False">
        <field name="name">Closed Orderline 2.2.1</field>
        <field name="product_id" ref="magnetic_board" />
        <field name="price_subtotal">1.98</field>
        <field name="price_subtotal_incl">1.98</field>
        <field name="price_unit">1.98</field>
        <field name="order_id" ref="pos_closed_order_2_2" />
        <field name="full_product_name">Magnetic Board</field>
    </record>

    <record id="pos_closed_orderline_2_2_2" model="pos.order.line" forcecreate="False">
        <field name="name">Closed Orderline 2.1.2</field>
        <field name="product_id" ref="monitor_stand" />
        <field name="price_subtotal">6.38</field>
        <field name="price_subtotal_incl">6.38</field>
        <field name="qty">2</field>
        <field name="price_unit">3.19</field>
        <field name="order_id" ref="pos_closed_order_2_2" />
        <field name="full_product_name">Monitor Stand</field>
    </record>

    <record id="pos_payment_4" model="pos.payment" forcecreate="False">
        <field name="payment_method_id" ref="point_of_sale.payment_method" />
        <field name="pos_order_id" ref="pos_closed_order_2_2" />
        <field name="amount">8.36</field>
    </record>

    <function model="pos.session" name="post_closing_cash_details"
            eval="[[ref('pos_closed_session_2')], 2243.57]" />

    <function model="pos.session" name="update_closing_control_state_session"
        eval="[[ref('pos_closed_session_2')], '']" />

    <function model="pos.session" name="action_pos_session_closing_control"
        eval="[[ref('pos_closed_session_2')]]" />

</odoo>
