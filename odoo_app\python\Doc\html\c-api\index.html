<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Python/C API Reference Manual" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/index.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This manual documents the API used by C and C++ programmers who want to write extension modules or embed Python. It is a companion to Extending and Embedding the Python Interpreter, which describes..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This manual documents the API used by C and C++ programmers who want to write extension modules or embed Python. It is a companion to Extending and Embedding the Python Interpreter, which describes..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Python/C API Reference Manual &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Introduction" href="intro.html" />
    <link rel="prev" title="1. Embedding Python in Another Application" href="../extending/embedding.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/index.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../extending/embedding.html"
                          title="previous chapter"><span class="section-number">1. </span>Embedding Python in Another Application</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="intro.html"
                          title="next chapter">Introduction</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/index.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../extending/embedding.html" title="1. Embedding Python in Another Application"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

        <li class="nav-item nav-item-this"><a href="">Python/C API Reference Manual</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="python-c-api-reference-manual">
<span id="c-api-index"></span><h1>Python/C API Reference Manual<a class="headerlink" href="#python-c-api-reference-manual" title="Link to this heading">¶</a></h1>
<p>This manual documents the API used by C and C++ programmers who want to write
extension modules or embed Python.  It is a companion to <a class="reference internal" href="../extending/index.html#extending-index"><span class="std std-ref">Extending and Embedding the Python Interpreter</span></a>,
which describes the general principles of extension writing but does not
document the API functions in detail.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="intro.html">Introduction</a><ul>
<li class="toctree-l2"><a class="reference internal" href="intro.html#coding-standards">Coding standards</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#include-files">Include Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#useful-macros">Useful macros</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#objects-types-and-reference-counts">Objects, Types and Reference Counts</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#exceptions">Exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#embedding-python">Embedding Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#debugging-builds">Debugging Builds</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="stable.html">C API Stability</a><ul>
<li class="toctree-l2"><a class="reference internal" href="stable.html#unstable-c-api">Unstable C API</a></li>
<li class="toctree-l2"><a class="reference internal" href="stable.html#stable-application-binary-interface">Stable Application Binary Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="stable.html#platform-considerations">Platform Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="stable.html#contents-of-limited-api">Contents of Limited API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="veryhigh.html">The Very High Level Layer</a></li>
<li class="toctree-l1"><a class="reference internal" href="refcounting.html">Reference Counting</a></li>
<li class="toctree-l1"><a class="reference internal" href="exceptions.html">Exception Handling</a><ul>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#printing-and-clearing">Printing and clearing</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#raising-exceptions">Raising exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#issuing-warnings">Issuing warnings</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#querying-the-error-indicator">Querying the error indicator</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#signal-handling">Signal Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#exception-classes">Exception Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#exception-objects">Exception Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#unicode-exception-objects">Unicode Exception Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#recursion-control">Recursion Control</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#standard-exceptions">Standard Exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="exceptions.html#standard-warning-categories">Standard Warning Categories</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="utilities.html">Utilities</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sys.html">Operating System Utilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="sys.html#system-functions">System Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="sys.html#process-control">Process Control</a></li>
<li class="toctree-l2"><a class="reference internal" href="import.html">Importing Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="marshal.html">Data marshalling support</a></li>
<li class="toctree-l2"><a class="reference internal" href="arg.html">Parsing arguments and building values</a></li>
<li class="toctree-l2"><a class="reference internal" href="conversion.html">String conversion and formatting</a></li>
<li class="toctree-l2"><a class="reference internal" href="hash.html">PyHash API</a></li>
<li class="toctree-l2"><a class="reference internal" href="reflection.html">Reflection</a></li>
<li class="toctree-l2"><a class="reference internal" href="codec.html">Codec registry and support functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="perfmaps.html">Support for Perf Maps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="abstract.html">Abstract Objects Layer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="object.html">Object Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="call.html">Call Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="number.html">Number Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="sequence.html">Sequence Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="mapping.html">Mapping Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="iter.html">Iterator Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="buffer.html">Buffer Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="objbuffer.html">Old Buffer Protocol</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="concrete.html">Concrete Objects Layer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="concrete.html#fundamental-objects">Fundamental Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="concrete.html#numeric-objects">Numeric Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="concrete.html#sequence-objects">Sequence Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="concrete.html#container-objects">Container Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="concrete.html#function-objects">Function Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="concrete.html#other-objects">Other Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="init.html">Initialization, Finalization, and Threads</a><ul>
<li class="toctree-l2"><a class="reference internal" href="init.html#before-python-initialization">Before Python Initialization</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#global-configuration-variables">Global configuration variables</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#initializing-and-finalizing-the-interpreter">Initializing and finalizing the interpreter</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#process-wide-parameters">Process-wide parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#thread-state-and-the-global-interpreter-lock">Thread State and the Global Interpreter Lock</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#sub-interpreter-support">Sub-interpreter support</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#asynchronous-notifications">Asynchronous Notifications</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#profiling-and-tracing">Profiling and Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#advanced-debugger-support">Advanced Debugger Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="init.html#thread-local-storage-support">Thread Local Storage Support</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="init_config.html">Python Initialization Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#example">Example</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#pywidestringlist">PyWideStringList</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#pystatus">PyStatus</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#pypreconfig">PyPreConfig</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#preinitialize-python-with-pypreconfig">Preinitialize Python with PyPreConfig</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#pyconfig">PyConfig</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#initialization-with-pyconfig">Initialization with PyConfig</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#isolated-configuration">Isolated Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#python-configuration">Python Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#python-path-configuration">Python Path Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#py-runmain">Py_RunMain()</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#py-getargcargv">Py_GetArgcArgv()</a></li>
<li class="toctree-l2"><a class="reference internal" href="init_config.html#multi-phase-initialization-private-provisional-api">Multi-Phase Initialization Private Provisional API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="memory.html">Memory Management</a><ul>
<li class="toctree-l2"><a class="reference internal" href="memory.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#allocator-domains">Allocator Domains</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#raw-memory-interface">Raw Memory Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#memory-interface">Memory Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#object-allocators">Object allocators</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#default-memory-allocators">Default Memory Allocators</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#customize-memory-allocators">Customize Memory Allocators</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#debug-hooks-on-the-python-memory-allocators">Debug hooks on the Python memory allocators</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#the-pymalloc-allocator">The pymalloc allocator</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#tracemalloc-c-api">tracemalloc C API</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="objimpl.html">Object Implementation Support</a><ul>
<li class="toctree-l2"><a class="reference internal" href="allocation.html">Allocating Objects on the Heap</a></li>
<li class="toctree-l2"><a class="reference internal" href="structures.html">Common Object Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html">Type Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#number-object-structures">Number Object Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#mapping-object-structures">Mapping Object Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#sequence-object-structures">Sequence Object Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#buffer-object-structures">Buffer Object Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#async-object-structures">Async Object Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#slot-type-typedefs">Slot Type typedefs</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#examples">Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="gcsupport.html">Supporting Cyclic Garbage Collection</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="apiabiversion.html">API and ABI Versioning</a></li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../extending/embedding.html"
                          title="previous chapter"><span class="section-number">1. </span>Embedding Python in Another Application</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="intro.html"
                          title="next chapter">Introduction</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/index.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             >next</a> |</li>
        <li class="right" >
          <a href="../extending/embedding.html" title="1. Embedding Python in Another Application"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

        <li class="nav-item nav-item-this"><a href="">Python/C API Reference Manual</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>