<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_payment_method_view_form_inherit_pos_stripe" model="ir.ui.view">
        <field name="name">pos.payment.method.form.inherit.stripe</field>
        <field name="model">pos.payment.method</field>
        <field name="inherit_id" ref="point_of_sale.pos_payment_method_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='use_payment_terminal']" position="after">
                <!-- Stripe -->
                <field name="stripe_serial_number" invisible="use_payment_terminal != 'stripe'" required="use_payment_terminal == 'stripe'"/>
                <div colspan="2" class="mt16" invisible="use_payment_terminal != 'stripe'" required="use_payment_terminal == 'stripe'">
                    <button name="action_stripe_key" type="object" icon="oi-arrow-right" string="Don't forget to complete Stripe connect before using this payment method." class="btn-link"/>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
