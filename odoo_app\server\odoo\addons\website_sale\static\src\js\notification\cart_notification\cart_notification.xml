<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="website_sale.cartNotification">
        <div t-attf-class="toast show o_cc1 position-relative start-0 mt-2 {{props.className}}"
             t-attf-style="top: {{positionOffset}};"
             role="alert"
             aria-live="assertive"
             aria-atomic="true">
            <div class="toast-header justify-content-between">
                <strong t-if="props.message" t-out="props.message"/>
                <button class="btn-close"
                        type="button"
                        t-on-click="props.close"
                        aria-label="Close"/>
            </div>
            <div class="toast-body">
                <WarningNotification t-if="this.props.warning" warning="this.props.warning"/>
                <AddToCartNotification
                    t-elif="this.props.lines.length"
                    lines="this.props.lines"
                    currency_id="this.props.currency_id"/>
            </div>
        </div>
    </t>

</templates>
