# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mass_mailing
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# ghas<PERSON> yagh<PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:35+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid ".o_newsletter_popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid ""
".s_newsletter_block .s_newsletter_list, .o_newsletter_popup "
".s_newsletter_list"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid ".s_newsletter_list"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                Thank you for subscribing!"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid ""
"<span class=\"s_website_form_label_content\">I agree to receive updates</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid ""
"<span class=\"s_website_form_label_content\">Subscribe to</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\"> ایمیل شما </span>\n"
"<span class=\"s_website_form_mark\"> * </span>"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\"> نام شما </span>\n"
"<span class=\"s_website_form_mark\">*</span>"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Always <b>First</b>."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_default_template
msgid "Always First."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_default_template
msgid "Be the first to find out all the latest news, products, and trends."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid ""
"Be the first to find out all the latest news,<br/> products, and trends."
msgstr ""

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/xml/website_mass_mailing.xml:0
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
#, python-format
msgid "Close"
msgstr "بستن"

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social_left
msgid "Contact"
msgstr "مخاطب"

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid "Display Thanks Button"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Email Subscription"
msgstr ""

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#, python-format
msgid "Error"
msgstr "خطا"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Form Subscription"
msgstr ""

#. module: website_mass_mailing
#. odoo-python
#: code:addons/website_mass_mailing/controllers/website_form.py:0
#, python-format
msgid "Mailing List(s) not found!"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options_common
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.snippets
msgid "Newsletter"
msgstr "خبرنامه"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.snippets
msgid "Newsletter Block"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.snippets
msgid "Newsletter Popup"
msgstr "خبرنامه جهنده"

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid ""
"No mailing list found, do you want to create a new one? This will save all "
"your changes, are you sure you want to proceed?"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options_common
msgid "Show reCaptcha Policy"
msgstr "نمایش سیاست reCaptcha"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Subscribe"
msgstr "عضویت"

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/mass_mailing_form_editor.js:0
#, python-format
msgid "Subscribe to"
msgstr ""

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#, python-format
msgid "Success"
msgstr "موفقیت"

#. module: website_mass_mailing
#. odoo-python
#: code:addons/website_mass_mailing/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "فعالیت مشکوکی توسط گوگل reCaptcha شناسایی شد."

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Template"
msgstr "پوسته"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks"
msgstr "با تشکر"

#. module: website_mass_mailing
#. odoo-python
#: code:addons/website_mass_mailing/controllers/main.py:0
#, python-format
msgid "Thanks for subscribing!"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid ""
"We send one weekly newsletter per list and always try to keep it "
"interesting. You can unsubscribe at any time."
msgstr ""

#. module: website_mass_mailing
#. odoo-python
#: code:addons/website_mass_mailing/controllers/website_form.py:0
#, python-format
msgid "You cannot subscribe to the following list anymore : %s"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block_form_template
msgid "You will now be informed about the latest news.<br/>"
msgstr ""

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/mass_mailing_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "ایمیل شما"

#. module: website_mass_mailing
#. odoo-javascript
#: code:addons/website_mass_mailing/static/src/js/mass_mailing_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "نام"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "your email..."
msgstr "ایمیل شما..."
