<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xdrlib — Encode and decode XDR data" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xdrlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xdrlib.py The xdrlib module supports the External Data Representation Standard as described in RFC 1014, written by Sun Microsystems, Inc. June 1987. It supports most of the data t..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xdrlib.py The xdrlib module supports the External Data Representation Standard as described in RFC 1014, written by Sun Microsystems, Inc. June 1987. It supports most of the data t..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xdrlib — Encode and decode XDR data &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Security Considerations" href="security_warnings.html" />
    <link rel="prev" title="uu — Encode and decode uuencode files" href="uu.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xdrlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code> — Encode and decode XDR data</a><ul>
<li><a class="reference internal" href="#packer-objects">Packer Objects</a></li>
<li><a class="reference internal" href="#unpacker-objects">Unpacker Objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="uu.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uu</span></code> — Encode and decode uuencode files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="security_warnings.html"
                          title="next chapter">Security Considerations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xdrlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="security_warnings.html" title="Security Considerations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="uu.html" title="uu — Encode and decode uuencode files"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code> — Encode and decode XDR data</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xdrlib">
<span id="xdrlib-encode-and-decode-xdr-data"></span><h1><a class="reference internal" href="#module-xdrlib" title="xdrlib: Encoders and decoders for the External Data Representation (XDR). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code></a> — Encode and decode XDR data<a class="headerlink" href="#module-xdrlib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xdrlib.py">Lib/xdrlib.py</a></p>
<div class="deprecated-removed" id="index-0">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-xdrlib" title="xdrlib: Encoders and decoders for the External Data Representation (XDR). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code></a> module is deprecated
(see <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#xdrlib"><strong>PEP 594</strong></a> for details).</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-xdrlib" title="xdrlib: Encoders and decoders for the External Data Representation (XDR). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code></a> module supports the External Data Representation Standard as
described in <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1014.html"><strong>RFC 1014</strong></a>, written by Sun Microsystems, Inc. June 1987.  It
supports most of the data types described in the RFC.</p>
<p>The <a class="reference internal" href="#module-xdrlib" title="xdrlib: Encoders and decoders for the External Data Representation (XDR). (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code></a> module defines two classes, one for packing variables into XDR
representation, and another for unpacking from XDR representation.  There are
also two exception classes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="xdrlib.Packer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xdrlib.</span></span><span class="sig-name descname"><span class="pre">Packer</span></span><a class="headerlink" href="#xdrlib.Packer" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="#xdrlib.Packer" title="xdrlib.Packer"><code class="xref py py-class docutils literal notranslate"><span class="pre">Packer</span></code></a> is the class for packing data into XDR representation. The
<a class="reference internal" href="#xdrlib.Packer" title="xdrlib.Packer"><code class="xref py py-class docutils literal notranslate"><span class="pre">Packer</span></code></a> class is instantiated with no arguments.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xdrlib.Unpacker">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xdrlib.</span></span><span class="sig-name descname"><span class="pre">Unpacker</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Unpacker</span></code> is the complementary class which unpacks XDR data values from a
string buffer.  The input buffer is given as <em>data</em>.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1014.html"><strong>RFC 1014</strong></a> - XDR: External Data Representation Standard</dt><dd><p>This RFC defined the encoding of data which was XDR at the time this module was
originally written.  It has apparently been obsoleted by <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1832.html"><strong>RFC 1832</strong></a>.</p>
</dd>
<dt><span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1832.html"><strong>RFC 1832</strong></a> - XDR: External Data Representation Standard</dt><dd><p>Newer RFC that provides a revised definition of XDR.</p>
</dd>
</dl>
</div>
<section id="packer-objects">
<span id="xdr-packer-objects"></span><h2>Packer Objects<a class="headerlink" href="#packer-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#xdrlib.Packer" title="xdrlib.Packer"><code class="xref py py-class docutils literal notranslate"><span class="pre">Packer</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.get_buffer">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">get_buffer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.get_buffer" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current pack buffer as a string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.reset">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.reset" title="Link to this definition">¶</a></dt>
<dd><p>Resets the pack buffer to the empty string.</p>
</dd></dl>

<p>In general, you can pack any of the most common XDR data types by calling the
appropriate <code class="docutils literal notranslate"><span class="pre">pack_type()</span></code> method.  Each method takes a single argument, the
value to pack.  The following simple data type packing methods are supported:
<code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_uint()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_int()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_enum()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_bool()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_uhyper()</span></code>, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_hyper()</span></code>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_float">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_float</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_float" title="Link to this definition">¶</a></dt>
<dd><p>Packs the single-precision floating point number <em>value</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_double">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_double</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_double" title="Link to this definition">¶</a></dt>
<dd><p>Packs the double-precision floating point number <em>value</em>.</p>
</dd></dl>

<p>The following methods support packing strings, bytes, and opaque data:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_fstring">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_fstring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_fstring" title="Link to this definition">¶</a></dt>
<dd><p>Packs a fixed length string, <em>s</em>.  <em>n</em> is the length of the string but it is
<em>not</em> packed into the data buffer.  The string is padded with null bytes if
necessary to guaranteed 4 byte alignment.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_fopaque">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_fopaque</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_fopaque" title="Link to this definition">¶</a></dt>
<dd><p>Packs a fixed length opaque data stream, similarly to <a class="reference internal" href="#xdrlib.Packer.pack_fstring" title="xdrlib.Packer.pack_fstring"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_fstring()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_string">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_string" title="Link to this definition">¶</a></dt>
<dd><p>Packs a variable length string, <em>s</em>.  The length of the string is first packed
as an unsigned integer, then the string data is packed with
<a class="reference internal" href="#xdrlib.Packer.pack_fstring" title="xdrlib.Packer.pack_fstring"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_fstring()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_opaque">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_opaque</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_opaque" title="Link to this definition">¶</a></dt>
<dd><p>Packs a variable length opaque data string, similarly to <a class="reference internal" href="#xdrlib.Packer.pack_string" title="xdrlib.Packer.pack_string"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_string()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_bytes">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Packs a variable length byte stream, similarly to <a class="reference internal" href="#xdrlib.Packer.pack_string" title="xdrlib.Packer.pack_string"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_string()</span></code></a>.</p>
</dd></dl>

<p>The following methods support packing arrays and lists:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_list">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">list</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pack_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_list" title="Link to this definition">¶</a></dt>
<dd><p>Packs a <em>list</em> of homogeneous items.  This method is useful for lists with an
indeterminate size; i.e. the size is not available until the entire list has
been walked.  For each item in the list, an unsigned integer <code class="docutils literal notranslate"><span class="pre">1</span></code> is packed
first, followed by the data value from the list.  <em>pack_item</em> is the function
that is called to pack the individual item.  At the end of the list, an unsigned
integer <code class="docutils literal notranslate"><span class="pre">0</span></code> is packed.</p>
<p>For example, to pack a list of integers, the code might appear like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xdrlib</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">xdrlib</span><span class="o">.</span><span class="n">Packer</span><span class="p">()</span>
<span class="n">p</span><span class="o">.</span><span class="n">pack_list</span><span class="p">([</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">],</span> <span class="n">p</span><span class="o">.</span><span class="n">pack_int</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_farray">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_farray</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">array</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pack_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_farray" title="Link to this definition">¶</a></dt>
<dd><p>Packs a fixed length list (<em>array</em>) of homogeneous items.  <em>n</em> is the length of
the list; it is <em>not</em> packed into the buffer, but a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> exception
is raised if <code class="docutils literal notranslate"><span class="pre">len(array)</span></code> is not equal to <em>n</em>.  As above, <em>pack_item</em> is the
function used to pack each element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Packer.pack_array">
<span class="sig-prename descclassname"><span class="pre">Packer.</span></span><span class="sig-name descname"><span class="pre">pack_array</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">list</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pack_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Packer.pack_array" title="Link to this definition">¶</a></dt>
<dd><p>Packs a variable length <em>list</em> of homogeneous items.  First, the length of the
list is packed as an unsigned integer, then each element is packed as in
<a class="reference internal" href="#xdrlib.Packer.pack_farray" title="xdrlib.Packer.pack_farray"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pack_farray()</span></code></a> above.</p>
</dd></dl>

</section>
<section id="unpacker-objects">
<span id="xdr-unpacker-objects"></span><h2>Unpacker Objects<a class="headerlink" href="#unpacker-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xdrlib.Unpacker" title="xdrlib.Unpacker"><code class="xref py py-class docutils literal notranslate"><span class="pre">Unpacker</span></code></a> class offers the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.reset">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.reset" title="Link to this definition">¶</a></dt>
<dd><p>Resets the string buffer with the given <em>data</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.get_position">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">get_position</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.get_position" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current unpack position in the data buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.set_position">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">set_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">position</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.set_position" title="Link to this definition">¶</a></dt>
<dd><p>Sets the data buffer unpack position to <em>position</em>.  You should be careful about
using <a class="reference internal" href="#xdrlib.Unpacker.get_position" title="xdrlib.Unpacker.get_position"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_position()</span></code></a> and <a class="reference internal" href="#xdrlib.Unpacker.set_position" title="xdrlib.Unpacker.set_position"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_position()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.get_buffer">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">get_buffer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.get_buffer" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current unpack data buffer as a string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.done">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">done</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.done" title="Link to this definition">¶</a></dt>
<dd><p>Indicates unpack completion.  Raises an <a class="reference internal" href="#xdrlib.Error" title="xdrlib.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a> exception if all of the
data has not been unpacked.</p>
</dd></dl>

<p>In addition, every data type that can be packed with a <a class="reference internal" href="#xdrlib.Packer" title="xdrlib.Packer"><code class="xref py py-class docutils literal notranslate"><span class="pre">Packer</span></code></a>, can be
unpacked with an <a class="reference internal" href="#xdrlib.Unpacker" title="xdrlib.Unpacker"><code class="xref py py-class docutils literal notranslate"><span class="pre">Unpacker</span></code></a>.  Unpacking methods are of the form
<code class="docutils literal notranslate"><span class="pre">unpack_type()</span></code>, and take no arguments.  They return the unpacked object.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_float">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_float</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_float" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks a single-precision floating point number.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_double">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_double</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_double" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks a double-precision floating point number, similarly to
<a class="reference internal" href="#xdrlib.Unpacker.unpack_float" title="xdrlib.Unpacker.unpack_float"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unpack_float()</span></code></a>.</p>
</dd></dl>

<p>In addition, the following methods unpack strings, bytes, and opaque data:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_fstring">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_fstring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_fstring" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a fixed length string.  <em>n</em> is the number of characters
expected.  Padding with null bytes to guaranteed 4 byte alignment is assumed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_fopaque">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_fopaque</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_fopaque" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a fixed length opaque data stream, similarly to
<a class="reference internal" href="#xdrlib.Unpacker.unpack_fstring" title="xdrlib.Unpacker.unpack_fstring"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unpack_fstring()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_string">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_string</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_string" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a variable length string.  The length of the string is first
unpacked as an unsigned integer, then the string data is unpacked with
<a class="reference internal" href="#xdrlib.Unpacker.unpack_fstring" title="xdrlib.Unpacker.unpack_fstring"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unpack_fstring()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_opaque">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_opaque</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_opaque" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a variable length opaque data string, similarly to
<a class="reference internal" href="#xdrlib.Unpacker.unpack_string" title="xdrlib.Unpacker.unpack_string"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unpack_string()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_bytes">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_bytes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a variable length byte stream, similarly to
<a class="reference internal" href="#xdrlib.Unpacker.unpack_string" title="xdrlib.Unpacker.unpack_string"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unpack_string()</span></code></a>.</p>
</dd></dl>

<p>The following methods support unpacking arrays and lists:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_list">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unpack_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_list" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a list of homogeneous items.  The list is unpacked one
element at a time by first unpacking an unsigned integer flag.  If the flag is
<code class="docutils literal notranslate"><span class="pre">1</span></code>, then the item is unpacked and appended to the list.  A flag of <code class="docutils literal notranslate"><span class="pre">0</span></code>
indicates the end of the list.  <em>unpack_item</em> is the function that is called to
unpack the items.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_farray">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_farray</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unpack_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_farray" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns (as a list) a fixed length array of homogeneous items.  <em>n</em>
is number of list elements to expect in the buffer. As above, <em>unpack_item</em> is
the function used to unpack each element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xdrlib.Unpacker.unpack_array">
<span class="sig-prename descclassname"><span class="pre">Unpacker.</span></span><span class="sig-name descname"><span class="pre">unpack_array</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unpack_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xdrlib.Unpacker.unpack_array" title="Link to this definition">¶</a></dt>
<dd><p>Unpacks and returns a variable length <em>list</em> of homogeneous items. First, the
length of the list is unpacked as an unsigned integer, then each element is
unpacked as in <a class="reference internal" href="#xdrlib.Unpacker.unpack_farray" title="xdrlib.Unpacker.unpack_farray"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unpack_farray()</span></code></a> above.</p>
</dd></dl>

</section>
<section id="exceptions">
<span id="xdr-exceptions"></span><h2>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h2>
<p>Exceptions in this module are coded as class instances:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="xdrlib.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xdrlib.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#xdrlib.Error" title="Link to this definition">¶</a></dt>
<dd><p>The base exception class.  <a class="reference internal" href="#xdrlib.Error" title="xdrlib.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a> has a single public attribute
<code class="xref py py-attr docutils literal notranslate"><span class="pre">msg</span></code> containing the description of the error.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xdrlib.ConversionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xdrlib.</span></span><span class="sig-name descname"><span class="pre">ConversionError</span></span><a class="headerlink" href="#xdrlib.ConversionError" title="Link to this definition">¶</a></dt>
<dd><p>Class derived from <a class="reference internal" href="#xdrlib.Error" title="xdrlib.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a>.  Contains no additional instance variables.</p>
</dd></dl>

<p>Here is an example of how you would catch one of these exceptions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xdrlib</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">xdrlib</span><span class="o">.</span><span class="n">Packer</span><span class="p">()</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">p</span><span class="o">.</span><span class="n">pack_double</span><span class="p">(</span><span class="mf">8.01</span><span class="p">)</span>
<span class="k">except</span> <span class="n">xdrlib</span><span class="o">.</span><span class="n">ConversionError</span> <span class="k">as</span> <span class="n">instance</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;packing the double failed:&#39;</span><span class="p">,</span> <span class="n">instance</span><span class="o">.</span><span class="n">msg</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code> — Encode and decode XDR data</a><ul>
<li><a class="reference internal" href="#packer-objects">Packer Objects</a></li>
<li><a class="reference internal" href="#unpacker-objects">Unpacker Objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="uu.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uu</span></code> — Encode and decode uuencode files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="security_warnings.html"
                          title="next chapter">Security Considerations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xdrlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="security_warnings.html" title="Security Considerations"
             >next</a> |</li>
        <li class="right" >
          <a href="uu.html" title="uu — Encode and decode uuencode files"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xdrlib</span></code> — Encode and decode XDR data</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>