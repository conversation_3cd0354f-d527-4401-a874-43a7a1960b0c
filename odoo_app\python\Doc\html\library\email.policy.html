<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="email.policy: Policy Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/email.policy.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/email/policy.py The email package’s prime focus is the handling of email messages as described by the various email and MIME RFCs. However, the general format of email messages (a ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/email/policy.py The email package’s prime focus is the handling of email messages as described by the various email and MIME RFCs. However, the general format of email messages (a ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>email.policy: Policy Objects &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="email.errors: Exception and Defect classes" href="email.errors.html" />
    <link rel="prev" title="email.generator: Generating MIME documents" href="email.generator.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/email.policy.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.generator.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.generator</span></code>: Generating MIME documents</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.errors.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.errors</span></code>: Exception and Defect classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/email.policy.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.errors.html" title="email.errors: Exception and Defect classes"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="email.generator.html" title="email.generator: Generating MIME documents"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
          <li class="nav-item nav-item-3"><a href="email.html" accesskey="U"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.policy</span></code>: Policy Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-email.policy">
<span id="email-policy-policy-objects"></span><h1><a class="reference internal" href="#module-email.policy" title="email.policy: Controlling the parsing and generating of messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.policy</span></code></a>: Policy Objects<a class="headerlink" href="#module-email.policy" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/email/policy.py">Lib/email/policy.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="email.html#module-email" title="email: Package supporting the parsing, manipulating, and generating email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code></a> package’s prime focus is the handling of email messages as
described by the various email and MIME RFCs.  However, the general format of
email messages (a block of header fields each consisting of a name followed by
a colon followed by a value, the whole block followed by a blank line and an
arbitrary ‘body’), is a format that has found utility outside of the realm of
email.  Some of these uses conform fairly closely to the main email RFCs, some
do not.  Even when working with email, there are times when it is desirable to
break strict compliance with the RFCs, such as generating emails that
interoperate with email servers that do not themselves follow the standards, or
that implement extensions you want to use in ways that violate the
standards.</p>
<p>Policy objects give the email package the flexibility to handle all these
disparate use cases.</p>
<p>A <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> object encapsulates a set of attributes and methods that
control the behavior of various components of the email package during use.
<a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> instances can be passed to various classes and methods in the
email package to alter the default behavior.  The settable values and their
defaults are described below.</p>
<p>There is a default policy used by all classes in the email package.  For all of
the <a class="reference internal" href="email.parser.html#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">parser</span></code></a> classes and the related convenience functions, and for
the <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> class, this is the <a class="reference internal" href="#email.policy.Compat32" title="email.policy.Compat32"><code class="xref py py-class docutils literal notranslate"><span class="pre">Compat32</span></code></a>
policy, via its corresponding pre-defined instance <a class="reference internal" href="#email.policy.compat32" title="email.policy.compat32"><code class="xref py py-const docutils literal notranslate"><span class="pre">compat32</span></code></a>.  This
policy provides for complete backward compatibility (in some cases, including
bug compatibility) with the pre-Python3.3 version of the email package.</p>
<p>This default value for the <em>policy</em> keyword to
<a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> is the <a class="reference internal" href="#email.policy.EmailPolicy" title="email.policy.EmailPolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailPolicy</span></code></a> policy, via
its pre-defined instance <a class="reference internal" href="#email.policy.default" title="email.policy.default"><code class="xref py py-data docutils literal notranslate"><span class="pre">default</span></code></a>.</p>
<p>When a <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> or <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a>
object is created, it acquires a policy.  If the message is created by a
<a class="reference internal" href="email.parser.html#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">parser</span></code></a>, a policy passed to the parser will be the policy used by
the message it creates.  If the message is created by the program, then the
policy can be specified when it is created.  When a message is passed to a
<a class="reference internal" href="email.generator.html#module-email.generator" title="email.generator: Generate flat text email messages from a message structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">generator</span></code></a>, the generator uses the policy from the message by
default, but you can also pass a specific policy to the generator that will
override the one stored on the message object.</p>
<p>The default value for the <em>policy</em> keyword for the <a class="reference internal" href="email.parser.html#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code></a> classes
and the parser convenience functions <strong>will be changing</strong> in a future version of
Python.  Therefore you should <strong>always specify explicitly which policy you want
to use</strong> when calling any of the classes and functions described in the
<a class="reference internal" href="email.parser.html#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">parser</span></code></a> module.</p>
<p>The first part of this documentation covers the features of <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a>, an
<a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base class</span></a> that defines the features that are common to all
policy objects, including <a class="reference internal" href="#email.policy.compat32" title="email.policy.compat32"><code class="xref py py-const docutils literal notranslate"><span class="pre">compat32</span></code></a>.  This includes certain hook
methods that are called internally by the email package, which a custom policy
could override to obtain different behavior.  The second part describes the
concrete classes <a class="reference internal" href="#email.policy.EmailPolicy" title="email.policy.EmailPolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailPolicy</span></code></a> and <a class="reference internal" href="#email.policy.Compat32" title="email.policy.Compat32"><code class="xref py py-class docutils literal notranslate"><span class="pre">Compat32</span></code></a>, which implement
the hooks that provide the standard behavior and the backward compatible
behavior and features, respectively.</p>
<p><a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> instances are immutable, but they can be cloned, accepting the
same keyword arguments as the class constructor and returning a new
<a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> instance that is a copy of the original but with the specified
attributes values changed.</p>
<p>As an example, the following code could be used to read an email message from a
file on disk and pass it to the system <code class="docutils literal notranslate"><span class="pre">sendmail</span></code> program on a Unix system:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">email</span> <span class="kn">import</span> <span class="n">message_from_binary_file</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">email.generator</span> <span class="kn">import</span> <span class="n">BytesGenerator</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">email</span> <span class="kn">import</span> <span class="n">policy</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">subprocess</span> <span class="kn">import</span> <span class="n">Popen</span><span class="p">,</span> <span class="n">PIPE</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;mymsg.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">msg</span> <span class="o">=</span> <span class="n">message_from_binary_file</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="n">policy</span><span class="o">=</span><span class="n">policy</span><span class="o">.</span><span class="n">default</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">([</span><span class="s1">&#39;sendmail&#39;</span><span class="p">,</span> <span class="n">msg</span><span class="p">[</span><span class="s1">&#39;To&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">addresses</span><span class="p">[</span><span class="mi">0</span><span class="p">]],</span> <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">g</span> <span class="o">=</span> <span class="n">BytesGenerator</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="p">,</span> <span class="n">policy</span><span class="o">=</span><span class="n">msg</span><span class="o">.</span><span class="n">policy</span><span class="o">.</span><span class="n">clone</span><span class="p">(</span><span class="n">linesep</span><span class="o">=</span><span class="s1">&#39;</span><span class="se">\r\n</span><span class="s1">&#39;</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">g</span><span class="o">.</span><span class="n">flatten</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">rc</span> <span class="o">=</span> <span class="n">p</span><span class="o">.</span><span class="n">wait</span><span class="p">()</span>
</pre></div>
</div>
<p>Here we are telling <a class="reference internal" href="email.generator.html#email.generator.BytesGenerator" title="email.generator.BytesGenerator"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesGenerator</span></code></a> to use the RFC
correct line separator characters when creating the binary string to feed into
<code class="docutils literal notranslate"><span class="pre">sendmail's</span></code> <code class="docutils literal notranslate"><span class="pre">stdin</span></code>, where the default policy would use <code class="docutils literal notranslate"><span class="pre">\n</span></code> line
separators.</p>
<p>Some email package methods accept a <em>policy</em> keyword argument, allowing the
policy to be overridden for that method.  For example, the following code uses
the <a class="reference internal" href="email.compat32-message.html#email.message.Message.as_bytes" title="email.message.Message.as_bytes"><code class="xref py py-meth docutils literal notranslate"><span class="pre">as_bytes()</span></code></a> method of the <em>msg</em> object from
the previous example and writes the message to a file using the native line
separators for the platform on which it is running:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;converted.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">msg</span><span class="o">.</span><span class="n">as_bytes</span><span class="p">(</span><span class="n">policy</span><span class="o">=</span><span class="n">msg</span><span class="o">.</span><span class="n">policy</span><span class="o">.</span><span class="n">clone</span><span class="p">(</span><span class="n">linesep</span><span class="o">=</span><span class="n">os</span><span class="o">.</span><span class="n">linesep</span><span class="p">)))</span>
<span class="go">17</span>
</pre></div>
</div>
<p>Policy objects can also be combined using the addition operator, producing a
policy object whose settings are a combination of the non-default values of the
summed objects:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">compat_SMTP</span> <span class="o">=</span> <span class="n">policy</span><span class="o">.</span><span class="n">compat32</span><span class="o">.</span><span class="n">clone</span><span class="p">(</span><span class="n">linesep</span><span class="o">=</span><span class="s1">&#39;</span><span class="se">\r\n</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">compat_strict</span> <span class="o">=</span> <span class="n">policy</span><span class="o">.</span><span class="n">compat32</span><span class="o">.</span><span class="n">clone</span><span class="p">(</span><span class="n">raise_on_defect</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">compat_strict_SMTP</span> <span class="o">=</span> <span class="n">compat_SMTP</span> <span class="o">+</span> <span class="n">compat_strict</span>
</pre></div>
</div>
<p>This operation is not commutative; that is, the order in which the objects are
added matters.  To illustrate:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">policy100</span> <span class="o">=</span> <span class="n">policy</span><span class="o">.</span><span class="n">compat32</span><span class="o">.</span><span class="n">clone</span><span class="p">(</span><span class="n">max_line_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">policy80</span> <span class="o">=</span> <span class="n">policy</span><span class="o">.</span><span class="n">compat32</span><span class="o">.</span><span class="n">clone</span><span class="p">(</span><span class="n">max_line_length</span><span class="o">=</span><span class="mi">80</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">apolicy</span> <span class="o">=</span> <span class="n">policy100</span> <span class="o">+</span> <span class="n">policy80</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">apolicy</span><span class="o">.</span><span class="n">max_line_length</span>
<span class="go">80</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">apolicy</span> <span class="o">=</span> <span class="n">policy80</span> <span class="o">+</span> <span class="n">policy100</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">apolicy</span><span class="o">.</span><span class="n">max_line_length</span>
<span class="go">100</span>
</pre></div>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="email.policy.Policy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">Policy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy" title="Link to this definition">¶</a></dt>
<dd><p>This is the <a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base class</span></a> for all policy classes.  It provides
default implementations for a couple of trivial methods, as well as the
implementation of the immutability property, the <a class="reference internal" href="#email.policy.Policy.clone" title="email.policy.Policy.clone"><code class="xref py py-meth docutils literal notranslate"><span class="pre">clone()</span></code></a> method, and
the constructor semantics.</p>
<p>The constructor of a policy class can be passed various keyword arguments.
The arguments that may be specified are any non-method properties on this
class, plus any additional non-method properties on the concrete class.  A
value specified in the constructor will override the default value for the
corresponding attribute.</p>
<p>This class defines the following properties, and thus values for the
following may be passed in the constructor of any policy class:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Policy.max_line_length">
<span class="sig-name descname"><span class="pre">max_line_length</span></span><a class="headerlink" href="#email.policy.Policy.max_line_length" title="Link to this definition">¶</a></dt>
<dd><p>The maximum length of any line in the serialized output, not counting the
end of line character(s).  Default is 78, per <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>.  A value of
<code class="docutils literal notranslate"><span class="pre">0</span></code> or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> indicates that no line wrapping should be
done at all.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Policy.linesep">
<span class="sig-name descname"><span class="pre">linesep</span></span><a class="headerlink" href="#email.policy.Policy.linesep" title="Link to this definition">¶</a></dt>
<dd><p>The string to be used to terminate lines in serialized output.  The
default is <code class="docutils literal notranslate"><span class="pre">\n</span></code> because that’s the internal end-of-line discipline used
by Python, though <code class="docutils literal notranslate"><span class="pre">\r\n</span></code> is required by the RFCs.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Policy.cte_type">
<span class="sig-name descname"><span class="pre">cte_type</span></span><a class="headerlink" href="#email.policy.Policy.cte_type" title="Link to this definition">¶</a></dt>
<dd><p>Controls the type of Content Transfer Encodings that may be or are
required to be used.  The possible values are:</p>
<table class="docutils align-default">
<tbody>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">7bit</span></code></p></td>
<td><p>all data must be “7 bit clean” (ASCII-only).  This means that
where necessary data will be encoded using either
quoted-printable or base64 encoding.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">8bit</span></code></p></td>
<td><p>data is not constrained to be 7 bit clean.  Data in headers is
still required to be ASCII-only and so will be encoded (see
<a class="reference internal" href="#email.policy.Policy.fold_binary" title="email.policy.Policy.fold_binary"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fold_binary()</span></code></a> and <a class="reference internal" href="#email.policy.EmailPolicy.utf8" title="email.policy.EmailPolicy.utf8"><code class="xref py py-attr docutils literal notranslate"><span class="pre">utf8</span></code></a> below for
exceptions), but body parts may use the <code class="docutils literal notranslate"><span class="pre">8bit</span></code> CTE.</p></td>
</tr>
</tbody>
</table>
<p>A <code class="docutils literal notranslate"><span class="pre">cte_type</span></code> value of <code class="docutils literal notranslate"><span class="pre">8bit</span></code> only works with <code class="docutils literal notranslate"><span class="pre">BytesGenerator</span></code>, not
<code class="docutils literal notranslate"><span class="pre">Generator</span></code>, because strings cannot contain binary data.  If a
<code class="docutils literal notranslate"><span class="pre">Generator</span></code> is operating under a policy that specifies
<code class="docutils literal notranslate"><span class="pre">cte_type=8bit</span></code>, it will act as if <code class="docutils literal notranslate"><span class="pre">cte_type</span></code> is <code class="docutils literal notranslate"><span class="pre">7bit</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Policy.raise_on_defect">
<span class="sig-name descname"><span class="pre">raise_on_defect</span></span><a class="headerlink" href="#email.policy.Policy.raise_on_defect" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, any defects encountered will be raised as errors.  If
<a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> (the default), defects will be passed to the
<a class="reference internal" href="#email.policy.Policy.register_defect" title="email.policy.Policy.register_defect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_defect()</span></code></a> method.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Policy.mangle_from_">
<span class="sig-name descname"><span class="pre">mangle_from_</span></span><a class="headerlink" href="#email.policy.Policy.mangle_from_" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, lines starting with <em>“From “</em> in the body are
escaped by putting a <code class="docutils literal notranslate"><span class="pre">&gt;</span></code> in front of them. This parameter is used when
the message is being serialized by a generator.
Default: <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Policy.message_factory">
<span class="sig-name descname"><span class="pre">message_factory</span></span><a class="headerlink" href="#email.policy.Policy.message_factory" title="Link to this definition">¶</a></dt>
<dd><p>A factory function for constructing a new empty message object.  Used
by the parser when building messages.  Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>, in
which case <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> is used.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<p>The following <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> method is intended to be called by code using
the email library to create policy instances with custom settings:</p>
<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.clone">
<span class="sig-name descname"><span class="pre">clone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.clone" title="Link to this definition">¶</a></dt>
<dd><p>Return a new <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> instance whose attributes have the same
values as the current instance, except where those attributes are
given new values by the keyword arguments.</p>
</dd></dl>

<p>The remaining <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> methods are called by the email package code,
and are not intended to be called by an application using the email package.
A custom policy must implement all of these methods.</p>
<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.handle_defect">
<span class="sig-name descname"><span class="pre">handle_defect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">defect</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.handle_defect" title="Link to this definition">¶</a></dt>
<dd><p>Handle a <em>defect</em> found on <em>obj</em>.  When the email package calls this
method, <em>defect</em> will always be a subclass of
<code class="xref py py-class docutils literal notranslate"><span class="pre">Defect</span></code>.</p>
<p>The default implementation checks the <a class="reference internal" href="#email.policy.Policy.raise_on_defect" title="email.policy.Policy.raise_on_defect"><code class="xref py py-attr docutils literal notranslate"><span class="pre">raise_on_defect</span></code></a> flag.  If
it is <code class="docutils literal notranslate"><span class="pre">True</span></code>, <em>defect</em> is raised as an exception.  If it is <code class="docutils literal notranslate"><span class="pre">False</span></code>
(the default), <em>obj</em> and <em>defect</em> are passed to <a class="reference internal" href="#email.policy.Policy.register_defect" title="email.policy.Policy.register_defect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_defect()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.register_defect">
<span class="sig-name descname"><span class="pre">register_defect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">defect</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.register_defect" title="Link to this definition">¶</a></dt>
<dd><p>Register a <em>defect</em> on <em>obj</em>.  In the email package, <em>defect</em> will always
be a subclass of <code class="xref py py-class docutils literal notranslate"><span class="pre">Defect</span></code>.</p>
<p>The default implementation calls the <code class="docutils literal notranslate"><span class="pre">append</span></code> method of the <code class="docutils literal notranslate"><span class="pre">defects</span></code>
attribute of <em>obj</em>.  When the email package calls <a class="reference internal" href="#email.policy.Policy.handle_defect" title="email.policy.Policy.handle_defect"><code class="xref py py-attr docutils literal notranslate"><span class="pre">handle_defect</span></code></a>,
<em>obj</em> will normally have a <code class="docutils literal notranslate"><span class="pre">defects</span></code> attribute that has an <code class="docutils literal notranslate"><span class="pre">append</span></code>
method.  Custom object types used with the email package (for example,
custom <code class="docutils literal notranslate"><span class="pre">Message</span></code> objects) should also provide such an attribute,
otherwise defects in parsed messages will raise unexpected errors.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.header_max_count">
<span class="sig-name descname"><span class="pre">header_max_count</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.header_max_count" title="Link to this definition">¶</a></dt>
<dd><p>Return the maximum allowed number of headers named <em>name</em>.</p>
<p>Called when a header is added to an <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a>
or <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> object.  If the returned value is not
<code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">None</span></code>, and there are already a number of headers with the
name <em>name</em> greater than or equal to the value returned, a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p>Because the default behavior of <code class="docutils literal notranslate"><span class="pre">Message.__setitem__</span></code> is to append the
value to the list of headers, it is easy to create duplicate headers
without realizing it.  This method allows certain headers to be limited
in the number of instances of that header that may be added to a
<code class="docutils literal notranslate"><span class="pre">Message</span></code> programmatically.  (The limit is not observed by the parser,
which will faithfully produce as many headers as exist in the message
being parsed.)</p>
<p>The default implementation returns <code class="docutils literal notranslate"><span class="pre">None</span></code> for all header names.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.header_source_parse">
<span class="sig-name descname"><span class="pre">header_source_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sourcelines</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.header_source_parse" title="Link to this definition">¶</a></dt>
<dd><p>The email package calls this method with a list of strings, each string
ending with the line separation characters found in the source being
parsed.  The first line includes the field header name and separator.
All whitespace in the source is preserved.  The method should return the
<code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code> tuple that is to be stored in the <code class="docutils literal notranslate"><span class="pre">Message</span></code> to
represent the parsed header.</p>
<p>If an implementation wishes to retain compatibility with the existing
email package policies, <em>name</em> should be the case preserved name (all
characters up to the ‘<code class="docutils literal notranslate"><span class="pre">:</span></code>’ separator), while <em>value</em> should be the
unfolded value (all line separator characters removed, but whitespace
kept intact), stripped of leading whitespace.</p>
<p><em>sourcelines</em> may contain surrogateescaped binary data.</p>
<p>There is no default implementation</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.header_store_parse">
<span class="sig-name descname"><span class="pre">header_store_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.header_store_parse" title="Link to this definition">¶</a></dt>
<dd><p>The email package calls this method with the name and value provided by
the application program when the application program is modifying a
<code class="docutils literal notranslate"><span class="pre">Message</span></code> programmatically (as opposed to a <code class="docutils literal notranslate"><span class="pre">Message</span></code> created by a
parser).  The method should return the <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code> tuple that is to
be stored in the <code class="docutils literal notranslate"><span class="pre">Message</span></code> to represent the header.</p>
<p>If an implementation wishes to retain compatibility with the existing
email package policies, the <em>name</em> and <em>value</em> should be strings or
string subclasses that do not change the content of the passed in
arguments.</p>
<p>There is no default implementation</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.header_fetch_parse">
<span class="sig-name descname"><span class="pre">header_fetch_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.header_fetch_parse" title="Link to this definition">¶</a></dt>
<dd><p>The email package calls this method with the <em>name</em> and <em>value</em> currently
stored in the <code class="docutils literal notranslate"><span class="pre">Message</span></code> when that header is requested by the
application program, and whatever the method returns is what is passed
back to the application as the value of the header being retrieved.
Note that there may be more than one header with the same name stored in
the <code class="docutils literal notranslate"><span class="pre">Message</span></code>; the method is passed the specific name and value of the
header destined to be returned to the application.</p>
<p><em>value</em> may contain surrogateescaped binary data.  There should be no
surrogateescaped binary data in the value returned by the method.</p>
<p>There is no default implementation</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.fold">
<span class="sig-name descname"><span class="pre">fold</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.fold" title="Link to this definition">¶</a></dt>
<dd><p>The email package calls this method with the <em>name</em> and <em>value</em> currently
stored in the <code class="docutils literal notranslate"><span class="pre">Message</span></code> for a given header.  The method should return a
string that represents that header “folded” correctly (according to the
policy settings) by composing the <em>name</em> with the <em>value</em> and inserting
<a class="reference internal" href="#email.policy.Policy.linesep" title="email.policy.Policy.linesep"><code class="xref py py-attr docutils literal notranslate"><span class="pre">linesep</span></code></a> characters at the appropriate places.  See <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>
for a discussion of the rules for folding email headers.</p>
<p><em>value</em> may contain surrogateescaped binary data.  There should be no
surrogateescaped binary data in the string returned by the method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Policy.fold_binary">
<span class="sig-name descname"><span class="pre">fold_binary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Policy.fold_binary" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#email.policy.Policy.fold" title="email.policy.Policy.fold"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fold()</span></code></a>, except that the returned value should be a
bytes object rather than a string.</p>
<p><em>value</em> may contain surrogateescaped binary data.  These could be
converted back into binary data in the returned bytes object.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="email.policy.EmailPolicy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">EmailPolicy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy" title="Link to this definition">¶</a></dt>
<dd><p>This concrete <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> provides behavior that is intended to be fully
compliant with the current email RFCs.  These include (but are not limited
to) <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>, <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2047.html"><strong>RFC 2047</strong></a>, and the current MIME RFCs.</p>
<p>This policy adds new header parsing and folding algorithms.  Instead of
simple strings, headers are <code class="docutils literal notranslate"><span class="pre">str</span></code> subclasses with attributes that depend
on the type of the field.  The parsing and folding algorithm fully implement
<span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2047.html"><strong>RFC 2047</strong></a> and <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>.</p>
<p>The default value for the <a class="reference internal" href="#email.policy.Policy.message_factory" title="email.policy.Policy.message_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">message_factory</span></code></a>
attribute is <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a>.</p>
<p>In addition to the settable attributes listed above that apply to all
policies, this policy adds the following additional attributes:</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span><a class="footnote-reference brackets" href="#id2" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.utf8">
<span class="sig-name descname"><span class="pre">utf8</span></span><a class="headerlink" href="#email.policy.EmailPolicy.utf8" title="Link to this definition">¶</a></dt>
<dd><p>If <code class="docutils literal notranslate"><span class="pre">False</span></code>, follow <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>, supporting non-ASCII characters in
headers by encoding them as “encoded words”.  If <code class="docutils literal notranslate"><span class="pre">True</span></code>, follow
<span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6532.html"><strong>RFC 6532</strong></a> and use <code class="docutils literal notranslate"><span class="pre">utf-8</span></code> encoding for headers.  Messages
formatted in this way may be passed to SMTP servers that support
the <code class="docutils literal notranslate"><span class="pre">SMTPUTF8</span></code> extension (<span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6531.html"><strong>RFC 6531</strong></a>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.refold_source">
<span class="sig-name descname"><span class="pre">refold_source</span></span><a class="headerlink" href="#email.policy.EmailPolicy.refold_source" title="Link to this definition">¶</a></dt>
<dd><p>If the value for a header in the <code class="docutils literal notranslate"><span class="pre">Message</span></code> object originated from a
<a class="reference internal" href="email.parser.html#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">parser</span></code></a> (as opposed to being set by a program), this
attribute indicates whether or not a generator should refold that value
when transforming the message back into serialized form.  The possible
values are:</p>
<table class="docutils align-default">
<tbody>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">none</span></code></p></td>
<td><p>all source values use original folding</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">long</span></code></p></td>
<td><p>source values that have any line that is longer than
<code class="docutils literal notranslate"><span class="pre">max_line_length</span></code> will be refolded</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">all</span></code></p></td>
<td><p>all values are refolded.</p></td>
</tr>
</tbody>
</table>
<p>The default is <code class="docutils literal notranslate"><span class="pre">long</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.header_factory">
<span class="sig-name descname"><span class="pre">header_factory</span></span><a class="headerlink" href="#email.policy.EmailPolicy.header_factory" title="Link to this definition">¶</a></dt>
<dd><p>A callable that takes two arguments, <code class="docutils literal notranslate"><span class="pre">name</span></code> and <code class="docutils literal notranslate"><span class="pre">value</span></code>, where
<code class="docutils literal notranslate"><span class="pre">name</span></code> is a header field name and <code class="docutils literal notranslate"><span class="pre">value</span></code> is an unfolded header field
value, and returns a string subclass that represents that header.  A
default <code class="docutils literal notranslate"><span class="pre">header_factory</span></code> (see <a class="reference internal" href="email.headerregistry.html#module-email.headerregistry" title="email.headerregistry: Automatic Parsing of headers based on the field name"><code class="xref py py-mod docutils literal notranslate"><span class="pre">headerregistry</span></code></a>) is provided
that supports custom parsing for the various address and date <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>
header field types, and the major MIME header field stypes.  Support for
additional custom parsing will be added in the future.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.content_manager">
<span class="sig-name descname"><span class="pre">content_manager</span></span><a class="headerlink" href="#email.policy.EmailPolicy.content_manager" title="Link to this definition">¶</a></dt>
<dd><p>An object with at least two methods: get_content and set_content.  When
the <a class="reference internal" href="email.message.html#email.message.EmailMessage.get_content" title="email.message.EmailMessage.get_content"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_content()</span></code></a> or
<a class="reference internal" href="email.message.html#email.message.EmailMessage.set_content" title="email.message.EmailMessage.set_content"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_content()</span></code></a> method of an
<a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> object is called, it calls the
corresponding method of this object, passing it the message object as its
first argument, and any arguments or keywords that were passed to it as
additional arguments.  By default <code class="docutils literal notranslate"><span class="pre">content_manager</span></code> is set to
<a class="reference internal" href="email.contentmanager.html#email.contentmanager.raw_data_manager" title="email.contentmanager.raw_data_manager"><code class="xref py py-data docutils literal notranslate"><span class="pre">raw_data_manager</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<p>The class provides the following concrete implementations of the abstract
methods of <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.header_max_count">
<span class="sig-name descname"><span class="pre">header_max_count</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy.header_max_count" title="Link to this definition">¶</a></dt>
<dd><p>Returns the value of the
<a class="reference internal" href="email.headerregistry.html#email.headerregistry.BaseHeader.max_count" title="email.headerregistry.BaseHeader.max_count"><code class="xref py py-attr docutils literal notranslate"><span class="pre">max_count</span></code></a> attribute of the
specialized class used to represent the header with the given name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.header_source_parse">
<span class="sig-name descname"><span class="pre">header_source_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sourcelines</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy.header_source_parse" title="Link to this definition">¶</a></dt>
<dd><p>The name is parsed as everything up to the ‘<code class="docutils literal notranslate"><span class="pre">:</span></code>’ and returned
unmodified.  The value is determined by stripping leading whitespace off
the remainder of the first line, joining all subsequent lines together,
and stripping any trailing carriage return or linefeed characters.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.header_store_parse">
<span class="sig-name descname"><span class="pre">header_store_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy.header_store_parse" title="Link to this definition">¶</a></dt>
<dd><p>The name is returned unchanged.  If the input value has a <code class="docutils literal notranslate"><span class="pre">name</span></code>
attribute and it matches <em>name</em> ignoring case, the value is returned
unchanged.  Otherwise the <em>name</em> and <em>value</em> are passed to
<code class="docutils literal notranslate"><span class="pre">header_factory</span></code>, and the resulting header object is returned as
the value.  In this case a <code class="docutils literal notranslate"><span class="pre">ValueError</span></code> is raised if the input value
contains CR or LF characters.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.header_fetch_parse">
<span class="sig-name descname"><span class="pre">header_fetch_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy.header_fetch_parse" title="Link to this definition">¶</a></dt>
<dd><p>If the value has a <code class="docutils literal notranslate"><span class="pre">name</span></code> attribute, it is returned to unmodified.
Otherwise the <em>name</em>, and the <em>value</em> with any CR or LF characters
removed, are passed to the <code class="docutils literal notranslate"><span class="pre">header_factory</span></code>, and the resulting
header object is returned.  Any surrogateescaped bytes get turned into
the unicode unknown-character glyph.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.fold">
<span class="sig-name descname"><span class="pre">fold</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy.fold" title="Link to this definition">¶</a></dt>
<dd><p>Header folding is controlled by the <a class="reference internal" href="#email.policy.EmailPolicy.refold_source" title="email.policy.EmailPolicy.refold_source"><code class="xref py py-attr docutils literal notranslate"><span class="pre">refold_source</span></code></a> policy setting.
A value is considered to be a ‘source value’ if and only if it does not
have a <code class="docutils literal notranslate"><span class="pre">name</span></code> attribute (having a <code class="docutils literal notranslate"><span class="pre">name</span></code> attribute means it is a
header object of some sort).  If a source value needs to be refolded
according to the policy, it is converted into a header object by
passing the <em>name</em> and the <em>value</em> with any CR and LF characters removed
to the <code class="docutils literal notranslate"><span class="pre">header_factory</span></code>.  Folding of a header object is done by
calling its <code class="docutils literal notranslate"><span class="pre">fold</span></code> method with the current policy.</p>
<p>Source values are split into lines using <a class="reference internal" href="stdtypes.html#str.splitlines" title="str.splitlines"><code class="xref py py-meth docutils literal notranslate"><span class="pre">splitlines()</span></code></a>.  If
the value is not to be refolded, the lines are rejoined using the
<code class="docutils literal notranslate"><span class="pre">linesep</span></code> from the policy and returned.  The exception is lines
containing non-ascii binary data.  In that case the value is refolded
regardless of the <code class="docutils literal notranslate"><span class="pre">refold_source</span></code> setting, which causes the binary data
to be CTE encoded using the <code class="docutils literal notranslate"><span class="pre">unknown-8bit</span></code> charset.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.EmailPolicy.fold_binary">
<span class="sig-name descname"><span class="pre">fold_binary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.EmailPolicy.fold_binary" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#email.policy.EmailPolicy.fold" title="email.policy.EmailPolicy.fold"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fold()</span></code></a> if <a class="reference internal" href="#email.policy.Policy.cte_type" title="email.policy.Policy.cte_type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">cte_type</span></code></a> is <code class="docutils literal notranslate"><span class="pre">7bit</span></code>, except
that the returned value is bytes.</p>
<p>If <a class="reference internal" href="#email.policy.Policy.cte_type" title="email.policy.Policy.cte_type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">cte_type</span></code></a> is <code class="docutils literal notranslate"><span class="pre">8bit</span></code>, non-ASCII binary data is
converted back
into bytes.  Headers with binary data are not refolded, regardless of the
<code class="docutils literal notranslate"><span class="pre">refold_header</span></code> setting, since there is no way to know whether the
binary data consists of single byte characters or multibyte characters.</p>
</dd></dl>

</dd></dl>

<p>The following instances of <a class="reference internal" href="#email.policy.EmailPolicy" title="email.policy.EmailPolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailPolicy</span></code></a> provide defaults suitable for
specific application domains.  Note that in the future the behavior of these
instances (in particular the <code class="docutils literal notranslate"><span class="pre">HTTP</span></code> instance) may be adjusted to conform even
more closely to the RFCs relevant to their domains.</p>
<dl class="py data">
<dt class="sig sig-object py" id="email.policy.default">
<span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">default</span></span><a class="headerlink" href="#email.policy.default" title="Link to this definition">¶</a></dt>
<dd><p>An instance of <code class="docutils literal notranslate"><span class="pre">EmailPolicy</span></code> with all defaults unchanged.  This policy
uses the standard Python <code class="docutils literal notranslate"><span class="pre">\n</span></code> line endings rather than the RFC-correct
<code class="docutils literal notranslate"><span class="pre">\r\n</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="email.policy.SMTP">
<span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">SMTP</span></span><a class="headerlink" href="#email.policy.SMTP" title="Link to this definition">¶</a></dt>
<dd><p>Suitable for serializing messages in conformance with the email RFCs.
Like <code class="docutils literal notranslate"><span class="pre">default</span></code>, but with <code class="docutils literal notranslate"><span class="pre">linesep</span></code> set to <code class="docutils literal notranslate"><span class="pre">\r\n</span></code>, which is RFC
compliant.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="email.policy.SMTPUTF8">
<span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">SMTPUTF8</span></span><a class="headerlink" href="#email.policy.SMTPUTF8" title="Link to this definition">¶</a></dt>
<dd><p>The same as <code class="docutils literal notranslate"><span class="pre">SMTP</span></code> except that <a class="reference internal" href="#email.policy.EmailPolicy.utf8" title="email.policy.EmailPolicy.utf8"><code class="xref py py-attr docutils literal notranslate"><span class="pre">utf8</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>.
Useful for serializing messages to a message store without using encoded
words in the headers.  Should only be used for SMTP transmission if the
sender or recipient addresses have non-ASCII characters (the
<a class="reference internal" href="smtplib.html#smtplib.SMTP.send_message" title="smtplib.SMTP.send_message"><code class="xref py py-meth docutils literal notranslate"><span class="pre">smtplib.SMTP.send_message()</span></code></a> method handles this automatically).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="email.policy.HTTP">
<span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">HTTP</span></span><a class="headerlink" href="#email.policy.HTTP" title="Link to this definition">¶</a></dt>
<dd><p>Suitable for serializing headers with for use in HTTP traffic.  Like
<code class="docutils literal notranslate"><span class="pre">SMTP</span></code> except that <code class="docutils literal notranslate"><span class="pre">max_line_length</span></code> is set to <code class="docutils literal notranslate"><span class="pre">None</span></code> (unlimited).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="email.policy.strict">
<span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">strict</span></span><a class="headerlink" href="#email.policy.strict" title="Link to this definition">¶</a></dt>
<dd><p>Convenience instance.  The same as <code class="docutils literal notranslate"><span class="pre">default</span></code> except that
<code class="docutils literal notranslate"><span class="pre">raise_on_defect</span></code> is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>.  This allows any policy to be made
strict by writing:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">somepolicy</span> <span class="o">+</span> <span class="n">policy</span><span class="o">.</span><span class="n">strict</span>
</pre></div>
</div>
</dd></dl>

<p>With all of these <a class="reference internal" href="#email.policy.EmailPolicy" title="email.policy.EmailPolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailPolicies</span></code></a>, the effective API of
the email package is changed from the Python 3.2 API in the following ways:</p>
<ul class="simple">
<li><p>Setting a header on a <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> results in that
header being parsed and a header object created.</p></li>
<li><p>Fetching a header value from a <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> results
in that header being parsed and a header object created and
returned.</p></li>
<li><p>Any header object, or any header that is refolded due to the
policy settings, is folded using an algorithm that fully implements the
RFC folding algorithms, including knowing where encoded words are required
and allowed.</p></li>
</ul>
<p>From the application view, this means that any header obtained through the
<a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> is a header object with extra
attributes, whose string value is the fully decoded unicode value of the
header.  Likewise, a header may be assigned a new value, or a new header
created, using a unicode string, and the policy will take care of converting
the unicode string into the correct RFC encoded form.</p>
<p>The header objects and their attributes are described in
<a class="reference internal" href="email.headerregistry.html#module-email.headerregistry" title="email.headerregistry: Automatic Parsing of headers based on the field name"><code class="xref py py-mod docutils literal notranslate"><span class="pre">headerregistry</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="email.policy.Compat32">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">Compat32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Compat32" title="Link to this definition">¶</a></dt>
<dd><p>This concrete <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> is the backward compatibility policy.  It
replicates the behavior of the email package in Python 3.2.  The
<a class="reference internal" href="#module-email.policy" title="email.policy: Controlling the parsing and generating of messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">policy</span></code></a> module also defines an instance of this class,
<a class="reference internal" href="#email.policy.compat32" title="email.policy.compat32"><code class="xref py py-const docutils literal notranslate"><span class="pre">compat32</span></code></a>, that is used as the default policy.  Thus the default
behavior of the email package is to maintain compatibility with Python 3.2.</p>
<p>The following attributes have values that are different from the
<a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a> default:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="email.policy.Compat32.mangle_from_">
<span class="sig-name descname"><span class="pre">mangle_from_</span></span><a class="headerlink" href="#email.policy.Compat32.mangle_from_" title="Link to this definition">¶</a></dt>
<dd><p>The default is <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</dd></dl>

<p>The class provides the following concrete implementations of the
abstract methods of <a class="reference internal" href="#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Compat32.header_source_parse">
<span class="sig-name descname"><span class="pre">header_source_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sourcelines</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Compat32.header_source_parse" title="Link to this definition">¶</a></dt>
<dd><p>The name is parsed as everything up to the ‘<code class="docutils literal notranslate"><span class="pre">:</span></code>’ and returned
unmodified.  The value is determined by stripping leading whitespace off
the remainder of the first line, joining all subsequent lines together,
and stripping any trailing carriage return or linefeed characters.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Compat32.header_store_parse">
<span class="sig-name descname"><span class="pre">header_store_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Compat32.header_store_parse" title="Link to this definition">¶</a></dt>
<dd><p>The name and value are returned unmodified.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Compat32.header_fetch_parse">
<span class="sig-name descname"><span class="pre">header_fetch_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Compat32.header_fetch_parse" title="Link to this definition">¶</a></dt>
<dd><p>If the value contains binary data, it is converted into a
<a class="reference internal" href="email.header.html#email.header.Header" title="email.header.Header"><code class="xref py py-class docutils literal notranslate"><span class="pre">Header</span></code></a> object using the <code class="docutils literal notranslate"><span class="pre">unknown-8bit</span></code> charset.
Otherwise it is returned unmodified.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Compat32.fold">
<span class="sig-name descname"><span class="pre">fold</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Compat32.fold" title="Link to this definition">¶</a></dt>
<dd><p>Headers are folded using the <a class="reference internal" href="email.header.html#email.header.Header" title="email.header.Header"><code class="xref py py-class docutils literal notranslate"><span class="pre">Header</span></code></a> folding
algorithm, which preserves existing line breaks in the value, and wraps
each resulting line to the <code class="docutils literal notranslate"><span class="pre">max_line_length</span></code>.  Non-ASCII binary data are
CTE encoded using the <code class="docutils literal notranslate"><span class="pre">unknown-8bit</span></code> charset.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.policy.Compat32.fold_binary">
<span class="sig-name descname"><span class="pre">fold_binary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.policy.Compat32.fold_binary" title="Link to this definition">¶</a></dt>
<dd><p>Headers are folded using the <a class="reference internal" href="email.header.html#email.header.Header" title="email.header.Header"><code class="xref py py-class docutils literal notranslate"><span class="pre">Header</span></code></a> folding
algorithm, which preserves existing line breaks in the value, and wraps
each resulting line to the <code class="docutils literal notranslate"><span class="pre">max_line_length</span></code>.  If <code class="docutils literal notranslate"><span class="pre">cte_type</span></code> is
<code class="docutils literal notranslate"><span class="pre">7bit</span></code>, non-ascii binary data is CTE encoded using the <code class="docutils literal notranslate"><span class="pre">unknown-8bit</span></code>
charset.  Otherwise the original source header is used, with its existing
line breaks and any (RFC invalid) binary data it may contain.</p>
</dd></dl>

</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="email.policy.compat32">
<span class="sig-prename descclassname"><span class="pre">email.policy.</span></span><span class="sig-name descname"><span class="pre">compat32</span></span><a class="headerlink" href="#email.policy.compat32" title="Link to this definition">¶</a></dt>
<dd><p>An instance of <a class="reference internal" href="#email.policy.Compat32" title="email.policy.Compat32"><code class="xref py py-class docutils literal notranslate"><span class="pre">Compat32</span></code></a>, providing  backward compatibility with the
behavior of the email package in Python 3.2.</p>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id2" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>Originally added in 3.3 as a <a class="reference internal" href="../glossary.html#term-provisional-package"><span class="xref std std-term">provisional feature</span></a>.</p>
</aside>
</aside>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.generator.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.generator</span></code>: Generating MIME documents</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.errors.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.errors</span></code>: Exception and Defect classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/email.policy.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.errors.html" title="email.errors: Exception and Defect classes"
             >next</a> |</li>
        <li class="right" >
          <a href="email.generator.html" title="email.generator: Generating MIME documents"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
          <li class="nav-item nav-item-3"><a href="email.html" ><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.policy</span></code>: Policy Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>