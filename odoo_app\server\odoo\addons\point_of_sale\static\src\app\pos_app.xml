<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.Chrome">
        <div class="pos dvh-100 d-flex flex-column" t-att-class="{ 'big-scrollbars': pos.hasBigScrollBars }">
            <Navbar />
            <div class="pos-content flex-grow-1 overflow-auto bg-200">
                <!-- FIXME POSREF: better error handling in main screens (currently, a crash in owl lifecycle of a main screen blows up the application and the error can't be displayed) -->
                <t isShown="!pos.tempScreen" t-component="pos.mainScreen.component"
                    t-props="pos.mainScreen.props"/>
                <ErrorHandler onError.bind="onTempScreenError">
                    <t t-if="pos.tempScreen" t-component="pos.tempScreen.component"
                        t-props="pos.tempScreen.props" t-key="pos.tempScreen.name" />
                </ErrorHandler>
            </div>
        </div>
        <MainComponentsContainer/>
    </t>

</templates>
