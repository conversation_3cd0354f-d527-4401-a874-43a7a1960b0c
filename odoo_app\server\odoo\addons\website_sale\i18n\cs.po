# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# ka<PERSON><PERSON><PERSON>chus<PERSON> <karolina.schus<PERSON><PERSON>@vdp.sk>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# Vojtech Smolka, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" kategorie."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"\"Optional\" allows guests to register from the order confirmation email to "
"track their order."
msgstr ""
"\"Volitelné\" umožňuje návštěvníkům registrovat se pomocí e-mailu s "
"potvrzením objednávky, aby mohli sledovat stav své objednávky."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s kontrola"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s kontrol"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "&amp; Shipping"
msgstr "&amp; Dodací adresa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "&amp;nbsp;(<i>Your shipping address will be requested later)</i>"
msgstr "&amp;nbsp;(<i>Vaše dodací adresa bude vyžádána později)</i>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"
msgstr "&amp;nbsp;položka/y&amp;nbsp;-&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. Zobrazeny výsledky pro '"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__100_pc
msgid "100 %"
msgstr "100 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "100 percent"
msgstr "100 procent"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__50_pc
msgid "50 %"
msgstr "50 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "50 percent"
msgstr "50 procent"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__66_pc
msgid "66 %"
msgstr "66 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "66 percent"
msgstr "66 procent"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<b class=\"w-100\">Order summary</b>"
msgstr "<b class=\"w-100\">Shrnutí objednávky</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
msgid "<b>Categories</b>"
msgstr "<b>Kategorie</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Komunikace: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
msgid "<b>Price Range</b>"
msgstr "<b>Cenové rozpětí</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Pricelist</b>"
msgstr "<b>Ceník</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<b>Shipping: </b>"
msgstr "<b>Dodací adresa: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Sort By</b>"
msgstr "<b>Třídit podle</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_tags
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Tags</b>"
msgstr "<b>Štítky</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"30denní záruka vrácení peněz<br/>\n"
"Doprava: 2-3 pracovní dny"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Return to shipping"
msgstr ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Zpět na dopravu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Buy now"
msgstr ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Koupit nyní"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart
msgid "<i class=\"fa fa-cart-plus me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-cart-plus me-2\"/> Přidat do košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Buy Now"
msgstr ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Koupit teď"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Upravit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.row_addresses
msgid ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-inline\">Add "
"address</span>"
msgstr ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-inline\">Přidat "
"adresu</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print me-2\"/>Print"
msgstr "<i class=\"fa fa-print me-2\"/>Vytisknout"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_re_order_btn
msgid ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Order Again"
msgstr ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Znovu objednat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Add to cart"
msgstr "<i class=\"fa fa-shopping-cart me-2\"/> Přidat do košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<i class=\"fw-light fa fa-angle-left me-2\"/>Discard"
msgstr "<i class=\"fw-light fa fa-angle-left me-2\"/>Zrušit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<option value=\"\" selected=\"true\">-</option>"
msgstr "<option value=\"\" selected=\"true\">-</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Země...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">Stát / Oblast...</option>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"Zde najdete všechny opuštěné košíky, tj. košíky vytvořené návštěvníky vašeho webu před více než hodinou, které dosud nebyly potvrzeny.</p>\n"
"<p>Zákazníkům byste měli poslat e-mail, abyste je povzbudili!</p>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<small class=\"d-none d-lg-inline text-muted\">Sort By:</small>"
msgstr "<small class=\"d-none d-lg-inline text-muted\">Třídit podle:</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">Změna názvu společnosti nebo DIČ není "
"po vystavení dokumentu (dokumentů) pro váš účet povolena. Pro tuto operaci "
"nás prosím kontaktujte.</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid ""
"<small class=\"mx-auto\"><b>Clear Filters</b></small>\n"
"                        <i class=\"oi oi-close\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Add to cart</span>"
msgstr ""
"<span class=\"d-md-none fa fa-shopping-cart\"/> <span class=\"d-none d-md-"
"inline\">Přidat do košíku</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "<span class=\"mx-2 o_wsale_ppr_by\">by</span>"
msgstr "<span class=\"mx-2 o_wsale_ppr_by\">podle</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_order_location\">\n"
"                    <b class=\"o_order_location_name\"/>\n"
"                    <br/>\n"
"                    <i class=\"o_order_location_address\"/>\n"
"                </span>\n"
"                <span class=\"fa fa-times ms-2 o_remove_order_location\" aria-label=\"Remove this location\" title=\"Remove this location\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">Select to compute delivery rate</span>"
msgstr ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">Zvolte pro výpočet rychlosti doručení</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Next\" title=\"Next\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "<span class=\"px-3\">or</span>"
msgstr "<span class=\"px-3\">nebo</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Dejte nám zpětnou vazbu</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">Nahrát dokument</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">Vaše reference</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Based"
" on variants</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Na "
"základě variant</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "<span class=\"visually-hidden\">filters active</span>"
msgstr "<span class=\"visually-hidden\">aktivní filtry</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>Objednávka</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_kanban
msgid "<span>Show on product page</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>Bohužel vaše objednávka nemůže být potvrzená, protože částka vaší platby se neshoduje s částkou ve vašem košíku.\n"
"                        Kontaktujte prosím odpovědnou osobu z obchodu pro více informací.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>Video náhled</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong><i class=\"oi oi-arrow-right\"/> View alternatives</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Zobrazit alternativy</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong>No suitable payment option could be found.</strong><br/>"
msgstr "<strong>Nebyla nalezena žádná vhodná možnost platby.</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Celkem:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total</strong>"
msgstr "<strong>Celkem</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<strong>Warning!</strong>"
msgstr "<strong>Varování!</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- OBSAH -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">NĚCO MÁTE VE VAŠEM KOŠÍKU.</h1>\n"
"                    Chcete dokončit svůj nákup?<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Kombinace psacího stolu</strong><br><t t-out=\"line.name or ''\">[FURN_7800] Kombinace psacího stolu Kombinace psacího stolu, černo-hnědá: židle + stůl + zásuvka.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Jednotek</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Obnovit objednávku\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>Děkujeme, že jste nakupovali s <t t-out=\"company.name or ''\">Mojí společností (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "<u>Terms and Conditions</u>"
msgstr "<u>Obchodní podmínky</u>"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Popis produktu, který chcete sdělit vašim zákazníkům. Tento popis bude "
"zkopírován do každé objednávky, objednávky a zákaznické faktury / kreditní "
"poznámky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"A detailed, formatted description to promote your product on this page. Use "
"'/' to discover more features."
msgstr ""
"Podrobný, formátovaný popis pro propagaci vašeho produktu na této stránce. "
"Použijte '/' pro zobrazení více funkcí."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"Produkt může být buď fyzický produkt nebo služba, kterou prodáváte svým "
"zákazníkům."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "Stručný popis, který se také objeví v dokumentech."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "Opuštěný"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
#: model:ir.model.fields,field_description:website_sale.field_sale_report__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "Opuštěný košík"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "Opuštěné košíky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "Opuštěné košíky k obnově"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "Zpoždění opuštění"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__send_abandoned_cart_email
msgid "Abandoned Email"
msgstr "Opuštěný e-mail"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "O křížovém prodeji produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Accept Terms & Conditions"
msgstr "Akceptujte obchodní podmínky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "Přijatelná velikost souboru"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "Doplňky k produktu"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"Příslušenství se zobrazí, když zákazník zkontroluje košík před platbou "
"(strategie křížového prodeje)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "Doplňkové produkty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Action"
msgstr "Akce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Vyžadována akce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add"
msgstr "Přidat"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Add To Cart"
msgstr "Přidat do košíku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__add_to_cart_action
#: model:ir.model.fields,field_description:website_sale.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Přidat do košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "Přidat média"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a customizable form during checkout (after address)"
msgstr "Přidejte přizpůsobitelný formulář během pokladny (po adrese)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Add a reference price per UoM on products (i.e $/kg), in addition to the "
"sale price"
msgstr ""
"Kromě prodejní ceny, přidejte referenční cenu produktu za MJ (např. Kč/kg)."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a strikethrough price, as a comparison"
msgstr "Přidejte přeškrtnutou cenu jako porovnání"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Add one"
msgstr "Přidat jeden"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#, python-format
msgid "Add to Cart"
msgstr "Přidat do košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
msgid "Add to Cart Button"
msgstr "Přidat tlačítko Košík"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Address"
msgstr "Adresy"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_autocomplete
msgid "Address Autocomplete"
msgstr "Automatické doplňování adresy"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "Všechny produkty"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "Všechny ceníky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow customers to pay in person at your stores"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr "Umožnit zákazníkům porovnávat produkty na základě jejich atributů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow signed-in users to save product in a wishlist"
msgstr "Umožnit přihlášeným uživatelům uložit si produkt do seznamu přání"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "Umožnit koncovému uživateli vybrat tento ceník"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow your customer to add products from previous order in their cart."
msgstr ""
"Umožněte svým zákazníkům přidávat do košíku produkty z předchozí objednávky."

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_alternative_products
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_alternative_products
msgid "Alternative Products"
msgstr "Alternativní produkty"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "Množství opuštěných košíků"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "Anonymous express checkout partner for order %s"
msgstr "Anonymní expresní pokladní partner pro objednávku %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Apartment, suite, etc."
msgstr "Byt, vchod, atd."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Použít"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Are you sure you want to delete this badge?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment"
msgstr "Úkol"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "Přiřazení online objednávek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Attributes"
msgstr "Atributy"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Automatically send abandoned checkout emails"
msgstr "Automaticky odesílejte e-maily o opuštěných košících"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Average Rating"
msgstr "Průměrné hodnocení"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Back to cart"
msgstr "Zpět do košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Background"
msgstr "Pozadí"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Badge"
msgstr "Odznak"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Badge Text"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "Počet základních jednotek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "Název základní jednotky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "Cena základní jednotky"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
msgid "Base Units"
msgstr "Základní jednotky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "Buďte si vědomi co děláte!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__big
msgid "Big"
msgstr "Velký"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing"
msgstr "Fakturační adresa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Billing address"
msgstr "Fakturační adresa"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "Koš"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "Koše"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Zvyšte své prodeje pomocí několika druhů programů: kupóny, akce, dárkové "
"karty, věrnostní programy. Mohou být nastaveny konkrétní podmínky (produkty,"
" zákazníci, minimální částka nákupu, doba). Odměny mohou být slevy (% nebo "
"částka) nebo produkty zdarma."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Both"
msgstr "Zájemce/Příležitost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "Spodek"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "Krabice"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "Krabice"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "Značka"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_contact_us_button_url
msgid "Button URL"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Button url"
msgstr "URL tlačítka"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buttons"
msgstr "Tlačítka"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_buy_now_button
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buy Now"
msgstr "Koupit nyní "

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#, python-format
msgid "Buy now"
msgstr "Koupit nyní "

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "Skříně"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Image 1024 lze přibližovat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__can_publish
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "Může publikovat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cards"
msgstr "Karty"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__carousel
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Carousel"
msgstr "Kolotoč"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cart"
msgstr "Košík"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "Množství košíku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "E-mail pro obnovení košíku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "E-mail na obnovu košíku byl již odeslán"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "Košíky jsou označeny jako opuštěné po této časové prodlevě."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Categories"
msgstr "Kategorie"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"Kategorie se používají k procházení vašich produktů prostřednictvím rozhraní"
" dotykové obrazovky."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Categories:"
msgstr "Kategorie\\:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "Kategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "Popis kategorie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Category:"
msgstr "Kategorie:"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "Židle"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "Židle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Změna DIČ není povolena, pokud byly pro váš účet vydány dokument(y). Prosím "
"kontaktujte nás přímo pro tuto operaci."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Po vydání dokumentu(ů) pro váš účet není změna názvu společnosti povolena. "
"Kontaktujte nás přímo pro tuto operaci."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Změna jména není povolena po vystavení faktur na váš účet. Pro tuto úpravu "
"nás kontaktujte přímo."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Checkout"
msgstr "Pokračovat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Checkout Pages"
msgstr "Stránky košíku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Podřízené kategorie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery
msgid "Choose a delivery method"
msgstr "Zvolit způsob doručení"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "Vánoce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Město"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "Clear Filters"
msgstr "Vymazat filtry"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"První produkt vytvoříte kliknutím na <i>'Nový'</i> v pravém horním rohu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "Kliknout zde"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click here to open the reporting menu"
msgstr "Kliknutím sem otevřete nabídku výkazů"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Save</em> to create the product."
msgstr "Kliknutím <em>Uložit</em> vytvoříte produkt."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "Klikněte na toto tlačítko, aby vás zákazníci mohli vidět."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/cart_notification/cart_notification.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#, python-format
msgid "Close"
msgstr "Zavřít"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapse Category Recursive"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Color"
msgstr "Barva"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Columns"
msgstr "Sloupce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid ""
"Comma-separated list of parts of product names, barcodes or internal "
"reference"
msgstr ""
"Seznam částí názvů produktů, čárových kódů nebo interních referencí oddělený"
" čárkami."

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Company Name"
msgstr " Název společnosti "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__compare_list_price
msgid "Compare to Price"
msgstr "Porovnat k ceně"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_price_comparison
#: model:res.groups,name:website_sale.group_product_price_comparison
msgid "Comparison Price"
msgstr "Srovnávací cena"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "Komponenty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Vypočítejte náklady na dopravu a přepravujte s Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Shiprocket"
msgstr "Výpočet nákladů na dopravu a odeslání pomocí služby Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Spočítej dopravní náklady a odešli DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Spočítej dopravní náklady a odešli přes FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Spočítat dopravní náklady a odeslat přes UPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Spočítej dopravní náklady a odešli přes USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Spočítej dopravní náklady a odešli přes bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Spočítej dopravní náklady na objednávce"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Configure Form"
msgstr "Konfigurace formuláře"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Confirm"
msgstr "Potvrdit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "Confirm Order"
msgstr "Potvrdit objednávku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm order"
msgstr "Potvrzení objednávky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "Potvrzený"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "Potvrzené objednávky"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Contact Us"
msgstr "Kontaktujte nás"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__contact_us_button_url
msgid "Contact Us Button URL"
msgstr "URL tlačítka kontaktujte nás"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Content"
msgstr "Obsah"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Continue checkout"
msgstr "Pokračovat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"Continue checkout\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"
msgstr ""
"Pokračovat\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Continue shopping"
msgstr "Pokračovat v nákupu"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "Lehátka"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Země"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Create"
msgstr "Vytvořit"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "Vytvořit nový produkt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "Datum vytvoření"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "Běžná kategorie nebo vše"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "Vlastní měrná jednotka"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Zákazník"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Zákaznický účet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__account_on_checkout
#: model:ir.model.fields,field_description:website_sale.field_website__account_on_checkout
msgid "Customer Accounts"
msgstr "Zákaznické účty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "Země zákazníka"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "Hodnocení zákazníků"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."
msgstr ""
"Zákazník musí být přihlášený, jinak není jeho emailová adresa známá.     \n"
"\n"
"- Pokud potenciální zákazník vytvoří jeden nebo více opuštěných košíků a následně dokončí nákup dříve, než bude odeslán e-mail pro obnovení, tento e-mail nebude odeslán.     \n"
"\n"
"- Pokud uživatel ručně odeslal e-mail pro obnovení, tento e-mail nebude odeslán podruhé.     \n"
"\n"
"- Pokud při pokusu o dokončení objednávky dojde k chybě zpracování platby, e-mail nebude odeslán.     \n"
"\n"
"- Pokud váš obchod nepodporuje doručení na adresu zákazníka, e-mail nebude odeslán.     \n"
"\n"
"- Pokud nejsou během pokladny k dispozici žádné produkty k zakoupení (např. nejsou skladem), e-mail nebude odeslán.     \n"
"\n"
"- Pokud jsou všechny produkty v košíku zdarma a zákazník nenavštíví stránku dopravy za účelem přidání poplatku za dopravu, nebo je i doprava zdarma, e-mail nebude odeslán."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Customers"
msgstr "Zákazníci"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customize Abandoned Email Template"
msgstr "Přizpůsobte šablonu opuštěných e-mailů"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_config_settings.py:0
#: code:addons/website_sale/models/res_config_settings.py:0
#, python-format
msgid "Customize Email Templates"
msgstr "Přizpůsobte šablony e-mailů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "DHL Express konektor"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "Přepravní možnosti DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr "SEM PŘETÁHNĚTE STAVEBNÍ BLOKY, ABY BYLY DOSTUPNÉ U VŠECH PRODUKTŮ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default"
msgstr "Výchozí"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1/1)"
msgstr "Výchozí (1/1)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "Výchozí měna"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist if any"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default Sort"
msgstr "Výchozí řazení"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr ""
"Definujte vlastní jednotku pro zobrazení v poli ceny za měrnou jednotku."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "Definovat novou kategorii"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete Badge"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery"
msgstr "Doprava"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "množství dodávky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__access_point_address
msgid "Delivery Point Address"
msgstr "Adresa místa doručení"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "Dodávka bude aktualizována po výběru nového způsobu doručení"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "Popis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "Popis zobrazený na eCommerce a na online nabídkách."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Popis pro online nabídky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "Popis k eshopové kartě"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "Stoly"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Určete pořadí zobrazení v e-shopu."

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "Přehled"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__disabled
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__disabled
msgid "Disabled (buy as guest)"
msgstr "Nepovolené (nakoupit jako host)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Discard"
msgstr "Zrušit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Discount code..."
msgstr "Slevový kód..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "Slevy, věrnostní programy a dárkové poukazy"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display Product Prices"
msgstr "Zobrazit ceny produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Display Type"
msgstr "Typ zobrazení"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"Zobrazit základní cenovou jednotku na stránkách eObchodu. Nastavením hodnoty"
" na 0 ji pro tento produkt skryjete."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "Zobrazuje se ve spodní části stránek produktu"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"Zobrazuje celní jednotku pro výrobky, pokud je definována, nebo zvolenou "
"měrnou jednotku v opačném případě."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Není přístup, přeskočit tato data pro uživatelský emailový přehled"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "Do you wish to clear your cart before adding products to it?"
msgstr "Přejete si vymazat košík před přidáním produktů?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Documents"
msgstr "Dokumenty"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_document.py:0
#, python-format
msgid ""
"Documents shown on product page cannot be restricted to a specific variant"
msgstr ""
"Dokumenty zobrazené na produktové stránce nelze omezit na konkrétní variantu"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "Dvojitým kliknutím nastavíte obrázek popisující váš produkt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "Přetáhněte stavební bloky sem a upravte záhlaví pro \""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "Přetáhněte tento blok webstránky a umístěte jej na svou stránku."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "Šuplík"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "Šuplíky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "Extra pole webshopu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "E-shop dodatečné informace, zobrazené na produktové stránce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_form
msgid "E-commerce"
msgstr "E-shop"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "Propagační kód e-shopu"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "Přepravní možnosti Easypost"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Ecommerce: Cart Recovery"
msgstr "eShop: Obnovování košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Edit"
msgstr "Upravit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "Upravte cenu tohoto produktu kliknutím na částku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "Upravit tuto adresu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "Email "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "Embed kód"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_delivery
msgid "Enable Shipping"
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "Zadejte název nového produktu"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "Chyba! Nelze vytvářet rekurzivní kategorie."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Images"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Extra Info"
msgstr "Další informace"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "Media produktu navíc"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Step"
msgstr "Dodatečný krok"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_extra_checkout_step
msgid "Extra Step During Checkout"
msgstr "Další krok během pokladny"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "Obrázky dalších variant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "Varianta produktu navíc"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra info"
msgstr "Další informace"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Featured"
msgstr "Doporučené"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "Způsoby přepravy FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "Pole"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "Pole visačka"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "Název pole"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Fill"
msgstr "Vyplnit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Fill in your address"
msgstr "Vyplňte svou adresu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskální pozice"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Odběratelé"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odběratelé (partneři)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Free"
msgstr "Zdarma"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "Z webstránky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Full name"
msgstr "Celé jméno"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "Nábytek"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "Fakturu vygenerujte automaticky při potvrzení platby online"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Určuje pořadí posloupnosti při zobrazení seznamu kategorií výrobků."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__go_to_cart
#, python-format
msgid "Go to cart"
msgstr "Přejít do košíku "

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__grid
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Grid"
msgstr "Mřížka"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "Seskupit podle"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__none
msgid "Hidden"
msgstr "Skrytý"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale
msgid "Hide 'Add To Cart' when price = 0"
msgstr "Hide 'Přidat do košíku' when price = 0"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Hours."
msgstr "Hodiny."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr ""
"Obrovská velikost souboru. Obrázek by měl být optimalizován / zmenšen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "I agree to the"
msgstr "Souhlasím s"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_tag_form_view_inherit_website_sale
msgid "If an image is set, the color will not be used on eCommerce."
msgstr "Pokud je nastaven obrázek, barva se v eShopu nepoužije."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Pokud zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "If product price equals 0, replace 'Add to Cart' by 'Contact us'."
msgstr ""
"Pokud se cena produktu rovná 0, tlačítko 'Přidat do košíku' bude nahrazeno "
"textem 'Kontaktujte nás'."

#. module: website_sale
#: model:mail.template,description:website_sale.mail_template_sale_cart_recovery
msgid ""
"If the setting is set, sent to authenticated visitors who abandoned their "
"cart"
msgstr ""
"Pokud je toto nastavení zapnuto, odesílá se ověřeným návštěvníkům, kteří "
"opustili své košíky"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"Pokud objednáváte za externí osobu, prosím proveďte objednávku "
"prostřednictvím backendu. Pokud si přejete změnit své jméno nebo e-mailovou "
"adresu, proveďte tak v nastavení účtu nebo kontaktujte svého správce."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "Pokud se domníváte, že se jedná o chybu, obraťte se na správce webu."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_tag__image
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "Obrázek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "Obrázek 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "Obrázek 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "Obrázek 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "Obrázek 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "Název obrázku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Spacing"
msgstr "Rozteč obrázků"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Zoom"
msgstr "Přiblížení obrázku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Size"
msgstr "Velikost obrázků"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Width"
msgstr "Šířka obrázků"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Instant checkout, instead of adding to cart"
msgstr "Okamžitá platba, místo přidání do košíku"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Neplatný e-mail! Prosím zadejte platnou emailovou adresu."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid image"
msgstr "Neplatný obrázek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Fakturace"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "Fakturační zvyklosti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Je odběratel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__is_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "Je publikováno"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "Vydávání faktur zákazníkům"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr ""
"Je zakázáno upravovat prodejní objednávku, která není ve stavu konceptu."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"Zdá se, že způsob doručení není kompatibilní s vaší adresou. Obnovte stránku"
" a zkuste to znovu."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"Zdá se, že pro vaši objednávku již proběhla transakce, způsob doručení již "
"nelze změnit."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#, python-format
msgid "Item(s) added to your cart"
msgstr "Zboží přidané do vašeho košíku"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "Účetní záznam"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "Kpi Celková hodnota prodeje z webových stránek "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "Svítidlo"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "Svítidla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Landscape (4/3)"
msgstr "Na šířku (4/3)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "Minulý měsíc"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "Poslední objednávka online"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "Minulý týden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "Minulý rok"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Layout"
msgstr "Rozvržení"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "Vlevo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "Nechte zákazníka zadat dodací adresu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer select a Mondial Relay shipping point"
msgstr "Nechte zákazníka vybrat výdejní místo Mondial Relay"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "Vytvořme si svůj první produkt."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your eCommerce dashboard to get your eCommerce "
"website ready in no time."
msgstr ""
"Podívejme se na váš e-shopový dashboard, abychom mohli ihned připravit "
"stránku vašeho obchodu."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "Žárovka se prodávána samostatně"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__show_line_subtotals_tax_selection
#: model:ir.model.fields,field_description:website_sale.field_website__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "Zobrazení daně mezisoučty řádků"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Propojená řádka objednávky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "List"
msgstr "Seznam"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Magnifier on hover"
msgstr "Lupa při najetí myší"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Mail only sent to signed in customers with items available for sale in their"
" cart."
msgstr ""
"Mail je zaslán pouze přihlášeným zákazníkům, kteří mají v košíku položky "
"dostupné k prodeji."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Main image"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr ""
"Správa propagačních akcí, kupónů, věrnostních karet, dárkových karet a "
"elektronické peněženky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Manage pricelists to apply specific prices per country, customer, products, "
"etc"
msgstr ""
"Spravujte ceníky pro uplatnění konkrétních cen podle země, zákazníka, "
"produktu atd."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__mandatory
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__mandatory
msgid "Mandatory (no guest checkout)"
msgstr "Povinné (žádná pokladna pro hosty)"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__medium
msgid "Medium"
msgstr "Médium"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zprávy"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Mondial Relay"
msgstr "Mondial Relay"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_delivery_mondialrelay
msgid "Mondial Relay Connector"
msgstr "Mondial Relay konektor"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to first"
msgstr "Přejít na první"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to last"
msgstr "Přejít na poslední"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to next"
msgstr "Přejít na další"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to previous"
msgstr "Přejít na předchozí"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "Multimedia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "My Cart"
msgstr "Můj košík"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Name"
msgstr "Název"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Name (A-Z)"
msgstr "Podle názvu (A-Z)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "Krátký název"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "New"
msgstr "Nové"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_product_action_add
msgid "New Product"
msgstr "Nový produkt"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "Novinka!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Newest Arrivals"
msgstr "Nejnovější"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "Nejnovější produkty"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "Další (šipka vpravo)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "No"
msgstr "Ne"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "Nenalezeny žádné opuštěné košíky"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "No pick-up point available for that shipping address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "Není definován žádný produkt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in this category."
msgstr "V této kategorii není definován žádný produkt."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "Pro tohoto návštěvníka zatím žádné zobrazení produktu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "Žádné výsledky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "Žádné výsledky pro “"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "Nenašly se výsledky pro '"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Pro vaši aktuální objednávku a dodací adresu není k dispozici žádný způsob "
"přepravy. Pro více informací nás prosím kontaktujte."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "No shipping method is selected."
msgstr "Není vybrán žádný způsob dopravy."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__none
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "Žádné"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "Not Published"
msgstr "Není zveřejněno"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/sale_variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "Není k dispozici s %s"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "Počet opuštěných košíků"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "Počet sloupců mřížky v e-shopu"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet zpráv vyžadujících akci"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "Počet produktů v mřížce v e-shopu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_picking
msgid "On Site Payments & Picking"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "Na kolečkách"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "Jakmile kliknete na <b>Uložit</b>, váš produkt je aktualizovaný."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "One product might have different attributes (size, color, ...)"
msgstr "Jeden výrobek může mít různé atributy (velikost, barvu, ...). "

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "Online prodeje"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "Analýza online prodeje"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "Pouze služby"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"Povoleny jsou pouze webové stránky společnosti.\n"
"Pole Společnost nechte prázdné nebo vyberte web od této společnosti."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid "Open Sale Orders"
msgstr "Otevřené prodejní objednávky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "ECommerce s otevřeným zdrojovým kódem"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"Je nutná optimalizace! Zmenšete velikost obrázku nebo zvyšte nastavení "
"komprese."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option for: %s"
msgstr "Možnost pro: %s"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option: %s"
msgstr "Možnost: %s"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__optional
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__optional
msgid "Optional"
msgstr "Volitelný"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Options"
msgstr "Možnosti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "Možnosti spojené"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "Nebo mě naskenujte pomocí vaší bankovní aplikace."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "Datum objednání"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "Řádky objednávek zobrazené na webstránce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Order overview"
msgstr "Shrnutí objednávky"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
msgid "Orders"
msgstr "Objednávky"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "Objednávky k fakturaci"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "Není skladem"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Nadřazená kategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "Nadřazená cesta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Pay now"
msgstr "Zaplaťte nyní"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Payment"
msgstr "Platba"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Payment Information"
msgstr "Informace o platbě"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_methods
msgid "Payment Methods"
msgstr "Platební metody"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_providers
msgid "Payment Providers"
msgstr "Poskytovatelé plateb"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_token
msgid "Payment Token"
msgstr "Platební token"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "Platební tokeny"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "Platební transakce"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Payment is already being processed."
msgstr "Platba se již zpracovává."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "Systém otevírání na pedálu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Telefon"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Telefonní číslo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pills"
msgstr "Pilulky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "Zadejte prosím platnou adresu URL videa."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "Pokračujte prosím v aktuálním košíku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pop-up on Click"
msgstr "Pop-up na kliknutí"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Portrait (4/5)"
msgstr "Portrét (4/5)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Position"
msgstr "Pozice"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_prevent_zero_price_sale
msgid "Prevent Sale of Zero Priced Product"
msgstr "Zabraňte prodeji produktů s nulovou cenou"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "Předchozí (Šipka doleva)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#, python-format
msgid "Price"
msgstr "Cena"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - High to Low"
msgstr "Od nejdražšího"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - Low to High"
msgstr "Od nejlevnějšího"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Price Filter"
msgstr "Filtr ceny"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "Cena za kus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Ceník dostupný pro tento e-shop/web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Ceník"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_pricelist
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Ceníky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Prices displayed on your eCommerce"
msgstr "Ceny zobrazené ve vašem eShopu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Tisk"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "Po přijetí platby zpracujte objednávku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Prod. Desc."
msgstr "Prod. sestupně"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "Produkt"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "Produktové doplňky"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Produktový atribut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid "Product Carousel"
msgstr "Kolotoč produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Produktová kategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Comparison Tool"
msgstr "Nástroj pro porovnání produktů"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_document
msgid "Product Document"
msgstr "Dokument o produktu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "Produktový obrázek"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "Produktové fotografie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "Product Name"
msgstr "Název produktu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Page"
msgstr "Stránka produktu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "Extra pole na stránce produktu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_grid_columns
msgid "Product Page Grid Columns"
msgstr "Sloupce mřížky stránky produktu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_layout
msgid "Product Page Image Layout"
msgstr "Rozložení obrázku na stránce produktu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_spacing
msgid "Product Page Image Spacing"
msgstr "Rozteč obrázků na stránce produktu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_width
msgid "Product Page Image Width"
msgstr "Šířka obrázků na stránce produktů"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_product_pages_list
msgid "Product Pages"
msgstr "Stránky produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "Veřejné kategorie produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Reference Price"
msgstr "Referenční cena produktu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_tag
msgid "Product Tag"
msgstr "Produktový štítek"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_product_tags
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags"
msgstr "Produktové štítky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags Filter"
msgstr "Filtr produktových štítků"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "Šablona produktu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Položka atributu šablony produktu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Hodnota atributu šablony produktu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Produktová šablona"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "Produktová varianta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Variants"
msgstr "Produktové varianty"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "Zobrazení produktu"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "Historie zobrazení produktu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Product not found"
msgstr "Produkt nebyl nalezen"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "Produktový ribbon"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_pages
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "Produkty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products List"
msgstr "Seznam produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products Page"
msgstr "Stránka produktů"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "Produkty nedávno prodány s"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "Produkty nedávno prodány s produktem"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Products Ribbon"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "Zobrazení produktů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Promo Code"
msgstr "Promo kód"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"Zadaná adresa videa pro  „%s“ není platná. Zadejte prosím platnou adresu "
"videa."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_search
msgid "Published"
msgstr "Publikováno"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "Posunout dolu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "Poslat dolů"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "Přesunout na začátek"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "Posunout nahoru"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Quantity"
msgstr "Množství"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Radio"
msgstr "Přepínač"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Rating"
msgstr "Hodnocení"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg_text
msgid "Rating Avg Text"
msgstr "Hodnocení prům. textu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Hodnocení Poslední připomínky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "Hodnocení Posledního obrázku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "Hodnocení Poslední hodnoty"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Hodnocení spokojenosti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_text
msgid "Rating Text"
msgstr "Text hodnocení"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "Počet hodnocení"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Ratings"
msgstr "Hodnocení"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Re-Order"
msgstr "Znovu objednat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Re-order"
msgstr "Znovu objednat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_enabled_portal_reorder_button
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_portal_reorder_button
msgid "Re-order From Portal"
msgstr "Znovu objednat z portálu"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "Nedávno prodané produkty"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "Nedávno zobrazené produkty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "Obnovovací e-mail odeslán"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "Obnovovací e-mail k odeslání"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "Vyztužené pro velké zatížení"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Remove"
msgstr "Odebrat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Remove all"
msgstr "Odebrat všechny"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "Odstranit z košíku"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Remove one"
msgstr "Odebrat jeden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Replace"
msgstr "Nahradit"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_tag__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "Omezit publikování na této webstránce."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Resume Order"
msgstr "Obnovit objednávku"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Return to shipping"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Review Order"
msgstr "Zkontrolovat objednávku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
msgid "Ribbon"
msgstr "Stužka"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "Barva pozadí stužky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "Třída stužky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "Stužka html"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "Barva textu stužky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "Vpravo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimalizováno"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "Prodej"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "Analýza prodeje"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Sales"
msgstr "Prodej"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "Analýza prodeje"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Analýza prodejů"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Položka prodejní objednávky"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Obchodní tým"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Obchodník"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_card_group
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "Vzorek"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Save address"
msgstr "Uložit adresu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "Hledání opuštěných prodejních objednávek"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Search bar"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select"
msgstr "Vybrat"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Vyberte <b>Nový produkt</b> a vytvořte jej a spravujte jeho vlastnosti, "
"abyste zvýšili svůj prodej."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select Quantity"
msgstr "Vyberte množství"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Select a pick-up point"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Volitelné"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Send After"
msgstr ""

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "Odeslat e-mail pro obnovu košíku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "Odeslat e-mail pro obnovení"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send after"
msgstr "Odeslat po"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send by Email"
msgstr "Odeslat e-mailem"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__send_abandoned_cart_email
msgid "Send email to customers who abandoned their cart."
msgstr "Odeslat e-mail zákazníkům, který opustili svůj košík."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "SEO jméno"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Servisní služby"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Share"
msgstr "Sdílet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Ship to the same address"
msgstr "Odeslat na stejnou adresu"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#, python-format
msgid "Shipping"
msgstr "Dodací adresa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr "Doručovací adresa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "Náklady na dopravu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Přepravní metody"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Shipping address"
msgstr "Doručovací adresa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket Shipping Methods"
msgstr "Přepravní metody Shiprocket"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "Obchod"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Obchod - Košík"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Checkout Process"
msgstr "Obchod - Proces pokladny"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Obchod  - Potvrzený"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Products"
msgstr "Obchod - Produkty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "Obchod - Vyberte platební metodu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_default_sort
msgid "Shop Default Sort"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "Nákupní košík"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty"
msgstr "Zobrazit prázdné"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show b2b Fields"
msgstr "Zobrazit pole B2B"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_search
msgid "Show on Ecommerce"
msgstr "Zobrazit na eShopu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_document__shown_on_product_page
msgid "Show on product page"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show/hide shopping cart"
msgstr "Zobrazit/skrýt nákupní košík"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Sign In"
msgstr "Přihlásit se"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "Registrovat se"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Sign in"
msgstr "Přihlásit se"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sign in/up at checkout"
msgstr "Přihlásit/registrovat se u pokladny"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "Sedněte si pohodlně"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "Velikost"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "Velikost X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "Velikost Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "Šikmé"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__small
msgid "Small"
msgstr "Malý"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "Vyprodáno"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "Některá povinná pole jsou prázdná."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "Je nám líto, ale vaši objednávku nemůžeme odeslat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Sort by"
msgstr "Řadit podle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_normal_website_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid "Specify unit"
msgstr "Určete jednotku"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "Stát / provincie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Stav"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__stay
msgid "Stay on Product Page"
msgstr "Zůstat na stránce produktu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street and Number"
msgstr "Ulice a číslo popisné"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect není ve vaší zemi dostupný, použijte prosím jiného "
"poskytovatele plateb."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Style"
msgstr "Styl"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal"
msgstr "Cena bez DPH"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"Navrhněte alternativy k vašemu zákazníkovi (strategie upsell). Tyto produkty"
" se zobrazí na stránce produktu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Suggested Accessories"
msgstr "Navrhované příslušenství"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested accessories"
msgstr "Navrhované příslušenství"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "Navrhované příslušenství v košíku elektronického obchodu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "Štítek"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Tags"
msgstr "Štítky"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_excluded
msgid "Tax Excluded"
msgstr "Bez daně"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_included
msgid "Tax Included"
msgstr "Včetně daně"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tax Indication"
msgstr "Daňové označení"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__amount_delivery
msgid "Tax included or excluded depending on the website configuration."
msgstr ""
"Daň je zahrnuta nebo nezahrnuta v závislosti na konfiguraci webových "
"stránek."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes"
msgstr "Daně"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Text"
msgstr "Text"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale_text
msgid "Text to show instead of price"
msgstr "Text, který se zobrazí místo ceny"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Děkujeme za vaší objednávku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "Číslo 1"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Přístupový token je neplatný."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,help:website_sale.field_product_template__compare_list_price
msgid ""
"The amount will be displayed strikethroughed on the eCommerce product page"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has already been paid. Please refresh the page."
msgstr "Košík je již zaplacen. Obnovte prosím stránku."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "Košík byl aktualizován. Obnovte prosím stránku."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The company of the website you are trying to sale from (%s) is different "
"than the one you want to use (%s)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "Celá adresa URL pro přístup k dokumentu prostřednictvím webstránky."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr "Uvedená kombinace neexistuje, proto ji nelze přidat do košíku."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "Daný produkt neexistuje, proto jej nelze přidat do košíku."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given product does not have a price therefore it cannot be added to "
"cart."
msgstr "Daný produkt nemá cenu, proto jej nelze přidat do košíku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"Zde zvolený režim platí jako fakturační politika pro každý nový vytvořený "
"produkt, ale nikoli pro již existující produkty."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Edit Click on the page and enable 'Categories' to view all eCommerce "
"categories."
msgstr ""
"Produkt bude k dispozici ve všech zmíněných kategoriích eShopu. Přejděte na "
"Obchod > Úprava; klikněte na stránku a povolte 'Kategorie', abyste zobrazili"
" všechny kategorie eShopu."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr "Čas od kdy bude košík označen za opuštěný lze změnit v nastavení."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"Hodnota Základního počtu jednotek musí být větší než 0. Použijte 0, pokud "
"chcete skryt cenu za jednotku pro tento produkt."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "Z webových stránek není potvrzena objednávka"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "Z webových stránek zatím není žádná nezaplacená objednávka"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "Tato kombinace neexistuje."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "Toto je váš aktuální košík."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid ""
"This partner has an open cart. Please note that the pricelist will not be "
"updated on that cart. Also, the cart might not be visible for the customer "
"until you update the pricelist of that cart."
msgstr ""
"Tento partner má otevřený košík. Mějte na paměti, že ceník nebude pro tento "
"košík aktualizován. Košík nemusí být pro zákazníka viditelný, dokud "
"neaktualizujete ceník."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/000.js:0
#, python-format
msgid "This product does not exist therefore it cannot be added to cart."
msgstr "Tento produkt neexistuje, proto jej nelze přidat do košíku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "Tento produkt nemá platnou kombinaci."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "Tento produkt již není k dispozici."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "This product is not available for purchase."
msgstr "Tento produkt již nelze koupit."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "Tento produkt je nepublikovaný. "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "Tento promo kód není k dispozici."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails"
msgstr "Miniatury"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Chcete-li posílat pozvánky v režimu B2B, otevřete kontakt nebo vyberte "
"několik v zobrazení seznamu a klikněte na možnost Správa portálového "
"přístupu v rozevírací nabídce *Akce*."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top"
msgstr "Nahoru"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top Bar"
msgstr "Horní lišta"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Total"
msgstr "Celkem"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "Celkový počet zobrazených produktů"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "Celkový počet zobrazení produktů"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"Platí pouze pro produktové filtry, které vyžadují product_id, protože jsou "
"spojeny s křížovým prodejem."

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "URL videa pro představení vašeho produktu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "Přepravní možnosti USPS"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "Měrná jednotka pro cenu za jednotku na e-shopových produktech."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "Nezaplacené"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
msgid "Unpaid Orders"
msgstr "Nezaplacené objednávky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "Nepublikovaný"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "Nahrát soubor z místní knihovny."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Use Google Places API to validate addresses entered by your visitors"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "VAT"
msgstr "DIČ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Excluded"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Included"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Variant"
msgstr "Varianta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__ribbon_id
msgid "Variant Ribbon"
msgstr "Stužka varianty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Variants"
msgstr "Varianty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Vertical (2/3)"
msgstr "Vertikální (2/3)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "Video URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "Zobrazit produkt"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml:0
#, python-format
msgid "View cart"
msgstr "Zobrazit košík"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "View product"
msgstr "Zobrazit produkt"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Viewer"
msgstr "Prohlížeč"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "Viditelnost"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "Viditelné"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "Viditelné na aktuální webstránce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_tag__visible_on_ecommerce
msgid "Visible on eCommerce"
msgstr "Viditelné na e-Shopu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "Navštívené stránky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "Navštívené produkty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "Zobrazení produktu návštěvníkovi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "Historie zobrazení produktu návštěvníka"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#: model:ir.model.fields,field_description:website_sale.field_sale_order__shop_warning
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__shop_warning
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
#, python-format
msgid "Warning"
msgstr "Varování"

#. module: website_sale
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Záruka"

#. module: website_sale
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"Záruka vydaná odběrateli předmětu výrobcem, slibující jej opravit nebo "
"nahradit, je-li to nutné, ve stanovené lhůtě."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_tag__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "Webová stránka"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "Webové zprávy"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "Webové produktové kategorie"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Veřejné kategorie webstránky"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "Pořadí webových stránek"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Webový obchod"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtr webových úryvků"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "URL webové stránky"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "Návštěvník webu"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_message_ids
msgid "Website communication history"
msgstr "Webová historie komunikace"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "Meta-popis webových stránek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta-klíčová slova webových stránek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "Meta-název webových stránek"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "Opengraph obrázek webových stránek"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created for eCommerce orders."
msgstr ""
"Webová stránka, ze které tato faktura byla vytvořená pro objednávky eShopu."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed for eCommerce orders."
msgstr ""
"Webová stránka, ze které tato objednávka pochází, pro objednávky eShopu."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "Webové stránky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "What should be done on \"Add to Cart\"?"
msgstr "Co by mělo dělat tlačítko \"Přidat do košíku\"?"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_tag__visible_on_ecommerce
msgid "Whether the tag is displayed on the eCommerce."
msgstr "Zda je štítek zobrazen w eShopu."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "Bílá tabule"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "Seznamy přání"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"V prvním režimu můžete nastavit několik cen ve formuláři konfigurace "
"produktu (z karty Prodej). S druhou nastavíte ceny a výpočetní pravidla z "
"Ceníků."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "Se třemi nohami"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Yes"
msgstr "Ano"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"Upravujete současně svou <b>fakturační a dodací</b> adresu!<br/>\n"
"Chcete-li upravit dodací adresu, vytvořte"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "You can't use a video as the product's main image."
msgstr "Jako hlavní obrázek produktu nelze použít video."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "Nemáte žádnou objednávku z webových stránek"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "Z webstránky nemáte žádný příkaz k fakturaci"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "Nechal jste položky v košíku!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"Najdete zde všechny košíky, které vaši návštěvníci opustili.\n"
"Pokud vyplnili svou adresu, měli byste jim poslat e-mail pro obnovení."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.alternative_products
msgid ""
"Your Dynamic Snippet will be displayed here...\n"
"                                This message is displayed because youy did not provide both a filter and a template to use."
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Váš e-mail"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Vaše jméno"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Your cart is empty!"
msgstr "Váš košík je prázdný!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Your cart is not ready to be paid, please verify previous steps."
msgstr "Váš košík není připraven k platbě. Ověřte prosím předchozí kroky."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr "Vaše platba byla autorizována"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "Váš předchozí košík již byl dokončen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "PSČ"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "PSČ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "Přepravní možnosti bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "e.g. Cheese Burger"
msgstr "např. Cheese Burger"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "např. lampa, koš"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "E-shop"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "E-shop produktové kategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__description_ecommerce
#: model:ir.model.fields,field_description:website_sale.field_product_template__description_ecommerce
msgid "eCommerce Description"
msgstr "Popis eShopu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "Viditelnost filtru eShopu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "prodeje e-shopu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "Obchod eShopu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "eCommerce cart"
msgstr "Košík eShopu"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_cron_send_availability_email_ir_actions_server
msgid "eCommerce: send email to customers about their abandoned cart"
msgstr "eShop: odešlete zákazníkům e-mail o jejích opuštěných košících"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "Pokud chcete sloučit svůj předchozí košík do aktuálního košíku."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"Pokud chcete obnovit předchozí košík. Váš současný košík bude nahrazen vaším"
" předchozím košíkem."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "v kategorii \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "nová adresa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "or"
msgstr "nebo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "remove"
msgstr "odstranit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "select to see available Pick-Up Locations"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "terms &amp; conditions"
msgstr "zásady a podmínky"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "pro sledování vaší objednávky."
