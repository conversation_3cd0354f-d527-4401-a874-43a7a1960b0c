<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="ir_rule_sms_template_sale_manager" model="ir.rule">
        <field name="name">SMS Template: sale manager CUD on opportunity / partner templates</field>
        <field name="model_id" ref="sms.model_sms_template"/>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
        <field name="domain_force">[('model_id.model', 'in', ('crm.lead', 'res.partner'))]</field>
        <field name="perm_read" eval="False"/>
    </record>
</odoo>
