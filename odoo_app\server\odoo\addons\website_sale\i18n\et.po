# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <PERSON><PERSON><PERSON> Vijar, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> Õigus <<EMAIL>>, 2023
# JanaAvalah, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# Rivo <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> avalah, 2023
# Leaanika Randmets, 2023
# <PERSON>-<PERSON>, 2024
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Stevin Lilla, 2025\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" kategooria."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"\"Optional\" allows guests to register from the order confirmation email to "
"track their order."
msgstr ""
"\"Valikuline\" annab võimaluse külalistel registreeruda kinnitusmeili kaudu,"
" et oma tellimust jälgida."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s ülevaade"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s ülevaade"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "&amp; Shipping"
msgstr "&amp; tarne"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "&amp;nbsp;(<i>Your shipping address will be requested later)</i>"
msgstr "&nbsp;(<i>Sinu tarneaadressit küsitakse hiljem)</i>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"
msgstr "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. Näidatakse tulemusi päringule '"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__100_pc
msgid "100 %"
msgstr "100 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "100 percent"
msgstr "100 protsenti"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__50_pc
msgid "50 %"
msgstr "50 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "50 percent"
msgstr "50 protsenti"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__66_pc
msgid "66 %"
msgstr "66 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "66 percent"
msgstr "66 protsenti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<b class=\"w-100\">Order summary</b>"
msgstr "<b class=\"w-100\">Tellimuse kokkuvõte</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
msgid "<b>Categories</b>"
msgstr "<b>Kategooria</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Kommunikatsioon: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
msgid "<b>Price Range</b>"
msgstr "<b>Hinnavahemik</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Pricelist</b>"
msgstr "<b>Hinnakiri</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<b>Shipping: </b>"
msgstr "<b>Tarne: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Sort By</b>"
msgstr "<b>Järjesta</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_tags
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Tags</b>"
msgstr "<b>Sildid</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                30 päeva raha tagasi garanteeritud<br/>\n"
"                Tarne: 2-3 tööpäeva"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Return to shipping"
msgstr ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                           Naase transpordi juurde"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Buy now"
msgstr ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Osta kohe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart
msgid "<i class=\"fa fa-cart-plus me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-cart-plus me-2\"/>Lisa ostukorvi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Buy Now"
msgstr ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Osta kohe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Muuda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.row_addresses
msgid ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-inline\">Add "
"address</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print me-2\"/>Print"
msgstr "<i class=\"fa fa-print me-2\"/>Prindi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_re_order_btn
msgid ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Order Again"
msgstr ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Telli uuesti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Add to cart"
msgstr ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Lisa ostukorvi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<i class=\"fw-light fa fa-angle-left me-2\"/>Discard"
msgstr "<i class=\"fw-light fa fa-angle-left me-2\"/>Tühista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<option value=\"\" selected=\"true\">-</option>"
msgstr "<option value=\"\" selected=\"true\">-</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Riik...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">Maakond</option>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Siit leiate kõik hüljatud ostukorvid, nt ostukorvid, mis teie veebikülastajad on loonud rohkem kui tund aega tagasi, aga pole neid veel kinnitanud.</p>\n"
"                        <p>Te võiksite oma klientidele innustamiseks e-kirja saata!</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<small class=\"d-none d-lg-inline text-muted\">Sort By:</small>"
msgstr "<small class=\"d-none d-lg-inline text-muted\">Järjesta:</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">Ettevõtte nime või "
"käibemaksukohustuslase numbri muutmine ei ole lubatud, kui teie konto jaoks "
"on dokument välja antud. Selle toimingu tegemiseks võtke meiega otse "
"ühendust.</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid ""
"<small class=\"mx-auto\"><b>Clear Filters</b></small>\n"
"                        <i class=\"oi oi-close\"/>"
msgstr ""
"<small class=\"mx-auto\"><b>Kustuta filtrid</b></small>\n"
"                        <i class=\"oi oi-close\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Add to cart</span>"
msgstr ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"<span class=\"d-none d-md-inline\">Lisa ostukorvi</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "<span class=\"mx-2 o_wsale_ppr_by\">by</span>"
msgstr "<span class=\"mx-2 o_wsale_ppr_by\">poolt</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_order_location\">\n"
"                    <b class=\"o_order_location_name\"/>\n"
"                    <br/>\n"
"                    <i class=\"o_order_location_address\"/>\n"
"                </span>\n"
"                <span class=\"fa fa-times ms-2 o_remove_order_location\" aria-label=\"Remove this location\" title=\"Remove this location\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">Select to compute delivery rate</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Next\" title=\"Next\"/>"
msgstr ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Next\" title=\"Next\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "<span class=\"px-3\">or</span>"
msgstr "<span class=\"px-3\">või</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr "<span class=\"s_website_form_label_content\">Tagasiside vorm</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">Laadi üles dokument </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">Teema</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Based"
" on variants</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= "
"1\">Põhineb variatsioonidel</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "<span class=\"visually-hidden\">filters active</span>"
msgstr "<span class=\"visually-hidden\">aktiivsed filtrid</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>Tellimus</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_kanban
msgid "<span>Show on product page</span>"
msgstr "<span>Näita tootekaardil</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>Kahjuks ei saa Teie tellimust kinnitada, kuna maksesumma ei ühti ostukorvi summaga.\n"
"                         Lisateabe saamiseks võtke ühendust poega.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>Video eelvaade</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong><i class=\"oi oi-arrow-right\"/> View alternatives</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong>No suitable payment option could be found.</strong><br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Kokku:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total</strong>"
msgstr "<strong>Kokku</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<strong>Warning!</strong>"
msgstr "<strong>Hoiatus!</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "<u>Terms and Conditions</u>"
msgstr "<u>Tingimused</u>"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Toote kirjeldus, mida soovite oma klientidele edastada. See kirjeldus "
"kopeeritakse igale müügitellimusele, saatelehele ja "
"kliendiarvele/krediidiarvele"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"A detailed, formatted description to promote your product on this page. Use "
"'/' to discover more features."
msgstr ""
"Detailne turundus kirjeldus valitud toote jaoks veebilehel. Kasutage '/' et "
"avastada veel võimalusi."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"Toode võib olla kas füüsiline toode või teenus, mida te enda klientidele "
"müüte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "Lühikirjeldus, mis hiljem kuvatakse ka dokumentidel."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "Hüljatud"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
#: model:ir.model.fields,field_description:website_sale.field_sale_report__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "Hüljatud ostukorv"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "Hüljatud ostukorvid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "Hüljatud ostukorvid, mida taastada"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "Hülgamise viivitus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__send_abandoned_cart_email
msgid "Abandoned Email"
msgstr "Mahajäetud ostukorvi kliendi e-post"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "Lugege rohkem ristmüügi kohta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Accept Terms & Conditions"
msgstr "Nõustuge tingimustega"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "Sobiv failisuurus"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "Toote tarvikud"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"Lisavalikud kuvatakse siis, kui klient vaatab enne maksmist oma ostukorvi "
"üle (ristmüügi strateegia)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "Lisavaliku tooted"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Action"
msgstr "Tegevus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add"
msgstr "Lisa"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Add To Cart"
msgstr "Lisa ostukorvi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__add_to_cart_action
#: model:ir.model.fields,field_description:website_sale.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Lisa ostukorvi nupu toiming"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "Lisa meedia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a customizable form during checkout (after address)"
msgstr ""
"Ostukorvi lehele võib tekitada lisasammu. (Peale aadressi lisamist) "
"Lisasammu võib kohandada veebilehe redaktoris. (nt. tagasisidevorm jne)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Add a reference price per UoM on products (i.e $/kg), in addition to the "
"sale price"
msgstr ""
"Lisa toodetele lisaks müügihinnale ka võrdlushind UoM kohta (st €/kg)."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a strikethrough price, as a comparison"
msgstr "Lisage võrdluseks läbikriipsutatud hind"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Add one"
msgstr "Lisage üks"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#, python-format
msgid "Add to Cart"
msgstr "Lisa ostukorvi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
msgid "Add to Cart Button"
msgstr "Lisa ostukorvi nupp"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Address"
msgstr "Aadress"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_autocomplete
msgid "Address Autocomplete"
msgstr "Aadressi automaatne täitmine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "Kõik tooted"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "Kõik hinnakirjad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow customers to pay in person at your stores"
msgstr "Luba klientidel maksta kauba kättesaamisel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr "Luba ostjatel tooteid võrrelda nende atribuutide põhjal. "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow signed-in users to save product in a wishlist"
msgstr "Luba sisselogitud kasutajatel toote soovinimekirja salvestada"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "Võimaldage lõppkasutajal seda hinnakirja valida"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow your customer to add products from previous order in their cart."
msgstr ""
"Kliendil on võimalik lisada ostukorvi eelmisel korral tellitud tooteid. "

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_alternative_products
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_alternative_products
msgid "Alternative Products"
msgstr "Alternatiivsed tooted"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "Hüljatud ostukorvide arv"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "Anonymous express checkout partner for order %s"
msgstr "Anonymous express checkout partner for order %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Apartment, suite, etc."
msgstr "Korterinumber jne."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Kinnita"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Are you sure you want to delete this badge?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment"
msgstr "Määramine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "Veebitellimuste määramine"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Attributes"
msgstr "Atribuudid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Automatically send abandoned checkout emails"
msgstr "Automaatselt saada hüljatud ostukorvi e-kirju"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Average Rating"
msgstr "Keskmine hinne"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Back to cart"
msgstr "Tagasi ostukorvi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Background"
msgstr "Taust"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Badge"
msgstr "Märk"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Badge Text"
msgstr "Teksti märk"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "Põhiühikute arv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "Põhiühiku nimetus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "Baasühiku hind"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
msgid "Base Units"
msgstr "Põhiühikud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "Pane tähele!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__big
msgid "Big"
msgstr "Suur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing"
msgstr "Arveldus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Billing address"
msgstr "Arveldusaadress"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "Kast"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "Prügikastid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Suurendage oma müüki mitmesuguste programmide abil: kupongid, kampaaniad, "
"kinkekaardid, lojaalsus. Võimalus seadistada konkreetsed tingimused (tooted,"
" kliendid, minimaalne ostusumma, periood). Preemiaks võivad olla "
"allahindlused (% või summa) või tasuta tooted."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Both"
msgstr "Mõlemad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "Alumine osa"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "Karp"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "Kastid"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "Kaubamärk"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_contact_us_button_url
msgid "Button URL"
msgstr "Nupu URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Button url"
msgstr "Nupu URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buttons"
msgstr "Nupud"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_buy_now_button
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buy Now"
msgstr "Osta kohe"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#, python-format
msgid "Buy now"
msgstr "Osta kohe"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "Kapid"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Kas pilti 1024 saab sisse suumida"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__can_publish
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "Võib avaldada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cards"
msgstr "Kaardid"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__carousel
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Carousel"
msgstr "Karussell"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cart"
msgstr "Kaart"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "Ostukorvi kogus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "Ostukorvi taastamise e-kiri"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "Ostukorvi taastamise e-kiri on juba saadetud."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "Korvid märgitakse peale seda viivitust hüljatuks."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Categories"
msgstr "Kategooriad"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"Kategooriaid kasutatakse teie toodete otsimisel \n"
"puuteekraani kaudu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Categories:"
msgstr "Kategooriad:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "Kategooria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "Kategooria kirjeldus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Category:"
msgstr "Kategooria:"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "Koosoleku juhataja"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "Toolid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"KMKR numbri muutmine ei ole lubatud, kui teie kontole on juba esitatud "
"dokument(e). Palun võtke meiega otse ühendust, kui seda on vaja."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Ettevõte nime muutmine ei ole lubatud, kui teie kontole on juba dokument "
"(dokumendid) väljastatud. Selle tegevuse sooritamiseks võtke palun meiega "
"otse ühendust."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Nime muutmine ei ole lubatud, kui teie kontole on juba esitatud arveid. "
"Palun võtke meiega otse ühendust, kui seda on vaja."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Checkout"
msgstr "Väljaregistreerimine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Checkout Pages"
msgstr "Ostukorvi lehed"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Alamkategooriad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery
msgid "Choose a delivery method"
msgstr "Vali tarneviis"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "Jõulud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Linn"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "Clear Filters"
msgstr "Eemalda filtrid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"Vajuta <i>\"Uus\"</i>nupule ülevalt paremast nurgast, et oma esimene toode "
"luua."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "Kliki siin"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click here to open the reporting menu"
msgstr "Aruandlusmenüü avamiseks klõpsake siin"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Save</em> to create the product."
msgstr "Vajutage <em>Salvesta</em> , et luua toode."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "Vajutage sellele nuppu, et see klientidele nähtavaks muuta."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/cart_notification/cart_notification.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#, python-format
msgid "Close"
msgstr "Sulge"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapse Category Recursive"
msgstr "Collapse Category Recursive"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Color"
msgstr "Värv"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Columns"
msgstr "Veerud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid ""
"Comma-separated list of parts of product names, barcodes or internal "
"reference"
msgstr "Komadega eraldatud tootenimetused, vöötkoodid või sisemised viited"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Company Name"
msgstr "Ettevõtte nimi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__compare_list_price
msgid "Compare to Price"
msgstr "Võrrelge hinnaga"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_price_comparison
#: model:res.groups,name:website_sale.group_product_price_comparison
msgid "Comparison Price"
msgstr "Võrdlushind"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "Koostisosad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Reaalse transpordihinna arvutamine ja saatmine Easypost'iga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Shiprocket"
msgstr "Arvuta saatmiskulu ja saada Shiprocketiga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Reaalse transpordihinna arvutamine ja saatmine DHL'ga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Reaalse transpordihinna arvutamine ja saatmine FedEx'iga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Reaalse transpordihinna arvutamine ja saatmine UPS'iga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Reaalse transpordihinna arvutamine ja saatmine USPS'iga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Reaalse transpordihinna arvutamine ja saatmine bpost'iga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Arvuta transpordi hind tellimustel"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Configure Form"
msgstr "Seadistage vorm"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Confirm"
msgstr "Kinnitage"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "Confirm Order"
msgstr "Kinnita tellimus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm order"
msgstr "Kinnita tellimus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "Kinnitatud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "Kinnitatud tellimused"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Contact Us"
msgstr "Võta meiega ühendust"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__contact_us_button_url
msgid "Contact Us Button URL"
msgstr "\"Võtke meiega ühendust\" nupu URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Content"
msgstr "Sisu"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Continue checkout"
msgstr "Jätka ostu eest maksmisega"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"Continue checkout\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"
msgstr ""
"Jätka ostuprotsessi\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Continue shopping"
msgstr "Jätka ostlemist"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "Diivanid"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Riik"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Create"
msgstr "Loo"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "Loo uus toode"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
msgid "Created by"
msgstr "Loodud (kelle poolt?)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
msgid "Created on"
msgstr "Loodud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "Loomise kuupäev"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "Praegune kategooria või kõik"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "Kohandatud mõõtühik"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Klient"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Kliendi konto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__account_on_checkout
#: model:ir.model.fields,field_description:website_sale.field_website__account_on_checkout
msgid "Customer Accounts"
msgstr "Kliendikontod"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "Kliendi riik"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "Klientide hinnangud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."
msgstr ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Customers"
msgstr "Kliendid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customize Abandoned Email Template"
msgstr "Kohandage mahajäetud ostukorvi meilimallid"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_config_settings.py:0
#: code:addons/website_sale/models/res_config_settings.py:0
#, python-format
msgid "Customize Email Templates"
msgstr "Kohandage meilimalle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "DHL Expressi ühendus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "DHL tarneviis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr ""
"LOHISTA KLOTSID SIIA, ET NEID OLEKS VÕIMALIK KASUTADA KÕIGI TOODETE JAOKS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default"
msgstr "Vaikimisi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1/1)"
msgstr "Vaikimisi (1/1)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "Vaikimisi valuuta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist if any"
msgstr "Vaikimisi hinnakiri, kui on"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default Sort"
msgstr "Vaikimisi sorteerimine"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr "Määrake kohandatud ühik, mida kuvada mõõtühiku hinna väljal."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "Määratlege uus kategooria"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete Badge"
msgstr "Eemalda märk"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery"
msgstr "Tarne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "Transpordi kogus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__access_point_address
msgid "Delivery Point Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "Tarnet uuendatakse pärast uue tarneviisi valimist."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "Kirjeldus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "Anrud kirjeldus kuvatakse e-poe müügipakkumistel. "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Veebipakkumiste kirjeldus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "Veebisaidi kirjeldus"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "Lauad"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Määra kuvamise järjekord Veebis"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "Kokkuvõte"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__disabled
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__disabled
msgid "Disabled (buy as guest)"
msgstr "Keelatud (külalistel osta)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Discard"
msgstr "Loobu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Discount code..."
msgstr "Sooduskood..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "Allahindlused, lojaalsusprogramm ja kinkekaardid"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display Product Prices"
msgstr "Näita toote hinnad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Display Type"
msgstr "Kuvamise tüüp"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"Kuvage oma e-kaubanduse lehtedel ühiku põhihinda. Selle toote peitmiseks "
"määrake väärtusele 0."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "Kuvage tootelehtede alumises osas"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"Kui see on määratud, kuvab toodete kohandatud ühikut või muul viisil valitud"
" mõõtühikut."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Puudub ligipääs. Jäta need andmed kasutaja kokkuvõtte kirjast välja"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "Do you wish to clear your cart before adding products to it?"
msgstr "Kas soovite oma ostukorvi enne toodete lisamist tühjendada?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Documents"
msgstr "Dokumendid"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_document.py:0
#, python-format
msgid ""
"Documents shown on product page cannot be restricted to a specific variant"
msgstr "Toote lehel näidatud dokumente ei saa piirata kindlale variatsioonile"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "Tee siia topeltklõps, et valida toote kirjeldamiseks pilt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "Lohistage siia ehitusblokid, et kohandada päist"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "Lohistage see veebilehe blokk ja kukutage see enda lehele."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "Sahtel"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "Sahtlid"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "E-commerce lisaväljad"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "E-Commerce Extra tootelehel kuvatav lisainfo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_form
msgid "E-commerce"
msgstr "E-kaubandus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "E-commerce promokood"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "EasyPost tarneviis"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Ecommerce: Cart Recovery"
msgstr "E-pood: ostukorvi taastamine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Edit"
msgstr "Muuda"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "Muutke selle toote hinda summale vajutades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "Muuda seda aadressi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "E-post"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "Põimise kood"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_delivery
msgid "Enable Shipping"
msgstr "Luba kohaletoimetamine"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "Sisestage oma uuele tootele nimi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "Viga! Ei saa luua rekursiivseid kategooriaid."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Images"
msgstr "Lisapildid"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Extra Info"
msgstr "Lisainfo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "Täiendav meedia tootele"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Step"
msgstr "Lisasamm"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_extra_checkout_step
msgid "Extra Step During Checkout"
msgstr "Lisasamm ostukorvi lehel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "Extra Variant Images"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "Extra Variant Media"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra info"
msgstr "Lisainfo"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Featured"
msgstr "Esiletõstetud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "FedEx tarneviis"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "Väli"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "Välja silt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "Välja nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Fill"
msgstr "Täida"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Fill in your address"
msgstr "Täida enda aadress"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__fiscal_position_id
msgid "Fiscal Position"
msgstr "Finantspositsioon"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad(Partnerid)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Free"
msgstr "Tasuta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "Veebilehelt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Full name"
msgstr "Täisnimi"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "Mööblid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "Genereeri arve automaatselt peale veebimakse kinnitamist"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Annab tootekategooriate nimekirja kuvamisel järjestuse järjekorra."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__go_to_cart
#, python-format
msgid "Go to cart"
msgstr "Mine ostukorvi"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__grid
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Grid"
msgstr "Võrgustik"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "Rühmitamine"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__none
msgid "Hidden"
msgstr "Varjatud"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale
msgid "Hide 'Add To Cart' when price = 0"
msgstr "Peida \"Lisa ostukorvi\", kui hind = 0"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Hours."
msgstr "tundi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "Suur failisuurus. Seda pilti peaks optimiseerima/vähendama."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "I agree to the"
msgstr "Ma nõustun"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_tag_form_view_inherit_website_sale
msgid "If an image is set, the color will not be used on eCommerce."
msgstr "Kui pilt on määratud, siis värvi e-poes ei kasutata."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Kui kontrollitud, siis uued sõnumid nõuavad Teie tähelepanu."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningate sõnumitel saatmiserror."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "If product price equals 0, replace 'Add to Cart' by 'Contact us'."
msgstr ""
"Kui toote hind on 0, asenda \"Lisa ostukorvi\" tekstiga \"Võtke meiega "
"ühendust\"."

#. module: website_sale
#: model:mail.template,description:website_sale.mail_template_sale_cart_recovery
msgid ""
"If the setting is set, sent to authenticated visitors who abandoned their "
"cart"
msgstr ""
"Kui seadistus on tehtud, siis saadetakse autentitud klientidele kiri "
"mahajäetud ostukorvi teemal."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"Kui tellid välisele inimesele, siis palun esita tellimus backend süsteemist."
" Kui soovid muuta oma nime või emaili aadressi, siis palun muuda kasutaja "
"seadistusi või võta ühendust oma administraatoriga."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""
"Kui arvate, et tegemist on veaga, võtke ühendust veebilehe "
"administraatoriga."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_tag__image
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "Pilt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "Pilt 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "Pilt 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "Pilt 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "Pilt 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "Pildi nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Spacing"
msgstr "Image Spacing"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Zoom"
msgstr "Pildi Zoom"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Size"
msgstr "Pildi suurus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Width"
msgstr "Pildi laius"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Instant checkout, instead of adding to cart"
msgstr "Ostukorvi lisamise asemel suunatakse otse kassasse"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "E-posti aadress ei ole korrektne! Palun kirjuta korrektne e-post."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid image"
msgstr "Vigane pilt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Raamatupidamine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "Arvelduse meetod"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "On jälgija"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__is_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "On avaldatud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "Määra klientidele arveid"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr "Müügitellimusi, mis ei ole mustandstaatused, ei ole lubatud muuta."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"Tundub, et tarneviis ei ühildu teie aadressiga. Värskendage lehte ja "
"proovige uuesti."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"Näib, et Teie tellimusega on juba tehing tehtud, tarneviisi ei saa enam "
"muuta."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#, python-format
msgid "Item(s) added to your cart"
msgstr "Esemed lisatud ostukorvi"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "Kpl veebilehe müügi koguväärtus"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "Lamp"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "Lambid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Landscape (4/3)"
msgstr "Landscape (4/3)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "Eelmine kuu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "Viimane online müügitellimus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "Eelmine nädal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "Eelmine aasta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Layout"
msgstr "Paigutus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "Jääk"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "Võimalda klientidel sisestada tarneaadress"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer select a Mondial Relay shipping point"
msgstr "Mondial Relay tarnepunkti valikuvõimalus"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "Loome sinu esimese toote."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your eCommerce dashboard to get your eCommerce "
"website ready in no time."
msgstr ""
"Vaatame teie ekaubanduse töölauda, et saada ekaubanduse veebileht kiiresti "
"valmis."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "Lambipirni müüakse eraldi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__show_line_subtotals_tax_selection
#: model:ir.model.fields,field_description:website_sale.field_website__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "Vahesumma rida maksude kuvamiseks"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Seotud tellimuse rida"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "List"
msgstr "Loend"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Magnifier on hover"
msgstr "Suurenda hõljumisel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Mail only sent to signed in customers with items available for sale in their"
" cart."
msgstr ""
"E-kirjad saadetakse ainult sisselogitud kasutajatele, kelle ostukorvis "
"olevad tooted on saadaval (müügis)."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Main image"
msgstr "Peamine pilt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr ""
"Halda pakkumisi, kuponge, lojaalsuskaarte, kinkekaarte ja e-rahakotte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Manage pricelists to apply specific prices per country, customer, products, "
"etc"
msgstr ""
"Halda hinnakirju, et rakendada konkreetseid hindu riigi, kliendi, toote jne "
"kohta"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__mandatory
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__mandatory
msgid "Mandatory (no guest checkout)"
msgstr "Kohustuslik (külalistel puudub ostukorv)"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__medium
msgid "Medium"
msgstr "Levitamise vahend"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi saatmise veateade"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_ids
msgid "Messages"
msgstr "Sõnum"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Mondial Relay"
msgstr "Mondial Relay"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_delivery_mondialrelay
msgid "Mondial Relay Connector"
msgstr "Mondial Relay ühendus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to first"
msgstr "Liiguta esimeseks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to last"
msgstr "Liiguta viimaseks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to next"
msgstr "Liiguta järgmiseks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to previous"
msgstr "Liiguta eelmiseks"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "Multimedia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "My Cart"
msgstr "Minu ostukorv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Name"
msgstr "Nimi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Name (A-Z)"
msgstr "Nimi (A-Z)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "Nimi lühidalt"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "New"
msgstr "Uus"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_product_action_add
msgid "New Product"
msgstr "Uus toode"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "Uus!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Newest Arrivals"
msgstr "Just saabunud"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "Uued tooted"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "Järgmine (paremnool)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "No"
msgstr "Ei"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "Hüljatud ostukorve ei leitud"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "No pick-up point available for that shipping address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "Toodet pole määratud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in this category."
msgstr " Selles kategoorias pole ühtegi toodet."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "Sellel külastajal pole veel tootearvustusi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "Tulemused puuduvad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "Tulemused puuduvad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "Päringule ei leitud tulemusi '"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Teie praeguse tellimuse ja tarneaadressi jaoks pole tarnemeetodit saadaval. "
"Lisateabe saamiseks võtke meiega ühendust."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "No shipping method is selected."
msgstr "Tarveviisi pole valitud."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__none
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "Pole"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "Not Published"
msgstr "Avaldamata"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/sale_variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "Pole saadaval koos %s"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "Hüljatud ostukorvide arv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Tegevuste arv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Vigade arv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "Veergude ruudustiku arv poes"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Tegevust nõudvate sõnumite arv"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Veateatega sõnumite arv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "Toodete arv ruudustikus poes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_picking
msgid "On Site Payments & Picking"
msgstr "Maksmine kättesaamisel"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "ratastel"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "Kui te olete vajutanud <b>Salvesta</b>, on teie toode uuendatud."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "One product might have different attributes (size, color, ...)"
msgstr "Ühel tootel võib olla palju erinevaid atribuute (suurus, värv, ...)"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "Veebimüügid"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "Veebimüügi analüüs"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "Ainult teenused"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"Ainult ettevõtte veebilehed on lubatud.\n"
"Jätke ettevõte väli tühjaks või valige selle ette veebileht."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid "Open Sale Orders"
msgstr "Avatud müügitellimused"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "Open Source eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"Optimiseerimine on vajalik! Vähendage pildi suurust või suurendage oma "
"pakkimise seadeid."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option for: %s"
msgstr "Valikule: %s"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option: %s"
msgstr "Valik:%s"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__optional
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__optional
msgid "Optional"
msgstr "Valikuline"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Options"
msgstr "Valikud"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "Valikud seotud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "Või skaneerige mind oma pangandusäpiga."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "Tellimuse kuupäev"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "Tellimused lingitud Veebis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Order overview"
msgstr "Tellimuse ülevaade"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
msgid "Orders"
msgstr "Tellimused"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "Tellimused arveks teha"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "Läbimüüdud"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Ülemkategooria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "Põhiliin"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "Parents And Self"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Pay now"
msgstr "Maksa kohe"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Payment"
msgstr "Makse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Payment Information"
msgstr "Makse info"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_methods
msgid "Payment Methods"
msgstr "Maksmise meetodid"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_providers
msgid "Payment Providers"
msgstr "Makseteenuse pakkujad"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_token
msgid "Payment Token"
msgstr "Sümboolne makse"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "Maksejärgud"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "Maksetehingud"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Payment is already being processed."
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Telefon"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Telefoninumber"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pills"
msgstr "Tabletid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "Palun sisestage kehtiv Video URL."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "Palun jätkale oma praeguse ostukorviga."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pop-up on Click"
msgstr "Hüpikaken klikkimisel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Portrait (4/5)"
msgstr "Portree (4/5)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Position"
msgstr "Amet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_prevent_zero_price_sale
msgid "Prevent Sale of Zero Priced Product"
msgstr "Väldi 0 hinnaga toodete müüki"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "Eelmine (vasak nool)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#, python-format
msgid "Price"
msgstr "Hind"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - High to Low"
msgstr "Hind - kallimast odavamaks"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - Low to High"
msgstr "Hind - odavast kallimaks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Price Filter"
msgstr "Hinna filter"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "Hind ühiku kohta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Hinnakiri saadaval sellele Ecommerce/Veebilehele"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Hinnakiri"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_pricelist
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Hinnakirjad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Prices displayed on your eCommerce"
msgstr "Hindu kuvatakse teie e-poes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Prindi"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "Lisage tellimus tööse, kui makse on kätte saadud."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Prod. Desc."
msgstr "Toote kirjeldus"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "Toode"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "Toote tarvikud"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Toote atribuudid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid "Product Carousel"
msgstr "Tootekarussell"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Toote kategooria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Comparison Tool"
msgstr "Tootevõrdluse tööriist"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_document
msgid "Product Document"
msgstr "Toote dokument"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "Toote pilt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "Tootepildid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "Product Name"
msgstr "Toote nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Page"
msgstr "Tooteleht"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "Toote lehe lisaväljad"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_grid_columns
msgid "Product Page Grid Columns"
msgstr "Tootelehe veergude ruudustik"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_layout
msgid "Product Page Image Layout"
msgstr "Toote lehe pildi vorming"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_spacing
msgid "Product Page Image Spacing"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_width
msgid "Product Page Image Width"
msgstr "Toote lehe pildi laius"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_product_pages_list
msgid "Product Pages"
msgstr "Tootelehed"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "Toote avalikud kategooriad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Reference Price"
msgstr "Toote võrdlushind"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_tag
msgid "Product Tag"
msgstr "Tootesilt"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_product_tags
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags"
msgstr "Tootesildid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags Filter"
msgstr "Toote sildi filter"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "Toote mall"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Toote malli atribuutide rida"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Tootemalli atribuudi väärtus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Tootemall"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "Toote variatsioon"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Variants"
msgstr "Toote variatsioonid"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "Toote vaated"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "Toote vaadete ajalugu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "Toodete nimed"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Product not found"
msgstr "Toodet ei leitud"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "Tooteriba"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_pages
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "Tooted"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products List"
msgstr "Toodete nimekiri"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products Page"
msgstr "Toodete leht"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "Hiljuti koos müüdud tooted"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "Tooted mida hiljuti müüdi koos tootega"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Products Ribbon"
msgstr "Toodete rida"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "Toote vaated"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Promo Code"
msgstr "Sooduskood"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
" '%s' peale lisatud video url ei kehti. Palun sisestage kehtiv video URL."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_search
msgid "Published"
msgstr "Avaldatud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "Lükka alla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "Lükka alla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "Lükka üles"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "Lükka ülespoole"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Quantity"
msgstr "Kogus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Radio"
msgstr "Ringidega valikud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Rating"
msgstr "Hinnang"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg_text
msgid "Rating Avg Text"
msgstr "Keskmine teksti hinnang"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Viimase tagasiside hinnang"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "Viimase pildi hinnang"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "Hinnangu viimane väärtus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Rahulolu hinnang"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_text
msgid "Rating Text"
msgstr "Hindamistekst"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "Hinnangu arv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Ratings"
msgstr "Hinnangud"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Re-Order"
msgstr "Telli uuesti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Re-order"
msgstr "Telli uuesti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_enabled_portal_reorder_button
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_portal_reorder_button
msgid "Re-order From Portal"
msgstr "Telli uuesti"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "Viimati müüdud tooted"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "Viimati vaadatud tooted"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "Taastamise e-kiri saadetud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "Taastamise e-mail saatmiseks"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "Armeeritud suurte raskuste jaoks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Remove"
msgstr "Eemalda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Remove all"
msgstr "Eemalda kõik"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "Eemalda ostukorvist"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Remove one"
msgstr "Eemaldage üks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Replace"
msgstr "Asenda"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_tag__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "Avaldage ainult see veebileht"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Resume Order"
msgstr "Jätka tellimust"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Return to shipping"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Review Order"
msgstr "Tellimuse ülevaade"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
msgid "Ribbon"
msgstr "Riba"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "Riba taustavärv"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "Riba klass"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "Riba html"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "Riba teksti värv"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "Parem"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimized"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise viga"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "Müük"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "Müügianalüüs"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Sales"
msgstr "Müük"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "Müügianalüüs"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Müükide analüüsiraport"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Müügitellimus"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Müügitellimuse rida"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Müügimeeskond"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Müügiesindaja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_card_group
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "Näidis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Save address"
msgstr "Salvesta aadress"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "Otsi hüljatud müügitellimusi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Search bar"
msgstr "Otsinguriba"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select"
msgstr "Vali"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Valige <b>Uus toode</b>, et luua ja hallata selle omadusi müügi "
"suurendamiseks."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select Quantity"
msgstr "Vali kogus"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Select a pick-up point"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Valitav"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Send After"
msgstr "Saada pärast"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "Saada ostukorvi taastamise e-kiri"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "Saatke taastamise e-kiri"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send after"
msgstr "Saada pärast"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send by Email"
msgstr "Saada e-kirjaga"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__send_abandoned_cart_email
msgid "Send email to customers who abandoned their cart."
msgstr "Saatke e-kirju klientidele, kellel on mahajäetud ostukorv. "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "Seo nimi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
msgid "Sequence"
msgstr "Jada"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Teenused"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Share"
msgstr "Jaga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Ship to the same address"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#, python-format
msgid "Shipping"
msgstr "Saatmine"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr "Tarneaadress"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "Transpordi hinnad"

#. module: website_sale
#: model:ir.model,name:website_sale.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Tarneviisid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Shipping address"
msgstr "Kohaletoimetamise aadress"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket Shipping Methods"
msgstr "Shiprocket tarneviis"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "Pood"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Pood - Kassa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Checkout Process"
msgstr "Pood - Kassa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Pood - Kinnitatud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Products"
msgstr "Pood - Tooted"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "Pood - vali maksemeetod"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_default_sort
msgid "Shop Default Sort"
msgstr "Poe vaikimisi sorteerimine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "Ostukorv"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty"
msgstr "Näita tühjana"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show b2b Fields"
msgstr "Näita B2B välju"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_search
msgid "Show on Ecommerce"
msgstr "Näita e-kaubanduses"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_document__shown_on_product_page
msgid "Show on product page"
msgstr "Näita tootekaardil"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show/hide shopping cart"
msgstr "Näita/Peida ostukorv "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Sign In"
msgstr "Logi sisse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "Registreeru"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Sign in"
msgstr "Logi sisse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sign in/up at checkout"
msgstr "Sisselogimise ja registreerimise võimalus ostukorvis"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "Istu mugavalt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "Suurus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "Suurus X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "Suurus Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "Viltu"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__small
msgid "Small"
msgstr "Väike"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "Välja müüdud"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "Mõned nõutud väljad on tühjad."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "Vabandust, me ei saa teie tellimust edastada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Sort by"
msgstr "Sorteeri"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_normal_website_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid "Specify unit"
msgstr "Täpsusta ühik"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "Maakond"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Olek"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__stay
msgid "Stay on Product Page"
msgstr "Jää tootelehele"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street and Number"
msgstr "Tänav ja tänavanumber"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect pole teie riigis saadaval, kasutage mõnda muud makseviisi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Style"
msgstr "Stiil"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal"
msgstr "Vahesumma"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"Paku välja klientidele alternatiive (ülesostu strateegia). Need tooted "
"kuvatakse tootelehel."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Suggested Accessories"
msgstr "Soovitatud lisavalikud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested accessories"
msgstr "Soovitatavad lisatarvikud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "Soovitatud lisavalikud ostukorvis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "Silt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Tags"
msgstr "Sildid"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_excluded
msgid "Tax Excluded"
msgstr "Maksudeta"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_included
msgid "Tax Included"
msgstr "Maksudega"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tax Indication"
msgstr "Maksu näitajad"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__amount_delivery
msgid "Tax included or excluded depending on the website configuration."
msgstr "Veebilehe seadistuse põhiselt kas hind sisaldab või ei sisalda makse."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes"
msgstr "Maksud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Text"
msgstr "Tekst"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale_text
msgid "Text to show instead of price"
msgstr "Kuvatav tekst hinna asemel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Täname teid tellimuse eest."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "#1"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Juurdepääsutähis on kehtetu."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,help:website_sale.field_product_template__compare_list_price
msgid ""
"The amount will be displayed strikethroughed on the eCommerce product page"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has already been paid. Please refresh the page."
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "Ostukäru on uuendatud. Palun värskenda lehte."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The company of the website you are trying to sale from (%s) is different "
"than the one you want to use (%s)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumendile veebisaidi kaudu juurdepääsuks täielik URL."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr ""
"Antud kombinatsiooni ei eksisteeri ja seetõttu ei saa seda korvi lisada."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "Antud toodet ei eksisteeri ja seetõttu ei saa seda korvi lisada."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given product does not have a price therefore it cannot be added to "
"cart."
msgstr "Antud tootel ei ole hinda ja seega ei saa seda korvi lisada."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"Siia valitud viis rakendub arveldamise meetodiks uutele loodud toodetele, "
"aga mitte juba olemasolevatele toodetele."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "Tellimus on tühistatud."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Edit Click on the page and enable 'Categories' to view all eCommerce "
"categories."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr "Aega, millal korv märgitakse hüljatuks, on võimalik muuta seadetest."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "Sellelt veebilehelt pole ühtegi kinnitatud tellimust"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "Veebilehelt pole veel ühtegi maksmata tellimust"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "Seda kombinatsiooni ei eksisteeri."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "See on teie praegune korv."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid ""
"This partner has an open cart. Please note that the pricelist will not be "
"updated on that cart. Also, the cart might not be visible for the customer "
"until you update the pricelist of that cart."
msgstr ""
"Sellel kliendil on lahtine korv. Pange tähele et hinnakirja ei uuendata "
"korvil. On võimalik, et klient ei näe oma korvi kui te uuendate hinnakirja."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/000.js:0
#, python-format
msgid "This product does not exist therefore it cannot be added to cart."
msgstr "Antud toodet ei eksisteeri ja seetõttu ei saa seda korvi lisada."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "Tootel ei ole ühtegi kehtivat kombinatsiooni."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "Seda toodet pole enam saadaval."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "This product is not available for purchase."
msgstr "See toode pole ostmiseks saadaval."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "See toode on avalikustamata."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "Sellist promokoodi pole saaval."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails"
msgstr "Pisipilt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Selleks, et saata kutseid B2B režiimis, avage kontakt või valige "
"listivaatest mitu kontakti ning vajutage \"Portaali ligipääsu haldus\" "
"valikule \"Tegevused\" alt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top"
msgstr "Ülemine"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top Bar"
msgstr "Ülemine rida"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Total"
msgstr "Kokku"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "Kogu vaadatud toodete arv"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "Toodete kogu vaatamiste arv"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"Tõene ainult toote filtritel mis vajavad product_id sest on seotud "
"ristmüügiga"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "Toodet demonstreeriva video url."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "USPS tarneviis"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "Tasumata"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
msgid "Unpaid Orders"
msgstr "Maksmata tellimused"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "Avaldamata"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "Laadige fail üles enda kohalikkust teegist."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Use Google Places API to validate addresses entered by your visitors"
msgstr ""
"Kasuta \"Google Places API\" kasutajate sisestanud aadressite "
"valideerimiseks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "VAT"
msgstr "KMKR nr"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Excluded"
msgstr "KM-ta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Included"
msgstr "KM-ga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Variant"
msgstr "Variant"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__ribbon_id
msgid "Variant Ribbon"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Variants"
msgstr "Variatsioonid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Vertical (2/3)"
msgstr "Vertikaalne (2/3)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "Video url"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "Vaata toodet"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml:0
#, python-format
msgid "View cart"
msgstr "Vaata ostukorvi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "View product"
msgstr "Vaata toodet"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Viewer"
msgstr "Ülevaataja"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "Nähtavus"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "Nähtav"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "Nähtav praegusel veebilehel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_tag__visible_on_ecommerce
msgid "Visible on eCommerce"
msgstr "Nähtav e-kaubanduses"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "Külastatud lehed"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "Külastatud tooted"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "Külastaja tootehinnangud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "Külastaja toodete vaadete ajalugu"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#: model:ir.model.fields,field_description:website_sale.field_sale_order__shop_warning
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__shop_warning
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
#, python-format
msgid "Warning"
msgstr "Hoiatus"

#. module: website_sale
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Garantii"

#. module: website_sale
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"Artikli ostjale väljastatud tootja garantii, mis lubab vajadusel parandada "
"või asendada selle kindlaksmääratud aja jooksul."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_tag__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "Veebileht"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "Veebilehe tootekategooria"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Veebilehe avalikud kategooriad"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "Veebilehe järjestus"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Veebipood"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Veebilehe väljalõike filter"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "Veebilehe URL"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "Veebilehe Külastaja"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "Veebilehe metakirjeldus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "Veebilehe meta võtmesõnad"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "Veebilehe metapealkiri"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "Veebilehe opengraph pilt"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created for eCommerce orders."
msgstr "Veebileht mille kaudu e-kaubanduse jaoks see arve esitati."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed for eCommerce orders."
msgstr "Veebileht, mille kaudu e-kaubanduse jaoks see tellimus esitati."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "Veebilehed"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "What should be done on \"Add to Cart\"?"
msgstr "Mis juhtub kui vajutatakse \"Lisa ostukorvi\"?"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_tag__visible_on_ecommerce
msgid "Whether the tag is displayed on the eCommerce."
msgstr "Kas silti näidatakse e-kaubanduses."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "Tahvel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "Soovide nimekirjad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"Esimese meetodiga saate määrata tootele seadistustest (Müük alt) mitu hinda."
" Teise valikuga saate määrata hindu ja arvutusi hinnakirjadest."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Yes"
msgstr "Jah"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"Sa muudad oma <b>arve- ja tarne</b>aadressi samaaegselt!<br/>\n"
"                                            Kui sa tahad muuta oma tarneaadressi, siis loo "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "You can't use a video as the product's main image."
msgstr "Toote peamise pildina ei saa kasutada videot."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "Teil ei ole sellelt veebilehelt ühtegi tellimust"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "Teil ei ole veebilehelt ühtegi tellimust arveldada"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "Te jätsite tooteid korvi!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"Siit leiate külaliste poolt hüljatud ostukorvid.\n"
"Kui nad lisasid oma aadressi, võiksite neile korvi taastamise e-kirja saata!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.alternative_products
msgid ""
"Your Dynamic Snippet will be displayed here...\n"
"                                This message is displayed because youy did not provide both a filter and a template to use."
msgstr ""
"Teie Dünaamiline väljalõige kuvatakse siin... \n"
"                                 See sõnum kuvati, kuna te ei lisanud nii filtrit kui ka malli, mida kasutusele võtta. "

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Sinu e-post"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Sinu nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Your cart is empty!"
msgstr "Teie korv on tühi!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Your cart is not ready to be paid, please verify previous steps."
msgstr "Teie korv ei ole valmis maksmiseks, kontrollige üle eelnevad sammud."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr "Teie makse on autoriseeritud"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "Teie eelnev korv on juba lõpetatud."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "Postiindeks"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "Sihtnumbri prefiks"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "Bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "Bpost tarneviis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "e.g. Cheese Burger"
msgstr "nt. Juustuburger"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "nt. lamp, prügikast"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "E-kaubandus"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "E-kaubanduse kategooriad"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__description_ecommerce
#: model:ir.model.fields,field_description:website_sale.field_product_template__description_ecommerce
msgid "eCommerce Description"
msgstr "E-kaubanduse kirjeldus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "E-poe filtri nähtavus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "E-kaubanduse müügid"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "E-kaubanduse pood"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "eCommerce cart"
msgstr "E-kaubanduse ostukäru"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_cron_send_availability_email_ir_actions_server
msgid "eCommerce: send email to customers about their abandoned cart"
msgstr "E-kaubandus: saatke klientidele e-kirju mahajäetud ostukorvi teemal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "Kui te soovite ühendada oma eelmist korvi praegusega."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"kui te soovite taastada eelmist korvi. Teie praegune korv asendatakse "
"eelmisega."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "kategoorias *"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "uus aadress"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "or"
msgstr "või"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "remove"
msgstr "eemalda"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "select to see available Pick-Up Locations"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "terms &amp; conditions"
msgstr "tingimused"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "oma toote jälgimiseks."
