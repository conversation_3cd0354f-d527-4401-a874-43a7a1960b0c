"id","name","model_id:id","group_id:id","perm_read","perm_write","perm_create","perm_unlink"
access_export_boolean,access_export_boolean,model_export_boolean,base.group_user,1,1,1,1
access_export_integer,access_export_integer,model_export_integer,base.group_user,1,1,1,1
access_export_float,access_export_float,model_export_float,base.group_user,1,1,1,1
access_export_decimal,access_export_decimal,model_export_decimal,base.group_user,1,1,1,1
access_export_string_bounded,access_export_string_bounded,model_export_string_bounded,base.group_user,1,1,1,1
access_export_string_required,access_export_string_required,model_export_string_required,base.group_user,1,1,1,1
access_export_string,access_export_string,model_export_string,base.group_user,1,1,1,1
access_export_date,access_export_date,model_export_date,base.group_user,1,1,1,1
access_export_datetime,access_export_datetime,model_export_datetime,base.group_user,1,1,1,1
access_export_text,access_export_text,model_export_text,base.group_user,1,1,1,1
access_export_selection,access_export_selection,model_export_selection,base.group_user,1,1,1,1
access_export_selection_function,access_export_selection_function,model_export_selection_function,base.group_user,1,1,1,1
access_export_many2one,access_export_many2one,model_export_many2one,base.group_user,1,1,1,1
access_export_one2many,access_export_one2many,model_export_one2many,base.group_user,1,1,1,1
access_export_many2many,access_export_many2many,model_export_many2many,base.group_user,1,1,1,1
access_export_function,access_export_function,model_export_function,base.group_user,1,1,1,1
access_export_one2many_child,access_export_one2many_child,model_export_one2many_child,base.group_user,1,1,1,1
access_export_one2many_multiple,access_export_one2many_multiple,model_export_one2many_multiple,base.group_user,1,1,1,1
access_export_one2many_multiple_child,access_export_one2many_multiple_child,model_export_one2many_multiple_child,base.group_user,1,1,1,1
access_export_one2many_child_1,access_export_one2many_child_1,model_export_one2many_child_1,base.group_user,1,1,1,1
access_export_one2many_child_2,access_export_one2many_child_2,model_export_one2many_child_2,base.group_user,1,1,1,1
access_export_many2many_other,access_export_many2many_other,model_export_many2many_other,base.group_user,1,1,1,1
access_export_selection_withdefault,access_export_selection_withdefault,model_export_selection_withdefault,base.group_user,1,1,1,1
access_export_one2many_recursive,access_export_one2many_recursive,model_export_one2many_recursive,base.group_user,1,1,1,1
access_export_unique,access_export_unique,model_export_unique,base.group_user,1,1,1,1
access_export_inherits_parent,access_export_inherits_parent,model_export_inherits_parent,base.group_user,1,1,1,1
access_export_inherits_child,access_export_inherits_child,model_export_inherits_child,base.group_user,1,1,1,1
access_export_m2o_str,access_export_m2o_str,model_export_m2o_str,base.group_user,1,1,1,1
access_export_m2o_str_child,access_export_m2o_str_child,model_export_m2o_str_child,base.group_user,1,1,1,1
access_export_with_required_field,access_export_with_required_field,model_export_with_required_field,base.group_user,1,1,1,1
access_export_many2one_required_subfield,access_export_many2one_required_subfield,model_export_many2one_required_subfield,base.group_user,1,1,1,1
