<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="mmap — Memory-mapped file support" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/mmap.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Availability: not Emscripten, not WASI. This module does not work or is not available on WebAssembly platforms wasm32-emscripten and wasm32-wasi. See WebAssembly platforms for more information. Mem..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Availability: not Emscripten, not WASI. This module does not work or is not available on WebAssembly platforms wasm32-emscripten and wasm32-wasi. See WebAssembly platforms for more information. Mem..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>mmap — Memory-mapped file support &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Internet Data Handling" href="netdata.html" />
    <link rel="prev" title="signal — Set handlers for asynchronous events" href="signal.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/mmap.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code> — Memory-mapped file support</a><ul>
<li><a class="reference internal" href="#madv-constants">MADV_* Constants</a></li>
<li><a class="reference internal" href="#map-constants">MAP_* Constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="signal.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code> — Set handlers for asynchronous events</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="netdata.html"
                          title="next chapter">Internet Data Handling</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mmap.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="netdata.html" title="Internet Data Handling"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="signal.html" title="signal — Set handlers for asynchronous events"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" accesskey="U">Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code> — Memory-mapped file support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-mmap">
<span id="mmap-memory-mapped-file-support"></span><h1><a class="reference internal" href="#module-mmap" title="mmap: Interface to memory-mapped files for Unix and Windows."><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code></a> — Memory-mapped file support<a class="headerlink" href="#module-mmap" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>Memory-mapped file objects behave like both <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> and like
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file objects</span></a>.  You can use mmap objects in most places
where <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> are expected; for example, you can use the <a class="reference internal" href="re.html#module-re" title="re: Regular expression operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code></a>
module to search through a memory-mapped file.  You can also change a single
byte by doing <code class="docutils literal notranslate"><span class="pre">obj[index]</span> <span class="pre">=</span> <span class="pre">97</span></code>, or change a subsequence by assigning to a
slice: <code class="docutils literal notranslate"><span class="pre">obj[i1:i2]</span> <span class="pre">=</span> <span class="pre">b'...'</span></code>.  You can also read and write data starting at
the current file position, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code> through the file to different positions.</p>
<p>A memory-mapped file is created by the <a class="reference internal" href="#mmap.mmap" title="mmap.mmap"><code class="xref py py-class docutils literal notranslate"><span class="pre">mmap</span></code></a> constructor, which is
different on Unix and on Windows.  In either case you must provide a file
descriptor for a file opened for update. If you wish to map an existing Python
file object, use its <a class="reference internal" href="io.html#io.IOBase.fileno" title="io.IOBase.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a> method to obtain the correct value for the
<em>fileno</em> parameter.  Otherwise, you can open the file using the
<a class="reference internal" href="os.html#os.open" title="os.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.open()</span></code></a> function, which returns a file descriptor directly (the file
still needs to be closed when done).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you want to create a memory-mapping for a writable, buffered file, you
should <a class="reference internal" href="io.html#io.IOBase.flush" title="io.IOBase.flush"><code class="xref py py-func docutils literal notranslate"><span class="pre">flush()</span></code></a> the file first.  This is necessary to ensure
that local modifications to the buffers are actually available to the
mapping.</p>
</div>
<p>For both the Unix and Windows versions of the constructor, <em>access</em> may be
specified as an optional keyword parameter. <em>access</em> accepts one of four
values: <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_READ</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_WRITE</span></code>, or <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_COPY</span></code> to
specify read-only, write-through or copy-on-write memory respectively, or
<code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_DEFAULT</span></code> to defer to <em>prot</em>.  <em>access</em> can be used on both Unix
and Windows.  If <em>access</em> is not specified, Windows mmap returns a
write-through mapping.  The initial memory values for all three access types
are taken from the specified file.  Assignment to an <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_READ</span></code>
memory map raises a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception.  Assignment to an
<code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_WRITE</span></code> memory map affects both memory and the underlying file.
Assignment to an <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_COPY</span></code> memory map affects memory but does not
update the underlying file.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_DEFAULT</span></code> constant.</p>
</div>
<p>To map anonymous memory, -1 should be passed as the fileno along with the length.</p>
<dl class="py class">
<dt class="sig sig-object py" id="mmap.mmap">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">mmap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tagname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">access=ACCESS_DEFAULT</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap" title="Link to this definition">¶</a></dt>
<dd><p><strong>(Windows version)</strong> Maps <em>length</em> bytes from the file specified by the
file handle <em>fileno</em>, and creates a mmap object.  If <em>length</em> is larger
than the current size of the file, the file is extended to contain <em>length</em>
bytes.  If <em>length</em> is <code class="docutils literal notranslate"><span class="pre">0</span></code>, the maximum length of the map is the current
size of the file, except that if the file is empty Windows raises an
exception (you cannot create an empty mapping on Windows).</p>
<p><em>tagname</em>, if specified and not <code class="docutils literal notranslate"><span class="pre">None</span></code>, is a string giving a tag name for
the mapping.  Windows allows you to have many different mappings against
the same file.  If you specify the name of an existing tag, that tag is
opened, otherwise a new tag of this name is created.  If this parameter is
omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, the mapping is created without a name.  Avoiding the
use of the <em>tagname</em> parameter will assist in keeping your code portable
between Unix and Windows.</p>
<p><em>offset</em> may be specified as a non-negative integer offset. mmap references
will be relative to the offset from the beginning of the file. <em>offset</em>
defaults to 0.  <em>offset</em> must be a multiple of the <code class="xref py py-const docutils literal notranslate"><span class="pre">ALLOCATIONGRANULARITY</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">mmap.__new__</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">fileno</span></code>, <code class="docutils literal notranslate"><span class="pre">length</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>, <code class="docutils literal notranslate"><span class="pre">offset</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">mmap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags=MAP_SHARED</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prot=PROT_WRITE|PROT_READ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">access=ACCESS_DEFAULT</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="optional">]</span><span class="sig-paren">)</span></dt>
<dd><p><strong>(Unix version)</strong> Maps <em>length</em> bytes from the file specified by the file
descriptor <em>fileno</em>, and returns a mmap object.  If <em>length</em> is <code class="docutils literal notranslate"><span class="pre">0</span></code>, the
maximum length of the map will be the current size of the file when
<a class="reference internal" href="#mmap.mmap" title="mmap.mmap"><code class="xref py py-class docutils literal notranslate"><span class="pre">mmap</span></code></a> is called.</p>
<p><em>flags</em> specifies the nature of the mapping. <a class="reference internal" href="#mmap.MAP_PRIVATE" title="mmap.MAP_PRIVATE"><code class="xref py py-const docutils literal notranslate"><span class="pre">MAP_PRIVATE</span></code></a> creates a
private copy-on-write mapping, so changes to the contents of the mmap
object will be private to this process, and <a class="reference internal" href="#mmap.MAP_SHARED" title="mmap.MAP_SHARED"><code class="xref py py-const docutils literal notranslate"><span class="pre">MAP_SHARED</span></code></a> creates a
mapping that’s shared with all other processes mapping the same areas of
the file.  The default value is <a class="reference internal" href="#mmap.MAP_SHARED" title="mmap.MAP_SHARED"><code class="xref py py-const docutils literal notranslate"><span class="pre">MAP_SHARED</span></code></a>. Some systems have
additional possible flags with the full list specified in
<a class="reference internal" href="#map-constants"><span class="std std-ref">MAP_* constants</span></a>.</p>
<p><em>prot</em>, if specified, gives the desired memory protection; the two most
useful values are <code class="xref py py-const docutils literal notranslate"><span class="pre">PROT_READ</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">PROT_WRITE</span></code>, to specify
that the pages may be read or written.  <em>prot</em> defaults to
<code class="xref py py-const docutils literal notranslate"><span class="pre">PROT_READ</span> <span class="pre">|</span> <span class="pre">PROT_WRITE</span></code>.</p>
<p><em>access</em> may be specified in lieu of <em>flags</em> and <em>prot</em> as an optional
keyword parameter.  It is an error to specify both <em>flags</em>, <em>prot</em> and
<em>access</em>.  See the description of <em>access</em> above for information on how to
use this parameter.</p>
<p><em>offset</em> may be specified as a non-negative integer offset. mmap references
will be relative to the offset from the beginning of the file. <em>offset</em>
defaults to 0. <em>offset</em> must be a multiple of <code class="xref py py-const docutils literal notranslate"><span class="pre">ALLOCATIONGRANULARITY</span></code>
which is equal to <code class="xref py py-const docutils literal notranslate"><span class="pre">PAGESIZE</span></code> on Unix systems.</p>
<p>To ensure validity of the created memory mapping the file specified
by the descriptor <em>fileno</em> is internally automatically synchronized
with the physical backing store on macOS.</p>
<p>This example shows a simple way of using <a class="reference internal" href="#mmap.mmap" title="mmap.mmap"><code class="xref py py-class docutils literal notranslate"><span class="pre">mmap</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mmap</span>

<span class="c1"># write a simple example file</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;hello.txt&quot;</span><span class="p">,</span> <span class="s2">&quot;wb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Hello Python!</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;hello.txt&quot;</span><span class="p">,</span> <span class="s2">&quot;r+b&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="c1"># memory-map the file, size 0 means whole file</span>
    <span class="n">mm</span> <span class="o">=</span> <span class="n">mmap</span><span class="o">.</span><span class="n">mmap</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">fileno</span><span class="p">(),</span> <span class="mi">0</span><span class="p">)</span>
    <span class="c1"># read content via standard file methods</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">mm</span><span class="o">.</span><span class="n">readline</span><span class="p">())</span>  <span class="c1"># prints b&quot;Hello Python!\n&quot;</span>
    <span class="c1"># read content via slice notation</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">mm</span><span class="p">[:</span><span class="mi">5</span><span class="p">])</span>  <span class="c1"># prints b&quot;Hello&quot;</span>
    <span class="c1"># update content using slice notation;</span>
    <span class="c1"># note that new content must have same size</span>
    <span class="n">mm</span><span class="p">[</span><span class="mi">6</span><span class="p">:]</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot; world!</span><span class="se">\n</span><span class="s2">&quot;</span>
    <span class="c1"># ... and read again using standard file methods</span>
    <span class="n">mm</span><span class="o">.</span><span class="n">seek</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">mm</span><span class="o">.</span><span class="n">readline</span><span class="p">())</span>  <span class="c1"># prints b&quot;Hello  world!\n&quot;</span>
    <span class="c1"># close the map</span>
    <span class="n">mm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p><a class="reference internal" href="#mmap.mmap" title="mmap.mmap"><code class="xref py py-class docutils literal notranslate"><span class="pre">mmap</span></code></a> can also be used as a context manager in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a>
statement:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mmap</span>

<span class="k">with</span> <span class="n">mmap</span><span class="o">.</span><span class="n">mmap</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="mi">13</span><span class="p">)</span> <span class="k">as</span> <span class="n">mm</span><span class="p">:</span>
    <span class="n">mm</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Hello world!&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span>Context manager support.</p>
</div>
<p>The next example demonstrates how to create an anonymous map and exchange
data between the parent and child processes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mmap</span>
<span class="kn">import</span> <span class="nn">os</span>

<span class="n">mm</span> <span class="o">=</span> <span class="n">mmap</span><span class="o">.</span><span class="n">mmap</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="mi">13</span><span class="p">)</span>
<span class="n">mm</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Hello world!&quot;</span><span class="p">)</span>

<span class="n">pid</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">fork</span><span class="p">()</span>

<span class="k">if</span> <span class="n">pid</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>  <span class="c1"># In a child process</span>
    <span class="n">mm</span><span class="o">.</span><span class="n">seek</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">mm</span><span class="o">.</span><span class="n">readline</span><span class="p">())</span>

    <span class="n">mm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">mmap.__new__</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">fileno</span></code>, <code class="docutils literal notranslate"><span class="pre">length</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>, <code class="docutils literal notranslate"><span class="pre">offset</span></code>.</p>
<p>Memory-mapped file objects support the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.close" title="Link to this definition">¶</a></dt>
<dd><p>Closes the mmap. Subsequent calls to other methods of the object will
result in a ValueError exception being raised. This will not close
the open file.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="mmap.mmap.closed">
<span class="sig-name descname"><span class="pre">closed</span></span><a class="headerlink" href="#mmap.mmap.closed" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the file is closed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.find">
<span class="sig-name descname"><span class="pre">find</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sub</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">end</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.find" title="Link to this definition">¶</a></dt>
<dd><p>Returns the lowest index in the object where the subsequence <em>sub</em> is
found, such that <em>sub</em> is contained in the range [<em>start</em>, <em>end</em>].
Optional arguments <em>start</em> and <em>end</em> are interpreted as in slice notation.
Returns <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.flush" title="Link to this definition">¶</a></dt>
<dd><p>Flushes changes made to the in-memory copy of a file back to disk. Without
use of this call there is no guarantee that changes are written back before
the object is destroyed.  If <em>offset</em> and <em>size</em> are specified, only
changes to the given range of bytes will be flushed to disk; otherwise, the
whole extent of the mapping is flushed.  <em>offset</em> must be a multiple of the
<code class="xref py py-const docutils literal notranslate"><span class="pre">PAGESIZE</span></code> or <code class="xref py py-const docutils literal notranslate"><span class="pre">ALLOCATIONGRANULARITY</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">None</span></code> is returned to indicate success.  An exception is raised when the
call failed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Previously, a nonzero value was returned on success; zero was returned
on error under Windows.  A zero value was returned on success; an
exception was raised on error under Unix.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.madvise">
<span class="sig-name descname"><span class="pre">madvise</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">option</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.madvise" title="Link to this definition">¶</a></dt>
<dd><p>Send advice <em>option</em> to the kernel about the memory region beginning at
<em>start</em> and extending <em>length</em> bytes.  <em>option</em> must be one of the
<a class="reference internal" href="#madvise-constants"><span class="std std-ref">MADV_* constants</span></a> available on the system.  If
<em>start</em> and <em>length</em> are omitted, the entire mapping is spanned.  On
some systems (including Linux), <em>start</em> must be a multiple of the
<code class="xref py py-const docutils literal notranslate"><span class="pre">PAGESIZE</span></code>.</p>
<p>Availability: Systems with the <code class="docutils literal notranslate"><span class="pre">madvise()</span></code> system call.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.move">
<span class="sig-name descname"><span class="pre">move</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.move" title="Link to this definition">¶</a></dt>
<dd><p>Copy the <em>count</em> bytes starting at offset <em>src</em> to the destination index
<em>dest</em>.  If the mmap was created with <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_READ</span></code>, then calls to
move will raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.read" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> containing up to <em>n</em> bytes starting from the
current file position. If the argument is omitted, <code class="docutils literal notranslate"><span class="pre">None</span></code> or negative,
return all bytes from the current file position to the end of the
mapping. The file position is updated to point after the bytes that were
returned.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Argument can be omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.read_byte">
<span class="sig-name descname"><span class="pre">read_byte</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.read_byte" title="Link to this definition">¶</a></dt>
<dd><p>Returns a byte at the current file position as an integer, and advances
the file position by 1.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.readline">
<span class="sig-name descname"><span class="pre">readline</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.readline" title="Link to this definition">¶</a></dt>
<dd><p>Returns a single line, starting at the current file position and up to the
next newline. The file position is updated to point after the bytes that were
returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.resize">
<span class="sig-name descname"><span class="pre">resize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newsize</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.resize" title="Link to this definition">¶</a></dt>
<dd><p>Resizes the map and the underlying file, if any. If the mmap was created
with <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_READ</span></code> or <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_COPY</span></code>, resizing the map will
raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception.</p>
<p><strong>On Windows</strong>: Resizing the map will raise an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if there are other
maps against the same named file. Resizing an anonymous map (ie against the
pagefile) will silently create a new map with the original data copied over
up to the length of the new size.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Correctly fails if attempting to resize when another map is held
Allows resize against an anonymous map on Windows</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.rfind">
<span class="sig-name descname"><span class="pre">rfind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sub</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">end</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.rfind" title="Link to this definition">¶</a></dt>
<dd><p>Returns the highest index in the object where the subsequence <em>sub</em> is
found, such that <em>sub</em> is contained in the range [<em>start</em>, <em>end</em>].
Optional arguments <em>start</em> and <em>end</em> are interpreted as in slice notation.
Returns <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.seek">
<span class="sig-name descname"><span class="pre">seek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">whence</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.seek" title="Link to this definition">¶</a></dt>
<dd><p>Set the file’s current position.  <em>whence</em> argument is optional and
defaults to <code class="docutils literal notranslate"><span class="pre">os.SEEK_SET</span></code> or <code class="docutils literal notranslate"><span class="pre">0</span></code> (absolute file positioning); other
values are <code class="docutils literal notranslate"><span class="pre">os.SEEK_CUR</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code> (seek relative to the current
position) and <code class="docutils literal notranslate"><span class="pre">os.SEEK_END</span></code> or <code class="docutils literal notranslate"><span class="pre">2</span></code> (seek relative to the file’s end).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.size">
<span class="sig-name descname"><span class="pre">size</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.size" title="Link to this definition">¶</a></dt>
<dd><p>Return the length of the file, which can be larger than the size of the
memory-mapped area.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.tell" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current position of the file pointer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the bytes in <em>bytes</em> into memory at the current position of the
file pointer and return the number of bytes written (never less than
<code class="docutils literal notranslate"><span class="pre">len(bytes)</span></code>, since if the write fails, a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be
raised).  The file position is updated to point after the bytes that
were written.  If the mmap was created with <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_READ</span></code>, then
writing to it will raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Writable <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The number of bytes written is now returned.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mmap.mmap.write_byte">
<span class="sig-name descname"><span class="pre">write_byte</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byte</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mmap.mmap.write_byte" title="Link to this definition">¶</a></dt>
<dd><p>Write the integer <em>byte</em> into memory at the current
position of the file pointer; the file position is advanced by <code class="docutils literal notranslate"><span class="pre">1</span></code>. If
the mmap was created with <code class="xref py py-const docutils literal notranslate"><span class="pre">ACCESS_READ</span></code>, then writing to it will
raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception.</p>
</dd></dl>

</dd></dl>

<section id="madv-constants">
<span id="madvise-constants"></span><h2>MADV_* Constants<a class="headerlink" href="#madv-constants" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="mmap.MADV_NORMAL">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_NORMAL</span></span><a class="headerlink" href="#mmap.MADV_NORMAL" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_RANDOM">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_RANDOM</span></span><a class="headerlink" href="#mmap.MADV_RANDOM" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_SEQUENTIAL">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_SEQUENTIAL</span></span><a class="headerlink" href="#mmap.MADV_SEQUENTIAL" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_WILLNEED">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_WILLNEED</span></span><a class="headerlink" href="#mmap.MADV_WILLNEED" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_DONTNEED">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_DONTNEED</span></span><a class="headerlink" href="#mmap.MADV_DONTNEED" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_REMOVE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_REMOVE</span></span><a class="headerlink" href="#mmap.MADV_REMOVE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_DONTFORK">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_DONTFORK</span></span><a class="headerlink" href="#mmap.MADV_DONTFORK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_DOFORK">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_DOFORK</span></span><a class="headerlink" href="#mmap.MADV_DOFORK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_HWPOISON">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_HWPOISON</span></span><a class="headerlink" href="#mmap.MADV_HWPOISON" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_MERGEABLE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_MERGEABLE</span></span><a class="headerlink" href="#mmap.MADV_MERGEABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_UNMERGEABLE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_UNMERGEABLE</span></span><a class="headerlink" href="#mmap.MADV_UNMERGEABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_SOFT_OFFLINE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_SOFT_OFFLINE</span></span><a class="headerlink" href="#mmap.MADV_SOFT_OFFLINE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_HUGEPAGE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_HUGEPAGE</span></span><a class="headerlink" href="#mmap.MADV_HUGEPAGE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_NOHUGEPAGE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_NOHUGEPAGE</span></span><a class="headerlink" href="#mmap.MADV_NOHUGEPAGE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_DONTDUMP">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_DONTDUMP</span></span><a class="headerlink" href="#mmap.MADV_DONTDUMP" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_DODUMP">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_DODUMP</span></span><a class="headerlink" href="#mmap.MADV_DODUMP" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_FREE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_FREE</span></span><a class="headerlink" href="#mmap.MADV_FREE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_NOSYNC">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_NOSYNC</span></span><a class="headerlink" href="#mmap.MADV_NOSYNC" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_AUTOSYNC">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_AUTOSYNC</span></span><a class="headerlink" href="#mmap.MADV_AUTOSYNC" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_NOCORE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_NOCORE</span></span><a class="headerlink" href="#mmap.MADV_NOCORE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_CORE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_CORE</span></span><a class="headerlink" href="#mmap.MADV_CORE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_PROTECT">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_PROTECT</span></span><a class="headerlink" href="#mmap.MADV_PROTECT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_FREE_REUSABLE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_FREE_REUSABLE</span></span><a class="headerlink" href="#mmap.MADV_FREE_REUSABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MADV_FREE_REUSE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MADV_FREE_REUSE</span></span><a class="headerlink" href="#mmap.MADV_FREE_REUSE" title="Link to this definition">¶</a></dt>
<dd><p>These options can be passed to <a class="reference internal" href="#mmap.mmap.madvise" title="mmap.mmap.madvise"><code class="xref py py-meth docutils literal notranslate"><span class="pre">mmap.madvise()</span></code></a>.  Not every option will
be present on every system.</p>
<p>Availability: Systems with the madvise() system call.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</section>
<section id="map-constants">
<span id="id1"></span><h2>MAP_* Constants<a class="headerlink" href="#map-constants" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="mmap.MAP_SHARED">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_SHARED</span></span><a class="headerlink" href="#mmap.MAP_SHARED" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_PRIVATE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_PRIVATE</span></span><a class="headerlink" href="#mmap.MAP_PRIVATE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_DENYWRITE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_DENYWRITE</span></span><a class="headerlink" href="#mmap.MAP_DENYWRITE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_EXECUTABLE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_EXECUTABLE</span></span><a class="headerlink" href="#mmap.MAP_EXECUTABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_ANON">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_ANON</span></span><a class="headerlink" href="#mmap.MAP_ANON" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_ANONYMOUS">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_ANONYMOUS</span></span><a class="headerlink" href="#mmap.MAP_ANONYMOUS" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_POPULATE">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_POPULATE</span></span><a class="headerlink" href="#mmap.MAP_POPULATE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_STACK">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_STACK</span></span><a class="headerlink" href="#mmap.MAP_STACK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_ALIGNED_SUPER">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_ALIGNED_SUPER</span></span><a class="headerlink" href="#mmap.MAP_ALIGNED_SUPER" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="mmap.MAP_CONCEAL">
<span class="sig-prename descclassname"><span class="pre">mmap.</span></span><span class="sig-name descname"><span class="pre">MAP_CONCEAL</span></span><a class="headerlink" href="#mmap.MAP_CONCEAL" title="Link to this definition">¶</a></dt>
<dd><p>These are the various flags that can be passed to <a class="reference internal" href="#mmap.mmap" title="mmap.mmap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">mmap.mmap()</span></code></a>.  <a class="reference internal" href="#mmap.MAP_ALIGNED_SUPER" title="mmap.MAP_ALIGNED_SUPER"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAP_ALIGNED_SUPER</span></code></a>
is only available at FreeBSD and <a class="reference internal" href="#mmap.MAP_CONCEAL" title="mmap.MAP_CONCEAL"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAP_CONCEAL</span></code></a> is only available at OpenBSD.  Note
that some options might not be present on some systems.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added <a class="reference internal" href="#mmap.MAP_POPULATE" title="mmap.MAP_POPULATE"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAP_POPULATE</span></code></a> constant.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>Added <a class="reference internal" href="#mmap.MAP_STACK" title="mmap.MAP_STACK"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAP_STACK</span></code></a> constant.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12: </span>Added <a class="reference internal" href="#mmap.MAP_ALIGNED_SUPER" title="mmap.MAP_ALIGNED_SUPER"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAP_ALIGNED_SUPER</span></code></a> constant.
Added <a class="reference internal" href="#mmap.MAP_CONCEAL" title="mmap.MAP_CONCEAL"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAP_CONCEAL</span></code></a> constant.</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code> — Memory-mapped file support</a><ul>
<li><a class="reference internal" href="#madv-constants">MADV_* Constants</a></li>
<li><a class="reference internal" href="#map-constants">MAP_* Constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="signal.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code> — Set handlers for asynchronous events</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="netdata.html"
                          title="next chapter">Internet Data Handling</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mmap.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="netdata.html" title="Internet Data Handling"
             >next</a> |</li>
        <li class="right" >
          <a href="signal.html" title="signal — Set handlers for asynchronous events"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" >Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code> — Memory-mapped file support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>