<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="utm_campaign_view_kanban">
        <field name="name">utm.campaign.view.kanban</field>
        <field name="model">utm.campaign</field>
        <field name="inherit_id" ref="utm.utm_campaign_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='user_id']" position="after">
                <field name="use_leads"/>
            </xpath>
            <xpath expr="//div[hasclass('oe_kanban_bottom_left')]" position="inside">
                <t t-if="record.use_leads.raw_value">
                    <t t-set="crm_lead_count_label">Leads</t>
                </t>
                <t t-else="">
                    <t t-set="crm_lead_count_label">Opportunities</t>
                </t>
                <a t-if="record.crm_lead_count" href="#" t-att-title="crm_lead_count_label" role="button"
                    groups="sales_team.group_sale_salesman" data-type="object" data-name="action_redirect_to_leads_opportunities"
                    class="oe_kanban_action oe_kanban_action_a btn-outline-primary rounded-pill me-1 order-3">
                    <span class="badge">
                        <i class="fa fa-fw fa-star" t-att-aria-label="crm_lead_count_label" role="img"/>
                        <field name="crm_lead_count"/>
                    </span>
                </a>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="utm_campaign_view_form">
        <field name="name">utm.campaign.view.form</field>
        <field name="model">utm.campaign</field>
        <field name="inherit_id" ref="utm.utm_campaign_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
                <button name="action_redirect_to_leads_opportunities"
                    type="object"
                    class="oe_stat_button order-3"
                    icon="fa-star"
                    groups="sales_team.group_sale_salesman">
                    <div class="o_field_widget o_stat_info">
                        <field name="use_leads" invisible="1"/>
                        <span class="o_stat_value"><field nolabel="1" name="crm_lead_count"/></span>
                        <span class="o_stat_text" invisible="not use_leads">Leads</span>
                        <span class="o_stat_text" invisible="use_leads">Opportunities</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>
</odoo>
