<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="XML Processing Modules" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xml.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xml/ Python’s interfaces for processing XML are grouped in the xml package. It is important to note that modules in the xml package require that there be at least one SAX-compliant..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xml/ Python’s interfaces for processing XML are grouped in the xml package. It is important to note that modules in the xml package require that there be at least one SAX-compliant..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>XML Processing Modules &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xml.etree.ElementTree — The ElementTree XML API" href="xml.etree.elementtree.html" />
    <link rel="prev" title="html.entities — Definitions of HTML general entities" href="html.entities.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xml.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">XML Processing Modules</a><ul>
<li><a class="reference internal" href="#xml-vulnerabilities">XML vulnerabilities</a></li>
<li><a class="reference internal" href="#the-defusedxml-package">The <code class="xref py py-mod docutils literal notranslate"><span class="pre">defusedxml</span></code> Package</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="html.entities.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.entities</span></code> — Definitions of HTML general entities</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.etree.elementtree.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.etree.elementtree.html" title="xml.etree.ElementTree — The ElementTree XML API"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="html.entities.html" title="html.entities — Definitions of HTML general entities"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">XML Processing Modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml">
<span id="xml-processing-modules"></span><span id="xml"></span><h1>XML Processing Modules<a class="headerlink" href="#module-xml" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xml/">Lib/xml/</a></p>
<hr class="docutils" />
<p>Python’s interfaces for processing XML are grouped in the <code class="docutils literal notranslate"><span class="pre">xml</span></code> package.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The XML modules are not secure against erroneous or maliciously
constructed data.  If you need to parse untrusted or
unauthenticated data see the <a class="reference internal" href="#xml-vulnerabilities"><span class="std std-ref">XML vulnerabilities</span></a> and
<a class="reference internal" href="#defusedxml-package"><span class="std std-ref">The defusedxml Package</span></a> sections.</p>
</div>
<p>It is important to note that modules in the <a class="reference internal" href="#module-xml" title="xml: Package containing XML processing modules"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml</span></code></a> package require that
there be at least one SAX-compliant XML parser available. The Expat parser is
included with Python, so the <a class="reference internal" href="pyexpat.html#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> module will always be
available.</p>
<p>The documentation for the <a class="reference internal" href="xml.dom.html#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a> and <a class="reference internal" href="xml.sax.html#module-xml.sax" title="xml.sax: Package containing SAX2 base classes and convenience functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax</span></code></a> packages are the
definition of the Python bindings for the DOM and SAX interfaces.</p>
<p>The XML handling submodules are:</p>
<ul class="simple">
<li><p><a class="reference internal" href="xml.etree.elementtree.html#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a>: the ElementTree API, a simple and lightweight
XML processor</p></li>
</ul>
<ul class="simple">
<li><p><a class="reference internal" href="xml.dom.html#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a>: the DOM API definition</p></li>
<li><p><a class="reference internal" href="xml.dom.minidom.html#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>: a minimal DOM implementation</p></li>
<li><p><a class="reference internal" href="xml.dom.pulldom.html#module-xml.dom.pulldom" title="xml.dom.pulldom: Support for building partial DOM trees from SAX events."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.pulldom</span></code></a>: support for building partial DOM trees</p></li>
</ul>
<ul class="simple">
<li><p><a class="reference internal" href="xml.sax.html#module-xml.sax" title="xml.sax: Package containing SAX2 base classes and convenience functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax</span></code></a>: SAX2 base classes and convenience functions</p></li>
<li><p><a class="reference internal" href="pyexpat.html#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a>: the Expat parser binding</p></li>
</ul>
<section id="xml-vulnerabilities">
<span id="id1"></span><h2>XML vulnerabilities<a class="headerlink" href="#xml-vulnerabilities" title="Link to this heading">¶</a></h2>
<p>The XML processing modules are not secure against maliciously constructed data.
An attacker can abuse XML features to carry out denial of service attacks,
access local files, generate network connections to other machines, or
circumvent firewalls.</p>
<p>The following table gives an overview of the known attacks and whether
the various modules are vulnerable to them.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>kind</p></th>
<th class="head"><p>sax</p></th>
<th class="head"><p>etree</p></th>
<th class="head"><p>minidom</p></th>
<th class="head"><p>pulldom</p></th>
<th class="head"><p>xmlrpc</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>billion laughs</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
</tr>
<tr class="row-odd"><td><p>quadratic blowup</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
<td><p><strong>Vulnerable</strong> (1)</p></td>
</tr>
<tr class="row-even"><td><p>external entity expansion</p></td>
<td><p>Safe (5)</p></td>
<td><p>Safe (2)</p></td>
<td><p>Safe (3)</p></td>
<td><p>Safe (5)</p></td>
<td><p>Safe (4)</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference external" href="https://en.wikipedia.org/wiki/Document_type_definition">DTD</a> retrieval</p></td>
<td><p>Safe (5)</p></td>
<td><p>Safe</p></td>
<td><p>Safe</p></td>
<td><p>Safe (5)</p></td>
<td><p>Safe</p></td>
</tr>
<tr class="row-even"><td><p>decompression bomb</p></td>
<td><p>Safe</p></td>
<td><p>Safe</p></td>
<td><p>Safe</p></td>
<td><p>Safe</p></td>
<td><p><strong>Vulnerable</strong></p></td>
</tr>
<tr class="row-odd"><td><p>large tokens</p></td>
<td><p><strong>Vulnerable</strong> (6)</p></td>
<td><p><strong>Vulnerable</strong> (6)</p></td>
<td><p><strong>Vulnerable</strong> (6)</p></td>
<td><p><strong>Vulnerable</strong> (6)</p></td>
<td><p><strong>Vulnerable</strong> (6)</p></td>
</tr>
</tbody>
</table>
<ol class="arabic simple">
<li><p>Expat 2.4.1 and newer is not vulnerable to the “billion laughs” and
“quadratic blowup” vulnerabilities. Items still listed as vulnerable due to
potential reliance on system-provided libraries. Check
<code class="xref py py-const docutils literal notranslate"><span class="pre">pyexpat.EXPAT_VERSION</span></code>.</p></li>
<li><p><a class="reference internal" href="xml.etree.elementtree.html#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> doesn’t expand external entities and raises a
<a class="reference internal" href="xml.etree.elementtree.html#xml.etree.ElementTree.ParseError" title="xml.etree.ElementTree.ParseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ParseError</span></code></a> when an entity occurs.</p></li>
<li><p><a class="reference internal" href="xml.dom.minidom.html#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> doesn’t expand external entities and simply returns
the unexpanded entity verbatim.</p></li>
<li><p><a class="reference internal" href="xmlrpc.client.html#module-xmlrpc.client" title="xmlrpc.client: XML-RPC client access."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code></a> doesn’t expand external entities and omits them.</p></li>
<li><p>Since Python 3.7.1, external general entities are no longer processed by
default.</p></li>
<li><p>Expat 2.6.0 and newer is not vulnerable to denial of service
through quadratic runtime caused by parsing large tokens.
Items still listed as vulnerable due to
potential reliance on system-provided libraries. Check
<code class="xref py py-const docutils literal notranslate"><span class="pre">pyexpat.EXPAT_VERSION</span></code>.</p></li>
</ol>
<dl class="simple">
<dt>billion laughs / exponential entity expansion</dt><dd><p>The <a class="reference external" href="https://en.wikipedia.org/wiki/Billion_laughs">Billion Laughs</a> attack – also known as exponential entity expansion –
uses multiple levels of nested entities. Each entity refers to another entity
several times, and the final entity definition contains a small string.
The exponential expansion results in several gigabytes of text and
consumes lots of memory and CPU time.</p>
</dd>
<dt>quadratic blowup entity expansion</dt><dd><p>A quadratic blowup attack is similar to a <a class="reference external" href="https://en.wikipedia.org/wiki/Billion_laughs">Billion Laughs</a> attack; it abuses
entity expansion, too. Instead of nested entities it repeats one large entity
with a couple of thousand chars over and over again. The attack isn’t as
efficient as the exponential case but it avoids triggering parser countermeasures
that forbid deeply nested entities.</p>
</dd>
<dt>external entity expansion</dt><dd><p>Entity declarations can contain more than just text for replacement. They can
also point to external resources or local files. The XML
parser accesses the resource and embeds the content into the XML document.</p>
</dd>
<dt><a class="reference external" href="https://en.wikipedia.org/wiki/Document_type_definition">DTD</a> retrieval</dt><dd><p>Some XML libraries like Python’s <a class="reference internal" href="xml.dom.pulldom.html#module-xml.dom.pulldom" title="xml.dom.pulldom: Support for building partial DOM trees from SAX events."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.pulldom</span></code></a> retrieve document type
definitions from remote or local locations. The feature has similar
implications as the external entity expansion issue.</p>
</dd>
<dt>decompression bomb</dt><dd><p>Decompression bombs (aka <a class="reference external" href="https://en.wikipedia.org/wiki/Zip_bomb">ZIP bomb</a>) apply to all XML libraries
that can parse compressed XML streams such as gzipped HTTP streams or
LZMA-compressed
files. For an attacker it can reduce the amount of transmitted data by three
magnitudes or more.</p>
</dd>
<dt>large tokens</dt><dd><p>Expat needs to re-parse unfinished tokens; without the protection
introduced in Expat 2.6.0, this can lead to quadratic runtime that can
be used to cause denial of service in the application parsing XML.
The issue is known as
<a class="reference external" href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-52425">CVE-2023-52425</a>.</p>
</dd>
</dl>
<p>The documentation for <a class="reference external" href="https://pypi.org/project/defusedxml/">defusedxml</a> on PyPI has further information about
all known attack vectors with examples and references.</p>
</section>
<section id="the-defusedxml-package">
<span id="defusedxml-package"></span><h2>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">defusedxml</span></code> Package<a class="headerlink" href="#the-defusedxml-package" title="Link to this heading">¶</a></h2>
<p><a class="reference external" href="https://pypi.org/project/defusedxml/">defusedxml</a> is a pure Python package with modified subclasses of all stdlib
XML parsers that prevent any potentially malicious operation. Use of this
package is recommended for any server code that parses untrusted XML data. The
package also ships with example exploits and extended documentation on more
XML exploits such as XPath injection.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">XML Processing Modules</a><ul>
<li><a class="reference internal" href="#xml-vulnerabilities">XML vulnerabilities</a></li>
<li><a class="reference internal" href="#the-defusedxml-package">The <code class="xref py py-mod docutils literal notranslate"><span class="pre">defusedxml</span></code> Package</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="html.entities.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.entities</span></code> — Definitions of HTML general entities</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.etree.elementtree.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.etree.elementtree.html" title="xml.etree.ElementTree — The ElementTree XML API"
             >next</a> |</li>
        <li class="right" >
          <a href="html.entities.html" title="html.entities — Definitions of HTML general entities"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">XML Processing Modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>