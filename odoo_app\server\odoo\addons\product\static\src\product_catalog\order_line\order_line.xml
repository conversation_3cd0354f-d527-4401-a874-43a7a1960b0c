<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="ProductCatalogOrderLine">
        <!-- Replace the element found using the css selector by the content of the portalled
             template.  -->
        <t t-portal="`#product-${props.productId}-price`">
            <span class="o_product_catalog_price">Unit price: <t t-out="price"/></span>
        </t>
        <div 
            t-if="props.readOnly and props.warning"
            class="text-danger text-truncate my-2 pt-3 border-top">
            <i class="fa fa-warning" t-att-title="props.warning"/>
            <span 
                class="px-1" 
                t-att-title="props.warning"
                t-out="props.warning"/>
        </div>
        <span t-elif="props.readOnly" class="my-2 pt-3 border-top" t-out="props.warning">
            You can't edit this product in the catalog.
        </span>
        <div t-else="" class="d-flex justify-content-end align-items-center">
            <div t-if="isInOrder()"
                    class="input-group o_product_catalog_quantity o_product_catalog_cancel_global_click w-50">
                <button class="btn btn-primary border"
                        t-on-click.stop="this.env.decreaseQuantity"
                        t-att-disabled="disableRemove"
                        t-att-data-tooltip="disabledButtonTooltip">
                    <i class="fa fa-minus center"/>
                </button>
                <input class="o_input form-control border text-center text-bg-light"
                        type="number"
                        t-att-value="quantity"
                        t-on-change="this.env.setQuantity"
                        t-on-focus="_onFocus"
                />
                <button class="btn btn-primary border"
                        t-on-click.stop="(ev) => this.env.increaseQuantity()">
                    <i class="fa fa-plus"/>
                </button>
            </div>
            <t t-elif="props.warning">
                <i class="fa fa-warning text-warning" t-att-title="props.warning"/>
                <span 
                    class="text-truncate text-warning px-1" 
                    t-att-title="props.warning"
                    t-out="props.warning"/>
            </t>
            <div 
                class="ms-auto o_product_catalog_buttons o_product_catalog_cancel_global_click"
                style="min-width: max-content;">
                <button t-if="!isInOrder()"
                        t-on-click.stop="() => this.env.addProduct()"
                        class="btn btn-secondary">
                    <i class="fa fa-shopping-cart"/>
                    Add
                </button>
                <div t-else="" class="o_tooltip_div_remove" t-att-data-tooltip="this.disabledButtonTooltip">
                    <button t-on-click.stop="this.env.removeProduct"
                            t-att-disabled="disableRemove"
                            class="btn btn-light border">
                        <i class="fa fa-trash"/>
                        Remove
                    </button>
                </div>
            </div>
        </div>
    </t>
</templates>
