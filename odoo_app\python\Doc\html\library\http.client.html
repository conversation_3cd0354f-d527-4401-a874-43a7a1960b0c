<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="http.client — HTTP protocol client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/http.client.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/http/client.py This module defines classes that implement the client side of the HTTP and HTTPS protocols. It is normally not used directly — the module urllib.request uses it to h..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/http/client.py This module defines classes that implement the client side of the HTTP and HTTPS protocols. It is normally not used directly — the module urllib.request uses it to h..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>http.client — HTTP protocol client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="ftplib — FTP protocol client" href="ftplib.html" />
    <link rel="prev" title="http — HTTP modules" href="http.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/http.client.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a><ul>
<li><a class="reference internal" href="#httpconnection-objects">HTTPConnection Objects</a></li>
<li><a class="reference internal" href="#httpresponse-objects">HTTPResponse Objects</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#httpmessage-objects">HTTPMessage Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ftplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.client.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ftplib.html" title="ftplib — FTP protocol client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="http.html" title="http — HTTP modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-http.client">
<span id="http-client-http-protocol-client"></span><h1><a class="reference internal" href="#module-http.client" title="http.client: HTTP and HTTPS protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code></a> — HTTP protocol client<a class="headerlink" href="#module-http.client" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/http/client.py">Lib/http/client.py</a></p>
<span id="index-0"></span><hr class="docutils" id="index-1" />
<p>This module defines classes that implement the client side of the HTTP and
HTTPS protocols.  It is normally not used directly — the module
<a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> uses it to handle URLs that use HTTP and HTTPS.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The <a class="reference external" href="https://requests.readthedocs.io/en/latest/">Requests package</a>
is recommended for a higher-level HTTP client interface.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>HTTPS support is only available if Python was compiled with SSL support
(through the <a class="reference internal" href="ssl.html#module-ssl" title="ssl: TLS/SSL wrapper for socket objects"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code></a> module).</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>The module provides the following classes:</p>
<dl class="py class">
<dt class="sig sig-object py" id="http.client.HTTPConnection">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTPConnection</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=None</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">source_address=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blocksize=8192</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="#http.client.HTTPConnection" title="http.client.HTTPConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPConnection</span></code></a> instance represents one transaction with an HTTP
server.  It should be instantiated by passing it a host and optional port
number.  If no port number is passed, the port is extracted from the host
string if it has the form <code class="docutils literal notranslate"><span class="pre">host:port</span></code>, else the default HTTP port (80) is
used.  If the optional <em>timeout</em> parameter is given, blocking
operations (like connection attempts) will timeout after that many seconds
(if it is not given, the global default timeout setting is used).
The optional <em>source_address</em> parameter may be a tuple of a (host, port)
to use as the source address the HTTP connection is made from.
The optional <em>blocksize</em> parameter sets the buffer size in bytes for
sending a file-like message body.</p>
<p>For example, the following calls all create instances that connect to the server
at the same host and port:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">h1</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="s1">&#39;www.python.org&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h2</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="s1">&#39;www.python.org:80&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h3</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="s1">&#39;www.python.org&#39;</span><span class="p">,</span> <span class="mi">80</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">h4</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="s1">&#39;www.python.org&#39;</span><span class="p">,</span> <span class="mi">80</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>source_address</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The  <em>strict</em> parameter was removed. HTTP 0.9-style “Simple Responses” are
no longer supported.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>blocksize</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.client.HTTPSConnection">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTPSConnection</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">source_address=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blocksize=8192</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPSConnection" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPConnection" title="http.client.HTTPConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPConnection</span></code></a> that uses SSL for communication with
secure servers.  Default port is <code class="docutils literal notranslate"><span class="pre">443</span></code>.  If <em>context</em> is specified, it
must be a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> instance describing the various SSL
options.</p>
<p>Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for more information on best practices.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>source_address</em>, <em>context</em> and <em>check_hostname</em> were added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>This class now supports HTTPS virtual hosts if possible (that is,
if <a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a> is true).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The <em>strict</em> parameter was removed. HTTP 0.9-style “Simple Responses” are
no longer supported.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.3: </span>This class now performs all the necessary certificate and hostname checks
by default. To revert to the previous, unverified, behavior
<code class="xref py py-func docutils literal notranslate"><span class="pre">ssl._create_unverified_context()</span></code> can be passed to the <em>context</em>
parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>This class now enables TLS 1.3
<a class="reference internal" href="ssl.html#ssl.SSLContext.post_handshake_auth" title="ssl.SSLContext.post_handshake_auth"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.post_handshake_auth</span></code></a> for the default <em>context</em> or
when <em>cert_file</em> is passed with a custom <em>context</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>This class now sends an ALPN extension with protocol indicator
<code class="docutils literal notranslate"><span class="pre">http/1.1</span></code> when no <em>context</em> is given. Custom <em>context</em> should set
ALPN protocols with <a class="reference internal" href="ssl.html#ssl.SSLContext.set_alpn_protocols" title="ssl.SSLContext.set_alpn_protocols"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_alpn_protocols()</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The deprecated <em>key_file</em>, <em>cert_file</em> and <em>check_hostname</em> parameters
have been removed.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.client.HTTPResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTPResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sock</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debuglevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse" title="Link to this definition">¶</a></dt>
<dd><p>Class whose instances are returned upon successful connection.  Not
instantiated directly by user.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The <em>strict</em> parameter was removed. HTTP 0.9 style “Simple Responses” are
no longer supported.</p>
</div>
</dd></dl>

<p>This module provides the following function:</p>
<dl class="py function">
<dt class="sig sig-object py" id="http.client.parse_headers">
<span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">parse_headers</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.parse_headers" title="Link to this definition">¶</a></dt>
<dd><p>Parse the headers from a file pointer <em>fp</em> representing a HTTP
request/response. The file has to be a <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> reader
(i.e. not text) and must provide a valid <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a> style header.</p>
<p>This function returns an instance of <a class="reference internal" href="#http.client.HTTPMessage" title="http.client.HTTPMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPMessage</span></code></a>
that holds the header fields, but no payload
(the same as <a class="reference internal" href="#http.client.HTTPResponse.msg" title="http.client.HTTPResponse.msg"><code class="xref py py-attr docutils literal notranslate"><span class="pre">HTTPResponse.msg</span></code></a>
and <a class="reference internal" href="http.server.html#http.server.BaseHTTPRequestHandler.headers" title="http.server.BaseHTTPRequestHandler.headers"><code class="xref py py-attr docutils literal notranslate"><span class="pre">http.server.BaseHTTPRequestHandler.headers</span></code></a>).
After returning, the file pointer <em>fp</em> is ready to read the HTTP body.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#http.client.parse_headers" title="http.client.parse_headers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse_headers()</span></code></a> does not parse the start-line of a HTTP message;
it only parses the <code class="docutils literal notranslate"><span class="pre">Name:</span> <span class="pre">value</span></code> lines. The file has to be ready to
read these field lines, so the first line should already be consumed
before calling the function.</p>
</div>
</dd></dl>

<p>The following exceptions are raised as appropriate:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="http.client.HTTPException">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTPException</span></span><a class="headerlink" href="#http.client.HTTPException" title="Link to this definition">¶</a></dt>
<dd><p>The base class of the other exceptions in this module.  It is a subclass of
<a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.NotConnected">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">NotConnected</span></span><a class="headerlink" href="#http.client.NotConnected" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.InvalidURL">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">InvalidURL</span></span><a class="headerlink" href="#http.client.InvalidURL" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>, raised if a port is given and is either
non-numeric or empty.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.UnknownProtocol">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">UnknownProtocol</span></span><a class="headerlink" href="#http.client.UnknownProtocol" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.UnknownTransferEncoding">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">UnknownTransferEncoding</span></span><a class="headerlink" href="#http.client.UnknownTransferEncoding" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.UnimplementedFileMode">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">UnimplementedFileMode</span></span><a class="headerlink" href="#http.client.UnimplementedFileMode" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.IncompleteRead">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">IncompleteRead</span></span><a class="headerlink" href="#http.client.IncompleteRead" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.ImproperConnectionState">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">ImproperConnectionState</span></span><a class="headerlink" href="#http.client.ImproperConnectionState" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.CannotSendRequest">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">CannotSendRequest</span></span><a class="headerlink" href="#http.client.CannotSendRequest" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.ImproperConnectionState" title="http.client.ImproperConnectionState"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImproperConnectionState</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.CannotSendHeader">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">CannotSendHeader</span></span><a class="headerlink" href="#http.client.CannotSendHeader" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.ImproperConnectionState" title="http.client.ImproperConnectionState"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImproperConnectionState</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.ResponseNotReady">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">ResponseNotReady</span></span><a class="headerlink" href="#http.client.ResponseNotReady" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.ImproperConnectionState" title="http.client.ImproperConnectionState"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImproperConnectionState</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.BadStatusLine">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">BadStatusLine</span></span><a class="headerlink" href="#http.client.BadStatusLine" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.  Raised if a server responds with a HTTP
status code that we don’t understand.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.LineTooLong">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">LineTooLong</span></span><a class="headerlink" href="#http.client.LineTooLong" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#http.client.HTTPException" title="http.client.HTTPException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPException</span></code></a>.  Raised if an excessively long line
is received in the HTTP protocol from the server.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="http.client.RemoteDisconnected">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">RemoteDisconnected</span></span><a class="headerlink" href="#http.client.RemoteDisconnected" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="exceptions.html#ConnectionResetError" title="ConnectionResetError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionResetError</span></code></a> and <a class="reference internal" href="#http.client.BadStatusLine" title="http.client.BadStatusLine"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BadStatusLine</span></code></a>.  Raised
by <a class="reference internal" href="#http.client.HTTPConnection.getresponse" title="http.client.HTTPConnection.getresponse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">HTTPConnection.getresponse()</span></code></a> when the attempt to read the response
results in no data read from the connection, indicating that the remote end
has closed the connection.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>Previously, <a class="reference internal" href="#http.client.BadStatusLine" title="http.client.BadStatusLine"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BadStatusLine</span></code></a><code class="docutils literal notranslate"><span class="pre">('')</span></code> was raised.</p>
</div>
</dd></dl>

<p>The constants defined in this module are:</p>
<dl class="py data">
<dt class="sig sig-object py" id="http.client.HTTP_PORT">
<span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTP_PORT</span></span><a class="headerlink" href="#http.client.HTTP_PORT" title="Link to this definition">¶</a></dt>
<dd><p>The default port for the HTTP protocol (always <code class="docutils literal notranslate"><span class="pre">80</span></code>).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="http.client.HTTPS_PORT">
<span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTPS_PORT</span></span><a class="headerlink" href="#http.client.HTTPS_PORT" title="Link to this definition">¶</a></dt>
<dd><p>The default port for the HTTPS protocol (always <code class="docutils literal notranslate"><span class="pre">443</span></code>).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="http.client.responses">
<span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">responses</span></span><a class="headerlink" href="#http.client.responses" title="Link to this definition">¶</a></dt>
<dd><p>This dictionary maps the HTTP 1.1 status codes to the W3C names.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">http.client.responses[http.client.NOT_FOUND]</span></code> is <code class="docutils literal notranslate"><span class="pre">'Not</span> <span class="pre">Found'</span></code>.</p>
</dd></dl>

<p>See <a class="reference internal" href="http.html#http-status-codes"><span class="std std-ref">HTTP status codes</span></a> for a list of HTTP status codes that are
available in this module as constants.</p>
<section id="httpconnection-objects">
<span id="id1"></span><h2>HTTPConnection Objects<a class="headerlink" href="#httpconnection-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#http.client.HTTPConnection" title="http.client.HTTPConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPConnection</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.request">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">request</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">method</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">body</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encode_chunked</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.request" title="Link to this definition">¶</a></dt>
<dd><p>This will send a request to the server using the HTTP request
method <em>method</em> and the request URI <em>url</em>. The provided <em>url</em> must be
an absolute path to conform with <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html#section-5.1.2"><strong>RFC 2616 §5.1.2</strong></a>
(unless connecting to an HTTP proxy server or using the <code class="docutils literal notranslate"><span class="pre">OPTIONS</span></code> or
<code class="docutils literal notranslate"><span class="pre">CONNECT</span></code> methods).</p>
<p>If <em>body</em> is specified, the specified data is sent after the headers are
finished.  It may be a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>, an
open <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>, or an iterable of <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.  If <em>body</em>
is a string, it is encoded as ISO-8859-1, the default for HTTP.  If it
is a bytes-like object, the bytes are sent as is.  If it is a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file
object</span></a>, the contents of the file is sent; this file object should
support at least the <code class="docutils literal notranslate"><span class="pre">read()</span></code> method.  If the file object is an
instance of <a class="reference internal" href="io.html#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOBase</span></code></a>, the data returned by the <code class="docutils literal notranslate"><span class="pre">read()</span></code>
method will be encoded as ISO-8859-1, otherwise the data returned by
<code class="docutils literal notranslate"><span class="pre">read()</span></code> is sent as is.  If <em>body</em> is an iterable, the elements of the
iterable are sent as is until the iterable is exhausted.</p>
<p>The <em>headers</em> argument should be a mapping of extra HTTP headers to send
with the request. A <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html#section-14.23"><strong>Host header</strong></a>
must be provided to conform with <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html#section-5.1.2"><strong>RFC 2616 §5.1.2</strong></a>
(unless connecting to an HTTP proxy server or using the <code class="docutils literal notranslate"><span class="pre">OPTIONS</span></code> or
<code class="docutils literal notranslate"><span class="pre">CONNECT</span></code> methods).</p>
<p>If <em>headers</em> contains neither Content-Length nor Transfer-Encoding,
but there is a request body, one of those
header fields will be added automatically.  If
<em>body</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the Content-Length header is set to <code class="docutils literal notranslate"><span class="pre">0</span></code> for
methods that expect a body (<code class="docutils literal notranslate"><span class="pre">PUT</span></code>, <code class="docutils literal notranslate"><span class="pre">POST</span></code>, and <code class="docutils literal notranslate"><span class="pre">PATCH</span></code>).  If
<em>body</em> is a string or a bytes-like object that is not also a
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file</span></a>, the Content-Length header is
set to its length.  Any other type of <em>body</em> (files
and iterables in general) will be chunk-encoded, and the
Transfer-Encoding header will automatically be set instead of
Content-Length.</p>
<p>The <em>encode_chunked</em> argument is only relevant if Transfer-Encoding is
specified in <em>headers</em>.  If <em>encode_chunked</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the
HTTPConnection object assumes that all encoding is handled by the
calling code.  If it is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the body will be chunk-encoded.</p>
<p>For example, to perform a <code class="docutils literal notranslate"><span class="pre">GET</span></code> request to <code class="docutils literal notranslate"><span class="pre">https://docs.python.org/3/</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">http.client</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">host</span> <span class="o">=</span> <span class="s2">&quot;docs.python.org&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPSConnection</span><span class="p">(</span><span class="n">host</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;/3/&quot;</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;Host&quot;</span><span class="p">:</span> <span class="n">host</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">status</span><span class="p">,</span> <span class="n">response</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
<span class="go">200 OK</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Chunked transfer encoding has been added to the HTTP protocol
version 1.1.  Unless the HTTP server is known to handle HTTP 1.1,
the caller must either specify the Content-Length, or must pass a
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> or bytes-like object that is not also a file as the
body representation.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>body</em> can now be an iterable.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>If neither Content-Length nor Transfer-Encoding are set in
<em>headers</em>, file and iterable <em>body</em> objects are now chunk-encoded.
The <em>encode_chunked</em> argument was added.
No attempt is made to determine the Content-Length for file
objects.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.getresponse">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">getresponse</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.getresponse" title="Link to this definition">¶</a></dt>
<dd><p>Should be called after a request is sent to get the response from the server.
Returns an <a class="reference internal" href="#http.client.HTTPResponse" title="http.client.HTTPResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPResponse</span></code></a> instance.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Note that you must have read the whole response before you can send a new
request to the server.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If a <a class="reference internal" href="exceptions.html#ConnectionError" title="ConnectionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ConnectionError</span></code></a> or subclass is raised, the
<a class="reference internal" href="#http.client.HTTPConnection" title="http.client.HTTPConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPConnection</span></code></a> object will be ready to reconnect when
a new request is sent.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.set_debuglevel">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">set_debuglevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.set_debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>Set the debugging level.  The default debug level is <code class="docutils literal notranslate"><span class="pre">0</span></code>, meaning no
debugging output is printed.  Any value greater than <code class="docutils literal notranslate"><span class="pre">0</span></code> will cause all
currently defined debug output to be printed to stdout.  The <code class="docutils literal notranslate"><span class="pre">debuglevel</span></code>
is passed to any new <a class="reference internal" href="#http.client.HTTPResponse" title="http.client.HTTPResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPResponse</span></code></a> objects that are created.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.set_tunnel">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">set_tunnel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.set_tunnel" title="Link to this definition">¶</a></dt>
<dd><p>Set the host and the port for HTTP Connect Tunnelling. This allows running
the connection through a proxy server.</p>
<p>The <em>host</em> and <em>port</em> arguments specify the endpoint of the tunneled connection
(i.e. the address included in the CONNECT request, <em>not</em> the address of the
proxy server).</p>
<p>The <em>headers</em> argument should be a mapping of extra HTTP headers to send with
the CONNECT request.</p>
<p>As HTTP/1.1 is used for HTTP CONNECT tunnelling request, <a class="reference external" href="https://datatracker.ietf.org/doc/html/rfc7231#section-4.3.6">as per the RFC</a>, a HTTP <code class="docutils literal notranslate"><span class="pre">Host:</span></code>
header must be provided, matching the authority-form of the request target
provided as the destination for the CONNECT request. If a HTTP <code class="docutils literal notranslate"><span class="pre">Host:</span></code>
header is not provided via the headers argument, one is generated and
transmitted automatically.</p>
<p>For example, to tunnel through a HTTPS proxy server running locally on port
8080, we would pass the address of the proxy to the <a class="reference internal" href="#http.client.HTTPSConnection" title="http.client.HTTPSConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPSConnection</span></code></a>
constructor, and the address of the host that we eventually want to reach to
the <a class="reference internal" href="#http.client.HTTPConnection.set_tunnel" title="http.client.HTTPConnection.set_tunnel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_tunnel()</span></code></a> method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">http.client</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPSConnection</span><span class="p">(</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8080</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">set_tunnel</span><span class="p">(</span><span class="s2">&quot;www.python.org&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;HEAD&quot;</span><span class="p">,</span><span class="s2">&quot;/index.html&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>HTTP CONNECT tunnelling requests use protocol HTTP/1.1, upgraded from
protocol HTTP/1.0. <code class="docutils literal notranslate"><span class="pre">Host:</span></code> HTTP headers are mandatory for HTTP/1.1, so
one will be automatically generated and transmitted if not provided in
the headers argument.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.get_proxy_response_headers">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">get_proxy_response_headers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.get_proxy_response_headers" title="Link to this definition">¶</a></dt>
<dd><p>Returns a dictionary with the headers of the response received from
the proxy server to the CONNECT request.</p>
<p>If the CONNECT request was not sent, the method returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.connect">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.connect" title="Link to this definition">¶</a></dt>
<dd><p>Connect to the server specified when the object was created.  By default,
this is called automatically when making a request if the client does not
already have a connection.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">http.client.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.close">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the connection to the server.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPConnection.blocksize">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">blocksize</span></span><a class="headerlink" href="#http.client.HTTPConnection.blocksize" title="Link to this definition">¶</a></dt>
<dd><p>Buffer size in bytes for sending a file-like message body.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<p>As an alternative to using the <a class="reference internal" href="#http.client.HTTPConnection.request" title="http.client.HTTPConnection.request"><code class="xref py py-meth docutils literal notranslate"><span class="pre">request()</span></code></a> method described above, you can
also send your request step by step, by using the four functions below.</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.putrequest">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">putrequest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">method</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip_host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip_accept_encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.putrequest" title="Link to this definition">¶</a></dt>
<dd><p>This should be the first call after the connection to the server has been
made. It sends a line to the server consisting of the <em>method</em> string,
the <em>url</em> string, and the HTTP version (<code class="docutils literal notranslate"><span class="pre">HTTP/1.1</span></code>).  To disable automatic
sending of <code class="docutils literal notranslate"><span class="pre">Host:</span></code> or <code class="docutils literal notranslate"><span class="pre">Accept-Encoding:</span></code> headers (for example to accept
additional content encodings), specify <em>skip_host</em> or <em>skip_accept_encoding</em>
with non-False values.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.putheader">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">putheader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">argument</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.putheader" title="Link to this definition">¶</a></dt>
<dd><p>Send an <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a>-style header to the server.  It sends a line to the server
consisting of the header, a colon and a space, and the first argument.  If more
arguments are given, continuation lines are sent, each consisting of a tab and
an argument.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.endheaders">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">endheaders</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_body</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encode_chunked</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.endheaders" title="Link to this definition">¶</a></dt>
<dd><p>Send a blank line to the server, signalling the end of the headers. The
optional <em>message_body</em> argument can be used to pass a message body
associated with the request.</p>
<p>If <em>encode_chunked</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the result of each iteration of
<em>message_body</em> will be chunk-encoded as specified in <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7230.html"><strong>RFC 7230</strong></a>,
Section 3.3.1.  How the data is encoded is dependent on the type of
<em>message_body</em>.  If <em>message_body</em> implements the <a class="reference internal" href="../c-api/buffer.html#bufferobjects"><span class="std std-ref">buffer interface</span></a> the encoding will result in a single chunk.
If <em>message_body</em> is a <a class="reference internal" href="collections.abc.html#collections.abc.Iterable" title="collections.abc.Iterable"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Iterable</span></code></a>, each iteration
of <em>message_body</em> will result in a chunk.  If <em>message_body</em> is a
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>, each call to <code class="docutils literal notranslate"><span class="pre">.read()</span></code> will result in a chunk.
The method automatically signals the end of the chunk-encoded data
immediately after <em>message_body</em>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Due to the chunked encoding specification, empty chunks
yielded by an iterator body will be ignored by the chunk-encoder.
This is to avoid premature termination of the read of the request by
the target server due to malformed encoding.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added chunked encoding support and the <em>encode_chunked</em> parameter.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPConnection.send">
<span class="sig-prename descclassname"><span class="pre">HTTPConnection.</span></span><span class="sig-name descname"><span class="pre">send</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPConnection.send" title="Link to this definition">¶</a></dt>
<dd><p>Send data to the server.  This should be used directly only after the
<a class="reference internal" href="#http.client.HTTPConnection.endheaders" title="http.client.HTTPConnection.endheaders"><code class="xref py py-meth docutils literal notranslate"><span class="pre">endheaders()</span></code></a> method has been called and before <a class="reference internal" href="#http.client.HTTPConnection.getresponse" title="http.client.HTTPConnection.getresponse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getresponse()</span></code></a> is
called.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">http.client.send</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">data</span></code>.</p>
</dd></dl>

</section>
<section id="httpresponse-objects">
<span id="id2"></span><h2>HTTPResponse Objects<a class="headerlink" href="#httpresponse-objects" title="Link to this heading">¶</a></h2>
<p>An <a class="reference internal" href="#http.client.HTTPResponse" title="http.client.HTTPResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPResponse</span></code></a> instance wraps the HTTP response from the
server.  It provides access to the request headers and the entity
body.  The response is an iterable object and can be used in a with
statement.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedIOBase</span></code></a> interface is now implemented and
all of its reader operations are supported.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.read">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">amt</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.read" title="Link to this definition">¶</a></dt>
<dd><p>Reads and returns the response body, or up to the next <em>amt</em> bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.readinto">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">readinto</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.readinto" title="Link to this definition">¶</a></dt>
<dd><p>Reads up to the next len(b) bytes of the response body into the buffer <em>b</em>.
Returns the number of bytes read.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.getheader">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">getheader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.getheader" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of the header <em>name</em>, or <em>default</em> if there is no header
matching <em>name</em>.  If there is more than one  header with the name <em>name</em>,
return all of the values joined by ‘, ‘.  If <em>default</em> is any iterable other
than a single string, its elements are similarly returned joined by commas.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.getheaders">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">getheaders</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.getheaders" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of (header, value) tuples.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.fileno">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Return the <code class="docutils literal notranslate"><span class="pre">fileno</span></code> of the underlying socket.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.msg">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">msg</span></span><a class="headerlink" href="#http.client.HTTPResponse.msg" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#http.client.HTTPMessage" title="http.client.HTTPMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPMessage</span></code></a> instance containing the response
headers.  <a class="reference internal" href="#http.client.HTTPMessage" title="http.client.HTTPMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPMessage</span></code></a> is a subclass of
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.version">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#http.client.HTTPResponse.version" title="Link to this definition">¶</a></dt>
<dd><p>HTTP protocol version used by server.  10 for HTTP/1.0, 11 for HTTP/1.1.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.url">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">url</span></span><a class="headerlink" href="#http.client.HTTPResponse.url" title="Link to this definition">¶</a></dt>
<dd><p>URL of the resource retrieved, commonly used to determine if a redirect was followed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.headers">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">headers</span></span><a class="headerlink" href="#http.client.HTTPResponse.headers" title="Link to this definition">¶</a></dt>
<dd><p>Headers of the response in the form of an <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.EmailMessage</span></code></a> instance.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.status">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">status</span></span><a class="headerlink" href="#http.client.HTTPResponse.status" title="Link to this definition">¶</a></dt>
<dd><p>Status code returned by server.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.reason">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">reason</span></span><a class="headerlink" href="#http.client.HTTPResponse.reason" title="Link to this definition">¶</a></dt>
<dd><p>Reason phrase returned by server.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.debuglevel">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">debuglevel</span></span><a class="headerlink" href="#http.client.HTTPResponse.debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>A debugging hook.  If <a class="reference internal" href="#http.client.HTTPResponse.debuglevel" title="http.client.HTTPResponse.debuglevel"><code class="xref py py-attr docutils literal notranslate"><span class="pre">debuglevel</span></code></a> is greater than zero, messages
will be printed to stdout as the response is read and parsed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.client.HTTPResponse.closed">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">closed</span></span><a class="headerlink" href="#http.client.HTTPResponse.closed" title="Link to this definition">¶</a></dt>
<dd><p>Is <code class="docutils literal notranslate"><span class="pre">True</span></code> if the stream is closed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.geturl">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">geturl</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.geturl" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#http.client.HTTPResponse.url" title="http.client.HTTPResponse.url"><code class="xref py py-attr docutils literal notranslate"><span class="pre">url</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.info">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.info" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#http.client.HTTPResponse.headers" title="http.client.HTTPResponse.headers"><code class="xref py py-attr docutils literal notranslate"><span class="pre">headers</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.client.HTTPResponse.getcode">
<span class="sig-prename descclassname"><span class="pre">HTTPResponse.</span></span><span class="sig-name descname"><span class="pre">getcode</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPResponse.getcode" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#http.client.HTTPResponse.status" title="http.client.HTTPResponse.status"><code class="xref py py-attr docutils literal notranslate"><span class="pre">status</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>Here is an example session that uses the <code class="docutils literal notranslate"><span class="pre">GET</span></code> method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">http.client</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPSConnection</span><span class="p">(</span><span class="s2">&quot;www.python.org&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">r1</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">r1</span><span class="o">.</span><span class="n">status</span><span class="p">,</span> <span class="n">r1</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
<span class="go">200 OK</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data1</span> <span class="o">=</span> <span class="n">r1</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>  <span class="c1"># This will return entire content.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># The following example demonstrates reading data in chunks.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">r1</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">while</span> <span class="n">chunk</span> <span class="o">:=</span> <span class="n">r1</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">200</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">chunk</span><span class="p">))</span>
<span class="go">b&#39;&lt;!doctype html&gt;\n&lt;!--[if&quot;...</span>
<span class="go">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Example of an invalid request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPSConnection</span><span class="p">(</span><span class="s2">&quot;docs.python.org&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;/parrot.spam&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">r2</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">r2</span><span class="o">.</span><span class="n">status</span><span class="p">,</span> <span class="n">r2</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
<span class="go">404 Not Found</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data2</span> <span class="o">=</span> <span class="n">r2</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>Here is an example session that uses the <code class="docutils literal notranslate"><span class="pre">HEAD</span></code> method.  Note that the
<code class="docutils literal notranslate"><span class="pre">HEAD</span></code> method never returns any data.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">http.client</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPSConnection</span><span class="p">(</span><span class="s2">&quot;www.python.org&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;HEAD&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">res</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">status</span><span class="p">,</span> <span class="n">res</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
<span class="go">200 OK</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data</span> <span class="o">=</span> <span class="n">res</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">))</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data</span> <span class="o">==</span> <span class="sa">b</span><span class="s1">&#39;&#39;</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Here is an example session that uses the <code class="docutils literal notranslate"><span class="pre">POST</span></code> method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">http.client</span><span class="o">,</span> <span class="nn">urllib.parse</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">params</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlencode</span><span class="p">({</span><span class="s1">&#39;@number&#39;</span><span class="p">:</span> <span class="mi">12524</span><span class="p">,</span> <span class="s1">&#39;@type&#39;</span><span class="p">:</span> <span class="s1">&#39;issue&#39;</span><span class="p">,</span> <span class="s1">&#39;@action&#39;</span><span class="p">:</span> <span class="s1">&#39;show&#39;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">headers</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Content-type&quot;</span><span class="p">:</span> <span class="s2">&quot;application/x-www-form-urlencoded&quot;</span><span class="p">,</span>
<span class="gp">... </span>           <span class="s2">&quot;Accept&quot;</span><span class="p">:</span> <span class="s2">&quot;text/plain&quot;</span><span class="p">}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="s2">&quot;bugs.python.org&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;POST&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">params</span><span class="p">,</span> <span class="n">headers</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">status</span><span class="p">,</span> <span class="n">response</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
<span class="go">302 Found</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data</span>
<span class="go">b&#39;Redirecting to &lt;a href=&quot;https://bugs.python.org/issue12524&quot;&gt;https://bugs.python.org/issue12524&lt;/a&gt;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>Client side HTTP <code class="docutils literal notranslate"><span class="pre">PUT</span></code> requests are very similar to <code class="docutils literal notranslate"><span class="pre">POST</span></code> requests. The
difference lies only on the server side where HTTP servers will allow resources to
be created via <code class="docutils literal notranslate"><span class="pre">PUT</span></code> requests. It should be noted that custom HTTP methods
are also handled in <a class="reference internal" href="urllib.request.html#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">urllib.request.Request</span></code></a> by setting the appropriate
method attribute. Here is an example session that uses the <code class="docutils literal notranslate"><span class="pre">PUT</span></code> method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># This creates an HTTP request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># with the content of BODY as the enclosed representation</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># for the resource http://localhost:8080/file</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">http.client</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">BODY</span> <span class="o">=</span> <span class="s2">&quot;***filecontents***&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">HTTPConnection</span><span class="p">(</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8080</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">conn</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="s2">&quot;PUT&quot;</span><span class="p">,</span> <span class="s2">&quot;/file&quot;</span><span class="p">,</span> <span class="n">BODY</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">getresponse</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">status</span><span class="p">,</span> <span class="n">response</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
<span class="go">200, OK</span>
</pre></div>
</div>
</section>
<section id="httpmessage-objects">
<span id="id3"></span><h2>HTTPMessage Objects<a class="headerlink" href="#httpmessage-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="http.client.HTTPMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.client.</span></span><span class="sig-name descname"><span class="pre">HTTPMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">email.message.Message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.client.HTTPMessage" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>An <a class="reference internal" href="#http.client.HTTPMessage" title="http.client.HTTPMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPMessage</span></code></a> instance holds the headers from an HTTP
response.  It is implemented using the <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> class.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a><ul>
<li><a class="reference internal" href="#httpconnection-objects">HTTPConnection Objects</a></li>
<li><a class="reference internal" href="#httpresponse-objects">HTTPResponse Objects</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#httpmessage-objects">HTTPMessage Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http</span></code> — HTTP modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ftplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.client.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ftplib.html" title="ftplib — FTP protocol client"
             >next</a> |</li>
        <li class="right" >
          <a href="http.html" title="http — HTTP modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.client</span></code> — HTTP protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>