<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="string — Common string operations" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/string.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/string.py String constants: The constants defined in this module are: Custom String Formatting: The built-in string class provides the ability to do complex variable substitutions ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/string.py String constants: The constants defined in this module are: Custom String Formatting: The built-in string class provides the ability to do complex variable substitutions ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>string — Common string operations &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="re — Regular expression operations" href="re.html" />
    <link rel="prev" title="Text Processing Services" href="text.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/string.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code> — Common string operations</a><ul>
<li><a class="reference internal" href="#string-constants">String constants</a></li>
<li><a class="reference internal" href="#custom-string-formatting">Custom String Formatting</a></li>
<li><a class="reference internal" href="#format-string-syntax">Format String Syntax</a><ul>
<li><a class="reference internal" href="#format-specification-mini-language">Format Specification Mini-Language</a></li>
<li><a class="reference internal" href="#format-examples">Format examples</a></li>
</ul>
</li>
<li><a class="reference internal" href="#template-strings">Template strings</a></li>
<li><a class="reference internal" href="#helper-functions">Helper functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="text.html"
                          title="previous chapter">Text Processing Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="re.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code> — Regular expression operations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/string.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="re.html" title="re — Regular expression operations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="text.html" title="Text Processing Services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" accesskey="U">Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code> — Common string operations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-string">
<span id="string-common-string-operations"></span><h1><a class="reference internal" href="#module-string" title="string: Common string operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code></a> — Common string operations<a class="headerlink" href="#module-string" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/string.py">Lib/string.py</a></p>
<hr class="docutils" />
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="stdtypes.html#textseq"><span class="std std-ref">Text Sequence Type — str</span></a></p>
<p><a class="reference internal" href="stdtypes.html#string-methods"><span class="std std-ref">String Methods</span></a></p>
</div>
<section id="string-constants">
<h2>String constants<a class="headerlink" href="#string-constants" title="Link to this heading">¶</a></h2>
<p>The constants defined in this module are:</p>
<dl class="py data">
<dt class="sig sig-object py" id="string.ascii_letters">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">ascii_letters</span></span><a class="headerlink" href="#string.ascii_letters" title="Link to this definition">¶</a></dt>
<dd><p>The concatenation of the <a class="reference internal" href="#string.ascii_lowercase" title="string.ascii_lowercase"><code class="xref py py-const docutils literal notranslate"><span class="pre">ascii_lowercase</span></code></a> and <a class="reference internal" href="#string.ascii_uppercase" title="string.ascii_uppercase"><code class="xref py py-const docutils literal notranslate"><span class="pre">ascii_uppercase</span></code></a>
constants described below.  This value is not locale-dependent.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.ascii_lowercase">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">ascii_lowercase</span></span><a class="headerlink" href="#string.ascii_lowercase" title="Link to this definition">¶</a></dt>
<dd><p>The lowercase letters <code class="docutils literal notranslate"><span class="pre">'abcdefghijklmnopqrstuvwxyz'</span></code>.  This value is not
locale-dependent and will not change.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.ascii_uppercase">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">ascii_uppercase</span></span><a class="headerlink" href="#string.ascii_uppercase" title="Link to this definition">¶</a></dt>
<dd><p>The uppercase letters <code class="docutils literal notranslate"><span class="pre">'ABCDEFGHIJKLMNOPQRSTUVWXYZ'</span></code>.  This value is not
locale-dependent and will not change.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.digits">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">digits</span></span><a class="headerlink" href="#string.digits" title="Link to this definition">¶</a></dt>
<dd><p>The string <code class="docutils literal notranslate"><span class="pre">'0123456789'</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.hexdigits">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">hexdigits</span></span><a class="headerlink" href="#string.hexdigits" title="Link to this definition">¶</a></dt>
<dd><p>The string <code class="docutils literal notranslate"><span class="pre">'0123456789abcdefABCDEF'</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.octdigits">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">octdigits</span></span><a class="headerlink" href="#string.octdigits" title="Link to this definition">¶</a></dt>
<dd><p>The string <code class="docutils literal notranslate"><span class="pre">'01234567'</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.punctuation">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">punctuation</span></span><a class="headerlink" href="#string.punctuation" title="Link to this definition">¶</a></dt>
<dd><p>String of ASCII characters which are considered punctuation characters
in the <code class="docutils literal notranslate"><span class="pre">C</span></code> locale: <code class="docutils literal notranslate"><span class="pre">!&quot;#$%&amp;'()*+,-./:;&lt;=&gt;?&#64;[\]^_`{|}~</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.printable">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">printable</span></span><a class="headerlink" href="#string.printable" title="Link to this definition">¶</a></dt>
<dd><p>String of ASCII characters which are considered printable.  This is a
combination of <a class="reference internal" href="#string.digits" title="string.digits"><code class="xref py py-const docutils literal notranslate"><span class="pre">digits</span></code></a>, <a class="reference internal" href="#string.ascii_letters" title="string.ascii_letters"><code class="xref py py-const docutils literal notranslate"><span class="pre">ascii_letters</span></code></a>, <a class="reference internal" href="#string.punctuation" title="string.punctuation"><code class="xref py py-const docutils literal notranslate"><span class="pre">punctuation</span></code></a>,
and <a class="reference internal" href="#string.whitespace" title="string.whitespace"><code class="xref py py-const docutils literal notranslate"><span class="pre">whitespace</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="string.whitespace">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">whitespace</span></span><a class="headerlink" href="#string.whitespace" title="Link to this definition">¶</a></dt>
<dd><p>A string containing all ASCII characters that are considered whitespace.
This includes the characters space, tab, linefeed, return, formfeed, and
vertical tab.</p>
</dd></dl>

</section>
<section id="custom-string-formatting">
<span id="string-formatting"></span><h2>Custom String Formatting<a class="headerlink" href="#custom-string-formatting" title="Link to this heading">¶</a></h2>
<p>The built-in string class provides the ability to do complex variable
substitutions and value formatting via the <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> method described in
<span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3101/"><strong>PEP 3101</strong></a>.  The <a class="reference internal" href="#string.Formatter" title="string.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> class in the <a class="reference internal" href="#module-string" title="string: Common string operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code></a> module allows
you to create and customize your own string formatting behaviors using the same
implementation as the built-in <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> method.</p>
<dl class="py class">
<dt class="sig sig-object py" id="string.Formatter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">Formatter</span></span><a class="headerlink" href="#string.Formatter" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#string.Formatter" title="string.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> class has the following public methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format_string</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.format" title="Link to this definition">¶</a></dt>
<dd><p>The primary API method.  It takes a format string and
an arbitrary set of positional and keyword arguments.
It is just a wrapper that calls <a class="reference internal" href="#string.Formatter.vformat" title="string.Formatter.vformat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">vformat()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>A format string argument is now <a class="reference internal" href="../glossary.html#positional-only-parameter"><span class="std std-ref">positional-only</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.vformat">
<span class="sig-name descname"><span class="pre">vformat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format_string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.vformat" title="Link to this definition">¶</a></dt>
<dd><p>This function does the actual work of formatting.  It is exposed as a
separate function for cases where you want to pass in a predefined
dictionary of arguments, rather than unpacking and repacking the
dictionary as individual arguments using the <code class="docutils literal notranslate"><span class="pre">*args</span></code> and <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code>
syntax.  <a class="reference internal" href="#string.Formatter.vformat" title="string.Formatter.vformat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">vformat()</span></code></a> does the work of breaking up the format string
into character data and replacement fields.  It calls the various
methods described below.</p>
</dd></dl>

<p>In addition, the <a class="reference internal" href="#string.Formatter" title="string.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> defines a number of methods that are
intended to be replaced by subclasses:</p>
<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.parse">
<span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format_string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.parse" title="Link to this definition">¶</a></dt>
<dd><p>Loop over the format_string and return an iterable of tuples
(<em>literal_text</em>, <em>field_name</em>, <em>format_spec</em>, <em>conversion</em>).  This is used
by <a class="reference internal" href="#string.Formatter.vformat" title="string.Formatter.vformat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">vformat()</span></code></a> to break the string into either literal text, or
replacement fields.</p>
<p>The values in the tuple conceptually represent a span of literal text
followed by a single replacement field.  If there is no literal text
(which can happen if two replacement fields occur consecutively), then
<em>literal_text</em> will be a zero-length string.  If there is no replacement
field, then the values of <em>field_name</em>, <em>format_spec</em> and <em>conversion</em>
will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.get_field">
<span class="sig-name descname"><span class="pre">get_field</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">field_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.get_field" title="Link to this definition">¶</a></dt>
<dd><p>Given <em>field_name</em> as returned by <a class="reference internal" href="#string.Formatter.parse" title="string.Formatter.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> (see above), convert it to
an object to be formatted.  Returns a tuple (obj, used_key).  The default
version takes strings of the form defined in <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-3101/"><strong>PEP 3101</strong></a>, such as
“0[name]” or “label.title”.  <em>args</em> and <em>kwargs</em> are as passed in to
<a class="reference internal" href="#string.Formatter.vformat" title="string.Formatter.vformat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">vformat()</span></code></a>.  The return value <em>used_key</em> has the same meaning as the
<em>key</em> parameter to <a class="reference internal" href="#string.Formatter.get_value" title="string.Formatter.get_value"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_value()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.get_value">
<span class="sig-name descname"><span class="pre">get_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.get_value" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve a given field value.  The <em>key</em> argument will be either an
integer or a string.  If it is an integer, it represents the index of the
positional argument in <em>args</em>; if it is a string, then it represents a
named argument in <em>kwargs</em>.</p>
<p>The <em>args</em> parameter is set to the list of positional arguments to
<a class="reference internal" href="#string.Formatter.vformat" title="string.Formatter.vformat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">vformat()</span></code></a>, and the <em>kwargs</em> parameter is set to the dictionary of
keyword arguments.</p>
<p>For compound field names, these functions are only called for the first
component of the field name; subsequent components are handled through
normal attribute and indexing operations.</p>
<p>So for example, the field expression ‘0.name’ would cause
<a class="reference internal" href="#string.Formatter.get_value" title="string.Formatter.get_value"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_value()</span></code></a> to be called with a <em>key</em> argument of 0.  The <code class="docutils literal notranslate"><span class="pre">name</span></code>
attribute will be looked up after <a class="reference internal" href="#string.Formatter.get_value" title="string.Formatter.get_value"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_value()</span></code></a> returns by calling the
built-in <a class="reference internal" href="functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a> function.</p>
<p>If the index or keyword refers to an item that does not exist, then an
<a class="reference internal" href="exceptions.html#IndexError" title="IndexError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexError</span></code></a> or <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> should be raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.check_unused_args">
<span class="sig-name descname"><span class="pre">check_unused_args</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">used_args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.check_unused_args" title="Link to this definition">¶</a></dt>
<dd><p>Implement checking for unused arguments if desired.  The arguments to this
function is the set of all argument keys that were actually referred to in
the format string (integers for positional arguments, and strings for
named arguments), and a reference to the <em>args</em> and <em>kwargs</em> that was
passed to vformat.  The set of unused args can be calculated from these
parameters.  <a class="reference internal" href="#string.Formatter.check_unused_args" title="string.Formatter.check_unused_args"><code class="xref py py-meth docutils literal notranslate"><span class="pre">check_unused_args()</span></code></a> is assumed to raise an exception if
the check fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.format_field">
<span class="sig-name descname"><span class="pre">format_field</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format_spec</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.format_field" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="#string.Formatter.format_field" title="string.Formatter.format_field"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format_field()</span></code></a> simply calls the global <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> built-in.  The
method is provided so that subclasses can override it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Formatter.convert_field">
<span class="sig-name descname"><span class="pre">convert_field</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">conversion</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Formatter.convert_field" title="Link to this definition">¶</a></dt>
<dd><p>Converts the value (returned by <a class="reference internal" href="#string.Formatter.get_field" title="string.Formatter.get_field"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_field()</span></code></a>) given a conversion type
(as in the tuple returned by the <a class="reference internal" href="#string.Formatter.parse" title="string.Formatter.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> method).  The default
version understands ‘s’ (str), ‘r’ (repr) and ‘a’ (ascii) conversion
types.</p>
</dd></dl>

</dd></dl>

</section>
<section id="format-string-syntax">
<span id="formatstrings"></span><h2>Format String Syntax<a class="headerlink" href="#format-string-syntax" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a> method and the <a class="reference internal" href="#string.Formatter" title="string.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> class share the same
syntax for format strings (although in the case of <a class="reference internal" href="#string.Formatter" title="string.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a>,
subclasses can define their own format string syntax).  The syntax is
related to that of <a class="reference internal" href="../reference/lexical_analysis.html#f-strings"><span class="std std-ref">formatted string literals</span></a>, but it is
less sophisticated and, in particular, does not support arbitrary expressions.</p>
<p id="index-2">Format strings contain “replacement fields” surrounded by curly braces <code class="docutils literal notranslate"><span class="pre">{}</span></code>.
Anything that is not contained in braces is considered literal text, which is
copied unchanged to the output.  If you need to include a brace character in the
literal text, it can be escaped by doubling: <code class="docutils literal notranslate"><span class="pre">{{</span></code> and <code class="docutils literal notranslate"><span class="pre">}}</span></code>.</p>
<p>The grammar for a replacement field is as follows:</p>
<pre>
<strong id="grammar-token-format-string-replacement_field">replacement_field</strong> ::=  &quot;{&quot; [<a class="reference internal" href="#grammar-token-format-string-field_name"><code class="xref docutils literal notranslate"><span class="pre">field_name</span></code></a>] [&quot;!&quot; <a class="reference internal" href="#grammar-token-format-string-conversion"><code class="xref docutils literal notranslate"><span class="pre">conversion</span></code></a>] [&quot;:&quot; <a class="reference internal" href="#grammar-token-format-string-format_spec"><code class="xref docutils literal notranslate"><span class="pre">format_spec</span></code></a>] &quot;}&quot;
<strong id="grammar-token-format-string-field_name">field_name       </strong> ::=  <a class="reference internal" href="#grammar-token-format-string-arg_name"><code class="xref docutils literal notranslate"><span class="pre">arg_name</span></code></a> (&quot;.&quot; <a class="reference internal" href="#grammar-token-format-string-attribute_name"><code class="xref docutils literal notranslate"><span class="pre">attribute_name</span></code></a> | &quot;[&quot; <a class="reference internal" href="#grammar-token-format-string-element_index"><code class="xref docutils literal notranslate"><span class="pre">element_index</span></code></a> &quot;]&quot;)*
<strong id="grammar-token-format-string-arg_name">arg_name         </strong> ::=  [<a class="reference internal" href="../reference/lexical_analysis.html#grammar-token-python-grammar-identifier"><code class="xref docutils literal notranslate"><span class="pre">identifier</span></code></a> | <a class="reference internal" href="../reference/lexical_analysis.html#grammar-token-python-grammar-digit"><code class="xref docutils literal notranslate"><span class="pre">digit</span></code></a>+]
<strong id="grammar-token-format-string-attribute_name">attribute_name   </strong> ::=  <a class="reference internal" href="../reference/lexical_analysis.html#grammar-token-python-grammar-identifier"><code class="xref docutils literal notranslate"><span class="pre">identifier</span></code></a>
<strong id="grammar-token-format-string-element_index">element_index    </strong> ::=  <a class="reference internal" href="../reference/lexical_analysis.html#grammar-token-python-grammar-digit"><code class="xref docutils literal notranslate"><span class="pre">digit</span></code></a>+ | <a class="reference internal" href="#grammar-token-format-string-index_string"><code class="xref docutils literal notranslate"><span class="pre">index_string</span></code></a>
<strong id="grammar-token-format-string-index_string">index_string     </strong> ::=  &lt;any source character except &quot;]&quot;&gt; +
<strong id="grammar-token-format-string-conversion">conversion       </strong> ::=  &quot;r&quot; | &quot;s&quot; | &quot;a&quot;
<strong id="grammar-token-format-string-format_spec">format_spec      </strong> ::=  <a class="reference internal" href="#grammar-token-format-spec-format_spec"><code class="xref docutils literal notranslate"><span class="pre">format-spec:format_spec</span></code></a>
</pre>
<p>In less formal terms, the replacement field can start with a <em>field_name</em> that specifies
the object whose value is to be formatted and inserted
into the output instead of the replacement field.
The <em>field_name</em> is optionally followed by a  <em>conversion</em> field, which is
preceded by an exclamation point <code class="docutils literal notranslate"><span class="pre">'!'</span></code>, and a <em>format_spec</em>, which is preceded
by a colon <code class="docutils literal notranslate"><span class="pre">':'</span></code>.  These specify a non-default format for the replacement value.</p>
<p>See also the <a class="reference internal" href="#formatspec"><span class="std std-ref">Format Specification Mini-Language</span></a> section.</p>
<p>The <em>field_name</em> itself begins with an <em>arg_name</em> that is either a number or a
keyword.  If it’s a number, it refers to a positional argument, and if it’s a keyword,
it refers to a named keyword argument. An <em>arg_name</em> is treated as a number if
a call to <a class="reference internal" href="stdtypes.html#str.isdecimal" title="str.isdecimal"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.isdecimal()</span></code></a> on the string would return true.
If the numerical arg_names in a format string
are 0, 1, 2, … in sequence, they can all be omitted (not just some)
and the numbers 0, 1, 2, … will be automatically inserted in that order.
Because <em>arg_name</em> is not quote-delimited, it is not possible to specify arbitrary
dictionary keys (e.g., the strings <code class="docutils literal notranslate"><span class="pre">'10'</span></code> or <code class="docutils literal notranslate"><span class="pre">':-]'</span></code>) within a format string.
The <em>arg_name</em> can be followed by any number of index or
attribute expressions. An expression of the form <code class="docutils literal notranslate"><span class="pre">'.name'</span></code> selects the named
attribute using <a class="reference internal" href="functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a>, while an expression of the form <code class="docutils literal notranslate"><span class="pre">'[index]'</span></code>
does an index lookup using <a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>The positional argument specifiers can be omitted for <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a>,
so <code class="docutils literal notranslate"><span class="pre">'{}</span> <span class="pre">{}'.format(a,</span> <span class="pre">b)</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">'{0}</span> <span class="pre">{1}'.format(a,</span> <span class="pre">b)</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The positional argument specifiers can be omitted for <a class="reference internal" href="#string.Formatter" title="string.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a>.</p>
</div>
<p>Some simple format string examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="s2">&quot;First, thou shalt count to </span><span class="si">{0}</span><span class="s2">&quot;</span>  <span class="c1"># References first positional argument</span>
<span class="s2">&quot;Bring me a </span><span class="si">{}</span><span class="s2">&quot;</span>                   <span class="c1"># Implicitly references the first positional argument</span>
<span class="s2">&quot;From </span><span class="si">{}</span><span class="s2"> to </span><span class="si">{}</span><span class="s2">&quot;</span>                   <span class="c1"># Same as &quot;From {0} to {1}&quot;</span>
<span class="s2">&quot;My quest is </span><span class="si">{name}</span><span class="s2">&quot;</span>              <span class="c1"># References keyword argument &#39;name&#39;</span>
<span class="s2">&quot;Weight in tons </span><span class="si">{0.weight}</span><span class="s2">&quot;</span>       <span class="c1"># &#39;weight&#39; attribute of first positional arg</span>
<span class="s2">&quot;Units destroyed: </span><span class="si">{players[0]}</span><span class="s2">&quot;</span>   <span class="c1"># First element of keyword argument &#39;players&#39;.</span>
</pre></div>
</div>
<p>The <em>conversion</em> field causes a type coercion before formatting.  Normally, the
job of formatting a value is done by the <a class="reference internal" href="../reference/datamodel.html#object.__format__" title="object.__format__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code></a> method of the value
itself.  However, in some cases it is desirable to force a type to be formatted
as a string, overriding its own definition of formatting.  By converting the
value to a string before calling <a class="reference internal" href="../reference/datamodel.html#object.__format__" title="object.__format__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code></a>, the normal formatting logic
is bypassed.</p>
<p>Three conversion flags are currently supported: <code class="docutils literal notranslate"><span class="pre">'!s'</span></code> which calls <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a>
on the value, <code class="docutils literal notranslate"><span class="pre">'!r'</span></code> which calls <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> and <code class="docutils literal notranslate"><span class="pre">'!a'</span></code> which calls
<a class="reference internal" href="functions.html#ascii" title="ascii"><code class="xref py py-func docutils literal notranslate"><span class="pre">ascii()</span></code></a>.</p>
<p>Some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="s2">&quot;Harold&#39;s a clever </span><span class="si">{0!s}</span><span class="s2">&quot;</span>        <span class="c1"># Calls str() on the argument first</span>
<span class="s2">&quot;Bring out the holy </span><span class="si">{name!r}</span><span class="s2">&quot;</span>    <span class="c1"># Calls repr() on the argument first</span>
<span class="s2">&quot;More </span><span class="si">{!a}</span><span class="s2">&quot;</span>                      <span class="c1"># Calls ascii() on the argument first</span>
</pre></div>
</div>
<p>The <em>format_spec</em> field contains a specification of how the value should be
presented, including such details as field width, alignment, padding, decimal
precision and so on.  Each value type can define its own “formatting
mini-language” or interpretation of the <em>format_spec</em>.</p>
<p>Most built-in types support a common formatting mini-language, which is
described in the next section.</p>
<p>A <em>format_spec</em> field can also include nested replacement fields within it.
These nested replacement fields may contain a field name, conversion flag
and format specification, but deeper nesting is
not allowed.  The replacement fields within the
format_spec are substituted before the <em>format_spec</em> string is interpreted.
This allows the formatting of a value to be dynamically specified.</p>
<p>See the <a class="reference internal" href="#formatexamples"><span class="std std-ref">Format examples</span></a> section for some examples.</p>
<section id="format-specification-mini-language">
<span id="formatspec"></span><h3>Format Specification Mini-Language<a class="headerlink" href="#format-specification-mini-language" title="Link to this heading">¶</a></h3>
<p>“Format specifications” are used within replacement fields contained within a
format string to define how individual values are presented (see
<a class="reference internal" href="#formatstrings"><span class="std std-ref">Format String Syntax</span></a> and <a class="reference internal" href="../reference/lexical_analysis.html#f-strings"><span class="std std-ref">f-strings</span></a>).
They can also be passed directly to the built-in
<a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> function.  Each formattable type may define how the format
specification is to be interpreted.</p>
<p>Most built-in types implement the following options for format specifications,
although some of the formatting options are only supported by the numeric types.</p>
<p>A general convention is that an empty format specification produces
the same result as if you had called <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> on the value. A
non-empty format specification typically modifies the result.</p>
<p>The general form of a <em>standard format specifier</em> is:</p>
<pre>
<strong id="grammar-token-format-spec-format_spec">format_spec    </strong> ::=  [[<a class="reference internal" href="#grammar-token-format-spec-fill"><code class="xref docutils literal notranslate"><span class="pre">fill</span></code></a>]<a class="reference internal" href="#grammar-token-format-spec-align"><code class="xref docutils literal notranslate"><span class="pre">align</span></code></a>][<a class="reference internal" href="#grammar-token-format-spec-sign"><code class="xref docutils literal notranslate"><span class="pre">sign</span></code></a>][&quot;z&quot;][&quot;#&quot;][&quot;0&quot;][<a class="reference internal" href="#grammar-token-format-spec-width"><code class="xref docutils literal notranslate"><span class="pre">width</span></code></a>][<a class="reference internal" href="#grammar-token-format-spec-grouping_option"><code class="xref docutils literal notranslate"><span class="pre">grouping_option</span></code></a>][&quot;.&quot; <a class="reference internal" href="#grammar-token-format-spec-precision"><code class="xref docutils literal notranslate"><span class="pre">precision</span></code></a>][<a class="reference internal" href="#grammar-token-format-spec-type"><code class="xref docutils literal notranslate"><span class="pre">type</span></code></a>]
<strong id="grammar-token-format-spec-fill">fill           </strong> ::=  &lt;any character&gt;
<strong id="grammar-token-format-spec-align">align          </strong> ::=  &quot;&lt;&quot; | &quot;&gt;&quot; | &quot;=&quot; | &quot;^&quot;
<strong id="grammar-token-format-spec-sign">sign           </strong> ::=  &quot;+&quot; | &quot;-&quot; | &quot; &quot;
<strong id="grammar-token-format-spec-width">width          </strong> ::=  <a class="reference internal" href="../reference/lexical_analysis.html#grammar-token-python-grammar-digit"><code class="xref docutils literal notranslate"><span class="pre">digit</span></code></a>+
<strong id="grammar-token-format-spec-grouping_option">grouping_option</strong> ::=  &quot;_&quot; | &quot;,&quot;
<strong id="grammar-token-format-spec-precision">precision      </strong> ::=  <a class="reference internal" href="../reference/lexical_analysis.html#grammar-token-python-grammar-digit"><code class="xref docutils literal notranslate"><span class="pre">digit</span></code></a>+
<strong id="grammar-token-format-spec-type">type           </strong> ::=  &quot;b&quot; | &quot;c&quot; | &quot;d&quot; | &quot;e&quot; | &quot;E&quot; | &quot;f&quot; | &quot;F&quot; | &quot;g&quot; | &quot;G&quot; | &quot;n&quot; | &quot;o&quot; | &quot;s&quot; | &quot;x&quot; | &quot;X&quot; | &quot;%&quot;
</pre>
<p>If a valid <em>align</em> value is specified, it can be preceded by a <em>fill</em>
character that can be any character and defaults to a space if omitted.
It is not possible to use a literal curly brace (”<code class="docutils literal notranslate"><span class="pre">{</span></code>” or “<code class="docutils literal notranslate"><span class="pre">}</span></code>”) as
the <em>fill</em> character in a <a class="reference internal" href="../reference/lexical_analysis.html#f-strings"><span class="std std-ref">formatted string literal</span></a> or when using the <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a>
method.  However, it is possible to insert a curly brace
with a nested replacement field.  This limitation doesn’t
affect the <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> function.</p>
<p>The meaning of the various alignment options is as follows:</p>
<table class="docutils align-default" id="index-3">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code></p></td>
<td><p>Forces the field to be left-aligned within the available
space (this is the default for most objects).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code></p></td>
<td><p>Forces the field to be right-aligned within the
available space (this is the default for numbers).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'='</span></code></p></td>
<td><p>Forces the padding to be placed after the sign (if any)
but before the digits.  This is used for printing fields
in the form ‘+000000120’. This alignment option is only
valid for numeric types.  It becomes the default for
numbers when ‘0’ immediately precedes the field width.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'^'</span></code></p></td>
<td><p>Forces the field to be centered within the available
space.</p></td>
</tr>
</tbody>
</table>
<p>Note that unless a minimum field width is defined, the field width will always
be the same size as the data to fill it, so that the alignment option has no
meaning in this case.</p>
<p>The <em>sign</em> option is only valid for number types, and can be one of the
following:</p>
<table class="docutils align-default" id="index-4">
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'+'</span></code></p></td>
<td><p>indicates that a sign should be used for both
positive as well as negative numbers.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'-'</span></code></p></td>
<td><p>indicates that a sign should be used only for negative
numbers (this is the default behavior).</p></td>
</tr>
<tr class="row-even"><td><p>space</p></td>
<td><p>indicates that a leading space should be used on
positive numbers, and a minus sign on negative numbers.</p></td>
</tr>
</tbody>
</table>
<p id="index-5">The <code class="docutils literal notranslate"><span class="pre">'z'</span></code> option coerces negative zero floating-point values to positive
zero after rounding to the format precision.  This option is only valid for
floating-point presentation types.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <code class="docutils literal notranslate"><span class="pre">'z'</span></code> option (see also <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0682/"><strong>PEP 682</strong></a>).</p>
</div>
<p id="index-7">The <code class="docutils literal notranslate"><span class="pre">'#'</span></code> option causes the “alternate form” to be used for the
conversion.  The alternate form is defined differently for different
types.  This option is only valid for integer, float and complex
types. For integers, when binary, octal, or hexadecimal output
is used, this option adds the respective prefix <code class="docutils literal notranslate"><span class="pre">'0b'</span></code>, <code class="docutils literal notranslate"><span class="pre">'0o'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'0x'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'0X'</span></code> to the output value. For float and complex the
alternate form causes the result of the conversion to always contain a
decimal-point character, even if no digits follow it. Normally, a
decimal-point character appears in the result of these conversions
only if a digit follows it. In addition, for <code class="docutils literal notranslate"><span class="pre">'g'</span></code> and <code class="docutils literal notranslate"><span class="pre">'G'</span></code>
conversions, trailing zeros are not removed from the result.</p>
<p id="index-8">The <code class="docutils literal notranslate"><span class="pre">','</span></code> option signals the use of a comma for a thousands separator.
For a locale aware separator, use the <code class="docutils literal notranslate"><span class="pre">'n'</span></code> integer presentation type
instead.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the <code class="docutils literal notranslate"><span class="pre">','</span></code> option (see also <span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0378/"><strong>PEP 378</strong></a>).</p>
</div>
<p id="index-10">The <code class="docutils literal notranslate"><span class="pre">'_'</span></code> option signals the use of an underscore for a thousands
separator for floating point presentation types and for integer
presentation type <code class="docutils literal notranslate"><span class="pre">'d'</span></code>.  For integer presentation types <code class="docutils literal notranslate"><span class="pre">'b'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'o'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'X'</span></code>, underscores will be inserted every 4
digits.  For other presentation types, specifying this option is an
error.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the <code class="docutils literal notranslate"><span class="pre">'_'</span></code> option (see also <span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-0515/"><strong>PEP 515</strong></a>).</p>
</div>
<p><em>width</em> is a decimal integer defining the minimum total field width,
including any prefixes, separators, and other formatting characters.
If not specified, then the field width will be determined by the content.</p>
<p>When no explicit alignment is given, preceding the <em>width</em> field by a zero
(<code class="docutils literal notranslate"><span class="pre">'0'</span></code>) character enables
sign-aware zero-padding for numeric types.  This is equivalent to a <em>fill</em>
character of <code class="docutils literal notranslate"><span class="pre">'0'</span></code> with an <em>alignment</em> type of <code class="docutils literal notranslate"><span class="pre">'='</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Preceding the <em>width</em> field by <code class="docutils literal notranslate"><span class="pre">'0'</span></code> no longer affects the default
alignment for strings.</p>
</div>
<p>The <em>precision</em> is a decimal integer indicating how many digits should be
displayed after the decimal point for presentation types
<code class="docutils literal notranslate"><span class="pre">'f'</span></code> and <code class="docutils literal notranslate"><span class="pre">'F'</span></code>, or before and after the decimal point for presentation
types <code class="docutils literal notranslate"><span class="pre">'g'</span></code> or <code class="docutils literal notranslate"><span class="pre">'G'</span></code>.  For string presentation types the field
indicates the maximum field size - in other words, how many characters will be
used from the field content.  The <em>precision</em> is not allowed for integer
presentation types.</p>
<p>Finally, the <em>type</em> determines how the data should be presented.</p>
<p>The available string presentation types are:</p>
<blockquote>
<div><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Type</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'s'</span></code></p></td>
<td><p>String format. This is the default type for strings and
may be omitted.</p></td>
</tr>
<tr class="row-odd"><td><p>None</p></td>
<td><p>The same as <code class="docutils literal notranslate"><span class="pre">'s'</span></code>.</p></td>
</tr>
</tbody>
</table>
</div></blockquote>
<p>The available integer presentation types are:</p>
<blockquote>
<div><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Type</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'b'</span></code></p></td>
<td><p>Binary format. Outputs the number in base 2.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'c'</span></code></p></td>
<td><p>Character. Converts the integer to the corresponding
unicode character before printing.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'d'</span></code></p></td>
<td><p>Decimal Integer. Outputs the number in base 10.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'o'</span></code></p></td>
<td><p>Octal format. Outputs the number in base 8.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'x'</span></code></p></td>
<td><p>Hex format. Outputs the number in base 16, using
lower-case letters for the digits above 9.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'X'</span></code></p></td>
<td><p>Hex format. Outputs the number in base 16, using
upper-case letters for the digits above 9.
In case <code class="docutils literal notranslate"><span class="pre">'#'</span></code> is specified, the prefix <code class="docutils literal notranslate"><span class="pre">'0x'</span></code> will
be upper-cased to <code class="docutils literal notranslate"><span class="pre">'0X'</span></code> as well.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'n'</span></code></p></td>
<td><p>Number. This is the same as <code class="docutils literal notranslate"><span class="pre">'d'</span></code>, except that it uses
the current locale setting to insert the appropriate
number separator characters.</p></td>
</tr>
<tr class="row-odd"><td><p>None</p></td>
<td><p>The same as <code class="docutils literal notranslate"><span class="pre">'d'</span></code>.</p></td>
</tr>
</tbody>
</table>
</div></blockquote>
<p>In addition to the above presentation types, integers can be formatted
with the floating point presentation types listed below (except
<code class="docutils literal notranslate"><span class="pre">'n'</span></code> and <code class="docutils literal notranslate"><span class="pre">None</span></code>). When doing so, <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a> is used to convert the
integer to a floating point number before formatting.</p>
<p>The available presentation types for <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> and
<a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a> values are:</p>
<blockquote>
<div><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Type</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'e'</span></code></p></td>
<td><p>Scientific notation. For a given precision <code class="docutils literal notranslate"><span class="pre">p</span></code>,
formats the number in scientific notation with the
letter ‘e’ separating the coefficient from the exponent.
The coefficient has one digit before and <code class="docutils literal notranslate"><span class="pre">p</span></code> digits
after the decimal point, for a total of <code class="docutils literal notranslate"><span class="pre">p</span> <span class="pre">+</span> <span class="pre">1</span></code>
significant digits. With no precision given, uses a
precision of <code class="docutils literal notranslate"><span class="pre">6</span></code> digits after the decimal point for
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, and shows all coefficient digits
for <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a>. If no digits follow the
decimal point, the decimal point is also removed unless
the <code class="docutils literal notranslate"><span class="pre">#</span></code> option is used.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'E'</span></code></p></td>
<td><p>Scientific notation. Same as <code class="docutils literal notranslate"><span class="pre">'e'</span></code> except it uses
an upper case ‘E’ as the separator character.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'f'</span></code></p></td>
<td><p>Fixed-point notation. For a given precision <code class="docutils literal notranslate"><span class="pre">p</span></code>,
formats the number as a decimal number with exactly
<code class="docutils literal notranslate"><span class="pre">p</span></code> digits following the decimal point. With no
precision given, uses a precision of <code class="docutils literal notranslate"><span class="pre">6</span></code> digits after
the decimal point for <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, and uses a
precision large enough to show all coefficient digits
for <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a>. If no digits follow the
decimal point, the decimal point is also removed unless
the <code class="docutils literal notranslate"><span class="pre">#</span></code> option is used.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'F'</span></code></p></td>
<td><p>Fixed-point notation. Same as <code class="docutils literal notranslate"><span class="pre">'f'</span></code>, but converts
<code class="docutils literal notranslate"><span class="pre">nan</span></code> to  <code class="docutils literal notranslate"><span class="pre">NAN</span></code> and <code class="docutils literal notranslate"><span class="pre">inf</span></code> to <code class="docutils literal notranslate"><span class="pre">INF</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'g'</span></code></p></td>
<td><p>General format.  For a given precision <code class="docutils literal notranslate"><span class="pre">p</span> <span class="pre">&gt;=</span> <span class="pre">1</span></code>,
this rounds the number to <code class="docutils literal notranslate"><span class="pre">p</span></code> significant digits and
then formats the result in either fixed-point format
or in scientific notation, depending on its magnitude.
A precision of <code class="docutils literal notranslate"><span class="pre">0</span></code> is treated as equivalent to a
precision of <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<p>The precise rules are as follows: suppose that the
result formatted with presentation type <code class="docutils literal notranslate"><span class="pre">'e'</span></code> and
precision <code class="docutils literal notranslate"><span class="pre">p-1</span></code> would have exponent <code class="docutils literal notranslate"><span class="pre">exp</span></code>.  Then,
if <code class="docutils literal notranslate"><span class="pre">m</span> <span class="pre">&lt;=</span> <span class="pre">exp</span> <span class="pre">&lt;</span> <span class="pre">p</span></code>, where <code class="docutils literal notranslate"><span class="pre">m</span></code> is -4 for floats and -6
for <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimals</span></code></a>, the number is
formatted with presentation type <code class="docutils literal notranslate"><span class="pre">'f'</span></code> and precision
<code class="docutils literal notranslate"><span class="pre">p-1-exp</span></code>.  Otherwise, the number is formatted
with presentation type <code class="docutils literal notranslate"><span class="pre">'e'</span></code> and precision <code class="docutils literal notranslate"><span class="pre">p-1</span></code>.
In both cases insignificant trailing zeros are removed
from the significand, and the decimal point is also
removed if there are no remaining digits following it,
unless the <code class="docutils literal notranslate"><span class="pre">'#'</span></code> option is used.</p>
<p>With no precision given, uses a precision of <code class="docutils literal notranslate"><span class="pre">6</span></code>
significant digits for <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>. For
<a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a>, the coefficient of the result
is formed from the coefficient digits of the value;
scientific notation is used for values smaller than
<code class="docutils literal notranslate"><span class="pre">1e-6</span></code> in absolute value and values where the place
value of the least significant digit is larger than 1,
and fixed-point notation is used otherwise.</p>
<p>Positive and negative infinity, positive and negative
zero, and nans, are formatted as <code class="docutils literal notranslate"><span class="pre">inf</span></code>, <code class="docutils literal notranslate"><span class="pre">-inf</span></code>,
<code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">-0</span></code> and <code class="docutils literal notranslate"><span class="pre">nan</span></code> respectively, regardless of
the precision.</p>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'G'</span></code></p></td>
<td><p>General format. Same as <code class="docutils literal notranslate"><span class="pre">'g'</span></code> except switches to
<code class="docutils literal notranslate"><span class="pre">'E'</span></code> if the number gets too large. The
representations of infinity and NaN are uppercased, too.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'n'</span></code></p></td>
<td><p>Number. This is the same as <code class="docutils literal notranslate"><span class="pre">'g'</span></code>, except that it uses
the current locale setting to insert the appropriate
number separator characters.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'%'</span></code></p></td>
<td><p>Percentage. Multiplies the number by 100 and displays
in fixed (<code class="docutils literal notranslate"><span class="pre">'f'</span></code>) format, followed by a percent sign.</p></td>
</tr>
<tr class="row-even"><td><p>None</p></td>
<td><p>For <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> this is the same as <code class="docutils literal notranslate"><span class="pre">'g'</span></code>, except
that when fixed-point notation is used to format the
result, it always includes at least one digit past the
decimal point. The precision used is as large as needed
to represent the given value faithfully.</p>
<p>For <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a>, this is the same as
either <code class="docutils literal notranslate"><span class="pre">'g'</span></code> or <code class="docutils literal notranslate"><span class="pre">'G'</span></code> depending on the value of
<code class="docutils literal notranslate"><span class="pre">context.capitals</span></code> for the current decimal context.</p>
<p>The overall effect is to match the output of <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a>
as altered by the other format modifiers.</p>
</td>
</tr>
</tbody>
</table>
</div></blockquote>
</section>
<section id="format-examples">
<span id="formatexamples"></span><h3>Format examples<a class="headerlink" href="#format-examples" title="Link to this heading">¶</a></h3>
<p>This section contains examples of the <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a> syntax and
comparison with the old <code class="docutils literal notranslate"><span class="pre">%</span></code>-formatting.</p>
<p>In most of the cases the syntax is similar to the old <code class="docutils literal notranslate"><span class="pre">%</span></code>-formatting, with the
addition of the <code class="docutils literal notranslate"><span class="pre">{}</span></code> and with <code class="docutils literal notranslate"><span class="pre">:</span></code> used instead of <code class="docutils literal notranslate"><span class="pre">%</span></code>.
For example, <code class="docutils literal notranslate"><span class="pre">'%03.2f'</span></code> can be translated to <code class="docutils literal notranslate"><span class="pre">'{:03.2f}'</span></code>.</p>
<p>The new format syntax also supports new and different options, shown in the
following examples.</p>
<p>Accessing arguments by position:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{0}</span><span class="s1">, </span><span class="si">{1}</span><span class="s1">, </span><span class="si">{2}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;a&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">)</span>
<span class="go">&#39;a, b, c&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{}</span><span class="s1">, </span><span class="si">{}</span><span class="s1">, </span><span class="si">{}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;a&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">)</span>  <span class="c1"># 3.1+ only</span>
<span class="go">&#39;a, b, c&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{2}</span><span class="s1">, </span><span class="si">{1}</span><span class="s1">, </span><span class="si">{0}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;a&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">)</span>
<span class="go">&#39;c, b, a&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{2}</span><span class="s1">, </span><span class="si">{1}</span><span class="s1">, </span><span class="si">{0}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="o">*</span><span class="s1">&#39;abc&#39;</span><span class="p">)</span>      <span class="c1"># unpacking argument sequence</span>
<span class="go">&#39;c, b, a&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{0}{1}{0}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;abra&#39;</span><span class="p">,</span> <span class="s1">&#39;cad&#39;</span><span class="p">)</span>   <span class="c1"># arguments&#39; indices can be repeated</span>
<span class="go">&#39;abracadabra&#39;</span>
</pre></div>
</div>
<p>Accessing arguments by name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;Coordinates: </span><span class="si">{latitude}</span><span class="s1">, </span><span class="si">{longitude}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">latitude</span><span class="o">=</span><span class="s1">&#39;37.24N&#39;</span><span class="p">,</span> <span class="n">longitude</span><span class="o">=</span><span class="s1">&#39;-115.81W&#39;</span><span class="p">)</span>
<span class="go">&#39;Coordinates: 37.24N, -115.81W&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">coord</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;latitude&#39;</span><span class="p">:</span> <span class="s1">&#39;37.24N&#39;</span><span class="p">,</span> <span class="s1">&#39;longitude&#39;</span><span class="p">:</span> <span class="s1">&#39;-115.81W&#39;</span><span class="p">}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;Coordinates: </span><span class="si">{latitude}</span><span class="s1">, </span><span class="si">{longitude}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="o">**</span><span class="n">coord</span><span class="p">)</span>
<span class="go">&#39;Coordinates: 37.24N, -115.81W&#39;</span>
</pre></div>
</div>
<p>Accessing arguments’ attributes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="mi">3</span><span class="o">-</span><span class="mi">5</span><span class="n">j</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="s1">&#39;The complex number </span><span class="si">{0}</span><span class="s1"> is formed from the real part </span><span class="si">{0.real}</span><span class="s1"> &#39;</span>
<span class="gp">... </span> <span class="s1">&#39;and the imaginary part </span><span class="si">{0.imag}</span><span class="s1">.&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">c</span><span class="p">)</span>
<span class="go">&#39;The complex number (3-5j) is formed from the real part 3.0 and the imaginary part -5.0.&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Point</span><span class="p">:</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span> <span class="o">=</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__str__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="s1">&#39;Point(</span><span class="si">{self.x}</span><span class="s1">, </span><span class="si">{self.y}</span><span class="s1">)&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">=</span><span class="bp">self</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">Point</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">2</span><span class="p">))</span>
<span class="go">&#39;Point(4, 2)&#39;</span>
</pre></div>
</div>
<p>Accessing arguments’ items:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">coord</span> <span class="o">=</span> <span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;X: </span><span class="si">{0[0]}</span><span class="s1">;  Y: </span><span class="si">{0[1]}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">coord</span><span class="p">)</span>
<span class="go">&#39;X: 3;  Y: 5&#39;</span>
</pre></div>
</div>
<p>Replacing <code class="docutils literal notranslate"><span class="pre">%s</span></code> and <code class="docutils literal notranslate"><span class="pre">%r</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s2">&quot;repr() shows quotes: </span><span class="si">{!r}</span><span class="s2">; str() doesn&#39;t: </span><span class="si">{!s}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;test1&#39;</span><span class="p">,</span> <span class="s1">&#39;test2&#39;</span><span class="p">)</span>
<span class="go">&quot;repr() shows quotes: &#39;test1&#39;; str() doesn&#39;t: test2&quot;</span>
</pre></div>
</div>
<p>Aligning the text and specifying a width:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:&lt;30}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;left aligned&#39;</span><span class="p">)</span>
<span class="go">&#39;left aligned                  &#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:&gt;30}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;right aligned&#39;</span><span class="p">)</span>
<span class="go">&#39;                 right aligned&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:^30}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;centered&#39;</span><span class="p">)</span>
<span class="go">&#39;           centered           &#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:*^30}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="s1">&#39;centered&#39;</span><span class="p">)</span>  <span class="c1"># use &#39;*&#39; as a fill char</span>
<span class="go">&#39;***********centered***********&#39;</span>
</pre></div>
</div>
<p>Replacing <code class="docutils literal notranslate"><span class="pre">%+f</span></code>, <code class="docutils literal notranslate"><span class="pre">%-f</span></code>, and <code class="docutils literal notranslate"><span class="pre">%</span> <span class="pre">f</span></code> and specifying a sign:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:+f}</span><span class="s1">; </span><span class="si">{:+f}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="mf">3.14</span><span class="p">,</span> <span class="o">-</span><span class="mf">3.14</span><span class="p">)</span>  <span class="c1"># show it always</span>
<span class="go">&#39;+3.140000; -3.140000&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{: f}</span><span class="s1">; </span><span class="si">{: f}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="mf">3.14</span><span class="p">,</span> <span class="o">-</span><span class="mf">3.14</span><span class="p">)</span>  <span class="c1"># show a space for positive numbers</span>
<span class="go">&#39; 3.140000; -3.140000&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:-f}</span><span class="s1">; </span><span class="si">{:-f}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="mf">3.14</span><span class="p">,</span> <span class="o">-</span><span class="mf">3.14</span><span class="p">)</span>  <span class="c1"># show only the minus -- same as &#39;{:f}; {:f}&#39;</span>
<span class="go">&#39;3.140000; -3.140000&#39;</span>
</pre></div>
</div>
<p>Replacing <code class="docutils literal notranslate"><span class="pre">%x</span></code> and <code class="docutils literal notranslate"><span class="pre">%o</span></code> and converting the value to different bases:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># format also supports binary numbers</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s2">&quot;int: </span><span class="si">{0:d}</span><span class="s2">;  hex: </span><span class="si">{0:x}</span><span class="s2">;  oct: </span><span class="si">{0:o}</span><span class="s2">;  bin: </span><span class="si">{0:b}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
<span class="go">&#39;int: 42;  hex: 2a;  oct: 52;  bin: 101010&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># with 0x, 0o, or 0b as prefix:</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s2">&quot;int: </span><span class="si">{0:d}</span><span class="s2">;  hex: </span><span class="si">{0:#x}</span><span class="s2">;  oct: </span><span class="si">{0:#o}</span><span class="s2">;  bin: </span><span class="si">{0:#b}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
<span class="go">&#39;int: 42;  hex: 0x2a;  oct: 0o52;  bin: 0b101010&#39;</span>
</pre></div>
</div>
<p>Using the comma as a thousands separator:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:,}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="mi">1234567890</span><span class="p">)</span>
<span class="go">&#39;1,234,567,890&#39;</span>
</pre></div>
</div>
<p>Expressing a percentage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">points</span> <span class="o">=</span> <span class="mi">19</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">total</span> <span class="o">=</span> <span class="mi">22</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;Correct answers: </span><span class="si">{:.2%}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">points</span><span class="o">/</span><span class="n">total</span><span class="p">)</span>
<span class="go">&#39;Correct answers: 86.36%&#39;</span>
</pre></div>
</div>
<p>Using type-specific formatting:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">datetime</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2010</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">58</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;{:%Y-%m-</span><span class="si">%d</span><span class="s1"> %H:%M:%S}&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">d</span><span class="p">)</span>
<span class="go">&#39;2010-07-04 12:15:58&#39;</span>
</pre></div>
</div>
<p>Nesting arguments and more complex examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">align</span><span class="p">,</span> <span class="n">text</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="s1">&#39;&lt;^&gt;&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;left&#39;</span><span class="p">,</span> <span class="s1">&#39;center&#39;</span><span class="p">,</span> <span class="s1">&#39;right&#39;</span><span class="p">]):</span>
<span class="gp">... </span>    <span class="s1">&#39;{0:</span><span class="si">{fill}{align}</span><span class="s1">16}&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="n">fill</span><span class="o">=</span><span class="n">align</span><span class="p">,</span> <span class="n">align</span><span class="o">=</span><span class="n">align</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">&#39;left&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&#39;</span>
<span class="go">&#39;^^^^^center^^^^^&#39;</span>
<span class="go">&#39;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;right&#39;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">octets</span> <span class="o">=</span> <span class="p">[</span><span class="mi">192</span><span class="p">,</span> <span class="mi">168</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:02X}{:02X}{:02X}{:02X}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="o">*</span><span class="n">octets</span><span class="p">)</span>
<span class="go">&#39;C0A80001&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">int</span><span class="p">(</span><span class="n">_</span><span class="p">,</span> <span class="mi">16</span><span class="p">)</span>
<span class="go">3232235521</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">width</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">num</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">12</span><span class="p">):</span> 
<span class="gp">... </span>    <span class="k">for</span> <span class="n">base</span> <span class="ow">in</span> <span class="s1">&#39;dXob&#39;</span><span class="p">:</span>
<span class="gp">... </span>        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;{0:</span><span class="si">{width}{base}</span><span class="s1">}&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">num</span><span class="p">,</span> <span class="n">base</span><span class="o">=</span><span class="n">base</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="n">width</span><span class="p">),</span> <span class="n">end</span><span class="o">=</span><span class="s1">&#39; &#39;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">()</span>
<span class="gp">...</span>
<span class="go">    5     5     5   101</span>
<span class="go">    6     6     6   110</span>
<span class="go">    7     7     7   111</span>
<span class="go">    8     8    10  1000</span>
<span class="go">    9     9    11  1001</span>
<span class="go">   10     A    12  1010</span>
<span class="go">   11     B    13  1011</span>
</pre></div>
</div>
</section>
</section>
<section id="template-strings">
<span id="id1"></span><h2>Template strings<a class="headerlink" href="#template-strings" title="Link to this heading">¶</a></h2>
<p>Template strings provide simpler string substitutions as described in
<span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0292/"><strong>PEP 292</strong></a>.  A primary use case for template strings is for
internationalization (i18n) since in that context, the simpler syntax and
functionality makes it easier to translate than other built-in string
formatting facilities in Python.  As an example of a library built on template
strings for i18n, see the
<a class="reference external" href="https://flufli18n.readthedocs.io/en/latest/">flufl.i18n</a> package.</p>
<p id="index-13">Template strings support <code class="docutils literal notranslate"><span class="pre">$</span></code>-based substitutions, using the following rules:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">$$</span></code> is an escape; it is replaced with a single <code class="docutils literal notranslate"><span class="pre">$</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$identifier</span></code> names a substitution placeholder matching a mapping key of
<code class="docutils literal notranslate"><span class="pre">&quot;identifier&quot;</span></code>.  By default, <code class="docutils literal notranslate"><span class="pre">&quot;identifier&quot;</span></code> is restricted to any
case-insensitive ASCII alphanumeric string (including underscores) that
starts with an underscore or ASCII letter.  The first non-identifier
character after the <code class="docutils literal notranslate"><span class="pre">$</span></code> character terminates this placeholder
specification.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">${identifier}</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">$identifier</span></code>.  It is required when
valid identifier characters follow the placeholder but are not part of the
placeholder, such as <code class="docutils literal notranslate"><span class="pre">&quot;${noun}ification&quot;</span></code>.</p></li>
</ul>
<p>Any other appearance of <code class="docutils literal notranslate"><span class="pre">$</span></code> in the string will result in a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>
being raised.</p>
<p>The <a class="reference internal" href="#module-string" title="string: Common string operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code></a> module provides a <a class="reference internal" href="#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">Template</span></code></a> class that implements
these rules.  The methods of <a class="reference internal" href="#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">Template</span></code></a> are:</p>
<dl class="py class">
<dt class="sig sig-object py" id="string.Template">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">Template</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">template</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Template" title="Link to this definition">¶</a></dt>
<dd><p>The constructor takes a single argument which is the template string.</p>
<dl class="py method">
<dt class="sig sig-object py" id="string.Template.substitute">
<span class="sig-name descname"><span class="pre">substitute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mapping</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Template.substitute" title="Link to this definition">¶</a></dt>
<dd><p>Performs the template substitution, returning a new string.  <em>mapping</em> is
any dictionary-like object with keys that match the placeholders in the
template.  Alternatively, you can provide keyword arguments, where the
keywords are the placeholders.  When both <em>mapping</em> and <em>kwds</em> are given
and there are duplicates, the placeholders from <em>kwds</em> take precedence.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Template.safe_substitute">
<span class="sig-name descname"><span class="pre">safe_substitute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mapping</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.Template.safe_substitute" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#string.Template.substitute" title="string.Template.substitute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">substitute()</span></code></a>, except that if placeholders are missing from
<em>mapping</em> and <em>kwds</em>, instead of raising a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> exception, the
original placeholder will appear in the resulting string intact.  Also,
unlike with <a class="reference internal" href="#string.Template.substitute" title="string.Template.substitute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">substitute()</span></code></a>, any other appearances of the <code class="docutils literal notranslate"><span class="pre">$</span></code> will
simply return <code class="docutils literal notranslate"><span class="pre">$</span></code> instead of raising <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p>While other exceptions may still occur, this method is called “safe”
because it always tries to return a usable string instead of
raising an exception.  In another sense, <a class="reference internal" href="#string.Template.safe_substitute" title="string.Template.safe_substitute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">safe_substitute()</span></code></a> may be
anything other than safe, since it will silently ignore malformed
templates containing dangling delimiters, unmatched braces, or
placeholders that are not valid Python identifiers.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Template.is_valid">
<span class="sig-name descname"><span class="pre">is_valid</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#string.Template.is_valid" title="Link to this definition">¶</a></dt>
<dd><p>Returns false if the template has invalid placeholders that will cause
<a class="reference internal" href="#string.Template.substitute" title="string.Template.substitute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">substitute()</span></code></a> to raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="string.Template.get_identifiers">
<span class="sig-name descname"><span class="pre">get_identifiers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#string.Template.get_identifiers" title="Link to this definition">¶</a></dt>
<dd><p>Returns a list of the valid identifiers in the template, in the order
they first appear, ignoring any invalid identifiers.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<p><a class="reference internal" href="#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">Template</span></code></a> instances also provide one public data attribute:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="string.Template.template">
<span class="sig-name descname"><span class="pre">template</span></span><a class="headerlink" href="#string.Template.template" title="Link to this definition">¶</a></dt>
<dd><p>This is the object passed to the constructor’s <em>template</em> argument.  In
general, you shouldn’t change it, but read-only access is not enforced.</p>
</dd></dl>

</dd></dl>

<p>Here is an example of how to use a Template:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">string</span> <span class="kn">import</span> <span class="n">Template</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">Template</span><span class="p">(</span><span class="s1">&#39;$who likes $what&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">substitute</span><span class="p">(</span><span class="n">who</span><span class="o">=</span><span class="s1">&#39;tim&#39;</span><span class="p">,</span> <span class="n">what</span><span class="o">=</span><span class="s1">&#39;kung pao&#39;</span><span class="p">)</span>
<span class="go">&#39;tim likes kung pao&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">who</span><span class="o">=</span><span class="s1">&#39;tim&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Template</span><span class="p">(</span><span class="s1">&#39;Give $who $100&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">substitute</span><span class="p">(</span><span class="n">d</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">Invalid placeholder in string: line 1, col 11</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Template</span><span class="p">(</span><span class="s1">&#39;$who likes $what&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">substitute</span><span class="p">(</span><span class="n">d</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">KeyError</span>: <span class="n">&#39;what&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Template</span><span class="p">(</span><span class="s1">&#39;$who likes $what&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">safe_substitute</span><span class="p">(</span><span class="n">d</span><span class="p">)</span>
<span class="go">&#39;tim likes $what&#39;</span>
</pre></div>
</div>
<p>Advanced usage: you can derive subclasses of <a class="reference internal" href="#string.Template" title="string.Template"><code class="xref py py-class docutils literal notranslate"><span class="pre">Template</span></code></a> to customize
the placeholder syntax, delimiter character, or the entire regular expression
used to parse template strings.  To do this, you can override these class
attributes:</p>
<ul>
<li><p><em>delimiter</em> – This is the literal string describing a placeholder
introducing delimiter.  The default value is <code class="docutils literal notranslate"><span class="pre">$</span></code>.  Note that this should
<em>not</em> be a regular expression, as the implementation will call
<a class="reference internal" href="re.html#re.escape" title="re.escape"><code class="xref py py-meth docutils literal notranslate"><span class="pre">re.escape()</span></code></a> on this string as needed.  Note further that you cannot
change the delimiter after class creation (i.e. a different delimiter must
be set in the subclass’s class namespace).</p></li>
<li><p><em>idpattern</em> – This is the regular expression describing the pattern for
non-braced placeholders.  The default value is the regular expression
<code class="docutils literal notranslate"><span class="pre">(?a:[_a-z][_a-z0-9]*)</span></code>.  If this is given and <em>braceidpattern</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code> this pattern will also apply to braced placeholders.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Since default <em>flags</em> is <code class="docutils literal notranslate"><span class="pre">re.IGNORECASE</span></code>, pattern <code class="docutils literal notranslate"><span class="pre">[a-z]</span></code> can match
with some non-ASCII characters. That’s why we use the local <code class="docutils literal notranslate"><span class="pre">a</span></code> flag
here.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>braceidpattern</em> can be used to define separate patterns used inside and
outside the braces.</p>
</div>
</li>
<li><p><em>braceidpattern</em> – This is like <em>idpattern</em> but describes the pattern for
braced placeholders.  Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code> which means to fall back to
<em>idpattern</em> (i.e. the same pattern is used both inside and outside braces).
If given, this allows you to define different patterns for braced and
unbraced placeholders.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</li>
<li><p><em>flags</em> – The regular expression flags that will be applied when compiling
the regular expression used for recognizing substitutions.  The default value
is <code class="docutils literal notranslate"><span class="pre">re.IGNORECASE</span></code>.  Note that <code class="docutils literal notranslate"><span class="pre">re.VERBOSE</span></code> will always be added to the
flags, so custom <em>idpattern</em>s must follow conventions for verbose regular
expressions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</li>
</ul>
<p>Alternatively, you can provide the entire regular expression pattern by
overriding the class attribute <em>pattern</em>.  If you do this, the value must be a
regular expression object with four named capturing groups.  The capturing
groups correspond to the rules given above, along with the invalid placeholder
rule:</p>
<ul class="simple">
<li><p><em>escaped</em> – This group matches the escape sequence, e.g. <code class="docutils literal notranslate"><span class="pre">$$</span></code>, in the
default pattern.</p></li>
<li><p><em>named</em> – This group matches the unbraced placeholder name; it should not
include the delimiter in capturing group.</p></li>
<li><p><em>braced</em> – This group matches the brace enclosed placeholder name; it should
not include either the delimiter or braces in the capturing group.</p></li>
<li><p><em>invalid</em> – This group matches any other delimiter pattern (usually a single
delimiter), and it should appear last in the regular expression.</p></li>
</ul>
<p>The methods on this class will raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if the pattern matches
the template without one of these named groups matching.</p>
</section>
<section id="helper-functions">
<h2>Helper functions<a class="headerlink" href="#helper-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="string.capwords">
<span class="sig-prename descclassname"><span class="pre">string.</span></span><span class="sig-name descname"><span class="pre">capwords</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sep</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#string.capwords" title="Link to this definition">¶</a></dt>
<dd><p>Split the argument into words using <a class="reference internal" href="stdtypes.html#str.split" title="str.split"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.split()</span></code></a>, capitalize each word
using <a class="reference internal" href="stdtypes.html#str.capitalize" title="str.capitalize"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.capitalize()</span></code></a>, and join the capitalized words using
<a class="reference internal" href="stdtypes.html#str.join" title="str.join"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.join()</span></code></a>.  If the optional second argument <em>sep</em> is absent
or <code class="docutils literal notranslate"><span class="pre">None</span></code>, runs of whitespace characters are replaced by a single space
and leading and trailing whitespace are removed, otherwise <em>sep</em> is used to
split and join the words.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code> — Common string operations</a><ul>
<li><a class="reference internal" href="#string-constants">String constants</a></li>
<li><a class="reference internal" href="#custom-string-formatting">Custom String Formatting</a></li>
<li><a class="reference internal" href="#format-string-syntax">Format String Syntax</a><ul>
<li><a class="reference internal" href="#format-specification-mini-language">Format Specification Mini-Language</a></li>
<li><a class="reference internal" href="#format-examples">Format examples</a></li>
</ul>
</li>
<li><a class="reference internal" href="#template-strings">Template strings</a></li>
<li><a class="reference internal" href="#helper-functions">Helper functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="text.html"
                          title="previous chapter">Text Processing Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="re.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code> — Regular expression operations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/string.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="re.html" title="re — Regular expression operations"
             >next</a> |</li>
        <li class="right" >
          <a href="text.html" title="Text Processing Services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" >Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">string</span></code> — Common string operations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>