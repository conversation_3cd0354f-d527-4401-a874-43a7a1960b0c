# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "%(browser)s on %(platform)s"
msgstr "%(browser)s на %(platform)s"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "2-Factor Setup Wizard"
msgstr "Помічник двофакторного налаштування"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "2-Factor authentication is now enabled."
msgstr "Двофакторну аутентифікацію тепер увімкнено."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                                    Learn More"
msgstr ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                                    Дізнатися більше "

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                            Learn More"
msgstr ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                            Дізнатися більше"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">This account is protected!</span>"
msgstr ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">Цей обліковий запис захищено!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">Your account is protected!</span>"
msgstr ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">Ваш обліковий запис захищено!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-md-none d-block\">Or install an authenticator app</span>\n"
"                                        <span class=\"d-none d-md-block\">Install an authenticator app on your mobile device</span>"
msgstr ""
"<span class=\"d-md-none d-block\">Або встановіть модуль аутентифікатора</span>\n"
"                                        <span class=\"d-none d-md-block\">Встановіть модуль аутентифікатора на ваш мобільний пристрій</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-none d-md-block\">When requested to do so, scan the barcode below</span>\n"
"                                    <span class=\"d-block d-md-none\">When requested to do so, copy the key below</span>"
msgstr ""
"<span class=\"d-none d-md-block\">Коли виникає такий запит, відскануйте штрих-код нижче</span>\n"
"                                    <span class=\"d-block d-md-none\">Коли виникає такий запит, скопіюйте ключ нижче</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"text-muted\">Popular ones include Authy, Google Authenticator "
"or the Microsoft Authenticator.</span>"
msgstr ""
"<span class=\"text-muted\">Популярні включають Authy, Google Authenticator "
"або Microsoft Authenticator.</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Account Security"
msgstr "Безпека облікового запису"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Activate"
msgstr "Активувати"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Added On"
msgstr "Додано на"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Are you sure? The user may be asked to enter two-factor codes again on those"
" devices"
msgstr ""
"Ви впевнені? Користувача можуть попросити знову ввести двофакторні коди на "
"цих пристроях"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Are you sure? You may be asked to enter two-factor codes again on those "
"devices"
msgstr ""
"Ви впевнені? Вас можуть попросити знову ввести двофакторні коди на цих "
"пристроях"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code"
msgstr "Код аутентифікації"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_device
msgid "Authentication Device"
msgstr "Пристрій аутентифікації"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Authenticator App Setup"
msgstr "Встановлення модуля аутентифікації"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "Скасувати"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cannot scan it?"
msgstr "Не можете відсканувати його?"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Click on this link to open your authenticator app"
msgstr "Натисніть на це посилання, щоби відкрити ваш модуль аутентифікації"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "Створив"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "Створено"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__create_date
msgid "Creation Date"
msgstr "Дата створення"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__name
msgid "Description"
msgstr "Опис"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Device"
msgstr "Пристрій"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Disable 2FA"
msgstr "Вимкнути 2FA"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable two-factor authentication"
msgstr "Вимкнути двофакторну аутентифікацію"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__display_name
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again on this device"
msgstr "Більше не питати на цьому пристрої"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Enable 2FA"
msgstr "Увімкнути 2FA"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter your six-digit code below"
msgstr "Введіть ваш шестизначний код нижче"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизація HTTP"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
msgid "ID"
msgstr "ID"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "Invalid authentication code format."
msgstr "Формат коду аутентифікації недійсний."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device____last_update
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard____last_update
msgid "Last Modified on"
msgstr "Остання модифікація"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Learn More"
msgstr "Детальніше"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Log in"
msgstr "Вхід"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Look for an \"Add an account\" button"
msgstr "Погляньте на кнопку \"Додати обліковий запис\""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Apple Store"
msgstr "На Apple Store"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Google Play"
msgstr "На Google Play"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr "Qr-код"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke"
msgstr "Скасувати"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke All"
msgstr "Скасувати все"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__scope
msgid "Scope"
msgstr "Сфера"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr "Секрет"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "The verification code should only contain numbers"
msgstr "Код верифікації має містити лише цифри"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"To login, enter below the six-digit authentication code provided by your Authenticator app.\n"
"                        <br/>"
msgstr ""
"Щоб увійти, введіть нижче шестизначний код аутентифікації, який надає ваш додаток Аутентифікатор.\n"
"                        <br/>"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr "Ключ Totp"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Devices"
msgstr "Надійні пристрої"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-Factor Authentication Activation"
msgstr "Активація двофакторної аутентифікації"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Two-factor Authentication"
msgstr "Двофакторна аутентифікація"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                                The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                                Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"Двофакторна аутентифікація («2FA») — це система подвійної перевірки.\n"
"                                Перша виконується за допомогою вашого пароля, а друга — за допомогою коду, який ви отримуєте зі спеціального мобільного додатка.\n"
"                                Серед популярних — Authy, Google Authenticator або Microsoft Authenticator."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"Двофакторна автентифікація («2FA») — це система подвійної автентифікації.\n"
"                        Перший виконується за допомогою вашого пароля, а другий – за допомогою коду, який ви отримуєте зі спеціального мобільного додатка.\n"
"                        До популярних належать Authy, Google Authenticator або Microsoft Authenticator."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "Двофакторна аутентифікація"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Disabled"
msgstr "Двофакторна аутентифікація вимкнена"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Enabled"
msgstr "Двофакторна аутентифікація увімкнена"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication already enabled"
msgstr "Двофакторну аутентифікацію вже увімкнено"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication can only be enabled for yourself"
msgstr "Двофакторну аутентифікацію можна вмикати лише для себе"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication disabled for the following user(s): %s"
msgstr "Двофакторна аутентифікаці вимкнена для наступних користувачів: %s"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "Url-адреса"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__user_id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "Користувач"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "Код перевірки"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "Верифікація не вдалася, перевірте ще раз  шестизначний код"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "e.g. 123456"
msgstr "напр., 123456"
