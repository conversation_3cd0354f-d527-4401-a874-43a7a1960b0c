# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# Wil O<PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
" \n"
"                        <br/>\n"
"                      ขอขอบคุณสำหรับการสนับสนุนสำหรับองค์กรของเรา\n"
"                        <br/>\n"
"                        ขอแสดงความนับถือ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>ความคิดเห็น:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>วันที่บริจาค:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>อีเมลผู้บริจาค:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>ชื่อผู้บริจาค:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>รหัสการชำระเงิน:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>วิธีการชำระเงิน:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">ประเทศ...</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>ไม่พบตัวเลือกการชำระเงินที่เหมาะสม</strong><br/>\n"
"                                หากคุณเชื่อว่าเป็นข้อผิดพลาด โปรดติดต่อผู้ดูแลเว็บไซต์"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>คำเตือน</strong>สกุลเงินหายไปหรือไม่ถูกต้อง"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "มีการบริจาคบนเว็บไซต์ของคุณ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "ปีแห่งการตื่นตัวทางวัฒนธรรม"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Payments"
msgstr "เปิดใช้งานการชำระเงิน"

#. module: website_payment
#: model:ir.actions.server,name:website_payment.action_activate_stripe
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Stripe"
msgstr "เปิดใช้งาน Stripe"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "เพิ่มคำบรรยายตรงนี้"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "เพิ่มตัวเลือกที่กรอกล่วงหน้าใหม่"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Amount"
msgstr "จำนวน"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Amount ("
msgstr "จำนวน ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "จำนวน("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "เลี้ยงทารกได้ 1 เดือน"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "เลือกจำนวนของคุณ"

#. module: website_payment
#: model:ir.model,name:website_payment.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/res_config_settings.py:0
#, python-format
msgid "Configure %s"
msgstr "การกำหนดค่า %s"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#: model:ir.model,name:website_payment.model_res_country
#, python-format
msgid "Country"
msgstr "ประเทศ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Country\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"ประเทศ\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "ต้องระบุประเทศ"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "จำนวนที่กำหนดเอง"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "เรียน"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "จำนวนเริ่มต้น"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Descriptions"
msgstr "รายละเอียด"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "ตัวเลือกการแสดง"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donate"
msgstr "บริจาค"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "บริจาคตอนนี้"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation"
msgstr "การบริจาค"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation Button"
msgstr "ปุ่มโดเนท"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "จำนวนเงินบริจาคต้องมีอย่างน้อย%.2f"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "ยืนยันการบริจาค"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "แจ้งเตือนการบริจาค"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email"
msgstr "อีเมล"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"อีเมล\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "อีเมล์ไม่ถูกต้อง"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "ต้องระบุอีเมล"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "ช่อง '%s' เป็นช่องบังคับ"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__first_provider_label
msgid "First Provider Label"
msgstr "ป้ายกำกับผู้ให้บริการรายแรก"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "การนำเข้า"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "บริจาค"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__is_stripe_supported_country
#: model:ir.model.fields,field_description:website_payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "เป็นประเทศที่รองรับ Stripe"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "บริจาค"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "โดเนท"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Maximum"
msgstr "สูงสุด"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Minimum"
msgstr "ขั้นต่ำ"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Name"
msgstr "ชื่อ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Name\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"ชื่อ\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "ต้องระบุชื่อ"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__none
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "ไม่มี"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "หนึ่งปีในโรงเรียนประถม"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "หนึ่งปีในโรงเรียนมัธยม"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__other_than_paypal
msgid "Other than Paypal"
msgstr "นอกเหนือจาก Paypal"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Payment Details"
msgstr "รายละเอียดการชำระเงิน"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_provider
msgid "Payment Provider"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "ธุรกรรมสำหรับการชำระเงิน"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "การประมวลผลการชำระเงินล้มเหลว"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "เงินที่ได้รับจากการบริจาคมีรายละเอียดดังต่อไปนี้:"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "การชำระเงิน"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__paypal_only
msgid "Paypal Only"
msgstr "Paypal เท่านั้น"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "กรุณาเลือกหรือใส่จำนวนเงิน"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "ตัวเลือกที่กรอกล่วงหน้า"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__providers_state
msgid "Providers State"
msgstr "ผู้ให้บริการของรัฐ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "อีเมลผู้รับ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Shop - Payment"
msgstr "ร้านค้า - ชำระเงิน"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "ตัวเลื่อน"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "ไม่ว่าจะเล็กหรือใหญ่ การมีส่วนร่วมของคุณเป็นสิ่งสำคัญ"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "ข้อมูลบางอย่างหายไปในการประมวลผลการชำระเงินของคุณ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Step"
msgstr "สถานะ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect ไม่สามารถใช้งานได้ในประเทศของคุณ "
"โปรดใช้ผู้ให้บริการชำระเงินรายอื่น"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple "
"Pay, etc. as well as recurring charges."
msgstr ""
"รองรับวิธีการชำระเงินส่วนใหญ่ Visa, Mastercard, Maestro, Google Pay, Apple "
"Pay ฯลฯ รวมถึงการเรียกเก็บเงินที่เกิดขึ้นประจำ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "ขอบคุณสำหรับการบริจาคของ"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "ยอดบริจาคขั้นต่ำคือ %s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "ไม่มีอะไรต้องชำระ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View Alternatives"
msgstr "ดูทางเลือก"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View other providers"
msgstr "ดูผู้ให้บริการรายอื่น"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_provider__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
msgid "Website: Donation"
msgstr "เว็บไซต์: บริจาค"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Write us a comment"
msgstr "เขียนความคิดเห็นถึงเรา"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Your comment"
msgstr "ความคิดเห็นของคุณ"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "ทำเมื่อ"
