# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar_sms
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_event
msgid "Calendar Event"
msgstr "رویداد تقویم"

#. module: calendar_sms
#: model:sms.template,name:calendar_sms.sms_template_data_calendar_reminder
msgid "Calendar Event: Reminder"
msgstr "رویداد تقویم: یادآور"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_alarm
msgid "Event Alarm"
msgstr "اطلاع رسانی رویداد"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "مدیریت اطلاع رسانی رویداد"

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_event.py:0
#, python-format
msgid "Event reminder: %(name)s, %(time)s."
msgstr "یادآوری رویداد: %(name)s, %(time)s."

#. module: calendar_sms
#: model:sms.template,body:calendar_sms.sms_template_data_calendar_reminder
msgid ""
"Event reminder: {{ object.name }}, {{ "
"object.get_display_time_tz(object.partner_id.tz) }}"
msgstr ""
"یادآوری رویداد: {{ object.name }}, {{ "
"object.get_display_time_tz(object.partner_id.tz) }}"

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_alarm.py:0
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__sms_notify_responsible
#, python-format
msgid "Notify Responsible"
msgstr "اطلاع‌رسانی به مسئول"

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_form_inherited
msgid "SMS"
msgstr "پیامک"

#. module: calendar_sms
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__sms_template_id
msgid "SMS Template"
msgstr "قالب پیامک"

#. module: calendar_sms
#: model:ir.model.fields.selection,name:calendar_sms.selection__calendar_alarm__alarm_type__sms
msgid "SMS Text Message"
msgstr "پیام متنی"

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_tree_inherited
msgid "Send SMS"
msgstr "ارسال پیامک"

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_event.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "ارسال پیامک متنی"

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_form_inherited
msgid "Send SMS to attendees"
msgstr "ارسال پیامک به حاضرین"

#. module: calendar_sms
#: model:ir.model.fields,help:calendar_sms.field_calendar_alarm__sms_template_id
msgid "Template used to render SMS reminder content."
msgstr "الگوی استفاده‌شده برای نمایش محتوای یادآوری پیامک."

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_event.py:0
#, python-format
msgid "There are no attendees on these events"
msgstr "هیچ شرکت کننده ای در این رویدادها وجود ندارد"

#. module: calendar_sms
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "نوع"
