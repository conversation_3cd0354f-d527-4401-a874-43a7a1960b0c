<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="33.944%" y2="66.056%">
      <stop offset="0%" stop-color="#00E2FF"/>
      <stop offset="100%" stop-color="#00A09D"/>
    </linearGradient>
    <linearGradient id="linearGradient-2" x1="0%" x2="100%" y1="31.569%" y2="68.431%">
      <stop offset="0%" stop-color="#00E2FF"/>
      <stop offset="100%" stop-color="#00A09D"/>
    </linearGradient>
    <path id="path-3" d="M20 12v1H4v-1h16zm-6-3v1H4V9h10zm6-3v1H4V6h16z"/>
    <filter id="filter-4" width="106.2%" height="128.6%" x="-3.1%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.2 0"/>
    </filter>
    <rect id="path-5" width="21" height="1" x="4" y="3"/>
    <filter id="filter-6" width="104.8%" height="300%" x="-2.4%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-7" d="M52 29v1H34v-1h18zm-6-3v1H34v-1h12zm6-3v1H34v-1h18z"/>
    <filter id="filter-8" width="105.6%" height="128.6%" x="-2.8%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.2 0"/>
    </filter>
    <rect id="path-9" width="19" height="1" x="34" y="19"/>
    <filter id="filter-10" width="105.3%" height="300%" x="-2.6%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-11" d="M52 12v1H34v-1h18zm-6-3v1H34V9h12zm6-3v1H34V6h18z"/>
    <filter id="filter-12" width="105.6%" height="128.6%" x="-2.8%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-13" width="21" height="1" x="34" y="3"/>
    <filter id="filter-14" width="104.8%" height="300%" x="-2.4%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-15" d="M20 29v1H4v-1h16zm-6-3v1H4v-1h10zm6-3v1H4v-1h16z"/>
    <filter id="filter-16" width="106.2%" height="128.6%" x="-3.1%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-17" width="15" height="1" x="4" y="19"/>
    <filter id="filter-18" width="106.7%" height="300%" x="-3.3%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-19" width="23.442" height="33" x="0" y="0"/>
    <linearGradient id="linearGradient-21" x1="72.875%" x2="40.332%" y1="46.301%" y2="33.313%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-22" x1="88.517%" x2="50%" y1="38.842%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_masonry_block">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(0 15)">
        <g class="group_2" transform="translate(24)">
          <rect width="58" height="33" fill="#D8D8D8" class="rectangle" opacity=".058"/>
          <rect width="30" height="17" fill="url(#linearGradient-1)" class="rectangle" opacity=".4"/>
          <rect width="28" height="17" x="30" y="16" fill="url(#linearGradient-2)" class="rectangle" opacity=".4"/>
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".8" xlink:href="#path-3"/>
          </g>
          <g class="rectangle_copy">
            <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
            <use fill="#FFF" fill-opacity=".95" xlink:href="#path-5"/>
          </g>
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
            <use fill="#FFF" fill-opacity=".8" xlink:href="#path-7"/>
          </g>
          <g class="rectangle_copy">
            <use fill="#000" filter="url(#filter-10)" xlink:href="#path-9"/>
            <use fill="#FFF" fill-opacity=".95" xlink:href="#path-9"/>
          </g>
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-12)" xlink:href="#path-11"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-11"/>
          </g>
          <g class="rectangle_copy">
            <use fill="#000" filter="url(#filter-14)" xlink:href="#path-13"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-13"/>
          </g>
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-16)" xlink:href="#path-15"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-15"/>
          </g>
          <g class="rectangle_copy">
            <use fill="#000" filter="url(#filter-18)" xlink:href="#path-17"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-17"/>
          </g>
        </g>
        <g class="image_1_border">
          <rect width="24" height="33" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask">
            <mask id="mask-20" fill="#fff">
              <use xlink:href="#path-19"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-19"/>
            <ellipse cx="16.465" cy="6.325" fill="#F3EC60" class="oval" mask="url(#mask-20)" rx="4.186" ry="4.125"/>
            <ellipse cx="19.256" cy="34.1" fill="url(#linearGradient-21)" class="oval" mask="url(#mask-20)" rx="13.116" ry="8.25"/>
            <ellipse cx="-8.651" cy="34.375" fill="url(#linearGradient-22)" class="oval" mask="url(#mask-20)" rx="20.93" ry="12.925"/>
          </g>
          <path fill="#FFF" d="M24 0v33H0V0h24zm-1 1H1v31h22V1z" class="rectangle_2"/>
        </g>
      </g>
    </g>
  </g>
</svg>
