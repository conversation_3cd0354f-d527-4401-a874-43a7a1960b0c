# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# odooers ir, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot display the payment form"
msgstr "نمی‌توان فرم پرداخت را نمایش داد"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__code
msgid "Code"
msgstr "کد"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Connect Stripe"
msgstr "اتصال Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "اتصال با API برقرار نشد."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Delivery"
msgstr "تحویل"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Enable Apple Pay"
msgstr "فعال کردن Apple Pay"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Free Shipping"
msgstr "ارسال رایگان"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Generate your webhook"
msgstr "وب‌هوک خود را تولید کنید"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Get your Secret and Publishable keys"
msgstr "کلیدهای محرمانه و قابل انتشار خود را دریافت کنید"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"اگر یک وب‌هوک در حساب استرایپ شما فعال شده باشد، این رمز امضای باید برای "
"تأیید پیام‌های ارسالی از استرایپ به اودوو تنظیم شود."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect payment details"
msgstr "جزئیات پرداخت نادرست"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "هیچ تراکنشی با مرجع %s یافت نشد."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Other Payment Providers"
msgstr "ارائه دهندگان دیگر پرداخت"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_provider
msgid "Payment Provider"
msgstr "سرویس دهنده پرداخت"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_provider_onboarding
msgid "Payment Providers"
msgstr "سرویس دهندگان پرداخت"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "توکن پرداخت"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "تراکنش پرداخت"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "پردازش پرداخت ناموفق بود"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Please use live credentials to enable Apple Pay."
msgstr "لطفاً از اعتبارنامه‌های واقعی برای فعال‌سازی Apple Pay استفاده کنید."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "Publishable Key"
msgstr "کلید عمومی"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "داده های دریافت شده با وضعیت نیت نامعتبر: %s"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "داده‌های دریافتی با وضعیت تشخیص‌داده نشده."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "دریافت داده‌ها با مرجع بازرگان گمشده"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_secret_key
msgid "Secret Key"
msgstr "کلید محرمانه"

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_provider__code__stripe
msgid "Stripe"
msgstr "استریپ"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"استرایپ کانکت در کشور شما در دسترس نیست، لطفاً از تأمین‌کننده پرداخت دیگری "
"استفاده کنید."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_mandate
msgid "Stripe Mandate"
msgstr "استرایپ وکالت نامه"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "شناسه روش پرداخت استرایپ"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "خطای پروکسی استرایپ: %(error)s"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr "پروکسی استرایپ: یک خطا در هنگام ارتباط با پروکسی رخ داد."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "پراکسی استرایپ: امکان برقراری اتصال وجود ندارد."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"ارتباط با API شکست خورد.\n"
"استرایپ اطلاعات زیر را درباره مشکل به ما داد:\n"
"'%s'"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The customer left the payment page."
msgstr "مشتری صفحه پرداخت را ترک کرد."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr "کلید به طور انحصاری برای شناسایی حساب با Stripe استفاده می‌شود."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The refund did not go through. Please log into your Stripe Dashboard to get "
"more information on that matter, and address any accounting discrepancies."
msgstr ""
"بازپرداخت انجام نشد. لطفاً وارد داشبورد Stripe خود شوید تا اطلاعات بیشتری در"
" این باره به دست آورید و هرگونه ناهماهنگی حسابداری را رسیدگی کنید."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "کد فنی این ارائه دهنده پرداخت."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "تراکنش به یک توکن متصل نیست."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "قادر به تبدیل توکن پرداخت به API جدید نیست."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "راز امضای وب‌هوک"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "وب‌هوک استریپ شما با موفقیت تنظیم شد!"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"شما نمی‌توانید یک وب‌هوک Stripe ایجاد کنید اگر کلید مخفی Stripe شما تنظیم "
"نشده باشد."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot set the provider state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""
"نمی‌توانید وضعیت ارائه‌دهنده را به فعال تغییر دهید تا فرآیند پذیرش شما در "
"Stripe کامل شود."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot set the provider to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"شما نمی‌توانید ارائه‌دهنده را به حالت آزمایشی تنظیم کنید در حالی که به حساب "
"Stripe شما متصل است."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "وبهوک Stripe شما قبلاً تنظیم شده است."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Your order"
msgstr "سفارش شما"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Your web domain was successfully verified."
msgstr "دامنه وب شما با موفقیت تایید شد."
