<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.Navbar">
        <div class="pos-topheader d-flex w-100 p-0 m-0 bg-white border-bottom">
            <div t-if="pos.tempScreen" class="block-top-header position-absolute top-0 start-0 bg-black opacity-50 w-100 h-inherit z-1000" />
            <div t-if="!ui.isSmall" class="pos-branding d-flex justify-content-start flex-grow-1 h-100 p-0 my-0 text-start">
                <img class="pos-logo h-75 ms-3 me-auto align-self-center" t-on-click="() => debug.toggleWidget()" src="/web/static/img/logo.png" alt="Logo" />
            </div>
            <div class="pos-rightheader d-flex flex-grow-1 h-100">
                <BackButton t-if="showBackButton()"/>
                <div class="status-buttons d-flex flex-grow-1 flex-basis-0 justify-content-end h-100">
                    <CashierName />
                    <SaleDetailsButton t-if="hardwareProxy.printer" isHeaderButton="true"/>
                    <ProxyStatus t-if="pos.config.use_proxy" />
                    <CustomerFacingDisplayButton t-if="customerFacingDisplayButtonIsShown" />
                    <SyncNotification />
                </div>
                <div t-if="isBurgerMenuClosed()" class="navbar-button menu-button btn btn-light rounded-0 d-flex align-items-center px-3 cursor-pointer" t-on-click="openMenu">
                    <i class="fa fa-bars" aria-hidden="true"></i>
                </div>
                <div t-else="" class="navbar-button menu menu-button btn btn-light rounded-0 d-flex align-items-center px-3 cursor-pointer" t-on-click="closeMenu">
                    <ul>
                        <li class="menu-item position-relative d-flex">
                            <i class="fa fa-bars" aria-hidden="true"></i>
                            <ul class="dropdown-menu sub-menu position-absolute d-flex flex-column align-items-stretch bg-white shadow-lg z-index-1">
                                <li class="ticket-button" t-on-click="onTicketButtonClick">
                                    <a class="dropdown-item with-badge py-2">
                                        Orders
                                        <span t-if="orderCount>0" class="badge text-bg-info rounded-pill py-1 ms-2" t-esc="orderCount"/>
                                    </a>
                                </li>
                                <li t-if="showCashMoveButton" class="menu-item navbar-button" t-on-click="onCashMoveButtonClick">
                                    <a class="dropdown-item py-2">
                                        Cash In/Out
                                    </a>
                                </li>
                                <li class="menu-item navbar-button" t-on-click="() => pos.toggleImages('product')">
                                    <a class="dropdown-item py-2">
                                        <t t-if="pos.show_product_images">Hide Product Images</t>
                                        <t t-else="">Show Product Images</t>
                                    </a>
                                </li>
                                <li class="menu-item navbar-button" t-on-click="() => pos.toggleImages('category')">
                                    <a class="dropdown-item py-2">
                                        <t t-if="pos.show_category_images">Hide Category Images</t>
                                        <t t-else="">Show Category Images</t>
                                    </a>
                                </li>
                                <CustomerFacingDisplayButton t-if="CustomerFacingDisplayButtonIsShown"/>
                                <li t-if="showToggleProductView" class="menu-item navbar-button" t-on-click="toggleProductView">
                                    <a class="dropdown-item py-2">
                                        Switch Product View
                                    </a>
                                </li>
                                <li class="backend-button" t-on-click="() => pos.closePos()">
                                    <a class="dropdown-item py-2">
                                        Backend
                                    </a>
                                </li>
                                <li class="close-button" t-on-click="closeSession">
                                    <a class="dropdown-item py-2">
                                        Close Session
                                    </a>
                                </li>
                                <li t-if="this.env.debug" class="menu-item navbar-button" t-on-click="() => debug.toggleWidget()">
                                    <a class="dropdown-item py-2">
                                        Debug Window
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </t>

</templates>
