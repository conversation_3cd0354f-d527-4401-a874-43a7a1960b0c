<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="base.module_category_services_project" model="ir.module.category">
        <field name="description">Helps you manage your projects and tasks by tracking them, generating plannings, etc...</field>
        <field name="sequence">3</field>
    </record>

    <record id="group_project_user" model="res.groups">
        <field name="name">User</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="category_id" ref="base.module_category_services_project"/>
    </record>

    <record id="group_project_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="category_id" ref="base.module_category_services_project"/>
        <field name="implied_ids" eval="[(4, ref('group_project_user')), (4, ref('mail.group_mail_template_editor'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <record id="group_project_rating" model="res.groups">
        <field name="name">Use Rating on Project</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_project_stages" model="res.groups">
        <field name="name">Use Stages on Project</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_project_recurring_tasks" model="res.groups">
        <field name="name">Use Recurring Tasks</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_project_task_dependencies" model="res.groups">
        <field name="name">Use Task Dependencies</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_project_milestone" model="res.groups">
        <field name="name">Use Milestones</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

<data noupdate="1">
    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record model="ir.rule" id="project_comp_rule">
        <field name="name">Project: multi-company</field>
        <field name="model_id" ref="model_project_project"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record model="ir.rule" id="project_project_stage_rule">
        <field name="name">Project Stage: multi-company</field>
        <field name="model_id" ref="model_project_project_stage"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record model="ir.rule" id="project_project_manager_rule">
        <field name="name">Project: project manager: see all</field>
        <field name="model_id" ref="model_project_project"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record model="ir.rule" id="project_public_members_rule">
        <field name="name">Project: employees: following required for follower-only projects</field>
        <field name="model_id" ref="model_project_project"/>
        <field name="domain_force">['|',
                                        ('privacy_visibility', '!=', 'followers'),
                                        ('message_partner_ids', 'in', [user.partner_id.id])
                                    ]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record model="ir.rule" id="task_comp_rule">
        <field name="name">Project/Task: multi-company</field>
        <field name="model_id" ref="model_project_task"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record model="ir.rule" id="task_visibility_rule">
        <field name="name">Project/Task: employees: follow required for follower-only projects</field>
        <field name="model_id" ref="model_project_task"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    ('project_id', '!=', False),
                    '|',
                        ('project_id.privacy_visibility', '!=', 'followers'),
                        ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                '|',
                    ('message_partner_ids', 'in', [user.partner_id.id]),
                    # to subscribe check access to the record, follower is not enough at creation
                    ('user_ids', 'in', user.id)
        ]</field>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>

    <record model="ir.rule" id="project_manager_all_project_tasks_rule">
        <field name="name">Project/Task: project manager: see all tasks linked to a project or its own tasks</field>
        <field name="model_id" ref="model_project_task"/>
        <field name="domain_force">[
            '|', ('project_id', '!=', False),
                 ('user_ids', 'in', user.id),
        ]</field>
        <field name="groups" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record model="ir.rule" id="task_type_manager_rule">
        <field name="name">Project/Task Type: manager sees all</field>
        <field name="model_id" ref="model_project_task_type"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record model="ir.rule" id="task_type_visibility_rule">
        <field name="name">Project/Task Type: see own or unowned stages</field>
        <field name="model_id" ref="model_project_task_type"/>
        <field name="domain_force">[('user_id', 'in', (False, user.id))]</field>
    </record>

    <record model="ir.rule" id="task_type_own_write_rule">
        <field name="name">Project/Task Type: write own stages</field>
        <field name="model_id" ref="model_project_task_type"/>
        <field name="domain_force">[('user_id', '=', user.id)]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="groups" eval="[(4,ref('project.group_project_user'))]"/>
    </record>

    <record model="ir.rule" id="report_project_task_user_report_comp_rule">
        <field name="name">Task Analysis multi-company</field>
        <field name="model_id" ref="model_report_project_task_user"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="ir_rule_project_personal_stage_my" model="ir.rule">
        <field name="name">Project: See my own personal stage</field>
        <field name="model_id" ref="project.model_project_task_stage_personal"/>
        <field name="domain_force">[('user_id', '=', user.id)]</field>
    </record>

    <record id="ir_rule_private_task" model="ir.rule">
        <field name="name">Project: See private tasks</field>
        <field name="model_id" ref="project.model_project_task"/>
        <field name="domain_force">[
            ('project_id.privacy_visibility', '!=', 'followers'),
            '|', '|', ('project_id', '!=', False),
                      ('parent_id', '!=', False),
                 ('user_ids', 'in', user.id),
        ]</field>
        <field name="groups" eval="[(4,ref('project.group_project_user'))]"/>
    </record>

     <!-- Portal -->
    <record id="project_project_rule_portal" model="ir.rule">
        <field name="name">Project: portal users: portal and following</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="domain_force">[
            '&amp;',
                ('privacy_visibility', '=', 'portal'),
                ('message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record id="project_collaborator_rule_portal" model="ir.rule">
        <field name="name">Project/Collaborator: portal users: can only see his own collobaroration in shared projects</field>
        <field name="model_id" ref="project.model_project_collaborator"/>
        <field name="domain_force">[
            ('project_id.privacy_visibility', '=', 'portal'),
            ('partner_id', '=', user.partner_id.id),
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record id="project_task_rule_portal" model="ir.rule">
        <field name="name">Project/Task: portal users: (portal and following project) or (portal and following task)</field>
        <field name="model_id" ref="project.model_project_task"/>
        <field name="domain_force">[
        ('project_id.privacy_visibility', '=', 'portal'),
        ('active', '=', True),
        '|',
            ('project_id.message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
            ('message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
        ]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record id="project_task_rule_portal_project_sharing" model="ir.rule">
        <field name="name">Project/Task: portal users: portal user can edit with project sharing feature</field>
        <field name="model_id" ref="project.model_project_task"/>
        <field name="active">0</field>
        <field name="domain_force">[
            ('project_id.privacy_visibility', '=', 'portal'),
            ('active', '=', True),
            '|',
                ('project_id.message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
                ('message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
            ('project_id.collaborator_ids.partner_id', 'in', [user.partner_id.id]),
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record model="ir.rule" id="update_comp_rule">
        <field name="name">Project/Updates: multi-company</field>
        <field name="model_id" ref="model_project_update"/>
        <field name="domain_force">['|', ('project_id.company_id', 'in', company_ids), ('project_id.company_id', '=', False)]</field>
    </record>

    <record model="ir.rule" id="update_visibility_rule">
        <field name="name">Project/Update: employees: follow required for follower-only projects</field>
        <field name="model_id" ref="model_project_update"/>
        <field name="domain_force">[
        '|',
            ('project_id.privacy_visibility', '!=', 'followers'),
            '|',
                ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                '|',
                    ('user_id', '=', user.id),
                    ('project_id.user_id', '=', user.id)
        ]</field>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>

    <record model="ir.rule" id="report_project_task_user_rule">
        <field name="name">Tasks Analysis: project visibility User</field>
        <field name="model_id" ref="model_report_project_task_user"/>
        <field name="domain_force">[
        '|',
            ('project_id.privacy_visibility', '!=', 'followers'),
            '|',
                ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                '|',
                    ('task_id.message_partner_ids', 'in', [user.partner_id.id]),
                    ('user_ids', 'in', user.id),
        ]</field>
        <field name="groups" eval="[(4,ref('project.group_project_user'))]"/>
    </record>

    <record model="ir.rule" id="report_project_task_manager_rule">
        <field name="name">Tasks Analysis: project visibility Manager</field>
        <field name="model_id" ref="model_report_project_task_user"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record id="update_visibility_project_admin" model="ir.rule">
        <field name="name">Project updates : Project user can see all project updates</field>
        <field name="model_id" ref="project.model_project_update"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record model="ir.rule" id="burndown_chart_project_user_rule">
        <field name="name">Burndown chart: project visibility User</field>
        <field name="model_id" ref="model_project_task_burndown_chart_report"/>
        <field name="domain_force">[
        '|',
            ('project_id.privacy_visibility', '!=', 'followers'),
            '|',
                ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                ('user_ids', 'in', user.id),
        ]</field>
        <field name="groups" eval="[(4,ref('project.group_project_user'))]"/>
    </record>

    <record model="ir.rule" id="burndown_chart_project_manager_rule">
        <field name="name">Burndown chart: project visibility User</field>
        <field name="model_id" ref="model_project_task_burndown_chart_report"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4,ref('project.group_project_manager'))]"/>
    </record>

    <record model="ir.rule" id="milestone_comp_rule">
        <field name="name">Project/Milestone: multi-company</field>
        <field name="model_id" ref="model_project_milestone"/>
        <field name="domain_force">['|', ('project_id.company_id', 'in', company_ids), ('project_id.company_id', '=', False)]</field>
    </record>

    <record model="ir.rule" id="milestone_visibility_rule">
        <field name="name">Project/Milestone: employees: follow required for follower-only projects</field>
        <field name="model_id" ref="model_project_milestone"/>
        <field name="domain_force">[
        '|',
            ('project_id.privacy_visibility', '!=', 'followers'),
            '|',
                ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                ('project_id.user_id', '=', user.id),
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="milestone_visibility_project_admin" model="ir.rule">
        <field name="name">Project/Milestone: Project manager can see all project milestones</field>
        <field name="model_id" ref="project.model_project_milestone"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('project.group_project_manager'))]"/>
    </record>

    <record id="project_milestone_rule_portal_project_sharing" model="ir.rule">
        <field name="name">Project/milestone portal users: portal user can read with project sharing feature</field>
        <field name="model_id" ref="project.model_project_milestone"/>
        <field name="domain_force">[
            ('project_id.privacy_visibility', '=', 'portal'),
            ('project_id.collaborator_ids.partner_id', 'in', [user.partner_id.id]),
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record id="mail_plan_rule_group_project_manager_task" model="ir.rule">
        <field name="name">Manager can manage project/task plans</field>
        <field name="groups" eval="[(4, ref('group_project_manager'))]"/>
        <field name="model_id" ref="mail.model_mail_activity_plan"/>
        <field name="domain_force">[('res_model', 'in', ('project.project', 'project.task'))]</field>
        <field name="perm_read" eval="False"/>
    </record>

    <record id="mail_plan_templates_rule_group_project_manager_task" model="ir.rule">
        <field name="name">Manager can manage project/task plan templates</field>
        <field name="groups" eval="[(4, ref('group_project_manager'))]"/>
        <field name="model_id" ref="mail.model_mail_activity_plan_template"/>
        <field name="domain_force">[('plan_id.res_model', 'in', ('project.project', 'project.task'))]</field>
        <field name="perm_read" eval="False"/>
    </record>

</data>
</odoo>
