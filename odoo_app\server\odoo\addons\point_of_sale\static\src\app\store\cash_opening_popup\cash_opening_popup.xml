<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.CashOpeningPopup">
        <div class="popup opening-cash-control">
            <div class="modal-header drag-handle">
                <h4 class="modal-title">Opening Cash Control</h4>
            </div>
            <main class="modal-body">
                <div class="opening-cash-section mb-3">
                    <span class="info-title d-flex flex-grow-1 align-items-start">Opening cash</span>
                    <div class="cash-input-sub-section input-group">
                        <Input tModel="[state, 'openingCash']"
                            isValid.bind="env.utils.isValidFloat"
                            callback.bind="handleInputChange"
                            autofocus="true" />
                        <div class="button icon btn btn-secondary" t-on-click="openDetailsPopup">
                            <i class="fa fa-money fa-2x" role="img" title="Open the money details popup"/>
                        </div>
                    </div>
                </div>
                <div class="opening-notes-container d-flex flex-column align-items-start">
                    <label class="form-label" for="openingNotes">Opening note</label>
                    <textarea class="opening-notes form-control" id="openingNotes" rows="4" t-model="state.notes" placeholder="Add an opening note..." />
                </div>
            </main>
            <footer class="footer modal-footer">
                <button class="button btn btn-lg btn-primary"
                    t-on-click="confirm"
                    t-att-disabled="!env.utils.isValidFloat(state.openingCash)"
                    >
                    Open session
                </button>
            </footer>
        </div>
    </t>
</templates>
