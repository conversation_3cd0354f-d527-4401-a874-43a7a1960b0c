# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_stock_picking_batch
# 
# Translators:
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2022
# Сергей Шебан<PERSON>н <<EMAIL>>, 2022
# <PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-19 09:20+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid ""
"A transfer will not be automatically added to batches that will exceed this weight if the transfer is added to it.\n"
"Leave this value as '0' if no weight limit."
msgstr ""
"Трансфер не будет автоматически добавляться к партиям, вес которых превысит этот предел, если трансфер будет добавлен к ним.\n"
"Оставьте это значение равным '0', если ограничение по весу отсутствует."

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Automatically group batches by carriers"
msgstr "Автоматическая группировка партий по перевозчикам"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_batch
msgid "Batch Transfer"
msgstr "Групповое перемещение"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Carrier"
msgstr "Перевозчик"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid "Maximum weight per batch"
msgstr "Максимальный вес одной партии"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Тип комплектования"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking
msgid "Transfer"
msgstr "Перемещение"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Метка единицы измерения веса"
