# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid ""
"\n"
"\n"
"Note: products that you don't have access to will not be shown above."
msgstr ""
"\n"
"\n"
"หมายเหตุ: ผลิตภัณฑ์ที่คุณไม่สามารถเข้าถึงได้จะไม่แสดงด้านบน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# ตัวเลือกสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# สินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$14.00"
msgstr "$14.00"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$15.00"
msgstr "$15.00"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid ""
"%(base)s with a %(discount)s %% discount and %(surcharge)s extra fee\n"
"Example: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"
msgstr ""
"%(base)s พร้อมส่วนลด %(discount)s %% และค่าธรรมเนียมเพิ่มเติม %(surcharge)s \n"
"ตัวอย่าง: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "%(percentage)s %% discount and %(price)s surcharge"
msgstr "%(percentage)s %% ส่วนลดและ %(price)s ค่าธรรมเนียมเพิ่มเติม"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "%s %% discount"
msgstr "ลดราคา %s %%"

#. module: product
#. odoo-python
#: code:addons/product/models/product_tag.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "%s: end date (%s) should be greater than start date (%s)"
msgstr "%s: วันที่สิ้นสุด (%s) ควรมากกว่าวันที่เริ่มต้น (%s)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_template_label_2x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12_noprice
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_dymo
msgid "'Products Labels - %s' % (object.name)"
msgstr "'ฉลากสินค้า - %s' % (object.name)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_packaging
msgid "'Products packaging - %s' % (object.name)"
msgstr "'บรรจุภัณฑ์สินค้า - %s' % (object.name)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "(e.g: product description, ebook, legal notice, ...)."
msgstr "(เช่น รายละเอียดสินค้า, eBook, ประกาศทางกฎหมาย, ...)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid "- Barcode \"%s\" already assigned to product(s): %s"
msgstr "- บาร์โค้ด \"%s\" กำหนดให้กับสินค้าแล้ว: %s"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid ""
"- Instantly: All possible variants are created as soon as the attribute and its values are added to a product.\n"
"        - Dynamically: Each variant is created only when its corresponding attributes and values are added to a sales order.\n"
"        - Never: Variants are never created for the attribute.\n"
"        Note: the variants creation mode cannot be changed once the attribute is used on at least one product."
msgstr ""
"- ทันที: ตัวเลือกย่อยสินค้าที่เป็นไปได้ทั้งหมดจะถูกสร้างขึ้นทันทีที่มีการเพิ่มคุณสมบัติและมูลค่าให้กับสินค้า\n"
"- ไดนามิก: แต่ละตัวเลือกย่อยจะถูกสร้างขึ้นเมื่อมีการเพิ่มคุณสมบัติและมูลค่าที่เกี่ยวข้องลงในใบสั่งขายเท่านั้น\n"
"- ไม่เคย: ไม่มีการสร้างตัวเลือกย่อยสำหรับคุณสมบัติ\n"
"หมายเหตุ: ไม่สามารถเปลี่ยนโหมดการสร้างตัวเลือกสินค้าได้เมื่อใช้คุณสมบัติกับสินค้าอย่างน้อยหนึ่งรายการ"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr "1 ปี"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "10"
msgstr "10"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "10 Units"
msgstr "10 หน่วย"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "123456789012"
msgstr "123456789012"

#. module: product
#: model:product.template,description_sale:product.product_product_4_product_template
msgid "160x80cm, with large legs."
msgstr "160x80ซม ขาใหญ่"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 พร้อมราคา"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr "2 ปี"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 พร้อมราคา"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 พร้อมราคา"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-"
"label=\"ไอคอนลูกศร\" title=\"ลูกศร\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"
msgstr "<i class=\"fa fa-money\" role=\"รูปภาพ\" aria-label=\"สกุลเงิน\" title=\"สกุลเงิน\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        Extra Prices\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        Extra Price\n"
"                                    </span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        ราคาเพิ่มเติม\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        ราคาเพิ่มเติม\n"
"                                    </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> สินค้า</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "<span class=\"o_stat_text\">Related Products</span>"
msgstr "<span class=\"o_stat_text\">สินค้าที่เกี่ยวข้อง</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">เก็บถาวรแล้ว</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "<span class=\"text-bg-secondary\">Variant</span>"
msgstr "<span class=\"text-bg-secondary\">ตัวแปร</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>All general settings about this product are managed on</span>"
msgstr "<span>การตั้งค่าทั่วไปทั้งหมดเกี่ยวกับสินค้านี้ได้รับการจัดการ</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "<span>Variant </span>"
msgstr "<span>ตัวแปร</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr "<strong>จำนวน: </strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back\n"
"                                here to set up the feature."
msgstr ""
"<strong>บันทึก</strong> หน้านี้แล้วกลับมา\n"
"                                ที่นี่เพื่อตั้งค่าคุณฟีเจอร์"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""
"<strong>คำเตือน</strong>: การเพิ่มหรือลบคุณสมบัติ\n"
"                        จะลบและสร้างตัวเลือกย่อยที่มีอยู่ขึ้นใหม่\n"
"                        และทำให้การปรับแต่งที่เป็นไปได้หายไป"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_barcode_uniq
msgid "A barcode can only be assigned to one packaging."
msgstr "สามารถกำหนดบาร์โค้ดให้กับบรรจุภัณฑ์เดียวเท่านั้น"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"คำอธิบายของผลิตภัณฑ์ที่คุณต้องการสื่อสารกับลูกค้าของคุณ "
"คำอธิบายนี้จะถูกคัดลอกไปยังทุกใบสั่งขาย ใบส่งสินค้า "
"และใบกำกับสินค้า/ใบลดหนี้ลูกค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid "A packaging already uses the barcode"
msgstr "บรรจุภัณฑ์ใช้บาร์โค้ดอยู่แล้ว"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"            This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""
"ราคาคือชุดของราคาขายหรือกฎเพื่อคำนวณราคาของรายการใบสั่งขายตามสินค้า ประเภทสินค้า วันที่ และปริมาณที่สั่ง\n"
"          นี่เป็นเครื่องมือที่สมบูรณ์แบบในการจัดการราคาต่างๆ ส่วนลดตามเทศกาล และอื่นๆ"

#. module: product
#. odoo-python
#: code:addons/product/models/product_packaging.py:0
#, python-format
msgid "A product already uses the barcode"
msgstr "สินค้าใช้บาร์โค้ดอยู่แล้ว"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__detailed_type
#: model:ir.model.fields,help:product.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"สินค้าที่จัดเก็บได้คือสินค้าที่ทคุณจัดการสต๊อก จำเป็นต้องติดตั้งแอปคลังสินค้า\n"
"สินค้าบริโภคคือสินค้าที่ไม่มีการจัดการสต๊อก\n"
"บริการคือสินค้าที่ไม่ใช่วัตถุที่คุณจัดหา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__access_token
msgid "Access Token"
msgstr "โทเคนเข้าถึง"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget"
msgstr "วิดเจ็ต Acme"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget - Blue"
msgstr "วิดเจ็ต Acme - สีฟ้า"

#. module: product
#: model:product.template,name:product.product_product_25_product_template
msgid "Acoustic Bloc Screens"
msgstr "หน้าจอบล็อกเสียง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__active
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_active
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Active Products"
msgstr "สินค้าที่ใช้งานอยู่"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_ids
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_state
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "Add"
msgstr "เพิ่ม"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Add a quantity"
msgstr "เพิ่มปริมาณ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__additional_product_tag_ids
msgid "Additional Product Tags"
msgstr "แท็กสินค้าเพิ่มเติม"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_sale_pricelist
#: model:res.groups,name:product.group_sale_pricelist
msgid "Advanced Pricelists"
msgstr "รายการราคาขั้นสูง"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__advanced
msgid "Advanced price rules (discounts, formulas)"
msgstr "กฎราคาขั้นสูง (ส่วนลด สูตร)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__all_product_tag_ids
msgid "All Product Tag"
msgstr "แท็กสินค้าทั้งหมด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_ids
msgid "All Product Variants using this Tag"
msgstr "ตัวเลือกย่อยสินค้าทั้งหมดที่ใช้แท็กนี้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__3_global
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
#, python-format
msgid "All Products"
msgstr "สินค้าทั้งหมด"

#. module: product
#. odoo-python
#: code:addons/product/controllers/product_document.py:0
#, python-format
msgid "All files uploaded"
msgstr "อัปโหลดไฟล์ทั้งหมด"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:product.field_product_template_attribute_value__is_custom
msgid "Allow users to input custom values for this attribute value"
msgstr "อนุญาตให้ผู้ใช้กรอกค่าที่กำหนดเองสำหรับค่าคุณสมบัตินี้"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""
"อนุญาตให้สามารถจัดการราคาที่แตกต่างกันตามกฎแต่ละประเภทของลูกค้า\n"
"                ตัวอย่าง: 10% สำหรับผู้ค้าปลีก โปรโมชั่น 5 ยูโร สำหรับสินค้านี้ และอื่นๆ"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "Aluminium"
msgstr "อลูมิเนียม"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr "นำไปใช้ได้กับ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
msgid "Applied On"
msgstr "นำไปใช้เมื่อ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "ใช้เมื่อ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "กำหนดลำดับความสำคัญให้กับรายชื่อผู้จำหน่ายสินค้า"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid ""
"At most %s quantities can be displayed simultaneously. Remove a selected "
"quantity to add others."
msgstr ""
"สามารถแสดงปริมาณได้สูงสุด %s พร้อมกัน "
"และลบจำนวนที่เลือกเพื่อเพิ่มรายการอื่นๆ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Attached To"
msgstr "แนบกับ"

#. module: product
#: model:ir.model,name:product.model_ir_attachment
msgid "Attachment"
msgstr "การแนบ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__local_url
msgid "Attachment URL"
msgstr "แนบไฟล์ URL"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "คุณสมบัติ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_line_id
msgid "Attribute Line"
msgstr "รายการคุณสมบัติ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "ชื่อคุณสมบัติ"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
msgid "Attribute Value"
msgstr "ค่าคุณสมบัติ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_list
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "ค่าคุณสมบัติ"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Attributes"
msgstr "คุณสมบัติ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Attributes & Variants"
msgstr "คุณสมบัติและตัวเลือกย่อยสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Availability"
msgstr "ความพร้อม"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
#, python-format
msgid "Back to Order"
msgstr "กลับไปที่การสั่งซื้อ"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
#, python-format
msgid "Back to Quotation"
msgstr "กลับไปที่ใบเสนอราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
msgid "Barcode"
msgstr "บาร์โค้ด"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid ""
"Barcode used for packaging identification. Scan this packaging barcode from "
"a transfer in the Barcode app to move all the contained units"
msgstr ""
"บาร์โค้ดที่ใช้ในการระบุบรรจุภัณฑ์ "
"สแกนบาร์โค้ดบรรจุภัณฑ์นี้จากการถ่ายโอนในแอปบาร์โค้ดเพื่อย้ายหน่วยที่มีอยู่ทั้งหมด"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid ""
"Barcode(s) already assigned:\n"
"\n"
"%s"
msgstr ""
"กำหนดบาร์โค้ดแล้ว:\n"
"\n"
"%s"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Sales Price: The base price will be the Sales Price.\n"
"Cost Price: The base price will be the cost price.\n"
"Other Pricelist: Computation of the base price based on another Pricelist."
msgstr ""
"ราคาฐานสำหรับการคำนวณ\n"
"ราคาขาย: ราคาฐานจะเป็นราคาขาย\n"
"ราคาต้นทุน: ราคาฐานจะเป็นราคาต้นทุน\n"
"รายการราคาอื่นๆ: การคำนวณราคาฐานตามรายการราคาอื่น"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "อิงตาม"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Basic Pricelists"
msgstr "รายการราคาพื้นฐาน"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "สีดำ"

#. module: product
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr "ตู้มีประตู"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:product.field_product_template__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "สามารถขยายภาพ 1024 ได้หรือไม่"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr "สามารถขยายภาพตัวเลือกย่อยสินค้า 1024 ได้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Purchased"
msgstr "สามารถซื้อ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "สามารถขายได้"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category"
msgstr "หมวดหมู่"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Category: %s"
msgstr "หมวด: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__checksum
msgid "Checksum/SHA1"
msgstr "ตรวจสอบ/SHA1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "กลุ่มย่อย"

#. module: product
#: model:ir.actions.act_window,name:product.action_open_label_layout
msgid "Choose Labels Layout"
msgstr "เลือกรูปแบบของฉลาก"

#. module: product
#: model:ir.model,name:product.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "เลือกรูปแบบของแผ่นงานเพื่อพิมพ์ฉลาก"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "โค้ด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__html_color
#: model:ir.model.fields,field_description:product.field_product_tag__color
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__color
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__color
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "สี"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__color
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__columns
msgid "Columns"
msgstr "คอลัมน์"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__combination_indices
msgid "Combination Indices"
msgstr "ดัชนีผสม"

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__company_id
#: model:ir.model.fields,field_description:product.field_product_packaging__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "บริษัท"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "ชื่อเต็ม"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Computation"
msgstr "การคำนวณ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
msgid "Compute Price"
msgstr "คำนวันนี้ราคา"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Conditions"
msgstr "เงื่อนไข"

#. module: product
#: model:product.template,name:product.product_product_11_product_template
msgid "Conference Chair"
msgstr "เก้าอี้ประชุม"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "Conference room table"
msgstr "โต๊ะห้องประชุม"

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Configure"
msgstr "กำหนดค่า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Configure tags"
msgstr "กำหนดค่าแท็ก"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__detailed_type__consu
#: model:ir.model.fields.selection,name:product.selection__product_template__type__consu
msgid "Consumable"
msgstr "บริโภคภัณฑ์"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"Consumables are physical products for which you don't manage the inventory "
"level: they are always available."
msgstr ""
"สินค้าอุปโภคบริโภคคือสินค้าที่จับต้องได้ซึ่งคุณไม่ต้องจัดการระดับสินค้าคงคลัง:"
" สินค้าจะพร้อมจำหน่ายเสมอ"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Contact Us"
msgstr "ติดต่อเรา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr "ปริมาณที่บรรจุอยู่"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_positive_qty
msgid "Contained Quantity should be positive."
msgstr "ปริมาณที่มีอยู่ควรมีค่าเป็นบวก"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr "ปริมาณที่บรรจุอยู่"

#. module: product
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Left Sit"
msgstr "โต๊ะเข้ามุมที่นั่งซ้าย"

#. module: product
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr "โต๊ะเข้ามุมที่นั่งขวา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__standard_price
msgid "Cost"
msgstr "ต้นทุน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__cost_currency_id
#: model:ir.model.fields,field_description:product.field_product_template__cost_currency_id
msgid "Cost Currency"
msgstr "ต้นทุนสกุลเงิน"

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "กลุ่มประเทศ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "กลุ่มประเทศ"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr "สร้างรายการราคาใหม่"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "สร้างสินค้าใหม่"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr "สร้างตัวเลือกย่อยสินค้าใหม่"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
#, python-format
msgid "Create a product"
msgstr "สร้างผลิตภัณฑ์"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_document__create_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_tag__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_document__create_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_tag__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Creation"
msgstr "การสร้าง"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__1
msgid "Cubic Feet"
msgstr "ลูกบาศก์ฟุต"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__0
msgid "Cubic Meters"
msgstr "ลูกบาศก์เมตร"

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_value
msgid "Custom Value"
msgstr "ค่าที่กำหนดเอง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "อ้างอิงลูกค้า"

#. module: product
#: model:product.template,name:product.product_product_4_product_template
msgid "Customizable Desk"
msgstr "โต๊ะปรับแต่งได้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__db_datas
msgid "Database Data"
msgstr "ฐานข้อมูล"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "ความถูกต้องของทศนิยม"

#. module: product
#. odoo-python
#: code:addons/product/models/res_company.py:0
#, python-format
msgid "Default %s pricelist"
msgstr "รายการราคาเริ่มต้น %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__default_extra_price
msgid "Default Extra Price"
msgstr "ราคาเพิ่มเติมเริ่มต้น"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "หน่วยวัดเริ่มต้นที่ใช้สำหรับการปฏิบัติการสต๊อกทั้งหมด"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""
"หน่วยวัดเริ่มต้นที่ใช้สำหรับใบสั่งซื้อ "
"ต้องอยู่ในหมวดหมู่เดียวกันกับหน่วยวัดเริ่มต้น"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Define a new tag"
msgstr "กำหนดแท็กใหม่"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your volume unit of measure"
msgstr "กำหนดหน่วยวัดปริมาตรของคุณ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your weight unit of measure"
msgstr "กำหนดหน่วยวัดน้ำหนักของคุณ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Delete"
msgstr "ลบ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "เวลานำการจัดส่ง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__description
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: product
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr "การรวมโต๊ะ"

#. module: product
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr "ขาตั้งโต๊ะพร้อมฉากกั้น"

#. module: product
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr "ชุดโต๊ะทำงาน, น้ำตาลดำ: เก้าอี้ + โต๊ะ + ลิ้นชัก"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
msgid "Determine the display order"
msgstr "กำหนดลำดับการแสดงผล"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__percentage
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Discount"
msgstr "ส่วนลด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__discount
msgid "Discount (%)"
msgstr "ส่วนลด (%)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__discount_policy
msgid "Discount Policy"
msgstr "นโยบายส่วนลด"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__with_discount
msgid "Discount included in the price"
msgstr "ส่วนลดรวมอยู่ในราคาแล้ว"

#. module: product
#: model:res.groups,name:product.group_discount_per_so_line
msgid "Discount on lines"
msgstr "ส่วนลดทางรายการ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price_discounted
msgid "Discounted Price"
msgstr "ลดราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_discount_per_so_line
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Discounts"
msgstr "ลดราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_document__display_name
#: model:ir.model.fields,field_description:product.field_product_label_layout__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_tag__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Display Pricelist"
msgstr "แสดงรายการราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_type
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_type
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_type
msgid "Display Type"
msgstr "ประเภทการแสดง"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Document"
msgstr "เอกสาร"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#: model:ir.model.fields,field_description:product.field_product_product__product_document_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_document_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#, python-format
msgid "Documents"
msgstr "เอกสาร"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_document_count
#: model:ir.model.fields,field_description:product.field_product_template__product_document_count
msgid "Documents Count"
msgstr "จำนวนเอกสาร"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
msgid "Documents of this variant"
msgstr "เอกสารของตัวแปรนี้"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Download examples"
msgstr "ดาวน์โหลดตัวอย่าง"

#. module: product
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr "ลิ้นชัก"

#. module: product
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr "ลิ้นชัก สีดำ"

#. module: product
#: model_terms:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr "ลิ้นชักที่มีความเป็นไปได้ในการกำหนดเส้นทางสองแบบ"

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "ระยะเวลา"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__dynamic
msgid "Dynamically"
msgstr "ไดนามิก"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_template_attribute_value_attribute_value_unique
msgid "Each value should be defined only once per attribute per product."
msgstr "ควรกำหนดค่าแต่ละค่าเพียงครั้งเดียวต่อคุณสมบัติต่อสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Eco-friendly Wooden Chair"
msgstr "เก้าอี้ไม้ที่เป็นมิตรต่อสิ่งแวดล้อม"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
msgid "Edit"
msgstr "แก้ไข"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "วันที่สิ้นสุด"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "วันที่สิ้นสุดสำหรับราคาของผู้ขายนี้"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid ""
"Ending datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"วันที่และเวลาสิ้นสุดสำหรับการตรวจสอบรายการของรายการราคา\n"
"ค่าที่แสดงขึ้นอยู่กับเขตเวลาที่ตั้งค่าไว้ในการตั้งค่าของคุณ"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Ergonomic"
msgstr "ตามหลักสรีรศาสตร์"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr "ยกเว้นสำหรับ"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "ชื่อกฎที่ชัดเจนสำหรับรายการของรายการราคานี้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__extra_html
msgid "Extra Content"
msgstr "เนื้อหาเพิ่มเติม"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Extra Fee"
msgstr "ค่าธรรมเนียมเพิ่มเติม"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Extra price for the variant with this attribute value on sale price. eg. 200"
" price extra, 1000 + 200 = 1200."
msgstr ""
"ราคาเพิ่มเติมสำหรับตัวเลือกย่อยสินค้าพร้อมค่าของคุณสมบัตินี้ในราคาขาย เช่น "
"ราคาเพิ่มเติม 200 เป็น 1,000 + 200 = 1,200"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__priority
#: model:ir.model.fields,field_description:product.field_product_template__priority
#: model:ir.model.fields.selection,name:product.selection__product_template__priority__1
msgid "Favorite"
msgstr "รายการโปรด"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Favorites"
msgstr "รายการโปรด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__datas
msgid "File Content (base64)"
msgstr "เนื้อหาไฟล์ (base64)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__raw
msgid "File Content (raw)"
msgstr "เนื้อหาไฟล์ (raw)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__file_size
msgid "File Size"
msgstr "ขนาดไฟล์"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__fixed
msgid "Fixed Price"
msgstr "ราคาคงที่"

#. module: product
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr "พลิก"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,help:product.field_product_product__activity_type_icon
#: model:ir.model.fields,help:product.field_product_template__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"เพื่อให้กฎมีผล ปริมาณการซื้อ/ขายจะต้องมากกว่าหรือเท่ากับปริมาณขั้นต่ำที่ระบุในฟิลด์นี้\n"
"แสดงเป็นหน่วยวัดเริ่มต้นของสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__print_format
msgid "Format"
msgstr "รูปแบบ"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__formula
msgid "Formula"
msgstr "สูตร"

#. module: product
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr "โต๊ะสี่คน"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid "Four person modern office workstation"
msgstr "เวิร์กสเตชันสำนักงานที่ทันสมัยสำหรับสี่คน"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "ข้อมูลทั่วไป"

#. module: product
#: model:ir.actions.server,name:product.action_product_price_list_report
#: model:ir.actions.server,name:product.action_product_template_price_list_report
msgid "Generate Pricelist Report"
msgstr "สร้างรายงานของรายการราคา"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Get product pictures using Barcode"
msgstr "รับรูปภาพสินค้าโดยใช้บาร์โค้ด"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "นำเสนอวิธีบรรจุผลิตภัณฑ์เดียวกัน"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "ให้ลำดับเมื่อแสดงรายการสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Gold Member Pricelist"
msgstr "รายการราคาสมาชิกระดับ Gold"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Google Images"
msgstr "Google Images"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr "ดัชนีสี HTML"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__has_message
#: model:ir.model.fields,field_description:product.field_product_product__has_message
#: model:ir.model.fields,field_description:product.field_product_template__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__html_color
#: model:ir.model.fields,help:product.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color if the attribute type is 'Color'."
msgstr ""
"ที่นี่คุณสามารถตั้งค่าดัชนีสี HTML เฉพาะ (เช่น #ff0000) "
"เพื่อแสดงสีหากประเภทคุณสมบัติเป็น 'สี' ได้"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "History"
msgstr "ประวัติ"

#. module: product
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "ที่พักโรงแรม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_document__id
#: model:ir.model.fields,field_description:product.field_product_label_layout__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_tag__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
msgid "ID"
msgstr "ไอดี"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr ""
"หากไม่ได้กำหนด ราคาของผู้ขายจะนำไปใช้กับตัวเลือกย่อยทุกรายการของสินค้านี้"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr ""
"หากไม่ได้ทำเครื่องหมาย จะทำให้คุณสามารถซ่อนรายการราคาได้โดยไม่ต้องลบออก"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "หากไม่ได้ทำเครื่องหมาย จะทำให้คุณสามารถซ่อนสินค้าได้โดยไม่ต้องลบออก"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__image
#: model:ir.model.fields,field_description:product.field_product_product__image_1920
#: model:ir.model.fields,field_description:product.field_product_template__image_1920
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__image
msgid "Image"
msgstr "รูปภาพ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1024
#: model:ir.model.fields,field_description:product.field_product_template__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_128
#: model:ir.model.fields,field_description:product.field_product_template__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_256
#: model:ir.model.fields,field_description:product.field_product_template__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_512
#: model:ir.model.fields,field_description:product.field_product_template__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_height
msgid "Image Height"
msgstr "ความสูงของภาพ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_src
msgid "Image Src"
msgstr "รูปภาพ Src"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_width
msgid "Image Width"
msgstr "ความกว้างรูปภาพ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Image is a link"
msgstr "รูปภาพคือลิงก์"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Import Template for Pricelists"
msgstr "นำเข้าเทมเพลตสำหรับรายการราคา"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "นำเข้าเทมเพลตสำหรับสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_supplierinfo.py:0
#, python-format
msgid "Import Template for Vendor Pricelists"
msgstr "นำเข้าเทมเพลตสำหรับรายการราคาของผู้ขาย"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Inactive"
msgstr "ปิดใช้งาน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__index_content
msgid "Indexed Content"
msgstr "ดัชนีเนื้อหา"

#. module: product
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr "สถานที่ทำงานส่วนบุคคล"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__always
msgid "Instantly"
msgstr "ทันที"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "บันทึกย่อภายใน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "อ้างอิงภายใน"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
msgid "International Article Number used for product identification."
msgstr "หมายเลขบทความสากลที่ใช้เพื่อระบุสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "คลังสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr "เป็นตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_configurable_attributes
#: model:ir.model.fields,field_description:product.field_product_template__has_configurable_attributes
msgid "Is a configurable product"
msgstr "เป็นสินค้าที่สามารถกำหนดค่าได้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr "เป็นตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__is_custom
msgid "Is custom value"
msgstr "เป็นค่าที่กำหนดเอง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__public
msgid "Is public document"
msgstr "เป็นเอกสารสาธารณะ"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__0
msgid "Kilograms"
msgstr "กิโลกรัม"

#. module: product
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr "ตู้ใหญ่"

#. module: product
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr "โต๊ะใหญ่"

#. module: product
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr "โต๊ะประชุมขนาดใหญ่"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_document__write_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_tag__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_document__write_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_tag__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "กิจกรรมล่าสุด"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"ระยะเวลาดำเนินการเป็นวันระหว่างการยืนยันคำสั่งซื้อและการรับสินค้าในคลังสินค้าของคุณ"
" ใช้โดยตัวกำหนดตารางเวลาสำหรับการคำนวณอัตโนมัติของการวางแผนใบสั่งซื้อ"

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr "ขาโต๊ะ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_attribute_value__pav_attribute_line_ids
msgid "Lines"
msgstr "รายการ"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Locally handmade"
msgstr "งานฝีมือท้องถิ่น"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Logistics"
msgstr "โลจิสติกส์"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""
"กำลังมองหาลายไม้ไผ่แบบกำหนดเองเพื่อให้เข้ากับเฟอร์นิเจอร์ที่มีอยู่หรือไม่? "
"ติดต่อเราเพื่อขอใบเสนอราคา"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with other values of the product or"
" some attribute values of optional and accessory products."
msgstr ""
"ทำให้ค่าคุณสมบัตินี้เข้ากันไม่ได้กับค่าอื่นของสินค้าหรือค่าคุณสมบัติบางรายการของสินค้าเสริมและอุปกรณ์เสริม"

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "จัดการบรรจุภัณฑ์สินค้า"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "จัดการตัวเลือกย่อยสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Margins"
msgstr "มาร์จิ้น"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "มาร์จิ้นสูงสุด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "ราคามาร์จิ้นสูงสุด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__mimetype
msgid "Mime Type"
msgstr "ประเภท Mime"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "มาร์จิ้นขั้นต่ำ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "ราคามาร์จิ้นขั้นต่ำ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "จำนวนขั้นต่ำ"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__multi
msgid "Multi-checkbox (option)"
msgstr "ช่องทำเครื่องหมายหลายช่อง (ตัวเลือก)"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_check_multi_checkbox_no_variant
msgid ""
"Multi-checkbox display type is not compatible with the creation of variants"
msgstr ""
"ประเภทการแสดงช่องทำเครื่องหมายหลายรายการเข้ากันไม่ได้กับการสร้างรูปแบบตัวเลือกสินค้า"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__basic
msgid "Multiple prices per product"
msgstr "หลายราคาต่อสินค้า"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__product_pricelist_setting
msgid ""
"Multiple prices: Pricelists with fixed price rules by product,\n"
"Advanced rules: enables advanced price rules for pricelists."
msgstr ""
"หลายราคา: รายการราคาที่มีกฎราคาคงที่ตามสินค้า\n"
"กฎขั้นสูง: เปิดใช้งานกฎราคาขั้นสูงสำหรับรายการราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__name
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_document__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_tag__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "ชื่อ"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__no_variant
msgid "Never (option)"
msgstr "ไม่เคย (ตัวเลือก)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_summary
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid ""
"No product to print, if the product is archived please unarchive it before "
"printing its label."
msgstr ""
"ไม่มีสินค้าที่จะพิมพ์ หากสินค้าถูกเก็บถาวรแล้ว "
"โปรดยกเลิกการเก็บถาวรก่อนพิมพ์ฉลาก"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
#, python-format
msgid "No products could be found."
msgstr "ไม่พบสินค้า"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid "No vendor pricelist found"
msgstr "ไม่พบรายการราคาของผู้ขาย"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__priority__0
msgid "Normal"
msgstr "ปกติ"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Note:"
msgstr "โน๊ต:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__number_related_products
msgid "Number Related Products"
msgstr "จำนวนสินค้าที่เกี่ยวข้อง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_count
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_item_count
msgid "Number of price rules"
msgstr "จำนวนกฎราคา"

#. module: product
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr "เก้าอี้สำนักงาน"

#. module: product
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr "เก้าอี้สำนักงานสีดำ"

#. module: product
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr "ซอฟต์แวร์การออกแบบสำนักงาน"

#. module: product
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr "โคมไฟสำนักงาน"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"On the product %(product)s you cannot associate the value %(value)s with the"
" attribute %(attribute)s because they do not match."
msgstr ""
"ในสินค้า %(product)s คุณไม่สามารถเชื่อมโยงค่า %(value)s กับคุณสมบัติ "
"%(attribute)s ได้ เนื่องจากค่าไม่ตรงกัน"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"On the product %(product)s you cannot transform the attribute "
"%(attribute_src)s into the attribute %(attribute_dest)s."
msgstr ""
"ในสินค้า %(product)s คุณไม่สามารถแปลงคุณสมบัติ %(attribute_src)s "
"ให้เป็นคุณสมบัติ %(attribute_dest)s ได้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "ต้นฉบับ (ไม่ได้ปรับให้เหมาะสม ไม่ได้ปรับขนาด) แนบไฟล์"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__pricelist
msgid "Other Pricelist"
msgstr "รายการราคาอื่นๆ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Package Type A"
msgstr "แพ็คเกจประเภท A"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "การบรรจุหีบห่อ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "หมวดหมู่หลัก"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr "เส้นทางหลัก"

#. module: product
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr "ถังเหยียบ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "ราคาร้อยละ"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__pills
msgid "Pills"
msgstr "ทรงยา"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid "Please enter a positive whole number."
msgstr "กรุณากรอกจำนวนเต็มบวก"

#. module: product
#. odoo-python
#: code:addons/product/models/product_document.py:0
#, python-format
msgid ""
"Please enter a valid URL.\n"
"Example: https://www.odoo.com\n"
"\n"
"Invalid URL: %s"
msgstr ""
"โปรดป้อน URL ที่ถูกต้อง\n"
"ตัวอย่าง: https://www.odoo.com\n"
"\n"
"URL ไม่ถูกต้อง: %s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Please specify the category for which this rule should be applied"
msgstr "โปรดระบุหมวดหมู่ที่ควรใช้กฎนี้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Please specify the product for which this rule should be applied"
msgstr "โปรดระบุสินค้าที่ควรใช้กฎนี้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid ""
"Please specify the product variant for which this rule should be applied"
msgstr "โปรดระบุตัวเลือกย่อยสินค้าที่ควรใช้กฎนี้"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__1
msgid "Pounds"
msgstr "ปอนด์"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr ""
"กดปุ่มและดูโต๊ะทำงานของคุณเลื่อนไปมาได้อย่างง่ายดายจากความสูงสำหรับนั่งไปจนถึงยืนในไม่กี่วินาที"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Price"
msgstr "ราคา"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "การคำนวณราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "ราคาลด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "การปัดเศษราคา"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#: model:ir.actions.act_window,name:product.product_pricelist_item_action
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#, python-format
msgid "Price Rules"
msgstr "กฎราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Price Surcharge"
msgstr "ค่าธรรมเนียมราคาเพิ่มเติม"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
msgid "Price at which the product is sold to customers."
msgstr "ราคาที่สินค้าถูกขายให้กับลูกค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "ราคา:"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_label_layout__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#, python-format
msgid "Pricelist"
msgstr "รายการราคา"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "รายการราคาใช้ได้กับตัวเลือกที่เลือก"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "ชื่อรายการราคา"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#: model:ir.model,name:product.model_report_product_report_pricelist
#, python-format
msgid "Pricelist Report"
msgstr "รายงานของรายการราคา"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Pricelist Rule"
msgstr "กฎรายการราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Pricelist Rules"
msgstr "กฎรายการราคา"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Pricelist:"
msgstr "รายการราคา:"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "รายการราคา"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_pricelist_setting
msgid "Pricelists Method"
msgstr "วิธีรายการราคา"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "รายการราคาได้รับการจัดการบน"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "กำหนดราคา"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Print"
msgstr "พิมพ์"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Print Labels"
msgstr "พิมพ์ฉลาก"

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_ids
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__1_product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product"
msgstr "สินค้า"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "คุณสมบัติสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "ค่าคุณสมบัติของสินค้าที่กำหนดเอง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr "ค่าคุณสมบัติของสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "คุณสมบัติและมูลค่าของสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "คุณสมบัติของสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_catalog_mixin
msgid "Product Catalog Mixin"
msgstr "แคตตาล็อกสินค้า Mixin"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "หมวดหมู่สินค้า"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: product
#: model:ir.model,name:product.model_product_document
msgid "Product Document"
msgstr "เอกสารสินค้า"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_dymo
msgid "Product Label (PDF)"
msgstr "ฉลากสินค้า (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_2x7
msgid "Product Label 2x7 (PDF)"
msgstr "ฉลากสินค้า 2x7 (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12
msgid "Product Label 4x12 (PDF)"
msgstr "ฉลากสินค้า 4x12 (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12_noprice
msgid "Product Label 4x12 No Price (PDF)"
msgstr "ฉลากสินค้า 4x12 ไม่มีราคา (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x7
msgid "Product Label 4x7 (PDF)"
msgstr "ฉลากสินค้า 4x7 (PDF)"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel_dymo
msgid "Product Label Report"
msgstr "รายงานฉลากสินค้า"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel2x7
msgid "Product Label Report 2x7"
msgstr "รายงานฉลากสินค้า 2x7"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12
msgid "Product Label Report 4x12"
msgstr "รายงานฉลากสินค้า 4x12"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12noprice
msgid "Product Label Report 4x12 No Price"
msgstr "รายงานฉลากสินค้า 4x12 ไม่มีราคา"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x7
msgid "Product Label Report 4x7"
msgstr "รายงานฉลากสินค้า 4x7"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "ชื่อสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "บรรจุภัณฑ์สินค้า"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model:ir.model.fields,field_description:product.field_product_packaging__name
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr "บรรจุภัณฑ์ของสินค้า"

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr "บรรจุภัณฑ์สินค้า (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr "การบรรจุสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_properties_definition
msgid "Product Properties"
msgstr "คุณสมบัติของสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Tag"
msgstr "แท็กสินค้า"

#. module: product
#: model:ir.actions.act_window,name:product.product_tag_action
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Tags"
msgstr "แท็กสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr "การยกเว้นคุณสมบัติของเทมเพลตสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "รายการคุณสมบัติเทมเพลตสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "ค่าคุณสมบัติของเทมเพลตสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tag_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_tag_ids
msgid "Product Template Tags"
msgstr "แท็กเทมเพลตสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_template_ids
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_tree_tag
msgid "Product Templates"
msgstr "เทมเพลตสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_tmpl_ids
msgid "Product Tmpl"
msgstr "เทมเพลตสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tooltip
#: model:ir.model.fields,field_description:product.field_product_template__product_tooltip
msgid "Product Tooltip"
msgstr "คำแนะนำสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__detailed_type
#: model:ir.model.fields,field_description:product.field_product_template__detailed_type
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Type"
msgstr "ประเภทสินค้า"

#. module: product
#: model:ir.model,name:product.model_uom_uom
msgid "Product Unit of Measure"
msgstr "หน่วยวัดสินค้า"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__0_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_document_form
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Variant"
msgstr "ตัวเลือกย่อยสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid "Product Variant Values"
msgstr "ค่าตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.model.fields,field_description:product.field_product_tag__product_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_product_view_activity
#: model_terms:ir.ui.view,arch_db:product.product_product_view_tree_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Variants"
msgstr "ตัวเลือกย่อยสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr "ไม่ได้กำหนดรุ่นสินค้า โปรดติดต่อผู้ดูแลระบบของคุณ"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Product: %s"
msgstr "สินค้า: %s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_catalog_mixin.py:0
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_activity
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
#, python-format
msgid "Products"
msgstr "สินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "ราคาสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "รายการราคาสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Products Price Rules Search"
msgstr "ค้นหากฎราคาสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "ค้นหาราคาสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid "Products: %(category)s"
msgstr "สินค้า: %(category)s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_loyalty
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "โปรโมชั่น คูปอง บัตรของขวัญ และโปรแกรมสะสมคะแนน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_properties
#: model:ir.model.fields,field_description:product.field_product_template__product_properties
msgid "Properties"
msgstr "คุณสมบัติ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Purchase"
msgstr "สั่งซื้อ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "คำอธิบายการซื้อ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase UoM"
msgstr "จัดซื้อ UoM"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Quantities"
msgstr "ปริมาณ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Quantities (Price)"
msgstr "ปริมาณ (ราคา)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__custom_quantity
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Quantity"
msgstr "ปริมาณ"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid "Quantity already present (%s)."
msgstr "จำนวนที่มีอยู่แล้ว (%s)"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "Quantity of products contained in the packaging."
msgstr "จำนวนสินค้าที่บรรจุในบรรจุภัณฑ์"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__radio
msgid "Radio"
msgstr "คลื่นวิทยุ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
msgid "Reference"
msgstr "การอ้างอิง"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid ""
"Register the prices requested by your vendors for each product, based on the"
" quantity and the period."
msgstr ""
"ลงทะเบียนราคาที่ผู้ขายของคุณร้องขอสำหรับแต่ละสินค้า "
"โดยขึ้นอยู่กับปริมาณและระยะเวลา"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#: model:ir.model.fields,field_description:product.field_product_attribute__product_tmpl_ids
#, python-format
msgid "Related Products"
msgstr "สินค้าที่เกี่ยวข้อง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_product_variant_ids
msgid "Related Variants"
msgstr "ตัวเลือกย่อยสินค้าที่เกี่ยวข้อง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__ir_attachment_id
msgid "Related attachment"
msgstr "ไฟล์แนบที่เกี่ยวข้อง"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "Remove"
msgstr "นำออก"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Remove quantity"
msgstr "ลบปริมาณ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_field
msgid "Resource Field"
msgstr "ข้อมูลทรัพยากร"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_id
msgid "Resource ID"
msgstr "ไอดีทรัพยากร"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_model
msgid "Resource Model"
msgstr "โมเดลทรัพยากร"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_name
msgid "Resource Name"
msgstr "ชื่อทรัพยากร"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr "ค่าใช้จ่ายร้านอาหาร"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "วิธีการปัดเศษ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__rows
msgid "Rows"
msgstr "แถว"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__rule_tip
msgid "Rule Tip"
msgstr "เคล็ดลับของกฎ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "การขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales Description"
msgstr "คำอธิบายการขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "รายการตารางการขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__list_price
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales Price"
msgstr "ราคาขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
msgid "Sales Price"
msgstr "ราคาขาย"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__select
msgid "Select"
msgstr "เลือก"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__detailed_type__service
#: model:ir.model.fields.selection,name:product.selection__product_template__type__service
msgid "Service"
msgstr "บริการ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Services"
msgstr "บริการ"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"กำหนดราคาเพื่อให้เป็นผลคูณของค่านี้\n"
"การปัดเศษจะใช้หลังส่วนลดและก่อนคิดค่าธรรมเนียม\n"
"หากต้องการมีราคาที่ลงท้ายด้วย 9.99 ให้ปัดเศษ 10 บวกเพิ่ม -0.01"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr "แสดงระเบียนทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__without_discount
msgid "Show public price & discount to the customer"
msgstr "แสดงราคาและส่วนลดสาธารณะแก่ลูกค้า"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"ระบุหมวดหมู่สินค้าหากกฎนี้ใช้กับสินค้าที่อยู่ในหมวดหมู่นี้หรือหมวดหมู่ย่อยเท่านั้น"
" เว้นว่างไว้ถ้าเป็นอย่างอื่น"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"ระบุสินค้าหากกฎนี้ใช้กับสินค้าเดียวเท่านั้น เว้นว่างไว้ถ้าเป็นอย่างอื่น"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"ระบุเทมเพลตหากกฎนี้ใช้กับเทมเพลตสินค้าเดียวเท่านั้น "
"เว้นว่างไว้ถ้าเป็นอย่างอื่น"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or subtract (if negative) to the amount "
"calculated with the discount."
msgstr ""
"ระบุจำนวนเงินคงที่ที่จะบวกหรือลบ (หากมีค่าเป็นลบ) "
"เป็นจำนวนเงินที่คำนวณพร้อมกับส่วนลด"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "ระบุจำนวนมาร์จิ้นสูงสุดเหนือราคาฐาน"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "ระบุจำนวนมาร์จิ้นขั้นต่ำเหนือราคาฐาน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "วันที่เริ่มต้นสำหรับราคาของผู้ขายนี้"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid ""
"Starting datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"วันที่และเวลาเริ่มต้นสำหรับการตรวจสอบรายการรายการราคา\n"
"ค่าที่แสดงขึ้นอยู่กับเขตเวลาที่ตั้งค่าไว้ในการตั้งค่าของคุณ"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_state
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "Steel"
msgstr "เหล็ก"

#. module: product
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr "กล่องเก็บของ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__store_fname
msgid "Stored Filename"
msgstr "ชื่อไฟล์ที่จัดเก็บ"

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "รายการราคาซัพพลายเออร์"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_tag_name_uniq
msgid "Tag name already exists!"
msgstr "มีชื่อแท็กแล้ว!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Tags"
msgstr "แท็ก"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Tags are used to search product for a given theme."
msgstr "แท็กใช้เพื่อค้นหาสินค้าสำหรับธีมที่กำหนด"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The Internal Reference '%s' already exists."
msgstr "การอ้างอิงภายใน '%s' มีอยู่แล้ว"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The Type of this product doesn't match the Detailed Type"
msgstr "ประเภทของสินค้านี้ไม่ตรงกับประเภทรายละเอียด"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"The attribute %(attribute)s must have at least one value for the product "
"%(product)s."
msgstr ""
"คุณสมบัติ %(attribute)s ต้องมีค่าอย่างน้อยหนึ่งค่าสำหรับสินค้า %(product)s"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__attribute_id
msgid ""
"The attribute cannot be changed once the value is used on at least one "
"product."
msgstr ""
"ไม่สามารถเปลี่ยนคุณสมบัติได้เมื่อมีการใช้ค่ากับสินค้าอย่างน้อยหนึ่งรายการ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr "ราคาที่คำนวณจะแสดงเป็นหน่วยวัดเริ่มต้นของสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr "หน่วยวัดเริ่มต้นและหน่วยวัดการซื้อจะต้องอยู่ในหมวดหมู่เดียวกัน"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__display_type
#: model:ir.model.fields,help:product.field_product_attribute_value__display_type
#: model:ir.model.fields,help:product.field_product_template_attribute_value__display_type
msgid "The display type used in the Product Configurator."
msgstr "ประเภทการแสดงผลที่ใช้ในเครื่องมือกำหนดค่าของสินค้า"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr "อันแรกในลำดับคืออันที่ถูกกำหนดโดยค่าเริ่มต้น"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr "ความสูงขั้นต่ำคือ 65 ซม. สำหรับงานยืน ตำแหน่งสูงสูงสุดคือ 125 ซม."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "The minimum margin should be lower than the maximum margin."
msgstr "มาร์จิ้นขั้นต่ำควรต่ำกว่ามาร์จิ้นสูงสุด"

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr "จำนวนสินค้าตามหมวดหมู่นี้ (ไม่นับหมวดหมู่ย่อย)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The number of variants to generate is above allowed limit. You should either"
" not generate variants for each combination or generate them on demand from "
"the sales order. To do so, open the form view of attributes and change the "
"mode of *Create Variants*."
msgstr ""
"จำนวนตัวเลือกสินค้าที่จะสร้างเกินขีดจำกัดที่ได้รับอนุญาต "
"คุณไม่ควรสร้างตัวเลือกสินค้าสำหรับชุดแต่ละชุดหรือสร้างตามความต้องการจากใบสั่งขาย"
" โดยเปิดมุมมองแบบฟอร์มของคุณสมบัติและเปลี่ยนโหมดของ *สร้างตัวเลือกสินค้า*"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr "ราคาที่จะซื้อสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The product template is archived so no combination is possible."
msgstr "เทมเพลตสินค้าถูกเก็บถาวรแล้ว จึงไม่สามารถรวมกันได้"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The quantity to purchase from this vendor to benefit from the price, "
"expressed in the vendor Product Unit of Measure if not any, in the default "
"unit of measure of the product otherwise."
msgstr ""
"ปริมาณที่จะซื้อจากผู้ขายรายนี้เพื่อรับประโยชน์จากราคา "
"แสดงในหน่วยวัดสินค้าของผู้ขาย หากไม่มี ในหน่วยวัดเริ่มต้นของสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "The rounding method must be strictly positive."
msgstr "วิธีการปัดเศษต้องเป็นค่าบวกเท่านั้น"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"ราคาลดได้รับการจัดการจากเทมเพลตสินค้า คลิกปุ่ม 'กำหนดค่าตัวเลือกย่อยสินค้า' "
"เพื่อกำหนดราคาคุณสมบัติเพิ่มเติม"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"The value %(value)s is not defined for the attribute %(attribute)s on the "
"product %(product)s."
msgstr ""
"ไม่ได้กำหนดค่า %(value)s สำหรับคุณสมบัติ %(attribute)s บนสินค้า %(product)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no possible combination."
msgstr "ไม่สามารถรวมกันได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining closest combination."
msgstr "ไม่มีชุดค่ารวมที่ใกล้เคียงที่สุดเหลืออยู่"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining possible combination."
msgstr "ไม่มีชุดค่ารวมที่เป็นไปได้เหลืออยู่"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"This configuration of product attributes, values, and exclusions would lead "
"to no possible variant. Please archive or delete your product directly if "
"intended."
msgstr ""
"การกำหนดค่าคุณสมบัติ ค่า "
"และการยกเว้นของสินค้านี้จะทำให้ไม่มีรายละเอียดตัวเลือกย่อยสินค้าที่เป็นไปได้"
" โปรดเก็บถาวรหรือลบสินค้าของคุณโดยตรงหากต้องการ"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "นี่คือผลรวมของราคาเพิ่มเติมของคุณสมบัติทั้งหมด"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is added to sales orders and invoices."
msgstr "หมายเหตุนี้ถูกเพิ่มลงในใบสั่งขายและใบแจ้งหนี้"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr "หมายเหตุนี้มีไว้เพื่อวัตถุประสงค์ภายในเท่านั้น"

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"รายการราคานี้จะถูกนำมาใช้แทนรายการเริ่มต้นสำหรับการขายให้กับพาร์ทเนอร์ปัจจุบัน"

#. module: product
#. odoo-python
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid ""
"This rounding precision is higher than the Decimal Accuracy (%s digits).\n"
"This may cause inconsistencies in computations.\n"
"Please set a precision between %s and 1."
msgstr ""
"ความแม่นยำในการปัดเศษนี้สูงกว่าความแม่นยำของทศนิยม (%s หลัก)\n"
"ซึ่งอาจทำให้เกิดความไม่สอดคล้องกันในการคำนวณ\n"
"โปรดตั้งค่าความแม่นยำระหว่าง %s ถึง 1"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"รหัสสินค้าของผู้ขายรายนี้จะถูกนำมาใช้เมื่อพิมพ์คำขอใบเสนอราคา "
"ให้เว้นว่างไว้เพื่อใช้ช่องภายใน"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"ชื่อสินค้าของผู้ขายรายนี้จะถูกนำมาใช้เมื่อพิมพ์คำขอใบเสนอราคา "
"ให้เว้นว่างไว้เพื่อใช้ช่องภายใน"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "Three Seater Sofa with Lounger in Steel Grey Colour"
msgstr "โซฟา 3 ที่นั่งพร้อมเก้าอี้นอน สีเทาเหล็ก"

#. module: product
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr "โซฟา 3 ที่นั่ง"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__type
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
msgid "Type"
msgstr "ประเภท"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "UOM"
msgstr "UOM"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid "Unable to find report template for %s format"
msgstr "ไม่พบเทมเพลตรายงานสำหรับรูปแบบ %s"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Unit"
msgstr "หน่วย"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Unit Price"
msgstr "ราคาต่อหน่วย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr "ชื่อหน่วยวัด"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "Unit price:"
msgstr "ราคาต่อหน่วย:"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Units"
msgstr "หน่วย"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Units of Measure"
msgstr "หน่วยวัด"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_document_kanban/product_document_kanban_controller.xml:0
#, python-format
msgid "Upload"
msgstr "อัปโหลด"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Upload files to your product"
msgstr "อัปโหลดไฟล์ไปยังสินค้าของคุณ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Upsell & Cross-Sell"
msgstr "การขายต่อยอดและการขายสินค้าแบบไขว้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__url
msgid "Url"
msgstr "Url"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"Use this feature to store any files you would like to share with your "
"customers"
msgstr "ใช้ฟีเจอร์นี้เพื่อจัดเก็บไฟล์ที่คุณต้องการแชร์กับลูกค้าของคุณ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_used_on_products
msgid "Used on Products"
msgstr "ใช้กับสินค้า"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Username"
msgstr "ชื่อผู้ใช้"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "รายการคุณสมบัติของสินค้าที่ถูกต้อง"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "อายุการใช้งาน"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "ค่า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_count
msgid "Value Count"
msgstr "การนับมูลค่า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Value Price Extra"
msgstr "มูลค่าราคาเพิ่มเติม"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"Value of the product (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"มูลค่าของสินค้า (คำนวณโดยอัตโนมัติใน AVCO)\n"
"       ใช้เพื่อประเมินมูลค่าสินค้าเมื่อไม่ทราบต้นทุนการซื้อ (เช่น การปรับปรุงสินค้าคงคลัง)\n"
"       ใช้เพื่อคำนวณอัตรากำไรจากคำสั่งซื้อขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "มูลค่า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Variant"
msgstr "ตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "จำนวนตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1920
msgid "Variant Image"
msgstr "รูปภาพตัวเลือกย่อย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1024
msgid "Variant Image 1024"
msgstr "รูปภาพตัวเลือกย่อย 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_128
msgid "Variant Image 128"
msgstr "รูปภาพตัวเลือกย่อย 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_256
msgid "Variant Image 256"
msgstr "รูปภาพตัวเลือกย่อย 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_512
msgid "Variant Image 512"
msgstr "รูปภาพตัวเลือกย่อย 512"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "ข้อมูลตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "ราคาตัวเลือกย่อยเพิ่มเติม"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr "ผู้ขายตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_variant_value_ids
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
msgid "Variant Values"
msgstr "มูลค่าตัวเลือกย่อยสินค้า"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Variant: %s"
msgstr "ตัวเลือกย่อย: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "ตัวเลือกย่อย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Variants Creation Mode"
msgstr "โหมดการสร้างตัวเลือกย่อยสินค้า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__partner_id
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "ผู้ขาย"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "บิลผู้ขาย"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "ข้อมูลผู้ขาย"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "รายการราคาผู้ขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "รหัสสินค้าของผู้ขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "ชื่อสินค้าของผู้ขาย"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "ผู้ขาย"

#. module: product
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr "การจัดเตรียมบ้านเสมือนจริง"

#. module: product
#: model:product.template,name:product.product_product_1_product_template
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Virtual Interior Design"
msgstr "การออกแบบตกแต่งภายในเสมือนจริง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__voice_ids
msgid "Voice"
msgstr "เสียง"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Volume"
msgstr "ปริมาตร"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr "หน่วยวัดปริมาตร"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__volume_uom_name
msgid "Volume unit of measure label"
msgstr "ฉลากหน่วยวัดปริมาตร"

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "คำเตือน!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Warnings"
msgstr "คำเตือน"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""
"เราใส่ใจในรายละเอียดเป็นพิเศษ "
"ซึ่งเป็นสาเหตุที่ทำให้โต๊ะทำงานของเรามีคุณภาพที่เหนือกว่า"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight"
msgstr "น้ำหนัก"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "หน่วยวัดน้ำหนัก"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr "หน่วยฉลากน้ำหนัก"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "ขาว"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__write_date
msgid "Write Date"
msgstr "เขียนวันที่"

#. module: product
#. odoo-python
#: code:addons/product/models/res_config_settings.py:0
#, python-format
msgid ""
"You are deactivating the pricelist feature. Every active pricelist will be "
"archived."
msgstr ""
"คุณกำลังปิดการใช้งานฟีเจอร์รายการราคา "
"รายการราคาที่ใช้งานอยู่ทั้งหมดจะถูกเก็บถาวร"

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You are setting a Decimal Accuracy less precise than the UOMs:\n"
"%s\n"
"This may cause inconsistencies in computations.\n"
"Please increase the rounding of those units of measure, or the digits of this Decimal Accuracy."
msgstr ""
"คุณกำลังตั้งค่าความแม่นยำของทศนิยมน้อยกว่า UOM:\n"
"%s\n"
"ซึ่งอาจทำให้เกิดความไม่สอดคล้องกันในการคำนวณ\n"
"โปรดเพิ่มการปัดเศษของหน่วยวัดเหล่านั้นหรือหลักความแม่นยำของทศนิยมนี้"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__percent_price
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_discount
msgid "You can apply a mark-up by setting a negative discount."
msgstr "คุณสามารถใช้ส่วนเพิ่มได้โดยการตั้งค่าส่วนลดที่ติดลบ"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""
"คุณสามารถกำหนดรายการราคาให้กับลูกค้าของคุณหรือเลือกรายการราคาหนึ่งรายการเมื่อสร้างใบเสนอราคาขายใหม่ได้"

#. module: product
#: model:ir.model.fields,help:product.field_product_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"คุณสามารถอัปโหลดไฟล์จากคอมพิวเตอร์ของคุณ "
"หรือคัดลอก/วางอินเตอร์เน็ตลิงก์ไปยังไฟล์ของคุณ"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__image
#: model:ir.model.fields,help:product.field_product_template_attribute_value__image
msgid ""
"You can upload an image that will be used as the color of the attribute "
"value."
msgstr "คุณสามารถอัปโหลดรูปภาพที่จะใช้เป็นสีของค่าแอตทริบิวต์ได้"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "You can't edit this product in the catalog."
msgstr "คุณไม่สามารถแก้ไขสินค้านี้ในแค็ตตาล็อกได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr ""
"คุณไม่สามารถกำหนดรายการราคาหลักเป็นรายการราคาอื่นในรายการรายการราคาได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot change the Variants Creation Mode of the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""
"คุณไม่สามารถเปลี่ยนโหมดการสร้างตัวเลือกย่อยสินค้าของคุณสมบัติ %(attribute)s ได้ เนื่องจากมีการใช้กับผลิตภัณฑ์ต่อไปนี้:\n"
"%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
#, python-format
msgid ""
"You cannot change the attribute of the value %(value)s because it is used on"
" the following products: %(products)s"
msgstr ""
"คุณไม่สามารถเปลี่ยนคุณสมบัติของค่า %(value)s ได้ "
"เนื่องจากมีการใช้กับผลิตภัณฑ์ต่อไปนี้: %(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"You cannot change the product of the value %(value)s set on product "
"%(product)s."
msgstr ""
"คุณไม่สามารถเปลี่ยนผลคูณของค่า %(value)s ที่ตั้งไว้ในผลิตภัณฑ์ %(product)s "
"ได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"You cannot change the value of the value %(value)s set on product "
"%(product)s."
msgstr ""
"คุณไม่สามารถเปลี่ยนค่าของค่า %(value)s ที่ตั้งไว้ในผลิตภัณฑ์ %(product)s ได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
#, python-format
msgid "You cannot create recursive categories."
msgstr "คุณไม่สามารถสร้างหมวดหมู่แบบเรียกซ้ำได้"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_value_value_company_uniq
msgid ""
"You cannot create two values with the same name for the same attribute."
msgstr "คุณไม่สามารถสร้างสองค่าด้วยชื่อเดียวกันสำหรับคุณสมบัติเดียวกันได้"

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""
"คุณไม่สามารถกำหนดความแม่นยำของทศนิยมของ 'บัญชี' "
"ให้มากกว่าค่าตัวคูณการปัดเศษของสกุลเงินหลักของบริษัทได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
#, python-format
msgid "You cannot delete the %s product category."
msgstr "คุณไม่สามารถลบหมวดหมู่สินค้า %s ได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot delete the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""
"คุณไม่สามารถลบคุณสมบัติ %(attribute)s ได้ เนื่องจากมีการใช้กับผลิตภัณฑ์ต่อไปนี้:\n"
"%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
#, python-format
msgid ""
"You cannot delete the value %(value)s because it is used on the following products:\n"
"%(products)s\n"
" If the value has been associated to a product in the past, you will not be able to delete it."
msgstr ""
"คุณไม่สามารถลบค่า %(value)s ได้ เนื่องจากมีการใช้กับผลิตภัณฑ์ต่อไปนี้:\n"
"%(products)s\n"
"หากค่าเคยเชื่อมโยงกับผลิตภัณฑ์ในอดีต คุณจะไม่สามารถลบออกได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
#, python-format
msgid ""
"You cannot delete this product category, it is the default generic category."
msgstr ""
"คุณไม่สามารถลบหมวดหมู่ผลิตภัณฑ์นี้ได้ เนื่องจากเป็นหมวดหมู่ทั่วไปเริ่มต้น"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot delete those pricelist(s):\n"
"(%s)\n"
", they are used in other pricelist(s):\n"
"%s"
msgstr ""
"คุณไม่สามารถลบรายการราคาเหล่านั้นได้:\n"
"(%s)\n"
"จะถูกใช้ในรายการราคาอื่นๆ:\n"
"%s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
#, python-format
msgid "You cannot delete value %s because it was used in some products."
msgstr "คุณไม่สามารถลบค่า %s ได้ เนื่องจากมีการใช้งานในบางผลิตภัณฑ์อยู่"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"You cannot move the attribute %(attribute)s from the product %(product_src)s"
" to the product %(product_dest)s."
msgstr ""
"คุณไม่สามารถย้ายคุณสมบัติ %(attribute)s จากผลิตภัณฑ์ %(product_src)s "
"ไปยังผลิตภัณฑ์ %(product_dest)s ได้"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"You cannot update related variants from the values. Please update related "
"values from the variants."
msgstr ""
"คุณไม่สามารถอัปเดตรูปแบบที่เกี่ยวข้องจากมูลค่าได้ "
"โปรดอัปเดตค่าที่เกี่ยวข้องจากตัวเลือกย่อยสินค้า"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
#, python-format
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service."
msgstr ""
"คุณต้องกำหนดสินค้าสำหรับทุกสิ่งที่คุณขายหรือซื้อ\n"
"                            ไม่ว่าจะเป็นสินค้าที่สามารถจัดเก็บได้ สินค้าอุปโภคบริโภค หรือบริการ"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"คุณต้องกำหนดสินค้าสำหรับทุกสิ่งที่คุณขายหรือซื้อ\n"
"              ไม่ว่าจะเป็นสินค้าที่สามารถจัดเก็บได้ สินค้าอุปโภคบริโภค หรือบริการ"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"คุณต้องกำหนดสินค้าสำหรับทุกสิ่งที่คุณขายหรือซื้อ\n"
"               ไม่ว่าจะเป็นสินค้าที่สามารถจัดเก็บได้ สินค้าอุปโภคบริโภค หรือบริการ\n"
"               แบบฟอร์มสินค้ามีข้อมูลเพื่อทำให้กระบวนการขายง่ายขึ้น:\n"
"               ราคา, หมายเหตุในใบเสนอราคา, ข้อมูลทางบัญชี, วิธีการจัดซื้อจัดจ้าง ฯลฯ"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"คุณต้องกำหนดสินค้าสำหรับทุกสิ่งที่คุณขาย ไม่ว่าจะเป็นผลิตภัณฑ์ที่จับต้องได้\n"
"              อุปโภคบริโภคหรือบริการที่คุณเสนอให้กับลูกค้า\n"
"              แบบฟอร์มสินค้ามีข้อมูลเพื่อทำให้กระบวนการขายง่ายขึ้น:\n"
"              ราคา, หมายเหตุในใบเสนอราคา, ข้อมูลทางบัญชี, วิธีการจัดซื้อ ฯลฯ"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid "You must leave at least one quantity."
msgstr "คุณต้องเหลือไว้อย่างน้อยหนึ่งปริมาณ"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid "You need to set a positive quantity."
msgstr "คุณต้องกำหนดปริมาณที่เป็นบวก"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "วัน"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Cheese Burger"
msgstr "เช่น ชีสเบอร์เกอร์"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "เช่น หลอดไฟ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr "เช่น การสมัครสมาชิก Odoo สำหรับองค์กร"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "เช่น ผู้ค้าปลีก USD"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "on"
msgstr "เมื่อ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "per"
msgstr "ต่อ"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "product"
msgstr "สินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "บริษัทแม่"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template."
msgstr "เทมเพลตสินค้า"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "ถึง"
