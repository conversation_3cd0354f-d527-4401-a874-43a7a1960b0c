# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_ldap
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <<PERSON>.<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2023-07-13 13:02+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_filter
msgid ""
"    Filter used to look up user accounts in the LDAP database. It is an    arbitrary LDAP filter in string representation. Any `%s` placeholder    will be replaced by the login (identifier) provided by the user, the filter    should contain at least one such placeholder.\n"
"\n"
"    The filter must result in exactly one (1) result, otherwise the login will    be considered invalid.\n"
"\n"
"    Example (actual attributes depend on LDAP server and setup):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    or\n"
"\n"
"        (|(mail=%s)(uid=%s))\n"
"    "
msgstr ""
"    用於在 LDAP 數據庫中查找用戶帳戶的過濾器. 它是字符串表示形式的任意 LDAP 過濾器. 任何“%s”佔位符都將被使用者提供的登錄名稱(標識符)替換，過濾器應至少包含一個這樣的佔位符.\n"
"\n"
"    過濾器必須產生一 (1) 個結果，否則登錄將被視為無效。\n"
"\n"
"    範例(實際屬性取決於 LDAP 伺服器和設置定):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    或是\n"
"\n"
"        (|(郵件=%s)(uid=%s))\n"
" "

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__create_user
msgid ""
"Automatically create local user accounts for new users authenticating via "
"LDAP"
msgstr "通過LDAP，為新使用者自動建立本地使用者帳戶"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company
msgid "Companies"
msgstr "公司"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__company
msgid "Company"
msgstr "公司"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company_ldap
msgid "Company LDAP configuration"
msgstr "公司 LDAP 設定"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_config_settings
msgid "Config Settings"
msgstr "基礎設定"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_user
msgid "Create User"
msgstr "建立使用者"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_uid
msgid "Created by"
msgstr "建立者"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_date
msgid "Created on"
msgstr "建立於"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_base
msgid ""
"DN of the user search scope: all descendants of this base will be searched "
"for users."
msgstr "使用者搜索範圍的DN: 將搜索基於此範圍的所有使用者."

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__id
msgid "ID"
msgstr "識別碼"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_company_ldap_view_tree
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "LDAP Configuration"
msgstr "LDAP 設定"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company__ldaps
#: model:ir.model.fields,field_description:auth_ldap.field_res_config_settings__ldaps
msgid "LDAP Parameters"
msgstr "LDAP 參數"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_config_settings_view_form
msgid "LDAP Server"
msgstr "LDAP 伺服器"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server
msgid "LDAP Server address"
msgstr "LDAP 伺服器地址"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server_port
msgid "LDAP Server port"
msgstr "LDAP 伺服器埠"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_base
msgid "LDAP base"
msgstr "LDAP 基礎"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_binddn
msgid "LDAP binddn"
msgstr "LDAP binddn"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_filter
msgid "LDAP filter"
msgstr "LDAP 篩選"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_password
msgid "LDAP password"
msgstr "LDAP 密碼"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap____last_update
msgid "Last Modified on"
msgstr "最後修改"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Login Information"
msgstr "登錄信息"

#. module: auth_ldap
#. odoo-python
#: code:addons/auth_ldap/models/res_company_ldap.py:0
#, python-format
msgid "No local user found for LDAP login and not configured to create one"
msgstr "沒有找到LDAP登入的本地使用者，也沒有為建立使用者設定參數"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Process Parameter"
msgstr "處理參數"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_tls
msgid ""
"Request secure TLS/SSL encryption when connecting to the LDAP server. This "
"option requires a server with STARTTLS enabled, otherwise all authentication"
" attempts will fail."
msgstr "當連接 LDAP 伺服器時請求伺服器使用安全的 TLS/SSL 加密。該選項需要伺服器啟用 STARTTLS，否則所有使用者驗證都將失敗."

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__sequence
msgid "Sequence"
msgstr "序列號"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Server Information"
msgstr "伺服器信息"

#. module: auth_ldap
#: model:ir.actions.act_window,name:auth_ldap.action_ldap_installer
msgid "Setup your LDAP Server"
msgstr "設定您的 LDAP 伺服器"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__user
msgid "Template User"
msgstr "模板使用者"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_password
msgid ""
"The password of the user account on the LDAP server that is used to query "
"the directory."
msgstr "LDAP 伺服器上的使用者帳號密碼，用於查詢該目錄服務."

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_binddn
msgid ""
"The user account on the LDAP server that is used to query the directory. "
"Leave empty to connect anonymously."
msgstr "用於查詢 LDAP 伺服器目錄的使用者帳號。如果要匿名連接請保持為空."

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_tls
msgid "Use TLS"
msgstr "使用TLS傳輸層套接字"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_users
msgid "User"
msgstr "使用者"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "User Information"
msgstr "使用者資訊"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__user
msgid "User to copy when creating new users"
msgstr "當建立新使用者時複製的使用者"
