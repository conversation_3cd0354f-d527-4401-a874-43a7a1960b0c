<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Analytic Plans -->
        <record id="analytic_plan_projects" model="account.analytic.plan">
            <field name="name">Projects</field>
            <field name="default_applicability">optional</field>
        </record>
        <record id="analytic_plan_departments" model="account.analytic.plan">
            <field name="name">Departments</field>
            <field name="default_applicability">optional</field>
        </record>
        <record id="analytic_plan_internal" model="account.analytic.plan">
            <field name="name">Internal</field>
            <field name="default_applicability">unavailable</field>
        </record>
        <!-- Analytic Accounts -->
        <record id="analytic_absences" model="account.analytic.account">
            <field name="name">Time Off</field>
            <field name="plan_id" ref="analytic.analytic_plan_internal"/>
        </record>
        <record id="analytic_internal" model="account.analytic.account">
            <field name="name">Operating Costs</field>
            <field name="plan_id" ref="analytic.analytic_plan_internal"/>
        </record>
        <record id="analytic_our_super_product" model="account.analytic.account">
            <field name="name">Our Super Product</field>
            <field name="partner_id" ref="base.res_partner_2"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_seagate_p2" model="account.analytic.account">
            <field name="name">Seagate P2</field>
            <field name="partner_id" ref="base.res_partner_2"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_millennium_industries" model="account.analytic.account">
            <field name="name">Millennium Industries</field>
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_integration_c2c" model="account.analytic.account">
            <field name="name">CampToCamp</field>
            <field name="partner_id" ref="base.res_partner_12"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_agrolait" model="account.analytic.account">
            <field name="name">Deco Addict</field>
            <field name="partner_id" ref="base.res_partner_2"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_asustek" model="account.analytic.account">
            <field name="name">Asustek</field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_deltapc" model="account.analytic.account">
            <field name="name">Delta PC</field>
            <field name="partner_id" ref="base.res_partner_4"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_spark" model="account.analytic.account">
            <field name="name">Spark Systems</field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_nebula" model="account.analytic.account">
            <field name="name">Nebula</field>
            <field name="partner_id" ref="base.res_partner_12"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_luminous_technologies" model="account.analytic.account">
            <field name="name">Luminous Technologies</field>
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_desertic_hispafuentes" model="account.analytic.account">
            <field name="name">Desertic - Hispafuentes</field>
            <field name="partner_id" ref="base.res_partner_12"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_think_big_systems" model="account.analytic.account">
            <field name="name">Lumber Inc</field>
            <field name="partner_id" ref="base.res_partner_18"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_partners_camp_to_camp" model="account.analytic.account">
            <field name="name">Camp to Camp</field>
            <field name="partner_id" ref="base.res_partner_12"/>
            <field name="plan_id" ref="analytic.analytic_plan_projects"/>
        </record>
        <record id="analytic_administratif" model="account.analytic.account">
            <field name="name">Administrative</field>
            <field name="plan_id" ref="analytic.analytic_plan_departments"/>
        </record>
        <record id="analytic_commercial_marketing" model="account.analytic.account">
            <field name="name">Commercial &amp; Marketing</field>
            <field name="plan_id" ref="analytic.analytic_plan_departments"/>
        </record>
        <record id="analytic_rd_department" model="account.analytic.account">
            <field name="name">Research &amp; Development</field>
            <field name="plan_id" ref="analytic.analytic_plan_departments"/>
        </record>
    </data>
</odoo>
