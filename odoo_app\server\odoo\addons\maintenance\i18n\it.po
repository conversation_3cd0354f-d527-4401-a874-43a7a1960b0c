# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* maintenance
# 
# Translators:
# <PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Category:</b>"
msgstr "<b>Categoria:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Model Number:</b>"
msgstr "<b>Numero modello:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Request to:</b>"
msgstr "<b>Richiesta per:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Serial Number:</b>"
msgstr "<b>Numero di serie:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"badge text-bg-warning float-end\">Canceled</span>"
msgstr "<span class=\"badge text-bg-warning float-end\">Annullata</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<span class=\"badge text-bg-warning float-end\">Cancelled</span>"
msgstr "<span class=\"badge text-bg-warning float-end\">Annullata</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"ml8\">hours</span>"
msgstr "<span class=\"ml8\">ore</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Rendiconto</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Requests</span>"
msgstr "<span>Richieste</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Equipment:</strong>"
msgstr "<strong>Attrezzatura:</strong>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Maintenance:</strong>"
msgstr "<strong>Manutenzione:</strong>"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Dizionario Python che verrà esaminato per fornire valori predefiniti durante"
" la creazione di nuovi record per l'alias."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer3
#: model:maintenance.equipment,name:maintenance.equipment_computer5
msgid "Acer Laptop"
msgstr "Portatile Acer"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__active
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__active
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Active"
msgstr "Attivo"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo di attività"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.mail_activity_type_action_config_maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipi di attività"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid "Add a new equipment"
msgstr "Aggiungi una nuova attrezzatura"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_category_action
msgid "Add a new equipment category"
msgstr "Aggiungi una nuova categoria di attrezzatura"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid "Add a new maintenance request"
msgstr "Aggiungi una nuova richiesta di manutenzione"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_stage_action
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_dashboard_action
msgid "Add a new stage in the maintenance request"
msgstr "Aggiungi una nuova fase alla richiesta di manutenzione"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_team_action_settings
msgid "Add a team in the maintenance request"
msgstr "Aggiungi una squadra alla richiesta di manutenzione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_id
msgid "Alias"
msgstr "Alias"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "Sicurezza contatto alias"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain_id
msgid "Alias Domain"
msgstr "Dominio alias"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Alias Domain Name"
msgstr "Nome dominio alias"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_full_name
msgid "Alias Email"
msgstr "E-mail alias"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_name
msgid "Alias Name"
msgstr "Nome alias"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias Status"
msgstr "Stato alias"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Stato alias verificato sull'ultimo messaggio ricevuto."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_model_id
msgid "Aliased Model"
msgstr "Modello con alias"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "All"
msgstr "Tutte"

#. module: maintenance
#: model:ir.model.constraint,message:maintenance.constraint_maintenance_equipment_serial_no
msgid "Another asset already exists with this serial number!"
msgstr "Esiste già un altro bene con lo stesso numero di serie."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__archive
msgid "Archive"
msgstr "Archivia"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Archived"
msgstr "In archivio"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Assign To User"
msgstr "Assegna a utente"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Assigned"
msgstr "Assegnata"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "Data di assegnazione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Assigned to"
msgstr "Assegnata a"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Blocked"
msgstr "Bloccate"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Cancelled"
msgstr "Annullato"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__category_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Category"
msgstr "Categoria"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
msgid "Category Name"
msgstr "Nome categoria"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__close_date
msgid "Close Date"
msgstr "Data chiusura"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__color
msgid "Color Index"
msgstr "Indice colore"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__note
msgid "Comments"
msgstr "Commenti"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__company_id
msgid "Company"
msgstr "Azienda"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr "Ultima data di guasto + MTBF"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_computer
msgid "Computers"
msgstr "Computer"

#. module: maintenance
#: model:ir.model,name:maintenance.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_configuration
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Configuration"
msgstr "Configurazione"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__corrective
msgid "Corrective"
msgstr "Correttivo"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__cost
msgid "Cost"
msgstr "Costo"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr "Crea modelli di fogli di lavoro personalizzati"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Created By"
msgstr "Creato da"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "Creato da utente"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_open_count
msgid "Current Maintenance"
msgstr "Manutenzione corrente"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Messaggio personalizzato di non recapito"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_res_config_settings__module_maintenance_worksheet
msgid "Custom Maintenance Worksheets"
msgstr "Fogli di lavoro personalizzati per la manutenzione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Custom Worksheets"
msgstr "Fogli di lavoro personalizzati"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_dashboard
msgid "Dashboard"
msgstr "Bacheca"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__request_date
msgid "Date requested for the maintenance to happen"
msgstr "Data richiesta per eseguire la manutenzione"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__schedule_date
msgid ""
"Date the maintenance team plans the maintenance.  It should not differ much "
"from the Request Date. "
msgstr ""
"Data alla quale la squadra ha pianificato la manutenzione. Non deve "
"differire molto dalla data di richiesta."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__close_date
msgid "Date the maintenance was finished. "
msgstr "Data di termine della manutenzione. "

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__day
msgid "Days"
msgstr "Giorno/i"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_defaults
msgid "Default Values"
msgstr "Valori predefiniti"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Delete"
msgstr "Elimina"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__description
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Description"
msgstr "Descrizione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Done"
msgstr "Completate"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__duration
msgid "Duration"
msgstr "Durata"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__duration
msgid "Duration in hours."
msgstr "Durata in ore."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Edit..."
msgstr "Modifica..."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__effective_date
msgid "Effective Date"
msgstr "Data effettiva"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_email
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Email Alias"
msgstr "Alias e-mail"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_id
msgid ""
"Email alias for this equipment category. New emails will automatically "
"create a new equipment under this category."
msgstr ""
"Alias e-mail per questa categoria di attrezzatura. Le nuove e-mail creano "
"automaticamente attrezzature in questa categoria. "

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__email_cc
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Email cc"
msgstr "E-mail in cc"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Dominio e-mail ad es. \"example.com\" in \"<EMAIL>\""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_until
msgid "End Date"
msgstr "Data fine"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action_from_category_form
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__equipment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__equipment_ids
#: model:ir.ui.menu,name:maintenance.menu_equipment_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Equipment"
msgstr "Attrezzatura"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_cat_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_mat_assign
msgid "Equipment Assigned"
msgstr "Attrezzatura assegnata"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_category_action
#: model:ir.ui.menu,name:maintenance.menu_maintenance_cat
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Equipment Categories"
msgstr "Categorie attrezzatura"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__category_id
msgid "Equipment Category"
msgstr "Categoria attrezzatura"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_count
msgid "Equipment Count"
msgstr "Numero attrezzature"

#. module: maintenance
#: model:res.groups,name:maintenance.group_equipment_manager
msgid "Equipment Manager"
msgstr "Responsabile attrezzature"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__name
msgid "Equipment Name"
msgstr "Nome attrezzatura"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Estimated Next Failure"
msgstr "Stima prossimo guasto"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr "Tempo stimato prima il prossimo guasto (in giorni)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__expected_mtbf
msgid "Expected MTBF"
msgstr "MTBF previsto"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__expected_mtbf
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Expected Mean Time Between Failure"
msgstr "Tempo medio previsto fra i guasti"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__fold
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__fold
msgid "Folded in Maintenance Pipe"
msgstr "Minimizzata nel flusso di manutenzione"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr "Segui il procedimento di richiesta e comunica con il collaboratore."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__forever
msgid "Forever"
msgstr "Per sempre"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Future Activities"
msgstr "Attività future"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_google_slide
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__google_slide
msgid "Google Slide"
msgstr "Presentazione Google"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Google Slide Link"
msgstr "Link della presentazione Google"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Group by..."
msgstr "Raggruppa per..."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_printer1
msgid "HP Inkjet printer"
msgstr "Stampante HP Inkjet"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer11
#: model:maintenance.equipment,name:maintenance.equipment_computer9
msgid "HP Laptop"
msgstr "Portatile HP"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__3
msgid "High"
msgstr "Alta"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "High-priority"
msgstr "Alta priorità"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__id
msgid "ID"
msgstr "ID"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del record principale contenente l’alias (esempio: progetto che contiene "
"l’alias di creazione del lavoro)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Se impostato, il contenuto verrà inviato automaticamente, al posto del "
"messaggio predefinito, agli utenti non autorizzati."

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__normal
#: model:maintenance.stage,name:maintenance.stage_1
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "In Progress"
msgstr "In corso"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_type
msgid "Instruction"
msgstr "Istruzione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Instructions"
msgstr "Istruzioni"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_maintenance
msgid "Internal Maintenance"
msgstr "Manutenzione interna"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Internal Notes"
msgstr "Note interne"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__kanban_state
msgid "Kanban State"
msgstr "Stato kanban"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Late Activities"
msgstr "Attività in ritardo"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Latest Failure"
msgstr "Ultimo guasto"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__latest_failure_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__latest_failure_date
msgid "Latest Failure Date"
msgstr "Data ultimo guasto"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Rilevamento dell'arrivo di una parte locale"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__location
msgid "Location"
msgstr "Ubicazione"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_losses
msgid "Losses Analysis"
msgstr "Analisi perdite"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__1
msgid "Low"
msgstr "Bassa"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mtbf
msgid "MTBF"
msgstr "MTBF"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mttr
msgid "MTTR"
msgstr "MTTR"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_ids
#: model:ir.ui.menu,name:maintenance.menu_m_request
#: model:ir.ui.menu,name:maintenance.menu_maintenance_title
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Maintenance"
msgstr "Manutenzione"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_request_calendar
msgid "Maintenance Calendar"
msgstr "Calendario manutenzione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_count
msgid "Maintenance Count"
msgstr "Numero manutenzioni"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "Attrezzatura per manutenzione"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment_category
msgid "Maintenance Equipment Category"
msgstr "Categoria attrezzatura per manutenzione"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_mixin
msgid "Maintenance Maintained Item"
msgstr "Manutenzione elemento mantenuto"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_request
#: model:mail.activity.type,name:maintenance.mail_act_maintenance_request
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_request_view_activity
msgid "Maintenance Request"
msgstr "Richiesta di manutenzione"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_cat_req_created
msgid "Maintenance Request Created"
msgstr "Richiesta di manutenzione creata"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Maintenance Request Search"
msgstr "Ricerca richiesta di manutenzione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_tree
msgid "Maintenance Request Stage"
msgstr "Fase di richiesta manutenzione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_search
msgid "Maintenance Request Stages"
msgstr "Fasi di richiesta manutenzione"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_created
msgid "Maintenance Request created"
msgstr "Richiesta di manutenzione creata"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_cal
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_from_equipment
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_link
#: model:ir.actions.act_window,name:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model:ir.actions.act_window,name:maintenance.maintenance_request_action_reports
#: model:ir.ui.menu,name:maintenance.maintenance_request_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_request_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Maintenance Requests"
msgstr "Richieste di manutenzione"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_stage
msgid "Maintenance Stage"
msgstr "Fase di manutenzione"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_stage_configuration
msgid "Maintenance Stages"
msgstr "Fasi di manutenzione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_team_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_tree
msgid "Maintenance Team"
msgstr "Team di manutenzione"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_dashboard_action
#: model:ir.model,name:maintenance.model_maintenance_team
#: model:ir.ui.menu,name:maintenance.menu_maintenance_teams
msgid "Maintenance Teams"
msgstr "Team di manutenzione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_type
msgid "Maintenance Type"
msgstr "Tipo manutenzione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time Between Failure"
msgstr "Tempo medio fra i guasti"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mtbf
msgid ""
"Mean Time Between Failure, computed based on done corrective maintenances."
msgstr ""
"Tempo medio fra i guasti, calcolato in base alle riparazioni effettuate. "

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mttr
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time To Repair"
msgstr "Tempo medio fino alla riparazione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_metrology
msgid "Metrology"
msgstr "Metrologia"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__model
msgid "Model"
msgstr "Modello"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_monitor
msgid "Monitors"
msgstr "Monitor"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__month
msgid "Months"
msgstr "Mesi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__my_activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mie attività"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "My Equipment"
msgstr "La mia attrezzatura"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "My Maintenances"
msgstr "Le mie manutenzioni"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Name"
msgstr "Nome"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_0
msgid "New Request"
msgstr "Nuova richiesta"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_calendar_event_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Prossimo evento del calendario delle attività"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_summary
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_id
msgid "Next Activity Type"
msgstr "Tipologia prossima attività"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__2
msgid "Normal"
msgstr "Normale"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__note
msgid "Note"
msgstr "Nota"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Notes"
msgstr "Note"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count
msgid "Number of Requests"
msgstr "Numero di richieste"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_block
msgid "Number of Requests Blocked"
msgstr "Numero di richieste bloccate"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_date
msgid "Number of Requests Scheduled"
msgstr "Numero di richieste programmate"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_unscheduled
msgid "Number of Requests Unscheduled"
msgstr "Numero di richieste non programmate"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_high_priority
msgid "Number of Requests in High Priority"
msgstr "Numero di richieste con priorità alta"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID facoltativo di un thread (record) a cui verranno allegati tutti i "
"messaggi in arrivo, anche se non hanno risposto ad esso. Se impostato, "
"disabiliterà completamente la creazione di nuovi record."

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_oee
msgid "Overall Equipment Effectiveness (OEE)"
msgstr "Efficacia complessiva dell'impianto (OEE)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__owner_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Owner"
msgstr "Proprietario"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_pdf
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid "Parent Model"
msgstr "Modello principale"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID discussione record principale"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modello principale contenente l'alias. Il modello che contiene il "
"riferimento alias non è necessariamente quello fornito da alias_model_id "
"(esempio: progetto (parent_model) e lavoro (model))"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__instruction_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"Incollare URL della presentazione Google. Controllare che l'accesso al "
"documento sia pubblico."

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_phone
msgid "Phones"
msgstr "Telefoni"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Politica di pubblicazione messaggi su documenti usando il gateway di posta.\n"
"- tutti: tutti possono pubblicare\n"
"- partner: solo partner autenticati\n"
"- follower: solo chi segue il documento collegato o gli iscritti ai relativi canali\n"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__preventive
msgid "Preventive"
msgstr "Preventivo"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_printer
msgid "Printers"
msgstr "Stampanti"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__priority
msgid "Priority"
msgstr "Priorità"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Product Information"
msgstr "Informazioni prodotto"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Ready"
msgstr "Pronto"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__done
msgid "Ready for next stage"
msgstr "Pronto per prossima fase"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Record Colour"
msgstr "Colore record"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Registra ID discussione"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__recurring_maintenance
msgid "Recurrent"
msgstr "Ricorrente"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Reopen Request"
msgstr "Riapri richiesta"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_3
msgid "Repaired"
msgstr "Riparato"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_interval
msgid "Repeat Every"
msgstr "Ripeti ogni"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_unit
msgid "Repeat Unit"
msgstr "Unità di ripetizione"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_reports
msgid "Reporting"
msgstr "Rendiconto"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__request_ids
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Request"
msgstr "Richiesta"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_created
msgid "Request Created"
msgstr "Richiesta creata"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__request_date
msgid "Request Date"
msgstr "Data richiesta"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__done
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__done
msgid "Request Done"
msgstr "Richiesta completata"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid "Request planned for %s"
msgstr "Richiesta pianificata per %s"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Requested By"
msgstr "Richiesto Da"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "Requested by:"
msgstr "Richiesta da"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_ids
msgid "Requests"
msgstr "Richieste"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__technician_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Responsible"
msgstr "Responsabile"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_monitor1
#: model:maintenance.equipment,name:maintenance.equipment_monitor4
#: model:maintenance.equipment,name:maintenance.equipment_monitor6
msgid "Samsung Monitor 15\""
msgstr "Monitor Samsung 15\""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Scheduled"
msgstr "Pianificato"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__schedule_date
msgid "Scheduled Date"
msgstr "Data pianificata"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_4
msgid "Scrap"
msgstr "Scarto"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__scrap_date
msgid "Scrap Date"
msgstr "Data Scarto"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Search"
msgstr "Ricerca"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__serial_no
msgid "Serial Number"
msgstr "Numero di serie"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__archive
msgid ""
"Set archive to true to hide the maintenance request without deleting it."
msgstr ""
"Per nascondere la richiesta di manutenzione senza eliminarla, impostare "
"Archivia a vero."

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.action_maintenance_configuration
#: model:ir.ui.menu,name:maintenance.menu_maintenance_config
msgid "Settings"
msgstr "Impostazioni"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Show all records which has next action date is before today"
msgstr "Mostra tutti i record con data prossima azione precedente a oggi"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_software
msgid "Software"
msgstr "Software"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__stage_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Stage"
msgstr "Fase"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_stage_action
msgid "Stages"
msgstr "Fasi"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_status
msgid "Status Changed"
msgstr "Stato cambiato"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato basato sulle attività\n"
"In ritardo: scadenza già superata\n"
"Oggi: attività in data odierna\n"
"Pianificato: attività future."

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_status
msgid "Status changed"
msgstr "Stato cambiato"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_subcontractor
msgid "Subcontractor"
msgstr "Subappaltatore"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__name
msgid "Subjects"
msgstr "Oggetti"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Team"
msgstr "Team"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__member_ids
msgid "Team Members"
msgstr "Iscritti al team"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__name
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "Team Name"
msgstr "Nome squadra"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_team_action_settings
msgid "Teams"
msgstr "Gruppi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Technician"
msgstr "Tecnico"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_text
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__text
msgid "Text"
msgstr "Testo"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Il modello (tipo di documento Odoo) a cui corrisponde questo alias. Le "
"e-mail in ingresso che non rispondono a un record esistente attiveranno la "
"creazione di un nuovo record per questo modello (es. lavoro di un progetto)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Il nome dell’alias e-mail, es. “jobs” per intercettare le e-mail "
"<<EMAIL>>"

#. module: maintenance
#: model:res.groups,comment:maintenance.group_equipment_manager
msgid "The user will be able to manage equipment."
msgstr "L'utente sarà in grado di gestire l'attrezzatura."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__effective_date
msgid "This date will be used to compute the Mean Time Between Failure."
msgstr ""
"Questa data verrà utilizzata per calcolare il tempo medio tra i guasti."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "To Do"
msgstr "Da fare"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Today Activities"
msgstr "Attività odierne"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Top Priorities"
msgstr "Priorità"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid ""
"Track equipment and link it to an employee or department.\n"
"                You will be able to manage allocations, issues and maintenance of your equipment."
msgstr ""
"Tiene traccia dell'attrezzatura e le collega a un dipendente o ad un dipartimento.\n"
"                Sarà possibile gestire assegnazioni, problematiche e manutenzioni delle attrezzature."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unassigned"
msgstr "Non assegnati"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Under Maintenance"
msgstr "In manutenzione"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unread Messages"
msgstr "Messaggi non letti"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Unscheduled"
msgstr "Non programmate"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_type
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__until
msgid "Until"
msgstr "Fino a"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Upload your PDF file."
msgstr "Caricare il file PDF."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Used in location"
msgstr "In uso nella location"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Vendor"
msgstr "Fornitore"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_ref
msgid "Vendor Reference"
msgstr "Riferimento fornitore"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__0
msgid "Very Low"
msgstr "Molto Bassa"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__warranty_date
msgid "Warranty Expiration Date"
msgstr "Data scadenza garanzia"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__week
msgid "Weeks"
msgstr "Settimane"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__year
msgid "Years"
msgstr "Anni"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid ""
"You cannot delete an equipment category containing equipment or maintenance "
"requests."
msgstr ""
"Non è possibile eliminare una categoria che contiene attrezzature o "
"richieste di manutenzione."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Your instructions"
msgstr "Le tue istruzioni"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "days"
msgstr "giorni"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "e.g. Internal Maintenance"
msgstr "es. manutenzione interna"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "e.g. LED Monitor"
msgstr "es. Monitor LED"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. Monitors"
msgstr "es. Monitor"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "e.g. Screen not working"
msgstr "es. Schermo non funziona"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. domain.com"
msgstr "ad es. domain.com"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_graph
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_pivot
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_tree
msgid "maintenance Request"
msgstr "Richiesta manutenzione"
