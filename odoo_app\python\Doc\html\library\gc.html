<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="gc — Garbage Collector interface" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/gc.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides an interface to the optional garbage collector. It provides the ability to disable the collector, tune the collection frequency, and set debugging options. It also provides acc..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides an interface to the optional garbage collector. It provides the ability to disable the collector, tune the collection frequency, and set debugging options. It also provides acc..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>gc — Garbage Collector interface &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="inspect — Inspect live objects" href="inspect.html" />
    <link rel="prev" title="__future__ — Future statement definitions" href="__future__.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/gc.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="__future__.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="inspect.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/gc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="inspect.html" title="inspect — Inspect live objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="__future__.html" title="__future__ — Future statement definitions"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-gc">
<span id="gc-garbage-collector-interface"></span><h1><a class="reference internal" href="#module-gc" title="gc: Interface to the cycle-detecting garbage collector."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code></a> — Garbage Collector interface<a class="headerlink" href="#module-gc" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module provides an interface to the optional garbage collector.  It
provides the ability to disable the collector, tune the collection frequency,
and set debugging options.  It also provides access to unreachable objects that
the collector found but cannot free.  Since the collector supplements the
reference counting already used in Python, you can disable the collector if you
are sure your program does not create reference cycles.  Automatic collection
can be disabled by calling <code class="docutils literal notranslate"><span class="pre">gc.disable()</span></code>.  To debug a leaking program call
<code class="docutils literal notranslate"><span class="pre">gc.set_debug(gc.DEBUG_LEAK)</span></code>. Notice that this includes
<code class="docutils literal notranslate"><span class="pre">gc.DEBUG_SAVEALL</span></code>, causing garbage-collected objects to be saved in
gc.garbage for inspection.</p>
<p>The <a class="reference internal" href="#module-gc" title="gc: Interface to the cycle-detecting garbage collector."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code></a> module provides the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="gc.enable">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.enable" title="Link to this definition">¶</a></dt>
<dd><p>Enable automatic garbage collection.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.disable">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.disable" title="Link to this definition">¶</a></dt>
<dd><p>Disable automatic garbage collection.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.isenabled">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">isenabled</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.isenabled" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if automatic collection is enabled.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.collect">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">collect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">generation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.collect" title="Link to this definition">¶</a></dt>
<dd><p>With no arguments, run a full collection.  The optional argument <em>generation</em>
may be an integer specifying which generation to collect (from 0 to 2).  A
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if the generation number  is invalid. The number of
unreachable objects found is returned.</p>
<p>The free lists maintained for a number of built-in types are cleared
whenever a full collection or collection of the highest generation (2)
is run.  Not all items in some free lists may be freed due to the
particular implementation, in particular <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>.</p>
<p>The effect of calling <code class="docutils literal notranslate"><span class="pre">gc.collect()</span></code> while the interpreter is already
performing a collection is undefined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.set_debug">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">set_debug</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.set_debug" title="Link to this definition">¶</a></dt>
<dd><p>Set the garbage collection debugging flags. Debugging information will be
written to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>.  See below for a list of debugging flags which can be
combined using bit operations to control debugging.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_debug">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_debug</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_debug" title="Link to this definition">¶</a></dt>
<dd><p>Return the debugging flags currently set.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_objects">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_objects</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">generation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_objects" title="Link to this definition">¶</a></dt>
<dd><p>Returns a list of all objects tracked by the collector, excluding the list
returned. If <em>generation</em> is not None, return only the objects tracked by
the collector that are in that generation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>New <em>generation</em> parameter.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">gc.get_objects</span></code> with argument <code class="docutils literal notranslate"><span class="pre">generation</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_stats">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_stats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_stats" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of three per-generation dictionaries containing collection
statistics since interpreter start.  The number of keys may change
in the future, but currently each dictionary will contain the following
items:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">collections</span></code> is the number of times this generation was collected;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">collected</span></code> is the total number of objects collected inside this
generation;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">uncollectable</span></code> is the total number of objects which were found
to be uncollectable (and were therefore moved to the <a class="reference internal" href="#gc.garbage" title="gc.garbage"><code class="xref py py-data docutils literal notranslate"><span class="pre">garbage</span></code></a>
list) inside this generation.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.set_threshold">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">set_threshold</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">threshold0</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">threshold1</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">threshold2</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.set_threshold" title="Link to this definition">¶</a></dt>
<dd><p>Set the garbage collection thresholds (the collection frequency). Setting
<em>threshold0</em> to zero disables collection.</p>
<p>The GC classifies objects into three generations depending on how many
collection sweeps they have survived.  New objects are placed in the youngest
generation (generation <code class="docutils literal notranslate"><span class="pre">0</span></code>).  If an object survives a collection it is moved
into the next older generation.  Since generation <code class="docutils literal notranslate"><span class="pre">2</span></code> is the oldest
generation, objects in that generation remain there after a collection.  In
order to decide when to run, the collector keeps track of the number object
allocations and deallocations since the last collection.  When the number of
allocations minus the number of deallocations exceeds <em>threshold0</em>, collection
starts.  Initially only generation <code class="docutils literal notranslate"><span class="pre">0</span></code> is examined.  If generation <code class="docutils literal notranslate"><span class="pre">0</span></code> has
been examined more than <em>threshold1</em> times since generation <code class="docutils literal notranslate"><span class="pre">1</span></code> has been
examined, then generation <code class="docutils literal notranslate"><span class="pre">1</span></code> is examined as well.
With the third generation, things are a bit more complicated,
see <a class="reference external" href="https://devguide.python.org/garbage_collector/#collecting-the-oldest-generation">Collecting the oldest generation</a> for more information.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_count">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_count</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_count" title="Link to this definition">¶</a></dt>
<dd><p>Return the current collection  counts as a tuple of <code class="docutils literal notranslate"><span class="pre">(count0,</span> <span class="pre">count1,</span>
<span class="pre">count2)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_threshold">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_threshold</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_threshold" title="Link to this definition">¶</a></dt>
<dd><p>Return the current collection thresholds as a tuple of <code class="docutils literal notranslate"><span class="pre">(threshold0,</span>
<span class="pre">threshold1,</span> <span class="pre">threshold2)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_referrers">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_referrers</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">objs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_referrers" title="Link to this definition">¶</a></dt>
<dd><p>Return the list of objects that directly refer to any of objs. This function
will only locate those containers which support garbage collection; extension
types which do refer to other objects but do not support garbage collection will
not be found.</p>
<p>Note that objects which have already been dereferenced, but which live in cycles
and have not yet been collected by the garbage collector can be listed among the
resulting referrers.  To get only currently live objects, call <a class="reference internal" href="#gc.collect" title="gc.collect"><code class="xref py py-func docutils literal notranslate"><span class="pre">collect()</span></code></a>
before calling <a class="reference internal" href="#gc.get_referrers" title="gc.get_referrers"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_referrers()</span></code></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Care must be taken when using objects returned by <a class="reference internal" href="#gc.get_referrers" title="gc.get_referrers"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_referrers()</span></code></a> because
some of them could still be under construction and hence in a temporarily
invalid state. Avoid using <a class="reference internal" href="#gc.get_referrers" title="gc.get_referrers"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_referrers()</span></code></a> for any purpose other than
debugging.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">gc.get_referrers</span></code> with argument <code class="docutils literal notranslate"><span class="pre">objs</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_referents">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_referents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">objs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_referents" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of objects directly referred to by any of the arguments. The
referents returned are those objects visited by the arguments’ C-level
<a class="reference internal" href="../c-api/typeobj.html#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> methods (if any), and may not be all objects actually
directly reachable.  <a class="reference internal" href="../c-api/typeobj.html#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> methods are supported only by objects
that support garbage collection, and are only required to visit objects that may
be involved in a cycle.  So, for example, if an integer is directly reachable
from an argument, that integer object may or may not appear in the result list.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">gc.get_referents</span></code> with argument <code class="docutils literal notranslate"><span class="pre">objs</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.is_tracked">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">is_tracked</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.is_tracked" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the object is currently tracked by the garbage collector,
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.  As a general rule, instances of atomic types aren’t
tracked and instances of non-atomic types (containers, user-defined
objects…) are.  However, some type-specific optimizations can be present
in order to suppress the garbage collector footprint of simple instances
(e.g. dicts containing only atomic keys and values):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_tracked</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_tracked</span><span class="p">(</span><span class="s2">&quot;a&quot;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_tracked</span><span class="p">([])</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_tracked</span><span class="p">({})</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_tracked</span><span class="p">({</span><span class="s2">&quot;a&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">})</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_tracked</span><span class="p">({</span><span class="s2">&quot;a&quot;</span><span class="p">:</span> <span class="p">[]})</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.is_finalized">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">is_finalized</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gc.is_finalized" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the given object has been finalized by the
garbage collector, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="kc">None</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Lazarus</span><span class="p">:</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__del__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">global</span> <span class="n">x</span>
<span class="gp">... </span>        <span class="n">x</span> <span class="o">=</span> <span class="bp">self</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">lazarus</span> <span class="o">=</span> <span class="n">Lazarus</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_finalized</span><span class="p">(</span><span class="n">lazarus</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">del</span> <span class="n">lazarus</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gc</span><span class="o">.</span><span class="n">is_finalized</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.freeze">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">freeze</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.freeze" title="Link to this definition">¶</a></dt>
<dd><p>Freeze all the objects tracked by the garbage collector; move them to a
permanent generation and ignore them in all the future collections.</p>
<p>If a process will <code class="docutils literal notranslate"><span class="pre">fork()</span></code> without <code class="docutils literal notranslate"><span class="pre">exec()</span></code>, avoiding unnecessary
copy-on-write in child processes will maximize memory sharing and reduce
overall memory usage. This requires both avoiding creation of freed “holes”
in memory pages in the parent process and ensuring that GC collections in
child processes won’t touch the <code class="docutils literal notranslate"><span class="pre">gc_refs</span></code> counter of long-lived objects
originating in the parent process. To accomplish both, call <code class="docutils literal notranslate"><span class="pre">gc.disable()</span></code>
early in the parent process, <code class="docutils literal notranslate"><span class="pre">gc.freeze()</span></code> right before <code class="docutils literal notranslate"><span class="pre">fork()</span></code>, and
<code class="docutils literal notranslate"><span class="pre">gc.enable()</span></code> early in child processes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.unfreeze">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">unfreeze</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.unfreeze" title="Link to this definition">¶</a></dt>
<dd><p>Unfreeze the objects in the permanent generation, put them back into the
oldest generation.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gc.get_freeze_count">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">get_freeze_count</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gc.get_freeze_count" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of objects in the permanent generation.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<p>The following variables are provided for read-only access (you can mutate the
values but should not rebind them):</p>
<dl class="py data">
<dt class="sig sig-object py" id="gc.garbage">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">garbage</span></span><a class="headerlink" href="#gc.garbage" title="Link to this definition">¶</a></dt>
<dd><p>A list of objects which the collector found to be unreachable but could
not be freed (uncollectable objects).  Starting with Python 3.4, this
list should be empty most of the time, except when using instances of
C extension types with a non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> <code class="docutils literal notranslate"><span class="pre">tp_del</span></code> slot.</p>
<p>If <a class="reference internal" href="#gc.DEBUG_SAVEALL" title="gc.DEBUG_SAVEALL"><code class="xref py py-const docutils literal notranslate"><span class="pre">DEBUG_SAVEALL</span></code></a> is set, then all unreachable objects will be
added to this list rather than freed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>If this list is non-empty at <a class="reference internal" href="../glossary.html#term-interpreter-shutdown"><span class="xref std std-term">interpreter shutdown</span></a>, a
<a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a> is emitted, which is silent by default.  If
<a class="reference internal" href="#gc.DEBUG_UNCOLLECTABLE" title="gc.DEBUG_UNCOLLECTABLE"><code class="xref py py-const docutils literal notranslate"><span class="pre">DEBUG_UNCOLLECTABLE</span></code></a> is set, in addition all uncollectable objects
are printed.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Following <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0442/"><strong>PEP 442</strong></a>, objects with a <a class="reference internal" href="../reference/datamodel.html#object.__del__" title="object.__del__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code></a> method don’t end
up in <a class="reference internal" href="#gc.garbage" title="gc.garbage"><code class="xref py py-data docutils literal notranslate"><span class="pre">gc.garbage</span></code></a> anymore.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="gc.callbacks">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">callbacks</span></span><a class="headerlink" href="#gc.callbacks" title="Link to this definition">¶</a></dt>
<dd><p>A list of callbacks that will be invoked by the garbage collector before and
after collection.  The callbacks will be called with two arguments,
<em>phase</em> and <em>info</em>.</p>
<p><em>phase</em> can be one of two values:</p>
<blockquote>
<div><p>“start”: The garbage collection is about to start.</p>
<p>“stop”: The garbage collection has finished.</p>
</div></blockquote>
<p><em>info</em> is a dict providing more information for the callback.  The following
keys are currently defined:</p>
<blockquote>
<div><p>“generation”: The oldest generation being collected.</p>
<p>“collected”: When <em>phase</em> is “stop”, the number of objects
successfully collected.</p>
<p>“uncollectable”: When <em>phase</em> is “stop”, the number of objects
that could not be collected and were put in <a class="reference internal" href="#gc.garbage" title="gc.garbage"><code class="xref py py-data docutils literal notranslate"><span class="pre">garbage</span></code></a>.</p>
</div></blockquote>
<p>Applications can add their own callbacks to this list.  The primary
use cases are:</p>
<blockquote>
<div><p>Gathering statistics about garbage collection, such as how often
various generations are collected, and how long the collection
takes.</p>
<p>Allowing applications to identify and clear their own uncollectable
types when they appear in <a class="reference internal" href="#gc.garbage" title="gc.garbage"><code class="xref py py-data docutils literal notranslate"><span class="pre">garbage</span></code></a>.</p>
</div></blockquote>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<p>The following constants are provided for use with <a class="reference internal" href="#gc.set_debug" title="gc.set_debug"><code class="xref py py-func docutils literal notranslate"><span class="pre">set_debug()</span></code></a>:</p>
<dl class="py data">
<dt class="sig sig-object py" id="gc.DEBUG_STATS">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">DEBUG_STATS</span></span><a class="headerlink" href="#gc.DEBUG_STATS" title="Link to this definition">¶</a></dt>
<dd><p>Print statistics during collection.  This information can be useful when tuning
the collection frequency.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="gc.DEBUG_COLLECTABLE">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">DEBUG_COLLECTABLE</span></span><a class="headerlink" href="#gc.DEBUG_COLLECTABLE" title="Link to this definition">¶</a></dt>
<dd><p>Print information on collectable objects found.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="gc.DEBUG_UNCOLLECTABLE">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">DEBUG_UNCOLLECTABLE</span></span><a class="headerlink" href="#gc.DEBUG_UNCOLLECTABLE" title="Link to this definition">¶</a></dt>
<dd><p>Print information of uncollectable objects found (objects which are not
reachable but cannot be freed by the collector).  These objects will be added
to the <code class="docutils literal notranslate"><span class="pre">garbage</span></code> list.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Also print the contents of the <a class="reference internal" href="#gc.garbage" title="gc.garbage"><code class="xref py py-data docutils literal notranslate"><span class="pre">garbage</span></code></a> list at
<a class="reference internal" href="../glossary.html#term-interpreter-shutdown"><span class="xref std std-term">interpreter shutdown</span></a>, if it isn’t empty.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="gc.DEBUG_SAVEALL">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">DEBUG_SAVEALL</span></span><a class="headerlink" href="#gc.DEBUG_SAVEALL" title="Link to this definition">¶</a></dt>
<dd><p>When set, all unreachable objects found will be appended to <em>garbage</em> rather
than being freed.  This can be useful for debugging a leaking program.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="gc.DEBUG_LEAK">
<span class="sig-prename descclassname"><span class="pre">gc.</span></span><span class="sig-name descname"><span class="pre">DEBUG_LEAK</span></span><a class="headerlink" href="#gc.DEBUG_LEAK" title="Link to this definition">¶</a></dt>
<dd><p>The debugging flags necessary for the collector to print information about a
leaking program (equal to <code class="docutils literal notranslate"><span class="pre">DEBUG_COLLECTABLE</span> <span class="pre">|</span> <span class="pre">DEBUG_UNCOLLECTABLE</span> <span class="pre">|</span>
<span class="pre">DEBUG_SAVEALL</span></code>).</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="__future__.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="inspect.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/gc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="inspect.html" title="inspect — Inspect live objects"
             >next</a> |</li>
        <li class="right" >
          <a href="__future__.html" title="__future__ — Future statement definitions"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>