# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing
# 
# Translators:
# <PERSON> <z<PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# Анд<PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# Сергей <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# Collex100, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid " %(subject)s (final)"
msgstr " %(subject)s (окончательный вариант)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid " %i duplicates have been ignored."
msgstr " %i дубликаты были проигнорированы."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid ""
"\"Damien Roberts\" <<EMAIL>>\n"
"\"Rick Sanchez\" <<EMAIL>>\n"
"<EMAIL>"
msgstr ""
"\"Дэмиен Робертс\" <<EMAIL>>\n"
"\"Rick Sanchez\" <<EMAIL>>\n"
"<EMAIL>"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_count
msgid "# Favorite Filters"
msgstr "# Любимые фильтры"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "$18"
msgstr "$18"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Blacklist"
msgstr "% Черный список"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Bounce"
msgstr "% Отскок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Opt-out"
msgstr "% Отказ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients"
msgstr "% of получатели"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%(contact_name)s subscribed to the following mailing list(s)"
msgstr "%(contact_name)s подписался на следующие списки рассылки"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%(contact_name)s unsubscribed from the following mailing list(s)"
msgstr "%(contact_name)s отписались от следующих списков рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "%Click (Total)"
msgstr "%Клик (Всего)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "%i Contacts have been imported."
msgstr "%i Контакты были импортированы."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_to_list.py:0
#, python-format
msgid "%s Mailing Contacts have been added. "
msgstr "%s Добавлены контакты для рассылки. "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
#, python-format
msgid "(scheduled for %s)"
msgstr "(запланировано на %s)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "100%"
msgstr "100%"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_reports
msgid "24H Stat Mailing Reports"
msgstr "24H Stat Mailing Reports"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr "24H Статистика %(mailing_type)s \"%(mailing_name)s\""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "25%"
msgstr "25%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "400px"
msgstr "400 пикселей"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "50%"
msgstr "50%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "75%"
msgstr "75%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "800px"
msgstr "800px"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid "<b>John DOE</b> • CEO of MyCompany"
msgstr "<b>Джон Доу</b> - генеральный директор MyCompany"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">\n"
"                                        <i class=\"fa fa-envelope-o\"/> Contacts\n"
"                                    </span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">\n"
"                                        <i class=\"fa fa-envelope-o\"/> Контакты\n"
"                                    </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Blacklist</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Черный список</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Bounce</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Подпрыгивание</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Mailings</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Рассылки</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Opt-Out</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Opt-Out</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid ""
"<br/>We want to take this opportunity to welcome you to our ever-growing community!\n"
"                <br/>Your platform is ready for work, it will help you reduce the costs of digital signatures, attract new customers and increase sales."
msgstr ""
"<br/>Пользуясь случаем, мы хотим поприветствовать вас в нашем постоянно растущем сообществе!\n"
"               <br/>Ваша платформа готова к работе, она поможет вам сократить расходы на цифровые подписи, привлечь новых клиентов и увеличить продажи."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font class=\"text-o-color-1\">25 September 2022 - 4:30 PM</font>"
msgstr "<font class=\"text-o-color-1\">25 сентября 2022 - 4:30 PM</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font class=\"text-o-color-1\">26 September 2022 - 1:30 PM</font>"
msgstr "<font class=\"text-o-color-1\">26 сентября 2022 - 1:30 PM</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"<font class=\"text-o-color-2\"><span style=\"font-"
"weight:bolder;\">-20%</span></font>"
msgstr ""
"<font class=\"text-o-color-2\"><span style=\"font-"
"weight:bolder;\">-20%</span></font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<font style=\"color: rgb(12 84 96);\">Don't write about products or services"
" here, write about solutions.</font>"
msgstr ""
"<font style=\"color: rgb(12 84 96);\">Не пишите здесь о продуктах или "
"услугах, пишите о решениях.</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"<font style=\"font-size: 12px;\">Add a caption to enhance the meaning of "
"this image.</font>"
msgstr ""
"<font style=\"font-size: 12px;\">Добавьте надпись, чтобы подчеркнуть смысл "
"этого изображения.</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font style=\"font-size: 18px\">Event Two</font>"
msgstr "<font style=\"font-size: 18px\">Событие второе</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font style=\"font-size: 18px;\">Event One</font>"
msgstr "<font style=\"font-size: 18px;\">Событие первое</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid "<font style=\"font-size: 48px;\">A Punchy Headline</font>"
msgstr "<font style=\"font-size: 48px;\">Эффектный заголовок</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">Броский заголовок</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
msgid ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" invisible=\"not "
"is_blacklisted\" groups=\"base.group_user\"/>"
msgstr ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" invisible=\"not "
"is_blacklisted\" groups=\"base.group_user\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-bar-chart\"/> Compare Version"
msgstr "<i class=\"fa fa-bar-chart\"/> Сравнить версию"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-copy\"/> Create an Alternative"
msgstr "<i class=\"fa fa-copy\"/> Создайте альтернативу"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-envelope\"/> Send this as winner"
msgstr "<i class=\"fa fa-envelope\"/> Отправьте это победителю"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<i class=\"fa fa-envelope\"/><span name=\"ab_test_auto\">\n"
"                                                    Send Winner Now\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope\"/><span name=\"ab_test_auto\">\n"
"                                                    Отправить победителя сейчас\n"
"                                                </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"<i class=\"fa fa-exclamation-triangle text-danger\" aria-hidden=\"true\"/>\n"
"                        The sum of all percentages for this A/B campaign totals"
msgstr ""
"<i class=\"fa fa-exclamation-triangle text-danger\" aria-hidden=\"true\"/>\n"
"                        Сумма всех процентов для этой A/B-кампании составляет"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> Круги"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> Сердца"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh me-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh me-1\"/> Заменить значок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Квадраты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Звезды"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Большие пальцы"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<i>Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services.</i>"
msgstr ""
"<i>Напишите здесь цитату одного из ваших клиентов. Цитаты - отличный способ "
"повысить доверие к вашим товарам или услугам.</i>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" invisible=\"reply_to_mode == 'update' or mailing_model_name in ['mailing.contact', 'res.partner', 'mailing.list']\">\n"
"                                                    To track replies, this address must belong to this database.\n"
"                                                </small>"
msgstr ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" invisible=\"reply_to_mode == 'update' or mailing_model_name in ['mailing.contact', 'res.partner', 'mailing.list']\">\n"
"                                                    Чтобы отслеживать ответы, этот адрес должен принадлежать этой базе данных.\n"
"                                                </small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-calendar-check-o me-2 small my-auto\" aria-label=\"Sent "
"date\"/>"
msgstr ""
"<span class=\"fa fa-calendar-check-o me-2 small my-auto\" aria-label=\"Sent "
"date\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-half me-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"
msgstr ""
"<span class=\"fa fa-hourglass-half me-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-o me-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                            <span class=\"align-self-baseline\">Next Batch</span>"
msgstr ""
"<span class=\"fa fa-hourglass-o me-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                            <span class=\"align-self-baseline\">Следующая партия</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "<span class=\"o_stat_text\">Open Recipient</span>"
msgstr "<span class=\"o_stat_text\">Открытый получатель</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Blacklist</span>"
msgstr "<span class=\"text-muted\">Черный список</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Bounce</span>"
msgstr "<span class=\"text-muted\">Подпрыгивание</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Mailings</span>"
msgstr "<span class=\"text-muted\">Рассылки</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Opt-out</span>"
msgstr "<span class=\"text-muted\">Отказ от услуг</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "<span invisible=\"not mailing_on_mailing_list\">Mailing Contact</span>"
msgstr "<span invisible=\"not mailing_on_mailing_list\">Почтовый контакт</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"canceled_text\">emails have been canceled and will not be "
"sent.</span>"
msgstr ""
"<span name=\"canceled_text\">электронные письма были отменены и не будут "
"отправляться.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"failed_text\">email(s) not sent.</span>"
msgstr "<span name=\"failed_text\">письмо(я) не отправлено.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"failed_text\">emails could not be sent.</span>"
msgstr ""
"<span name=\"failed_text\">не удалось отправить электронное письмо.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"mailing_schedule_type_now_text\">This mailing will be sent as "
"soon as possible.</span>"
msgstr ""
"<span name=\"mailing_schedule_type_now_text\">Эта рассылка будет сделана как"
" можно скорее.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"next_departure_text\">This mailing is scheduled for </span>"
msgstr "<span name=\"next_departure_text\">Эта рассылка запланирована на </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"process_text\">emails are being processed.</span>"
msgstr "<span name=\"process_text\">электронные письма обрабатываются.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"refresh_text\">This mailing will be sent as soon as "
"possible.</span>"
msgstr ""
"<span name=\"refresh_text\">Эта рассылка будет сделана как можно "
"скорее.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"scheduled_text\">email(s) scheduled for </span>"
msgstr "<span name=\"scheduled_text\">письмо(я), запланированное на </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"scheduled_text\">emails are in queue and will be sent "
"soon.</span>"
msgstr ""
"<span name=\"scheduled_text\">письма находятся в очереди и будут отправлены "
"в ближайшее время.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"sent\">emails have been sent.</span>"
msgstr "<span name=\"sent\">письма были отправлены.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.digest_mail_main
msgid "<span style=\"color: #878d97;\">Turn off Mailing Reports</span>"
msgstr "<span style=\"color: #878d97;\">Отключить отчеты по рассылкам</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<span style=\"color: rgb(12 84 96); font-size: 16px; font-weight: "
"bolder;\">Explain the benefits you offer</span>"
msgstr ""
"<span style=\"color: rgb(12 84 96); font-size: 16px; font-weight: "
"bolder;\">Объясните, какие преимущества вы предлагаете</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-size: 11px\">user / month (billed annually)</span>"
msgstr ""
"<span style=\"font-size: 11px\">пользователь / месяц (оплачивается "
"ежегодно)</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">12</span>"
msgstr "<span style=\"font-size: 48px;\">12</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">45</span>"
msgstr "<span style=\"font-size: 48px;\">45</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">8</span>"
msgstr "<span style=\"font-size: 48px;\">8</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "<span style=\"font-weight: bolder;\">50,000+ companies</span> run Odoo."
msgstr ""
"<span style=\"font-weight: bolder;\">50 000+ компаний</span> используют "
"Odoo."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight: bolder;\">DEFAULT</span>"
msgstr "<span style=\"font-weight: bolder;\">ПО УМОЛЧАНИЮ</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "<span style=\"font-weight: bolder;\">GET $20 OFF</span>"
msgstr "<span style=\"font-weight: bolder;\">ПОЛУЧИТЬ СКИДКУ $20</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight: bolder;\">PRO</span>"
msgstr "<span style=\"font-weight: bolder;\">PRO</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight:bolder\">24/7 Support</span>"
msgstr "<span style=\"font-weight:bolder\">Поддержка 24/7</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span style=\"font-weight:bolder\">Advanced</span>\n"
"                                    features"
msgstr ""
"<span style=\"font-weight:bolder\">Расширенный</span>\n"
"                                    возможности"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight:bolder\">Fully customizable</span>"
msgstr "<span style=\"font-weight:bolder\">Полностью настраиваемый</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span style=\"font-weight:bolder\">Total</span>\n"
"                                    management"
msgstr ""
"<span style=\"font-weight:bolder\">Всего</span>\n"
"                                    управление"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Contacts</span>"
msgstr "<span>Контакты</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<span>Design</span>"
msgstr "<span>Дизайн</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "<span>There wasn't enough recipients left for this mailing</span>"
msgstr "<span>Для этой рассылки не хватало адресатов</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Valid Email Recipients</span>"
msgstr "<span>Действительные получатели электронной почты</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "<span>​</span>"
msgstr "<span></span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<strong class=\"d-block\" invisible=\"mailing_type == 'mail' or not ab_testing_enabled or state != 'done' or sent != 0 or failed != 0 or canceled != 0\">\n"
"                                <span name=\"ab_test_text\">There wasn't enough recipients given to this mailing. </span>\n"
"                            </strong>"
msgstr ""
"<strong class=\"d-block\" invisible=\"mailing_type == 'mail' or not ab_testing_enabled or state != 'done' or sent != 0 or failed != 0 or canceled != 0\">\n"
"                                <span name=\"ab_test_text\">Этой рассылке было уделено недостаточно внимания. </span>\n"
"                            </strong>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<u>Refresh <i class=\"fa fa-refresh ms-1\"/></u>"
msgstr "<u>Обновить <i class=\"fa fa-refresh ms-1\"/></u>"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A campaign should be set when A/B test is enabled"
msgstr "Кампания должна быть установлена, когда включено A/B-тестирование"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "A color block"
msgstr "Цветной блок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "Отличный заголовок"

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_subscription_unique_contact_list
msgid ""
"A mailing contact cannot subscribe to the same mailing list multiple times."
msgstr ""
"Один почтовый контакт не может подписаться на одну и ту же рассылку "
"несколько раз."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "A sample of"
msgstr "Образец"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "A short description of this great feature."
msgstr "Краткое описание этой замечательной функции."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "A small explanation of this great feature, in clear words."
msgstr "Небольшое объяснение этой замечательной функции, в понятных словах."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "A unique value"
msgstr "Уникальное значение"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_mailing_id
msgid "A/B Campaign Winner Mailing"
msgstr "Рассылка победителей A/B-кампаний"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "A/B Test"
msgstr "A/B-тестирование"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_mailings_count
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr "A/B-тестирование рассылок #"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_ab_testing_open_winner_mailing
msgid "A/B Test Winner"
msgstr "Победитель A/B-тестирования"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr "A/B-тестирование: %s"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_completed
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr "Завершение кампании A/B-тестирования"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_description
msgid "A/B Testing Description"
msgstr "Описание A/B-тестирования"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid "A/B Testing percentage"
msgstr "Процентное соотношение A/B-тестирования"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#, python-format
msgid "A/B Tests"
msgstr "A/B-тесты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "A/B Tests to review"
msgstr "A/B-тесты для просмотра"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B test option has not been enabled"
msgstr "Опция A/B-тестирования не была включена"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__active
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__active
msgid "Active"
msgstr "Активный"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_mail_server__active_mailing_ids
msgid "Active mailing using this mail server"
msgstr "Активная рассылка с использованием этого почтового сервера"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_ids
msgid "Activities"
msgstr "Активность"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_state
msgid "Activity State"
msgstr "Состояние активности"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Activity Type Icon"
msgstr "Значок типа активности"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Адаптируйте эти три столбца под нужды вашего дизайна. Чтобы дублировать, "
"удалить или переместить столбцы, выберите столбец и используйте верхние "
"значки для выполнения действия."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#, python-format
msgid "Add"
msgstr "Добавить"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_to_list
msgid "Add Contacts to Mailing List"
msgstr "Добавить контакты в список рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Add Mailing Contacts"
msgstr "Добавить контакты для рассылки"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_to_list_action
msgid "Add Selected Contacts to a Mailing List"
msgstr "Добавление выбранных контактов в список рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Add a great slogan."
msgstr "Добавьте отличный слоган."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add and Send Mailing"
msgstr "Добавление и отправка рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Add to List"
msgstr "Добавить в список"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Add to Templates"
msgstr "Добавить шаблон"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "Add to favorite filters"
msgstr "Добавить в избранные фильтры"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Advanced"
msgstr "Дополнительно"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Alert"
msgstr "Оповещение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Bottom"
msgstr "Выровнять по низу"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Center"
msgstr "Выровнять по центру"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Left"
msgstr "Выровнить по левому краю"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Middle"
msgstr "Выровнять по центру"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Right"
msgstr "Выровнить по правому краю"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Top"
msgstr "Выравнивание по верху"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Alignment"
msgstr "Выравнивание"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Aline Turner, CTO"
msgstr "Алин Тернер, CTO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Алин - одна из тех знаковых людей, которые могут сказать, что любят свое "
"дело. Она руководит 100+ штатными разработчиками и заботится о сообществе, "
"состоящем из тысяч разработчиков."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid "All Rights Reserved"
msgstr "Все права защищены"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "Все эти иконки совершенно бесплатны для коммерческого использования."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid "Allow A/B Testing"
msgstr "Разрешить A / B тестирование"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__is_feedback
msgid "Allow Feedback"
msgstr "Разрешить обратную связь"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Allow recipients to blacklist themselves"
msgstr "Позволяет получателям самим вносить себя в черный список"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid ""
"Allow the recipient to manage themselves their state in the blacklist via "
"the unsubscription page."
msgstr ""
"Предоставьте получателю возможность самостоятельно управлять своим "
"состоянием в черном списке на странице отписки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage themselves their state in the blacklist via "
"the unsubscription page. If the option is active, the 'Blacklist Me' button "
"is hidden on the unsubscription page. The 'come Back' button will always be "
"visible in any case to allow leads and partners to re-subscribe."
msgstr ""
"Позволяет получателю самостоятельно управлять своим состоянием в черном "
"списке на странице отписки. Если опция активна, кнопка \"Занести меня в "
"черный список\" будет скрыта на странице отписки. Кнопка \"Вернуться\" будет"
" видна в любом случае, чтобы лиды и партнеры могли повторно подписаться."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Текст альтернативного изображения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text"
msgstr "Альтернативный текст"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Альтернативное текстовое изображение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Альтернативный текст Текст изображения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Amazing pages"
msgstr "Изумительные страницы"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_feedback.xml:0
#, python-format
msgid "An error occurred. Please retry later or contact us."
msgstr ""
"Произошла ошибка. Пожалуйста, повторите попытку позже или свяжитесь с нами."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "An error occurred. Please retry later."
msgstr "Произошла ошибка. Пожалуйста, повторите попытку позже."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "И отличный подзаголовок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "Another color block"
msgstr "Еще один цветовой блок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Another feature"
msgstr "Другая характеристика"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Apply changes"
msgstr "Применить изменения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Archive"
msgstr "Архив"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__archive_src_lists
msgid "Archive source mailing lists"
msgstr "Архивные списки рассылки исходных текстов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr "Архивировано"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Are you sure you want to unsubscribe from our mailing list?"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Are you sure you want to unsubscribe from the mailing list "
"\"%(unsubscribed_lists)s\"?"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid ""
"At least one of the mailing list you are trying to archive is used in an "
"ongoing mailing campaign."
msgstr ""
"По крайней мере один из списков рассылки, который вы пытаетесь "
"заархивировать, используется в текущей кампании рассылки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr "Прикрепить файл"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_attachment_count
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__attachment_ids
msgid "Attachments"
msgstr "Вложения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Auto"
msgstr "Авто"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Average"
msgstr "Средне"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Bounced"
msgstr "Среднее количество отказов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Clicked"
msgstr "Среднее количество кликов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Delivered"
msgstr "Среднее количество доставленных"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Opened"
msgstr "Среднее количество открытых"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Replied"
msgstr "Среднее число ответивших"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Background Color"
msgstr "Цвет фона"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_on_mailing_list
msgid "Based on Mailing Lists"
msgstr "На основе списков рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "Basic features"
msgstr "Базовые функции"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "Basic management"
msgstr "Базовый менеджмент"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Beautiful snippets"
msgstr "Красивые сниппеты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Big Boxes"
msgstr "Большие коробки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__is_blacklisted
msgid "Blacklist"
msgstr "Черный список"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Blacklist (%s)"
msgstr "Черный список (%s)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid "Blacklist Option when Unsubscribing"
msgstr "Опция \"Черный список\" при отмене подписки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Blacklisted"
msgstr "Блокированные"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Адрес в черном списке"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mail_blacklist_mm_menu
msgid "Blacklisted Email Addresses"
msgstr "Адреса электронной почты в черном списке"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Blocklist removal request from portal"
msgstr "Запрос на удаление блок-листа с портала"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist removal request from portal of mailing %(mailing_link)s (document "
"%(record_link)s)"
msgstr ""
"Запрос на удаление из блок-листа с портала рассылки %(mailing_link)s "
"(документ %(record_link)s)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Blocklist request from portal"
msgstr "Запрос блок-листа с портала"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist request from portal of mailing %(mailing_link)s (document "
"%(record_link)s)"
msgstr ""
"Запрос блок-листа с портала рассылки %(mailing_link)s (документ "
"%(record_link)s)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist request from unsubscribe link of mailing %(mailing_link)s (direct "
"link usage)"
msgstr ""
"Запрос блок-листа по ссылке на отписку от рассылки %(mailing_link)s "
"(использование прямой ссылки)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"Blocklist request from unsubscribe link of mailing %(mailing_link)s "
"(document %(record_link)s)"
msgstr ""
"Запрос блок-листа по ссылке отписки от рассылки %(mailing_link)s (документ "
"%(record_link)s)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Blockquote"
msgstr "Цитата"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_arch
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Body"
msgstr "Тело"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Body Width"
msgstr "Ширина кузова"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_html
msgid "Body converted to be sent by mail"
msgstr "Тело, преобразованное для отправки по почте"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Bold"
msgstr "Жирный"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Books"
msgstr "Книги"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Border"
msgstr "Рамка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__message_bounce
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bounce
msgid "Bounce"
msgstr "Отскок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Bounce (%)"
msgstr "Отскок (%)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Bounce happens when a mailing cannot be delivered (fake address, server "
"issues, ...). Check each record to see what went wrong."
msgstr ""
"Отказ происходит, когда рассылка не может быть доставлена (поддельный адрес,"
" проблемы с сервером, ...). Проверьте каждую запись, чтобы узнать, что пошло"
" не так."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__bounced
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__bounce
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Bounced"
msgstr "Возвращенные"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Bounced (%)"
msgstr "Отскок (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "Коэффициент отскока"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Business Benefits on %(expected)i %(mailing_type)s Sent"
msgstr "Преимущества для бизнеса на %(expected)i %(mailing_type)s Отправлено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "Button Label"
msgstr "Текст кнопки"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "By using the <b>Breadcrumb</b>, you can navigate back to the overview."
msgstr "С помощью <b>хлебной крошки</b> вы можете вернуться к обзору."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__calendar_date
msgid "Calendar Date"
msgstr "Календарная дата"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Call to Action"
msgstr "Призыв к действию"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Campaign"
msgstr "Кампания"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
msgid "Campaign Stages"
msgstr "Этапы кампании"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_tag_menu
msgid "Campaign Tags"
msgstr "Теги Кампании"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_utm_campaigns
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
msgid "Campaigns"
msgstr "Кампании"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid ""
"Campaigns are the perfect tool to track results across multiple mailings."
msgstr ""
"Кампании - идеальный инструмент для отслеживания результатов нескольких "
"рассылок."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Cancel"
msgstr "Отменить"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__canceled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__canceled
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__cancel
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Canceled"
msgstr "Отменено"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing_test__email_to
msgid "Carriage-return-separated list of email addresses."
msgstr "Список адресов электронной почты, разделенных по каретке."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__preview
msgid ""
"Catchy preview sentence that encourages recipients to open this email.\n"
"In most inboxes, this is displayed next to the subject.\n"
"Keep it empty if you prefer the first characters of your email content to appear instead."
msgstr ""
"Захватывающее предложение в превью, побуждающее получателей открыть это письмо.\n"
"В большинстве почтовых ящиков оно отображается рядом с темой.\n"
"Оставьте его пустым, если хотите, чтобы вместо него отображались первые символы содержимого вашего письма."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Center"
msgstr "Центр"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Centered Logo"
msgstr "Логотип по центру"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Change Icons"
msgstr "Изменить значки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Check how well your mailing is doing a day after it has been sent"
msgstr ""
"Проверьте, насколько хорошо работает ваша рассылка через день после ее "
"отправки"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_reports
msgid "Check how well your mailing is doing a day after it has been sent."
msgstr ""
"Проверьте, насколько хорошо работает ваша рассылка через день после ее "
"отправки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our books"
msgstr "Посмотрите все наши книги"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our clothes"
msgstr "Посмотрите всю нашу одежду"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our furniture"
msgstr "Посмотрите всю нашу мебель"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Check the email address and click send."
msgstr "Проверьте адрес электронной почты и нажмите кнопку \"Отправить\"."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Choose a date"
msgstr "Выбрать дату"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Choose this <b>theme</b>."
msgstr "Выберите эту <b>тему</b>."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Choose your mailing subscriptions"
msgstr "Выберите подписку на рассылку"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#, python-format
msgid "Click on the ⭐ next to the subject to save this mailing as a"
msgstr "Нажмите на ⭐ рядом с темой, чтобы сохранить это письмо как"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Click on this button to add this mailing to your templates."
msgstr "Нажмите на эту кнопку, чтобы добавить эту рассылку в свои шаблоны."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Click on this paragraph to edit it."
msgstr "Нажмите на этот абзац, чтобы отредактировать его."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__clicked
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Clicked"
msgstr "Нажатый"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Clicked (%)"
msgstr "Нажато (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Clicked On"
msgstr "Нажата"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Clicks"
msgstr "Клики"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing_mobile_preview.xml:0
#, python-format
msgid "Close"
msgstr "Закрыть"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Clothes"
msgstr "Одежда"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__color
msgid "Color Index"
msgstr "Цветовой индекс"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Цветные блоки - это простой и эффективный способ <b>представить и выделить "
"ваш контент</b>. Выберите изображение или цвет для фона. Вы даже можете "
"изменять размер и дублировать блоки, чтобы создать свой собственный макет. "
"Добавьте изображения или значки, чтобы настроить блоки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Columns"
msgstr "Колонки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Come Back"
msgstr "Вернись"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Come back once your mailing has been sent to track who opened your mailing."
msgstr "Зайдите после отправки письма, чтобы отследить, кто его открыл."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__company_name
msgid "Company Name"
msgstr "Название компании"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Comparisons"
msgstr "Сравнение"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
msgid "Configuration"
msgstr "Конфигурация"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Configure Outgoing Mail Servers"
msgstr "Настройка серверов исходящей почты"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Congratulations, I love your first mailing. :)"
msgstr "Поздравляю, мне нравится ваша первая почта :)"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Ошибка подключения (проблема с сервером исходящей почты)"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_partner
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__contact_id
msgid "Contact"
msgstr "Контакты"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__contact_list
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "Contact List"
msgstr "Список контактов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Contact Name"
msgstr "Контактное лицо"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_import__contact_list
msgid "Contact list that will be imported, one contact per line"
msgstr ""
"Список контактов, которые будут импортированы, по одному контакту в строке"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Contact us"
msgstr "Свяжитесь с нами"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__contact_ids
msgid "Contacts"
msgstr "Контакты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr "Продолжить чтение <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Could not retrieve URL: %s"
msgstr "Не удалось получить URL: %s"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Счетчик количества отскакивающих писем для этого контакта"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__country_id
msgid "Country"
msgstr "Страна"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Cover"
msgstr "Обложка"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Create a Mailing"
msgstr "Создайте рассылку"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Create a Mailing List"
msgstr "Создайте список рассылки"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid "Create a mailing campaign"
msgstr "Создайте почтовую кампанию"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Create a mailing contact"
msgstr "Создайте контакт для рассылки"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Create a new mailing"
msgstr "Создайте новую рассылку"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Create an Alternative Version"
msgstr "Создание альтернативной версии"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_uid
msgid "Created by"
msgstr "Создано"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_date
msgid "Created on"
msgstr "Создано"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Creation Date"
msgstr "Дата создания"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
msgid "Creation Period"
msgstr "Период создания"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Custom"
msgstr "Пользовательский"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "ПЕРЕТАЩИТЕ СЮДА СТРОИТЕЛЬНЫЕ БЛОКИ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Пунктирная"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Date"
msgstr "Дата"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__calendar_date
msgid "Date at which the mailing was or will be sent."
msgstr "Дата, когда была или будет отправлена рассылка."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""
"Дата, которая будет использоваться для определения победителя и отправки ему"
" письма"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
msgid "Dedicated Server"
msgstr "Выделенный сервер"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default"
msgstr "По умолчанию"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default Reversed"
msgstr "Отмена дефолта"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Default Server"
msgstr "Сервер по умолчанию"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delete"
msgstr "Удалить"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Delete Blocks"
msgstr "Удалить блоки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Удалите приведенное выше изображение или замените его картинкой, которая "
"иллюстрирует ваше сообщение. Щелкните на изображении, чтобы изменить стиль "
"<em>закругленных углов</em>."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__delivered
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__sent
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Delivered"
msgstr "Доставлено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Delivered (%)"
msgstr "Доставлено (%)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delivered to"
msgstr "Доставлено в"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/snippets.editor.js:0
#, python-format
msgid "Design Options"
msgstr "Опции дизайна"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Design a striking email, define recipients and track its results."
msgstr ""
"Создайте потрясающее письмо, определите получателей и отследите результаты."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Design added to the %s Templates!"
msgstr "Дизайн добавлен в %s Шаблоны!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Design removed from the %s Templates!"
msgstr "Дизайн удален из %s Шаблонов!"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__dest_list_id
msgid "Destination Mailing List"
msgstr "Список рассылки по месту назначения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Discard"
msgstr "Отменить"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Discount Offer"
msgstr "Скидочное предложение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Discover"
msgstr "Обнаружить"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Discover all the features"
msgstr "Раскройте все функции"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Display Inline"
msgstr "Отобразить внутри"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__res_id
msgid "Document ID"
msgstr "Документ ID"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__model
msgid "Document model"
msgstr "Модель документа"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_domain
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Domain"
msgstr "Домен"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
#, python-format
msgid "Domain field"
msgstr "Доменное поле"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Don't forget to send your preferred version"
msgstr "Не забудьте прислать предпочтительную версию"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Don't worry, the mailing contact we created is an internal user."
msgstr ""
"Не волнуйтесь, созданный нами почтовый контакт - это внутренний "
"пользователь."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Пунктирный"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Double"
msgstr "Двойная"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr ""
"Дважды щелкните значок, чтобы заменить его на другой по вашему выбору."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__draft
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__draft
msgid "Draft"
msgstr "Черновик"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Duplicate"
msgstr "Дублировать"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Дублируйте блоки и колонки, чтобы добавить больше возможностей."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Дублирование электронной почты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "ENDOFSUMMER20"
msgstr "КОНЕЦ ЛЕТА20"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Edit Styles"
msgstr "Редактировать стили"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__email
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__mailing_type__mail
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_type__mail
#, python-format
msgid "Email"
msgstr "Email"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Email Blacklisted"
msgstr "Электронная почта в черном списке"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Email Content"
msgstr "Содержимое письма"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/res_users.py:0
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
#, python-format
msgid "Email Marketing"
msgstr "Рекламная рассылка"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
#, python-format
msgid ""
"Email Marketing uses it as its default mail server to send mass mailings"
msgstr ""
"Email Marketing использует его в качестве почтового сервера по умолчанию для"
" массовых рассылок"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr "Цепочка Email"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#, python-format
msgid "Email added to our blocklist"
msgstr "Электронная почта добавлена в наш блок-лист"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Мастер составления Email"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#, python-format
msgid "Email removed from our blocklist"
msgstr "Электронная почта удалена из нашего блок-листа"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Emails"
msgstr "Emails"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_trace_ids
msgid "Emails Statistics"
msgstr "Статистика писем"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Engagement on %(expected)i %(mailing_type)s Sent"
msgstr "Задействовано %(expected)i %(mailing_type)s Отправлено"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__error
msgid "Error"
msgstr "Ошибка"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Event"
msgstr "Событие"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Event heading"
msgstr "Заголовок события"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__error
msgid "Exception"
msgstr "Исключение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Blacklisted Emails"
msgstr "Исключение писем из черного списка"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Exclude Me"
msgstr "Исключи меня"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Opt Out"
msgstr "Исключить отказ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__expected
msgid "Expected"
msgstr "Ожидаемый"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Extended Filters..."
msgstr "Расширенные фильтры..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Facebook"
msgstr "Facebook"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__failed
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Failed"
msgstr "Ошибка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_reason
msgid "Failure reason"
msgstr "Причина отказа"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "Тип отказа"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__favorite
msgid "Favorite"
msgstr "Избранное"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__favorite_date
msgid "Favorite Date"
msgstr "Любимое свидание"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_id
msgid "Favorite Filter"
msgstr "Любимый фильтр"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_filter_action
#: model:ir.ui.menu,name:mass_mailing.mailing_filter_menu_action
msgid "Favorite Filters"
msgstr "Любимые фильтры"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_domain
msgid "Favorite filter domain"
msgstr "Любимый домен фильтра"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature One"
msgstr "Функция один"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Three"
msgstr "Функция три"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Two"
msgstr "Функция два"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Features"
msgstr "Особенности"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Features Grid"
msgstr "Особенности сетки"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Feedback from %(author_name)s"
msgstr "Отзывы от %(author_name)s"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "Размер файла превышает заданный максимум (%s байт)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_domain
msgid "Filter Domain"
msgstr "Фильтр домена"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__name
msgid "Filter Name"
msgstr "Имя Фильтра"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "Filter templates"
msgstr "Шаблоны фильтров"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "Filters saved by me"
msgstr "Фильтры, сохраненные мной"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "First Feature"
msgstr "Первая линейка тарифов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "First feature"
msgstr "Первая особенность"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "First list of Features"
msgstr "Первый список характеристик"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Fit content"
msgstr "Содержание"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_follower_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_partner_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Font Family"
msgstr "Семейство шрифтов"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Шрифт, отличный значок, например. к.-а.-задачи"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footer Center"
msgstr "Футер в центре"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footer Left"
msgstr "Футер слева"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footers"
msgstr "Футеры"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"software, marketing, and customer experience strategies."
msgstr ""
"Основатель и главный визионер, Тони - движущая сила компании. Он любит "
"держать себя в руках, участвуя в разработке программного обеспечения, "
"маркетинга и стратегий работы с клиентами."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__email_from
msgid "From"
msgstr "От"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Full"
msgstr "Полный"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Fullscreen"
msgstr "Полный экран"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Furniture"
msgstr "Мебель"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
msgid ""
"Get your inside sales (CRM) fully integrated with online sales (eCommerce), "
"in-store sales (Point of Sale) and marketplaces like eBay and Amazon."
msgstr ""
"Получите ваши внутренние продажи (CRM) полностью интегрирован с интернет-"
"продажами (электронной коммерции), в магазине продаж (точках продаж) и "
"рынках, такие как eBay и Amazon."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Great Value"
msgstr "Большая ценность"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr "Группировать по"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Group By..."
msgstr "Группировать По…"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизация HTTP"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__has_message
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Headers"
msgstr "Заголовки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 1"
msgstr "Заголовок 1"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 2"
msgstr "Заголовок 2"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 3"
msgstr "Заголовок 3"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Height"
msgstr "Высота"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Here's your coupon code - but hurry! Ends 9/28"
msgstr "Вот ваш код купона - но поторопитесь! Окончание 9/28"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "Самый высокий показатель кликов"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__opened_ratio
msgid "Highest Open Rate"
msgstr "Самый высокий показатель открываемости"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__replied_ratio
msgid "Highest Reply Rate"
msgstr "Наибольшее количество ответов"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_1
msgid "I changed my mind"
msgstr "Я передумал"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_0
msgid "I never subscribed to this list"
msgstr "Я никогда не подписывался на этот список"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_2
msgid "I receive too many emails from this list"
msgstr "Я получаю слишком много писем из этого списка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the"
" ID is needed for several action and controllers."
msgstr ""
"ID соответствующего mail_mail. Это поле представляет собой целое поле, так "
"как связанные с mail_mail могут быть удалены отдельно от своей статистики. "
"Однако ID необходим для нескольких действий и контроллеров."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_icon
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Icon"
msgstr "Иконка"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий исключение Дела."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid ""
"If checked, recipients will be mailed only once for the whole campaign. This"
" lets you send different mailings to randomly selected recipients and test "
"the effectiveness of the mailings, without causing duplicate messages."
msgstr ""
"Если флажок установлен, сообщение получателям будет отправляться только один"
" раз для всей кампании. Это позволяет отправлять разные рассылки случайным "
"образом выбранным получателям и проверять эффективность почтовых рассылок, "
"не вызывая повторяющихся сообщений."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid ""
"If set, a mass mailing will be created so that you can track its results in "
"the Email Marketing app."
msgstr ""
"Если установить этот параметр, будет создана массовая рассылка, и вы сможете"
" отслеживать ее результаты в приложении Email Marketing."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Если адрес электронной почты находится в черном списке, контакт больше не "
"будет получать массовую рассылку из любого списка"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Ignored"
msgstr "Игнорировать"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Image - Text"
msgstr "Изображение - Текст"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Image Text Image"
msgstr "Изображение Текст Изображение"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"Размер изображения слишком велик, импортируемые изображения должны быть "
"меньше 42 миллионов пикселей"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Images"
msgstr "Изображения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Import"
msgstr "Импорт"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "Import Contacts"
msgstr "Контакты по импорту"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_import_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#, python-format
msgid "Import Mailing Contacts"
msgstr "Импорт контактов для рассылки"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_contact.py:0
#, python-format
msgid "Import Template for Mailing List Contacts"
msgstr "Шаблон импорта для контактов списка рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Import contacts in"
msgstr "Импорт контактов в"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__in_queue
msgid "In Queue"
msgstr "В очереди"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "Inline field"
msgstr "Встроенное поле"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Inner Content"
msgstr "Внутреннее содержание"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Instagram"
msgstr "Instagram"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Неверный адрес электронной почты"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "Неверный адрес"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Iris Joe, CFO"
msgstr "Айрис Джо, финансовый директор"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Благодаря своему международному опыту Айрис помогает нам легко понимать "
"цифры и улучшать их. Она стремится к успеху и использует свои "
"профессиональные навыки, чтобы вывести компанию на новый уровень."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_ab_test_sent
msgid "Is Ab Test Sent"
msgstr "Отправлен ли Ab-тест"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Is Body Empty"
msgstr "Пусто ли тело"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_is_follower
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr "Активирована ли кампания рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_is_winner_mailing
msgid "Is the Winner of its Campaign"
msgstr "Победитель своей кампании"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Italic"
msgstr "Курсив"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Items"
msgstr "Элементы"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Join us and make your company a better place."
msgstr "Присоединяйтесь к нам и сделайте свою компанию лучше."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__kpi_mail_required
msgid "KPI mail required"
msgstr "Требуется почта KPI"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__keep_archives
msgid "Keep Archives"
msgstr "Сохраняйте архивы"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "LOGIN"
msgstr "ВОЙТИ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__lang
msgid "Language"
msgstr "Язык"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Large"
msgstr "Большой"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Last Feature"
msgstr "Последняя особенность"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Last State Update"
msgstr "Последнее состояние обновления"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Left"
msgstr "Слева"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Left Logo"
msgstr "Логотип слева"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Left Text"
msgstr "Текст слева"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Let's try the Email Marketing app."
msgstr "Давайте попробуем приложение Email Marketing."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
#: model:ir.ui.menu,name:mass_mailing.link_tracker_menu_mass_mailing
msgid "Link Tracker"
msgstr "Трекер ссылок"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Отслеживание ссылок Нажмите"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Link Trackers will measure how many times each link is clicked as well as "
"the proportion of %s who clicked at least once in your mailing."
msgstr ""
"Система отслеживания ссылок будет измерять количество переходов по каждой "
"ссылке, а также долю %s, которые хотя бы раз перешли по вашей рассылке."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Links"
msgstr "Ссылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_ids
msgid "Links click"
msgstr "Нажмите ссылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__mailing_list_ids
msgid "Lists"
msgstr "Списки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "London, United Kingdom"
msgstr "Лондон, Соединенное Королевство"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__mailing_type__mail
msgid "Mail"
msgstr "Почта"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_blacklist
msgid "Mail Blacklist"
msgstr "Черный список почты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr "Тело письма"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Mail Debug"
msgstr "Отладка почты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Mail ID"
msgstr "ID почты"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid "Mail ID (tech)"
msgstr "Почтовый идентификатор (техник)"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_ab_testing_ir_actions_server
msgid "Mail Marketing: A/B Testing"
msgstr "Почтовый маркетинг: A/B-тестирование"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_queue_ir_actions_server
msgid "Mail Marketing: Process queue"
msgstr "Почтовый маркетинг: Технологическая очередь"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Миксин почтового рендеринга"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_mail_server
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_mail_server_id
msgid "Mail Server"
msgstr "Почтовый Сервер"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_available
msgid "Mail Server Available"
msgstr "Доступен почтовый сервер"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mailing_trace_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
msgid "Mail Statistics"
msgstr "Статистика почты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Mail Traces"
msgstr "Почтовые следы"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailing"
msgstr "Рассылка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__campaign
msgid "Mailing Campaign"
msgstr "Почтовая кампания"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid "Mailing Campaigns"
msgstr "Почтовые кампании"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact
msgid "Mailing Contact"
msgstr "Почтовый контакт"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_import
msgid "Mailing Contact Import"
msgstr "Импорт почтовых контактов"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_model__is_mailing_enabled
msgid "Mailing Enabled"
msgstr "Включена рассылка"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_filter
msgid "Mailing Favorite Filters"
msgstr "Избранные фильтры рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "Mailing Filters"
msgstr "Фильтры рассылки"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__mailing_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__list_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Mailing List"
msgstr "Список рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Mailing List #"
msgstr "Список рассылки #"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Mailing List Contacts"
msgstr "Контакты рассылки"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_subscription
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
msgid "Mailing List Subscription"
msgstr "Подписка на рассылку"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Mailing List Subscriptions"
msgstr "Подписки на рассылку"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__src_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__contact_list_ids
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_mailing_list_menu
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Mailing Lists"
msgstr "Списки рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid "Mailing Reports Turned Off"
msgstr "Отключение рассылки отчетов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid ""
"Mailing Reports have been turned off for all users. <br/>\n"
"                                If needed, they can be turned back on from the"
msgstr ""
"Отчеты о рассылке были отключены для всех пользователей. <br/>\n"
"                                При необходимости их можно снова включить в разделе"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Статистика рассылок"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_subscription_optout
msgid "Mailing Subscription Reason"
msgstr "Причина подписки на рассылку"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Mailing Subscriptions"
msgstr "Подписка на рассылку"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr "Тест рассылки"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_action
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
msgid "Mailing Traces"
msgstr "Следы рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "Тип рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type_description
msgid "Mailing Type Description"
msgstr "Тип рассылки Описание"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Mailing addresses incorrect: %s"
msgstr "Неверные почтовые адреса: %s"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"business contact directory."
msgstr ""
"Рассылка контактов позволяет отделить маркетинговую аудиторию от каталога "
"деловых контактов."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Mailing filters"
msgstr "Фильтры рассылки"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_action_mail
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mailings"
msgstr "Рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings that are assigned to me"
msgstr "Рассылки, которые назначены мне"

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr "Управление кампаниями массовой рассылки"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Manage Subscriptions"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Manage mass mailing campaigns"
msgstr "Управление кампаниями массовой рассылки"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__manual
msgid "Manual"
msgstr "Руководство"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Marketing"
msgstr "Маркетинг"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Marketing Content"
msgstr "Маркетинговый контент"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Masonry"
msgstr "Кирпичная кладка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__name
msgid "Mass Mail"
msgstr "Массовая рассылка"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__mass_mailing_id
#: model:ir.ui.menu,name:mass_mailing.mailing_mailing_menu_technical
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Mass Mailing"
msgstr "Массовая рассылка"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
#, python-format
msgid "Mass Mailing \"%s\""
msgstr "Массовая рассылка \"%s\""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_report_action_mail
#: model:ir.ui.menu,name:mass_mailing.mailing_menu_report_mailing
msgid "Mass Mailing Analysis"
msgstr "Анализ массовых рассылок"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Mass Mailing Campaign"
msgstr "Кампания массовой рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid "Mass Mailing Name"
msgstr "Имя массовой рассылки"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Mass Mailing Statistics"
msgstr "Статистика массовых рассылок"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_trace_report_action_mail
msgid ""
"Mass Mailing Statistics allows you to check different mailing related information\n"
"    like number of bounced mails, opened mails, replied mails. You can sort out\n"
"    your analysis by different groups to get accurate grained analysis."
msgstr ""
"Статистика массовых рассылок позволяет проверять различную информацию, связанную с рассылкой\n"
"    например, количество отбитых писем, открытых писем, отвеченных писем. Вы можете отсортировать\n"
"    анализ по различным группам, чтобы получить точный анализ."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_ids
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Массовые рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Media List"
msgstr "Список СМИ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Media heading"
msgstr "Медиа заголовок"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__medium_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Medium"
msgstr "Средний"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "Membership updated"
msgstr "Абонемент обновлён"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_list_merge_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge"
msgstr "Объединить"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list_merge
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge Mass Mailing List"
msgstr "Слияние списков массовой рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__merge_options
msgid "Merge Option"
msgstr "Опция слияния"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__new
msgid "Merge into a new mailing list"
msgstr "Объединение в новый список рассылки"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__existing
msgid "Merge into an existing mailing list"
msgstr "Включение в существующий список рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__message_id
msgid "Message-ID"
msgstr "Message-ID"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Mich Stark, COO"
msgstr "Мих Старк, COO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Миш любит принимать вызовы. Благодаря своему многолетнему опыту работы "
"коммерческим директором в сфере программного обеспечения Мич помог компании "
"достичь того уровня, на котором она находится сегодня. Мич входит в число "
"лучших умов."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid ""
"Michael Fletcher<br/>\n"
"                   <span style=\"font-size: 12px; font-weight: bolder;\">Customer Service</span>"
msgstr ""
"Майкл Флетчер<br/>\n"
"                  <span style=\"font-size: 12px; font-weight: bolder;\">Обслуживание клиентов</span>"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Не указан адрес электронной почты"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "Отсутствует по адресу"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#, python-format
msgid "Mobile Preview"
msgstr "Мобильный просмотр"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
#, python-format
msgid "Model field"
msgstr "Модельное поле"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_model
msgid "Models"
msgstr "Модели"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "More"
msgstr "Больше"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "More Details"
msgstr "Подробнее"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "More Info"
msgstr "Больше информации"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Mosaic"
msgstr "Мозаика"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "My Company"
msgstr "Компания"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "My Filters"
msgstr "Мои фильтры"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "My Mailings"
msgstr "Мои рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Name"
msgstr "Имя"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Name / Email"
msgstr "Имя / Email"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__new_list_name
msgid "New Mailing List Name"
msgstr "Новое имя списка рассылки"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "New contacts imported"
msgstr "Импорт новых контактов"

#. module: mass_mailing
#: model:utm.campaign,title:mass_mailing.mass_mail_campaign_1
msgid "Newsletter"
msgstr "Новостная рассылка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следующее событие календаря активности"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Следующий срок мероприятия"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего действия"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_id
msgid "Next Activity Type"
msgstr "Следующий Тип Мероприятия"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure_is_past
msgid "Next Departure Is Past"
msgstr "Следующий Отъезд в прошлое"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s address bounced yet!"
msgstr "Ни один адрес %s еще не отскочил!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s clicked your mailing yet!"
msgstr "Ни один %s еще не кликнул на вашу рассылку!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s opened your mailing yet!"
msgstr "Ни один %s еще не открыл вашу рассылку!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s received your mailing yet!"
msgstr "Ни один %s еще не получил вашу рассылку!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No %s replied to your mailing yet!"
msgstr "Ни один %s еще не ответил на вашу рассылку!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid ""
"No contacts were imported. All email addresses are already in the mailing "
"list."
msgstr ""
"Не было импортировано ни одного контакта. Все адреса электронной почты уже "
"содержатся в списке рассылки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "No customization"
msgstr "Нет настройки"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mail_mail_statistics_mailing
msgid "No data yet!"
msgstr "Пока нет данных!"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_action_report_optout
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_optout_action
msgid "No data yet."
msgstr "Данных пока нет."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No mailing campaign has been found"
msgstr "Не найдено ни одной почтовой кампании"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"No mailing for this A/B testing campaign has been sent yet! Send one first "
"and try again later."
msgstr ""
"Рассылка для этой кампании A/B-тестирования еще не отправлена! Отправьте ее "
"сначала и повторите попытку позже."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid ""
"No need to import mailing lists, you can send mailings to contacts saved in "
"other Odoo apps."
msgstr ""
"Нет необходимости импортировать списки рассылки, вы можете отправлять "
"рассылки по контактам, сохраненным в других приложениях Odoo."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_filter_action
msgid "No saved filter yet!"
msgstr "Пока нет сохраненного фильтра!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "No support"
msgstr "Нет поддержки"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "No valid email address found."
msgstr "Не найден действующий адрес электронной почты."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "None"
msgstr "Нет"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email_normalized
msgid "Normalized Email"
msgstr "Нормализованная электронная почта"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__email
msgid "Normalized email address"
msgstr "Нормализованный адрес электронной почты"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Not subscribed"
msgstr "Не подписаны"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_blacklisted
msgid "Number of Blacklisted"
msgstr "Количество занесенных в черный список"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicks_ratio
msgid "Number of Clicks"
msgstr "Количество кликов"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count
msgid "Number of Contacts"
msgstr "Количество контактов"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_email
msgid "Number of Emails"
msgstr "Количество писем"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_count
msgid "Number of Mailing"
msgstr "Номер рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr "Количество массовых рассылок"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_opt_out
msgid "Number of Opted-out"
msgstr "Количество отказавшихся"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Number of Recipients"
msgstr "Количество получателей"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid "Number of bounced email."
msgstr "Количество отклоненных email."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Numbers"
msgstr "Числа"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "ON YOUR NEXT ORDER!"
msgstr "НА СЛЕДУЮЩИЙ ЗАКАЗ!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "OPENED (%i)"
msgstr "ОТКРЫТО (%i)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
msgid "Omnichannel sales"
msgstr "Омни канал продаж"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Once the best version is identified, we will send the best one to the "
"remaining recipients."
msgstr ""
"Как только будет определена лучшая версия, мы разошлем ее остальным "
"получателям."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"Once you send these emails, they'll be making a grand entrance in all the "
"inboxes, creating quite the buzz!"
msgstr ""
"Как только вы отправите эти письма, они появятся во всех почтовых ящиках и "
"вызовут большой резонанс!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Open Date"
msgstr "День начала"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Open Recipient"
msgstr "Открытый получатель"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__opened
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__open
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Opened"
msgstr "Открыто"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Opened (%)"
msgstr "Открыто (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__open_datetime
msgid "Opened On"
msgstr "Открыто"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Открытое соотношение"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__opt_out
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out
msgid "Opt Out"
msgstr "Отписаться"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__opt_out
msgid ""
"Opt out flag for a specific mailing list. This field should not be used in a"
" view without a unique and active mailing list context."
msgstr ""
"Флаг отказа от рассылки для конкретного списка рассылки. Это поле не должно "
"использоваться в представлении без уникального и активного контекста списка "
"рассылки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Opt-out (%)"
msgstr "Отказ (%)"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_optout
msgid "Opted Out"
msgstr "Отказался"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Opted-out"
msgstr "Отказ от услуг"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Необязательный язык перевода (код ISO) для выбора при отправке сообщения "
"электронной почты. Если он не задан, будет использоваться английская версия."
" Как правило, это должно быть выражение-заполнитель, обеспечивающее "
"соответствующий язык, например {{ object.partner_id.lang }}."

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mailing_menu_report_subscribe_reason
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Optout"
msgstr "Отказ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Optout Reason"
msgstr "Причина отказа"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_subscription_optout_action
#: model:ir.ui.menu,name:mass_mailing.mailing_subscription_optout_menu
msgid "Optout Reasons"
msgstr "Причины отказа"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_4
msgid "Other"
msgstr "Другое"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "Our References"
msgstr "Наши рекомендации"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__outgoing
msgid "Outgoing"
msgstr "Исходящие"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr "Исходящие письма"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Padding ↕"
msgstr "Набивка ↕"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Padding ⭤"
msgstr "Набивка ⭤"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__pending
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__pending
msgid "Pending"
msgstr "В ожидании"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_blacklisted
msgid "Percentage of Blacklisted"
msgstr "Процент попавших в черный список"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_bounce
msgid "Percentage of Bouncing"
msgstr "Процент отказов"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_opt_out
msgid "Percentage of Opted-out"
msgstr "Процент отказавшихся"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be chosen "
"randomly."
msgstr ""
"Процент контактов, которые будут отправлены по почте. Получатели будут "
"выбраны случайным образом."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Pick a dedicated outgoing mail server for your mass mailings"
msgstr "Выберите выделенный сервер исходящей почты для массовых рассылок"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Pick the <b>email subject</b>."
msgstr "Выберите <b>тему письма</b>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Picture"
msgstr "Изображение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Plain Text"
msgstr "Простой текст"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_portal_subscription_feedback.js:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Please let us know why you updated your subscription."
msgstr "Пожалуйста, сообщите нам, почему вы обновили подписку."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_portal_subscription_feedback.js:0
#, python-format
msgid "Please let us know why you want to be in our block list."
msgstr ""
"Пожалуйста, сообщите нам, почему вы хотите попасть в наш список блокировки."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
#, python-format
msgid "Please provide a name for the filter"
msgstr "Укажите название фильтра"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Post heading"
msgstr "Заголовок сообщения"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to
msgid "Preferred Reply-To Address"
msgstr "Предпочтительный адрес ответа-получения"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__preview
msgid "Preview"
msgstr "Предпросмотр"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Preview Text"
msgstr "Предварительный просмотр текста"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Primary Buttons"
msgstr "Первичные кнопки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__process
msgid "Process"
msgstr "Процесс"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__processing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__process
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Processing"
msgstr "Обработка"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Progress bar"
msgstr "Прогресс-бар"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Promo Code"
msgstr "Промо код"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "Постарайтесь сосредоточиться на том, что вы должны сказать!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating
msgid "Quality"
msgstr "Качество"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr "ПРИНЯТО (%i)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "REPLIED (%i)"
msgstr "ПОВТОРЯЕТСЯ (%i)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Rating"
msgstr "Рейтинг"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__rating_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "Read More"
msgstr "Подробнее"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Ready for take-off!"
msgstr "Готовы к взлету!"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_blacklist__opt_out_reason_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out_reason_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_search
msgid "Reason"
msgstr "Причина"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr "Получено"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__received_ratio
msgid "Received Ratio"
msgstr "Полученное соотношение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
msgid "Recipient"
msgstr "Получатель"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Recipient Address"
msgstr "Адрес получателя"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__update
msgid "Recipient Followers"
msgstr "Последователи получателя"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Recipients"
msgstr "Получатели"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_model_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Получатели Модель"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_model_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_name
msgid "Recipients Model Name"
msgstr "Название модели получателей"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_real
msgid "Recipients Real Model"
msgstr "Реальная модель получателей"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "Redeem Discount!"
msgstr "Воспользуйтесь скидкой!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "References"
msgstr "Ссылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "Register Now"
msgstr "Зарегистрируйтесь сейчас"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Regular"
msgstr "Обычный"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Reload a favorite filter"
msgstr "Перезагрузка избранного фильтра"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "Remove from Favorites"
msgstr "Удалить из избранного"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#, python-format
msgid "Remove from Templates"
msgstr "Удалить из шаблонов"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__render_model
msgid "Rendering Model"
msgstr "Модель рендеринга"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__replied
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__reply
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Replied"
msgstr "Отвечено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Replied (%)"
msgstr "Ответили (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__reply_datetime
msgid "Replied On"
msgstr "Ответил"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Ответное соотношение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Reply Date"
msgstr "Дата ответа"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to
msgid "Reply To"
msgstr "Ответить"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to_mode
msgid "Reply-To Mode"
msgstr "Режим ответа на сообщение"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reporting"
msgstr "Отчет"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__user_id
msgid "Responsible"
msgstr "Ответственный"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_user_id
msgid "Responsible User"
msgstr "Ответственный пользователь"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Restore"
msgstr "Восстановить"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr "Повторить"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Right"
msgstr "Справа"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Круглые углы"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Sample Icons"
msgstr "Образцы значков"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_test
msgid "Sample Mail Wizard"
msgstr "Мастер создания образцов писем"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "Save as Favorite Filter"
msgstr "Сохранить как избранный фильтр"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__create_uid
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Saved by"
msgstr "Спасен"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Schedule"
msgstr "Расписание"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Scheduled"
msgstr "Запланировано"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled_date
msgid "Scheduled Date"
msgstr "Планируемая дата"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Scheduled On"
msgstr "Запланировано на"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Scheduled Period"
msgstr "Плановый период"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure
msgid "Scheduled date"
msgstr "Планируемая дата"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__schedule_date
msgid "Scheduled for"
msgstr "Запланировано для"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.next_departure.value}"
msgstr "Запланировано на #{record.next_departure.value}"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.schedule_date.value}"
msgstr "Запланировано на #{record.schedule_date.value}"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Score"
msgstr "Оценка"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_model.py:0
#, python-format
msgid ""
"Searching Mailing Enabled models supports only direct search using '='' or "
"'!='."
msgstr ""
"Поиск моделей с поддержкой рассылки поддерживает только прямой поиск с "
"использованием '='' или '!=''."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Second Feature"
msgstr "Вторая линейка тарифов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Second feature"
msgstr "Вторая особенность"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Second list of Features"
msgstr "Второй список характеристик"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Secondary Buttons"
msgstr "Вторичные кнопки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Выберите и удалите блоки, чтобы удалить функции."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Select mailing lists"
msgstr "Выберите списки рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists..."
msgstr "Выберите списки рассылки..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists:"
msgstr "Выберите списки рассылки:"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr "Выбор для определения победителя рассылки, которая будет отправлена."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Send"
msgstr "Отправить"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr "Отправить окончательный вариант"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__email_from
msgid "Send From"
msgstr "Отправить из"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Send Mailing"
msgstr "Отправить рассылку"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_compose_form_mass_mailing
msgid "Send Mass Mailing"
msgstr "Отправка массовой рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr "Отправить письмо с образцом"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Send a report to the mailing responsible one day after the mailing has been "
"sent."
msgstr ""
"Отправьте отчет ответственному за рассылку через день после того, как "
"рассылка была отправлена."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a sample mailing for testing purpose to the address below."
msgstr "Отправьте образец письма для проверки по указанному ниже адресу."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__now
msgid "Send now"
msgstr "Отправить сейчас"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Send on"
msgstr "Отправить"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send test"
msgstr "Оправить тест"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__sending
msgid "Sending"
msgstr "Отправка"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__done
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__pending
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__done
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Sent"
msgstr "Отправлено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent By"
msgstr "Кем Отправлено"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent_date
msgid "Sent Date"
msgstr "Дата отправления"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Sent Mailings"
msgstr "Отправленные рассылки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__sent_datetime
msgid "Sent On"
msgstr "Отправлено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Period"
msgstr "Период отправки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Sent on #{record.sent_date.value}"
msgstr "Отправлено на #{record.sent_date.value}"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_feedback.xml:0
#, python-format
msgid "Sent. Thanks you for your feedback!"
msgstr "Отправлено. Спасибо за ваш отзыв!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Separator"
msgstr "Разделитель"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Separators"
msgstr "Разделители"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Settings"
msgstr "Настройки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid "Settings Menu."
msgstr "Меню настроек."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__is_public
msgid "Show In Preferences"
msgstr "Показать в настройках"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Showcase"
msgstr "Витрина"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Signature"
msgstr "Подпись"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Since the date and time for this test has not been scheduled, don't forget "
"to manually send your preferred version."
msgstr ""
"Поскольку дата и время проведения этого теста еще не назначены, не забудьте "
"вручную отправить предпочтительный вариант."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Size"
msgstr "Размер"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Small"
msgstr "Маленький"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Сплошная"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Source"
msgstr "Источник"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Спикеры со всего мира присоединятся к нашим экспертам, чтобы выступить с "
"вдохновляющими докладами на различные темы. Будьте в курсе последних "
"тенденций в области управления бизнесом и &amp; технологий"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__new
msgid "Specified Email Address"
msgstr "Указанный адрес электронной почты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Start From Scratch"
msgstr "Начать с нуля"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Start by creating your first <b>Mailing</b>."
msgstr "Начните с создания своей первой <b>рассылки</b>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "State"
msgstr "Область"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_trace_ids
msgid "Statistics"
msgstr "Статистика"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__state
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_status
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "Статус"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус, основанный на мероприятии\n"
"Просроченная: Дата, уже прошла\n"
"Сегодня: Дата мероприятия сегодня\n"
"Запланировано: будущая деятельность."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr "Сохраняет дату последнего клика в случае нескольких кликов."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Stretch to Equal Height"
msgstr "Растянуть до одинаковой высоты"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__subject
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Subject"
msgstr "Тема"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "Subscribed"
msgstr "В подписках"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Subscription Date"
msgstr "Дата подписки"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__subscription_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__subscription_ids
msgid "Subscription Information"
msgstr "Информация о подписке"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_subscription_action_report_optout
msgid "Subscriptions"
msgstr "Подписки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Successfully Unsubscribed"
msgstr "Подписка успешно отменена"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Successfully unsubscribed!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__tag_ids
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Tags"
msgstr "Теги"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Take Future Schedule Date"
msgstr "Возьмите будущую дату расписания"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Team"
msgstr "Команда"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_available
msgid ""
"Technical field used to know if the user has activated the outgoing mail "
"server option in the settings"
msgstr ""
"Техническое поле, используемое для того, чтобы узнать, активировал ли "
"пользователь опцию сервера исходящей почты в настройках"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Tell what's the value for the customer for this feature."
msgstr "Расскажите, какова ценность для клиента этой функции."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Template"
msgstr "Шаблон"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Test"
msgstr "Тест"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Test Mailing"
msgstr "Тестовая рассылка"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing could not be sent to %s:"
msgstr "Тестовая рассылка не может быть отправлена на %s:"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing successfully sent to %s"
msgstr "Тестовая рассылка успешно отправлена на %s"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Test this mailing by sending a copy to yourself."
msgstr "Проверьте эту рассылку, отправив копию самому себе."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__test
msgid "Tested"
msgstr "Проверено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Text"
msgstr "Текст"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Text - Image"
msgstr "Текст - Изображение"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Text Highlight"
msgstr "Выделение текста"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Text Image Text"
msgstr "Текст Изображение Текст"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Thank you for joining us!"
msgstr "Большое спасибо, что вы с нами!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid "That way, Odoo evolves much faster than any other solution."
msgstr ""
"Таким образом, Odoo развивается гораздо быстрее, чем любое другое решение."

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_mailing_percentage_valid
msgid "The A/B Testing Percentage needs to be between 0 and 100%"
msgstr "Процент A/B-тестирования должен составлять от 0 до 100%"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr "Контакт решил больше не получать письма из этого списка"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_3
msgid "The content of these emails is not relevant to me"
msgstr "Содержание этих писем не имеет для меня никакого значения"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_filter.py:0
#, python-format
msgid "The filter domain is not valid for this recipients."
msgstr "Домен фильтра не действителен для этого получателя."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_list__is_public
msgid ""
"The mailing list can be accessible by recipients in the subscription "
"management page to allow them to update their preferences."
msgstr ""
"Список рассылки может быть доступен получателям на странице управления "
"подпиской, чтобы они могли обновить свои предпочтения."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                    business experts to build hundreds of apps in just a few years."
msgstr ""
"Модель Odoo с открытым исходным кодом позволила нам привлечь тысячи разработчиков и\n"
"                    и бизнес-экспертов для создания сотен приложений всего за несколько лет."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of "
"developers and business experts to build hundreds of apps in just a few "
"years."
msgstr ""
"Модель Odoo с открытым исходным кодом позволила нам привлечь тысячи "
"разработчиков и бизнес-экспертов для создания сотен приложений всего за "
"несколько лет."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"The saved filter targets different recipients and is incompatible with this "
"mailing."
msgstr ""
"Сохраненный фильтр нацелен на других получателей и несовместим с этой "
"рассылкой."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"The winner has already been sent. Use <b>Compare Version</b> to get an "
"overview of this A/B testing campaign."
msgstr ""
"Победитель уже отправлен. Используйте функцию <b>Compare Version</b>, чтобы "
"получить обзор этой кампании A/B-тестирования."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Then on"
msgstr "Затем на"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "There are no recipients selected."
msgstr "Получатели не выбраны."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Third Feature"
msgstr "Третья функция"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "This"
msgstr "Это"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"This email from can not be used with this mail server.\n"
"Your emails might be marked as spam on the mail clients."
msgstr ""
"Этот адрес электронной почты не может быть использован на данном почтовом сервере.\n"
"Ваши письма могут быть помечены как спам на почтовых клиентах."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mail_thread.py:0
#, python-format
msgid ""
"This email has been automatically added in blocklist because of too much "
"bounced."
msgstr ""
"Это письмо было автоматически добавлено в блок-лист из-за слишком большого "
"количества отказов."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Этот адрес электронной почты занесен в черный список для массовых рассылок. "
"Нажмите, чтобы исключить из черного списка."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Это поле используется для поиска по адресу электронной почты, поскольку "
"основное поле электронной почты может содержать не только адрес электронной "
"почты."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid ""
"This is useful if your marketing campaigns are composed of several emails"
msgstr ""
"Это полезно, если ваши маркетинговые кампании состоят из нескольких "
"электронных писем"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "This mailing has no selected design (yet!)."
msgstr "У этой рассылки нет выбранного дизайна (пока!)."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This tool is advised if your marketing campaign is composed of several "
"emails."
msgstr ""
"Этот инструмент рекомендуется, если ваша маркетинговая кампания состоит из "
"нескольких электронных писем."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to_mode
msgid ""
"Thread: replies go to target document. Email: replies are routed to a given "
"email."
msgstr ""
"Thread: ответы направляются в целевой документ. Электронная почта: ответы "
"направляются на заданный адрес электронной почты."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "TikTok"
msgstr "TikTok"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__title_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Title"
msgstr "Заголовок"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Чтобы добавить четвертый столбец, уменьшите размер этих трех столбцов с "
"помощью правой иконки каждого блока. Затем продублируйте один из столбцов, "
"чтобы создать новый в виде копии."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the campaign should not have been completed."
msgstr ""
"Чтобы отправить победителю рассылку, кампания не должна была быть завершена."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the same campaign should be used by the mailings"
msgstr ""
"Для рассылки победителям следует использовать ту же кампанию, что и для "
"рассылки победителям"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To track how many replies this mailing gets, make sure its reply-to address "
"belongs to this database."
msgstr ""
"Чтобы отследить, сколько ответов получает эта рассылка, убедитесь, что ее "
"ответный адрес принадлежит этой базе данных."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Tony Fred, CEO"
msgstr "Тони Фред, генеральный директор"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__total
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Total"
msgstr "Всего"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "Total <br/>Contacts"
msgstr "Всего <br/>Контакты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Total Bounces"
msgstr "Всего отказов"

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_trace_check_res_id_is_set
msgid "Traces have to be linked to records with a not null res_id."
msgstr "Трассы должны быть связаны с записями, имеющими не нулевой res_id."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Tracking"
msgstr "Отслеживание"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Try different variations in the campaign to compare their"
msgstr "Попробуйте разные варианты кампании, чтобы сравнить их"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Превратите каждую особенность в преимущество для вашего читателя."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Twitter"
msgstr "Twitter"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_type
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__mailing_type
msgid "Type"
msgstr "Тип"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип Дела для исключения в записи."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__campaign_id
msgid "UTM Campaign"
msgstr "Кампания UTM"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_medium
msgid "UTM Medium"
msgstr "UTM Medium"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__medium_id
msgid "UTM Medium: delivery method (email, sms, ...)"
msgstr "UTM Medium: способ доставки (электронная почта, смс, ...)"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_source
msgid "UTM Source"
msgstr "Источник UTM"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Underline"
msgstr "Подчеркивание"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__unknown
msgid "Unknown error"
msgstr "Неизвестная ошибка"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_basic_template
#, python-format
msgid "Unsubscribe"
msgstr "Отписаться"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out_datetime
msgid "Unsubscription Date"
msgstr "Дата отписки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Upload a file"
msgstr "Загрузить файл"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                    of our fully integrated apps."
msgstr ""
"Улучшения удобства использования, сделанные в Odoo, автоматически распространяются на все\n"
"                    все наши полностью интегрированные приложения."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Use a specific mail server in priority. Otherwise Odoo relies on the first "
"outgoing mail server available (based on their sequencing) as it does for "
"normal mails."
msgstr ""
"Используйте определенный почтовый сервер в приоритете. В противном случае "
"Odoo использует первый доступный сервер исходящей почты (на основе их "
"последовательности), как и для обычных писем."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Use alternative versions to be able to select the winner."
msgstr ""
"Используйте альтернативные версии, чтобы иметь возможность выбрать "
"победителя."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Use now"
msgstr "Используйте сейчас"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Используйте этот компонент для создания списка элементов, на которые вы "
"хотите обратить внимание."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Используйте этот сниппет для создания различных типов компонентов, в которых"
" изображение выравнивается по левому или правому краю и сопровождается "
"текстовым содержимым. Дублируйте элемент, чтобы создать список, "
"соответствующий вашим потребностям."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Useful options"
msgstr "Полезные опции"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_users
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "Пользователь"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Valid Email Recipients"
msgstr "Действительные получатели электронной почты"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vert. Alignment"
msgstr "Верт. Выравнивание"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vertical Alignment"
msgstr "Вертикальное выравнивание"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_view
msgid "View Online"
msgstr "Просмотреть онлайн"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"Wait until your mailing has been sent to check how many recipients you "
"managed to reach."
msgstr ""
"Дождитесь окончания рассылки, чтобы проверить, сколько адресатов вам удалось"
" охватить."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Want to import country, company name and more?"
msgstr "Хотите импортировать страну, название компании и многое другое?"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning Message"
msgstr "Предупреждающее сообщение"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning message displayed in the mailing form view"
msgstr ""
"Предупреждающее сообщение, отображаемое в представлении формы рассылки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available. To welcome you"
" back please accept this 20% discount on you next purchase by clicking the "
"button."
msgstr ""
"Мы продолжаем расти, и мы не упускаем момента что бы вы могли быть частью "
"этого! Мы увеличили часы работы магазина и имеем много новых доступных "
"брендов. Для того, чтобы приветствовать вас обратно, пожалуйста, примите эту"
" скидку 20% для вашей следующую покупке, нажав на кнопку."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "We are in good company."
msgstr "Мы в хорошей компании."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Welcome Message"
msgstr "Приветственное сообщение"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_schedule_date_action
msgid "When do you want to send your mailing?"
msgstr "Когда вы хотите отправить письмо?"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__favorite_date
msgid "When this mailing was added in the favorites"
msgstr "Когда эта рассылка была добавлена в избранное"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_ir_model__is_mailing_enabled
msgid ""
"Whether this model supports marketing mailing capabilities (notably email "
"and SMS)."
msgstr ""
"Поддерживает ли эта модель возможности маркетинговой рассылки (в частности, "
"по электронной почте и SMS)."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_filter_action
msgid ""
"While designing the mailing, you can define the rules to filter recipients.\n"
"                To save the same criteria for future use, you can add it to the favorite list\n"
"                by clicking on <i class=\"fa fa-floppy-o text-warning\"></i> icon next to \"Recipients\"."
msgstr ""
"При разработке рассылки вы можете задать правила фильтрации получателей.\n"
"                Чтобы сохранить те же критерии для использования в будущем, можно добавить их в список избранных\n"
"                нажав на значок <i class=\"fa fa-floppy-o text-warning\"></i> рядом с пунктом \"Получатели\"."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Width"
msgstr "Ширина"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr "Выбор победителя"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                    It provides top notch usability that scales across all apps."
msgstr ""
"Благодаря прочной технической базе фреймворк Odoo уникален.\n"
"                    Он обеспечивает первоклассное удобство использования, масштабируемое для всех приложений."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"With strong technical foundations, Odoo's framework is unique. It provides "
"<span style=\"font-weight: bolder;\">top notch usability that scales across "
"all apps</span>."
msgstr ""
"Благодаря прочной технической базе фреймворк Odoo уникален. Он обеспечивает "
"<span style=\"font-weight: bolder;\">первоклассное удобство использования, "
"масштабируемое для всех приложений</span>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Напишите один или два абзаца, описывающих ваш продукт, услуги или конкретную"
" особенность.<br/> Чтобы добиться успеха, ваш контент должен быть полезным "
"для читателей."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid ""
"Write or paste email addresses in the field below.\n"
"                    Each line will be imported as a mailing list contact."
msgstr ""
"Введите или вставьте адреса электронной почты в поле ниже.\n"
"                    Каждая строка будет импортирована как контакт из списка рассылки."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Write what the customer would like to know, not what you want to show."
msgstr "Напишите, что хотел бы знать клиент, а не то, что вы хотите показать."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "You are no longer part of our mailing list(s)."
msgstr "Вы больше не являетесь частью нашего списка рассылки."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid ""
"You are no longer part of our services and will not be contacted again."
msgstr ""
"Вы больше не пользуетесь нашими услугами, и мы больше с вами не свяжемся."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "You are no longer part of the %(mailing_name)s mailing list."
msgstr "Вы больше не являетесь частью списка рассылки %(mailing_name)s."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "You are no longer part of the %(mailing_names)s mailing list."
msgstr "Вы больше не являетесь частью списка рассылки %(mailing_names)s."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "You are not subscribed to any of our mailing list."
msgstr "Вы не подписаны ни на одну из наших рассылок."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Вы можете изменять цвета и фоны, чтобы подчеркнуть особенности."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_list_merge.py:0
#, python-format
msgid "You can only apply this action from Mailing Lists."
msgstr "Вы можете применить это действие только из списков рассылки."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/utm_medium.py:0
#, python-format
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following mailings in Mass Mailing:\n"
"%(mailing_names)s"
msgstr ""
"Вы не можете удалить эти UTM-средства, поскольку они связаны со следующими рассылками в Массовой рассылке:\n"
"%(mailing_names)s"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following mailings in Mass Mailing:\n"
"%(mailing_names)s"
msgstr ""
"Вы не можете удалить эти источники UTM, поскольку они связаны со следующими рассылками в массовой рассылке:\n"
"%(mailing_names)s"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"You don't need to import your mailing lists, you can easily\n"
"                send emails<br> to any contact saved in other Odoo apps."
msgstr ""
"Вам не нужно импортировать свои списки рассылки, вы можете легко\n"
"                отправлять письма<br> любому контакту, сохраненному в других приложениях Odoo."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#, python-format
msgid "You have to much emails, please upload a file."
msgstr "У вас много электронных писем, пожалуйста, загрузите файл."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "You may also be interested in"
msgstr "Вам также может быть интересно:"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_contact.py:0
#, python-format
msgid ""
"You should give either list_ids, either subscription_ids to create new "
"contacts."
msgstr ""
"You should give either list_ids, either subscription_ids to create new "
"contacts."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid "You will not hear from us anymore."
msgstr "Вы больше о нас не услышите."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#, python-format
msgid ""
"You will not receive any news from those mailing lists you are a member of:"
msgstr ""
"Вы не будете получать никаких новостей из тех списков рассылки, участником "
"которых вы являетесь:"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_title
msgid "Your Title"
msgstr "Ваш заголовок"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "Your email is currently"
msgstr "Ваш текущий email"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Your email is currently <strong>in our block list</strong>."
msgstr ""
"Ваш адрес электронной почты в настоящее время <strong> находится в нашем "
"списке заблокированных</strong>."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#, python-format
msgid "e.g. \"VIP Customers\""
msgstr "например, \"VIP-клиенты\""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. Check it out before it's too late!"
msgstr "например, Проверьте это, пока не стало слишком поздно!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "e.g. Consumer Newsletter"
msgstr "например новостная рассылка для клиентов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "e.g. John Smith"
msgstr "пример: Иван Иванов"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. New Sale on all T-shirts"
msgstr "например, Новая распродажа на все футболки"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"<EMAIL>\n"
"<EMAIL>"
msgstr ""
"<EMAIL>\n"
"<EMAIL>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "having the"
msgstr "имеющий"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#, python-format
msgid "in our block list"
msgstr "в нашем блок-листе"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"is the winner of the A/B testing campaign and has been sent to all remaining"
" recipients."
msgstr ""
"является победителем кампании A/B тестирования и был отправлен всем "
"оставшимся получателям."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"of all potential recipients.<br/>\n"
"                        <b class=\"text-danger\">Some of the mailings will not be sent</b>, as only 1 email will be sent for each unique recipient in this campaign."
msgstr ""
"всех потенциальных получателей.<br/>\n"
"                        <b class=\"text-danger\">Некоторые рассылки не будут отправлены</b>, так как для каждого уникального получателя в этой кампании будет отправлено только 1 электронное письмо."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "on"
msgstr "вкл"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_schedule_date
msgid "schedule a mailing"
msgstr "запланируйте рассылку"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#, python-format
msgid "template"
msgstr "шаблон"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "the"
msgstr "в"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "to the remaining recipients."
msgstr "остальным получателям."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent"
msgstr "будет отправлено"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this version."
msgstr "получит эту версию."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this version.<br/>"
msgstr "получит эту версию.<br/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Active"
msgstr "⌙ Активный"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ Неактивный"
