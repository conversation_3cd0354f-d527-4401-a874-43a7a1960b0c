<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="symtable — Access to the compiler’s symbol tables" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/symtable.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/symtable.py Symbol tables are generated by the compiler from AST just before bytecode is generated. The symbol table is responsible for calculating the scope of every identifier in..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/symtable.py Symbol tables are generated by the compiler from AST just before bytecode is generated. The symbol table is responsible for calculating the scope of every identifier in..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>symtable — Access to the compiler’s symbol tables &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="token — Constants used with Python parse trees" href="token.html" />
    <link rel="prev" title="ast — Abstract Syntax Trees" href="ast.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/symtable.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">symtable</span></code> — Access to the compiler’s symbol tables</a><ul>
<li><a class="reference internal" href="#generating-symbol-tables">Generating Symbol Tables</a></li>
<li><a class="reference internal" href="#examining-symbol-tables">Examining Symbol Tables</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ast.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ast</span></code> — Abstract Syntax Trees</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="token.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">token</span></code> — Constants used with Python parse trees</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/symtable.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="token.html" title="token — Constants used with Python parse trees"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="ast.html" title="ast — Abstract Syntax Trees"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" accesskey="U">Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">symtable</span></code> — Access to the compiler’s symbol tables</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-symtable">
<span id="symtable-access-to-the-compiler-s-symbol-tables"></span><h1><a class="reference internal" href="#module-symtable" title="symtable: Interface to the compiler's internal symbol tables."><code class="xref py py-mod docutils literal notranslate"><span class="pre">symtable</span></code></a> — Access to the compiler’s symbol tables<a class="headerlink" href="#module-symtable" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/symtable.py">Lib/symtable.py</a></p>
<hr class="docutils" />
<p>Symbol tables are generated by the compiler from AST just before bytecode is
generated.  The symbol table is responsible for calculating the scope of every
identifier in the code.  <a class="reference internal" href="#module-symtable" title="symtable: Interface to the compiler's internal symbol tables."><code class="xref py py-mod docutils literal notranslate"><span class="pre">symtable</span></code></a> provides an interface to examine these
tables.</p>
<section id="generating-symbol-tables">
<h2>Generating Symbol Tables<a class="headerlink" href="#generating-symbol-tables" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="symtable.symtable">
<span class="sig-prename descclassname"><span class="pre">symtable.</span></span><span class="sig-name descname"><span class="pre">symtable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compile_type</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#symtable.symtable" title="Link to this definition">¶</a></dt>
<dd><p>Return the toplevel <a class="reference internal" href="#symtable.SymbolTable" title="symtable.SymbolTable"><code class="xref py py-class docutils literal notranslate"><span class="pre">SymbolTable</span></code></a> for the Python source <em>code</em>.
<em>filename</em> is the name of the file containing the code.  <em>compile_type</em> is
like the <em>mode</em> argument to <a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>.</p>
</dd></dl>

</section>
<section id="examining-symbol-tables">
<h2>Examining Symbol Tables<a class="headerlink" href="#examining-symbol-tables" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="symtable.SymbolTable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">symtable.</span></span><span class="sig-name descname"><span class="pre">SymbolTable</span></span><a class="headerlink" href="#symtable.SymbolTable" title="Link to this definition">¶</a></dt>
<dd><p>A namespace table for a block.  The constructor is not public.</p>
<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_type">
<span class="sig-name descname"><span class="pre">get_type</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_type" title="Link to this definition">¶</a></dt>
<dd><p>Return the type of the symbol table.  Possible values are <code class="docutils literal notranslate"><span class="pre">'class'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'module'</span></code>, <code class="docutils literal notranslate"><span class="pre">'function'</span></code>, <code class="docutils literal notranslate"><span class="pre">'annotation'</span></code>, <code class="docutils literal notranslate"><span class="pre">'TypeVar</span> <span class="pre">bound'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'type</span> <span class="pre">alias'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'type</span> <span class="pre">parameter'</span></code>. The latter four refer to
different flavors of <a class="reference internal" href="../reference/executionmodel.html#annotation-scopes"><span class="std std-ref">annotation scopes</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added <code class="docutils literal notranslate"><span class="pre">'annotation'</span></code>,  <code class="docutils literal notranslate"><span class="pre">'TypeVar</span> <span class="pre">bound'</span></code>, <code class="docutils literal notranslate"><span class="pre">'type</span> <span class="pre">alias'</span></code>,
and <code class="docutils literal notranslate"><span class="pre">'type</span> <span class="pre">parameter'</span></code> as possible return values.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_id">
<span class="sig-name descname"><span class="pre">get_id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_id" title="Link to this definition">¶</a></dt>
<dd><p>Return the table’s identifier.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_name">
<span class="sig-name descname"><span class="pre">get_name</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_name" title="Link to this definition">¶</a></dt>
<dd><p>Return the table’s name.  This is the name of the class if the table is
for a class, the name of the function if the table is for a function, or
<code class="docutils literal notranslate"><span class="pre">'top'</span></code> if the table is global (<a class="reference internal" href="#symtable.SymbolTable.get_type" title="symtable.SymbolTable.get_type"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_type()</span></code></a> returns <code class="docutils literal notranslate"><span class="pre">'module'</span></code>).
For type parameter scopes (which are used for generic classes, functions,
and type aliases), it is the name of the underlying class, function, or
type alias. For type alias scopes, it is the name of the type alias.
For <a class="reference internal" href="typing.html#typing.TypeVar" title="typing.TypeVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a> bound scopes, it is the name of the <code class="docutils literal notranslate"><span class="pre">TypeVar</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_lineno">
<span class="sig-name descname"><span class="pre">get_lineno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_lineno" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of the first line in the block this table represents.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.is_optimized">
<span class="sig-name descname"><span class="pre">is_optimized</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.is_optimized" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the locals in this table can be optimized.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.is_nested">
<span class="sig-name descname"><span class="pre">is_nested</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.is_nested" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the block is a nested class or function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.has_children">
<span class="sig-name descname"><span class="pre">has_children</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.has_children" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the block has nested namespaces within it.  These can
be obtained with <a class="reference internal" href="#symtable.SymbolTable.get_children" title="symtable.SymbolTable.get_children"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_children()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_identifiers">
<span class="sig-name descname"><span class="pre">get_identifiers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_identifiers" title="Link to this definition">¶</a></dt>
<dd><p>Return a view object containing the names of symbols in the table.
See the <a class="reference internal" href="stdtypes.html#dict-views"><span class="std std-ref">documentation of view objects</span></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.lookup">
<span class="sig-name descname"><span class="pre">lookup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.lookup" title="Link to this definition">¶</a></dt>
<dd><p>Lookup <em>name</em> in the table and return a <a class="reference internal" href="#symtable.Symbol" title="symtable.Symbol"><code class="xref py py-class docutils literal notranslate"><span class="pre">Symbol</span></code></a> instance.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_symbols">
<span class="sig-name descname"><span class="pre">get_symbols</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_symbols" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of <a class="reference internal" href="#symtable.Symbol" title="symtable.Symbol"><code class="xref py py-class docutils literal notranslate"><span class="pre">Symbol</span></code></a> instances for names in the table.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.SymbolTable.get_children">
<span class="sig-name descname"><span class="pre">get_children</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.SymbolTable.get_children" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of the nested symbol tables.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="symtable.Function">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">symtable.</span></span><span class="sig-name descname"><span class="pre">Function</span></span><a class="headerlink" href="#symtable.Function" title="Link to this definition">¶</a></dt>
<dd><p>A namespace for a function or method.  This class inherits from
<a class="reference internal" href="#symtable.SymbolTable" title="symtable.SymbolTable"><code class="xref py py-class docutils literal notranslate"><span class="pre">SymbolTable</span></code></a>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="symtable.Function.get_parameters">
<span class="sig-name descname"><span class="pre">get_parameters</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Function.get_parameters" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing names of parameters to this function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Function.get_locals">
<span class="sig-name descname"><span class="pre">get_locals</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Function.get_locals" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing names of locals in this function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Function.get_globals">
<span class="sig-name descname"><span class="pre">get_globals</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Function.get_globals" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing names of globals in this function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Function.get_nonlocals">
<span class="sig-name descname"><span class="pre">get_nonlocals</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Function.get_nonlocals" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing names of nonlocals in this function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Function.get_frees">
<span class="sig-name descname"><span class="pre">get_frees</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Function.get_frees" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing names of free variables in this function.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="symtable.Class">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">symtable.</span></span><span class="sig-name descname"><span class="pre">Class</span></span><a class="headerlink" href="#symtable.Class" title="Link to this definition">¶</a></dt>
<dd><p>A namespace of a class.  This class inherits from <a class="reference internal" href="#symtable.SymbolTable" title="symtable.SymbolTable"><code class="xref py py-class docutils literal notranslate"><span class="pre">SymbolTable</span></code></a>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="symtable.Class.get_methods">
<span class="sig-name descname"><span class="pre">get_methods</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Class.get_methods" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing the names of methods declared in the class.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="symtable.Symbol">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">symtable.</span></span><span class="sig-name descname"><span class="pre">Symbol</span></span><a class="headerlink" href="#symtable.Symbol" title="Link to this definition">¶</a></dt>
<dd><p>An entry in a <a class="reference internal" href="#symtable.SymbolTable" title="symtable.SymbolTable"><code class="xref py py-class docutils literal notranslate"><span class="pre">SymbolTable</span></code></a> corresponding to an identifier in the
source.  The constructor is not public.</p>
<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.get_name">
<span class="sig-name descname"><span class="pre">get_name</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.get_name" title="Link to this definition">¶</a></dt>
<dd><p>Return the symbol’s name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_referenced">
<span class="sig-name descname"><span class="pre">is_referenced</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_referenced" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is used in its block.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_imported">
<span class="sig-name descname"><span class="pre">is_imported</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_imported" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is created from an import statement.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_parameter">
<span class="sig-name descname"><span class="pre">is_parameter</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_parameter" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is a parameter.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_global">
<span class="sig-name descname"><span class="pre">is_global</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_global" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is global.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_nonlocal">
<span class="sig-name descname"><span class="pre">is_nonlocal</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_nonlocal" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is nonlocal.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_declared_global">
<span class="sig-name descname"><span class="pre">is_declared_global</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_declared_global" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is declared global with a global statement.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_local">
<span class="sig-name descname"><span class="pre">is_local</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_local" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is local to its block.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_annotated">
<span class="sig-name descname"><span class="pre">is_annotated</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_annotated" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is annotated.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_free">
<span class="sig-name descname"><span class="pre">is_free</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_free" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is referenced in its block, but not assigned
to.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_assigned">
<span class="sig-name descname"><span class="pre">is_assigned</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_assigned" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the symbol is assigned to in its block.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.is_namespace">
<span class="sig-name descname"><span class="pre">is_namespace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.is_namespace" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if name binding introduces new namespace.</p>
<p>If the name is used as the target of a function or class statement, this
will be true.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">table</span> <span class="o">=</span> <span class="n">symtable</span><span class="o">.</span><span class="n">symtable</span><span class="p">(</span><span class="s2">&quot;def some_func(): pass&quot;</span><span class="p">,</span> <span class="s2">&quot;string&quot;</span><span class="p">,</span> <span class="s2">&quot;exec&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">table</span><span class="o">.</span><span class="n">lookup</span><span class="p">(</span><span class="s2">&quot;some_func&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">is_namespace</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Note that a single name can be bound to multiple objects.  If the result
is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the name may also be bound to other objects, like an int or
list, that does not introduce a new namespace.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.get_namespaces">
<span class="sig-name descname"><span class="pre">get_namespaces</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.get_namespaces" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of namespaces bound to this name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symtable.Symbol.get_namespace">
<span class="sig-name descname"><span class="pre">get_namespace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#symtable.Symbol.get_namespace" title="Link to this definition">¶</a></dt>
<dd><p>Return the namespace bound to this name. If more than one or no namespace
is bound to this name, a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
</dd></dl>

</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">symtable</span></code> — Access to the compiler’s symbol tables</a><ul>
<li><a class="reference internal" href="#generating-symbol-tables">Generating Symbol Tables</a></li>
<li><a class="reference internal" href="#examining-symbol-tables">Examining Symbol Tables</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ast.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ast</span></code> — Abstract Syntax Trees</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="token.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">token</span></code> — Constants used with Python parse trees</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/symtable.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="token.html" title="token — Constants used with Python parse trees"
             >next</a> |</li>
        <li class="right" >
          <a href="ast.html" title="ast — Abstract Syntax Trees"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" >Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">symtable</span></code> — Access to the compiler’s symbol tables</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>