<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.LandingPage">
        <div t-if="languages.length > 1" t-on-click="openLanguages" class="self_order_language_selector position-absolute top-0 end-0 m-4 rounded p-4 bg-white shadow-lg">
            <img class="rounded" t-attf-src="{{currentLanguage.flag_image_url}}" />
            <span t-esc="currentLanguage.display_name" class="ms-3"></span>
        </div>
        <div t-if="selfOrder.config.self_ordering_image_home_ids.length > 0" t-on-click="start" class="d-flex flex-column vh-100 align-items-center overflow-hidden">
            <div id="carouselAutoplaying" t-ref="carousel" class="carousel slide w-100 h-100" data-bs-ride="true">
                <div class="carousel-inner h-100 w-100">
                    <div
                        t-foreach="selfOrder.config.self_ordering_image_home_ids"
                        t-as="image"
                        t-key="image.id"
                        t-attf-class="carousel-item object-fit-cover h-100 w-100 {{activeImage}}"
                        t-attf-style="background-image: url('data:image/png;base64,{{image.data}}'); background-size: cover; background-position: center;" />
                </div>
            </div>
        </div>
        <div class="o_pos_landing_footer position-absolute bottom-0 end-0 d-flex w-100 gap-3 p-3">
            <div class="d-flex gap-3" t-att-class="{'flex-grow-1 justify-content-around': !selfOrder.custom_links.length}">
                <t t-if="showMyOrderBtn()">
                    <a
                        type="button"
                        t-on-click="clickMyOrder"
                        class="btn btn-lg btn-primary">
                        <t t-if="draftOrder.length > 0">
                            My Order
                        </t>
                        <t t-else="">
                            My Orders
                        </t>
                    </a>
                </t>
            </div>
            <div t-if="selfOrder.custom_links.length" class="d-flex gap-3 flex-grow-1">
                <t t-foreach="selfOrder.custom_links" t-as="link" t-key="link.id">
                    <a type="button"
                        t-if="!hideBtn(link)"
                        t-on-click="(event) => this.clickCustomLink(link)"
                        t-attf-class="btn btn-lg btn-{{link.style}}">
                        <t t-esc="link.name"/>
                    </a>
                </t>
            </div>
        </div>
    </t>
</templates>
