<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.PartnerLine">
        <tr t-attf-class="partner-line gap-2 gap-lg-0 align-top {{highlight}}" t-att-data-id="props.partner.id"
            t-on-click="() => this.props.onClickPartner(props.partner)">
            <td>
                <b>
                    <t t-esc="props.partner.name or ''" />
                </b>
                <div class="company-field text-bg-muted">
                    <t t-esc="props.partner.parent_name or ''" />
                </div>
                <button t-if="_isPartnerSelected" class="unselect-tag d-lg-inline-block d-none btn btn-light mt-2">
                    <i class="fa fa-times me-1"></i>
                    <span> Unselect </span>
                </button>
            </td>
            <td>
                <div class="partner-line-adress" t-if="props.partner.address">
                    <t t-esc="props.partner.address" />
                </div>
            </td>
            <td class="partner-line-email ">
                <div class="mb-2" t-if="props.partner.phone">
                    <i class="fa fa-fw fa-phone me-2"/><t t-esc="props.partner.phone"/>
                </div>
                <div class="mb-2" t-if="props.partner.mobile">
                    <i class="fa fa-fw fa-mobile me-2"/><t t-esc="props.partner.mobile"/>
                </div>
                <div t-if="props.partner.email" class="email-field mb-2">
                    <i class="fa fa-fw fa-paper-plane-o me-2"/><t t-esc="props.partner.email" />
                </div>
            </td>
            <td class="partner-line-balance" t-if="props.isBalanceDisplayed"></td>
            <td class="edit-partner-button-cell">
                <button class="edit-partner-button btn btn-light border" t-on-click.stop="() => props.onClickEdit(props.partner)">DETAILS</button>
                <button t-if="_isPartnerSelected" class="unselect-tag-mobile d-inline-block d-lg-none btn btn-light border ms-2">
                    <i class="fa fa-times"></i>
                    <span> UNSELECT </span>
                </button>
            </td>
            <td class="partner-line-last-column-placeholder oe_invisible"></td>
        </tr>
    </t>

</templates>
