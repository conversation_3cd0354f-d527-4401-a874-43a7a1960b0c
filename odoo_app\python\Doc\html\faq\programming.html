<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Programming FAQ" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/faq/programming.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Contents: Programming FAQ- General Questions- Is there a source code level debugger with breakpoints, single-stepping, etc.?, Are there tools to help find bugs or perform static analysis?, How can ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Contents: Programming FAQ- General Questions- Is there a source code level debugger with breakpoints, single-stepping, etc.?, Are there tools to help find bugs or perform static analysis?, How can ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Programming FAQ &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Design and History FAQ" href="design.html" />
    <link rel="prev" title="General Python FAQ" href="general.html" />
    <link rel="canonical" href="https://docs.python.org/3/faq/programming.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Programming FAQ</a><ul>
<li><a class="reference internal" href="#general-questions">General Questions</a></li>
<li><a class="reference internal" href="#core-language">Core Language</a></li>
<li><a class="reference internal" href="#numbers-and-strings">Numbers and strings</a></li>
<li><a class="reference internal" href="#performance">Performance</a></li>
<li><a class="reference internal" href="#sequences-tuples-lists">Sequences (Tuples/Lists)</a></li>
<li><a class="reference internal" href="#objects">Objects</a></li>
<li><a class="reference internal" href="#modules">Modules</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="general.html"
                          title="previous chapter">General Python FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="design.html"
                          title="next chapter">Design and History FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/programming.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="design.html" title="Design and History FAQ"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="general.html" title="General Python FAQ"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Programming FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="programming-faq">
<h1><a class="toc-backref" href="#id2" role="doc-backlink">Programming FAQ</a><a class="headerlink" href="#programming-faq" title="Link to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#programming-faq" id="id2">Programming FAQ</a></p>
<ul>
<li><p><a class="reference internal" href="#general-questions" id="id3">General Questions</a></p>
<ul>
<li><p><a class="reference internal" href="#is-there-a-source-code-level-debugger-with-breakpoints-single-stepping-etc" id="id4">Is there a source code level debugger with breakpoints, single-stepping, etc.?</a></p></li>
<li><p><a class="reference internal" href="#are-there-tools-to-help-find-bugs-or-perform-static-analysis" id="id5">Are there tools to help find bugs or perform static analysis?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-create-a-stand-alone-binary-from-a-python-script" id="id6">How can I create a stand-alone binary from a Python script?</a></p></li>
<li><p><a class="reference internal" href="#are-there-coding-standards-or-a-style-guide-for-python-programs" id="id7">Are there coding standards or a style guide for Python programs?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#core-language" id="id8">Core Language</a></p>
<ul>
<li><p><a class="reference internal" href="#why-am-i-getting-an-unboundlocalerror-when-the-variable-has-a-value" id="id9">Why am I getting an UnboundLocalError when the variable has a value?</a></p></li>
<li><p><a class="reference internal" href="#what-are-the-rules-for-local-and-global-variables-in-python" id="id10">What are the rules for local and global variables in Python?</a></p></li>
<li><p><a class="reference internal" href="#why-do-lambdas-defined-in-a-loop-with-different-values-all-return-the-same-result" id="id11">Why do lambdas defined in a loop with different values all return the same result?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-share-global-variables-across-modules" id="id12">How do I share global variables across modules?</a></p></li>
<li><p><a class="reference internal" href="#what-are-the-best-practices-for-using-import-in-a-module" id="id13">What are the “best practices” for using import in a module?</a></p></li>
<li><p><a class="reference internal" href="#why-are-default-values-shared-between-objects" id="id14">Why are default values shared between objects?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-pass-optional-or-keyword-parameters-from-one-function-to-another" id="id15">How can I pass optional or keyword parameters from one function to another?</a></p></li>
<li><p><a class="reference internal" href="#what-is-the-difference-between-arguments-and-parameters" id="id16">What is the difference between arguments and parameters?</a></p></li>
<li><p><a class="reference internal" href="#why-did-changing-list-y-also-change-list-x" id="id17">Why did changing list ‘y’ also change list ‘x’?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-write-a-function-with-output-parameters-call-by-reference" id="id18">How do I write a function with output parameters (call by reference)?</a></p></li>
<li><p><a class="reference internal" href="#how-do-you-make-a-higher-order-function-in-python" id="id19">How do you make a higher order function in Python?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-copy-an-object-in-python" id="id20">How do I copy an object in Python?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-find-the-methods-or-attributes-of-an-object" id="id21">How can I find the methods or attributes of an object?</a></p></li>
<li><p><a class="reference internal" href="#how-can-my-code-discover-the-name-of-an-object" id="id22">How can my code discover the name of an object?</a></p></li>
<li><p><a class="reference internal" href="#what-s-up-with-the-comma-operator-s-precedence" id="id23">What’s up with the comma operator’s precedence?</a></p></li>
<li><p><a class="reference internal" href="#is-there-an-equivalent-of-c-s-ternary-operator" id="id24">Is there an equivalent of C’s “?:” ternary operator?</a></p></li>
<li><p><a class="reference internal" href="#is-it-possible-to-write-obfuscated-one-liners-in-python" id="id25">Is it possible to write obfuscated one-liners in Python?</a></p></li>
<li><p><a class="reference internal" href="#what-does-the-slash-in-the-parameter-list-of-a-function-mean" id="id26">What does the slash(/) in the parameter list of a function mean?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#numbers-and-strings" id="id27">Numbers and strings</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-specify-hexadecimal-and-octal-integers" id="id28">How do I specify hexadecimal and octal integers?</a></p></li>
<li><p><a class="reference internal" href="#why-does-22-10-return-3" id="id29">Why does -22 // 10 return -3?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-get-int-literal-attribute-instead-of-syntaxerror" id="id30">How do I get int literal attribute instead of SyntaxError?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-convert-a-string-to-a-number" id="id31">How do I convert a string to a number?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-convert-a-number-to-a-string" id="id32">How do I convert a number to a string?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-modify-a-string-in-place" id="id33">How do I modify a string in place?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-use-strings-to-call-functions-methods" id="id34">How do I use strings to call functions/methods?</a></p></li>
<li><p><a class="reference internal" href="#is-there-an-equivalent-to-perl-s-chomp-for-removing-trailing-newlines-from-strings" id="id35">Is there an equivalent to Perl’s chomp() for removing trailing newlines from strings?</a></p></li>
<li><p><a class="reference internal" href="#is-there-a-scanf-or-sscanf-equivalent" id="id36">Is there a scanf() or sscanf() equivalent?</a></p></li>
<li><p><a class="reference internal" href="#what-does-unicodedecodeerror-or-unicodeencodeerror-error-mean" id="id37">What does ‘UnicodeDecodeError’ or ‘UnicodeEncodeError’ error  mean?</a></p></li>
<li><p><a class="reference internal" href="#can-i-end-a-raw-string-with-an-odd-number-of-backslashes" id="id38">Can I end a raw string with an odd number of backslashes?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#performance" id="id39">Performance</a></p>
<ul>
<li><p><a class="reference internal" href="#my-program-is-too-slow-how-do-i-speed-it-up" id="id40">My program is too slow. How do I speed it up?</a></p></li>
<li><p><a class="reference internal" href="#what-is-the-most-efficient-way-to-concatenate-many-strings-together" id="id41">What is the most efficient way to concatenate many strings together?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#sequences-tuples-lists" id="id42">Sequences (Tuples/Lists)</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-convert-between-tuples-and-lists" id="id43">How do I convert between tuples and lists?</a></p></li>
<li><p><a class="reference internal" href="#what-s-a-negative-index" id="id44">What’s a negative index?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-iterate-over-a-sequence-in-reverse-order" id="id45">How do I iterate over a sequence in reverse order?</a></p></li>
<li><p><a class="reference internal" href="#how-do-you-remove-duplicates-from-a-list" id="id46">How do you remove duplicates from a list?</a></p></li>
<li><p><a class="reference internal" href="#how-do-you-remove-multiple-items-from-a-list" id="id47">How do you remove multiple items from a list</a></p></li>
<li><p><a class="reference internal" href="#how-do-you-make-an-array-in-python" id="id48">How do you make an array in Python?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-create-a-multidimensional-list" id="id49">How do I create a multidimensional list?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-apply-a-method-or-function-to-a-sequence-of-objects" id="id50">How do I apply a method or function to a sequence of objects?</a></p></li>
<li><p><a class="reference internal" href="#why-does-a-tuple-i-item-raise-an-exception-when-the-addition-works" id="id51">Why does a_tuple[i] += [‘item’] raise an exception when the addition works?</a></p></li>
<li><p><a class="reference internal" href="#i-want-to-do-a-complicated-sort-can-you-do-a-schwartzian-transform-in-python" id="id52">I want to do a complicated sort: can you do a Schwartzian Transform in Python?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-sort-one-list-by-values-from-another-list" id="id53">How can I sort one list by values from another list?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#objects" id="id54">Objects</a></p>
<ul>
<li><p><a class="reference internal" href="#what-is-a-class" id="id55">What is a class?</a></p></li>
<li><p><a class="reference internal" href="#what-is-a-method" id="id56">What is a method?</a></p></li>
<li><p><a class="reference internal" href="#what-is-self" id="id57">What is self?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-check-if-an-object-is-an-instance-of-a-given-class-or-of-a-subclass-of-it" id="id58">How do I check if an object is an instance of a given class or of a subclass of it?</a></p></li>
<li><p><a class="reference internal" href="#what-is-delegation" id="id59">What is delegation?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-call-a-method-defined-in-a-base-class-from-a-derived-class-that-extends-it" id="id60">How do I call a method defined in a base class from a derived class that extends it?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-organize-my-code-to-make-it-easier-to-change-the-base-class" id="id61">How can I organize my code to make it easier to change the base class?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-create-static-class-data-and-static-class-methods" id="id62">How do I create static class data and static class methods?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-overload-constructors-or-methods-in-python" id="id63">How can I overload constructors (or methods) in Python?</a></p></li>
<li><p><a class="reference internal" href="#i-try-to-use-spam-and-i-get-an-error-about-someclassname-spam" id="id64">I try to use __spam and I get an error about _SomeClassName__spam.</a></p></li>
<li><p><a class="reference internal" href="#my-class-defines-del-but-it-is-not-called-when-i-delete-the-object" id="id65">My class defines __del__ but it is not called when I delete the object.</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-get-a-list-of-all-instances-of-a-given-class" id="id66">How do I get a list of all instances of a given class?</a></p></li>
<li><p><a class="reference internal" href="#why-does-the-result-of-id-appear-to-be-not-unique" id="id67">Why does the result of <code class="docutils literal notranslate"><span class="pre">id()</span></code> appear to be not unique?</a></p></li>
<li><p><a class="reference internal" href="#when-can-i-rely-on-identity-tests-with-the-is-operator" id="id68">When can I rely on identity tests with the <em>is</em> operator?</a></p></li>
<li><p><a class="reference internal" href="#how-can-a-subclass-control-what-data-is-stored-in-an-immutable-instance" id="id69">How can a subclass control what data is stored in an immutable instance?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-cache-method-calls" id="id70">How do I cache method calls?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#modules" id="id71">Modules</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-create-a-pyc-file" id="id72">How do I create a .pyc file?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-find-the-current-module-name" id="id73">How do I find the current module name?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-have-modules-that-mutually-import-each-other" id="id74">How can I have modules that mutually import each other?</a></p></li>
<li><p><a class="reference internal" href="#import-x-y-z-returns-module-x-how-do-i-get-z" id="id75">__import__(‘x.y.z’) returns &lt;module ‘x’&gt;; how do I get z?</a></p></li>
<li><p><a class="reference internal" href="#when-i-edit-an-imported-module-and-reimport-it-the-changes-don-t-show-up-why-does-this-happen" id="id76">When I edit an imported module and reimport it, the changes don’t show up.  Why does this happen?</a></p></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
<section id="general-questions">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">General Questions</a><a class="headerlink" href="#general-questions" title="Link to this heading">¶</a></h2>
<section id="is-there-a-source-code-level-debugger-with-breakpoints-single-stepping-etc">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Is there a source code level debugger with breakpoints, single-stepping, etc.?</a><a class="headerlink" href="#is-there-a-source-code-level-debugger-with-breakpoints-single-stepping-etc" title="Link to this heading">¶</a></h3>
<p>Yes.</p>
<p>Several debuggers for Python are described below, and the built-in function
<a class="reference internal" href="../library/functions.html#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a> allows you to drop into any of them.</p>
<p>The pdb module is a simple but adequate console-mode debugger for Python. It is
part of the standard Python library, and is <a class="reference internal" href="../library/pdb.html#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">documented</span> <span class="pre">in</span> <span class="pre">the</span> <span class="pre">Library</span>
<span class="pre">Reference</span> <span class="pre">Manual</span></code></a>. You can also write your own debugger by using the code
for pdb as an example.</p>
<p>The IDLE interactive development environment, which is part of the standard
Python distribution (normally available as
<a class="reference external" href="https://github.com/python/cpython/blob/main/Tools/scripts/idle3">Tools/scripts/idle3</a>),
includes a graphical debugger.</p>
<p>PythonWin is a Python IDE that includes a GUI debugger based on pdb.  The
PythonWin debugger colors breakpoints and has quite a few cool features such as
debugging non-PythonWin programs.  PythonWin is available as part of
<a class="reference external" href="https://github.com/mhammond/pywin32">pywin32</a> project and
as a part of the
<a class="reference external" href="https://www.activestate.com/products/python/">ActivePython</a> distribution.</p>
<p><a class="reference external" href="https://eric-ide.python-projects.org/">Eric</a> is an IDE built on PyQt
and the Scintilla editing component.</p>
<p><a class="reference external" href="https://github.com/rocky/python3-trepan/">trepan3k</a> is a gdb-like debugger.</p>
<p><a class="reference external" href="https://code.visualstudio.com/">Visual Studio Code</a> is an IDE with debugging
tools that integrates with version-control software.</p>
<p>There are a number of commercial Python IDEs that include graphical debuggers.
They include:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://wingware.com/">Wing IDE</a></p></li>
<li><p><a class="reference external" href="https://www.activestate.com/products/komodo-ide/">Komodo IDE</a></p></li>
<li><p><a class="reference external" href="https://www.jetbrains.com/pycharm/">PyCharm</a></p></li>
</ul>
</section>
<section id="are-there-tools-to-help-find-bugs-or-perform-static-analysis">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Are there tools to help find bugs or perform static analysis?</a><a class="headerlink" href="#are-there-tools-to-help-find-bugs-or-perform-static-analysis" title="Link to this heading">¶</a></h3>
<p>Yes.</p>
<p><a class="reference external" href="https://pylint.pycqa.org/en/latest/index.html">Pylint</a> and
<a class="reference external" href="https://github.com/PyCQA/pyflakes">Pyflakes</a> do basic checking that will
help you catch bugs sooner.</p>
<p>Static type checkers such as <a class="reference external" href="https://mypy-lang.org/">Mypy</a>,
<a class="reference external" href="https://pyre-check.org/">Pyre</a>, and
<a class="reference external" href="https://github.com/google/pytype">Pytype</a> can check type hints in Python
source code.</p>
</section>
<section id="how-can-i-create-a-stand-alone-binary-from-a-python-script">
<span id="faq-create-standalone-binary"></span><h3><a class="toc-backref" href="#id6" role="doc-backlink">How can I create a stand-alone binary from a Python script?</a><a class="headerlink" href="#how-can-i-create-a-stand-alone-binary-from-a-python-script" title="Link to this heading">¶</a></h3>
<p>You don’t need the ability to compile Python to C code if all you want is a
stand-alone program that users can download and run without having to install
the Python distribution first.  There are a number of tools that determine the
set of modules required by a program and bind these modules together with a
Python binary to produce a single executable.</p>
<p>One is to use the freeze tool, which is included in the Python source tree as
<a class="reference external" href="https://github.com/python/cpython/tree/main/Tools/freeze">Tools/freeze</a>.
It converts Python byte code to C arrays; with a C compiler you can
embed all your modules into a new program, which is then linked with the
standard Python modules.</p>
<p>It works by scanning your source recursively for import statements (in both
forms) and looking for the modules in the standard Python path as well as in the
source directory (for built-in modules).  It then turns the bytecode for modules
written in Python into C code (array initializers that can be turned into code
objects using the marshal module) and creates a custom-made config file that
only contains those built-in modules which are actually used in the program.  It
then compiles the generated C code and links it with the rest of the Python
interpreter to form a self-contained binary which acts exactly like your script.</p>
<p>The following packages can help with the creation of console and GUI
executables:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://nuitka.net/">Nuitka</a> (Cross-platform)</p></li>
<li><p><a class="reference external" href="https://pyinstaller.org/">PyInstaller</a> (Cross-platform)</p></li>
<li><p><a class="reference external" href="https://pyoxidizer.readthedocs.io/en/stable/">PyOxidizer</a> (Cross-platform)</p></li>
<li><p><a class="reference external" href="https://marcelotduarte.github.io/cx_Freeze/">cx_Freeze</a> (Cross-platform)</p></li>
<li><p><a class="reference external" href="https://github.com/ronaldoussoren/py2app">py2app</a> (macOS only)</p></li>
<li><p><a class="reference external" href="https://www.py2exe.org/">py2exe</a> (Windows only)</p></li>
</ul>
</section>
<section id="are-there-coding-standards-or-a-style-guide-for-python-programs">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Are there coding standards or a style guide for Python programs?</a><a class="headerlink" href="#are-there-coding-standards-or-a-style-guide-for-python-programs" title="Link to this heading">¶</a></h3>
<p>Yes.  The coding style required for standard library modules is documented as
<span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0008/"><strong>PEP 8</strong></a>.</p>
</section>
</section>
<section id="core-language">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Core Language</a><a class="headerlink" href="#core-language" title="Link to this heading">¶</a></h2>
<section id="why-am-i-getting-an-unboundlocalerror-when-the-variable-has-a-value">
<span id="faq-unboundlocalerror"></span><h3><a class="toc-backref" href="#id9" role="doc-backlink">Why am I getting an UnboundLocalError when the variable has a value?</a><a class="headerlink" href="#why-am-i-getting-an-unboundlocalerror-when-the-variable-has-a-value" title="Link to this heading">¶</a></h3>
<p>It can be a surprise to get the <a class="reference internal" href="../library/exceptions.html#UnboundLocalError" title="UnboundLocalError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnboundLocalError</span></code></a> in previously working
code when it is modified by adding an assignment statement somewhere in
the body of a function.</p>
<p>This code:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="mi">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">bar</span><span class="p">():</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">bar</span><span class="p">()</span>
<span class="go">10</span>
</pre></div>
</div>
<p>works, but this code:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="mi">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foo</span><span class="p">():</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">x</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>
</div>
<p>results in an <code class="xref py py-exc docutils literal notranslate"><span class="pre">UnboundLocalError</span></code>:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">foo</span><span class="p">()</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">UnboundLocalError</span>: <span class="n">local variable &#39;x&#39; referenced before assignment</span>
</pre></div>
</div>
<p>This is because when you make an assignment to a variable in a scope, that
variable becomes local to that scope and shadows any similarly named variable
in the outer scope.  Since the last statement in foo assigns a new value to
<code class="docutils literal notranslate"><span class="pre">x</span></code>, the compiler recognizes it as a local variable.  Consequently when the
earlier <code class="docutils literal notranslate"><span class="pre">print(x)</span></code> attempts to print the uninitialized local variable and
an error results.</p>
<p>In the example above you can access the outer scope variable by declaring it
global:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="mi">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foobar</span><span class="p">():</span>
<span class="gp">... </span>    <span class="k">global</span> <span class="n">x</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">x</span> <span class="o">+=</span> <span class="mi">1</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">foobar</span><span class="p">()</span>
<span class="go">10</span>
</pre></div>
</div>
<p>This explicit declaration is required in order to remind you that (unlike the
superficially analogous situation with class and instance variables) you are
actually modifying the value of the variable in the outer scope:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="go">11</span>
</pre></div>
</div>
<p>You can do a similar thing in a nested scope using the <a class="reference internal" href="../reference/simple_stmts.html#nonlocal"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">nonlocal</span></code></a>
keyword:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">foo</span><span class="p">():</span>
<span class="gp">... </span>   <span class="n">x</span> <span class="o">=</span> <span class="mi">10</span>
<span class="gp">... </span>   <span class="k">def</span> <span class="nf">bar</span><span class="p">():</span>
<span class="gp">... </span>       <span class="k">nonlocal</span> <span class="n">x</span>
<span class="gp">... </span>       <span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="gp">... </span>       <span class="n">x</span> <span class="o">+=</span> <span class="mi">1</span>
<span class="gp">... </span>   <span class="n">bar</span><span class="p">()</span>
<span class="gp">... </span>   <span class="nb">print</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">foo</span><span class="p">()</span>
<span class="go">10</span>
<span class="go">11</span>
</pre></div>
</div>
</section>
<section id="what-are-the-rules-for-local-and-global-variables-in-python">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">What are the rules for local and global variables in Python?</a><a class="headerlink" href="#what-are-the-rules-for-local-and-global-variables-in-python" title="Link to this heading">¶</a></h3>
<p>In Python, variables that are only referenced inside a function are implicitly
global.  If a variable is assigned a value anywhere within the function’s body,
it’s assumed to be a local unless explicitly declared as global.</p>
<p>Though a bit surprising at first, a moment’s consideration explains this.  On
one hand, requiring <a class="reference internal" href="../reference/simple_stmts.html#global"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">global</span></code></a> for assigned variables provides a bar
against unintended side-effects.  On the other hand, if <code class="docutils literal notranslate"><span class="pre">global</span></code> was required
for all global references, you’d be using <code class="docutils literal notranslate"><span class="pre">global</span></code> all the time.  You’d have
to declare as global every reference to a built-in function or to a component of
an imported module.  This clutter would defeat the usefulness of the <code class="docutils literal notranslate"><span class="pre">global</span></code>
declaration for identifying side-effects.</p>
</section>
<section id="why-do-lambdas-defined-in-a-loop-with-different-values-all-return-the-same-result">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Why do lambdas defined in a loop with different values all return the same result?</a><a class="headerlink" href="#why-do-lambdas-defined-in-a-loop-with-different-values-all-return-the-same-result" title="Link to this heading">¶</a></h3>
<p>Assume you use a for loop to define a few different lambdas (or even plain
functions), e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">squares</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="k">lambda</span><span class="p">:</span> <span class="n">x</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>This gives you a list that contains 5 lambdas that calculate <code class="docutils literal notranslate"><span class="pre">x**2</span></code>.  You
might expect that, when called, they would return, respectively, <code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">1</span></code>,
<code class="docutils literal notranslate"><span class="pre">4</span></code>, <code class="docutils literal notranslate"><span class="pre">9</span></code>, and <code class="docutils literal notranslate"><span class="pre">16</span></code>.  However, when you actually try you will see that
they all return <code class="docutils literal notranslate"><span class="pre">16</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span><span class="p">[</span><span class="mi">2</span><span class="p">]()</span>
<span class="go">16</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span><span class="p">[</span><span class="mi">4</span><span class="p">]()</span>
<span class="go">16</span>
</pre></div>
</div>
<p>This happens because <code class="docutils literal notranslate"><span class="pre">x</span></code> is not local to the lambdas, but is defined in
the outer scope, and it is accessed when the lambda is called — not when it
is defined.  At the end of the loop, the value of <code class="docutils literal notranslate"><span class="pre">x</span></code> is <code class="docutils literal notranslate"><span class="pre">4</span></code>, so all the
functions now return <code class="docutils literal notranslate"><span class="pre">4**2</span></code>, i.e. <code class="docutils literal notranslate"><span class="pre">16</span></code>.  You can also verify this by
changing the value of <code class="docutils literal notranslate"><span class="pre">x</span></code> and see how the results of the lambdas change:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="mi">8</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span><span class="p">[</span><span class="mi">2</span><span class="p">]()</span>
<span class="go">64</span>
</pre></div>
</div>
<p>In order to avoid this, you need to save the values in variables local to the
lambdas, so that they don’t rely on the value of the global <code class="docutils literal notranslate"><span class="pre">x</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">squares</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="k">lambda</span> <span class="n">n</span><span class="o">=</span><span class="n">x</span><span class="p">:</span> <span class="n">n</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>Here, <code class="docutils literal notranslate"><span class="pre">n=x</span></code> creates a new variable <code class="docutils literal notranslate"><span class="pre">n</span></code> local to the lambda and computed
when the lambda is defined so that it has the same value that <code class="docutils literal notranslate"><span class="pre">x</span></code> had at
that point in the loop.  This means that the value of <code class="docutils literal notranslate"><span class="pre">n</span></code> will be <code class="docutils literal notranslate"><span class="pre">0</span></code>
in the first lambda, <code class="docutils literal notranslate"><span class="pre">1</span></code> in the second, <code class="docutils literal notranslate"><span class="pre">2</span></code> in the third, and so on.
Therefore each lambda will now return the correct result:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span><span class="p">[</span><span class="mi">2</span><span class="p">]()</span>
<span class="go">4</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">squares</span><span class="p">[</span><span class="mi">4</span><span class="p">]()</span>
<span class="go">16</span>
</pre></div>
</div>
<p>Note that this behaviour is not peculiar to lambdas, but applies to regular
functions too.</p>
</section>
<section id="how-do-i-share-global-variables-across-modules">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">How do I share global variables across modules?</a><a class="headerlink" href="#how-do-i-share-global-variables-across-modules" title="Link to this heading">¶</a></h3>
<p>The canonical way to share information across modules within a single program is
to create a special module (often called config or cfg).  Just import the config
module in all modules of your application; the module then becomes available as
a global name.  Because there is only one instance of each module, any changes
made to the module object get reflected everywhere.  For example:</p>
<p>config.py:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">x</span> <span class="o">=</span> <span class="mi">0</span>   <span class="c1"># Default value of the &#39;x&#39; configuration setting</span>
</pre></div>
</div>
<p>mod.py:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">config</span>
<span class="n">config</span><span class="o">.</span><span class="n">x</span> <span class="o">=</span> <span class="mi">1</span>
</pre></div>
</div>
<p>main.py:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">config</span>
<span class="kn">import</span> <span class="nn">mod</span>
<span class="nb">print</span><span class="p">(</span><span class="n">config</span><span class="o">.</span><span class="n">x</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that using a module is also the basis for implementing the singleton design
pattern, for the same reason.</p>
</section>
<section id="what-are-the-best-practices-for-using-import-in-a-module">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">What are the “best practices” for using import in a module?</a><a class="headerlink" href="#what-are-the-best-practices-for-using-import-in-a-module" title="Link to this heading">¶</a></h3>
<p>In general, don’t use <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">modulename</span> <span class="pre">import</span> <span class="pre">*</span></code>.  Doing so clutters the
importer’s namespace, and makes it much harder for linters to detect undefined
names.</p>
<p>Import modules at the top of a file.  Doing so makes it clear what other modules
your code requires and avoids questions of whether the module name is in scope.
Using one import per line makes it easy to add and delete module imports, but
using multiple imports per line uses less screen space.</p>
<p>It’s good practice if you import modules in the following order:</p>
<ol class="arabic simple">
<li><p>standard library modules – e.g. <a class="reference internal" href="../library/sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a>, <a class="reference internal" href="../library/os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a>, <a class="reference internal" href="../library/argparse.html#module-argparse" title="argparse: Command-line option and argument parsing library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">argparse</span></code></a>, <a class="reference internal" href="../library/re.html#module-re" title="re: Regular expression operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code></a></p></li>
<li><p>third-party library modules (anything installed in Python’s site-packages
directory) – e.g. <code class="xref py py-mod docutils literal notranslate"><span class="pre">dateutil</span></code>, <code class="xref py py-mod docutils literal notranslate"><span class="pre">requests</span></code>, <code class="xref py py-mod docutils literal notranslate"><span class="pre">PIL.Image</span></code></p></li>
<li><p>locally developed modules</p></li>
</ol>
<p>It is sometimes necessary to move imports to a function or class to avoid
problems with circular imports.  Gordon McMillan says:</p>
<blockquote>
<div><p>Circular imports are fine where both modules use the “import &lt;module&gt;” form
of import.  They fail when the 2nd module wants to grab a name out of the
first (“from module import name”) and the import is at the top level.  That’s
because names in the 1st are not yet available, because the first module is
busy importing the 2nd.</p>
</div></blockquote>
<p>In this case, if the second module is only used in one function, then the import
can easily be moved into that function.  By the time the import is called, the
first module will have finished initializing, and the second module can do its
import.</p>
<p>It may also be necessary to move imports out of the top level of code if some of
the modules are platform-specific.  In that case, it may not even be possible to
import all of the modules at the top of the file.  In this case, importing the
correct modules in the corresponding platform-specific code is a good option.</p>
<p>Only move imports into a local scope, such as inside a function definition, if
it’s necessary to solve a problem such as avoiding a circular import or are
trying to reduce the initialization time of a module.  This technique is
especially helpful if many of the imports are unnecessary depending on how the
program executes.  You may also want to move imports into a function if the
modules are only ever used in that function.  Note that loading a module the
first time may be expensive because of the one time initialization of the
module, but loading a module multiple times is virtually free, costing only a
couple of dictionary lookups.  Even if the module name has gone out of scope,
the module is probably available in <a class="reference internal" href="../library/sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>.</p>
</section>
<section id="why-are-default-values-shared-between-objects">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Why are default values shared between objects?</a><a class="headerlink" href="#why-are-default-values-shared-between-objects" title="Link to this heading">¶</a></h3>
<p>This type of bug commonly bites neophyte programmers.  Consider this function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">mydict</span><span class="o">=</span><span class="p">{}):</span>  <span class="c1"># Danger: shared reference to one dict for all calls</span>
    <span class="o">...</span> <span class="n">compute</span> <span class="n">something</span> <span class="o">...</span>
    <span class="n">mydict</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
    <span class="k">return</span> <span class="n">mydict</span>
</pre></div>
</div>
<p>The first time you call this function, <code class="docutils literal notranslate"><span class="pre">mydict</span></code> contains a single item.  The
second time, <code class="docutils literal notranslate"><span class="pre">mydict</span></code> contains two items because when <code class="docutils literal notranslate"><span class="pre">foo()</span></code> begins
executing, <code class="docutils literal notranslate"><span class="pre">mydict</span></code> starts out with an item already in it.</p>
<p>It is often expected that a function call creates new objects for default
values. This is not what happens. Default values are created exactly once, when
the function is defined.  If that object is changed, like the dictionary in this
example, subsequent calls to the function will refer to this changed object.</p>
<p>By definition, immutable objects such as numbers, strings, tuples, and <code class="docutils literal notranslate"><span class="pre">None</span></code>,
are safe from change. Changes to mutable objects such as dictionaries, lists,
and class instances can lead to confusion.</p>
<p>Because of this feature, it is good programming practice to not use mutable
objects as default values.  Instead, use <code class="docutils literal notranslate"><span class="pre">None</span></code> as the default value and
inside the function, check if the parameter is <code class="docutils literal notranslate"><span class="pre">None</span></code> and create a new
list/dictionary/whatever if it is.  For example, don’t write:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">mydict</span><span class="o">=</span><span class="p">{}):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>but:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">foo</span><span class="p">(</span><span class="n">mydict</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">mydict</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">mydict</span> <span class="o">=</span> <span class="p">{}</span>  <span class="c1"># create a new dict for local namespace</span>
</pre></div>
</div>
<p>This feature can be useful.  When you have a function that’s time-consuming to
compute, a common technique is to cache the parameters and the resulting value
of each call to the function, and return the cached value if the same value is
requested again.  This is called “memoizing”, and can be implemented like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Callers can only provide two parameters and optionally pass _cache by keyword</span>
<span class="k">def</span> <span class="nf">expensive</span><span class="p">(</span><span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">_cache</span><span class="o">=</span><span class="p">{}):</span>
    <span class="k">if</span> <span class="p">(</span><span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">)</span> <span class="ow">in</span> <span class="n">_cache</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">_cache</span><span class="p">[(</span><span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">)]</span>

    <span class="c1"># Calculate the value</span>
    <span class="n">result</span> <span class="o">=</span> <span class="o">...</span> <span class="n">expensive</span> <span class="n">computation</span> <span class="o">...</span>
    <span class="n">_cache</span><span class="p">[(</span><span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">)]</span> <span class="o">=</span> <span class="n">result</span>           <span class="c1"># Store result in the cache</span>
    <span class="k">return</span> <span class="n">result</span>
</pre></div>
</div>
<p>You could use a global variable containing a dictionary instead of the default
value; it’s a matter of taste.</p>
</section>
<section id="how-can-i-pass-optional-or-keyword-parameters-from-one-function-to-another">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">How can I pass optional or keyword parameters from one function to another?</a><a class="headerlink" href="#how-can-i-pass-optional-or-keyword-parameters-from-one-function-to-another" title="Link to this heading">¶</a></h3>
<p>Collect the arguments using the <code class="docutils literal notranslate"><span class="pre">*</span></code> and <code class="docutils literal notranslate"><span class="pre">**</span></code> specifiers in the function’s
parameter list; this gives you the positional arguments as a tuple and the
keyword arguments as a dictionary.  You can then pass these arguments when
calling another function by using <code class="docutils literal notranslate"><span class="pre">*</span></code> and <code class="docutils literal notranslate"><span class="pre">**</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">f</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="o">...</span>
    <span class="n">kwargs</span><span class="p">[</span><span class="s1">&#39;width&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;14.3c&#39;</span>
    <span class="o">...</span>
    <span class="n">g</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="what-is-the-difference-between-arguments-and-parameters">
<span id="faq-argument-vs-parameter"></span><span id="index-1"></span><h3><a class="toc-backref" href="#id16" role="doc-backlink">What is the difference between arguments and parameters?</a><a class="headerlink" href="#what-is-the-difference-between-arguments-and-parameters" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="../glossary.html#term-parameter"><span class="xref std std-term">Parameters</span></a> are defined by the names that appear in a
function definition, whereas <a class="reference internal" href="../glossary.html#term-argument"><span class="xref std std-term">arguments</span></a> are the values
actually passed to a function when calling it.  Parameters define what
<a class="reference internal" href="../glossary.html#term-parameter"><span class="xref std std-term">kind of arguments</span></a> a function can accept.  For
example, given the function definition:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">func</span><span class="p">(</span><span class="n">foo</span><span class="p">,</span> <span class="n">bar</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p><em>foo</em>, <em>bar</em> and <em>kwargs</em> are parameters of <code class="docutils literal notranslate"><span class="pre">func</span></code>.  However, when calling
<code class="docutils literal notranslate"><span class="pre">func</span></code>, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="mi">42</span><span class="p">,</span> <span class="n">bar</span><span class="o">=</span><span class="mi">314</span><span class="p">,</span> <span class="n">extra</span><span class="o">=</span><span class="n">somevar</span><span class="p">)</span>
</pre></div>
</div>
<p>the values <code class="docutils literal notranslate"><span class="pre">42</span></code>, <code class="docutils literal notranslate"><span class="pre">314</span></code>, and <code class="docutils literal notranslate"><span class="pre">somevar</span></code> are arguments.</p>
</section>
<section id="why-did-changing-list-y-also-change-list-x">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Why did changing list ‘y’ also change list ‘x’?</a><a class="headerlink" href="#why-did-changing-list-y-also-change-list-x" title="Link to this heading">¶</a></h3>
<p>If you wrote code like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">y</span> <span class="o">=</span> <span class="n">x</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">y</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">y</span>
<span class="go">[10]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span>
<span class="go">[10]</span>
</pre></div>
</div>
<p>you might be wondering why appending an element to <code class="docutils literal notranslate"><span class="pre">y</span></code> changed <code class="docutils literal notranslate"><span class="pre">x</span></code> too.</p>
<p>There are two factors that produce this result:</p>
<ol class="arabic simple">
<li><p>Variables are simply names that refer to objects.  Doing <code class="docutils literal notranslate"><span class="pre">y</span> <span class="pre">=</span> <span class="pre">x</span></code> doesn’t
create a copy of the list – it creates a new variable <code class="docutils literal notranslate"><span class="pre">y</span></code> that refers to
the same object <code class="docutils literal notranslate"><span class="pre">x</span></code> refers to.  This means that there is only one object
(the list), and both <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> refer to it.</p></li>
<li><p>Lists are <a class="reference internal" href="../glossary.html#term-mutable"><span class="xref std std-term">mutable</span></a>, which means that you can change their content.</p></li>
</ol>
<p>After the call to <code class="xref py py-meth docutils literal notranslate"><span class="pre">append()</span></code>, the content of the mutable object has
changed from <code class="docutils literal notranslate"><span class="pre">[]</span></code> to <code class="docutils literal notranslate"><span class="pre">[10]</span></code>.  Since both the variables refer to the same
object, using either name accesses the modified value <code class="docutils literal notranslate"><span class="pre">[10]</span></code>.</p>
<p>If we instead assign an immutable object to <code class="docutils literal notranslate"><span class="pre">x</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="mi">5</span>  <span class="c1"># ints are immutable</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">y</span> <span class="o">=</span> <span class="n">x</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="n">x</span> <span class="o">+</span> <span class="mi">1</span>  <span class="c1"># 5 can&#39;t be mutated, we are creating a new object here</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span>
<span class="go">6</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">y</span>
<span class="go">5</span>
</pre></div>
</div>
<p>we can see that in this case <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are not equal anymore.  This is
because integers are <a class="reference internal" href="../glossary.html#term-immutable"><span class="xref std std-term">immutable</span></a>, and when we do <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">=</span> <span class="pre">x</span> <span class="pre">+</span> <span class="pre">1</span></code> we are not
mutating the int <code class="docutils literal notranslate"><span class="pre">5</span></code> by incrementing its value; instead, we are creating a
new object (the int <code class="docutils literal notranslate"><span class="pre">6</span></code>) and assigning it to <code class="docutils literal notranslate"><span class="pre">x</span></code> (that is, changing which
object <code class="docutils literal notranslate"><span class="pre">x</span></code> refers to).  After this assignment we have two objects (the ints
<code class="docutils literal notranslate"><span class="pre">6</span></code> and <code class="docutils literal notranslate"><span class="pre">5</span></code>) and two variables that refer to them (<code class="docutils literal notranslate"><span class="pre">x</span></code> now refers to
<code class="docutils literal notranslate"><span class="pre">6</span></code> but <code class="docutils literal notranslate"><span class="pre">y</span></code> still refers to <code class="docutils literal notranslate"><span class="pre">5</span></code>).</p>
<p>Some operations (for example <code class="docutils literal notranslate"><span class="pre">y.append(10)</span></code> and <code class="docutils literal notranslate"><span class="pre">y.sort()</span></code>) mutate the
object, whereas superficially similar operations (for example <code class="docutils literal notranslate"><span class="pre">y</span> <span class="pre">=</span> <span class="pre">y</span> <span class="pre">+</span> <span class="pre">[10]</span></code>
and <a class="reference internal" href="../library/functions.html#sorted" title="sorted"><code class="xref py py-func docutils literal notranslate"><span class="pre">sorted(y)</span></code></a>) create a new object.  In general in Python (and in all cases
in the standard library) a method that mutates an object will return <code class="docutils literal notranslate"><span class="pre">None</span></code>
to help avoid getting the two types of operations confused.  So if you
mistakenly write <code class="docutils literal notranslate"><span class="pre">y.sort()</span></code> thinking it will give you a sorted copy of <code class="docutils literal notranslate"><span class="pre">y</span></code>,
you’ll instead end up with <code class="docutils literal notranslate"><span class="pre">None</span></code>, which will likely cause your program to
generate an easily diagnosed error.</p>
<p>However, there is one class of operations where the same operation sometimes
has different behaviors with different types:  the augmented assignment
operators.  For example, <code class="docutils literal notranslate"><span class="pre">+=</span></code> mutates lists but not tuples or ints (<code class="docutils literal notranslate"><span class="pre">a_list</span>
<span class="pre">+=</span> <span class="pre">[1,</span> <span class="pre">2,</span> <span class="pre">3]</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">a_list.extend([1,</span> <span class="pre">2,</span> <span class="pre">3])</span></code> and mutates
<code class="docutils literal notranslate"><span class="pre">a_list</span></code>, whereas <code class="docutils literal notranslate"><span class="pre">some_tuple</span> <span class="pre">+=</span> <span class="pre">(1,</span> <span class="pre">2,</span> <span class="pre">3)</span></code> and <code class="docutils literal notranslate"><span class="pre">some_int</span> <span class="pre">+=</span> <span class="pre">1</span></code> create
new objects).</p>
<p>In other words:</p>
<ul class="simple">
<li><p>If we have a mutable object (<a class="reference internal" href="../library/stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>, <a class="reference internal" href="../library/stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>, <a class="reference internal" href="../library/stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">set</span></code></a>,
etc.), we can use some specific operations to mutate it and all the variables
that refer to it will see the change.</p></li>
<li><p>If we have an immutable object (<a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference internal" href="../library/stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>,
etc.), all the variables that refer to it will always see the same value,
but operations that transform that value into a new value always return a new
object.</p></li>
</ul>
<p>If you want to know if two variables refer to the same object or not, you can
use the <a class="reference internal" href="../reference/expressions.html#is"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">is</span></code></a> operator, or the built-in function <a class="reference internal" href="../library/functions.html#id" title="id"><code class="xref py py-func docutils literal notranslate"><span class="pre">id()</span></code></a>.</p>
</section>
<section id="how-do-i-write-a-function-with-output-parameters-call-by-reference">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">How do I write a function with output parameters (call by reference)?</a><a class="headerlink" href="#how-do-i-write-a-function-with-output-parameters-call-by-reference" title="Link to this heading">¶</a></h3>
<p>Remember that arguments are passed by assignment in Python.  Since assignment
just creates references to objects, there’s no alias between an argument name in
the caller and callee, and so no call-by-reference per se.  You can achieve the
desired effect in a number of ways.</p>
<ol class="arabic">
<li><p>By returning a tuple of the results:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">func1</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">a</span> <span class="o">=</span> <span class="s1">&#39;new-value&#39;</span>        <span class="c1"># a and b are local names</span>
<span class="gp">... </span>    <span class="n">b</span> <span class="o">=</span> <span class="n">b</span> <span class="o">+</span> <span class="mi">1</span>              <span class="c1"># assigned to new objects</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span>            <span class="c1"># return new values</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="s1">&#39;old-value&#39;</span><span class="p">,</span> <span class="mi">99</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">func1</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
<span class="go">(&#39;new-value&#39;, 100)</span>
</pre></div>
</div>
<p>This is almost always the clearest solution.</p>
</li>
<li><p>By using global variables.  This isn’t thread-safe, and is not recommended.</p></li>
<li><p>By passing a mutable (changeable in-place) object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">func2</span><span class="p">(</span><span class="n">a</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">a</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;new-value&#39;</span>     <span class="c1"># &#39;a&#39; references a mutable list</span>
<span class="gp">... </span>    <span class="n">a</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="n">a</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>        <span class="c1"># changes a shared object</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">args</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;old-value&#39;</span><span class="p">,</span> <span class="mi">99</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">func2</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">args</span>
<span class="go">[&#39;new-value&#39;, 100]</span>
</pre></div>
</div>
</li>
<li><p>By passing in a dictionary that gets mutated:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">func3</span><span class="p">(</span><span class="n">args</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">args</span><span class="p">[</span><span class="s1">&#39;a&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;new-value&#39;</span>     <span class="c1"># args is a mutable dictionary</span>
<span class="gp">... </span>    <span class="n">args</span><span class="p">[</span><span class="s1">&#39;b&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">args</span><span class="p">[</span><span class="s1">&#39;b&#39;</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>   <span class="c1"># change it in-place</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">args</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;a&#39;</span><span class="p">:</span> <span class="s1">&#39;old-value&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">:</span> <span class="mi">99</span><span class="p">}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">func3</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">args</span>
<span class="go">{&#39;a&#39;: &#39;new-value&#39;, &#39;b&#39;: 100}</span>
</pre></div>
</div>
</li>
<li><p>Or bundle up values in a class instance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Namespace</span><span class="p">:</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">/</span><span class="p">,</span> <span class="o">**</span><span class="n">args</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">args</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
<span class="gp">... </span>            <span class="nb">setattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">func4</span><span class="p">(</span><span class="n">args</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">args</span><span class="o">.</span><span class="n">a</span> <span class="o">=</span> <span class="s1">&#39;new-value&#39;</span>        <span class="c1"># args is a mutable Namespace</span>
<span class="gp">... </span>    <span class="n">args</span><span class="o">.</span><span class="n">b</span> <span class="o">=</span> <span class="n">args</span><span class="o">.</span><span class="n">b</span> <span class="o">+</span> <span class="mi">1</span>         <span class="c1"># change object in-place</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">args</span> <span class="o">=</span> <span class="n">Namespace</span><span class="p">(</span><span class="n">a</span><span class="o">=</span><span class="s1">&#39;old-value&#39;</span><span class="p">,</span> <span class="n">b</span><span class="o">=</span><span class="mi">99</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">func4</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">vars</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
<span class="go">{&#39;a&#39;: &#39;new-value&#39;, &#39;b&#39;: 100}</span>
</pre></div>
</div>
<p>There’s almost never a good reason to get this complicated.</p>
</li>
</ol>
<p>Your best choice is to return a tuple containing the multiple results.</p>
</section>
<section id="how-do-you-make-a-higher-order-function-in-python">
<h3><a class="toc-backref" href="#id19" role="doc-backlink">How do you make a higher order function in Python?</a><a class="headerlink" href="#how-do-you-make-a-higher-order-function-in-python" title="Link to this heading">¶</a></h3>
<p>You have two choices: you can use nested scopes or you can use callable objects.
For example, suppose you wanted to define <code class="docutils literal notranslate"><span class="pre">linear(a,b)</span></code> which returns a
function <code class="docutils literal notranslate"><span class="pre">f(x)</span></code> that computes the value <code class="docutils literal notranslate"><span class="pre">a*x+b</span></code>.  Using nested scopes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">linear</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">result</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">a</span> <span class="o">*</span> <span class="n">x</span> <span class="o">+</span> <span class="n">b</span>
    <span class="k">return</span> <span class="n">result</span>
</pre></div>
</div>
<p>Or using a callable object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">linear</span><span class="p">:</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">a</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">b</span> <span class="o">=</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span>

    <span class="k">def</span> <span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">a</span> <span class="o">*</span> <span class="n">x</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">b</span>
</pre></div>
</div>
<p>In both cases,</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">taxes</span> <span class="o">=</span> <span class="n">linear</span><span class="p">(</span><span class="mf">0.3</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>gives a callable object where <code class="docutils literal notranslate"><span class="pre">taxes(10e6)</span> <span class="pre">==</span> <span class="pre">0.3</span> <span class="pre">*</span> <span class="pre">10e6</span> <span class="pre">+</span> <span class="pre">2</span></code>.</p>
<p>The callable object approach has the disadvantage that it is a bit slower and
results in slightly longer code.  However, note that a collection of callables
can share their signature via inheritance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">exponential</span><span class="p">(</span><span class="n">linear</span><span class="p">):</span>
    <span class="c1"># __init__ inherited</span>
    <span class="k">def</span> <span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">a</span> <span class="o">*</span> <span class="p">(</span><span class="n">x</span> <span class="o">**</span> <span class="bp">self</span><span class="o">.</span><span class="n">b</span><span class="p">)</span>
</pre></div>
</div>
<p>Object can encapsulate state for several methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">counter</span><span class="p">:</span>

    <span class="n">value</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">def</span> <span class="nf">set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">=</span> <span class="n">x</span>

    <span class="k">def</span> <span class="nf">up</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">+</span> <span class="mi">1</span>

    <span class="k">def</span> <span class="nf">down</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">-</span> <span class="mi">1</span>

<span class="n">count</span> <span class="o">=</span> <span class="n">counter</span><span class="p">()</span>
<span class="n">inc</span><span class="p">,</span> <span class="n">dec</span><span class="p">,</span> <span class="n">reset</span> <span class="o">=</span> <span class="n">count</span><span class="o">.</span><span class="n">up</span><span class="p">,</span> <span class="n">count</span><span class="o">.</span><span class="n">down</span><span class="p">,</span> <span class="n">count</span><span class="o">.</span><span class="n">set</span>
</pre></div>
</div>
<p>Here <code class="docutils literal notranslate"><span class="pre">inc()</span></code>, <code class="docutils literal notranslate"><span class="pre">dec()</span></code> and <code class="docutils literal notranslate"><span class="pre">reset()</span></code> act like functions which share the
same counting variable.</p>
</section>
<section id="how-do-i-copy-an-object-in-python">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">How do I copy an object in Python?</a><a class="headerlink" href="#how-do-i-copy-an-object-in-python" title="Link to this heading">¶</a></h3>
<p>In general, try <a class="reference internal" href="../library/copy.html#copy.copy" title="copy.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> or <a class="reference internal" href="../library/copy.html#copy.deepcopy" title="copy.deepcopy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.deepcopy()</span></code></a> for the general case.
Not all objects can be copied, but most can.</p>
<p>Some objects can be copied more easily.  Dictionaries have a <a class="reference internal" href="../library/stdtypes.html#dict.copy" title="dict.copy"><code class="xref py py-meth docutils literal notranslate"><span class="pre">copy()</span></code></a>
method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">newdict</span> <span class="o">=</span> <span class="n">olddict</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
</pre></div>
</div>
<p>Sequences can be copied by slicing:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">new_l</span> <span class="o">=</span> <span class="n">l</span><span class="p">[:]</span>
</pre></div>
</div>
</section>
<section id="how-can-i-find-the-methods-or-attributes-of-an-object">
<h3><a class="toc-backref" href="#id21" role="doc-backlink">How can I find the methods or attributes of an object?</a><a class="headerlink" href="#how-can-i-find-the-methods-or-attributes-of-an-object" title="Link to this heading">¶</a></h3>
<p>For an instance <code class="docutils literal notranslate"><span class="pre">x</span></code> of a user-defined class, <a class="reference internal" href="../library/functions.html#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir(x)</span></code></a> returns an alphabetized
list of the names containing the instance attributes and methods and attributes
defined by its class.</p>
</section>
<section id="how-can-my-code-discover-the-name-of-an-object">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">How can my code discover the name of an object?</a><a class="headerlink" href="#how-can-my-code-discover-the-name-of-an-object" title="Link to this heading">¶</a></h3>
<p>Generally speaking, it can’t, because objects don’t really have names.
Essentially, assignment always binds a name to a value; the same is true of
<code class="docutils literal notranslate"><span class="pre">def</span></code> and <code class="docutils literal notranslate"><span class="pre">class</span></code> statements, but in that case the value is a
callable. Consider the following code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">A</span><span class="p">:</span>
<span class="gp">... </span>    <span class="k">pass</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">B</span> <span class="o">=</span> <span class="n">A</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">B</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">a</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">b</span><span class="p">)</span>
<span class="go">&lt;__main__.A object at 0x16D07CC&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">&lt;__main__.A object at 0x16D07CC&gt;</span>
</pre></div>
</div>
<p>Arguably the class has a name: even though it is bound to two names and invoked
through the name <code class="docutils literal notranslate"><span class="pre">B</span></code> the created instance is still reported as an instance of
class <code class="docutils literal notranslate"><span class="pre">A</span></code>.  However, it is impossible to say whether the instance’s name is <code class="docutils literal notranslate"><span class="pre">a</span></code> or
<code class="docutils literal notranslate"><span class="pre">b</span></code>, since both names are bound to the same value.</p>
<p>Generally speaking it should not be necessary for your code to “know the names”
of particular values. Unless you are deliberately writing introspective
programs, this is usually an indication that a change of approach might be
beneficial.</p>
<p>In comp.lang.python, Fredrik Lundh once gave an excellent analogy in answer to
this question:</p>
<blockquote>
<div><p>The same way as you get the name of that cat you found on your porch: the cat
(object) itself cannot tell you its name, and it doesn’t really care – so
the only way to find out what it’s called is to ask all your neighbours
(namespaces) if it’s their cat (object)…</p>
<p>….and don’t be surprised if you’ll find that it’s known by many names, or
no name at all!</p>
</div></blockquote>
</section>
<section id="what-s-up-with-the-comma-operator-s-precedence">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">What’s up with the comma operator’s precedence?</a><a class="headerlink" href="#what-s-up-with-the-comma-operator-s-precedence" title="Link to this heading">¶</a></h3>
<p>Comma is not an operator in Python.  Consider this session:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s2">&quot;a&quot;</span> <span class="ow">in</span> <span class="s2">&quot;b&quot;</span><span class="p">,</span> <span class="s2">&quot;a&quot;</span>
<span class="go">(False, &#39;a&#39;)</span>
</pre></div>
</div>
<p>Since the comma is not an operator, but a separator between expressions the
above is evaluated as if you had entered:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="s2">&quot;a&quot;</span> <span class="ow">in</span> <span class="s2">&quot;b&quot;</span><span class="p">),</span> <span class="s2">&quot;a&quot;</span>
</pre></div>
</div>
<p>not:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="s2">&quot;a&quot;</span> <span class="ow">in</span> <span class="p">(</span><span class="s2">&quot;b&quot;</span><span class="p">,</span> <span class="s2">&quot;a&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The same is true of the various assignment operators (<code class="docutils literal notranslate"><span class="pre">=</span></code>, <code class="docutils literal notranslate"><span class="pre">+=</span></code> etc).  They
are not truly operators but syntactic delimiters in assignment statements.</p>
</section>
<section id="is-there-an-equivalent-of-c-s-ternary-operator">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">Is there an equivalent of C’s “?:” ternary operator?</a><a class="headerlink" href="#is-there-an-equivalent-of-c-s-ternary-operator" title="Link to this heading">¶</a></h3>
<p>Yes, there is. The syntax is as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="n">on_true</span><span class="p">]</span> <span class="k">if</span> <span class="p">[</span><span class="n">expression</span><span class="p">]</span> <span class="k">else</span> <span class="p">[</span><span class="n">on_false</span><span class="p">]</span>

<span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="mi">50</span><span class="p">,</span> <span class="mi">25</span>
<span class="n">small</span> <span class="o">=</span> <span class="n">x</span> <span class="k">if</span> <span class="n">x</span> <span class="o">&lt;</span> <span class="n">y</span> <span class="k">else</span> <span class="n">y</span>
</pre></div>
</div>
<p>Before this syntax was introduced in Python 2.5, a common idiom was to use
logical operators:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="n">expression</span><span class="p">]</span> <span class="ow">and</span> <span class="p">[</span><span class="n">on_true</span><span class="p">]</span> <span class="ow">or</span> <span class="p">[</span><span class="n">on_false</span><span class="p">]</span>
</pre></div>
</div>
<p>However, this idiom is unsafe, as it can give wrong results when <em>on_true</em>
has a false boolean value.  Therefore, it is always better to use
the <code class="docutils literal notranslate"><span class="pre">...</span> <span class="pre">if</span> <span class="pre">...</span> <span class="pre">else</span> <span class="pre">...</span></code> form.</p>
</section>
<section id="is-it-possible-to-write-obfuscated-one-liners-in-python">
<h3><a class="toc-backref" href="#id25" role="doc-backlink">Is it possible to write obfuscated one-liners in Python?</a><a class="headerlink" href="#is-it-possible-to-write-obfuscated-one-liners-in-python" title="Link to this heading">¶</a></h3>
<p>Yes.  Usually this is done by nesting <a class="reference internal" href="../reference/expressions.html#lambda"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">lambda</span></code></a> within
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">lambda</span></code>.  See the following three examples, slightly adapted from Ulf Bartelt:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">functools</span> <span class="kn">import</span> <span class="n">reduce</span>

<span class="c1"># Primes &lt; 1000</span>
<span class="nb">print</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="nb">filter</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span><span class="nb">map</span><span class="p">(</span><span class="k">lambda</span> <span class="n">y</span><span class="p">:</span><span class="n">y</span><span class="o">*</span><span class="n">reduce</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">:</span><span class="n">x</span><span class="o">*</span><span class="n">y</span><span class="o">!=</span><span class="mi">0</span><span class="p">,</span>
<span class="nb">map</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="o">=</span><span class="n">y</span><span class="p">:</span><span class="n">y</span><span class="o">%</span><span class="n">x</span><span class="p">,</span><span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="nb">int</span><span class="p">(</span><span class="nb">pow</span><span class="p">(</span><span class="n">y</span><span class="p">,</span><span class="mf">0.5</span><span class="p">)</span><span class="o">+</span><span class="mi">1</span><span class="p">))),</span><span class="mi">1</span><span class="p">),</span><span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="mi">1000</span><span class="p">)))))</span>

<span class="c1"># First 10 Fibonacci numbers</span>
<span class="nb">print</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">f</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">f</span><span class="p">:(</span><span class="n">f</span><span class="p">(</span><span class="n">x</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span><span class="n">f</span><span class="p">)</span><span class="o">+</span><span class="n">f</span><span class="p">(</span><span class="n">x</span><span class="o">-</span><span class="mi">2</span><span class="p">,</span><span class="n">f</span><span class="p">))</span> <span class="k">if</span> <span class="n">x</span><span class="o">&gt;</span><span class="mi">1</span> <span class="k">else</span> <span class="mi">1</span><span class="p">:</span>
<span class="n">f</span><span class="p">(</span><span class="n">x</span><span class="p">,</span><span class="n">f</span><span class="p">),</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">))))</span>

<span class="c1"># Mandelbrot set</span>
<span class="nb">print</span><span class="p">((</span><span class="k">lambda</span> <span class="n">Ru</span><span class="p">,</span><span class="n">Ro</span><span class="p">,</span><span class="n">Iu</span><span class="p">,</span><span class="n">Io</span><span class="p">,</span><span class="n">IM</span><span class="p">,</span><span class="n">Sx</span><span class="p">,</span><span class="n">Sy</span><span class="p">:</span><span class="n">reduce</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">:</span><span class="n">x</span><span class="o">+</span><span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span><span class="o">+</span><span class="n">y</span><span class="p">,</span><span class="nb">map</span><span class="p">(</span><span class="k">lambda</span> <span class="n">y</span><span class="p">,</span>
<span class="n">Iu</span><span class="o">=</span><span class="n">Iu</span><span class="p">,</span><span class="n">Io</span><span class="o">=</span><span class="n">Io</span><span class="p">,</span><span class="n">Ru</span><span class="o">=</span><span class="n">Ru</span><span class="p">,</span><span class="n">Ro</span><span class="o">=</span><span class="n">Ro</span><span class="p">,</span><span class="n">Sy</span><span class="o">=</span><span class="n">Sy</span><span class="p">,</span><span class="n">L</span><span class="o">=</span><span class="k">lambda</span> <span class="n">yc</span><span class="p">,</span><span class="n">Iu</span><span class="o">=</span><span class="n">Iu</span><span class="p">,</span><span class="n">Io</span><span class="o">=</span><span class="n">Io</span><span class="p">,</span><span class="n">Ru</span><span class="o">=</span><span class="n">Ru</span><span class="p">,</span><span class="n">Ro</span><span class="o">=</span><span class="n">Ro</span><span class="p">,</span><span class="n">i</span><span class="o">=</span><span class="n">IM</span><span class="p">,</span>
<span class="n">Sx</span><span class="o">=</span><span class="n">Sx</span><span class="p">,</span><span class="n">Sy</span><span class="o">=</span><span class="n">Sy</span><span class="p">:</span><span class="n">reduce</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">:</span><span class="n">x</span><span class="o">+</span><span class="n">y</span><span class="p">,</span><span class="nb">map</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">xc</span><span class="o">=</span><span class="n">Ru</span><span class="p">,</span><span class="n">yc</span><span class="o">=</span><span class="n">yc</span><span class="p">,</span><span class="n">Ru</span><span class="o">=</span><span class="n">Ru</span><span class="p">,</span><span class="n">Ro</span><span class="o">=</span><span class="n">Ro</span><span class="p">,</span>
<span class="n">i</span><span class="o">=</span><span class="n">i</span><span class="p">,</span><span class="n">Sx</span><span class="o">=</span><span class="n">Sx</span><span class="p">,</span><span class="n">F</span><span class="o">=</span><span class="k">lambda</span> <span class="n">xc</span><span class="p">,</span><span class="n">yc</span><span class="p">,</span><span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">,</span><span class="n">k</span><span class="p">,</span><span class="n">f</span><span class="o">=</span><span class="k">lambda</span> <span class="n">xc</span><span class="p">,</span><span class="n">yc</span><span class="p">,</span><span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">,</span><span class="n">k</span><span class="p">,</span><span class="n">f</span><span class="p">:(</span><span class="n">k</span><span class="o">&lt;=</span><span class="mi">0</span><span class="p">)</span><span class="ow">or</span> <span class="p">(</span><span class="n">x</span><span class="o">*</span><span class="n">x</span><span class="o">+</span><span class="n">y</span><span class="o">*</span><span class="n">y</span>
<span class="o">&gt;=</span><span class="mf">4.0</span><span class="p">)</span> <span class="ow">or</span> <span class="mi">1</span><span class="o">+</span><span class="n">f</span><span class="p">(</span><span class="n">xc</span><span class="p">,</span><span class="n">yc</span><span class="p">,</span><span class="n">x</span><span class="o">*</span><span class="n">x</span><span class="o">-</span><span class="n">y</span><span class="o">*</span><span class="n">y</span><span class="o">+</span><span class="n">xc</span><span class="p">,</span><span class="mf">2.0</span><span class="o">*</span><span class="n">x</span><span class="o">*</span><span class="n">y</span><span class="o">+</span><span class="n">yc</span><span class="p">,</span><span class="n">k</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span><span class="n">f</span><span class="p">):</span><span class="n">f</span><span class="p">(</span><span class="n">xc</span><span class="p">,</span><span class="n">yc</span><span class="p">,</span><span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">,</span><span class="n">k</span><span class="p">,</span><span class="n">f</span><span class="p">):</span><span class="nb">chr</span><span class="p">(</span>
<span class="mi">64</span><span class="o">+</span><span class="n">F</span><span class="p">(</span><span class="n">Ru</span><span class="o">+</span><span class="n">x</span><span class="o">*</span><span class="p">(</span><span class="n">Ro</span><span class="o">-</span><span class="n">Ru</span><span class="p">)</span><span class="o">/</span><span class="n">Sx</span><span class="p">,</span><span class="n">yc</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="n">i</span><span class="p">)),</span><span class="nb">range</span><span class="p">(</span><span class="n">Sx</span><span class="p">))):</span><span class="n">L</span><span class="p">(</span><span class="n">Iu</span><span class="o">+</span><span class="n">y</span><span class="o">*</span><span class="p">(</span><span class="n">Io</span><span class="o">-</span><span class="n">Iu</span><span class="p">)</span><span class="o">/</span><span class="n">Sy</span><span class="p">),</span><span class="nb">range</span><span class="p">(</span><span class="n">Sy</span>
<span class="p">))))(</span><span class="o">-</span><span class="mf">2.1</span><span class="p">,</span> <span class="mf">0.7</span><span class="p">,</span> <span class="o">-</span><span class="mf">1.2</span><span class="p">,</span> <span class="mf">1.2</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">80</span><span class="p">,</span> <span class="mi">24</span><span class="p">))</span>
<span class="c1">#    \___ ___/  \___ ___/  |   |   |__ lines on screen</span>
<span class="c1">#        V          V      |   |______ columns on screen</span>
<span class="c1">#        |          |      |__________ maximum of &quot;iterations&quot;</span>
<span class="c1">#        |          |_________________ range on y axis</span>
<span class="c1">#        |____________________________ range on x axis</span>
</pre></div>
</div>
<p>Don’t try this at home, kids!</p>
</section>
<section id="what-does-the-slash-in-the-parameter-list-of-a-function-mean">
<span id="faq-positional-only-arguments"></span><h3><a class="toc-backref" href="#id26" role="doc-backlink">What does the slash(/) in the parameter list of a function mean?</a><a class="headerlink" href="#what-does-the-slash-in-the-parameter-list-of-a-function-mean" title="Link to this heading">¶</a></h3>
<p>A slash in the argument list of a function denotes that the parameters prior to
it are positional-only.  Positional-only parameters are the ones without an
externally usable name.  Upon calling a function that accepts positional-only
parameters, arguments are mapped to parameters based solely on their position.
For example, <a class="reference internal" href="../library/functions.html#divmod" title="divmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">divmod()</span></code></a> is a function that accepts positional-only
parameters. Its documentation looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">help</span><span class="p">(</span><span class="nb">divmod</span><span class="p">)</span>
<span class="go">Help on built-in function divmod in module builtins:</span>

<span class="go">divmod(x, y, /)</span>
<span class="go">    Return the tuple (x//y, x%y).  Invariant: div*y + mod == x.</span>
</pre></div>
</div>
<p>The slash at the end of the parameter list means that both parameters are
positional-only. Thus, calling <a class="reference internal" href="../library/functions.html#divmod" title="divmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">divmod()</span></code></a> with keyword arguments would lead
to an error:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">divmod</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="gr">TypeError</span>: <span class="n">divmod() takes no keyword arguments</span>
</pre></div>
</div>
</section>
</section>
<section id="numbers-and-strings">
<h2><a class="toc-backref" href="#id27" role="doc-backlink">Numbers and strings</a><a class="headerlink" href="#numbers-and-strings" title="Link to this heading">¶</a></h2>
<section id="how-do-i-specify-hexadecimal-and-octal-integers">
<h3><a class="toc-backref" href="#id28" role="doc-backlink">How do I specify hexadecimal and octal integers?</a><a class="headerlink" href="#how-do-i-specify-hexadecimal-and-octal-integers" title="Link to this heading">¶</a></h3>
<p>To specify an octal digit, precede the octal value with a zero, and then a lower
or uppercase “o”.  For example, to set the variable “a” to the octal value “10”
(8 in decimal), type:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="mo">0o10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span>
<span class="go">8</span>
</pre></div>
</div>
<p>Hexadecimal is just as easy.  Simply precede the hexadecimal number with a zero,
and then a lower or uppercase “x”.  Hexadecimal digits can be specified in lower
or uppercase.  For example, in the Python interpreter:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="mh">0xa5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span>
<span class="go">165</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="mh">0XB2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span>
<span class="go">178</span>
</pre></div>
</div>
</section>
<section id="why-does-22-10-return-3">
<h3><a class="toc-backref" href="#id29" role="doc-backlink">Why does -22 // 10 return -3?</a><a class="headerlink" href="#why-does-22-10-return-3" title="Link to this heading">¶</a></h3>
<p>It’s primarily driven by the desire that <code class="docutils literal notranslate"><span class="pre">i</span> <span class="pre">%</span> <span class="pre">j</span></code> have the same sign as <code class="docutils literal notranslate"><span class="pre">j</span></code>.
If you want that, and also want:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">i</span> <span class="o">==</span> <span class="p">(</span><span class="n">i</span> <span class="o">//</span> <span class="n">j</span><span class="p">)</span> <span class="o">*</span> <span class="n">j</span> <span class="o">+</span> <span class="p">(</span><span class="n">i</span> <span class="o">%</span> <span class="n">j</span><span class="p">)</span>
</pre></div>
</div>
<p>then integer division has to return the floor.  C also requires that identity to
hold, and then compilers that truncate <code class="docutils literal notranslate"><span class="pre">i</span> <span class="pre">//</span> <span class="pre">j</span></code> need to make <code class="docutils literal notranslate"><span class="pre">i</span> <span class="pre">%</span> <span class="pre">j</span></code> have
the same sign as <code class="docutils literal notranslate"><span class="pre">i</span></code>.</p>
<p>There are few real use cases for <code class="docutils literal notranslate"><span class="pre">i</span> <span class="pre">%</span> <span class="pre">j</span></code> when <code class="docutils literal notranslate"><span class="pre">j</span></code> is negative.  When <code class="docutils literal notranslate"><span class="pre">j</span></code>
is positive, there are many, and in virtually all of them it’s more useful for
<code class="docutils literal notranslate"><span class="pre">i</span> <span class="pre">%</span> <span class="pre">j</span></code> to be <code class="docutils literal notranslate"><span class="pre">&gt;=</span> <span class="pre">0</span></code>.  If the clock says 10 now, what did it say 200 hours
ago?  <code class="docutils literal notranslate"><span class="pre">-190</span> <span class="pre">%</span> <span class="pre">12</span> <span class="pre">==</span> <span class="pre">2</span></code> is useful; <code class="docutils literal notranslate"><span class="pre">-190</span> <span class="pre">%</span> <span class="pre">12</span> <span class="pre">==</span> <span class="pre">-10</span></code> is a bug waiting to
bite.</p>
</section>
<section id="how-do-i-get-int-literal-attribute-instead-of-syntaxerror">
<h3><a class="toc-backref" href="#id30" role="doc-backlink">How do I get int literal attribute instead of SyntaxError?</a><a class="headerlink" href="#how-do-i-get-int-literal-attribute-instead-of-syntaxerror" title="Link to this heading">¶</a></h3>
<p>Trying to lookup an <code class="docutils literal notranslate"><span class="pre">int</span></code> literal attribute in the normal manner gives
a <a class="reference internal" href="../library/exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> because the period is seen as a decimal point:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mf">1.</span><span class="vm">__class__</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>
<span class="gr">  1.__class__</span>
<span class="gr">   ^</span>
<span class="gr">SyntaxError</span>: <span class="n">invalid decimal literal</span>
</pre></div>
</div>
<p>The solution is to separate the literal from the period
with either a space or parentheses.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mi">1</span> <span class="o">.</span><span class="vm">__class__</span>
<span class="go">&lt;class &#39;int&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="o">.</span><span class="vm">__class__</span>
<span class="go">&lt;class &#39;int&#39;&gt;</span>
</pre></div>
</div>
</section>
<section id="how-do-i-convert-a-string-to-a-number">
<h3><a class="toc-backref" href="#id31" role="doc-backlink">How do I convert a string to a number?</a><a class="headerlink" href="#how-do-i-convert-a-string-to-a-number" title="Link to this heading">¶</a></h3>
<p>For integers, use the built-in <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> type constructor, e.g. <code class="docutils literal notranslate"><span class="pre">int('144')</span>
<span class="pre">==</span> <span class="pre">144</span></code>.  Similarly, <a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a> converts to floating-point,
e.g. <code class="docutils literal notranslate"><span class="pre">float('144')</span> <span class="pre">==</span> <span class="pre">144.0</span></code>.</p>
<p>By default, these interpret the number as decimal, so that <code class="docutils literal notranslate"><span class="pre">int('0144')</span> <span class="pre">==</span>
<span class="pre">144</span></code> holds true, and <code class="docutils literal notranslate"><span class="pre">int('0x144')</span></code> raises <a class="reference internal" href="../library/exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>. <code class="docutils literal notranslate"><span class="pre">int(string,</span>
<span class="pre">base)</span></code> takes the base to convert from as a second optional argument, so <code class="docutils literal notranslate"><span class="pre">int(</span>
<span class="pre">'0x144',</span> <span class="pre">16)</span> <span class="pre">==</span> <span class="pre">324</span></code>.  If the base is specified as 0, the number is interpreted
using Python’s rules: a leading ‘0o’ indicates octal, and ‘0x’ indicates a hex
number.</p>
<p>Do not use the built-in function <a class="reference internal" href="../library/functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> if all you need is to convert
strings to numbers.  <a class="reference internal" href="../library/functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> will be significantly slower and it presents a
security risk: someone could pass you a Python expression that might have
unwanted side effects.  For example, someone could pass
<code class="docutils literal notranslate"><span class="pre">__import__('os').system(&quot;rm</span> <span class="pre">-rf</span> <span class="pre">$HOME&quot;)</span></code> which would erase your home
directory.</p>
<p><a class="reference internal" href="../library/functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> also has the effect of interpreting numbers as Python expressions,
so that e.g. <code class="docutils literal notranslate"><span class="pre">eval('09')</span></code> gives a syntax error because Python does not allow
leading ‘0’ in a decimal number (except ‘0’).</p>
</section>
<section id="how-do-i-convert-a-number-to-a-string">
<h3><a class="toc-backref" href="#id32" role="doc-backlink">How do I convert a number to a string?</a><a class="headerlink" href="#how-do-i-convert-a-number-to-a-string" title="Link to this heading">¶</a></h3>
<p>To convert, e.g., the number <code class="docutils literal notranslate"><span class="pre">144</span></code> to the string <code class="docutils literal notranslate"><span class="pre">'144'</span></code>, use the built-in type
constructor <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a>.  If you want a hexadecimal or octal representation, use
the built-in functions <a class="reference internal" href="../library/functions.html#hex" title="hex"><code class="xref py py-func docutils literal notranslate"><span class="pre">hex()</span></code></a> or <a class="reference internal" href="../library/functions.html#oct" title="oct"><code class="xref py py-func docutils literal notranslate"><span class="pre">oct()</span></code></a>.  For fancy formatting, see
the <a class="reference internal" href="../reference/lexical_analysis.html#f-strings"><span class="std std-ref">f-strings</span></a> and <a class="reference internal" href="../library/string.html#formatstrings"><span class="std std-ref">Format String Syntax</span></a> sections,
e.g. <code class="docutils literal notranslate"><span class="pre">&quot;{:04d}&quot;.format(144)</span></code> yields
<code class="docutils literal notranslate"><span class="pre">'0144'</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;{:.3f}&quot;.format(1.0/3.0)</span></code> yields <code class="docutils literal notranslate"><span class="pre">'0.333'</span></code>.</p>
</section>
<section id="how-do-i-modify-a-string-in-place">
<h3><a class="toc-backref" href="#id33" role="doc-backlink">How do I modify a string in place?</a><a class="headerlink" href="#how-do-i-modify-a-string-in-place" title="Link to this heading">¶</a></h3>
<p>You can’t, because strings are immutable.  In most situations, you should
simply construct a new string from the various parts you want to assemble
it from.  However, if you need an object with the ability to modify in-place
unicode data, try using an <a class="reference internal" href="../library/io.html#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.StringIO</span></code></a> object or the <a class="reference internal" href="../library/array.html#module-array" title="array: Space efficient arrays of uniformly typed numeric values."><code class="xref py py-mod docutils literal notranslate"><span class="pre">array</span></code></a>
module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">io</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="s2">&quot;Hello, world&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sio</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">StringIO</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sio</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>
<span class="go">&#39;Hello, world&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sio</span><span class="o">.</span><span class="n">seek</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>
<span class="go">7</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sio</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;there!&quot;</span><span class="p">)</span>
<span class="go">6</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sio</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>
<span class="go">&#39;Hello, there!&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">array</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="s1">&#39;u&#39;</span><span class="p">,</span> <span class="n">s</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">array(&#39;u&#39;, &#39;Hello, world&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;y&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">array(&#39;u&#39;, &#39;yello, world&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">tounicode</span><span class="p">()</span>
<span class="go">&#39;yello, world&#39;</span>
</pre></div>
</div>
</section>
<section id="how-do-i-use-strings-to-call-functions-methods">
<h3><a class="toc-backref" href="#id34" role="doc-backlink">How do I use strings to call functions/methods?</a><a class="headerlink" href="#how-do-i-use-strings-to-call-functions-methods" title="Link to this heading">¶</a></h3>
<p>There are various techniques.</p>
<ul>
<li><p>The best is to use a dictionary that maps strings to functions.  The primary
advantage of this technique is that the strings do not need to match the names
of the functions.  This is also the primary technique used to emulate a case
construct:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">a</span><span class="p">():</span>
    <span class="k">pass</span>

<span class="k">def</span> <span class="nf">b</span><span class="p">():</span>
    <span class="k">pass</span>

<span class="n">dispatch</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;go&#39;</span><span class="p">:</span> <span class="n">a</span><span class="p">,</span> <span class="s1">&#39;stop&#39;</span><span class="p">:</span> <span class="n">b</span><span class="p">}</span>  <span class="c1"># Note lack of parens for funcs</span>

<span class="n">dispatch</span><span class="p">[</span><span class="n">get_input</span><span class="p">()]()</span>  <span class="c1"># Note trailing parens to call function</span>
</pre></div>
</div>
</li>
<li><p>Use the built-in function <a class="reference internal" href="../library/functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">foo</span>
<span class="nb">getattr</span><span class="p">(</span><span class="n">foo</span><span class="p">,</span> <span class="s1">&#39;bar&#39;</span><span class="p">)()</span>
</pre></div>
</div>
<p>Note that <a class="reference internal" href="../library/functions.html#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a> works on any object, including classes, class
instances, modules, and so on.</p>
<p>This is used in several places in the standard library, like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Foo</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">do_foo</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="o">...</span>

    <span class="k">def</span> <span class="nf">do_bar</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="o">...</span>

<span class="n">f</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">foo_instance</span><span class="p">,</span> <span class="s1">&#39;do_&#39;</span> <span class="o">+</span> <span class="n">opname</span><span class="p">)</span>
<span class="n">f</span><span class="p">()</span>
</pre></div>
</div>
</li>
<li><p>Use <a class="reference internal" href="../library/functions.html#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a> to resolve the function name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">myFunc</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;hello&quot;</span><span class="p">)</span>

<span class="n">fname</span> <span class="o">=</span> <span class="s2">&quot;myFunc&quot;</span>

<span class="n">f</span> <span class="o">=</span> <span class="nb">locals</span><span class="p">()[</span><span class="n">fname</span><span class="p">]</span>
<span class="n">f</span><span class="p">()</span>
</pre></div>
</div>
</li>
</ul>
</section>
<section id="is-there-an-equivalent-to-perl-s-chomp-for-removing-trailing-newlines-from-strings">
<h3><a class="toc-backref" href="#id35" role="doc-backlink">Is there an equivalent to Perl’s chomp() for removing trailing newlines from strings?</a><a class="headerlink" href="#is-there-an-equivalent-to-perl-s-chomp-for-removing-trailing-newlines-from-strings" title="Link to this heading">¶</a></h3>
<p>You can use <code class="docutils literal notranslate"><span class="pre">S.rstrip(&quot;\r\n&quot;)</span></code> to remove all occurrences of any line
terminator from the end of the string <code class="docutils literal notranslate"><span class="pre">S</span></code> without removing other trailing
whitespace.  If the string <code class="docutils literal notranslate"><span class="pre">S</span></code> represents more than one line, with several
empty lines at the end, the line terminators for all the blank lines will
be removed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">lines</span> <span class="o">=</span> <span class="p">(</span><span class="s2">&quot;line 1 </span><span class="se">\r\n</span><span class="s2">&quot;</span>
<span class="gp">... </span>         <span class="s2">&quot;</span><span class="se">\r\n</span><span class="s2">&quot;</span>
<span class="gp">... </span>         <span class="s2">&quot;</span><span class="se">\r\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">lines</span><span class="o">.</span><span class="n">rstrip</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n\r</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">&#39;line 1 &#39;</span>
</pre></div>
</div>
<p>Since this is typically only desired when reading text one line at a time, using
<code class="docutils literal notranslate"><span class="pre">S.rstrip()</span></code> this way works well.</p>
</section>
<section id="is-there-a-scanf-or-sscanf-equivalent">
<h3><a class="toc-backref" href="#id36" role="doc-backlink">Is there a scanf() or sscanf() equivalent?</a><a class="headerlink" href="#is-there-a-scanf-or-sscanf-equivalent" title="Link to this heading">¶</a></h3>
<p>Not as such.</p>
<p>For simple input parsing, the easiest approach is usually to split the line into
whitespace-delimited words using the <a class="reference internal" href="../library/stdtypes.html#str.split" title="str.split"><code class="xref py py-meth docutils literal notranslate"><span class="pre">split()</span></code></a> method of string objects
and then convert decimal strings to numeric values using <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> or
<a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a>.  <code class="xref py py-meth docutils literal notranslate"><span class="pre">split()</span></code> supports an optional “sep” parameter which is useful
if the line uses something other than whitespace as a separator.</p>
<p>For more complicated input parsing, regular expressions are more powerful
than C’s <code class="docutils literal notranslate"><span class="pre">sscanf</span></code> and better suited for the task.</p>
</section>
<section id="what-does-unicodedecodeerror-or-unicodeencodeerror-error-mean">
<h3><a class="toc-backref" href="#id37" role="doc-backlink">What does ‘UnicodeDecodeError’ or ‘UnicodeEncodeError’ error  mean?</a><a class="headerlink" href="#what-does-unicodedecodeerror-or-unicodeencodeerror-error-mean" title="Link to this heading">¶</a></h3>
<p>See the <a class="reference internal" href="../howto/unicode.html#unicode-howto"><span class="std std-ref">Unicode HOWTO</span></a>.</p>
</section>
<section id="can-i-end-a-raw-string-with-an-odd-number-of-backslashes">
<span id="faq-programming-raw-string-backslash"></span><h3><a class="toc-backref" href="#id38" role="doc-backlink">Can I end a raw string with an odd number of backslashes?</a><a class="headerlink" href="#can-i-end-a-raw-string-with-an-odd-number-of-backslashes" title="Link to this heading">¶</a></h3>
<p>A raw string ending with an odd number of backslashes will escape the string’s quote:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="sa">r</span><span class="s1">&#39;C:\this\will\not\work</span><span class="se">\&#39;</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>
<span class="w">    </span><span class="sa">r</span><span class="s1">&#39;C:\this\will\not\work</span><span class="se">\&#39;</span>
<span class="w">         </span><span class="pm">^</span>
<span class="gr">SyntaxError</span>: <span class="n">unterminated string literal (detected at line 1)</span>
</pre></div>
</div>
<p>There are several workarounds for this. One is to use regular strings and double
the backslashes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;C:</span><span class="se">\\</span><span class="s1">this</span><span class="se">\\</span><span class="s1">will</span><span class="se">\\</span><span class="s1">work</span><span class="se">\\</span><span class="s1">&#39;</span>
<span class="go">&#39;C:\\this\\will\\work\\&#39;</span>
</pre></div>
</div>
<p>Another is to concatenate a regular string containing an escaped backslash to the
raw string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="sa">r</span><span class="s1">&#39;C:\this\will\work&#39;</span> <span class="s1">&#39;</span><span class="se">\\</span><span class="s1">&#39;</span>
<span class="go">&#39;C:\\this\\will\\work\\&#39;</span>
</pre></div>
</div>
<p>It is also possible to use <a class="reference internal" href="../library/os.path.html#os.path.join" title="os.path.join"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.join()</span></code></a> to append a backslash on Windows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;C:\this\will\work&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
<span class="go">&#39;C:\\this\\will\\work\\&#39;</span>
</pre></div>
</div>
<p>Note that while a backslash will “escape” a quote for the purposes of
determining where the raw string ends, no escaping occurs when interpreting the
value of the raw string. That is, the backslash remains present in the value of
the raw string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="sa">r</span><span class="s1">&#39;backslash</span><span class="se">\&#39;</span><span class="s1">preserved&#39;</span>
<span class="go">&quot;backslash\\&#39;preserved&quot;</span>
</pre></div>
</div>
<p>Also see the specification in the <a class="reference internal" href="../reference/lexical_analysis.html#strings"><span class="std std-ref">language reference</span></a>.</p>
</section>
</section>
<section id="performance">
<h2><a class="toc-backref" href="#id39" role="doc-backlink">Performance</a><a class="headerlink" href="#performance" title="Link to this heading">¶</a></h2>
<section id="my-program-is-too-slow-how-do-i-speed-it-up">
<h3><a class="toc-backref" href="#id40" role="doc-backlink">My program is too slow. How do I speed it up?</a><a class="headerlink" href="#my-program-is-too-slow-how-do-i-speed-it-up" title="Link to this heading">¶</a></h3>
<p>That’s a tough one, in general.  First, here are a list of things to
remember before diving further:</p>
<ul class="simple">
<li><p>Performance characteristics vary across Python implementations.  This FAQ
focuses on <a class="reference internal" href="../glossary.html#term-CPython"><span class="xref std std-term">CPython</span></a>.</p></li>
<li><p>Behaviour can vary across operating systems, especially when talking about
I/O or multi-threading.</p></li>
<li><p>You should always find the hot spots in your program <em>before</em> attempting to
optimize any code (see the <a class="reference internal" href="../library/profile.html#module-profile" title="profile: Python source profiler."><code class="xref py py-mod docutils literal notranslate"><span class="pre">profile</span></code></a> module).</p></li>
<li><p>Writing benchmark scripts will allow you to iterate quickly when searching
for improvements (see the <a class="reference internal" href="../library/timeit.html#module-timeit" title="timeit: Measure the execution time of small code snippets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">timeit</span></code></a> module).</p></li>
<li><p>It is highly recommended to have good code coverage (through unit testing
or any other technique) before potentially introducing regressions hidden
in sophisticated optimizations.</p></li>
</ul>
<p>That being said, there are many tricks to speed up Python code.  Here are
some general principles which go a long way towards reaching acceptable
performance levels:</p>
<ul class="simple">
<li><p>Making your algorithms faster (or changing to faster ones) can yield
much larger benefits than trying to sprinkle micro-optimization tricks
all over your code.</p></li>
<li><p>Use the right data structures.  Study documentation for the <a class="reference internal" href="../library/stdtypes.html#bltin-types"><span class="std std-ref">Built-in Types</span></a>
and the <a class="reference internal" href="../library/collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a> module.</p></li>
<li><p>When the standard library provides a primitive for doing something, it is
likely (although not guaranteed) to be faster than any alternative you
may come up with.  This is doubly true for primitives written in C, such
as builtins and some extension types.  For example, be sure to use
either the <a class="reference internal" href="../library/stdtypes.html#list.sort" title="list.sort"><code class="xref py py-meth docutils literal notranslate"><span class="pre">list.sort()</span></code></a> built-in method or the related <a class="reference internal" href="../library/functions.html#sorted" title="sorted"><code class="xref py py-func docutils literal notranslate"><span class="pre">sorted()</span></code></a>
function to do sorting (and see the <a class="reference internal" href="../howto/sorting.html#sortinghowto"><span class="std std-ref">Sorting Techniques</span></a> for examples
of moderately advanced usage).</p></li>
<li><p>Abstractions tend to create indirections and force the interpreter to work
more.  If the levels of indirection outweigh the amount of useful work
done, your program will be slower.  You should avoid excessive abstraction,
especially under the form of tiny functions or methods (which are also often
detrimental to readability).</p></li>
</ul>
<p>If you have reached the limit of what pure Python can allow, there are tools
to take you further away.  For example, <a class="reference external" href="https://cython.org">Cython</a> can
compile a slightly modified version of Python code into a C extension, and
can be used on many different platforms.  Cython can take advantage of
compilation (and optional type annotations) to make your code significantly
faster than when interpreted.  If you are confident in your C programming
skills, you can also <a class="reference internal" href="../extending/index.html#extending-index"><span class="std std-ref">write a C extension module</span></a>
yourself.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The wiki page devoted to <a class="reference external" href="https://wiki.python.org/moin/PythonSpeed/PerformanceTips">performance tips</a>.</p>
</div>
</section>
<section id="what-is-the-most-efficient-way-to-concatenate-many-strings-together">
<span id="efficient-string-concatenation"></span><h3><a class="toc-backref" href="#id41" role="doc-backlink">What is the most efficient way to concatenate many strings together?</a><a class="headerlink" href="#what-is-the-most-efficient-way-to-concatenate-many-strings-together" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> and <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects are immutable, therefore concatenating
many strings together is inefficient as each concatenation creates a new
object.  In the general case, the total runtime cost is quadratic in the
total string length.</p>
<p>To accumulate many <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> objects, the recommended idiom is to place
them into a list and call <a class="reference internal" href="../library/stdtypes.html#str.join" title="str.join"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.join()</span></code></a> at the end:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">chunks</span> <span class="o">=</span> <span class="p">[]</span>
<span class="k">for</span> <span class="n">s</span> <span class="ow">in</span> <span class="n">my_strings</span><span class="p">:</span>
    <span class="n">chunks</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="n">result</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">chunks</span><span class="p">)</span>
</pre></div>
</div>
<p>(another reasonably efficient idiom is to use <a class="reference internal" href="../library/io.html#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.StringIO</span></code></a>)</p>
<p>To accumulate many <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects, the recommended idiom is to extend
a <a class="reference internal" href="../library/stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> object using in-place concatenation (the <code class="docutils literal notranslate"><span class="pre">+=</span></code> operator):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">result</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">()</span>
<span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="n">my_bytes_objects</span><span class="p">:</span>
    <span class="n">result</span> <span class="o">+=</span> <span class="n">b</span>
</pre></div>
</div>
</section>
</section>
<section id="sequences-tuples-lists">
<h2><a class="toc-backref" href="#id42" role="doc-backlink">Sequences (Tuples/Lists)</a><a class="headerlink" href="#sequences-tuples-lists" title="Link to this heading">¶</a></h2>
<section id="how-do-i-convert-between-tuples-and-lists">
<h3><a class="toc-backref" href="#id43" role="doc-backlink">How do I convert between tuples and lists?</a><a class="headerlink" href="#how-do-i-convert-between-tuples-and-lists" title="Link to this heading">¶</a></h3>
<p>The type constructor <code class="docutils literal notranslate"><span class="pre">tuple(seq)</span></code> converts any sequence (actually, any
iterable) into a tuple with the same items in the same order.</p>
<p>For example, <code class="docutils literal notranslate"><span class="pre">tuple([1,</span> <span class="pre">2,</span> <span class="pre">3])</span></code> yields <code class="docutils literal notranslate"><span class="pre">(1,</span> <span class="pre">2,</span> <span class="pre">3)</span></code> and <code class="docutils literal notranslate"><span class="pre">tuple('abc')</span></code>
yields <code class="docutils literal notranslate"><span class="pre">('a',</span> <span class="pre">'b',</span> <span class="pre">'c')</span></code>.  If the argument is a tuple, it does not make a copy
but returns the same object, so it is cheap to call <a class="reference internal" href="../library/stdtypes.html#tuple" title="tuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">tuple()</span></code></a> when you
aren’t sure that an object is already a tuple.</p>
<p>The type constructor <code class="docutils literal notranslate"><span class="pre">list(seq)</span></code> converts any sequence or iterable into a list
with the same items in the same order.  For example, <code class="docutils literal notranslate"><span class="pre">list((1,</span> <span class="pre">2,</span> <span class="pre">3))</span></code> yields
<code class="docutils literal notranslate"><span class="pre">[1,</span> <span class="pre">2,</span> <span class="pre">3]</span></code> and <code class="docutils literal notranslate"><span class="pre">list('abc')</span></code> yields <code class="docutils literal notranslate"><span class="pre">['a',</span> <span class="pre">'b',</span> <span class="pre">'c']</span></code>.  If the argument
is a list, it makes a copy just like <code class="docutils literal notranslate"><span class="pre">seq[:]</span></code> would.</p>
</section>
<section id="what-s-a-negative-index">
<h3><a class="toc-backref" href="#id44" role="doc-backlink">What’s a negative index?</a><a class="headerlink" href="#what-s-a-negative-index" title="Link to this heading">¶</a></h3>
<p>Python sequences are indexed with positive numbers and negative numbers.  For
positive numbers 0 is the first index 1 is the second index and so forth.  For
negative indices -1 is the last index and -2 is the penultimate (next to last)
index and so forth.  Think of <code class="docutils literal notranslate"><span class="pre">seq[-n]</span></code> as the same as <code class="docutils literal notranslate"><span class="pre">seq[len(seq)-n]</span></code>.</p>
<p>Using negative indices can be very convenient.  For example <code class="docutils literal notranslate"><span class="pre">S[:-1]</span></code> is all of
the string except for its last character, which is useful for removing the
trailing newline from a string.</p>
</section>
<section id="how-do-i-iterate-over-a-sequence-in-reverse-order">
<h3><a class="toc-backref" href="#id45" role="doc-backlink">How do I iterate over a sequence in reverse order?</a><a class="headerlink" href="#how-do-i-iterate-over-a-sequence-in-reverse-order" title="Link to this heading">¶</a></h3>
<p>Use the <a class="reference internal" href="../library/functions.html#reversed" title="reversed"><code class="xref py py-func docutils literal notranslate"><span class="pre">reversed()</span></code></a> built-in function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="n">sequence</span><span class="p">):</span>
    <span class="o">...</span>  <span class="c1"># do something with x ...</span>
</pre></div>
</div>
<p>This won’t touch your original sequence, but build a new copy with reversed
order to iterate over.</p>
</section>
<section id="how-do-you-remove-duplicates-from-a-list">
<h3><a class="toc-backref" href="#id46" role="doc-backlink">How do you remove duplicates from a list?</a><a class="headerlink" href="#how-do-you-remove-duplicates-from-a-list" title="Link to this heading">¶</a></h3>
<p>See the Python Cookbook for a long discussion of many ways to do this:</p>
<blockquote>
<div><p><a class="reference external" href="https://code.activestate.com/recipes/52560/">https://code.activestate.com/recipes/52560/</a></p>
</div></blockquote>
<p>If you don’t mind reordering the list, sort it and then scan from the end of the
list, deleting duplicates as you go:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">mylist</span><span class="p">:</span>
    <span class="n">mylist</span><span class="o">.</span><span class="n">sort</span><span class="p">()</span>
    <span class="n">last</span> <span class="o">=</span> <span class="n">mylist</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">mylist</span><span class="p">)</span><span class="o">-</span><span class="mi">2</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">last</span> <span class="o">==</span> <span class="n">mylist</span><span class="p">[</span><span class="n">i</span><span class="p">]:</span>
            <span class="k">del</span> <span class="n">mylist</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">last</span> <span class="o">=</span> <span class="n">mylist</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
</pre></div>
</div>
<p>If all elements of the list may be used as set keys (i.e. they are all
<a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>) this is often faster</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">mylist</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">mylist</span><span class="p">))</span>
</pre></div>
</div>
<p>This converts the list into a set, thereby removing duplicates, and then back
into a list.</p>
</section>
<section id="how-do-you-remove-multiple-items-from-a-list">
<h3><a class="toc-backref" href="#id47" role="doc-backlink">How do you remove multiple items from a list</a><a class="headerlink" href="#how-do-you-remove-multiple-items-from-a-list" title="Link to this heading">¶</a></h3>
<p>As with removing duplicates, explicitly iterating in reverse with a
delete condition is one possibility.  However, it is easier and faster
to use slice replacement with an implicit or explicit forward iteration.
Here are three variations.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">mylist</span><span class="p">[:]</span> <span class="o">=</span> <span class="nb">filter</span><span class="p">(</span><span class="n">keep_function</span><span class="p">,</span> <span class="n">mylist</span><span class="p">)</span>
<span class="n">mylist</span><span class="p">[:]</span> <span class="o">=</span> <span class="p">(</span><span class="n">x</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">mylist</span> <span class="k">if</span> <span class="n">keep_condition</span><span class="p">)</span>
<span class="n">mylist</span><span class="p">[:]</span> <span class="o">=</span> <span class="p">[</span><span class="n">x</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">mylist</span> <span class="k">if</span> <span class="n">keep_condition</span><span class="p">]</span>
</pre></div>
</div>
<p>The list comprehension may be fastest.</p>
</section>
<section id="how-do-you-make-an-array-in-python">
<h3><a class="toc-backref" href="#id48" role="doc-backlink">How do you make an array in Python?</a><a class="headerlink" href="#how-do-you-make-an-array-in-python" title="Link to this heading">¶</a></h3>
<p>Use a list:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="s2">&quot;this&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="s2">&quot;is&quot;</span><span class="p">,</span> <span class="s2">&quot;an&quot;</span><span class="p">,</span> <span class="s2">&quot;array&quot;</span><span class="p">]</span>
</pre></div>
</div>
<p>Lists are equivalent to C or Pascal arrays in their time complexity; the primary
difference is that a Python list can contain objects of many different types.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">array</span></code> module also provides methods for creating arrays of fixed types
with compact representations, but they are slower to index than lists.  Also
note that <a class="reference external" href="https://numpy.org/">NumPy</a>
and other third party packages define array-like structures with
various characteristics as well.</p>
<p>To get Lisp-style linked lists, you can emulate <em>cons cells</em> using tuples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">lisp_list</span> <span class="o">=</span> <span class="p">(</span><span class="s2">&quot;like&quot;</span><span class="p">,</span>  <span class="p">(</span><span class="s2">&quot;this&quot;</span><span class="p">,</span>  <span class="p">(</span><span class="s2">&quot;example&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="p">)</span> <span class="p">)</span>
</pre></div>
</div>
<p>If mutability is desired, you could use lists instead of tuples.  Here the
analogue of a Lisp <em>car</em> is <code class="docutils literal notranslate"><span class="pre">lisp_list[0]</span></code> and the analogue of <em>cdr</em> is
<code class="docutils literal notranslate"><span class="pre">lisp_list[1]</span></code>.  Only do this if you’re sure you really need to, because it’s
usually a lot slower than using Python lists.</p>
</section>
<section id="how-do-i-create-a-multidimensional-list">
<span id="faq-multidimensional-list"></span><h3><a class="toc-backref" href="#id49" role="doc-backlink">How do I create a multidimensional list?</a><a class="headerlink" href="#how-do-i-create-a-multidimensional-list" title="Link to this heading">¶</a></h3>
<p>You probably tried to make a multidimensional array like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">A</span> <span class="o">=</span> <span class="p">[[</span><span class="kc">None</span><span class="p">]</span> <span class="o">*</span> <span class="mi">2</span><span class="p">]</span> <span class="o">*</span> <span class="mi">3</span>
</pre></div>
</div>
<p>This looks correct if you print it:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">A</span>
<span class="go">[[None, None], [None, None], [None, None]]</span>
</pre></div>
</div>
<p>But when you assign a value, it shows up in multiple places:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">A</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">A</span>
<span class="go">[[5, None], [5, None], [5, None]]</span>
</pre></div>
</div>
<p>The reason is that replicating a list with <code class="docutils literal notranslate"><span class="pre">*</span></code> doesn’t create copies, it only
creates references to the existing objects.  The <code class="docutils literal notranslate"><span class="pre">*3</span></code> creates a list
containing 3 references to the same list of length two.  Changes to one row will
show in all rows, which is almost certainly not what you want.</p>
<p>The suggested approach is to create a list of the desired length first and then
fill in each element with a newly created list:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">A</span> <span class="o">=</span> <span class="p">[</span><span class="kc">None</span><span class="p">]</span> <span class="o">*</span> <span class="mi">3</span>
<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">3</span><span class="p">):</span>
    <span class="n">A</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="kc">None</span><span class="p">]</span> <span class="o">*</span> <span class="mi">2</span>
</pre></div>
</div>
<p>This generates a list containing 3 different lists of length two.  You can also
use a list comprehension:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">w</span><span class="p">,</span> <span class="n">h</span> <span class="o">=</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span>
<span class="n">A</span> <span class="o">=</span> <span class="p">[[</span><span class="kc">None</span><span class="p">]</span> <span class="o">*</span> <span class="n">w</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">h</span><span class="p">)]</span>
</pre></div>
</div>
<p>Or, you can use an extension that provides a matrix datatype; <a class="reference external" href="https://numpy.org/">NumPy</a> is the best known.</p>
</section>
<section id="how-do-i-apply-a-method-or-function-to-a-sequence-of-objects">
<h3><a class="toc-backref" href="#id50" role="doc-backlink">How do I apply a method or function to a sequence of objects?</a><a class="headerlink" href="#how-do-i-apply-a-method-or-function-to-a-sequence-of-objects" title="Link to this heading">¶</a></h3>
<p>To call a method or function and accumulate the return values is a list,
a <a class="reference internal" href="../glossary.html#term-list-comprehension"><span class="xref std std-term">list comprehension</span></a> is an elegant solution:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">result</span> <span class="o">=</span> <span class="p">[</span><span class="n">obj</span><span class="o">.</span><span class="n">method</span><span class="p">()</span> <span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">mylist</span><span class="p">]</span>

<span class="n">result</span> <span class="o">=</span> <span class="p">[</span><span class="n">function</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span> <span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">mylist</span><span class="p">]</span>
</pre></div>
</div>
<p>To just run the method or function without saving the return values,
a plain <a class="reference internal" href="../reference/compound_stmts.html#for"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">for</span></code></a> loop will suffice:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">mylist</span><span class="p">:</span>
    <span class="n">obj</span><span class="o">.</span><span class="n">method</span><span class="p">()</span>

<span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">mylist</span><span class="p">:</span>
    <span class="n">function</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="why-does-a-tuple-i-item-raise-an-exception-when-the-addition-works">
<span id="faq-augmented-assignment-tuple-error"></span><h3><a class="toc-backref" href="#id51" role="doc-backlink">Why does a_tuple[i] += [‘item’] raise an exception when the addition works?</a><a class="headerlink" href="#why-does-a-tuple-i-item-raise-an-exception-when-the-addition-works" title="Link to this heading">¶</a></h3>
<p>This is because of a combination of the fact that augmented assignment
operators are <em>assignment</em> operators, and the difference between mutable and
immutable objects in Python.</p>
<p>This discussion applies in general when augmented assignment operators are
applied to elements of a tuple that point to mutable objects, but we’ll use
a <code class="docutils literal notranslate"><span class="pre">list</span></code> and <code class="docutils literal notranslate"><span class="pre">+=</span></code> as our exemplar.</p>
<p>If you wrote:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">   </span><span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">&#39;tuple&#39; object does not support item assignment</span>
</pre></div>
</div>
<p>The reason for the exception should be immediately clear: <code class="docutils literal notranslate"><span class="pre">1</span></code> is added to the
object <code class="docutils literal notranslate"><span class="pre">a_tuple[0]</span></code> points to (<code class="docutils literal notranslate"><span class="pre">1</span></code>), producing the result object, <code class="docutils literal notranslate"><span class="pre">2</span></code>,
but when we attempt to assign the result of the computation, <code class="docutils literal notranslate"><span class="pre">2</span></code>, to element
<code class="docutils literal notranslate"><span class="pre">0</span></code> of the tuple, we get an error because we can’t change what an element of
a tuple points to.</p>
<p>Under the covers, what this augmented assignment statement is doing is
approximately this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">result</span> <span class="o">=</span> <span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">result</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">&#39;tuple&#39; object does not support item assignment</span>
</pre></div>
</div>
<p>It is the assignment part of the operation that produces the error, since a
tuple is immutable.</p>
<p>When you write something like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span> <span class="o">=</span> <span class="p">([</span><span class="s1">&#39;foo&#39;</span><span class="p">],</span> <span class="s1">&#39;bar&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">+=</span> <span class="p">[</span><span class="s1">&#39;item&#39;</span><span class="p">]</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">&#39;tuple&#39; object does not support item assignment</span>
</pre></div>
</div>
<p>The exception is a bit more surprising, and even more surprising is the fact
that even though there was an error, the append worked:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">[&#39;foo&#39;, &#39;item&#39;]</span>
</pre></div>
</div>
<p>To see why this happens, you need to know that (a) if an object implements an
<a class="reference internal" href="../reference/datamodel.html#object.__iadd__" title="object.__iadd__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__iadd__()</span></code></a> magic method, it gets called when the <code class="docutils literal notranslate"><span class="pre">+=</span></code> augmented
assignment
is executed, and its return value is what gets used in the assignment statement;
and (b) for lists, <code class="xref py py-meth docutils literal notranslate"><span class="pre">__iadd__()</span></code> is equivalent to calling <code class="xref py py-meth docutils literal notranslate"><span class="pre">extend()</span></code> on the list
and returning the list.  That’s why we say that for lists, <code class="docutils literal notranslate"><span class="pre">+=</span></code> is a
“shorthand” for <code class="xref py py-meth docutils literal notranslate"><span class="pre">list.extend()</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a_list</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_list</span> <span class="o">+=</span> <span class="p">[</span><span class="mi">1</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_list</span>
<span class="go">[1]</span>
</pre></div>
</div>
<p>This is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">result</span> <span class="o">=</span> <span class="n">a_list</span><span class="o">.</span><span class="fm">__iadd__</span><span class="p">([</span><span class="mi">1</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_list</span> <span class="o">=</span> <span class="n">result</span>
</pre></div>
</div>
<p>The object pointed to by a_list has been mutated, and the pointer to the
mutated object is assigned back to <code class="docutils literal notranslate"><span class="pre">a_list</span></code>.  The end result of the
assignment is a no-op, since it is a pointer to the same object that <code class="docutils literal notranslate"><span class="pre">a_list</span></code>
was previously pointing to, but the assignment still happens.</p>
<p>Thus, in our tuple example what is happening is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">result</span> <span class="o">=</span> <span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="fm">__iadd__</span><span class="p">([</span><span class="s1">&#39;item&#39;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a_tuple</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">result</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">&#39;tuple&#39; object does not support item assignment</span>
</pre></div>
</div>
<p>The <code class="xref py py-meth docutils literal notranslate"><span class="pre">__iadd__()</span></code> succeeds, and thus the list is extended, but even though
<code class="docutils literal notranslate"><span class="pre">result</span></code> points to the same object that <code class="docutils literal notranslate"><span class="pre">a_tuple[0]</span></code> already points to,
that final assignment still results in an error, because tuples are immutable.</p>
</section>
<section id="i-want-to-do-a-complicated-sort-can-you-do-a-schwartzian-transform-in-python">
<h3><a class="toc-backref" href="#id52" role="doc-backlink">I want to do a complicated sort: can you do a Schwartzian Transform in Python?</a><a class="headerlink" href="#i-want-to-do-a-complicated-sort-can-you-do-a-schwartzian-transform-in-python" title="Link to this heading">¶</a></h3>
<p>The technique, attributed to Randal Schwartz of the Perl community, sorts the
elements of a list by a metric which maps each element to its “sort value”. In
Python, use the <code class="docutils literal notranslate"><span class="pre">key</span></code> argument for the <a class="reference internal" href="../library/stdtypes.html#list.sort" title="list.sort"><code class="xref py py-meth docutils literal notranslate"><span class="pre">list.sort()</span></code></a> method:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Isorted</span> <span class="o">=</span> <span class="n">L</span><span class="p">[:]</span>
<span class="n">Isorted</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">s</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="n">s</span><span class="p">[</span><span class="mi">10</span><span class="p">:</span><span class="mi">15</span><span class="p">]))</span>
</pre></div>
</div>
</section>
<section id="how-can-i-sort-one-list-by-values-from-another-list">
<h3><a class="toc-backref" href="#id53" role="doc-backlink">How can I sort one list by values from another list?</a><a class="headerlink" href="#how-can-i-sort-one-list-by-values-from-another-list" title="Link to this heading">¶</a></h3>
<p>Merge them into an iterator of tuples, sort the resulting list, and then pick
out the element you want.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">list1</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;what&quot;</span><span class="p">,</span> <span class="s2">&quot;I&#39;m&quot;</span><span class="p">,</span> <span class="s2">&quot;sorting&quot;</span><span class="p">,</span> <span class="s2">&quot;by&quot;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">list2</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;something&quot;</span><span class="p">,</span> <span class="s2">&quot;else&quot;</span><span class="p">,</span> <span class="s2">&quot;to&quot;</span><span class="p">,</span> <span class="s2">&quot;sort&quot;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pairs</span> <span class="o">=</span> <span class="nb">zip</span><span class="p">(</span><span class="n">list1</span><span class="p">,</span> <span class="n">list2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pairs</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">pairs</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pairs</span>
<span class="go">[(&quot;I&#39;m&quot;, &#39;else&#39;), (&#39;by&#39;, &#39;sort&#39;), (&#39;sorting&#39;, &#39;to&#39;), (&#39;what&#39;, &#39;something&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">result</span> <span class="o">=</span> <span class="p">[</span><span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">pairs</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">result</span>
<span class="go">[&#39;else&#39;, &#39;sort&#39;, &#39;to&#39;, &#39;something&#39;]</span>
</pre></div>
</div>
</section>
</section>
<section id="objects">
<h2><a class="toc-backref" href="#id54" role="doc-backlink">Objects</a><a class="headerlink" href="#objects" title="Link to this heading">¶</a></h2>
<section id="what-is-a-class">
<h3><a class="toc-backref" href="#id55" role="doc-backlink">What is a class?</a><a class="headerlink" href="#what-is-a-class" title="Link to this heading">¶</a></h3>
<p>A class is the particular object type created by executing a class statement.
Class objects are used as templates to create instance objects, which embody
both the data (attributes) and code (methods) specific to a datatype.</p>
<p>A class can be based on one or more other classes, called its base class(es). It
then inherits the attributes and methods of its base classes. This allows an
object model to be successively refined by inheritance.  You might have a
generic <code class="docutils literal notranslate"><span class="pre">Mailbox</span></code> class that provides basic accessor methods for a mailbox,
and subclasses such as <code class="docutils literal notranslate"><span class="pre">MboxMailbox</span></code>, <code class="docutils literal notranslate"><span class="pre">MaildirMailbox</span></code>, <code class="docutils literal notranslate"><span class="pre">OutlookMailbox</span></code>
that handle various specific mailbox formats.</p>
</section>
<section id="what-is-a-method">
<h3><a class="toc-backref" href="#id56" role="doc-backlink">What is a method?</a><a class="headerlink" href="#what-is-a-method" title="Link to this heading">¶</a></h3>
<p>A method is a function on some object <code class="docutils literal notranslate"><span class="pre">x</span></code> that you normally call as
<code class="docutils literal notranslate"><span class="pre">x.name(arguments...)</span></code>.  Methods are defined as functions inside the class
definition:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">meth</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">arg</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">arg</span> <span class="o">*</span> <span class="mi">2</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">attribute</span>
</pre></div>
</div>
</section>
<section id="what-is-self">
<h3><a class="toc-backref" href="#id57" role="doc-backlink">What is self?</a><a class="headerlink" href="#what-is-self" title="Link to this heading">¶</a></h3>
<p>Self is merely a conventional name for the first argument of a method.  A method
defined as <code class="docutils literal notranslate"><span class="pre">meth(self,</span> <span class="pre">a,</span> <span class="pre">b,</span> <span class="pre">c)</span></code> should be called as <code class="docutils literal notranslate"><span class="pre">x.meth(a,</span> <span class="pre">b,</span> <span class="pre">c)</span></code> for
some instance <code class="docutils literal notranslate"><span class="pre">x</span></code> of the class in which the definition occurs; the called
method will think it is called as <code class="docutils literal notranslate"><span class="pre">meth(x,</span> <span class="pre">a,</span> <span class="pre">b,</span> <span class="pre">c)</span></code>.</p>
<p>See also <a class="reference internal" href="design.html#why-self"><span class="std std-ref">Why must ‘self’ be used explicitly in method definitions and calls?</span></a>.</p>
</section>
<section id="how-do-i-check-if-an-object-is-an-instance-of-a-given-class-or-of-a-subclass-of-it">
<h3><a class="toc-backref" href="#id58" role="doc-backlink">How do I check if an object is an instance of a given class or of a subclass of it?</a><a class="headerlink" href="#how-do-i-check-if-an-object-is-an-instance-of-a-given-class-or-of-a-subclass-of-it" title="Link to this heading">¶</a></h3>
<p>Use the built-in function <a class="reference internal" href="../library/functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance(obj,</span> <span class="pre">cls)</span></code></a>.  You can
check if an object
is an instance of any of a number of classes by providing a tuple instead of a
single class, e.g. <code class="docutils literal notranslate"><span class="pre">isinstance(obj,</span> <span class="pre">(class1,</span> <span class="pre">class2,</span> <span class="pre">...))</span></code>, and can also
check whether an object is one of Python’s built-in types, e.g.
<code class="docutils literal notranslate"><span class="pre">isinstance(obj,</span> <span class="pre">str)</span></code> or <code class="docutils literal notranslate"><span class="pre">isinstance(obj,</span> <span class="pre">(int,</span> <span class="pre">float,</span> <span class="pre">complex))</span></code>.</p>
<p>Note that <a class="reference internal" href="../library/functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> also checks for virtual inheritance from an
<a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base class</span></a>.  So, the test will return <code class="docutils literal notranslate"><span class="pre">True</span></code> for a
registered class even if hasn’t directly or indirectly inherited from it.  To
test for “true inheritance”, scan the <a class="reference internal" href="../glossary.html#term-MRO"><span class="xref std std-term">MRO</span></a> of the class:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections.abc</span> <span class="kn">import</span> <span class="n">Mapping</span>

<span class="k">class</span> <span class="nc">P</span><span class="p">:</span>
     <span class="k">pass</span>

<span class="k">class</span> <span class="nc">C</span><span class="p">(</span><span class="n">P</span><span class="p">):</span>
    <span class="k">pass</span>

<span class="n">Mapping</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">P</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">C</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">C</span><span class="p">)</span>        <span class="c1"># direct</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">P</span><span class="p">)</span>        <span class="c1"># indirect</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">Mapping</span><span class="p">)</span>  <span class="c1"># virtual</span>
<span class="go">True</span>

<span class="go"># Actual inheritance chain</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">c</span><span class="p">)</span><span class="o">.</span><span class="vm">__mro__</span>
<span class="go">(&lt;class &#39;C&#39;&gt;, &lt;class &#39;P&#39;&gt;, &lt;class &#39;object&#39;&gt;)</span>

<span class="go"># Test for &quot;true inheritance&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Mapping</span> <span class="ow">in</span> <span class="nb">type</span><span class="p">(</span><span class="n">c</span><span class="p">)</span><span class="o">.</span><span class="vm">__mro__</span>
<span class="go">False</span>
</pre></div>
</div>
<p>Note that most programs do not use <a class="reference internal" href="../library/functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> on user-defined classes
very often.  If you are developing the classes yourself, a more proper
object-oriented style is to define methods on the classes that encapsulate a
particular behaviour, instead of checking the object’s class and doing a
different thing based on what class it is.  For example, if you have a function
that does something:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">search</span><span class="p">(</span><span class="n">obj</span><span class="p">):</span>
    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="n">Mailbox</span><span class="p">):</span>
        <span class="o">...</span>  <span class="c1"># code to search a mailbox</span>
    <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="n">Document</span><span class="p">):</span>
        <span class="o">...</span>  <span class="c1"># code to search a document</span>
    <span class="k">elif</span> <span class="o">...</span>
</pre></div>
</div>
<p>A better approach is to define a <code class="docutils literal notranslate"><span class="pre">search()</span></code> method on all the classes and just
call it:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Mailbox</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">search</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="o">...</span>  <span class="c1"># code to search a mailbox</span>

<span class="k">class</span> <span class="nc">Document</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">search</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="o">...</span>  <span class="c1"># code to search a document</span>

<span class="n">obj</span><span class="o">.</span><span class="n">search</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="what-is-delegation">
<h3><a class="toc-backref" href="#id59" role="doc-backlink">What is delegation?</a><a class="headerlink" href="#what-is-delegation" title="Link to this heading">¶</a></h3>
<p>Delegation is an object oriented technique (also called a design pattern).
Let’s say you have an object <code class="docutils literal notranslate"><span class="pre">x</span></code> and want to change the behaviour of just one
of its methods.  You can create a new class that provides a new implementation
of the method you’re interested in changing and delegates all other methods to
the corresponding method of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
<p>Python programmers can easily implement delegation.  For example, the following
class implements a class that behaves like a file but converts all written data
to uppercase:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">UpperOut</span><span class="p">:</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">outfile</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_outfile</span> <span class="o">=</span> <span class="n">outfile</span>

    <span class="k">def</span> <span class="nf">write</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">s</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_outfile</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">upper</span><span class="p">())</span>

    <span class="k">def</span> <span class="fm">__getattr__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">):</span>
        <span class="k">return</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_outfile</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span>
</pre></div>
</div>
<p>Here the <code class="docutils literal notranslate"><span class="pre">UpperOut</span></code> class redefines the <code class="docutils literal notranslate"><span class="pre">write()</span></code> method to convert the
argument string to uppercase before calling the underlying
<code class="docutils literal notranslate"><span class="pre">self._outfile.write()</span></code> method.  All other methods are delegated to the
underlying <code class="docutils literal notranslate"><span class="pre">self._outfile</span></code> object.  The delegation is accomplished via the
<a class="reference internal" href="../reference/datamodel.html#object.__getattr__" title="object.__getattr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattr__()</span></code></a> method; consult <a class="reference internal" href="../reference/datamodel.html#attribute-access"><span class="std std-ref">the language reference</span></a>
for more information about controlling attribute access.</p>
<p>Note that for more general cases delegation can get trickier. When attributes
must be set as well as retrieved, the class must define a <a class="reference internal" href="../reference/datamodel.html#object.__setattr__" title="object.__setattr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__setattr__()</span></code></a>
method too, and it must do so carefully.  The basic implementation of
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__setattr__()</span></code> is roughly equivalent to the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">X</span><span class="p">:</span>
    <span class="o">...</span>
    <span class="k">def</span> <span class="fm">__setattr__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="vm">__dict__</span><span class="p">[</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Most <code class="xref py py-meth docutils literal notranslate"><span class="pre">__setattr__()</span></code> implementations must modify
<a class="reference internal" href="../library/stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">self.__dict__</span></code></a> to store
local state for self without causing an infinite recursion.</p>
</section>
<section id="how-do-i-call-a-method-defined-in-a-base-class-from-a-derived-class-that-extends-it">
<h3><a class="toc-backref" href="#id60" role="doc-backlink">How do I call a method defined in a base class from a derived class that extends it?</a><a class="headerlink" href="#how-do-i-call-a-method-defined-in-a-base-class-from-a-derived-class-that-extends-it" title="Link to this heading">¶</a></h3>
<p>Use the built-in <a class="reference internal" href="../library/functions.html#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Derived</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">meth</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">meth</span><span class="p">()</span>  <span class="c1"># calls Base.meth</span>
</pre></div>
</div>
<p>In the example, <a class="reference internal" href="../library/functions.html#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> will automatically determine the instance from
which it was called (the <code class="docutils literal notranslate"><span class="pre">self</span></code> value), look up the <a class="reference internal" href="../glossary.html#term-method-resolution-order"><span class="xref std std-term">method resolution
order</span></a> (MRO) with <code class="docutils literal notranslate"><span class="pre">type(self).__mro__</span></code>, and return the next in line after
<code class="docutils literal notranslate"><span class="pre">Derived</span></code> in the MRO: <code class="docutils literal notranslate"><span class="pre">Base</span></code>.</p>
</section>
<section id="how-can-i-organize-my-code-to-make-it-easier-to-change-the-base-class">
<h3><a class="toc-backref" href="#id61" role="doc-backlink">How can I organize my code to make it easier to change the base class?</a><a class="headerlink" href="#how-can-i-organize-my-code-to-make-it-easier-to-change-the-base-class" title="Link to this heading">¶</a></h3>
<p>You could assign the base class to an alias and derive from the alias.  Then all
you have to change is the value assigned to the alias.  Incidentally, this trick
is also handy if you want to decide dynamically (e.g. depending on availability
of resources) which base class to use.  Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Base</span><span class="p">:</span>
    <span class="o">...</span>

<span class="n">BaseAlias</span> <span class="o">=</span> <span class="n">Base</span>

<span class="k">class</span> <span class="nc">Derived</span><span class="p">(</span><span class="n">BaseAlias</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
</section>
<section id="how-do-i-create-static-class-data-and-static-class-methods">
<h3><a class="toc-backref" href="#id62" role="doc-backlink">How do I create static class data and static class methods?</a><a class="headerlink" href="#how-do-i-create-static-class-data-and-static-class-methods" title="Link to this heading">¶</a></h3>
<p>Both static data and static methods (in the sense of C++ or Java) are supported
in Python.</p>
<p>For static data, simply define a class attribute.  To assign a new value to the
attribute, you have to explicitly use the class name in the assignment:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="n">count</span> <span class="o">=</span> <span class="mi">0</span>   <span class="c1"># number of times C.__init__ called</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">C</span><span class="o">.</span><span class="n">count</span> <span class="o">=</span> <span class="n">C</span><span class="o">.</span><span class="n">count</span> <span class="o">+</span> <span class="mi">1</span>

    <span class="k">def</span> <span class="nf">getcount</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">C</span><span class="o">.</span><span class="n">count</span>  <span class="c1"># or return self.count</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">c.count</span></code> also refers to <code class="docutils literal notranslate"><span class="pre">C.count</span></code> for any <code class="docutils literal notranslate"><span class="pre">c</span></code> such that <code class="docutils literal notranslate"><span class="pre">isinstance(c,</span>
<span class="pre">C)</span></code> holds, unless overridden by <code class="docutils literal notranslate"><span class="pre">c</span></code> itself or by some class on the base-class
search path from <code class="docutils literal notranslate"><span class="pre">c.__class__</span></code> back to <code class="docutils literal notranslate"><span class="pre">C</span></code>.</p>
<p>Caution: within a method of C, an assignment like <code class="docutils literal notranslate"><span class="pre">self.count</span> <span class="pre">=</span> <span class="pre">42</span></code> creates a
new and unrelated instance named “count” in <code class="docutils literal notranslate"><span class="pre">self</span></code>’s own dict.  Rebinding of a
class-static data name must always specify the class whether inside a method or
not:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">C</span><span class="o">.</span><span class="n">count</span> <span class="o">=</span> <span class="mi">314</span>
</pre></div>
</div>
<p>Static methods are possible:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">static</span><span class="p">(</span><span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">,</span> <span class="n">arg3</span><span class="p">):</span>
        <span class="c1"># No &#39;self&#39; parameter!</span>
        <span class="o">...</span>
</pre></div>
</div>
<p>However, a far more straightforward way to get the effect of a static method is
via a simple module-level function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">getcount</span><span class="p">():</span>
    <span class="k">return</span> <span class="n">C</span><span class="o">.</span><span class="n">count</span>
</pre></div>
</div>
<p>If your code is structured so as to define one class (or tightly related class
hierarchy) per module, this supplies the desired encapsulation.</p>
</section>
<section id="how-can-i-overload-constructors-or-methods-in-python">
<h3><a class="toc-backref" href="#id63" role="doc-backlink">How can I overload constructors (or methods) in Python?</a><a class="headerlink" href="#how-can-i-overload-constructors-or-methods-in-python" title="Link to this heading">¶</a></h3>
<p>This answer actually applies to all methods, but the question usually comes up
first in the context of constructors.</p>
<p>In C++ you’d write</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">class</span><span class="w"> </span><span class="n">C</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">C</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="s">&quot;No arguments</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">;</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="n">C</span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">i</span><span class="p">)</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="s">&quot;Argument is &quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="s">&quot;</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">;</span><span class="w"> </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>In Python you have to write a single constructor that catches all cases using
default arguments.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">i</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No arguments&quot;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Argument is&quot;</span><span class="p">,</span> <span class="n">i</span><span class="p">)</span>
</pre></div>
</div>
<p>This is not entirely equivalent, but close enough in practice.</p>
<p>You could also try a variable-length argument list, e.g.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>The same approach works for all method definitions.</p>
</section>
<section id="i-try-to-use-spam-and-i-get-an-error-about-someclassname-spam">
<h3><a class="toc-backref" href="#id64" role="doc-backlink">I try to use __spam and I get an error about _SomeClassName__spam.</a><a class="headerlink" href="#i-try-to-use-spam-and-i-get-an-error-about-someclassname-spam" title="Link to this heading">¶</a></h3>
<p>Variable names with double leading underscores are “mangled” to provide a simple
but effective way to define class private variables.  Any identifier of the form
<code class="docutils literal notranslate"><span class="pre">__spam</span></code> (at least two leading underscores, at most one trailing underscore)
is textually replaced with <code class="docutils literal notranslate"><span class="pre">_classname__spam</span></code>, where <code class="docutils literal notranslate"><span class="pre">classname</span></code> is the
current class name with any leading underscores stripped.</p>
<p>This doesn’t guarantee privacy: an outside user can still deliberately access
the “_classname__spam” attribute, and private values are visible in the object’s
<code class="docutils literal notranslate"><span class="pre">__dict__</span></code>.  Many Python programmers never bother to use private variable
names at all.</p>
</section>
<section id="my-class-defines-del-but-it-is-not-called-when-i-delete-the-object">
<h3><a class="toc-backref" href="#id65" role="doc-backlink">My class defines __del__ but it is not called when I delete the object.</a><a class="headerlink" href="#my-class-defines-del-but-it-is-not-called-when-i-delete-the-object" title="Link to this heading">¶</a></h3>
<p>There are several possible reasons for this.</p>
<p>The <a class="reference internal" href="../reference/simple_stmts.html#del"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">del</span></code></a> statement does not necessarily call <a class="reference internal" href="../reference/datamodel.html#object.__del__" title="object.__del__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code></a> – it simply
decrements the object’s reference count, and if this reaches zero
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code> is called.</p>
<p>If your data structures contain circular links (e.g. a tree where each child has
a parent reference and each parent has a list of children) the reference counts
will never go back to zero.  Once in a while Python runs an algorithm to detect
such cycles, but the garbage collector might run some time after the last
reference to your data structure vanishes, so your <code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code> method may be
called at an inconvenient and random time. This is inconvenient if you’re trying
to reproduce a problem. Worse, the order in which object’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code>
methods are executed is arbitrary.  You can run <a class="reference internal" href="../library/gc.html#gc.collect" title="gc.collect"><code class="xref py py-func docutils literal notranslate"><span class="pre">gc.collect()</span></code></a> to force a
collection, but there <em>are</em> pathological cases where objects will never be
collected.</p>
<p>Despite the cycle collector, it’s still a good idea to define an explicit
<code class="docutils literal notranslate"><span class="pre">close()</span></code> method on objects to be called whenever you’re done with them.  The
<code class="docutils literal notranslate"><span class="pre">close()</span></code> method can then remove attributes that refer to subobjects.  Don’t
call <code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code> directly – <code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code> should call <code class="docutils literal notranslate"><span class="pre">close()</span></code> and
<code class="docutils literal notranslate"><span class="pre">close()</span></code> should make sure that it can be called more than once for the same
object.</p>
<p>Another way to avoid cyclical references is to use the <a class="reference internal" href="../library/weakref.html#module-weakref" title="weakref: Support for weak references and weak dictionaries."><code class="xref py py-mod docutils literal notranslate"><span class="pre">weakref</span></code></a> module,
which allows you to point to objects without incrementing their reference count.
Tree data structures, for instance, should use weak references for their parent
and sibling references (if they need them!).</p>
<p>Finally, if your <code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code> method raises an exception, a warning message
is printed to <a class="reference internal" href="../library/sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>.</p>
</section>
<section id="how-do-i-get-a-list-of-all-instances-of-a-given-class">
<h3><a class="toc-backref" href="#id66" role="doc-backlink">How do I get a list of all instances of a given class?</a><a class="headerlink" href="#how-do-i-get-a-list-of-all-instances-of-a-given-class" title="Link to this heading">¶</a></h3>
<p>Python does not keep track of all instances of a class (or of a built-in type).
You can program the class’s constructor to keep track of all instances by
keeping a list of weak references to each instance.</p>
</section>
<section id="why-does-the-result-of-id-appear-to-be-not-unique">
<h3><a class="toc-backref" href="#id67" role="doc-backlink">Why does the result of <code class="docutils literal notranslate"><span class="pre">id()</span></code> appear to be not unique?</a><a class="headerlink" href="#why-does-the-result-of-id-appear-to-be-not-unique" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/functions.html#id" title="id"><code class="xref py py-func docutils literal notranslate"><span class="pre">id()</span></code></a> builtin returns an integer that is guaranteed to be unique during
the lifetime of the object.  Since in CPython, this is the object’s memory
address, it happens frequently that after an object is deleted from memory, the
next freshly created object is allocated at the same position in memory.  This
is illustrated by this example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">id</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span> 
<span class="go">13901272</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">id</span><span class="p">(</span><span class="mi">2000</span><span class="p">)</span> 
<span class="go">13901272</span>
</pre></div>
</div>
<p>The two ids belong to different integer objects that are created before, and
deleted immediately after execution of the <code class="docutils literal notranslate"><span class="pre">id()</span></code> call.  To be sure that
objects whose id you want to examine are still alive, create another reference
to the object:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="mi">1000</span><span class="p">;</span> <span class="n">b</span> <span class="o">=</span> <span class="mi">2000</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">id</span><span class="p">(</span><span class="n">a</span><span class="p">)</span> 
<span class="go">13901272</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">id</span><span class="p">(</span><span class="n">b</span><span class="p">)</span> 
<span class="go">13891296</span>
</pre></div>
</div>
</section>
<section id="when-can-i-rely-on-identity-tests-with-the-is-operator">
<h3><a class="toc-backref" href="#id68" role="doc-backlink">When can I rely on identity tests with the <em>is</em> operator?</a><a class="headerlink" href="#when-can-i-rely-on-identity-tests-with-the-is-operator" title="Link to this heading">¶</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">is</span></code> operator tests for object identity.  The test <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">is</span> <span class="pre">b</span></code> is
equivalent to <code class="docutils literal notranslate"><span class="pre">id(a)</span> <span class="pre">==</span> <span class="pre">id(b)</span></code>.</p>
<p>The most important property of an identity test is that an object is always
identical to itself, <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">is</span> <span class="pre">a</span></code> always returns <code class="docutils literal notranslate"><span class="pre">True</span></code>.  Identity tests are
usually faster than equality tests.  And unlike equality tests, identity tests
are guaranteed to return a boolean <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<p>However, identity tests can <em>only</em> be substituted for equality tests when
object identity is assured.  Generally, there are three circumstances where
identity is guaranteed:</p>
<p>1) Assignments create new names but do not change object identity.  After the
assignment <code class="docutils literal notranslate"><span class="pre">new</span> <span class="pre">=</span> <span class="pre">old</span></code>, it is guaranteed that <code class="docutils literal notranslate"><span class="pre">new</span> <span class="pre">is</span> <span class="pre">old</span></code>.</p>
<p>2) Putting an object in a container that stores object references does not
change object identity.  After the list assignment <code class="docutils literal notranslate"><span class="pre">s[0]</span> <span class="pre">=</span> <span class="pre">x</span></code>, it is
guaranteed that <code class="docutils literal notranslate"><span class="pre">s[0]</span> <span class="pre">is</span> <span class="pre">x</span></code>.</p>
<p>3) If an object is a singleton, it means that only one instance of that object
can exist.  After the assignments <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">=</span> <span class="pre">None</span></code> and <code class="docutils literal notranslate"><span class="pre">b</span> <span class="pre">=</span> <span class="pre">None</span></code>, it is
guaranteed that <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">is</span> <span class="pre">b</span></code> because <code class="docutils literal notranslate"><span class="pre">None</span></code> is a singleton.</p>
<p>In most other circumstances, identity tests are inadvisable and equality tests
are preferred.  In particular, identity tests should not be used to check
constants such as <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> and <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> which aren’t guaranteed to be
singletons:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="mi">1000</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="mi">500</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">b</span> <span class="o">+</span> <span class="mi">500</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="n">c</span>
<span class="go">False</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="s1">&#39;Python&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="s1">&#39;Py&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">b</span> <span class="o">+</span> <span class="s1">&#39;thon&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="n">c</span>
<span class="go">False</span>
</pre></div>
</div>
<p>Likewise, new instances of mutable containers are never identical:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="n">b</span>
<span class="go">False</span>
</pre></div>
</div>
<p>In the standard library code, you will see several common patterns for
correctly using identity tests:</p>
<p>1) As recommended by <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0008/"><strong>PEP 8</strong></a>, an identity test is the preferred way to check
for <code class="docutils literal notranslate"><span class="pre">None</span></code>.  This reads like plain English in code and avoids confusion with
other objects that may have boolean values that evaluate to false.</p>
<p>2) Detecting optional arguments can be tricky when <code class="docutils literal notranslate"><span class="pre">None</span></code> is a valid input
value.  In those situations, you can create a singleton sentinel object
guaranteed to be distinct from other objects.  For example, here is how
to implement a method that behaves like <a class="reference internal" href="../library/stdtypes.html#dict.pop" title="dict.pop"><code class="xref py py-meth docutils literal notranslate"><span class="pre">dict.pop()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">_sentinel</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">pop</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">_sentinel</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">key</span> <span class="ow">in</span> <span class="bp">self</span><span class="p">:</span>
        <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
        <span class="k">del</span> <span class="bp">self</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">value</span>
    <span class="k">if</span> <span class="n">default</span> <span class="ow">is</span> <span class="n">_sentinel</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">KeyError</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">default</span>
</pre></div>
</div>
<p>3) Container implementations sometimes need to augment equality tests with
identity tests.  This prevents the code from being confused by objects such as
<code class="docutils literal notranslate"><span class="pre">float('NaN')</span></code> that are not equal to themselves.</p>
<p>For example, here is the implementation of
<code class="xref py py-meth docutils literal notranslate"><span class="pre">collections.abc.Sequence.__contains__()</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="fm">__contains__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">v</span> <span class="ow">in</span> <span class="bp">self</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">v</span> <span class="ow">is</span> <span class="n">value</span> <span class="ow">or</span> <span class="n">v</span> <span class="o">==</span> <span class="n">value</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span>
    <span class="k">return</span> <span class="kc">False</span>
</pre></div>
</div>
</section>
<section id="how-can-a-subclass-control-what-data-is-stored-in-an-immutable-instance">
<h3><a class="toc-backref" href="#id69" role="doc-backlink">How can a subclass control what data is stored in an immutable instance?</a><a class="headerlink" href="#how-can-a-subclass-control-what-data-is-stored-in-an-immutable-instance" title="Link to this heading">¶</a></h3>
<p>When subclassing an immutable type, override the <a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a> method
instead of the <a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a> method.  The latter only runs <em>after</em> an
instance is created, which is too late to alter data in an immutable
instance.</p>
<p>All of these immutable classes have a different signature than their
parent class:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">date</span>

<span class="k">class</span> <span class="nc">FirstOfMonthDate</span><span class="p">(</span><span class="n">date</span><span class="p">):</span>
    <span class="s2">&quot;Always choose the first day of the month&quot;</span>
    <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">year</span><span class="p">,</span> <span class="n">month</span><span class="p">,</span> <span class="n">day</span><span class="p">):</span>
        <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">year</span><span class="p">,</span> <span class="n">month</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">NamedInt</span><span class="p">(</span><span class="nb">int</span><span class="p">):</span>
    <span class="s2">&quot;Allow text names for some numbers&quot;</span>
    <span class="n">xlat</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;zero&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s1">&#39;one&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;ten&#39;</span><span class="p">:</span> <span class="mi">10</span><span class="p">}</span>
    <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="n">value</span> <span class="o">=</span> <span class="bp">cls</span><span class="o">.</span><span class="n">xlat</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
        <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">TitleStr</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="s2">&quot;Convert str to name suitable for a URL path&quot;</span>
    <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">s</span><span class="p">):</span>
        <span class="n">s</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39; &#39;</span><span class="p">,</span> <span class="s1">&#39;-&#39;</span><span class="p">)</span>
        <span class="n">s</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="n">c</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">s</span> <span class="k">if</span> <span class="n">c</span><span class="o">.</span><span class="n">isalnum</span><span class="p">()</span> <span class="ow">or</span> <span class="n">c</span> <span class="o">==</span> <span class="s1">&#39;-&#39;</span><span class="p">])</span>
        <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">s</span><span class="p">)</span>
</pre></div>
</div>
<p>The classes can be used like this:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">FirstOfMonthDate</span><span class="p">(</span><span class="mi">2012</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">14</span><span class="p">)</span>
<span class="go">FirstOfMonthDate(2012, 2, 1)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">NamedInt</span><span class="p">(</span><span class="s1">&#39;ten&#39;</span><span class="p">)</span>
<span class="go">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">NamedInt</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
<span class="go">20</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">TitleStr</span><span class="p">(</span><span class="s1">&#39;Blog: Why Python Rocks&#39;</span><span class="p">)</span>
<span class="go">&#39;blog-why-python-rocks&#39;</span>
</pre></div>
</div>
</section>
<section id="how-do-i-cache-method-calls">
<span id="faq-cache-method-calls"></span><h3><a class="toc-backref" href="#id70" role="doc-backlink">How do I cache method calls?</a><a class="headerlink" href="#how-do-i-cache-method-calls" title="Link to this heading">¶</a></h3>
<p>The two principal tools for caching methods are
<a class="reference internal" href="../library/functools.html#functools.cached_property" title="functools.cached_property"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.cached_property()</span></code></a> and <a class="reference internal" href="../library/functools.html#functools.lru_cache" title="functools.lru_cache"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.lru_cache()</span></code></a>.  The
former stores results at the instance level and the latter at the class
level.</p>
<p>The <em>cached_property</em> approach only works with methods that do not take
any arguments.  It does not create a reference to the instance.  The
cached method result will be kept only as long as the instance is alive.</p>
<p>The advantage is that when an instance is no longer used, the cached
method result will be released right away.  The disadvantage is that if
instances accumulate, so too will the accumulated method results.  They
can grow without bound.</p>
<p>The <em>lru_cache</em> approach works with methods that have <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>
arguments.  It creates a reference to the instance unless special
efforts are made to pass in weak references.</p>
<p>The advantage of the least recently used algorithm is that the cache is
bounded by the specified <em>maxsize</em>.  The disadvantage is that instances
are kept alive until they age out of the cache or until the cache is
cleared.</p>
<p>This example shows the various techniques:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Weather</span><span class="p">:</span>
    <span class="s2">&quot;Lookup weather information on a government website&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">station_id</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_station_id</span> <span class="o">=</span> <span class="n">station_id</span>
        <span class="c1"># The _station_id is private and immutable</span>

    <span class="k">def</span> <span class="nf">current_temperature</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="s2">&quot;Latest hourly observation&quot;</span>
        <span class="c1"># Do not cache this because old results</span>
        <span class="c1"># can be out of date.</span>

    <span class="nd">@cached_property</span>
    <span class="k">def</span> <span class="nf">location</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="s2">&quot;Return the longitude/latitude coordinates of the station&quot;</span>
        <span class="c1"># Result only depends on the station_id</span>

    <span class="nd">@lru_cache</span><span class="p">(</span><span class="n">maxsize</span><span class="o">=</span><span class="mi">20</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">historic_rainfall</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">date</span><span class="p">,</span> <span class="n">units</span><span class="o">=</span><span class="s1">&#39;mm&#39;</span><span class="p">):</span>
        <span class="s2">&quot;Rainfall on a given date&quot;</span>
        <span class="c1"># Depends on the station_id, date, and units.</span>
</pre></div>
</div>
<p>The above example assumes that the <em>station_id</em> never changes.  If the
relevant instance attributes are mutable, the <em>cached_property</em> approach
can’t be made to work because it cannot detect changes to the
attributes.</p>
<p>To make the <em>lru_cache</em> approach work when the <em>station_id</em> is mutable,
the class needs to define the <a class="reference internal" href="../reference/datamodel.html#object.__eq__" title="object.__eq__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__eq__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__hash__" title="object.__hash__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__hash__()</span></code></a>
methods so that the cache can detect relevant attribute updates:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Weather</span><span class="p">:</span>
    <span class="s2">&quot;Example with a mutable station identifier&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">station_id</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">station_id</span> <span class="o">=</span> <span class="n">station_id</span>

    <span class="k">def</span> <span class="nf">change_station</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">station_id</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">station_id</span> <span class="o">=</span> <span class="n">station_id</span>

    <span class="k">def</span> <span class="fm">__eq__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">station_id</span> <span class="o">==</span> <span class="n">other</span><span class="o">.</span><span class="n">station_id</span>

    <span class="k">def</span> <span class="fm">__hash__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="nb">hash</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">station_id</span><span class="p">)</span>

    <span class="nd">@lru_cache</span><span class="p">(</span><span class="n">maxsize</span><span class="o">=</span><span class="mi">20</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">historic_rainfall</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">date</span><span class="p">,</span> <span class="n">units</span><span class="o">=</span><span class="s1">&#39;cm&#39;</span><span class="p">):</span>
        <span class="s1">&#39;Rainfall on a given date&#39;</span>
        <span class="c1"># Depends on the station_id, date, and units.</span>
</pre></div>
</div>
</section>
</section>
<section id="modules">
<h2><a class="toc-backref" href="#id71" role="doc-backlink">Modules</a><a class="headerlink" href="#modules" title="Link to this heading">¶</a></h2>
<section id="how-do-i-create-a-pyc-file">
<h3><a class="toc-backref" href="#id72" role="doc-backlink">How do I create a .pyc file?</a><a class="headerlink" href="#how-do-i-create-a-pyc-file" title="Link to this heading">¶</a></h3>
<p>When a module is imported for the first time (or when the source file has
changed since the current compiled file was created) a <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file containing
the compiled code should be created in a <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code> subdirectory of the
directory containing the <code class="docutils literal notranslate"><span class="pre">.py</span></code> file.  The <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file will have a
filename that starts with the same name as the <code class="docutils literal notranslate"><span class="pre">.py</span></code> file, and ends with
<code class="docutils literal notranslate"><span class="pre">.pyc</span></code>, with a middle component that depends on the particular <code class="docutils literal notranslate"><span class="pre">python</span></code>
binary that created it.  (See <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a> for details.)</p>
<p>One reason that a <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file may not be created is a permissions problem
with the directory containing the source file, meaning that the <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code>
subdirectory cannot be created. This can happen, for example, if you develop as
one user but run as another, such as if you are testing with a web server.</p>
<p>Unless the <span class="target" id="index-4"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDONTWRITEBYTECODE</span></code></a> environment variable is set,
creation of a .pyc file is automatic if you’re importing a module and Python
has the ability (permissions, free space, etc…) to create a <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code>
subdirectory and write the compiled module to that subdirectory.</p>
<p>Running Python on a top level script is not considered an import and no
<code class="docutils literal notranslate"><span class="pre">.pyc</span></code> will be created.  For example, if you have a top-level module
<code class="docutils literal notranslate"><span class="pre">foo.py</span></code> that imports another module <code class="docutils literal notranslate"><span class="pre">xyz.py</span></code>, when you run <code class="docutils literal notranslate"><span class="pre">foo</span></code> (by
typing <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">foo.py</span></code> as a shell command), a <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> will be created for
<code class="docutils literal notranslate"><span class="pre">xyz</span></code> because <code class="docutils literal notranslate"><span class="pre">xyz</span></code> is imported, but no <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file will be created for
<code class="docutils literal notranslate"><span class="pre">foo</span></code> since <code class="docutils literal notranslate"><span class="pre">foo.py</span></code> isn’t being imported.</p>
<p>If you need to create a <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file for <code class="docutils literal notranslate"><span class="pre">foo</span></code> – that is, to create a
<code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file for a module that is not imported – you can, using the
<a class="reference internal" href="../library/py_compile.html#module-py_compile" title="py_compile: Generate byte-code files from Python source files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code></a> and <a class="reference internal" href="../library/compileall.html#module-compileall" title="compileall: Tools for byte-compiling all Python source files in a directory tree."><code class="xref py py-mod docutils literal notranslate"><span class="pre">compileall</span></code></a> modules.</p>
<p>The <a class="reference internal" href="../library/py_compile.html#module-py_compile" title="py_compile: Generate byte-code files from Python source files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code></a> module can manually compile any module.  One way is to use
the <code class="docutils literal notranslate"><span class="pre">compile()</span></code> function in that module interactively:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">py_compile</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">py_compile</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="s1">&#39;foo.py&#39;</span><span class="p">)</span>                 
</pre></div>
</div>
<p>This will write the <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> to a <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code> subdirectory in the same
location as <code class="docutils literal notranslate"><span class="pre">foo.py</span></code> (or you can override that with the optional parameter
<code class="docutils literal notranslate"><span class="pre">cfile</span></code>).</p>
<p>You can also automatically compile all files in a directory or directories using
the <a class="reference internal" href="../library/compileall.html#module-compileall" title="compileall: Tools for byte-compiling all Python source files in a directory tree."><code class="xref py py-mod docutils literal notranslate"><span class="pre">compileall</span></code></a> module.  You can do it from the shell prompt by running
<code class="docutils literal notranslate"><span class="pre">compileall.py</span></code> and providing the path of a directory containing Python files
to compile:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">compileall</span> <span class="o">.</span>
</pre></div>
</div>
</section>
<section id="how-do-i-find-the-current-module-name">
<h3><a class="toc-backref" href="#id73" role="doc-backlink">How do I find the current module name?</a><a class="headerlink" href="#how-do-i-find-the-current-module-name" title="Link to this heading">¶</a></h3>
<p>A module can find out its own module name by looking at the predefined global
variable <code class="docutils literal notranslate"><span class="pre">__name__</span></code>.  If this has the value <code class="docutils literal notranslate"><span class="pre">'__main__'</span></code>, the program is
running as a script.  Many modules that are usually used by importing them also
provide a command-line interface or a self-test, and only execute this code
after checking <code class="docutils literal notranslate"><span class="pre">__name__</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Running test...&#39;</span><span class="p">)</span>
    <span class="o">...</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="how-can-i-have-modules-that-mutually-import-each-other">
<h3><a class="toc-backref" href="#id74" role="doc-backlink">How can I have modules that mutually import each other?</a><a class="headerlink" href="#how-can-i-have-modules-that-mutually-import-each-other" title="Link to this heading">¶</a></h3>
<p>Suppose you have the following modules:</p>
<p><code class="file docutils literal notranslate"><span class="pre">foo.py</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">bar</span> <span class="kn">import</span> <span class="n">bar_var</span>
<span class="n">foo_var</span> <span class="o">=</span> <span class="mi">1</span>
</pre></div>
</div>
<p><code class="file docutils literal notranslate"><span class="pre">bar.py</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">foo</span> <span class="kn">import</span> <span class="n">foo_var</span>
<span class="n">bar_var</span> <span class="o">=</span> <span class="mi">2</span>
</pre></div>
</div>
<p>The problem is that the interpreter will perform the following steps:</p>
<ul class="simple">
<li><p>main imports <code class="docutils literal notranslate"><span class="pre">foo</span></code></p></li>
<li><p>Empty globals for <code class="docutils literal notranslate"><span class="pre">foo</span></code> are created</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">foo</span></code> is compiled and starts executing</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">foo</span></code> imports <code class="docutils literal notranslate"><span class="pre">bar</span></code></p></li>
<li><p>Empty globals for <code class="docutils literal notranslate"><span class="pre">bar</span></code> are created</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">bar</span></code> is compiled and starts executing</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">bar</span></code> imports <code class="docutils literal notranslate"><span class="pre">foo</span></code> (which is a no-op since there already is a module named <code class="docutils literal notranslate"><span class="pre">foo</span></code>)</p></li>
<li><p>The import mechanism tries to read <code class="docutils literal notranslate"><span class="pre">foo_var</span></code> from <code class="docutils literal notranslate"><span class="pre">foo</span></code> globals, to set <code class="docutils literal notranslate"><span class="pre">bar.foo_var</span> <span class="pre">=</span> <span class="pre">foo.foo_var</span></code></p></li>
</ul>
<p>The last step fails, because Python isn’t done with interpreting <code class="docutils literal notranslate"><span class="pre">foo</span></code> yet and
the global symbol dictionary for <code class="docutils literal notranslate"><span class="pre">foo</span></code> is still empty.</p>
<p>The same thing happens when you use <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">foo</span></code>, and then try to access
<code class="docutils literal notranslate"><span class="pre">foo.foo_var</span></code> in global code.</p>
<p>There are (at least) three possible workarounds for this problem.</p>
<p>Guido van Rossum recommends avoiding all uses of <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">&lt;module&gt;</span> <span class="pre">import</span> <span class="pre">...</span></code>,
and placing all code inside functions.  Initializations of global variables and
class variables should use constants or built-in functions only.  This means
everything from an imported module is referenced as <code class="docutils literal notranslate"><span class="pre">&lt;module&gt;.&lt;name&gt;</span></code>.</p>
<p>Jim Roskind suggests performing steps in the following order in each module:</p>
<ul class="simple">
<li><p>exports (globals, functions, and classes that don’t need imported base
classes)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">import</span></code> statements</p></li>
<li><p>active code (including globals that are initialized from imported values).</p></li>
</ul>
<p>Van Rossum doesn’t like this approach much because the imports appear in a
strange place, but it does work.</p>
<p>Matthias Urlichs recommends restructuring your code so that the recursive import
is not necessary in the first place.</p>
<p>These solutions are not mutually exclusive.</p>
</section>
<section id="import-x-y-z-returns-module-x-how-do-i-get-z">
<h3><a class="toc-backref" href="#id75" role="doc-backlink">__import__(‘x.y.z’) returns &lt;module ‘x’&gt;; how do I get z?</a><a class="headerlink" href="#import-x-y-z-returns-module-x-how-do-i-get-z" title="Link to this heading">¶</a></h3>
<p>Consider using the convenience function <a class="reference internal" href="../library/importlib.html#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">import_module()</span></code></a> from
<a class="reference internal" href="../library/importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> instead:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">z</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">import_module</span><span class="p">(</span><span class="s1">&#39;x.y.z&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="when-i-edit-an-imported-module-and-reimport-it-the-changes-don-t-show-up-why-does-this-happen">
<h3><a class="toc-backref" href="#id76" role="doc-backlink">When I edit an imported module and reimport it, the changes don’t show up.  Why does this happen?</a><a class="headerlink" href="#when-i-edit-an-imported-module-and-reimport-it-the-changes-don-t-show-up-why-does-this-happen" title="Link to this heading">¶</a></h3>
<p>For reasons of efficiency as well as consistency, Python only reads the module
file on the first time a module is imported.  If it didn’t, in a program
consisting of many modules where each one imports the same basic module, the
basic module would be parsed and re-parsed many times.  To force re-reading of a
changed module, do this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">importlib</span>
<span class="kn">import</span> <span class="nn">modname</span>
<span class="n">importlib</span><span class="o">.</span><span class="n">reload</span><span class="p">(</span><span class="n">modname</span><span class="p">)</span>
</pre></div>
</div>
<p>Warning: this technique is not 100% fool-proof.  In particular, modules
containing statements like</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">modname</span> <span class="kn">import</span> <span class="n">some_objects</span>
</pre></div>
</div>
<p>will continue to work with the old version of the imported objects.  If the
module contains class definitions, existing class instances will <em>not</em> be
updated to use the new class definition.  This can result in the following
paradoxical behaviour:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">importlib</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">cls</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="bp">cls</span><span class="o">.</span><span class="n">C</span><span class="p">()</span>                <span class="c1"># Create an instance of C</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">importlib</span><span class="o">.</span><span class="n">reload</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>
<span class="go">&lt;module &#39;cls&#39; from &#39;cls.py&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="bp">cls</span><span class="o">.</span><span class="n">C</span><span class="p">)</span>       <span class="c1"># isinstance is false?!?</span>
<span class="go">False</span>
</pre></div>
</div>
<p>The nature of the problem is made clear if you print out the “identity” of the
class objects:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">hex</span><span class="p">(</span><span class="nb">id</span><span class="p">(</span><span class="n">c</span><span class="o">.</span><span class="vm">__class__</span><span class="p">))</span>
<span class="go">&#39;0x7352a0&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">hex</span><span class="p">(</span><span class="nb">id</span><span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="n">C</span><span class="p">))</span>
<span class="go">&#39;0x4198d0&#39;</span>
</pre></div>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Programming FAQ</a><ul>
<li><a class="reference internal" href="#general-questions">General Questions</a></li>
<li><a class="reference internal" href="#core-language">Core Language</a></li>
<li><a class="reference internal" href="#numbers-and-strings">Numbers and strings</a></li>
<li><a class="reference internal" href="#performance">Performance</a></li>
<li><a class="reference internal" href="#sequences-tuples-lists">Sequences (Tuples/Lists)</a></li>
<li><a class="reference internal" href="#objects">Objects</a></li>
<li><a class="reference internal" href="#modules">Modules</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="general.html"
                          title="previous chapter">General Python FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="design.html"
                          title="next chapter">Design and History FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/programming.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="design.html" title="Design and History FAQ"
             >next</a> |</li>
        <li class="right" >
          <a href="general.html" title="General Python FAQ"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Programming FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>