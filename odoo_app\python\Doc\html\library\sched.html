<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sched — Event scheduler" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sched.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/sched.py The sched module defines a class which implements a general purpose event scheduler: Example: Scheduler Objects: scheduler instances have the following methods and attribu..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/sched.py The sched module defines a class which implements a general purpose event scheduler: Example: Scheduler Objects: scheduler instances have the following methods and attribu..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sched — Event scheduler &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="queue — A synchronized queue class" href="queue.html" />
    <link rel="prev" title="subprocess — Subprocess management" href="subprocess.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sched.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code> — Event scheduler</a><ul>
<li><a class="reference internal" href="#scheduler-objects">Scheduler Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="subprocess.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> — Subprocess management</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="queue.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">queue</span></code> — A synchronized queue class</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sched.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="queue.html" title="queue — A synchronized queue class"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="subprocess.html" title="subprocess — Subprocess management"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concurrency.html" accesskey="U">Concurrent Execution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code> — Event scheduler</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sched">
<span id="sched-event-scheduler"></span><h1><a class="reference internal" href="#module-sched" title="sched: General purpose event scheduler."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code></a> — Event scheduler<a class="headerlink" href="#module-sched" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/sched.py">Lib/sched.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-sched" title="sched: General purpose event scheduler."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code></a> module defines a class which implements a general purpose event
scheduler:</p>
<dl class="py class">
<dt class="sig sig-object py" id="sched.scheduler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sched.</span></span><span class="sig-name descname"><span class="pre">scheduler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timefunc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">time.monotonic</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delayfunc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">time.sleep</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sched.scheduler" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#sched.scheduler" title="sched.scheduler"><code class="xref py py-class docutils literal notranslate"><span class="pre">scheduler</span></code></a> class defines a generic interface to scheduling events.
It needs two functions to actually deal with the “outside world” — <em>timefunc</em>
should be callable without arguments, and return  a number (the “time”, in any
units whatsoever).  The <em>delayfunc</em> function should be callable with one
argument, compatible with the output of <em>timefunc</em>, and should delay that many
time units. <em>delayfunc</em> will also be called with the argument <code class="docutils literal notranslate"><span class="pre">0</span></code> after each
event is run to allow other threads an opportunity to run in multi-threaded
applications.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>timefunc</em> and <em>delayfunc</em> parameters are optional.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="#sched.scheduler" title="sched.scheduler"><code class="xref py py-class docutils literal notranslate"><span class="pre">scheduler</span></code></a> class can be safely used in multi-threaded
environments.</p>
</div>
</dd></dl>

<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">sched</span><span class="o">,</span> <span class="nn">time</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">sched</span><span class="o">.</span><span class="n">scheduler</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">,</span> <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">print_time</span><span class="p">(</span><span class="n">a</span><span class="o">=</span><span class="s1">&#39;default&#39;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;From print_time&quot;</span><span class="p">,</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">(),</span> <span class="n">a</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">print_some_times</span><span class="p">():</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">())</span>
<span class="gp">... </span>    <span class="n">s</span><span class="o">.</span><span class="n">enter</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">print_time</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">s</span><span class="o">.</span><span class="n">enter</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="n">print_time</span><span class="p">,</span> <span class="n">argument</span><span class="o">=</span><span class="p">(</span><span class="s1">&#39;positional&#39;</span><span class="p">,))</span>
<span class="gp">... </span>    <span class="c1"># despite having higher priority, &#39;keyword&#39; runs after &#39;positional&#39; as enter() is relative</span>
<span class="gp">... </span>    <span class="n">s</span><span class="o">.</span><span class="n">enter</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">print_time</span><span class="p">,</span> <span class="n">kwargs</span><span class="o">=</span><span class="p">{</span><span class="s1">&#39;a&#39;</span><span class="p">:</span> <span class="s1">&#39;keyword&#39;</span><span class="p">})</span>
<span class="gp">... </span>    <span class="n">s</span><span class="o">.</span><span class="n">enterabs</span><span class="p">(</span><span class="mi">1_650_000_000</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="n">print_time</span><span class="p">,</span> <span class="n">argument</span><span class="o">=</span><span class="p">(</span><span class="s2">&quot;first enterabs&quot;</span><span class="p">,))</span>
<span class="gp">... </span>    <span class="n">s</span><span class="o">.</span><span class="n">enterabs</span><span class="p">(</span><span class="mi">1_650_000_000</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="n">print_time</span><span class="p">,</span> <span class="n">argument</span><span class="o">=</span><span class="p">(</span><span class="s2">&quot;second enterabs&quot;</span><span class="p">,))</span>
<span class="gp">... </span>    <span class="n">s</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">())</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">print_some_times</span><span class="p">()</span>
<span class="go">1652342830.3640375</span>
<span class="go">From print_time 1652342830.3642538 second enterabs</span>
<span class="go">From print_time 1652342830.3643398 first enterabs</span>
<span class="go">From print_time 1652342835.3694863 positional</span>
<span class="go">From print_time 1652342835.3696074 keyword</span>
<span class="go">From print_time 1652342840.369612 default</span>
<span class="go">1652342840.3697174</span>
</pre></div>
</div>
<section id="scheduler-objects">
<span id="id1"></span><h2>Scheduler Objects<a class="headerlink" href="#scheduler-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#sched.scheduler" title="sched.scheduler"><code class="xref py py-class docutils literal notranslate"><span class="pre">scheduler</span></code></a> instances have the following methods and attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="sched.scheduler.enterabs">
<span class="sig-prename descclassname"><span class="pre">scheduler.</span></span><span class="sig-name descname"><span class="pre">enterabs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">priority</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">action</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">argument</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sched.scheduler.enterabs" title="Link to this definition">¶</a></dt>
<dd><p>Schedule a new event. The <em>time</em> argument should be a numeric type compatible
with the return value of the <em>timefunc</em> function passed  to the constructor.
Events scheduled for the same <em>time</em> will be executed in the order of their
<em>priority</em>. A lower number represents a higher priority.</p>
<p>Executing the event means executing <code class="docutils literal notranslate"><span class="pre">action(*argument,</span> <span class="pre">**kwargs)</span></code>.
<em>argument</em> is a sequence holding the positional arguments for <em>action</em>.
<em>kwargs</em> is a dictionary holding the keyword arguments for <em>action</em>.</p>
<p>Return value is an event which may be used for later cancellation of the event
(see <a class="reference internal" href="#sched.scheduler.cancel" title="sched.scheduler.cancel"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cancel()</span></code></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>argument</em> parameter is optional.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>kwargs</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sched.scheduler.enter">
<span class="sig-prename descclassname"><span class="pre">scheduler.</span></span><span class="sig-name descname"><span class="pre">enter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">delay</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">priority</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">action</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">argument</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sched.scheduler.enter" title="Link to this definition">¶</a></dt>
<dd><p>Schedule an event for <em>delay</em> more time units. Other than the relative time, the
other arguments, the effect and the return value are the same as those for
<a class="reference internal" href="#sched.scheduler.enterabs" title="sched.scheduler.enterabs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enterabs()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>argument</em> parameter is optional.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>kwargs</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sched.scheduler.cancel">
<span class="sig-prename descclassname"><span class="pre">scheduler.</span></span><span class="sig-name descname"><span class="pre">cancel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">event</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sched.scheduler.cancel" title="Link to this definition">¶</a></dt>
<dd><p>Remove the event from the queue. If <em>event</em> is not an event currently in the
queue, this method will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sched.scheduler.empty">
<span class="sig-prename descclassname"><span class="pre">scheduler.</span></span><span class="sig-name descname"><span class="pre">empty</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sched.scheduler.empty" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the event queue is empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sched.scheduler.run">
<span class="sig-prename descclassname"><span class="pre">scheduler.</span></span><span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">blocking</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sched.scheduler.run" title="Link to this definition">¶</a></dt>
<dd><p>Run all scheduled events. This method will wait  (using the <em>delayfunc</em>
function passed to the constructor) for the next event, then execute it and so
on until there are no more scheduled events.</p>
<p>If <em>blocking</em> is false executes the scheduled events due to expire soonest
(if any) and then return the deadline of the next scheduled call in the
scheduler (if any).</p>
<p>Either <em>action</em> or <em>delayfunc</em> can raise an exception.  In either case, the
scheduler will maintain a consistent state and propagate the exception.  If an
exception is raised by <em>action</em>, the event will not be attempted in future calls
to <a class="reference internal" href="#sched.scheduler.run" title="sched.scheduler.run"><code class="xref py py-meth docutils literal notranslate"><span class="pre">run()</span></code></a>.</p>
<p>If a sequence of events takes longer to run than the time available before the
next event, the scheduler will simply fall behind.  No events will be dropped;
the calling code is responsible for canceling  events which are no longer
pertinent.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>blocking</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sched.scheduler.queue">
<span class="sig-prename descclassname"><span class="pre">scheduler.</span></span><span class="sig-name descname"><span class="pre">queue</span></span><a class="headerlink" href="#sched.scheduler.queue" title="Link to this definition">¶</a></dt>
<dd><p>Read-only attribute returning a list of upcoming events in the order they
will be run.  Each event is shown as a <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> with the
following fields:  time, priority, action, argument, kwargs.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code> — Event scheduler</a><ul>
<li><a class="reference internal" href="#scheduler-objects">Scheduler Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="subprocess.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> — Subprocess management</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="queue.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">queue</span></code> — A synchronized queue class</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sched.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="queue.html" title="queue — A synchronized queue class"
             >next</a> |</li>
        <li class="right" >
          <a href="subprocess.html" title="subprocess — Subprocess management"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concurrency.html" >Concurrent Execution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code> — Event scheduler</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>