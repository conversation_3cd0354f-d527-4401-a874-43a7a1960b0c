<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Concrete Objects Layer" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/concrete.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The functions in this chapter are specific to certain Python object types. Passing them an object of the wrong type is not a good idea; if you receive an object from a Python program and you are no..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The functions in this chapter are specific to certain Python object types. Passing them an object of the wrong type is not a good idea; if you receive an object from a Python program and you are no..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Concrete Objects Layer &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Type Objects" href="type.html" />
    <link rel="prev" title="Old Buffer Protocol" href="objbuffer.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/concrete.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Concrete Objects Layer</a><ul>
<li><a class="reference internal" href="#fundamental-objects">Fundamental Objects</a></li>
<li><a class="reference internal" href="#numeric-objects">Numeric Objects</a></li>
<li><a class="reference internal" href="#sequence-objects">Sequence Objects</a></li>
<li><a class="reference internal" href="#container-objects">Container Objects</a></li>
<li><a class="reference internal" href="#function-objects">Function Objects</a></li>
<li><a class="reference internal" href="#other-objects">Other Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="objbuffer.html"
                          title="previous chapter">Old Buffer Protocol</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="type.html"
                          title="next chapter">Type Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/concrete.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="type.html" title="Type Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="objbuffer.html" title="Old Buffer Protocol"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Concrete Objects Layer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="concrete-objects-layer">
<span id="concrete"></span><h1>Concrete Objects Layer<a class="headerlink" href="#concrete-objects-layer" title="Link to this heading">¶</a></h1>
<p>The functions in this chapter are specific to certain Python object types.
Passing them an object of the wrong type is not a good idea; if you receive an
object from a Python program and you are not sure that it has the right type,
you must perform a type check first; for example, to check that an object is a
dictionary, use <a class="reference internal" href="dict.html#c.PyDict_Check" title="PyDict_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Check()</span></code></a>.  The chapter is structured like the
“family tree” of Python object types.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>While the functions described in this chapter carefully check the type of the
objects which are passed in, many of them do not check for <code class="docutils literal notranslate"><span class="pre">NULL</span></code> being passed
instead of a valid object.  Allowing <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to be passed in can cause memory
access violations and immediate termination of the interpreter.</p>
</div>
<section id="fundamental-objects">
<span id="fundamental"></span><h2>Fundamental Objects<a class="headerlink" href="#fundamental-objects" title="Link to this heading">¶</a></h2>
<p>This section describes Python type objects and the singleton object <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="type.html">Type Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="type.html#creating-heap-allocated-types">Creating Heap-Allocated Types</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="none.html">The <code class="docutils literal notranslate"><span class="pre">None</span></code> Object</a></li>
</ul>
</div>
</section>
<section id="numeric-objects">
<span id="numericobjects"></span><h2>Numeric Objects<a class="headerlink" href="#numeric-objects" title="Link to this heading">¶</a></h2>
<div class="toctree-wrapper compound" id="index-0">
<ul>
<li class="toctree-l1"><a class="reference internal" href="long.html">Integer Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="bool.html">Boolean Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="float.html">Floating Point Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="float.html#pack-and-unpack-functions">Pack and Unpack functions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="float.html#pack-functions">Pack functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="float.html#unpack-functions">Unpack functions</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="complex.html">Complex Number Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="complex.html#complex-numbers-as-c-structures">Complex Numbers as C Structures</a></li>
<li class="toctree-l2"><a class="reference internal" href="complex.html#complex-numbers-as-python-objects">Complex Numbers as Python Objects</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="sequence-objects">
<span id="sequenceobjects"></span><h2>Sequence Objects<a class="headerlink" href="#sequence-objects" title="Link to this heading">¶</a></h2>
<p id="index-1">Generic operations on sequence objects were discussed in the previous chapter;
this section deals with the specific kinds of sequence objects that are
intrinsic to the Python language.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="bytes.html">Bytes Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="bytearray.html">Byte Array Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="bytearray.html#type-check-macros">Type check macros</a></li>
<li class="toctree-l2"><a class="reference internal" href="bytearray.html#direct-api-functions">Direct API functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="bytearray.html#macros">Macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="unicode.html">Unicode Objects and Codecs</a><ul>
<li class="toctree-l2"><a class="reference internal" href="unicode.html#unicode-objects">Unicode Objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#unicode-type">Unicode Type</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#unicode-character-properties">Unicode Character Properties</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#creating-and-accessing-unicode-strings">Creating and accessing Unicode strings</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#locale-encoding">Locale Encoding</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#file-system-encoding">File System Encoding</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#wchar-t-support">wchar_t Support</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="unicode.html#built-in-codecs">Built-in Codecs</a><ul>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#generic-codecs">Generic Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#utf-8-codecs">UTF-8 Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#utf-32-codecs">UTF-32 Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#utf-16-codecs">UTF-16 Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#utf-7-codecs">UTF-7 Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#unicode-escape-codecs">Unicode-Escape Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#raw-unicode-escape-codecs">Raw-Unicode-Escape Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#latin-1-codecs">Latin-1 Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#ascii-codecs">ASCII Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#character-map-codecs">Character Map Codecs</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#mbcs-codecs-for-windows">MBCS codecs for Windows</a></li>
<li class="toctree-l3"><a class="reference internal" href="unicode.html#methods-slots">Methods &amp; Slots</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="unicode.html#methods-and-slot-functions">Methods and Slot Functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tuple.html">Tuple Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="tuple.html#struct-sequence-objects">Struct Sequence Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="list.html">List Objects</a></li>
</ul>
</div>
</section>
<section id="container-objects">
<span id="mapobjects"></span><h2>Container Objects<a class="headerlink" href="#container-objects" title="Link to this heading">¶</a></h2>
<div class="toctree-wrapper compound" id="index-2">
<ul>
<li class="toctree-l1"><a class="reference internal" href="dict.html">Dictionary Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="set.html">Set Objects</a></li>
</ul>
</div>
</section>
<section id="function-objects">
<span id="otherobjects"></span><h2>Function Objects<a class="headerlink" href="#function-objects" title="Link to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="function.html">Function Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="method.html">Instance Method Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="method.html#method-objects">Method Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="cell.html">Cell Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="code.html">Code Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="code.html#extra-information">Extra information</a></li>
</ul>
</div>
</section>
<section id="other-objects">
<h2>Other Objects<a class="headerlink" href="#other-objects" title="Link to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="file.html">File Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="module.html">Module Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="module.html#initializing-c-modules">Initializing C modules</a><ul>
<li class="toctree-l3"><a class="reference internal" href="module.html#single-phase-initialization">Single-phase initialization</a></li>
<li class="toctree-l3"><a class="reference internal" href="module.html#multi-phase-initialization">Multi-phase initialization</a></li>
<li class="toctree-l3"><a class="reference internal" href="module.html#low-level-module-creation-functions">Low-level module creation functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="module.html#support-functions">Support functions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="module.html#module-lookup">Module lookup</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="iterator.html">Iterator Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="descriptor.html">Descriptor Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="slice.html">Slice Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="slice.html#ellipsis-object">Ellipsis Object</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="memoryview.html">MemoryView objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="weakref.html">Weak Reference Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="capsule.html">Capsules</a></li>
<li class="toctree-l1"><a class="reference internal" href="frame.html">Frame Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="frame.html#internal-frames">Internal Frames</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="gen.html">Generator Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="coro.html">Coroutine Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="contextvars.html">Context Variables Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="datetime.html">DateTime Objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="typehints.html">Objects for Type Hinting</a></li>
</ul>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Concrete Objects Layer</a><ul>
<li><a class="reference internal" href="#fundamental-objects">Fundamental Objects</a></li>
<li><a class="reference internal" href="#numeric-objects">Numeric Objects</a></li>
<li><a class="reference internal" href="#sequence-objects">Sequence Objects</a></li>
<li><a class="reference internal" href="#container-objects">Container Objects</a></li>
<li><a class="reference internal" href="#function-objects">Function Objects</a></li>
<li><a class="reference internal" href="#other-objects">Other Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="objbuffer.html"
                          title="previous chapter">Old Buffer Protocol</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="type.html"
                          title="next chapter">Type Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/concrete.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="type.html" title="Type Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="objbuffer.html" title="Old Buffer Protocol"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Concrete Objects Layer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>