<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="logging.handlers — Logging handlers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/logging.handlers.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/logging/handlers.py Important: This page contains only reference information. For tutorials, please see Basic Tutorial, Advanced Tutorial, Logging Cookbook. The following useful ha..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/logging/handlers.py Important: This page contains only reference information. For tutorials, please see Basic Tutorial, Advanced Tutorial, Logging Cookbook. The following useful ha..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>logging.handlers — Logging handlers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="getpass — Portable password input" href="getpass.html" />
    <link rel="prev" title="logging.config — Logging configuration" href="logging.config.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/logging.handlers.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code> — Logging handlers</a><ul>
<li><a class="reference internal" href="#streamhandler">StreamHandler</a></li>
<li><a class="reference internal" href="#filehandler">FileHandler</a></li>
<li><a class="reference internal" href="#nullhandler">NullHandler</a></li>
<li><a class="reference internal" href="#watchedfilehandler">WatchedFileHandler</a></li>
<li><a class="reference internal" href="#baserotatinghandler">BaseRotatingHandler</a></li>
<li><a class="reference internal" href="#rotatingfilehandler">RotatingFileHandler</a></li>
<li><a class="reference internal" href="#timedrotatingfilehandler">TimedRotatingFileHandler</a></li>
<li><a class="reference internal" href="#sockethandler">SocketHandler</a></li>
<li><a class="reference internal" href="#datagramhandler">DatagramHandler</a></li>
<li><a class="reference internal" href="#sysloghandler">SysLogHandler</a></li>
<li><a class="reference internal" href="#nteventloghandler">NTEventLogHandler</a></li>
<li><a class="reference internal" href="#smtphandler">SMTPHandler</a></li>
<li><a class="reference internal" href="#memoryhandler">MemoryHandler</a></li>
<li><a class="reference internal" href="#httphandler">HTTPHandler</a></li>
<li><a class="reference internal" href="#queuehandler">QueueHandler</a></li>
<li><a class="reference internal" href="#queuelistener">QueueListener</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="logging.config.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code> — Logging configuration</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="getpass.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">getpass</span></code> — Portable password input</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/logging.handlers.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="getpass.html" title="getpass — Portable password input"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="logging.config.html" title="logging.config — Logging configuration"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" accesskey="U">Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code> — Logging handlers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-logging.handlers">
<span id="logging-handlers-logging-handlers"></span><h1><a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> — Logging handlers<a class="headerlink" href="#module-logging.handlers" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/logging/handlers.py">Lib/logging/handlers.py</a></p>
<aside class="sidebar">
<p class="sidebar-title">Important</p>
<p>This page contains only reference information. For tutorials,
please see</p>
<ul class="simple">
<li><p><a class="reference internal" href="../howto/logging.html#logging-basic-tutorial"><span class="std std-ref">Basic Tutorial</span></a></p></li>
<li><p><a class="reference internal" href="../howto/logging.html#logging-advanced-tutorial"><span class="std std-ref">Advanced Tutorial</span></a></p></li>
<li><p><a class="reference internal" href="../howto/logging-cookbook.html#logging-cookbook"><span class="std std-ref">Logging Cookbook</span></a></p></li>
</ul>
</aside>
<hr class="docutils" />
<p>The following useful handlers are provided in the package. Note that three of
the handlers (<a class="reference internal" href="#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a>, <a class="reference internal" href="#logging.FileHandler" title="logging.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a> and
<a class="reference internal" href="#logging.NullHandler" title="logging.NullHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullHandler</span></code></a>) are actually defined in the <a class="reference internal" href="logging.html#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> module itself,
but have been documented here along with the other handlers.</p>
<section id="streamhandler">
<span id="stream-handler"></span><h2>StreamHandler<a class="headerlink" href="#streamhandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a> class, located in the core <a class="reference internal" href="logging.html#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> package,
sends logging output to streams such as <em>sys.stdout</em>, <em>sys.stderr</em> or any
file-like object (or, more precisely, any object which supports <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code>
and <code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code> methods).</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.StreamHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">StreamHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.StreamHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a> class. If <em>stream</em> is
specified, the instance will use it for logging output; otherwise, <em>sys.stderr</em>
will be used.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.StreamHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.StreamHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>If a formatter is specified, it is used to format the record. The record
is then written to the stream followed by <a class="reference internal" href="#logging.StreamHandler.terminator" title="logging.StreamHandler.terminator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">terminator</span></code></a>. If exception information
is present, it is formatted using <a class="reference internal" href="traceback.html#traceback.print_exception" title="traceback.print_exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">traceback.print_exception()</span></code></a> and
appended to the stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.StreamHandler.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.StreamHandler.flush" title="Link to this definition">¶</a></dt>
<dd><p>Flushes the stream by calling its <a class="reference internal" href="#logging.StreamHandler.flush" title="logging.StreamHandler.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> method. Note that the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code> method is inherited from <a class="reference internal" href="logging.html#logging.Handler" title="logging.Handler"><code class="xref py py-class docutils literal notranslate"><span class="pre">Handler</span></code></a> and so
does no output, so an explicit <a class="reference internal" href="#logging.StreamHandler.flush" title="logging.StreamHandler.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> call may be needed at times.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.StreamHandler.setStream">
<span class="sig-name descname"><span class="pre">setStream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.StreamHandler.setStream" title="Link to this definition">¶</a></dt>
<dd><p>Sets the instance’s stream to the specified value, if it is different.
The old stream is flushed before the new stream is set.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stream</strong> – The stream that the handler should use.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>the old stream, if the stream was changed, or <em>None</em> if it wasn’t.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.StreamHandler.terminator">
<span class="sig-name descname"><span class="pre">terminator</span></span><a class="headerlink" href="#logging.StreamHandler.terminator" title="Link to this definition">¶</a></dt>
<dd><p>String used as the terminator when writing a formatted record to a stream.
Default value is <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>.</p>
<p>If you don’t want a newline termination, you can set the handler instance’s
<code class="docutils literal notranslate"><span class="pre">terminator</span></code> attribute to the empty string.</p>
<p>In earlier versions, the terminator was hardcoded as <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="filehandler">
<span id="file-handler"></span><h2>FileHandler<a class="headerlink" href="#filehandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.FileHandler" title="logging.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a> class, located in the core <a class="reference internal" href="logging.html#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> package,
sends logging output to a disk file.  It inherits the output functionality from
<a class="reference internal" href="#logging.StreamHandler" title="logging.StreamHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">StreamHandler</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.FileHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">FileHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'a'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.FileHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.FileHandler" title="logging.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a> class. The specified file is
opened and used as the stream for logging. If <em>mode</em> is not specified,
<code class="docutils literal notranslate"><span class="pre">'a'</span></code> is used.  If <em>encoding</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it is used to open the file
with that encoding.  If <em>delay</em> is true, then file opening is deferred until the
first call to <a class="reference internal" href="#logging.FileHandler.emit" title="logging.FileHandler.emit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code></a>. By default, the file grows indefinitely. If
<em>errors</em> is specified, it’s used to determine how encoding errors are handled.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>As well as string values, <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> objects are also accepted
for the <em>filename</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>errors</em> parameter was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.FileHandler.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.FileHandler.close" title="Link to this definition">¶</a></dt>
<dd><p>Closes the file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.FileHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.FileHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Outputs the record to the file.</p>
<p>Note that if the file was closed due to logging shutdown at exit and the file
mode is ‘w’, the record will not be emitted (see <a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=42378">bpo-42378</a>).</p>
</dd></dl>

</dd></dl>

</section>
<section id="nullhandler">
<span id="null-handler"></span><h2>NullHandler<a class="headerlink" href="#nullhandler" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>The <a class="reference internal" href="#logging.NullHandler" title="logging.NullHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullHandler</span></code></a> class, located in the core <a class="reference internal" href="logging.html#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a> package,
does not do any formatting or output. It is essentially a ‘no-op’ handler
for use by library developers.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.NullHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.</span></span><span class="sig-name descname"><span class="pre">NullHandler</span></span><a class="headerlink" href="#logging.NullHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.NullHandler" title="logging.NullHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullHandler</span></code></a> class.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.NullHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.NullHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>This method does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.NullHandler.handle">
<span class="sig-name descname"><span class="pre">handle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.NullHandler.handle" title="Link to this definition">¶</a></dt>
<dd><p>This method does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.NullHandler.createLock">
<span class="sig-name descname"><span class="pre">createLock</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.NullHandler.createLock" title="Link to this definition">¶</a></dt>
<dd><p>This method returns <code class="docutils literal notranslate"><span class="pre">None</span></code> for the lock, since there is no
underlying I/O to which access needs to be serialized.</p>
</dd></dl>

</dd></dl>

<p>See <a class="reference internal" href="../howto/logging.html#library-config"><span class="std std-ref">Configuring Logging for a Library</span></a> for more information on how to use
<a class="reference internal" href="#logging.NullHandler" title="logging.NullHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullHandler</span></code></a>.</p>
</section>
<section id="watchedfilehandler">
<span id="watched-file-handler"></span><h2>WatchedFileHandler<a class="headerlink" href="#watchedfilehandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.WatchedFileHandler" title="logging.handlers.WatchedFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">WatchedFileHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>
module, is a <code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code> which watches the file it is logging to. If
the file changes, it is closed and reopened using the file name.</p>
<p>A file change can happen because of usage of programs such as <em>newsyslog</em> and
<em>logrotate</em> which perform log file rotation. This handler, intended for use
under Unix/Linux, watches the file to see if it has changed since the last emit.
(A file is deemed to have changed if its device or inode have changed.) If the
file has changed, the old file stream is closed, and the file opened to get a
new stream.</p>
<p>This handler is not appropriate for use under Windows, because under Windows
open log files cannot be moved or renamed - logging opens the files with
exclusive locks - and so there is no need for such a handler. Furthermore,
<em>ST_INO</em> is not supported under Windows; <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">stat()</span></code></a> always returns zero
for this value.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.WatchedFileHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">WatchedFileHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'a'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.WatchedFileHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.WatchedFileHandler" title="logging.handlers.WatchedFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">WatchedFileHandler</span></code></a> class. The specified
file is opened and used as the stream for logging. If <em>mode</em> is not specified,
<code class="docutils literal notranslate"><span class="pre">'a'</span></code> is used.  If <em>encoding</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it is used to open the file
with that encoding.  If <em>delay</em> is true, then file opening is deferred until the
first call to <a class="reference internal" href="#logging.handlers.WatchedFileHandler.emit" title="logging.handlers.WatchedFileHandler.emit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code></a>.  By default, the file grows indefinitely. If
<em>errors</em> is provided, it determines how encoding errors are handled.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>As well as string values, <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> objects are also accepted
for the <em>filename</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>errors</em> parameter was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.WatchedFileHandler.reopenIfNeeded">
<span class="sig-name descname"><span class="pre">reopenIfNeeded</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.WatchedFileHandler.reopenIfNeeded" title="Link to this definition">¶</a></dt>
<dd><p>Checks to see if the file has changed.  If it has, the existing stream is
flushed and closed and the file opened again, typically as a precursor to
outputting the record to the file.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.WatchedFileHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.WatchedFileHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Outputs the record to the file, but first calls <a class="reference internal" href="#logging.handlers.WatchedFileHandler.reopenIfNeeded" title="logging.handlers.WatchedFileHandler.reopenIfNeeded"><code class="xref py py-meth docutils literal notranslate"><span class="pre">reopenIfNeeded()</span></code></a> to
reopen the file if it has changed.</p>
</dd></dl>

</dd></dl>

</section>
<section id="baserotatinghandler">
<span id="base-rotating-handler"></span><h2>BaseRotatingHandler<a class="headerlink" href="#baserotatinghandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.BaseRotatingHandler" title="logging.handlers.BaseRotatingHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseRotatingHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>
module, is the base class for the rotating file handlers,
<a class="reference internal" href="#logging.handlers.RotatingFileHandler" title="logging.handlers.RotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">RotatingFileHandler</span></code></a> and <a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler" title="logging.handlers.TimedRotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimedRotatingFileHandler</span></code></a>. You should
not need to instantiate this class, but it has attributes and methods you may
need to override.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.BaseRotatingHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">BaseRotatingHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BaseRotatingHandler" title="Link to this definition">¶</a></dt>
<dd><p>The parameters are as for <code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code>. The attributes are:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="logging.handlers.BaseRotatingHandler.namer">
<span class="sig-name descname"><span class="pre">namer</span></span><a class="headerlink" href="#logging.handlers.BaseRotatingHandler.namer" title="Link to this definition">¶</a></dt>
<dd><p>If this attribute is set to a callable, the <a class="reference internal" href="#logging.handlers.BaseRotatingHandler.rotation_filename" title="logging.handlers.BaseRotatingHandler.rotation_filename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rotation_filename()</span></code></a>
method delegates to this callable. The parameters passed to the callable
are those passed to <a class="reference internal" href="#logging.handlers.BaseRotatingHandler.rotation_filename" title="logging.handlers.BaseRotatingHandler.rotation_filename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rotation_filename()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The namer function is called quite a few times during rollover,
so it should be as simple and as fast as possible. It should also
return the same output every time for a given input, otherwise the
rollover behaviour may not work as expected.</p>
<p>It’s also worth noting that care should be taken when using a namer to
preserve certain attributes in the filename which are used during rotation.
For example, <a class="reference internal" href="#logging.handlers.RotatingFileHandler" title="logging.handlers.RotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">RotatingFileHandler</span></code></a> expects to have a set of log files
whose names contain successive integers, so that rotation works as expected,
and <a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler" title="logging.handlers.TimedRotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimedRotatingFileHandler</span></code></a> deletes old log files (based on the
<code class="docutils literal notranslate"><span class="pre">backupCount</span></code> parameter passed to the handler’s initializer) by determining
the oldest files to delete. For this to happen, the filenames should be
sortable using the date/time portion of the filename, and a namer needs to
respect this. (If a namer is wanted that doesn’t respect this scheme, it will
need to be used in a subclass of <a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler" title="logging.handlers.TimedRotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimedRotatingFileHandler</span></code></a> which
overrides the <a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler.getFilesToDelete" title="logging.handlers.TimedRotatingFileHandler.getFilesToDelete"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getFilesToDelete()</span></code></a> method to
fit in with the custom naming scheme.)</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.handlers.BaseRotatingHandler.rotator">
<span class="sig-name descname"><span class="pre">rotator</span></span><a class="headerlink" href="#logging.handlers.BaseRotatingHandler.rotator" title="Link to this definition">¶</a></dt>
<dd><p>If this attribute is set to a callable, the <a class="reference internal" href="#logging.handlers.BaseRotatingHandler.rotate" title="logging.handlers.BaseRotatingHandler.rotate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rotate()</span></code></a> method
delegates to this callable.  The parameters passed to the callable are
those passed to <a class="reference internal" href="#logging.handlers.BaseRotatingHandler.rotate" title="logging.handlers.BaseRotatingHandler.rotate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rotate()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.BaseRotatingHandler.rotation_filename">
<span class="sig-name descname"><span class="pre">rotation_filename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">default_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BaseRotatingHandler.rotation_filename" title="Link to this definition">¶</a></dt>
<dd><p>Modify the filename of a log file when rotating.</p>
<p>This is provided so that a custom filename can be provided.</p>
<p>The default implementation calls the ‘namer’ attribute of the handler,
if it’s callable, passing the default name to it. If the attribute isn’t
callable (the default is <code class="docutils literal notranslate"><span class="pre">None</span></code>), the name is returned unchanged.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>default_name</strong> – The default name for the log file.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.BaseRotatingHandler.rotate">
<span class="sig-name descname"><span class="pre">rotate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dest</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BaseRotatingHandler.rotate" title="Link to this definition">¶</a></dt>
<dd><p>When rotating, rotate the current log.</p>
<p>The default implementation calls the ‘rotator’ attribute of the handler,
if it’s callable, passing the source and dest arguments to it. If the
attribute isn’t callable (the default is <code class="docutils literal notranslate"><span class="pre">None</span></code>), the source is simply
renamed to the destination.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>source</strong> – The source filename. This is normally the base
filename, e.g. ‘test.log’.</p></li>
<li><p><strong>dest</strong> – The destination filename. This is normally
what the source is rotated to, e.g. ‘test.log.1’.</p></li>
</ul>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

</dd></dl>

<p>The reason the attributes exist is to save you having to subclass - you can use
the same callables for instances of <a class="reference internal" href="#logging.handlers.RotatingFileHandler" title="logging.handlers.RotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">RotatingFileHandler</span></code></a> and
<a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler" title="logging.handlers.TimedRotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimedRotatingFileHandler</span></code></a>. If either the namer or rotator callable
raises an exception, this will be handled in the same way as any other
exception during an <code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code> call, i.e. via the <code class="xref py py-meth docutils literal notranslate"><span class="pre">handleError()</span></code> method
of the handler.</p>
<p>If you need to make more significant changes to rotation processing, you can
override the methods.</p>
<p>For an example, see <a class="reference internal" href="../howto/logging-cookbook.html#cookbook-rotator-namer"><span class="std std-ref">Using a rotator and namer to customize log rotation processing</span></a>.</p>
</section>
<section id="rotatingfilehandler">
<span id="rotating-file-handler"></span><h2>RotatingFileHandler<a class="headerlink" href="#rotatingfilehandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.RotatingFileHandler" title="logging.handlers.RotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">RotatingFileHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>
module, supports rotation of disk log files.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.RotatingFileHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">RotatingFileHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'a'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxBytes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backupCount</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.RotatingFileHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.RotatingFileHandler" title="logging.handlers.RotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">RotatingFileHandler</span></code></a> class. The specified
file is opened and used as the stream for logging. If <em>mode</em> is not specified,
<code class="docutils literal notranslate"><span class="pre">'a'</span></code> is used.  If <em>encoding</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it is used to open the file
with that encoding.  If <em>delay</em> is true, then file opening is deferred until the
first call to <a class="reference internal" href="#logging.handlers.RotatingFileHandler.emit" title="logging.handlers.RotatingFileHandler.emit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code></a>.  By default, the file grows indefinitely. If
<em>errors</em> is provided, it determines how encoding errors are handled.</p>
<p>You can use the <em>maxBytes</em> and <em>backupCount</em> values to allow the file to
<em class="dfn">rollover</em> at a predetermined size. When the size is about to be exceeded,
the file is closed and a new file is silently opened for output. Rollover occurs
whenever the current log file is nearly <em>maxBytes</em> in length; but if either of
<em>maxBytes</em> or <em>backupCount</em> is zero, rollover never occurs, so you generally want
to set <em>backupCount</em> to at least 1, and have a non-zero <em>maxBytes</em>.
When <em>backupCount</em> is non-zero, the system will save old log files by appending
the extensions ‘.1’, ‘.2’ etc., to the filename. For example, with a <em>backupCount</em>
of 5 and a base file name of <code class="file docutils literal notranslate"><span class="pre">app.log</span></code>, you would get <code class="file docutils literal notranslate"><span class="pre">app.log</span></code>,
<code class="file docutils literal notranslate"><span class="pre">app.log.1</span></code>, <code class="file docutils literal notranslate"><span class="pre">app.log.2</span></code>, up to <code class="file docutils literal notranslate"><span class="pre">app.log.5</span></code>. The file being
written to is always <code class="file docutils literal notranslate"><span class="pre">app.log</span></code>.  When this file is filled, it is closed
and renamed to <code class="file docutils literal notranslate"><span class="pre">app.log.1</span></code>, and if files <code class="file docutils literal notranslate"><span class="pre">app.log.1</span></code>,
<code class="file docutils literal notranslate"><span class="pre">app.log.2</span></code>, etc. exist, then they are renamed to <code class="file docutils literal notranslate"><span class="pre">app.log.2</span></code>,
<code class="file docutils literal notranslate"><span class="pre">app.log.3</span></code> etc. respectively.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>As well as string values, <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> objects are also accepted
for the <em>filename</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>errors</em> parameter was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.RotatingFileHandler.doRollover">
<span class="sig-name descname"><span class="pre">doRollover</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.RotatingFileHandler.doRollover" title="Link to this definition">¶</a></dt>
<dd><p>Does a rollover, as described above.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.RotatingFileHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.RotatingFileHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Outputs the record to the file, catering for rollover as described
previously.</p>
</dd></dl>

</dd></dl>

</section>
<section id="timedrotatingfilehandler">
<span id="timed-rotating-file-handler"></span><h2>TimedRotatingFileHandler<a class="headerlink" href="#timedrotatingfilehandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler" title="logging.handlers.TimedRotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimedRotatingFileHandler</span></code></a> class, located in the
<a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module, supports rotation of disk log files at certain
timed intervals.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.TimedRotatingFileHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">TimedRotatingFileHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">when</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'h'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">interval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backupCount</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">utc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">atTime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.TimedRotatingFileHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler" title="logging.handlers.TimedRotatingFileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimedRotatingFileHandler</span></code></a> class. The
specified file is opened and used as the stream for logging. On rotating it also
sets the filename suffix. Rotating happens based on the product of <em>when</em> and
<em>interval</em>.</p>
<p>You can use the <em>when</em> to specify the type of <em>interval</em>. The list of possible
values is below.  Note that they are not case sensitive.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Value</p></th>
<th class="head"><p>Type of interval</p></th>
<th class="head"><p>If/how <em>atTime</em> is used</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'S'</span></code></p></td>
<td><p>Seconds</p></td>
<td><p>Ignored</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'M'</span></code></p></td>
<td><p>Minutes</p></td>
<td><p>Ignored</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'H'</span></code></p></td>
<td><p>Hours</p></td>
<td><p>Ignored</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'D'</span></code></p></td>
<td><p>Days</p></td>
<td><p>Ignored</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'W0'-'W6'</span></code></p></td>
<td><p>Weekday (0=Monday)</p></td>
<td><p>Used to compute initial
rollover time</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'midnight'</span></code></p></td>
<td><p>Roll over at midnight, if
<em>atTime</em> not specified,
else at time <em>atTime</em></p></td>
<td><p>Used to compute initial
rollover time</p></td>
</tr>
</tbody>
</table>
<p>When using weekday-based rotation, specify ‘W0’ for Monday, ‘W1’ for
Tuesday, and so on up to ‘W6’ for Sunday. In this case, the value passed for
<em>interval</em> isn’t used.</p>
<p>The system will save old log files by appending extensions to the filename.
The extensions are date-and-time based, using the strftime format
<code class="docutils literal notranslate"><span class="pre">%Y-%m-%d_%H-%M-%S</span></code> or a leading portion thereof, depending on the
rollover interval.</p>
<p>When computing the next rollover time for the first time (when the handler
is created), the last modification time of an existing log file, or else
the current time, is used to compute when the next rotation will occur.</p>
<p>If the <em>utc</em> argument is true, times in UTC will be used; otherwise
local time is used.</p>
<p>If <em>backupCount</em> is nonzero, at most <em>backupCount</em> files
will be kept, and if more would be created when rollover occurs, the oldest
one is deleted. The deletion logic uses the interval to determine which
files to delete, so changing the interval may leave old files lying around.</p>
<p>If <em>delay</em> is true, then file opening is deferred until the first call to
<a class="reference internal" href="#logging.handlers.TimedRotatingFileHandler.emit" title="logging.handlers.TimedRotatingFileHandler.emit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code></a>.</p>
<p>If <em>atTime</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it must be a <code class="docutils literal notranslate"><span class="pre">datetime.time</span></code> instance which
specifies the time of day when rollover occurs, for the cases where rollover
is set to happen “at midnight” or “on a particular weekday”. Note that in
these cases, the <em>atTime</em> value is effectively used to compute the <em>initial</em>
rollover, and subsequent rollovers would be calculated via the normal
interval calculation.</p>
<p>If <em>errors</em> is specified, it’s used to determine how encoding errors are
handled.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calculation of the initial rollover time is done when the handler
is initialised. Calculation of subsequent rollover times is done only
when rollover occurs, and rollover occurs only when emitting output. If
this is not kept in mind, it might lead to some confusion. For example,
if an interval of “every minute” is set, that does not mean you will
always see log files with times (in the filename) separated by a minute;
if, during application execution, logging output is generated more
frequently than once a minute, <em>then</em> you can expect to see log files
with times separated by a minute. If, on the other hand, logging messages
are only output once every five minutes (say), then there will be gaps in
the file times corresponding to the minutes where no output (and hence no
rollover) occurred.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><em>atTime</em> parameter was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>As well as string values, <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> objects are also accepted
for the <em>filename</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>errors</em> parameter was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.TimedRotatingFileHandler.doRollover">
<span class="sig-name descname"><span class="pre">doRollover</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.TimedRotatingFileHandler.doRollover" title="Link to this definition">¶</a></dt>
<dd><p>Does a rollover, as described above.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.TimedRotatingFileHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.TimedRotatingFileHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Outputs the record to the file, catering for rollover as described above.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.TimedRotatingFileHandler.getFilesToDelete">
<span class="sig-name descname"><span class="pre">getFilesToDelete</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.TimedRotatingFileHandler.getFilesToDelete" title="Link to this definition">¶</a></dt>
<dd><p>Returns a list of filenames which should be deleted as part of rollover. These
are the absolute paths of the oldest backup log files written by the handler.</p>
</dd></dl>

</dd></dl>

</section>
<section id="sockethandler">
<span id="socket-handler"></span><h2>SocketHandler<a class="headerlink" href="#sockethandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.SocketHandler" title="logging.handlers.SocketHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SocketHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module,
sends logging output to a network socket. The base class uses a TCP socket.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">SocketHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.SocketHandler" title="logging.handlers.SocketHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SocketHandler</span></code></a> class intended to
communicate with a remote machine whose address is given by <em>host</em> and <em>port</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>If <code class="docutils literal notranslate"><span class="pre">port</span></code> is specified as <code class="docutils literal notranslate"><span class="pre">None</span></code>, a Unix domain socket is created
using the value in <code class="docutils literal notranslate"><span class="pre">host</span></code> - otherwise, a TCP socket is created.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.close" title="Link to this definition">¶</a></dt>
<dd><p>Closes the socket.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Pickles the record’s attribute dictionary and writes it to the socket in
binary format. If there is an error with the socket, silently drops the
packet. If the connection was previously lost, re-establishes the
connection. To unpickle the record at the receiving end into a
<a class="reference internal" href="logging.html#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>, use the <a class="reference internal" href="logging.html#logging.makeLogRecord" title="logging.makeLogRecord"><code class="xref py py-func docutils literal notranslate"><span class="pre">makeLogRecord()</span></code></a>
function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.handleError">
<span class="sig-name descname"><span class="pre">handleError</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.handleError" title="Link to this definition">¶</a></dt>
<dd><p>Handles an error which has occurred during <a class="reference internal" href="#logging.handlers.SocketHandler.emit" title="logging.handlers.SocketHandler.emit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">emit()</span></code></a>. The most likely
cause is a lost connection. Closes the socket so that we can retry on the
next event.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.makeSocket">
<span class="sig-name descname"><span class="pre">makeSocket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.makeSocket" title="Link to this definition">¶</a></dt>
<dd><p>This is a factory method which allows subclasses to define the precise
type of socket they want. The default implementation creates a TCP socket
(<a class="reference internal" href="socket.html#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">socket.SOCK_STREAM</span></code></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.makePickle">
<span class="sig-name descname"><span class="pre">makePickle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.makePickle" title="Link to this definition">¶</a></dt>
<dd><p>Pickles the record’s attribute dictionary in binary format with a length
prefix, and returns it ready for transmission across the socket. The
details of this operation are equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="n">pickle</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">record_attr_dict</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">datalen</span> <span class="o">=</span> <span class="n">struct</span><span class="o">.</span><span class="n">pack</span><span class="p">(</span><span class="s1">&#39;&gt;L&#39;</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">))</span>
<span class="k">return</span> <span class="n">datalen</span> <span class="o">+</span> <span class="n">data</span>
</pre></div>
</div>
<p>Note that pickles aren’t completely secure. If you are concerned about
security, you may want to override this method to implement a more secure
mechanism. For example, you can sign pickles using HMAC and then verify
them on the receiving end, or alternatively you can disable unpickling of
global objects on the receiving end.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.send">
<span class="sig-name descname"><span class="pre">send</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">packet</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.send" title="Link to this definition">¶</a></dt>
<dd><p>Send a pickled byte-string <em>packet</em> to the socket. The format of the sent
byte-string is as described in the documentation for
<a class="reference internal" href="#logging.handlers.SocketHandler.makePickle" title="logging.handlers.SocketHandler.makePickle"><code class="xref py py-meth docutils literal notranslate"><span class="pre">makePickle()</span></code></a>.</p>
<p>This function allows for partial sends, which can happen when the network
is busy.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SocketHandler.createSocket">
<span class="sig-name descname"><span class="pre">createSocket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SocketHandler.createSocket" title="Link to this definition">¶</a></dt>
<dd><p>Tries to create a socket; on failure, uses an exponential back-off
algorithm.  On initial failure, the handler will drop the message it was
trying to send.  When subsequent messages are handled by the same
instance, it will not try connecting until some time has passed.  The
default parameters are such that the initial delay is one second, and if
after that delay the connection still can’t be made, the handler will
double the delay each time up to a maximum of 30 seconds.</p>
<p>This behaviour is controlled by the following handler attributes:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">retryStart</span></code> (initial delay, defaulting to 1.0 seconds).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">retryFactor</span></code> (multiplier, defaulting to 2.0).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">retryMax</span></code> (maximum delay, defaulting to 30.0 seconds).</p></li>
</ul>
<p>This means that if the remote listener starts up <em>after</em> the handler has
been used, you could lose messages (since the handler won’t even attempt
a connection until the delay has elapsed, but just silently drop messages
during the delay period).</p>
</dd></dl>

</dd></dl>

</section>
<section id="datagramhandler">
<span id="datagram-handler"></span><h2>DatagramHandler<a class="headerlink" href="#datagramhandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.DatagramHandler" title="logging.handlers.DatagramHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DatagramHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>
module, inherits from <a class="reference internal" href="#logging.handlers.SocketHandler" title="logging.handlers.SocketHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SocketHandler</span></code></a> to support sending logging messages
over UDP sockets.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.DatagramHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">DatagramHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.DatagramHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.DatagramHandler" title="logging.handlers.DatagramHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DatagramHandler</span></code></a> class intended to
communicate with a remote machine whose address is given by <em>host</em> and <em>port</em>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>As UDP is not a streaming protocol, there is no persistent connection
between an instance of this handler and <em>host</em>. For this reason, when using a
network socket, a DNS lookup might have to be made each time an event is
logged, which can introduce some latency into the system. If this affects you,
you can do a lookup yourself and initialize this handler using the looked-up IP
address rather than the hostname.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>If <code class="docutils literal notranslate"><span class="pre">port</span></code> is specified as <code class="docutils literal notranslate"><span class="pre">None</span></code>, a Unix domain socket is created
using the value in <code class="docutils literal notranslate"><span class="pre">host</span></code> - otherwise, a UDP socket is created.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.DatagramHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.DatagramHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Pickles the record’s attribute dictionary and writes it to the socket in
binary format. If there is an error with the socket, silently drops the
packet. To unpickle the record at the receiving end into a
<a class="reference internal" href="logging.html#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a>, use the <a class="reference internal" href="logging.html#logging.makeLogRecord" title="logging.makeLogRecord"><code class="xref py py-func docutils literal notranslate"><span class="pre">makeLogRecord()</span></code></a>
function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.DatagramHandler.makeSocket">
<span class="sig-name descname"><span class="pre">makeSocket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.DatagramHandler.makeSocket" title="Link to this definition">¶</a></dt>
<dd><p>The factory method of <a class="reference internal" href="#logging.handlers.SocketHandler" title="logging.handlers.SocketHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SocketHandler</span></code></a> is here overridden to create
a UDP socket (<a class="reference internal" href="socket.html#socket.SOCK_DGRAM" title="socket.SOCK_DGRAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">socket.SOCK_DGRAM</span></code></a>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.DatagramHandler.send">
<span class="sig-name descname"><span class="pre">send</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.DatagramHandler.send" title="Link to this definition">¶</a></dt>
<dd><p>Send a pickled byte-string to a socket. The format of the sent byte-string
is as described in the documentation for <a class="reference internal" href="#logging.handlers.SocketHandler.makePickle" title="logging.handlers.SocketHandler.makePickle"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SocketHandler.makePickle()</span></code></a>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="sysloghandler">
<span id="syslog-handler"></span><h2>SysLogHandler<a class="headerlink" href="#sysloghandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.SysLogHandler" title="logging.handlers.SysLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SysLogHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module,
supports sending logging messages to a remote or local Unix syslog.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.SysLogHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">SysLogHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('localhost',</span> <span class="pre">SYSLOG_UDP_PORT)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">facility</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">LOG_USER</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">socktype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">socket.SOCK_DGRAM</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SysLogHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.SysLogHandler" title="logging.handlers.SysLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SysLogHandler</span></code></a> class intended to
communicate with a remote Unix machine whose address is given by <em>address</em> in
the form of a <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> tuple.  If <em>address</em> is not specified,
<code class="docutils literal notranslate"><span class="pre">('localhost',</span> <span class="pre">514)</span></code> is used.  The address is used to open a socket.  An
alternative to providing a <code class="docutils literal notranslate"><span class="pre">(host,</span> <span class="pre">port)</span></code> tuple is providing an address as a
string, for example ‘/dev/log’. In this case, a Unix domain socket is used to
send the message to the syslog. If <em>facility</em> is not specified,
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_USER</span></code> is used. The type of socket opened depends on the
<em>socktype</em> argument, which defaults to <a class="reference internal" href="socket.html#socket.SOCK_DGRAM" title="socket.SOCK_DGRAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">socket.SOCK_DGRAM</span></code></a> and thus
opens a UDP socket. To open a TCP socket (for use with the newer syslog
daemons such as rsyslog), specify a value of <a class="reference internal" href="socket.html#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">socket.SOCK_STREAM</span></code></a>.</p>
<p>Note that if your server is not listening on UDP port 514,
<a class="reference internal" href="#logging.handlers.SysLogHandler" title="logging.handlers.SysLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SysLogHandler</span></code></a> may appear not to work. In that case, check what
address you should be using for a domain socket - it’s system dependent.
For example, on Linux it’s usually ‘/dev/log’ but on OS/X it’s
‘/var/run/syslog’. You’ll need to check your platform and use the
appropriate address (you may need to do this check at runtime if your
application needs to run on several platforms). On Windows, you pretty
much have to use the UDP option.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On macOS 12.x (Monterey), Apple has changed the behaviour of their
syslog daemon - it no longer listens on a domain socket. Therefore, you cannot
expect <a class="reference internal" href="#logging.handlers.SysLogHandler" title="logging.handlers.SysLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SysLogHandler</span></code></a> to work on this system.</p>
<p>See <a class="reference external" href="https://github.com/python/cpython/issues/91070">gh-91070</a> for more information.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>socktype</em> was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SysLogHandler.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SysLogHandler.close" title="Link to this definition">¶</a></dt>
<dd><p>Closes the socket to the remote host.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SysLogHandler.createSocket">
<span class="sig-name descname"><span class="pre">createSocket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SysLogHandler.createSocket" title="Link to this definition">¶</a></dt>
<dd><p>Tries to create a socket and, if it’s not a datagram socket, connect it
to the other end. This method is called during handler initialization,
but it’s not regarded as an error if the other end isn’t listening at
this point - the method will be called again when emitting an event, if
there is no socket at that point.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SysLogHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SysLogHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>The record is formatted, and then sent to the syslog server. If exception
information is present, it is <em>not</em> sent to the server.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2.1: </span>(See: <a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=12168">bpo-12168</a>.) In earlier versions, the message sent to the
syslog daemons was always terminated with a NUL byte, because early
versions of these daemons expected a NUL terminated message - even
though it’s not in the relevant specification (<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5424.html"><strong>RFC 5424</strong></a>). More recent
versions of these daemons don’t expect the NUL byte but strip it off
if it’s there, and even more recent daemons (which adhere more closely
to RFC 5424) pass the NUL byte on as part of the message.</p>
<p>To enable easier handling of syslog messages in the face of all these
differing daemon behaviours, the appending of the NUL byte has been
made configurable, through the use of a class-level attribute,
<code class="docutils literal notranslate"><span class="pre">append_nul</span></code>. This defaults to <code class="docutils literal notranslate"><span class="pre">True</span></code> (preserving the existing
behaviour) but can be set to <code class="docutils literal notranslate"><span class="pre">False</span></code> on a <code class="docutils literal notranslate"><span class="pre">SysLogHandler</span></code> instance
in order for that instance to <em>not</em> append the NUL terminator.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>(See: <a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=12419">bpo-12419</a>.) In earlier versions, there was no facility for
an “ident” or “tag” prefix to identify the source of the message. This
can now be specified using a class-level attribute, defaulting to
<code class="docutils literal notranslate"><span class="pre">&quot;&quot;</span></code> to preserve existing behaviour, but which can be overridden on
a <code class="docutils literal notranslate"><span class="pre">SysLogHandler</span></code> instance in order for that instance to prepend
the ident to every message handled. Note that the provided ident must
be text, not bytes, and is prepended to the message exactly as is.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SysLogHandler.encodePriority">
<span class="sig-name descname"><span class="pre">encodePriority</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">facility</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">priority</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SysLogHandler.encodePriority" title="Link to this definition">¶</a></dt>
<dd><p>Encodes the facility and priority into an integer. You can pass in strings
or integers - if strings are passed, internal mapping dictionaries are
used to convert them to integers.</p>
<p>The symbolic <code class="docutils literal notranslate"><span class="pre">LOG_</span></code> values are defined in <a class="reference internal" href="#logging.handlers.SysLogHandler" title="logging.handlers.SysLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SysLogHandler</span></code></a> and
mirror the values defined in the <code class="docutils literal notranslate"><span class="pre">sys/syslog.h</span></code> header file.</p>
<p><strong>Priorities</strong></p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Name (string)</p></th>
<th class="head"><p>Symbolic value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">alert</span></code></p></td>
<td><p>LOG_ALERT</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">crit</span></code> or <code class="docutils literal notranslate"><span class="pre">critical</span></code></p></td>
<td><p>LOG_CRIT</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">debug</span></code></p></td>
<td><p>LOG_DEBUG</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">emerg</span></code> or <code class="docutils literal notranslate"><span class="pre">panic</span></code></p></td>
<td><p>LOG_EMERG</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">err</span></code> or <code class="docutils literal notranslate"><span class="pre">error</span></code></p></td>
<td><p>LOG_ERR</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">info</span></code></p></td>
<td><p>LOG_INFO</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">notice</span></code></p></td>
<td><p>LOG_NOTICE</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">warn</span></code> or <code class="docutils literal notranslate"><span class="pre">warning</span></code></p></td>
<td><p>LOG_WARNING</p></td>
</tr>
</tbody>
</table>
<p><strong>Facilities</strong></p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Name (string)</p></th>
<th class="head"><p>Symbolic value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">auth</span></code></p></td>
<td><p>LOG_AUTH</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">authpriv</span></code></p></td>
<td><p>LOG_AUTHPRIV</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cron</span></code></p></td>
<td><p>LOG_CRON</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">daemon</span></code></p></td>
<td><p>LOG_DAEMON</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ftp</span></code></p></td>
<td><p>LOG_FTP</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kern</span></code></p></td>
<td><p>LOG_KERN</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">lpr</span></code></p></td>
<td><p>LOG_LPR</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mail</span></code></p></td>
<td><p>LOG_MAIL</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">news</span></code></p></td>
<td><p>LOG_NEWS</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">syslog</span></code></p></td>
<td><p>LOG_SYSLOG</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">user</span></code></p></td>
<td><p>LOG_USER</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">uucp</span></code></p></td>
<td><p>LOG_UUCP</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">local0</span></code></p></td>
<td><p>LOG_LOCAL0</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">local1</span></code></p></td>
<td><p>LOG_LOCAL1</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">local2</span></code></p></td>
<td><p>LOG_LOCAL2</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">local3</span></code></p></td>
<td><p>LOG_LOCAL3</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">local4</span></code></p></td>
<td><p>LOG_LOCAL4</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">local5</span></code></p></td>
<td><p>LOG_LOCAL5</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">local6</span></code></p></td>
<td><p>LOG_LOCAL6</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">local7</span></code></p></td>
<td><p>LOG_LOCAL7</p></td>
</tr>
</tbody>
</table>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SysLogHandler.mapPriority">
<span class="sig-name descname"><span class="pre">mapPriority</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">levelname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SysLogHandler.mapPriority" title="Link to this definition">¶</a></dt>
<dd><p>Maps a logging level name to a syslog priority name.
You may need to override this if you are using custom levels, or
if the default algorithm is not suitable for your needs. The
default algorithm maps <code class="docutils literal notranslate"><span class="pre">DEBUG</span></code>, <code class="docutils literal notranslate"><span class="pre">INFO</span></code>, <code class="docutils literal notranslate"><span class="pre">WARNING</span></code>, <code class="docutils literal notranslate"><span class="pre">ERROR</span></code> and
<code class="docutils literal notranslate"><span class="pre">CRITICAL</span></code> to the equivalent syslog names, and all other level
names to ‘warning’.</p>
</dd></dl>

</dd></dl>

</section>
<section id="nteventloghandler">
<span id="nt-eventlog-handler"></span><h2>NTEventLogHandler<a class="headerlink" href="#nteventloghandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.NTEventLogHandler" title="logging.handlers.NTEventLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">NTEventLogHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>
module, supports sending logging messages to a local Windows NT, Windows 2000 or
Windows XP event log. Before you can use it, you need Mark Hammond’s Win32
extensions for Python installed.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.NTEventLogHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">NTEventLogHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">appname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dllname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">logtype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'Application'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.NTEventLogHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.NTEventLogHandler" title="logging.handlers.NTEventLogHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">NTEventLogHandler</span></code></a> class. The <em>appname</em> is
used to define the application name as it appears in the event log. An
appropriate registry entry is created using this name. The <em>dllname</em> should give
the fully qualified pathname of a .dll or .exe which contains message
definitions to hold in the log (if not specified, <code class="docutils literal notranslate"><span class="pre">'win32service.pyd'</span></code> is used
- this is installed with the Win32 extensions and contains some basic
placeholder message definitions. Note that use of these placeholders will make
your event logs big, as the entire message source is held in the log. If you
want slimmer logs, you have to pass in the name of your own .dll or .exe which
contains the message definitions you want to use in the event log). The
<em>logtype</em> is one of <code class="docutils literal notranslate"><span class="pre">'Application'</span></code>, <code class="docutils literal notranslate"><span class="pre">'System'</span></code> or <code class="docutils literal notranslate"><span class="pre">'Security'</span></code>, and
defaults to <code class="docutils literal notranslate"><span class="pre">'Application'</span></code>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.NTEventLogHandler.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.NTEventLogHandler.close" title="Link to this definition">¶</a></dt>
<dd><p>At this point, you can remove the application name from the registry as a
source of event log entries. However, if you do this, you will not be able
to see the events as you intended in the Event Log Viewer - it needs to be
able to access the registry to get the .dll name. The current version does
not do this.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.NTEventLogHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.NTEventLogHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Determines the message ID, event category and event type, and then logs
the message in the NT event log.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.NTEventLogHandler.getEventCategory">
<span class="sig-name descname"><span class="pre">getEventCategory</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.NTEventLogHandler.getEventCategory" title="Link to this definition">¶</a></dt>
<dd><p>Returns the event category for the record. Override this if you want to
specify your own categories. This version returns 0.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.NTEventLogHandler.getEventType">
<span class="sig-name descname"><span class="pre">getEventType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.NTEventLogHandler.getEventType" title="Link to this definition">¶</a></dt>
<dd><p>Returns the event type for the record. Override this if you want to
specify your own types. This version does a mapping using the handler’s
typemap attribute, which is set up in <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> to a dictionary
which contains mappings for <code class="xref py py-const docutils literal notranslate"><span class="pre">DEBUG</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">INFO</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">WARNING</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">CRITICAL</span></code>. If you are using
your own levels, you will either need to override this method or place a
suitable dictionary in the handler’s <em>typemap</em> attribute.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.NTEventLogHandler.getMessageID">
<span class="sig-name descname"><span class="pre">getMessageID</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.NTEventLogHandler.getMessageID" title="Link to this definition">¶</a></dt>
<dd><p>Returns the message ID for the record. If you are using your own messages,
you could do this by having the <em>msg</em> passed to the logger being an ID
rather than a format string. Then, in here, you could use a dictionary
lookup to get the message ID. This version returns 1, which is the base
message ID in <code class="file docutils literal notranslate"><span class="pre">win32service.pyd</span></code>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="smtphandler">
<span id="smtp-handler"></span><h2>SMTPHandler<a class="headerlink" href="#smtphandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.SMTPHandler" title="logging.handlers.SMTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTPHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module,
supports sending logging messages to an email address via SMTP.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.SMTPHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">SMTPHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailhost</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fromaddr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">toaddrs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subject</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">credentials</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">secure</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1.0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SMTPHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.SMTPHandler" title="logging.handlers.SMTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTPHandler</span></code></a> class. The instance is
initialized with the from and to addresses and subject line of the email. The
<em>toaddrs</em> should be a list of strings. To specify a non-standard SMTP port, use
the (host, port) tuple format for the <em>mailhost</em> argument. If you use a string,
the standard SMTP port is used. If your SMTP server requires authentication, you
can specify a (username, password) tuple for the <em>credentials</em> argument.</p>
<p>To specify the use of a secure protocol (TLS), pass in a tuple to the
<em>secure</em> argument. This will only be used when authentication credentials are
supplied. The tuple should be either an empty tuple, or a single-value tuple
with the name of a keyfile, or a 2-value tuple with the names of the keyfile
and certificate file. (This tuple is passed to the
<a class="reference internal" href="smtplib.html#smtplib.SMTP.starttls" title="smtplib.SMTP.starttls"><code class="xref py py-meth docutils literal notranslate"><span class="pre">smtplib.SMTP.starttls()</span></code></a> method.)</p>
<p>A timeout can be specified for communication with the SMTP server using the
<em>timeout</em> argument.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>timeout</em> parameter.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SMTPHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SMTPHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Formats the record and sends it to the specified addressees.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.SMTPHandler.getSubject">
<span class="sig-name descname"><span class="pre">getSubject</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.SMTPHandler.getSubject" title="Link to this definition">¶</a></dt>
<dd><p>If you want to specify a subject line which is record-dependent, override
this method.</p>
</dd></dl>

</dd></dl>

</section>
<section id="memoryhandler">
<span id="memory-handler"></span><h2>MemoryHandler<a class="headerlink" href="#memoryhandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.MemoryHandler" title="logging.handlers.MemoryHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">MemoryHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module,
supports buffering of logging records in memory, periodically flushing them to a
<em class="dfn">target</em> handler. Flushing occurs whenever the buffer is full, or when an
event of a certain severity or greater is seen.</p>
<p><a class="reference internal" href="#logging.handlers.MemoryHandler" title="logging.handlers.MemoryHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">MemoryHandler</span></code></a> is a subclass of the more general
<a class="reference internal" href="#logging.handlers.BufferingHandler" title="logging.handlers.BufferingHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferingHandler</span></code></a>, which is an abstract class. This buffers logging
records in memory. Whenever each record is added to the buffer, a check is made
by calling <code class="xref py py-meth docutils literal notranslate"><span class="pre">shouldFlush()</span></code> to see if the buffer should be flushed.  If it
should, then <code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code> is expected to do the flushing.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.BufferingHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">BufferingHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">capacity</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BufferingHandler" title="Link to this definition">¶</a></dt>
<dd><p>Initializes the handler with a buffer of the specified capacity. Here,
<em>capacity</em> means the number of logging records buffered.</p>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.BufferingHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BufferingHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Append the record to the buffer. If <a class="reference internal" href="#logging.handlers.BufferingHandler.shouldFlush" title="logging.handlers.BufferingHandler.shouldFlush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shouldFlush()</span></code></a> returns true,
call <a class="reference internal" href="#logging.handlers.BufferingHandler.flush" title="logging.handlers.BufferingHandler.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> to process the buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.BufferingHandler.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BufferingHandler.flush" title="Link to this definition">¶</a></dt>
<dd><p>For a <a class="reference internal" href="#logging.handlers.BufferingHandler" title="logging.handlers.BufferingHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferingHandler</span></code></a> instance, flushing means that it sets the
buffer to an empty list. This method can be overwritten to implement more useful
flushing behavior.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.BufferingHandler.shouldFlush">
<span class="sig-name descname"><span class="pre">shouldFlush</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.BufferingHandler.shouldFlush" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the buffer is up to capacity. This method can be
overridden to implement custom flushing strategies.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.MemoryHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">MemoryHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">capacity</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flushLevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">ERROR</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flushOnClose</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.MemoryHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.MemoryHandler" title="logging.handlers.MemoryHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">MemoryHandler</span></code></a> class. The instance is
initialized with a buffer size of <em>capacity</em> (number of records buffered).
If <em>flushLevel</em> is not specified, <code class="xref py py-const docutils literal notranslate"><span class="pre">ERROR</span></code> is used. If no <em>target</em> is
specified, the target will need to be set using <a class="reference internal" href="#logging.handlers.MemoryHandler.setTarget" title="logging.handlers.MemoryHandler.setTarget"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setTarget()</span></code></a> before this
handler does anything useful. If <em>flushOnClose</em> is specified as <code class="docutils literal notranslate"><span class="pre">False</span></code>,
then the buffer is <em>not</em> flushed when the handler is closed. If not specified
or specified as <code class="docutils literal notranslate"><span class="pre">True</span></code>, the previous behaviour of flushing the buffer will
occur when the handler is closed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>flushOnClose</em> parameter was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.MemoryHandler.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.MemoryHandler.close" title="Link to this definition">¶</a></dt>
<dd><p>Calls <a class="reference internal" href="#logging.handlers.MemoryHandler.flush" title="logging.handlers.MemoryHandler.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a>, sets the target to <code class="docutils literal notranslate"><span class="pre">None</span></code> and clears the
buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.MemoryHandler.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.MemoryHandler.flush" title="Link to this definition">¶</a></dt>
<dd><p>For a <a class="reference internal" href="#logging.handlers.MemoryHandler" title="logging.handlers.MemoryHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">MemoryHandler</span></code></a> instance, flushing means just sending the buffered
records to the target, if there is one. The buffer is also cleared when
buffered records are sent to the target. Override if you want different behavior.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.MemoryHandler.setTarget">
<span class="sig-name descname"><span class="pre">setTarget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.MemoryHandler.setTarget" title="Link to this definition">¶</a></dt>
<dd><p>Sets the target handler for this handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.MemoryHandler.shouldFlush">
<span class="sig-name descname"><span class="pre">shouldFlush</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.MemoryHandler.shouldFlush" title="Link to this definition">¶</a></dt>
<dd><p>Checks for buffer full or a record at the <em>flushLevel</em> or higher.</p>
</dd></dl>

</dd></dl>

</section>
<section id="httphandler">
<span id="http-handler"></span><h2>HTTPHandler<a class="headerlink" href="#httphandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#logging.handlers.HTTPHandler" title="logging.handlers.HTTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module,
supports sending logging messages to a web server, using either <code class="docutils literal notranslate"><span class="pre">GET</span></code> or
<code class="docutils literal notranslate"><span class="pre">POST</span></code> semantics.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.HTTPHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">HTTPHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'GET'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">secure</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">credentials</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.HTTPHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.HTTPHandler" title="logging.handlers.HTTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPHandler</span></code></a> class. The <em>host</em> can be
of the form <code class="docutils literal notranslate"><span class="pre">host:port</span></code>, should you need to use a specific port number.  If
no <em>method</em> is specified, <code class="docutils literal notranslate"><span class="pre">GET</span></code> is used. If <em>secure</em> is true, a HTTPS
connection will be used. The <em>context</em> parameter may be set to a
<a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> instance to configure the SSL settings used for the
HTTPS connection. If <em>credentials</em> is specified, it should be a 2-tuple
consisting of userid and password, which will be placed in a HTTP
‘Authorization’ header using Basic authentication. If you specify
credentials, you should also specify secure=True so that your userid and
password are not passed in cleartext across the wire.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>context</em> parameter was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.HTTPHandler.mapLogRecord">
<span class="sig-name descname"><span class="pre">mapLogRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.HTTPHandler.mapLogRecord" title="Link to this definition">¶</a></dt>
<dd><p>Provides a dictionary, based on <code class="docutils literal notranslate"><span class="pre">record</span></code>, which is to be URL-encoded
and sent to the web server. The default implementation just returns
<code class="docutils literal notranslate"><span class="pre">record.__dict__</span></code>. This method can be overridden if e.g. only a
subset of <a class="reference internal" href="logging.html#logging.LogRecord" title="logging.LogRecord"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogRecord</span></code></a> is to be sent to the web server, or
if more specific customization of what’s sent to the server is required.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.HTTPHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.HTTPHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Sends the record to the web server as a URL-encoded dictionary. The
<a class="reference internal" href="#logging.handlers.HTTPHandler.mapLogRecord" title="logging.handlers.HTTPHandler.mapLogRecord"><code class="xref py py-meth docutils literal notranslate"><span class="pre">mapLogRecord()</span></code></a> method is used to convert the record to the
dictionary to be sent.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Since preparing a record for sending it to a web server is not
the same as a generic formatting operation, using
<a class="reference internal" href="logging.html#logging.Handler.setFormatter" title="logging.Handler.setFormatter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setFormatter()</span></code></a> to specify a
<a class="reference internal" href="logging.html#logging.Formatter" title="logging.Formatter"><code class="xref py py-class docutils literal notranslate"><span class="pre">Formatter</span></code></a> for a <a class="reference internal" href="#logging.handlers.HTTPHandler" title="logging.handlers.HTTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPHandler</span></code></a> has no effect.
Instead of calling <a class="reference internal" href="logging.html#logging.Handler.format" title="logging.Handler.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a>, this handler calls
<a class="reference internal" href="#logging.handlers.HTTPHandler.mapLogRecord" title="logging.handlers.HTTPHandler.mapLogRecord"><code class="xref py py-meth docutils literal notranslate"><span class="pre">mapLogRecord()</span></code></a> and then <a class="reference internal" href="urllib.parse.html#urllib.parse.urlencode" title="urllib.parse.urlencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">urllib.parse.urlencode()</span></code></a> to encode the
dictionary in a form suitable for sending to a web server.</p>
</div>
</dd></dl>

</section>
<section id="queuehandler">
<span id="queue-handler"></span><h2>QueueHandler<a class="headerlink" href="#queuehandler" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>The <a class="reference internal" href="#logging.handlers.QueueHandler" title="logging.handlers.QueueHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueHandler</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module,
supports sending logging messages to a queue, such as those implemented in the
<a class="reference internal" href="queue.html#module-queue" title="queue: A synchronized queue class."><code class="xref py py-mod docutils literal notranslate"><span class="pre">queue</span></code></a> or <a class="reference internal" href="multiprocessing.html#module-multiprocessing" title="multiprocessing: Process-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code></a> modules.</p>
<p>Along with the <a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> class, <a class="reference internal" href="#logging.handlers.QueueHandler" title="logging.handlers.QueueHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueHandler</span></code></a> can be used
to let handlers do their work on a separate thread from the one which does the
logging. This is important in web applications and also other service
applications where threads servicing clients need to respond as quickly as
possible, while any potentially slow operations (such as sending an email via
<a class="reference internal" href="#logging.handlers.SMTPHandler" title="logging.handlers.SMTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTPHandler</span></code></a>) are done on a separate thread.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.QueueHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">QueueHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">queue</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueHandler" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.QueueHandler" title="logging.handlers.QueueHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueHandler</span></code></a> class. The instance is
initialized with the queue to send messages to. The <em>queue</em> can be any
queue-like object; it’s used as-is by the <a class="reference internal" href="#logging.handlers.QueueHandler.enqueue" title="logging.handlers.QueueHandler.enqueue"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enqueue()</span></code></a> method, which
needs to know how to send messages to it. The queue is not <em>required</em> to
have the task tracking API, which means that you can use
<a class="reference internal" href="queue.html#queue.SimpleQueue" title="queue.SimpleQueue"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleQueue</span></code></a> instances for <em>queue</em>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you are using <a class="reference internal" href="multiprocessing.html#module-multiprocessing" title="multiprocessing: Process-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code></a>, you should avoid using
<a class="reference internal" href="queue.html#queue.SimpleQueue" title="queue.SimpleQueue"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleQueue</span></code></a> and instead use <a class="reference internal" href="multiprocessing.html#multiprocessing.Queue" title="multiprocessing.Queue"><code class="xref py py-class docutils literal notranslate"><span class="pre">multiprocessing.Queue</span></code></a>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueHandler.emit">
<span class="sig-name descname"><span class="pre">emit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueHandler.emit" title="Link to this definition">¶</a></dt>
<dd><p>Enqueues the result of preparing the LogRecord. Should an exception
occur (e.g. because a bounded queue has filled up), the
<a class="reference internal" href="logging.html#logging.Handler.handleError" title="logging.Handler.handleError"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handleError()</span></code></a> method is called to handle the
error. This can result in the record silently being dropped (if
<a class="reference internal" href="logging.html#logging.raiseExceptions" title="logging.raiseExceptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">logging.raiseExceptions</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>) or a message printed to
<code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> (if <a class="reference internal" href="logging.html#logging.raiseExceptions" title="logging.raiseExceptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">logging.raiseExceptions</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueHandler.prepare">
<span class="sig-name descname"><span class="pre">prepare</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueHandler.prepare" title="Link to this definition">¶</a></dt>
<dd><p>Prepares a record for queuing. The object returned by this
method is enqueued.</p>
<p>The base implementation formats the record to merge the message,
arguments, exception and stack information, if present.  It also removes
unpickleable items from the record in-place. Specifically, it overwrites
the record’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">msg</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">message</span></code> attributes with the merged
message (obtained by calling the handler’s <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> method), and
sets the <code class="xref py py-attr docutils literal notranslate"><span class="pre">args</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">exc_info</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">exc_text</span></code> attributes
to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>You might want to override this method if you want to convert
the record to a dict or JSON string, or send a modified copy
of the record while leaving the original intact.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The base implementation formats the message with arguments, sets
the <code class="docutils literal notranslate"><span class="pre">message</span></code> and <code class="docutils literal notranslate"><span class="pre">msg</span></code> attributes to the formatted message and
sets the <code class="docutils literal notranslate"><span class="pre">args</span></code> and <code class="docutils literal notranslate"><span class="pre">exc_text</span></code> attributes to <code class="docutils literal notranslate"><span class="pre">None</span></code> to allow
pickling and to prevent further attempts at formatting. This means
that a handler on the <a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> side won’t have the
information to do custom formatting, e.g. of exceptions. You may wish
to subclass <code class="docutils literal notranslate"><span class="pre">QueueHandler</span></code> and override this method to e.g. avoid
setting <code class="docutils literal notranslate"><span class="pre">exc_text</span></code> to <code class="docutils literal notranslate"><span class="pre">None</span></code>. Note that the <code class="docutils literal notranslate"><span class="pre">message</span></code> / <code class="docutils literal notranslate"><span class="pre">msg</span></code>
/ <code class="docutils literal notranslate"><span class="pre">args</span></code> changes are related to ensuring the record is pickleable,
and you might or might not be able to avoid doing that depending on
whether your <code class="docutils literal notranslate"><span class="pre">args</span></code> are pickleable. (Note that you may have to
consider not only your own code but also code in any libraries that
you use.)</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueHandler.enqueue">
<span class="sig-name descname"><span class="pre">enqueue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueHandler.enqueue" title="Link to this definition">¶</a></dt>
<dd><p>Enqueues the record on the queue using <code class="docutils literal notranslate"><span class="pre">put_nowait()</span></code>; you may
want to override this if you want to use blocking behaviour, or a
timeout, or a customized queue implementation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="logging.handlers.QueueHandler.listener">
<span class="sig-name descname"><span class="pre">listener</span></span><a class="headerlink" href="#logging.handlers.QueueHandler.listener" title="Link to this definition">¶</a></dt>
<dd><p>When created via configuration using <a class="reference internal" href="logging.config.html#logging.config.dictConfig" title="logging.config.dictConfig"><code class="xref py py-func docutils literal notranslate"><span class="pre">dictConfig()</span></code></a>, this
attribute will contain a <a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> instance for use with this
handler. Otherwise, it will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="queuelistener">
<span id="queue-listener"></span><h2>QueueListener<a class="headerlink" href="#queuelistener" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>The <a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> class, located in the <a class="reference internal" href="#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a>
module, supports receiving logging messages from a queue, such as those
implemented in the <a class="reference internal" href="queue.html#module-queue" title="queue: A synchronized queue class."><code class="xref py py-mod docutils literal notranslate"><span class="pre">queue</span></code></a> or <a class="reference internal" href="multiprocessing.html#module-multiprocessing" title="multiprocessing: Process-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code></a> modules. The
messages are received from a queue in an internal thread and passed, on
the same thread, to one or more handlers for processing. While
<a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> is not itself a handler, it is documented here
because it works hand-in-hand with <a class="reference internal" href="#logging.handlers.QueueHandler" title="logging.handlers.QueueHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueHandler</span></code></a>.</p>
<p>Along with the <a class="reference internal" href="#logging.handlers.QueueHandler" title="logging.handlers.QueueHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueHandler</span></code></a> class, <a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> can be used
to let handlers do their work on a separate thread from the one which does the
logging. This is important in web applications and also other service
applications where threads servicing clients need to respond as quickly as
possible, while any potentially slow operations (such as sending an email via
<a class="reference internal" href="#logging.handlers.SMTPHandler" title="logging.handlers.SMTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SMTPHandler</span></code></a>) are done on a separate thread.</p>
<dl class="py class">
<dt class="sig sig-object py" id="logging.handlers.QueueListener">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">logging.handlers.</span></span><span class="sig-name descname"><span class="pre">QueueListener</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">queue</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">handlers</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">respect_handler_level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new instance of the <a class="reference internal" href="#logging.handlers.QueueListener" title="logging.handlers.QueueListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">QueueListener</span></code></a> class. The instance is
initialized with the queue to send messages to and a list of handlers which
will handle entries placed on the queue. The queue can be any queue-like
object; it’s passed as-is to the <a class="reference internal" href="#logging.handlers.QueueListener.dequeue" title="logging.handlers.QueueListener.dequeue"><code class="xref py py-meth docutils literal notranslate"><span class="pre">dequeue()</span></code></a> method, which needs
to know how to get messages from it. The queue is not <em>required</em> to have the
task tracking API (though it’s used if available), which means that you can
use <a class="reference internal" href="queue.html#queue.SimpleQueue" title="queue.SimpleQueue"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleQueue</span></code></a> instances for <em>queue</em>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you are using <a class="reference internal" href="multiprocessing.html#module-multiprocessing" title="multiprocessing: Process-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code></a>, you should avoid using
<a class="reference internal" href="queue.html#queue.SimpleQueue" title="queue.SimpleQueue"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleQueue</span></code></a> and instead use <a class="reference internal" href="multiprocessing.html#multiprocessing.Queue" title="multiprocessing.Queue"><code class="xref py py-class docutils literal notranslate"><span class="pre">multiprocessing.Queue</span></code></a>.</p>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">respect_handler_level</span></code> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, a handler’s level is respected
(compared with the level for the message) when deciding whether to pass
messages to that handler; otherwise, the behaviour is as in previous Python
versions - to always pass each message to each handler.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <code class="docutils literal notranslate"><span class="pre">respect_handler_level</span></code> argument was added.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueListener.dequeue">
<span class="sig-name descname"><span class="pre">dequeue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">block</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener.dequeue" title="Link to this definition">¶</a></dt>
<dd><p>Dequeues a record and return it, optionally blocking.</p>
<p>The base implementation uses <code class="docutils literal notranslate"><span class="pre">get()</span></code>. You may want to override this
method if you want to use timeouts or work with custom queue
implementations.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueListener.prepare">
<span class="sig-name descname"><span class="pre">prepare</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener.prepare" title="Link to this definition">¶</a></dt>
<dd><p>Prepare a record for handling.</p>
<p>This implementation just returns the passed-in record. You may want to
override this method if you need to do any custom marshalling or
manipulation of the record before passing it to the handlers.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueListener.handle">
<span class="sig-name descname"><span class="pre">handle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener.handle" title="Link to this definition">¶</a></dt>
<dd><p>Handle a record.</p>
<p>This just loops through the handlers offering them the record
to handle. The actual object passed to the handlers is that which
is returned from <a class="reference internal" href="#logging.handlers.QueueListener.prepare" title="logging.handlers.QueueListener.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueListener.start">
<span class="sig-name descname"><span class="pre">start</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener.start" title="Link to this definition">¶</a></dt>
<dd><p>Starts the listener.</p>
<p>This starts up a background thread to monitor the queue for
LogRecords to process.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueListener.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener.stop" title="Link to this definition">¶</a></dt>
<dd><p>Stops the listener.</p>
<p>This asks the thread to terminate, and then waits for it to do so.
Note that if you don’t call this before your application exits, there
may be some records still left on the queue, which won’t be processed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="logging.handlers.QueueListener.enqueue_sentinel">
<span class="sig-name descname"><span class="pre">enqueue_sentinel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#logging.handlers.QueueListener.enqueue_sentinel" title="Link to this definition">¶</a></dt>
<dd><p>Writes a sentinel to the queue to tell the listener to quit. This
implementation uses <code class="docutils literal notranslate"><span class="pre">put_nowait()</span></code>.  You may want to override this
method if you want to use timeouts or work with custom queue
implementations.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="logging.html#module-logging" title="logging: Flexible event logging system for applications."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code></a></dt><dd><p>API reference for the logging module.</p>
</dd>
<dt>Module <a class="reference internal" href="logging.config.html#module-logging.config" title="logging.config: Configuration of the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code></a></dt><dd><p>Configuration API for the logging module.</p>
</dd>
</dl>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code> — Logging handlers</a><ul>
<li><a class="reference internal" href="#streamhandler">StreamHandler</a></li>
<li><a class="reference internal" href="#filehandler">FileHandler</a></li>
<li><a class="reference internal" href="#nullhandler">NullHandler</a></li>
<li><a class="reference internal" href="#watchedfilehandler">WatchedFileHandler</a></li>
<li><a class="reference internal" href="#baserotatinghandler">BaseRotatingHandler</a></li>
<li><a class="reference internal" href="#rotatingfilehandler">RotatingFileHandler</a></li>
<li><a class="reference internal" href="#timedrotatingfilehandler">TimedRotatingFileHandler</a></li>
<li><a class="reference internal" href="#sockethandler">SocketHandler</a></li>
<li><a class="reference internal" href="#datagramhandler">DatagramHandler</a></li>
<li><a class="reference internal" href="#sysloghandler">SysLogHandler</a></li>
<li><a class="reference internal" href="#nteventloghandler">NTEventLogHandler</a></li>
<li><a class="reference internal" href="#smtphandler">SMTPHandler</a></li>
<li><a class="reference internal" href="#memoryhandler">MemoryHandler</a></li>
<li><a class="reference internal" href="#httphandler">HTTPHandler</a></li>
<li><a class="reference internal" href="#queuehandler">QueueHandler</a></li>
<li><a class="reference internal" href="#queuelistener">QueueListener</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="logging.config.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code> — Logging configuration</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="getpass.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">getpass</span></code> — Portable password input</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/logging.handlers.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="getpass.html" title="getpass — Portable password input"
             >next</a> |</li>
        <li class="right" >
          <a href="logging.config.html" title="logging.config — Logging configuration"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" >Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code> — Logging handlers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>