<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Object Implementation Support" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/objimpl.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This chapter describes the functions, types, and macros used when defining new object types. Allocating Objects on the Heap, Common Object Structures- Base object types and macros, Implementing fun..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This chapter describes the functions, types, and macros used when defining new object types. Allocating Objects on the Heap, Common Object Structures- Base object types and macros, Implementing fun..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Object Implementation Support &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Allocating Objects on the Heap" href="allocation.html" />
    <link rel="prev" title="Memory Management" href="memory.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/objimpl.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="memory.html"
                          title="previous chapter">Memory Management</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="allocation.html"
                          title="next chapter">Allocating Objects on the Heap</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/objimpl.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="allocation.html" title="Allocating Objects on the Heap"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="memory.html" title="Memory Management"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Object Implementation Support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="object-implementation-support">
<span id="newtypes"></span><h1>Object Implementation Support<a class="headerlink" href="#object-implementation-support" title="Link to this heading">¶</a></h1>
<p>This chapter describes the functions, types, and macros used when defining new
object types.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="allocation.html">Allocating Objects on the Heap</a></li>
<li class="toctree-l1"><a class="reference internal" href="structures.html">Common Object Structures</a><ul>
<li class="toctree-l2"><a class="reference internal" href="structures.html#base-object-types-and-macros">Base object types and macros</a></li>
<li class="toctree-l2"><a class="reference internal" href="structures.html#implementing-functions-and-methods">Implementing functions and methods</a></li>
<li class="toctree-l2"><a class="reference internal" href="structures.html#accessing-attributes-of-extension-types">Accessing attributes of extension types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="structures.html#member-flags">Member flags</a></li>
<li class="toctree-l3"><a class="reference internal" href="structures.html#member-types">Member types</a></li>
<li class="toctree-l3"><a class="reference internal" href="structures.html#defining-getters-and-setters">Defining Getters and Setters</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html">Type Objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#quick-reference">Quick Reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="typeobj.html#tp-slots">“tp slots”</a></li>
<li class="toctree-l3"><a class="reference internal" href="typeobj.html#sub-slots">sub-slots</a></li>
<li class="toctree-l3"><a class="reference internal" href="typeobj.html#slot-typedefs">slot typedefs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#pytypeobject-definition">PyTypeObject Definition</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#pyobject-slots">PyObject Slots</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#pyvarobject-slots">PyVarObject Slots</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#pytypeobject-slots">PyTypeObject Slots</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#static-types">Static Types</a></li>
<li class="toctree-l2"><a class="reference internal" href="typeobj.html#heap-types">Heap Types</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#number-object-structures">Number Object Structures</a></li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#mapping-object-structures">Mapping Object Structures</a></li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#sequence-object-structures">Sequence Object Structures</a></li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#buffer-object-structures">Buffer Object Structures</a></li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#async-object-structures">Async Object Structures</a></li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#slot-type-typedefs">Slot Type typedefs</a></li>
<li class="toctree-l1"><a class="reference internal" href="typeobj.html#examples">Examples</a></li>
<li class="toctree-l1"><a class="reference internal" href="gcsupport.html">Supporting Cyclic Garbage Collection</a><ul>
<li class="toctree-l2"><a class="reference internal" href="gcsupport.html#controlling-the-garbage-collector-state">Controlling the Garbage Collector State</a></li>
<li class="toctree-l2"><a class="reference internal" href="gcsupport.html#querying-garbage-collector-state">Querying Garbage Collector State</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="memory.html"
                          title="previous chapter">Memory Management</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="allocation.html"
                          title="next chapter">Allocating Objects on the Heap</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/objimpl.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="allocation.html" title="Allocating Objects on the Heap"
             >next</a> |</li>
        <li class="right" >
          <a href="memory.html" title="Memory Management"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Object Implementation Support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>