<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="readline — GNU readline interface" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/readline.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The readline module defines a number of functions to facilitate completion and reading/writing of history files from the Python interpreter. This module can be used directly, or via the rlcompleter..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The readline module defines a number of functions to facilitate completion and reading/writing of history files from the Python interpreter. This module can be used directly, or via the rlcompleter..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>readline — GNU readline interface &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="rlcompleter — Completion function for GNU readline" href="rlcompleter.html" />
    <link rel="prev" title="stringprep — Internet String Preparation" href="stringprep.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/readline.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code> — GNU readline interface</a><ul>
<li><a class="reference internal" href="#init-file">Init file</a></li>
<li><a class="reference internal" href="#line-buffer">Line buffer</a></li>
<li><a class="reference internal" href="#history-file">History file</a></li>
<li><a class="reference internal" href="#history-list">History list</a></li>
<li><a class="reference internal" href="#startup-hooks">Startup hooks</a></li>
<li><a class="reference internal" href="#completion">Completion</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stringprep.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code> — Internet String Preparation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="rlcompleter.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">rlcompleter</span></code> — Completion function for GNU readline</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/readline.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="rlcompleter.html" title="rlcompleter — Completion function for GNU readline"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="stringprep.html" title="stringprep — Internet String Preparation"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" accesskey="U">Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code> — GNU readline interface</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-readline">
<span id="readline-gnu-readline-interface"></span><h1><a class="reference internal" href="#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> — GNU readline interface<a class="headerlink" href="#module-readline" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module defines a number of functions to facilitate
completion and reading/writing of history files from the Python interpreter.
This module can be used directly, or via the <a class="reference internal" href="rlcompleter.html#module-rlcompleter" title="rlcompleter: Python identifier completion, suitable for the GNU readline library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">rlcompleter</span></code></a> module, which
supports completion of Python identifiers at the interactive prompt.  Settings
made using  this module affect the behaviour of both the interpreter’s
interactive prompt  and the prompts offered by the built-in <a class="reference internal" href="functions.html#input" title="input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a>
function.</p>
<p>Readline keybindings may be configured via an initialization file, typically
<code class="docutils literal notranslate"><span class="pre">.inputrc</span></code> in your home directory.  See <a class="reference external" href="https://tiswww.cwru.edu/php/chet/readline/rluserman.html#Readline-Init-File">Readline Init File</a>
in the GNU Readline manual for information about the format and
allowable constructs of that file, and the capabilities of the
Readline library in general.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The underlying Readline library API may be implemented by
the <code class="docutils literal notranslate"><span class="pre">libedit</span></code> library instead of GNU readline.
On macOS the <a class="reference internal" href="#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module detects which library is being used
at run time.</p>
<p>The configuration file for <code class="docutils literal notranslate"><span class="pre">libedit</span></code> is different from that
of GNU readline. If you programmatically load configuration strings
you can check for the text “libedit” in <code class="xref py py-const docutils literal notranslate"><span class="pre">readline.__doc__</span></code>
to differentiate between GNU readline and libedit.</p>
<p>If you use <em>editline</em>/<code class="docutils literal notranslate"><span class="pre">libedit</span></code> readline emulation on macOS, the
initialization file located in your home directory is named
<code class="docutils literal notranslate"><span class="pre">.editrc</span></code>. For example, the following content in <code class="docutils literal notranslate"><span class="pre">~/.editrc</span></code> will
turn ON <em>vi</em> keybindings and TAB completion:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span><span class="p">:</span><span class="n">bind</span> <span class="o">-</span><span class="n">v</span>
<span class="n">python</span><span class="p">:</span><span class="n">bind</span> <span class="o">^</span><span class="n">I</span> <span class="n">rl_complete</span>
</pre></div>
</div>
</div>
<section id="init-file">
<h2>Init file<a class="headerlink" href="#init-file" title="Link to this heading">¶</a></h2>
<p>The following functions relate to the init file and user configuration:</p>
<dl class="py function">
<dt class="sig sig-object py" id="readline.parse_and_bind">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">parse_and_bind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.parse_and_bind" title="Link to this definition">¶</a></dt>
<dd><p>Execute the init line provided in the <em>string</em> argument. This calls
<code class="xref c c-func docutils literal notranslate"><span class="pre">rl_parse_and_bind()</span></code> in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.read_init_file">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">read_init_file</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.read_init_file" title="Link to this definition">¶</a></dt>
<dd><p>Execute a readline initialization file. The default filename is the last filename
used. This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">rl_read_init_file()</span></code> in the underlying library.</p>
</dd></dl>

</section>
<section id="line-buffer">
<h2>Line buffer<a class="headerlink" href="#line-buffer" title="Link to this heading">¶</a></h2>
<p>The following functions operate on the line buffer:</p>
<dl class="py function">
<dt class="sig sig-object py" id="readline.get_line_buffer">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_line_buffer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_line_buffer" title="Link to this definition">¶</a></dt>
<dd><p>Return the current contents of the line buffer (<code class="xref c c-data docutils literal notranslate"><span class="pre">rl_line_buffer</span></code>
in the underlying library).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.insert_text">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">insert_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.insert_text" title="Link to this definition">¶</a></dt>
<dd><p>Insert text into the line buffer at the cursor position.  This calls
<code class="xref c c-func docutils literal notranslate"><span class="pre">rl_insert_text()</span></code> in the underlying library, but ignores
the return value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.redisplay">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">redisplay</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.redisplay" title="Link to this definition">¶</a></dt>
<dd><p>Change what’s displayed on the screen to reflect the current contents of the
line buffer.  This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">rl_redisplay()</span></code> in the underlying library.</p>
</dd></dl>

</section>
<section id="history-file">
<h2>History file<a class="headerlink" href="#history-file" title="Link to this heading">¶</a></h2>
<p>The following functions operate on a history file:</p>
<dl class="py function">
<dt class="sig sig-object py" id="readline.read_history_file">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">read_history_file</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.read_history_file" title="Link to this definition">¶</a></dt>
<dd><p>Load a readline history file, and append it to the history list.
The default filename is <code class="file docutils literal notranslate"><span class="pre">~/.history</span></code>.  This calls
<code class="xref c c-func docutils literal notranslate"><span class="pre">read_history()</span></code> in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.write_history_file">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">write_history_file</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.write_history_file" title="Link to this definition">¶</a></dt>
<dd><p>Save the history list to a readline history file, overwriting any
existing file.  The default filename is <code class="file docutils literal notranslate"><span class="pre">~/.history</span></code>.  This calls
<code class="xref c c-func docutils literal notranslate"><span class="pre">write_history()</span></code> in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.append_history_file">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">append_history_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nelements</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.append_history_file" title="Link to this definition">¶</a></dt>
<dd><p>Append the last <em>nelements</em> items of history to a file.  The default filename is
<code class="file docutils literal notranslate"><span class="pre">~/.history</span></code>.  The file must already exist.  This calls
<code class="xref c c-func docutils literal notranslate"><span class="pre">append_history()</span></code> in the underlying library.  This function
only exists if Python was compiled for a version of the library
that supports it.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.get_history_length">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_history_length</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_history_length" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="readline.set_history_length">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_history_length</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_history_length" title="Link to this definition">¶</a></dt>
<dd><p>Set or return the desired number of lines to save in the history file.
The <a class="reference internal" href="#readline.write_history_file" title="readline.write_history_file"><code class="xref py py-func docutils literal notranslate"><span class="pre">write_history_file()</span></code></a> function uses this value to truncate
the history file, by calling <code class="xref c c-func docutils literal notranslate"><span class="pre">history_truncate_file()</span></code> in
the underlying library.  Negative values imply
unlimited history file size.</p>
</dd></dl>

</section>
<section id="history-list">
<h2>History list<a class="headerlink" href="#history-list" title="Link to this heading">¶</a></h2>
<p>The following functions operate on a global history list:</p>
<dl class="py function">
<dt class="sig sig-object py" id="readline.clear_history">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">clear_history</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.clear_history" title="Link to this definition">¶</a></dt>
<dd><p>Clear the current history.  This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">clear_history()</span></code> in the
underlying library.  The Python function only exists if Python was
compiled for a version of the library that supports it.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.get_current_history_length">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_current_history_length</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_current_history_length" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of items currently in the history.  (This is different from
<a class="reference internal" href="#readline.get_history_length" title="readline.get_history_length"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_history_length()</span></code></a>, which returns the maximum number of lines that will
be written to a history file.)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.get_history_item">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_history_item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_history_item" title="Link to this definition">¶</a></dt>
<dd><p>Return the current contents of history item at <em>index</em>.  The item index
is one-based.  This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">history_get()</span></code> in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.remove_history_item">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">remove_history_item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.remove_history_item" title="Link to this definition">¶</a></dt>
<dd><p>Remove history item specified by its position from the history.
The position is zero-based.  This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">remove_history()</span></code> in
the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.replace_history_item">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">replace_history_item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.replace_history_item" title="Link to this definition">¶</a></dt>
<dd><p>Replace history item specified by its position with <em>line</em>.
The position is zero-based.  This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">replace_history_entry()</span></code>
in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.add_history">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">add_history</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">line</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.add_history" title="Link to this definition">¶</a></dt>
<dd><p>Append <em>line</em> to the history buffer, as if it was the last line typed.
This calls <code class="xref c c-func docutils literal notranslate"><span class="pre">add_history()</span></code> in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.set_auto_history">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_auto_history</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enabled</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_auto_history" title="Link to this definition">¶</a></dt>
<dd><p>Enable or disable automatic calls to <code class="xref c c-func docutils literal notranslate"><span class="pre">add_history()</span></code> when reading
input via readline.  The <em>enabled</em> argument should be a Boolean value
that when true, enables auto history, and that when false, disables
auto history.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> Auto history is enabled by default, and changes to this do not persist
across multiple sessions.</p>
</div>
</dd></dl>

</section>
<section id="startup-hooks">
<h2>Startup hooks<a class="headerlink" href="#startup-hooks" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="readline.set_startup_hook">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_startup_hook</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_startup_hook" title="Link to this definition">¶</a></dt>
<dd><p>Set or remove the function invoked by the <code class="xref c c-data docutils literal notranslate"><span class="pre">rl_startup_hook</span></code>
callback of the underlying library.  If <em>function</em> is specified, it will
be used as the new hook function; if omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, any function
already installed is removed.  The hook is called with no
arguments just before readline prints the first prompt.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.set_pre_input_hook">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_pre_input_hook</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_pre_input_hook" title="Link to this definition">¶</a></dt>
<dd><p>Set or remove the function invoked by the <code class="xref c c-data docutils literal notranslate"><span class="pre">rl_pre_input_hook</span></code>
callback of the underlying library.  If <em>function</em> is specified, it will
be used as the new hook function; if omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, any
function already installed is removed.  The hook is called
with no arguments after the first prompt has been printed and just before
readline starts reading input characters.  This function only exists
if Python was compiled for a version of the library that supports it.</p>
</dd></dl>

</section>
<section id="completion">
<span id="readline-completion"></span><h2>Completion<a class="headerlink" href="#completion" title="Link to this heading">¶</a></h2>
<p>The following functions relate to implementing a custom word completion
function.  This is typically operated by the Tab key, and can suggest and
automatically complete a word being typed.  By default, Readline is set up
to be used by <a class="reference internal" href="rlcompleter.html#module-rlcompleter" title="rlcompleter: Python identifier completion, suitable for the GNU readline library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">rlcompleter</span></code></a> to complete Python identifiers for
the interactive interpreter.  If the <a class="reference internal" href="#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module is to be used
with a custom completer, a different set of word delimiters should be set.</p>
<dl class="py function">
<dt class="sig sig-object py" id="readline.set_completer">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_completer</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_completer" title="Link to this definition">¶</a></dt>
<dd><p>Set or remove the completer function.  If <em>function</em> is specified, it will be
used as the new completer function; if omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, any completer
function already installed is removed.  The completer function is called as
<code class="docutils literal notranslate"><span class="pre">function(text,</span> <span class="pre">state)</span></code>, for <em>state</em> in <code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">1</span></code>, <code class="docutils literal notranslate"><span class="pre">2</span></code>, …, until it
returns a non-string value.  It should return the next possible completion
starting with <em>text</em>.</p>
<p>The installed completer function is invoked by the <em>entry_func</em> callback
passed to <code class="xref c c-func docutils literal notranslate"><span class="pre">rl_completion_matches()</span></code> in the underlying library.
The <em>text</em> string comes from the first parameter to the
<code class="xref c c-data docutils literal notranslate"><span class="pre">rl_attempted_completion_function</span></code> callback of the
underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.get_completer">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_completer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_completer" title="Link to this definition">¶</a></dt>
<dd><p>Get the completer function, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no completer function has been set.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.get_completion_type">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_completion_type</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_completion_type" title="Link to this definition">¶</a></dt>
<dd><p>Get the type of completion being attempted.  This returns the
<code class="xref c c-data docutils literal notranslate"><span class="pre">rl_completion_type</span></code> variable in the underlying library as
an integer.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.get_begidx">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_begidx</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_begidx" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="readline.get_endidx">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_endidx</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_endidx" title="Link to this definition">¶</a></dt>
<dd><p>Get the beginning or ending index of the completion scope.
These indexes are the <em>start</em> and <em>end</em> arguments passed to the
<code class="xref c c-data docutils literal notranslate"><span class="pre">rl_attempted_completion_function</span></code> callback of the
underlying library.  The values may be different in the same
input editing scenario based on the underlying C readline implementation.
Ex: libedit is known to behave differently than libreadline.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.set_completer_delims">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_completer_delims</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_completer_delims" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="readline.get_completer_delims">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">get_completer_delims</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.get_completer_delims" title="Link to this definition">¶</a></dt>
<dd><p>Set or get the word delimiters for completion.  These determine the
start of the word to be considered for completion (the completion scope).
These functions access the <code class="xref c c-data docutils literal notranslate"><span class="pre">rl_completer_word_break_characters</span></code>
variable in the underlying library.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="readline.set_completion_display_matches_hook">
<span class="sig-prename descclassname"><span class="pre">readline.</span></span><span class="sig-name descname"><span class="pre">set_completion_display_matches_hook</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#readline.set_completion_display_matches_hook" title="Link to this definition">¶</a></dt>
<dd><p>Set or remove the completion display function.  If <em>function</em> is
specified, it will be used as the new completion display function;
if omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, any completion display function already
installed is removed.  This sets or clears the
<code class="xref c c-data docutils literal notranslate"><span class="pre">rl_completion_display_matches_hook</span></code> callback in the
underlying library.  The completion display function is called as
<code class="docutils literal notranslate"><span class="pre">function(substitution,</span> <span class="pre">[matches],</span> <span class="pre">longest_match_length)</span></code> once
each time matches need to be displayed.</p>
</dd></dl>

</section>
<section id="example">
<span id="readline-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>The following example demonstrates how to use the <a class="reference internal" href="#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module’s
history reading and writing functions to automatically load and save a history
file named <code class="file docutils literal notranslate"><span class="pre">.python_history</span></code> from the user’s home directory.  The code
below would normally be executed automatically during interactive sessions
from the user’s <span class="target" id="index-0"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONSTARTUP"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONSTARTUP</span></code></a> file.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">atexit</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">readline</span>

<span class="n">histfile</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="s2">&quot;~&quot;</span><span class="p">),</span> <span class="s2">&quot;.python_history&quot;</span><span class="p">)</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">readline</span><span class="o">.</span><span class="n">read_history_file</span><span class="p">(</span><span class="n">histfile</span><span class="p">)</span>
    <span class="c1"># default history len is -1 (infinite), which may grow unruly</span>
    <span class="n">readline</span><span class="o">.</span><span class="n">set_history_length</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span>
<span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
    <span class="k">pass</span>

<span class="n">atexit</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">readline</span><span class="o">.</span><span class="n">write_history_file</span><span class="p">,</span> <span class="n">histfile</span><span class="p">)</span>
</pre></div>
</div>
<p>This code is actually automatically run when Python is run in
<a class="reference internal" href="../tutorial/interpreter.html#tut-interactive"><span class="std std-ref">interactive mode</span></a> (see <a class="reference internal" href="site.html#rlcompleter-config"><span class="std std-ref">Readline configuration</span></a>).</p>
<p>The following example achieves the same goal but supports concurrent interactive
sessions, by only appending the new history.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">atexit</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">readline</span>
<span class="n">histfile</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="s2">&quot;~&quot;</span><span class="p">),</span> <span class="s2">&quot;.python_history&quot;</span><span class="p">)</span>

<span class="k">try</span><span class="p">:</span>
    <span class="n">readline</span><span class="o">.</span><span class="n">read_history_file</span><span class="p">(</span><span class="n">histfile</span><span class="p">)</span>
    <span class="n">h_len</span> <span class="o">=</span> <span class="n">readline</span><span class="o">.</span><span class="n">get_current_history_length</span><span class="p">()</span>
<span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
    <span class="nb">open</span><span class="p">(</span><span class="n">histfile</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
    <span class="n">h_len</span> <span class="o">=</span> <span class="mi">0</span>

<span class="k">def</span> <span class="nf">save</span><span class="p">(</span><span class="n">prev_h_len</span><span class="p">,</span> <span class="n">histfile</span><span class="p">):</span>
    <span class="n">new_h_len</span> <span class="o">=</span> <span class="n">readline</span><span class="o">.</span><span class="n">get_current_history_length</span><span class="p">()</span>
    <span class="n">readline</span><span class="o">.</span><span class="n">set_history_length</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span>
    <span class="n">readline</span><span class="o">.</span><span class="n">append_history_file</span><span class="p">(</span><span class="n">new_h_len</span> <span class="o">-</span> <span class="n">prev_h_len</span><span class="p">,</span> <span class="n">histfile</span><span class="p">)</span>
<span class="n">atexit</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">save</span><span class="p">,</span> <span class="n">h_len</span><span class="p">,</span> <span class="n">histfile</span><span class="p">)</span>
</pre></div>
</div>
<p>The following example extends the <a class="reference internal" href="code.html#code.InteractiveConsole" title="code.InteractiveConsole"><code class="xref py py-class docutils literal notranslate"><span class="pre">code.InteractiveConsole</span></code></a> class to
support history save/restore.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">atexit</span>
<span class="kn">import</span> <span class="nn">code</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">readline</span>

<span class="k">class</span> <span class="nc">HistoryConsole</span><span class="p">(</span><span class="n">code</span><span class="o">.</span><span class="n">InteractiveConsole</span><span class="p">):</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">locals</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;&lt;console&gt;&quot;</span><span class="p">,</span>
                 <span class="n">histfile</span><span class="o">=</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="s2">&quot;~/.console-history&quot;</span><span class="p">)):</span>
        <span class="n">code</span><span class="o">.</span><span class="n">InteractiveConsole</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">locals</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">init_history</span><span class="p">(</span><span class="n">histfile</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">init_history</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">histfile</span><span class="p">):</span>
        <span class="n">readline</span><span class="o">.</span><span class="n">parse_and_bind</span><span class="p">(</span><span class="s2">&quot;tab: complete&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">readline</span><span class="p">,</span> <span class="s2">&quot;read_history_file&quot;</span><span class="p">):</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">readline</span><span class="o">.</span><span class="n">read_history_file</span><span class="p">(</span><span class="n">histfile</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
                <span class="k">pass</span>
            <span class="n">atexit</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">save_history</span><span class="p">,</span> <span class="n">histfile</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">save_history</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">histfile</span><span class="p">):</span>
        <span class="n">readline</span><span class="o">.</span><span class="n">set_history_length</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span>
        <span class="n">readline</span><span class="o">.</span><span class="n">write_history_file</span><span class="p">(</span><span class="n">histfile</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code> — GNU readline interface</a><ul>
<li><a class="reference internal" href="#init-file">Init file</a></li>
<li><a class="reference internal" href="#line-buffer">Line buffer</a></li>
<li><a class="reference internal" href="#history-file">History file</a></li>
<li><a class="reference internal" href="#history-list">History list</a></li>
<li><a class="reference internal" href="#startup-hooks">Startup hooks</a></li>
<li><a class="reference internal" href="#completion">Completion</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stringprep.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code> — Internet String Preparation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="rlcompleter.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">rlcompleter</span></code> — Completion function for GNU readline</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/readline.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="rlcompleter.html" title="rlcompleter — Completion function for GNU readline"
             >next</a> |</li>
        <li class="right" >
          <a href="stringprep.html" title="stringprep — Internet String Preparation"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" >Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code> — GNU readline interface</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>