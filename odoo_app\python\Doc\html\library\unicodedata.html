<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="unicodedata — Unicode Database" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/unicodedata.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides access to the Unicode Character Database (UCD) which defines character properties for all Unicode characters. The data contained in this database is compiled from the UCD versi..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides access to the Unicode Character Database (UCD) which defines character properties for all Unicode characters. The data contained in this database is compiled from the UCD versi..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>unicodedata — Unicode Database &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="stringprep — Internet String Preparation" href="stringprep.html" />
    <link rel="prev" title="textwrap — Text wrapping and filling" href="textwrap.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/unicodedata.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="textwrap.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">textwrap</span></code> — Text wrapping and filling</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="stringprep.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code> — Internet String Preparation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/unicodedata.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="stringprep.html" title="stringprep — Internet String Preparation"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="textwrap.html" title="textwrap — Text wrapping and filling"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" accesskey="U">Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code> — Unicode Database</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-unicodedata">
<span id="unicodedata-unicode-database"></span><h1><a class="reference internal" href="#module-unicodedata" title="unicodedata: Access the Unicode Database."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code></a> — Unicode Database<a class="headerlink" href="#module-unicodedata" title="Link to this heading">¶</a></h1>
<hr class="docutils" id="index-0" />
<p>This module provides access to the Unicode Character Database (UCD) which
defines character properties for all Unicode characters. The data contained in
this database is compiled from the <a class="reference external" href="https://www.unicode.org/Public/15.0.0/ucd">UCD version 15.0.0</a>.</p>
<p>The module uses the same names and symbols as defined by Unicode
Standard Annex #44, <a class="reference external" href="https://www.unicode.org/reports/tr44/">“Unicode Character Database”</a>.  It defines the
following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.lookup">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">lookup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.lookup" title="Link to this definition">¶</a></dt>
<dd><p>Look up character by name.  If a character with the given name is found, return
the corresponding character.  If not found, <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Support for name aliases <a class="footnote-reference brackets" href="#id3" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> and named sequences <a class="footnote-reference brackets" href="#id4" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></a> has been added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.name">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.name" title="Link to this definition">¶</a></dt>
<dd><p>Returns the name assigned to the character <em>chr</em> as a string. If no
name is defined, <em>default</em> is returned, or, if not given, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is
raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.decimal">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">decimal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.decimal" title="Link to this definition">¶</a></dt>
<dd><p>Returns the decimal value assigned to the character <em>chr</em> as integer.
If no such value is defined, <em>default</em> is returned, or, if not given,
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.digit">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">digit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.digit" title="Link to this definition">¶</a></dt>
<dd><p>Returns the digit value assigned to the character <em>chr</em> as integer.
If no such value is defined, <em>default</em> is returned, or, if not given,
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.numeric">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">numeric</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.numeric" title="Link to this definition">¶</a></dt>
<dd><p>Returns the numeric value assigned to the character <em>chr</em> as float.
If no such value is defined, <em>default</em> is returned, or, if not given,
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.category">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">category</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.category" title="Link to this definition">¶</a></dt>
<dd><p>Returns the general category assigned to the character <em>chr</em> as
string.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.bidirectional">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">bidirectional</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.bidirectional" title="Link to this definition">¶</a></dt>
<dd><p>Returns the bidirectional class assigned to the character <em>chr</em> as
string. If no such value is defined, an empty string is returned.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.combining">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">combining</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.combining" title="Link to this definition">¶</a></dt>
<dd><p>Returns the canonical combining class assigned to the character <em>chr</em>
as integer. Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> if no combining class is defined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.east_asian_width">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">east_asian_width</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.east_asian_width" title="Link to this definition">¶</a></dt>
<dd><p>Returns the east asian width assigned to the character <em>chr</em> as
string.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.mirrored">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">mirrored</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.mirrored" title="Link to this definition">¶</a></dt>
<dd><p>Returns the mirrored property assigned to the character <em>chr</em> as
integer. Returns <code class="docutils literal notranslate"><span class="pre">1</span></code> if the character has been identified as a “mirrored”
character in bidirectional text, <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.decomposition">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">decomposition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.decomposition" title="Link to this definition">¶</a></dt>
<dd><p>Returns the character decomposition mapping assigned to the character
<em>chr</em> as string. An empty string is returned in case no such mapping is
defined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.normalize">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">normalize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">form</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unistr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.normalize" title="Link to this definition">¶</a></dt>
<dd><p>Return the normal form <em>form</em> for the Unicode string <em>unistr</em>. Valid values for
<em>form</em> are ‘NFC’, ‘NFKC’, ‘NFD’, and ‘NFKD’.</p>
<p>The Unicode standard defines various normalization forms of a Unicode string,
based on the definition of canonical equivalence and compatibility equivalence.
In Unicode, several characters can be expressed in various way. For example, the
character U+00C7 (LATIN CAPITAL LETTER C WITH CEDILLA) can also be expressed as
the sequence U+0043 (LATIN CAPITAL LETTER C) U+0327 (COMBINING CEDILLA).</p>
<p>For each character, there are two normal forms: normal form C and normal form D.
Normal form D (NFD) is also known as canonical decomposition, and translates
each character into its decomposed form. Normal form C (NFC) first applies a
canonical decomposition, then composes pre-combined characters again.</p>
<p>In addition to these two forms, there are two additional normal forms based on
compatibility equivalence. In Unicode, certain characters are supported which
normally would be unified with other characters. For example, U+2160 (ROMAN
NUMERAL ONE) is really the same thing as U+0049 (LATIN CAPITAL LETTER I).
However, it is supported in Unicode for compatibility with existing character
sets (e.g. gb2312).</p>
<p>The normal form KD (NFKD) will apply the compatibility decomposition, i.e.
replace all compatibility characters with their equivalents. The normal form KC
(NFKC) first applies the compatibility decomposition, followed by the canonical
composition.</p>
<p>Even if two unicode strings are normalized and look the same to
a human reader, if one has combining characters and the other
doesn’t, they may not compare equal.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="unicodedata.is_normalized">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">is_normalized</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">form</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unistr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#unicodedata.is_normalized" title="Link to this definition">¶</a></dt>
<dd><p>Return whether the Unicode string <em>unistr</em> is in the normal form <em>form</em>. Valid
values for <em>form</em> are ‘NFC’, ‘NFKC’, ‘NFD’, and ‘NFKD’.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<p>In addition, the module exposes the following constant:</p>
<dl class="py data">
<dt class="sig sig-object py" id="unicodedata.unidata_version">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">unidata_version</span></span><a class="headerlink" href="#unicodedata.unidata_version" title="Link to this definition">¶</a></dt>
<dd><p>The version of the Unicode database used in this module.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="unicodedata.ucd_3_2_0">
<span class="sig-prename descclassname"><span class="pre">unicodedata.</span></span><span class="sig-name descname"><span class="pre">ucd_3_2_0</span></span><a class="headerlink" href="#unicodedata.ucd_3_2_0" title="Link to this definition">¶</a></dt>
<dd><p>This is an object that has the same methods as the entire module, but uses the
Unicode database version 3.2 instead, for applications that require this
specific version of the Unicode database (such as IDNA).</p>
</dd></dl>

<p>Examples:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">unicodedata</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unicodedata</span><span class="o">.</span><span class="n">lookup</span><span class="p">(</span><span class="s1">&#39;LEFT CURLY BRACKET&#39;</span><span class="p">)</span>
<span class="go">&#39;{&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unicodedata</span><span class="o">.</span><span class="n">name</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)</span>
<span class="go">&#39;SOLIDUS&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unicodedata</span><span class="o">.</span><span class="n">decimal</span><span class="p">(</span><span class="s1">&#39;9&#39;</span><span class="p">)</span>
<span class="go">9</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unicodedata</span><span class="o">.</span><span class="n">decimal</span><span class="p">(</span><span class="s1">&#39;a&#39;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="gr">ValueError</span>: <span class="n">not a decimal</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unicodedata</span><span class="o">.</span><span class="n">category</span><span class="p">(</span><span class="s1">&#39;A&#39;</span><span class="p">)</span>  <span class="c1"># &#39;L&#39;etter, &#39;u&#39;ppercase</span>
<span class="go">&#39;Lu&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unicodedata</span><span class="o">.</span><span class="n">bidirectional</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\u0660</span><span class="s1">&#39;</span><span class="p">)</span> <span class="c1"># &#39;A&#39;rabic, &#39;N&#39;umber</span>
<span class="go">&#39;AN&#39;</span>
</pre></div>
</div>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id3" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p><a class="reference external" href="https://www.unicode.org/Public/15.0.0/ucd/NameAliases.txt">https://www.unicode.org/Public/15.0.0/ucd/NameAliases.txt</a></p>
</aside>
<aside class="footnote brackets" id="id4" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id2">2</a><span class="fn-bracket">]</span></span>
<p><a class="reference external" href="https://www.unicode.org/Public/15.0.0/ucd/NamedSequences.txt">https://www.unicode.org/Public/15.0.0/ucd/NamedSequences.txt</a></p>
</aside>
</aside>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="textwrap.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">textwrap</span></code> — Text wrapping and filling</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="stringprep.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code> — Internet String Preparation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/unicodedata.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="stringprep.html" title="stringprep — Internet String Preparation"
             >next</a> |</li>
        <li class="right" >
          <a href="textwrap.html" title="textwrap — Text wrapping and filling"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" >Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code> — Unicode Database</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>