<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="selectors — High-level I/O multiplexing" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/selectors.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/selectors.py Introduction: This module allows high-level and efficient I/O multiplexing, built upon the select module primitives. Users are encouraged to use this module instead, u..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/selectors.py Introduction: This module allows high-level and efficient I/O multiplexing, built upon the select module primitives. Users are encouraged to use this module instead, u..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>selectors — High-level I/O multiplexing &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="signal — Set handlers for asynchronous events" href="signal.html" />
    <link rel="prev" title="select — Waiting for I/O completion" href="select.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/selectors.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code> — High-level I/O multiplexing</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#classes">Classes</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="select.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code> — Waiting for I/O completion</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="signal.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code> — Set handlers for asynchronous events</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/selectors.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="signal.html" title="signal — Set handlers for asynchronous events"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="select.html" title="select — Waiting for I/O completion"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" accesskey="U">Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code> — High-level I/O multiplexing</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-selectors">
<span id="selectors-high-level-i-o-multiplexing"></span><h1><a class="reference internal" href="#module-selectors" title="selectors: High-level I/O multiplexing."><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code></a> — High-level I/O multiplexing<a class="headerlink" href="#module-selectors" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/selectors.py">Lib/selectors.py</a></p>
<hr class="docutils" />
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Link to this heading">¶</a></h2>
<p>This module allows high-level and efficient I/O multiplexing, built upon the
<a class="reference internal" href="select.html#module-select" title="select: Wait for I/O completion on multiple streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code></a> module primitives. Users are encouraged to use this module
instead, unless they want precise control over the OS-level primitives used.</p>
<p>It defines a <a class="reference internal" href="#selectors.BaseSelector" title="selectors.BaseSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseSelector</span></code></a> abstract base class, along with several
concrete implementations (<a class="reference internal" href="#selectors.KqueueSelector" title="selectors.KqueueSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">KqueueSelector</span></code></a>, <a class="reference internal" href="#selectors.EpollSelector" title="selectors.EpollSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">EpollSelector</span></code></a>…),
that can be used to wait for I/O readiness notification on multiple file
objects. In the following, “file object” refers to any object with a
<a class="reference internal" href="io.html#io.IOBase.fileno" title="io.IOBase.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a> method, or a raw file descriptor. See <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>.</p>
<p><a class="reference internal" href="#selectors.DefaultSelector" title="selectors.DefaultSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultSelector</span></code></a> is an alias to the most efficient implementation
available on the current platform: this should be the default choice for most
users.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The type of file objects supported depends on the platform: on Windows,
sockets are supported, but not pipes, whereas on Unix, both are supported
(some other types may be supported as well, such as fifos or special file
devices).</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="select.html#module-select" title="select: Wait for I/O completion on multiple streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code></a></dt><dd><p>Low-level I/O multiplexing module.</p>
</dd>
</dl>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
</section>
<section id="classes">
<h2>Classes<a class="headerlink" href="#classes" title="Link to this heading">¶</a></h2>
<p>Classes hierarchy:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">BaseSelector</span>
<span class="o">+--</span> <span class="n">SelectSelector</span>
<span class="o">+--</span> <span class="n">PollSelector</span>
<span class="o">+--</span> <span class="n">EpollSelector</span>
<span class="o">+--</span> <span class="n">DevpollSelector</span>
<span class="o">+--</span> <span class="n">KqueueSelector</span>
</pre></div>
</div>
<p>In the following, <em>events</em> is a bitwise mask indicating which I/O events should
be waited for on a given file object. It can be a combination of the modules
constants below:</p>
<blockquote>
<div><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Constant</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="py data">
<dt class="sig sig-object py" id="selectors.EVENT_READ">
<span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">EVENT_READ</span></span><a class="headerlink" href="#selectors.EVENT_READ" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>Available for read</p></td>
</tr>
<tr class="row-odd"><td><dl class="py data">
<dt class="sig sig-object py" id="selectors.EVENT_WRITE">
<span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">EVENT_WRITE</span></span><a class="headerlink" href="#selectors.EVENT_WRITE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>Available for write</p></td>
</tr>
</tbody>
</table>
</div></blockquote>
<dl class="py class">
<dt class="sig sig-object py" id="selectors.SelectorKey">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">SelectorKey</span></span><a class="headerlink" href="#selectors.SelectorKey" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a> is a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">namedtuple</span></code></a> used to
associate a file object to its underlying file descriptor, selected event
mask and attached data. It is returned by several <a class="reference internal" href="#selectors.BaseSelector" title="selectors.BaseSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseSelector</span></code></a>
methods.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="selectors.SelectorKey.fileobj">
<span class="sig-name descname"><span class="pre">fileobj</span></span><a class="headerlink" href="#selectors.SelectorKey.fileobj" title="Link to this definition">¶</a></dt>
<dd><p>File object registered.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="selectors.SelectorKey.fd">
<span class="sig-name descname"><span class="pre">fd</span></span><a class="headerlink" href="#selectors.SelectorKey.fd" title="Link to this definition">¶</a></dt>
<dd><p>Underlying file descriptor.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="selectors.SelectorKey.events">
<span class="sig-name descname"><span class="pre">events</span></span><a class="headerlink" href="#selectors.SelectorKey.events" title="Link to this definition">¶</a></dt>
<dd><p>Events that must be waited for on this file object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="selectors.SelectorKey.data">
<span class="sig-name descname"><span class="pre">data</span></span><a class="headerlink" href="#selectors.SelectorKey.data" title="Link to this definition">¶</a></dt>
<dd><p>Optional opaque data associated to this file object: for example, this
could be used to store a per-client session ID.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.BaseSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">BaseSelector</span></span><a class="headerlink" href="#selectors.BaseSelector" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#selectors.BaseSelector" title="selectors.BaseSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseSelector</span></code></a> is used to wait for I/O event readiness on multiple
file objects. It supports file stream registration, unregistration, and a
method to wait for I/O events on those streams, with an optional timeout.
It’s an abstract base class, so cannot be instantiated. Use
<a class="reference internal" href="#selectors.DefaultSelector" title="selectors.DefaultSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultSelector</span></code></a> instead, or one of <a class="reference internal" href="#selectors.SelectSelector" title="selectors.SelectSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectSelector</span></code></a>,
<a class="reference internal" href="#selectors.KqueueSelector" title="selectors.KqueueSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">KqueueSelector</span></code></a> etc. if you want to specifically use an
implementation, and your platform supports it.
<a class="reference internal" href="#selectors.BaseSelector" title="selectors.BaseSelector"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseSelector</span></code></a> and its concrete implementations support the
<a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> protocol.</p>
<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.register">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">register</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileobj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">events</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.register" title="Link to this definition">¶</a></dt>
<dd><p>Register a file object for selection, monitoring it for I/O events.</p>
<p><em>fileobj</em> is the file object to monitor.  It may either be an integer
file descriptor or an object with a <code class="docutils literal notranslate"><span class="pre">fileno()</span></code> method.
<em>events</em> is a bitwise mask of events to monitor.
<em>data</em> is an opaque object.</p>
<p>This returns a new <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a> instance, or raises a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> in case of invalid event mask or file descriptor, or
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> if the file object is already registered.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.unregister">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">unregister</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileobj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.unregister" title="Link to this definition">¶</a></dt>
<dd><p>Unregister a file object from selection, removing it from monitoring. A
file object shall be unregistered prior to being closed.</p>
<p><em>fileobj</em> must be a file object previously registered.</p>
<p>This returns the associated <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a> instance, or raises a
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> if <em>fileobj</em> is not registered.  It will raise
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>fileobj</em> is invalid (e.g. it has no <code class="docutils literal notranslate"><span class="pre">fileno()</span></code>
method or its <code class="docutils literal notranslate"><span class="pre">fileno()</span></code> method has an invalid return value).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.modify">
<span class="sig-name descname"><span class="pre">modify</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileobj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">events</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.modify" title="Link to this definition">¶</a></dt>
<dd><p>Change a registered file object’s monitored events or attached data.</p>
<p>This is equivalent to <code class="docutils literal notranslate"><span class="pre">BaseSelector.unregister(fileobj)</span></code> followed
by <code class="docutils literal notranslate"><span class="pre">BaseSelector.register(fileobj,</span> <span class="pre">events,</span> <span class="pre">data)</span></code>, except that it
can be implemented more efficiently.</p>
<p>This returns a new <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a> instance, or raises a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> in case of invalid event mask or file descriptor, or
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> if the file object is not registered.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.select">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">select</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.select" title="Link to this definition">¶</a></dt>
<dd><p>Wait until some registered file objects become ready, or the timeout
expires.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">timeout</span> <span class="pre">&gt;</span> <span class="pre">0</span></code>, this specifies the maximum wait time, in seconds.
If <code class="docutils literal notranslate"><span class="pre">timeout</span> <span class="pre">&lt;=</span> <span class="pre">0</span></code>, the call won’t block, and will report the currently
ready file objects.
If <em>timeout</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the call will block until a monitored file object
becomes ready.</p>
<p>This returns a list of <code class="docutils literal notranslate"><span class="pre">(key,</span> <span class="pre">events)</span></code> tuples, one for each ready file
object.</p>
<p><em>key</em> is the <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a> instance corresponding to a ready file
object.
<em>events</em> is a bitmask of events ready on this file object.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This method can return before any file object becomes ready or the
timeout has elapsed if the current process receives a signal: in this
case, an empty list will be returned.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The selector is now retried with a recomputed timeout when interrupted
by a signal if the signal handler did not raise an exception (see
<span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale), instead of returning an empty list
of events before the timeout.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the selector.</p>
<p>This must be called to make sure that any underlying resource is freed.
The selector shall not be used once it has been closed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.get_key">
<span class="sig-name descname"><span class="pre">get_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileobj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.get_key" title="Link to this definition">¶</a></dt>
<dd><p>Return the key associated with a registered file object.</p>
<p>This returns the <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a> instance associated to this file
object, or raises <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> if the file object is not registered.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="selectors.BaseSelector.get_map">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">get_map</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#selectors.BaseSelector.get_map" title="Link to this definition">¶</a></dt>
<dd><p>Return a mapping of file objects to selector keys.</p>
<p>This returns a <a class="reference internal" href="collections.abc.html#collections.abc.Mapping" title="collections.abc.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mapping</span></code></a> instance mapping
registered file objects to their associated <a class="reference internal" href="#selectors.SelectorKey" title="selectors.SelectorKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorKey</span></code></a>
instance.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.DefaultSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">DefaultSelector</span></span><a class="headerlink" href="#selectors.DefaultSelector" title="Link to this definition">¶</a></dt>
<dd><p>The default selector class, using the most efficient implementation
available on the current platform. This should be the default choice for
most users.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.SelectSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">SelectSelector</span></span><a class="headerlink" href="#selectors.SelectSelector" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="select.html#select.select" title="select.select"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.select()</span></code></a>-based selector.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.PollSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">PollSelector</span></span><a class="headerlink" href="#selectors.PollSelector" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="select.html#select.poll" title="select.poll"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.poll()</span></code></a>-based selector.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.EpollSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">EpollSelector</span></span><a class="headerlink" href="#selectors.EpollSelector" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="select.html#select.epoll" title="select.epoll"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.epoll()</span></code></a>-based selector.</p>
<dl class="py method">
<dt class="sig sig-object py" id="selectors.EpollSelector.fileno">
<span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#selectors.EpollSelector.fileno" title="Link to this definition">¶</a></dt>
<dd><p>This returns the file descriptor used by the underlying
<a class="reference internal" href="select.html#select.epoll" title="select.epoll"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.epoll()</span></code></a> object.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.DevpollSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">DevpollSelector</span></span><a class="headerlink" href="#selectors.DevpollSelector" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="select.html#select.devpoll" title="select.devpoll"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.devpoll()</span></code></a>-based selector.</p>
<dl class="py method">
<dt class="sig sig-object py" id="selectors.DevpollSelector.fileno">
<span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#selectors.DevpollSelector.fileno" title="Link to this definition">¶</a></dt>
<dd><p>This returns the file descriptor used by the underlying
<a class="reference internal" href="select.html#select.devpoll" title="select.devpoll"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.devpoll()</span></code></a> object.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="selectors.KqueueSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">selectors.</span></span><span class="sig-name descname"><span class="pre">KqueueSelector</span></span><a class="headerlink" href="#selectors.KqueueSelector" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="select.html#select.kqueue" title="select.kqueue"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.kqueue()</span></code></a>-based selector.</p>
<dl class="py method">
<dt class="sig sig-object py" id="selectors.KqueueSelector.fileno">
<span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#selectors.KqueueSelector.fileno" title="Link to this definition">¶</a></dt>
<dd><p>This returns the file descriptor used by the underlying
<a class="reference internal" href="select.html#select.kqueue" title="select.kqueue"><code class="xref py py-func docutils literal notranslate"><span class="pre">select.kqueue()</span></code></a> object.</p>
</dd></dl>

</dd></dl>

</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>Here is a simple echo server implementation:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">selectors</span>
<span class="kn">import</span> <span class="nn">socket</span>

<span class="n">sel</span> <span class="o">=</span> <span class="n">selectors</span><span class="o">.</span><span class="n">DefaultSelector</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">accept</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">mask</span><span class="p">):</span>
    <span class="n">conn</span><span class="p">,</span> <span class="n">addr</span> <span class="o">=</span> <span class="n">sock</span><span class="o">.</span><span class="n">accept</span><span class="p">()</span>  <span class="c1"># Should be ready</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;accepted&#39;</span><span class="p">,</span> <span class="n">conn</span><span class="p">,</span> <span class="s1">&#39;from&#39;</span><span class="p">,</span> <span class="n">addr</span><span class="p">)</span>
    <span class="n">conn</span><span class="o">.</span><span class="n">setblocking</span><span class="p">(</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">sel</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">conn</span><span class="p">,</span> <span class="n">selectors</span><span class="o">.</span><span class="n">EVENT_READ</span><span class="p">,</span> <span class="n">read</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">read</span><span class="p">(</span><span class="n">conn</span><span class="p">,</span> <span class="n">mask</span><span class="p">):</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">conn</span><span class="o">.</span><span class="n">recv</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span>  <span class="c1"># Should be ready</span>
    <span class="k">if</span> <span class="n">data</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;echoing&#39;</span><span class="p">,</span> <span class="nb">repr</span><span class="p">(</span><span class="n">data</span><span class="p">),</span> <span class="s1">&#39;to&#39;</span><span class="p">,</span> <span class="n">conn</span><span class="p">)</span>
        <span class="n">conn</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>  <span class="c1"># Hope it won&#39;t block</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;closing&#39;</span><span class="p">,</span> <span class="n">conn</span><span class="p">)</span>
        <span class="n">sel</span><span class="o">.</span><span class="n">unregister</span><span class="p">(</span><span class="n">conn</span><span class="p">)</span>
        <span class="n">conn</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

<span class="n">sock</span> <span class="o">=</span> <span class="n">socket</span><span class="o">.</span><span class="n">socket</span><span class="p">()</span>
<span class="n">sock</span><span class="o">.</span><span class="n">bind</span><span class="p">((</span><span class="s1">&#39;localhost&#39;</span><span class="p">,</span> <span class="mi">1234</span><span class="p">))</span>
<span class="n">sock</span><span class="o">.</span><span class="n">listen</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="n">sock</span><span class="o">.</span><span class="n">setblocking</span><span class="p">(</span><span class="kc">False</span><span class="p">)</span>
<span class="n">sel</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">sock</span><span class="p">,</span> <span class="n">selectors</span><span class="o">.</span><span class="n">EVENT_READ</span><span class="p">,</span> <span class="n">accept</span><span class="p">)</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">events</span> <span class="o">=</span> <span class="n">sel</span><span class="o">.</span><span class="n">select</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">mask</span> <span class="ow">in</span> <span class="n">events</span><span class="p">:</span>
        <span class="n">callback</span> <span class="o">=</span> <span class="n">key</span><span class="o">.</span><span class="n">data</span>
        <span class="n">callback</span><span class="p">(</span><span class="n">key</span><span class="o">.</span><span class="n">fileobj</span><span class="p">,</span> <span class="n">mask</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code> — High-level I/O multiplexing</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#classes">Classes</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="select.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code> — Waiting for I/O completion</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="signal.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code> — Set handlers for asynchronous events</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/selectors.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="signal.html" title="signal — Set handlers for asynchronous events"
             >next</a> |</li>
        <li class="right" >
          <a href="select.html" title="select — Waiting for I/O completion"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="ipc.html" >Networking and Interprocess Communication</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">selectors</span></code> — High-level I/O multiplexing</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>