<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- equiment.request: views -->
    <record id="hr_equipment_request_view_search" model="ir.ui.view">
        <field name="name">equipment.request.search</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <search string="Maintenance Request Search">
                <field name="name" string="Request"/>
                <field name="category_id"/>
                <field name="user_id"/>
                <field name="equipment_id"/>
                <field name="owner_user_id"/>
                <field name="stage_id"/>
                <field name="maintenance_team_id"/>
                <filter string="My Maintenances" name="my_maintenances" domain="[('user_id', '=', uid)]"/>
                <separator/>
                <filter string="To Do" name="todo" domain="[('stage_id.done', '=', False)]"/>
                <filter string="Done" name="done" domain="[('stage_id.done', '=', True)]"/>
                <separator/>
                <filter string="Blocked" name="kanban_state_block" domain="[('stage_id.done', '=', False), ('kanban_state', '=', 'blocked')]"/>
                <filter string="Ready" name="done" domain="[('stage_id.done', '=', False), ('kanban_state', '=', 'done')]"/>
                <separator/>
                <filter string="High-priority" name="high_priority" domain="[('stage_id.done', '=', False), ('priority', '=', '3')]"/>
                <separator/>
                <filter string="Unscheduled" name="unscheduled" domain="[('stage_id.done', '=', False), ('schedule_date', '=', False)]"/>
                <separator/>
                <filter name="filter_request_date" date="request_date"/>
                <filter name="filter_schedule_date" date="schedule_date"/>
                <filter name="filter_close_date" date="close_date"/>
                <separator/>
                <filter string="Unread Messages" name="message_needaction" domain="[('message_needaction', '=', True)]" groups="mail.group_mail_notification_type_inbox"/>
                <separator/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <separator/>
                <filter string="Active" name="active" domain="[('archive', '=', False)]"/>
                <filter string="Cancelled" name="inactive" domain="[('archive', '=', True)]"/>
                <group  expand='0' string='Group by...'>
                    <filter string='Assigned to' name="assigned" domain="[]" context="{'group_by': 'user_id'}"/>
                    <filter string='Category' name="category" domain="[]" context="{'group_by' : 'category_id'}"/>
                    <filter string='Stage' name="stages" domain="[]" context="{'group_by' : 'stage_id'}"/>
                    <filter string='Created By' name='created_by' domain="[]" context="{'group_by': 'owner_user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="maintenance_request_view_activity" model="ir.ui.view">
        <field name="name">maintenance.request.view.activity</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <activity string="Maintenance Request">
                <field name="user_id"/>
                <templates>
                    <div t-name="activity-box">
                        <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]"/>
                        <div class="flex-grow-1 d-block">
                            <div class="d-flex justify-content-between">
                                <field name="name" display="full" class="o_text_block o_text_bold"/>
                            </div>
                            <field name="equipment_id" muted="1" display="full" class="o_text_block"/>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record id="hr_equipment_request_view_form" model="ir.ui.view">
        <field name="name">equipment.request.form</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <form string="Maintenance Request">
                <field name="company_id" invisible="1"/>
                <field name="category_id" invisible="1"/>
                <header>
                    <button string="Cancel" name="archive_equipment_request" type="object" invisible="archive"/>
                    <button string="Reopen Request" name="reset_equipment_request" type="object" invisible="not archive"/>
                    <field name="stage_id" widget="statusbar" options="{'clickable': '1'}" invisible="archive"/>
                </header>
                <sheet>
                    <div invisible="not archive">
                        <span class="badge text-bg-warning float-end">Canceled</span>
                    </div>
                    <field name="kanban_state" widget="state_selection"/>
                    <div class="oe_title">
                        <label for="name" string="Request"/>
                        <h1>
                            <field name="name" placeholder="e.g. Screen not working"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="owner_user_id" string="Requested By" invisible="1"/>
                            <field name="equipment_id"  context="{'default_company_id':company_id, 'default_category_id':category_id}"/>
                            <field name="category_id" groups="maintenance.group_equipment_manager" context="{'default_company_id':company_id}" invisible="not equipment_id"/>
                            <field name="request_date" readonly="True"/>
                            <field name="done" invisible="1"/>
                            <field name="close_date" readonly="True" invisible="not done"/>
                            <field name="archive" invisible="1"/>
                            <field name="maintenance_type" widget="radio"/>
                        </group>
                        <group>
                            <field name="maintenance_team_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="user_id" string="Responsible"/>
                            <field name="schedule_date"/>
                            <label for="duration"/>
                            <div>
                                <field name="duration"
                                       widget="float_time"
                                       class="oe_inline"/> <span class="ml8">hours</span>
                            </div>
                            <label for="recurring_maintenance" invisible="maintenance_type == 'corrective'"/>
                            <div class="d-inline-flex" invisible="maintenance_type == 'corrective'">
                                <field name="recurring_maintenance" nolabel="1" class="ms-0" style="width: fit-content;"/>
                            </div>
                            <label for="repeat_interval" invisible="not recurring_maintenance"/>
                            <div class="d-flex" invisible="not recurring_maintenance">
                                <field name="repeat_interval" required="recurring_maintenance" class="me-2" style="max-width: 2rem !important;" />
                                <field name="repeat_unit" required="recurring_maintenance" class="me-2" style="max-width: 4rem !important;" />
                                <field name="repeat_type" required="recurring_maintenance" class="me-2" style="max-width: 15rem !important;" />
                                <field name="repeat_until" invisible="repeat_type != 'until'" required="repeat_type == 'until'" class="me-2" />
                            </div>
                            <field name="priority" widget="priority"/>
                            <field name="email_cc" string="Email cc" groups="base.group_no_one"/>
                            <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Notes">
                            <field name='description' placeholder="Internal Notes"/>
                        </page>
                        <page string="Instructions">
                            <group col="1">
                                <field name="instruction_type" widget="radio" nolabel="1"/>
                                <field name="instruction_pdf" help="Upload your PDF file." widget="pdf_viewer" invisible="instruction_type != 'pdf'" required="instruction_type == 'pdf'" nolabel="1"/>
                                <field name="instruction_google_slide" placeholder="Google Slide Link" widget="embed_viewer" invisible="instruction_type != 'google_slide'" required="instruction_type == 'google_slide'" nolabel="1"/>
                                <field name="instruction_text" placeholder="Your instructions" invisible="instruction_type != 'text'" nolabel="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="hr_equipment_request_view_kanban" model="ir.ui.view">
        <field name="name">equipment.request.kanban</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <kanban default_group_by="stage_id" sample="1">
                <field name="stage_id"/>
                <field name="color"/>
                <field name="priority"/>
                <field name="equipment_id"/>
                <field name="user_id"/>
                <field name="owner_user_id"/>
                <field name="category_id"/>
                <field name="kanban_state"/>
                <field name="activity_ids" />
                <field name="activity_state" />
                <field name="archive" />
                <progressbar field="kanban_state" colors='{"done": "success", "blocked": "danger"}'/>
                <templates>
                    <t t-name="kanban-tooltip">
                       <ul class="oe_kanban_tooltip">
                          <li t-if="record.category_id.raw_value"><b>Category:</b> <t t-esc="record.category_id.value"/></li>
                          <li t-if="record.user_id.raw_value"><b>Request to:</b> <t t-esc="record.user_id.value"/></li>
                       </ul>
                    </t>
                    <t t-name="kanban-menu">
                        <t t-if="widget.editable"><a role="menuitem" type="edit" class="dropdown-item">Edit...</a></t>
                        <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                        <ul class="oe_kanban_colorpicker" data-field="color"/>
                    </t>
                    <t t-name="kanban-box">
                        <div t-attf-class="{{!selection_mode ? 'oe_kanban_color_' + kanban_getcolor(record.color.raw_value) : ''}} oe_kanban_card oe_kanban_global_click oe_semantic_html_override">
                            <div class="oe_kanban_content" tooltip="kanban-tooltip">
                                <div class="o_kanban_record_top">
                                    <b class="o_kanban_record_title"><field name="name"/></b>
                                </div>
                                <div class="o_kanban_record_body">
                                    <span name="owner_user_id" t-if="record.owner_user_id.raw_value">Requested by: <field name="owner_user_id"/><br/></span>
                                    <span class="oe_grey" t-if="record.equipment_id.raw_value"><field name="equipment_id"/><span t-if="record.category_id.raw_value"> (<field name="category_id"/>)</span><br/></span>
                                    <span name="schedule_date" t-if="record.schedule_date.raw_value"><field name="schedule_date"/><br/></span>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="priority" widget="priority"/>
                                        <div class="o_kanban_inline_block ml4 mr4">
                                            <field name="activity_ids" widget="kanban_activity" />
                                        </div>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <div invisible="not archive">
                                            <span class="badge text-bg-warning float-end">Cancelled</span>
                                        </div>
                                        <field name="kanban_state" widget="state_selection"/>
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </div>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_equipment_request_view_tree" model="ir.ui.view">
        <field name="name">equipment.request.tree</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <tree string="maintenance Request" multi_edit="1" sample="1">
                <field name="message_needaction" column_invisible="True"/>
                <field name="name"/>
                <field name="request_date" groups="base.group_no_one"/>
                <field name="owner_user_id"/>
                <field name="user_id"/>
                <field name="category_id" readonly="1" groups="maintenance.group_equipment_manager"/>
                <field name="stage_id"/>
                <field name="company_id" readonly="1" groups="base.group_multi_company"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </tree>
        </field>
    </record>

    <record id="hr_equipment_request_view_graph" model="ir.ui.view">
        <field name="name">equipment.request.graph</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <graph string="maintenance Request" sample="1">
                <field name="user_id"/>
                <field name="stage_id"/>
            </graph>
        </field>
    </record>

    <record id="hr_equipment_request_view_pivot" model="ir.ui.view">
        <field name="name">equipment.request.pivot</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <pivot string="maintenance Request" sample="1">
                <field name="user_id"/>
                <field name="stage_id"/>
                <field name="color" invisible="1"/>
            </pivot>
        </field>
    </record>


    <record id="hr_equipment_view_calendar" model="ir.ui.view">
        <field name="name">equipment.request.calendar</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <calendar date_start="schedule_date" date_delay="duration" color="user_id" event_limit="5" js_class="calendar_with_recurrence">
                <field name="user_id" filters="1"/>
                <field name="priority"/>
                <field name="maintenance_type"/>
                <field name="recurring_maintenance" invisible="1"/>
                <field name="repeat_interval" invisible="1"/>
                <field name="repeat_unit" invisible="1"/>
                <field name="repeat_type" invisible="1"/>
                <field name="repeat_until" invisible="1"/>
                <field name="done" invisible="1"/>
                <field name="archive" invisible="1"/>
            </calendar>
        </field>
    </record>

    <!-- equiment.request: actions -->
    <record id="hr_equipment_request_action" model="ir.actions.act_window">
        <field name="name">Maintenance Requests</field>
        <field name="res_model">maintenance.request</field>
        <field name="view_mode">kanban,tree,form,pivot,graph,calendar,activity</field>
        <field name="view_id" ref="hr_equipment_request_view_kanban"/>
        <field name="context">{
            'search_default_active': True,
            'default_user_id': uid
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new maintenance request
            </p><p>
                Follow the process of the request and communicate with the collaborator.
            </p>
        </field>
    </record>

    <record id="hr_equipment_request_action_link" model="ir.actions.act_window">
        <field name="name">Maintenance Requests</field>
        <field name="res_model">maintenance.request</field>
        <field name="view_mode">kanban,tree,form,pivot,graph,calendar,activity</field>
        <field name="search_view_id" ref="hr_equipment_request_view_search"/>
        <field name="view_id" ref="hr_equipment_request_view_kanban"/>
        <field name="context">{
            'search_default_category_id': [active_id],
            'search_default_active': True,
            'default_category_id': active_id,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new maintenance request
            </p><p>
                Follow the process of the request and communicate with the collaborator.
            </p>
        </field>
    </record>

    <record id="hr_equipment_request_action_from_equipment" model="ir.actions.act_window">
        <field name="name">Maintenance Requests</field>
        <field name="res_model">maintenance.request</field>
        <field name="binding_model_id" ref="maintenance.model_maintenance_equipment"/>
        <field name="view_mode">kanban,tree,form,pivot,graph,calendar,activity</field>
        <field name="context">{
            'search_default_active': True,
            'default_equipment_id': active_id,
        }</field>
        <field name="domain">[('equipment_id', '=', active_id)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new maintenance request
            </p><p>
                Follow the process of the request and communicate with the collaborator.
            </p>
        </field>
    </record>

    <record id="hr_equipment_todo_request_action_from_dashboard" model="ir.actions.act_window">
        <field name="name">Maintenance Requests</field>
        <field name="res_model">maintenance.request</field>
        <field name="view_mode">kanban,tree,form,pivot,graph,calendar,activity</field>
        <field name="context">{
            'search_default_active': True,
            'search_default_maintenance_team_id': active_id,
            'default_maintenance_team_id': active_id,
        }</field>
        <field name="domain">[('maintenance_team_id', '=', active_id), ('maintenance_type', 'in', context.get('maintenance_type', ['preventive', 'corrective']))]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new maintenance request
            </p><p>
                Follow the process of the request and communicate with the collaborator.
            </p>
        </field>
    </record>

    <record id="hr_equipment_request_action_cal" model="ir.actions.act_window">
        <field name="name">Maintenance Requests</field>
        <field name="res_model">maintenance.request</field>
        <field name="view_mode">calendar,kanban,tree,form,pivot,graph,activity</field>
        <field name="view_id" ref="hr_equipment_view_calendar"/>
        <field name="context">{
            'search_default_active': True,
            'search_default_todo': True,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new maintenance request
            </p><p>
                Follow the process of the request and communicate with the collaborator.
            </p>
        </field>
    </record>

    <record id="maintenance_request_action_reports" model="ir.actions.act_window">
        <field name="name">Maintenance Requests</field>
        <field name="res_model">maintenance.request</field>
        <field name="view_mode">graph,pivot,kanban,tree,form,calendar,activity</field>
        <field name="context">{
            'search_default_active': True,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new maintenance request
            </p><p>
                Follow the process of the request and communicate with the collaborator.
            </p>
        </field>
    </record>

    <!-- equiment: views -->
    <record id="hr_equipment_view_form" model="ir.ui.view">
        <field name="name">equipment.form</field>
        <field name="model">maintenance.equipment</field>
        <field name="arch" type="xml">
            <form string="Equipment">
                <sheet>
                    <field name="company_id" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(hr_equipment_request_action_from_equipment)d"
                            type="action"
                            class="oe_stat_button"
                            context="{'search_default_active': True, 'default_company_id': company_id, 'default_maintenance_team_id': maintenance_team_id}"
                            icon="fa-wrench">
                            <field string="Maintenance" name="maintenance_open_count" widget="statinfo"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1><field name="name" string="Name" placeholder="e.g. LED Monitor"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="category_id" options="{&quot;no_open&quot;: True}" context="{'default_company_id':company_id}"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="owner_user_id" string="Owner"/>
                        </group>
                        <group>
                            <field name="maintenance_team_id" context="{'default_company_id':company_id}"/>
                            <field name="technician_user_id" domain="[('share', '=', False)]"/>
                            <field name="assign_date" groups="base.group_no_one"/>
                            <field name="scrap_date" groups="base.group_no_one"/>
                            <field name="location" string="Used in location"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="note"/>
                        </page>
                        <page string="Product Information" name="product_information">
                            <group>
                                <group>
                                    <field name="partner_id"/>
                                    <field name="partner_ref"/>
                                    <field name="model"/>
                                    <field name="serial_no"/>
                                </group><group>
                                    <field name="effective_date"/>
                                    <field name="cost" groups="maintenance.group_equipment_manager"/>
                                    <field name="warranty_date"/>
                                </group>
                            </group>
                        </page>
                        <page string="Maintenance" name="maintenance">
                            <group>
                                <group name="statistics">
                                    <label for="expected_mtbf" string="Expected Mean Time Between Failure"/>
                                    <div class="o_row">
                                        <field name="expected_mtbf"/> days
                                    </div>
                                    <label for="mtbf" string="Mean Time Between Failure"/>
                                    <div class="o_row">
                                        <field name="mtbf" /> days
                                    </div>
                                    <label for="estimated_next_failure" string="Estimated Next Failure"/>
                                    <div class="o_row">
                                        <field name="estimated_next_failure" />
                                    </div>
                                    <field name="latest_failure_date" string="Latest Failure" />
                                    <label for="mttr" string="Mean Time To Repair"/>
                                    <div class="o_row">
                                        <field name="mttr" /> days
                                    </div>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="hr_equipment_view_kanban" model="ir.ui.view">
        <field name="name">equipment.kanban</field>
        <field name="model">maintenance.equipment</field>
        <field name="arch" type="xml">
            <kanban sample="1">
                <field name="name"/>
                <field name="color"/>
                <field name="technician_user_id"/>
                <field name="owner_user_id"/>
                <field name="category_id"/>
                <field name="serial_no"/>
                <field name="model"/>
                <field name="maintenance_ids"/>
                <field name="maintenance_open_count"/>
                <field name="activity_ids" />
                <field name="activity_state" />
                <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                <templates>
                    <t t-name="kanban-tooltip">
                        <ul class="oe_kanban_tooltip">
                            <li t-if="record.serial_no.raw_value"><b>Serial Number:</b> <t t-esc="record.serial_no.value"/></li>
                            <li t-if="record.model.raw_value"><b>Model Number:</b> <t t-esc="record.model.value"/></li>
                        </ul>
                    </t>
                    <t t-name="kanban-menu">
                        <t t-if="widget.editable"><a role="menuitem" type="edit" class="dropdown-item">Edit...</a></t>
                        <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                        <div role="separator" class="dropdown-divider"></div>
                        <div role="separator" class="dropdown-header">Record Colour</div>
                        <ul class="oe_kanban_colorpicker" data-field="color"/>
                    </t>
                    <t t-name="kanban-box">
                        <div t-attf-class="{{!selection_mode ? 'oe_kanban_color_' + kanban_getcolor(record.color.raw_value) : ''}} oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content" tooltip="kanban-tooltip">
                                <div class="o_kanban_record_top">
                                    <b class="o_kanban_record_title"><field name="name"/><small><span t-if="record.model.raw_value"> (<field name="model"/>)</span></small></b>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div t-if="record.serial_no.raw_value"><field name="serial_no"/></div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <div class="badge text-bg-danger" t-if="!selection_mode and record.maintenance_open_count.raw_value" >
                                            <t t-out="record.maintenance_open_count.raw_value"/> Request
                                        </div>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <div class="o_kanban_inline_block" t-if="!selection_mode">
                                            <field name="activity_ids" widget="kanban_activity" />
                                        </div>
                                        <field name="owner_user_id" widget="many2one_avatar_user"/>
                                    </div>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_equipment_view_tree" model="ir.ui.view">
        <field name="name">equipment.tree</field>
        <field name="model">maintenance.equipment</field>
        <field name="arch" type="xml">
            <tree string="Assign To User" sample="1">
                <field name="message_needaction" column_invisible="True"/>
                <field name="name"/>
                <!-- <field name="active" invisible="1"/> -->
                <field name="owner_user_id" string="Owner"/>
                <field name="assign_date" groups="base.group_no_one"/>
                <field name="serial_no"/>
                <field name="technician_user_id"/>
                <field name="category_id"/>
                <field name="partner_id" column_invisible="True"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </tree>
        </field>
    </record>

    <record id="hr_equipment_view_search" model="ir.ui.view">
        <field name="name">equipment.search</field>
        <field name="model">maintenance.equipment</field>
        <field name="arch" type="xml">
            <search string="Search">
                <field string="Equipment" name="name" filter_domain="[
                    '|', '|', '|',
                    ('name', 'ilike', self), ('model', 'ilike', self), ('serial_no', 'ilike', self), ('partner_ref', 'ilike', self)]"/>
                <field string="Category" name="category_id"/>
                <field name="owner_user_id"/>
                <filter string="My Equipment" name="my" domain="[('owner_user_id', '=', uid)]"/>
                <filter string="Assigned" name="assigned" domain="[('owner_user_id', '!=', False)]"/>
                <filter string="Unassigned" name="available" domain="[('owner_user_id', '=', False)]"/>
                <separator/>
                <filter string="Under Maintenance" name="under_maintenance" domain="[('maintenance_open_count', '&gt;', 0)]"/>
                <separator/>
                <filter string="Unread Messages" name="message_needaction" domain="[('message_needaction','=',True)]" groups="mail.group_mail_notification_type_inbox"/>
                <separator/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                <group  expand='0' string='Group by...'>
                    <filter string='Technician' name="technicians" domain="[]" context="{'group_by': 'technician_user_id'}"/>
                    <filter string='Category' name="category" domain="[]" context="{'group_by': 'category_id'}"/>
                    <filter string='Owner' name="owner" domain="[]" context="{'group_by': 'owner_user_id'}"/>
                    <filter string='Vendor' name="vendor" domain="[]" context="{'group_by': 'partner_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_equipment_action" model="ir.actions.act_window">
        <field name="name">Equipment</field>
        <field name="res_model">maintenance.equipment</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="hr_equipment_view_kanban"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new equipment
            </p><p>
                Track equipment and link it to an employee or department.
                You will be able to manage allocations, issues and maintenance of your equipment.
            </p>
        </field>
    </record>

    <!-- equiment: actions -->
    <record id="hr_equipment_action_from_category_form" model="ir.actions.act_window">
        <field name="name">Equipment</field>
        <field name="res_model">maintenance.equipment</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="hr_equipment_view_search"/>
        <field name="view_id" ref="hr_equipment_view_kanban"/>
        <field name="context">{
            'search_default_category_id': [active_id],
            'default_category_id': active_id,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new equipment
            </p><p>
                Track equipment and link it to an employee or department.
                You will be able to manage allocations, issues and maintenance of your equipment.
            </p>
        </field>
    </record>

    <!-- equipment.category: views -->
    <record id="hr_equipment_category_view_form" model="ir.ui.view">
        <field name="name">equipment.category.form</field>
        <field name="model">maintenance.equipment.category</field>
        <field name="arch" type="xml">
            <form string="Equipment Categories">
                <sheet>
                <div class="oe_button_box" name="button_box">
                    <button name="%(hr_equipment_action_from_category_form)d"
                        class="oe_stat_button"
                        icon="fa-cubes"
                        type="action">
                        <field string="Equipment" name="equipment_count" widget="statinfo"/>
                    </button>
                    <button name="%(hr_equipment_request_action_link)d"
                        type="action"
                        class="oe_stat_button"
                        icon="fa-wrench">
                        <field string="Maintenance" name="maintenance_open_count" widget="statinfo"/>
                    </button>
                </div>
                <div class="oe_title">
                    <label for="name" string="Category Name"/>
                    <h1>
                        <field name="name" placeholder="e.g. Monitors"/>
                    </h1>
                </div>
                <group>
                    <field name="technician_user_id" class="oe_inline" domain="[('share', '=', False)]"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" class="oe_inline"/>
                </group>
                <group name="group_alias">
                    <label for="alias_name" string="Email Alias"/>
                    <div name="alias_def">
                        <field name="alias_id" class="oe_read_only oe_inline" string="Email Alias" required="0"/>
                        <div class="oe_edit_only oe_inline" name="edit_alias" style="display: inline;" dir="ltr">
                            <field name="alias_name" class="oe_inline"/>@
                            <field name="alias_domain_id" class="oe_inline" placeholder="e.g. domain.com"
                                   options="{'no_create': True, 'no_open': True}"/>
                        </div>
                    </div>
                </group>
                <group>
                    <field name="note"/>
                </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="hr_equipment_category_view_tree" model="ir.ui.view">
        <field name="name">equipment.category.tree</field>
        <field name="model">maintenance.equipment.category</field>
        <field name="arch" type="xml">
            <tree string="Assign To User">
                <field name="name" string="Name"/>
                <field name="technician_user_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="hr_equipment_category_view_search" model="ir.ui.view">
        <field name="name">equipment.category.search</field>
        <field name="model">maintenance.equipment.category</field>
        <field name="arch" type="xml">
            <search string="Search">
                <field name="name" string="Category Name" filter_domain="[('name','ilike',self)]"/>
                <group  expand='0' string='Group by...'>
                    <filter string='Responsible' name="responsible" domain="[]" context="{'group_by' : 'technician_user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_maintenance_equipment_category_kanban" model="ir.ui.view">
        <field name="name">maintenance.equipment.category.kanban</field>
        <field name="model">maintenance.equipment.category</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="technician_user_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="mb4">
                                <strong><field name="name"/></strong>
                            </div>
                            <div class="row mt4">
                                <div class="col-6">
                                    <span class="badge rounded-pill">
                                        <strong>Equipment:</strong> <field name="equipment_count"/>
                                    </span>
                                </div>
                                <div class="col-6 text-end">
                                    <span class="badge rounded-pill">
                                        <strong>Maintenance:</strong> <field name="maintenance_open_count"/>
                                    </span>
                                    <field name="technician_user_id" widget="many2one_avatar_user"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- equipment.category: actions -->
    <record id="hr_equipment_category_action" model="ir.actions.act_window">
        <field name="name">Equipment Categories</field>
        <field name="res_model">maintenance.equipment.category</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_id" ref="hr_equipment_category_view_tree"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new equipment category
            </p>
        </field>
    </record>

    <!-- equipment.stage: views -->
    <record id="hr_equipment_stage_view_search" model="ir.ui.view">
        <field name="name">equipment.stage.search</field>
        <field name="model">maintenance.stage</field>
        <field name="arch" type="xml">
            <search string="Maintenance Request Stages">
               <field name="name" string="Maintenance Request Stages"/>
            </search>
        </field>
    </record>

    <record id="hr_equipment_stage_view_tree" model="ir.ui.view">
        <field name="name">equipment.stage.tree</field>
        <field name="model">maintenance.stage</field>
        <field name="arch" type="xml">
            <tree string="Maintenance Request Stage" editable="top">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="fold"/>
                <field name="done"/>
            </tree>
        </field>
    </record>
    <record id="hr_equipment_stage_view_kanban" model="ir.ui.view">
        <field name="name">equipment.stage.kanban</field>
        <field name="model">maintenance.stage</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <strong><field name="name"/></strong>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- equipment.stages: actions -->
    <record id="hr_equipment_stage_action" model="ir.actions.act_window">
        <field name="name">Stages</field>
        <field name="res_model">maintenance.stage</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a new stage in the maintenance request
          </p>
        </field>
    </record>

    <!-- maintenance.team: views -->
    <record id="maintenance_team_view_form" model="ir.ui.view">
        <field name="name">maintenance.team.form</field>
        <field name="model">maintenance.team</field>
        <field name="arch" type="xml">
            <form string="Maintenance Team">
                <sheet>
                <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                <div class="oe_title">
                    <label for="name" string="Team Name"/>
                    <h1>
                        <field name="name" placeholder="e.g. Internal Maintenance"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="active" invisible="1"/>
                        <field name="member_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create': True}" domain="[('share', '=', False)]"/>
                    </group>
                    <group>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="maintenance_team_view_tree" model="ir.ui.view">
        <field name="name">maintenance.team.tree</field>
        <field name="model">maintenance.team</field>
        <field name="arch" type="xml">
            <tree string="Maintenance Team" editable="bottom">
                <field name="name"/>
                <field name="member_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create': True}" domain="[('share', '=', False)]"/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
            </tree>
        </field>
    </record>

    <record id="maintenance_team_view_kanban" model="ir.ui.view">
        <field name="name">maintenance.team.kanban</field>
        <field name="model">maintenance.team</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <strong><field name="name"/></strong>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="maintenance_team_kanban" model="ir.ui.view">
        <field name="name">maintenance.team.kanban</field>
        <field name="model">maintenance.team</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_dashboard o_maintenance_team_kanban" create="0">
                <field name="name"/>
                <field name="color"/>
                <field name="todo_request_ids"/>
                <field name="todo_request_count"/>
                <field name="todo_request_count_date"/>
                <field name="todo_request_count_high_priority"/>
                <field name="todo_request_count_block"/>
                <field name="todo_request_count_unscheduled"/>
                <templates>
                    <t t-name="kanban-menu">
                        <div class="container">
                            <div class="row">
                                <div class="col-6 o_kanban_card_manage_section o_kanban_manage_view">
                                    <h5 role="menuitem" class="o_kanban_card_manage_title">
                                        <span>Requests</span>
                                    </h5>
                                    <div role="menuitem">
                                        <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action">
                                            All
                                        </a>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_todo': 1}">
                                            To Do
                                        </a>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_progress': 1}">
                                            In Progress
                                        </a>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_done': 1}">
                                            Done
                                        </a>
                                    </div>
                                </div>
                                <div class="col-6 o_kanban_card_manage_section o_kanban_manage_new">
                                    <h5 role="menuitem" class="o_kanban_card_manage_title">
                                        <span>Reporting</span>
                                    </h5>
                                    <div role="menuitem">
                                        <a name="%(maintenance_request_action_reports)d" type="action" context="{'search_default_maintenance_team_id': id}">
                                        Maintenance Requests
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                                <div class="col-8" role="menuitem" aria-haspopup="true">
                                    <ul role="menu" class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                                <div role="menuitem" class="col-4">
                                    <a type="edit" class="dropdown-item">Configuration</a>
                                </div>
                            </div>
                        </div>
                    </t>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                            <div t-attf-class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action">
                                            <field name="name"/>
                                        </a></div>
                                </div>
                            </div>
                            <div class="container o_kanban_card_content">
                                <div class="row">
                                    <div class="col-6 o_kanban_primary_left">
                                        <button class="btn btn-primary" name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_todo': 1}">
                                            <t t-esc="record.todo_request_count.value"/> To Do
                                        </button>
                                    </div>
                                    <div class="col-6 o_kanban_primary_right">
                                        <div t-if="record.todo_request_count_date.raw_value > 0">
                                            <a name="%(hr_equipment_request_action_cal)d" type="action" context="{'search_default_todo': 1, 'search_default_maintenance_team_id': id}">
                                                <t t-esc="record.todo_request_count_date.value"/>
                                                Scheduled
                                            </a>
                                        </div>
                                        <div t-if="record.todo_request_count_high_priority.raw_value > 0">
                                            <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_high_priority': 1}">
                                                <t t-esc="record.todo_request_count_high_priority.value"/>
                                                Top Priorities
                                            </a>
                                        </div>
                                        <div t-if="record.todo_request_count_block.raw_value > 0">
                                            <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_kanban_state_block': 1}">
                                                <t t-esc="record.todo_request_count_block.value"/>
                                                Blocked
                                            </a>
                                        </div>
                                        <div t-if="record.todo_request_count_unscheduled.raw_value > 0">
                                            <a name="%(hr_equipment_todo_request_action_from_dashboard)d" type="action" context="{'search_default_todo': 1, 'search_default_unscheduled': 1}">
                                                <t t-esc="record.todo_request_count_unscheduled.value"/>
                                                Unscheduled
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="maintenance_team_view_search" model="ir.ui.view">
        <field name="name">maintenance.team.search</field>
        <field name="model">maintenance.team</field>
        <field name="arch" type="xml">
            <search string="Search">
                <field string="Team" name="name"/>
                <filter string="Archived" domain="[('active', '=', False)]" name="inactive"/>
            </search>
        </field>
    </record>

    <!-- equipment.team: actions -->
    <record id="maintenance_team_action_settings" model="ir.actions.act_window">
        <field name="name">Teams</field>
        <field name="res_model">maintenance.team</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="maintenance_team_view_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('maintenance_team_view_tree')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('maintenance_team_view_kanban')})]"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a team in the maintenance request
          </p>
        </field>
    </record>

    <record id="maintenance_dashboard_action" model="ir.actions.act_window">
        <field name="name">Maintenance Teams</field>
        <field name="res_model">maintenance.team</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="maintenance_team_kanban"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a new stage in the maintenance request
          </p>
        </field>
    </record>


    <!-- Menu items hierachy -->
    <menuitem
        id="menu_maintenance_title"
        name="Maintenance"
        web_icon="maintenance,static/description/icon.png"
        sequence="160"/>

    <menuitem
        id="menu_m_dashboard"
        name="Dashboard"
        parent="menu_maintenance_title"
        groups="group_equipment_manager,base.group_user"
        action="maintenance_dashboard_action"
        sequence="0"/>

    <menuitem
        id="menu_m_request"
        name="Maintenance"
        parent="menu_maintenance_title"
        groups="group_equipment_manager,base.group_user"
        sequence="1"/>

    <menuitem
        id="menu_m_request_form"
        name="Maintenance Requests"
        parent="menu_m_request"
        action="hr_equipment_request_action"
        groups="group_equipment_manager,base.group_user"
        sequence="1"/>

    <menuitem
        id="menu_m_request_calendar"
        name="Maintenance Calendar"
        parent="menu_m_request"
        action="hr_equipment_request_action_cal"
        groups="group_equipment_manager,base.group_user"
        sequence="2"/>

    <menuitem
        id="menu_equipment_form"
        name="Equipment"
        parent="menu_maintenance_title"
        action="hr_equipment_action"
        groups="group_equipment_manager,base.group_user"
        sequence="2"/>

    <menuitem
        id="menu_m_reports"
        name="Reporting"
        parent="menu_maintenance_title"
        groups="group_equipment_manager,base.group_user"
        sequence="3"/>

    <menuitem
        id="menu_m_reports_oee"
        name="Overall Equipment Effectiveness (OEE)"
        parent="menu_m_reports"
        groups="group_equipment_manager,base.group_user"
        sequence="1"/>

    <menuitem
        id="menu_m_reports_losses"
        name="Losses Analysis"
        parent="menu_m_reports"
        groups="group_equipment_manager,base.group_user"
        sequence="2"/>

    <menuitem
        id="maintenance_reporting"
        name="Reporting"
        parent="menu_maintenance_title"
        sequence="20"/>
    <menuitem
        id="maintenance_request_reporting"
        action="maintenance_request_action_reports"
        parent="maintenance_reporting"/>

    <menuitem
        id="menu_maintenance_configuration"
        name="Configuration"
        parent="menu_maintenance_title"
        groups="group_equipment_manager"
        sequence="100"/>

    <menuitem
        id="menu_maintenance_teams"
        name="Maintenance Teams"
        parent="menu_maintenance_configuration"
        action="maintenance_team_action_settings"
        groups="group_equipment_manager"
        sequence="1"/>

    <menuitem
        id="menu_maintenance_cat"
        name="Equipment Categories"
        parent="menu_maintenance_configuration"
        action="hr_equipment_category_action"
        sequence="2"/>

    <menuitem
        id="menu_maintenance_stage_configuration"
        name="Maintenance Stages"
        parent="menu_maintenance_configuration"
        action="hr_equipment_stage_action"
        groups="base.group_no_one"
        sequence="3" />
</odoo>
