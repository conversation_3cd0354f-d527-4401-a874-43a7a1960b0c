<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.calendar</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="Calendar" string="Calendar" name="calendar" groups="base.group_system">
                    <block title="Calendar Settings" name="calendar">
                        <setting string="Outlook Calendar" documentation="/applications/productivity/calendar/outlook.html" help="Synchronize your calendar with Outlook" id="sync_outlook_calendar_setting">
                            <field name="module_microsoft_calendar" />
                            <div class="content-group" invisible="not module_microsoft_calendar" id="msg_module_microsoft_calendar">
                                <div class="text-warning mt16"><strong>Save</strong> this page and come back here to set up the feature.</div>
                            </div>
                        </setting>
                        <setting string="Google Calendar" documentation="/applications/productivity/calendar/google.html" help="Synchronize your calendar with Google Calendar" id="sync_google_calendar_setting">
                            <field name="module_google_calendar"/>
                            <div class="content-group" invisible="not module_google_calendar" id="msg_module_google_calendar">
                                <div class="text-warning mt16"><strong>Save</strong> this page and come back here to set up the feature.</div>
                            </div>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>
</odoo>
