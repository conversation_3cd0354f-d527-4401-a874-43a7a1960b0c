<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="winreg — Windows registry access" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/winreg.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="These functions expose the Windows registry API to Python. Instead of using an integer as the registry handle, a handle object is used to ensure that the handles are closed correctly, even if the p..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="These functions expose the Windows registry API to Python. Instead of using an integer as the registry handle, a handle object is used to ensure that the handles are closed correctly, even if the p..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>winreg — Windows registry access &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="winsound — Sound-playing interface for Windows" href="winsound.html" />
    <link rel="prev" title="msvcrt — Useful routines from the MS VC++ runtime" href="msvcrt.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/winreg.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#constants">Constants</a><ul>
<li><a class="reference internal" href="#hkey-constants">HKEY_* Constants</a></li>
<li><a class="reference internal" href="#access-rights">Access Rights</a><ul>
<li><a class="reference internal" href="#bit-specific">64-bit Specific</a></li>
</ul>
</li>
<li><a class="reference internal" href="#value-types">Value Types</a></li>
</ul>
</li>
<li><a class="reference internal" href="#registry-handle-objects">Registry Handle Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="msvcrt.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code> — Useful routines from the MS VC++ runtime</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="winsound.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winsound</span></code> — Sound-playing interface for Windows</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/winreg.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="winsound.html" title="winsound — Sound-playing interface for Windows"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="msvcrt.html" title="msvcrt — Useful routines from the MS VC++ runtime"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="windows.html" accesskey="U">MS Windows Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-winreg">
<span id="winreg-windows-registry-access"></span><h1><a class="reference internal" href="#module-winreg" title="winreg: Routines and objects for manipulating the Windows registry. (Windows)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code></a> — Windows registry access<a class="headerlink" href="#module-winreg" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>These functions expose the Windows registry API to Python.  Instead of using an
integer as the registry handle, a <a class="reference internal" href="#handle-object"><span class="std std-ref">handle object</span></a> is used
to ensure that the handles are closed correctly, even if the programmer neglects
to explicitly close them.</p>
<div class="versionchanged" id="exception-changed">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Several functions in this module used to raise a
<a class="reference internal" href="exceptions.html#WindowsError" title="WindowsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">WindowsError</span></code></a>, which is now an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
<section id="functions">
<span id="id1"></span><h2>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h2>
<p>This module offers the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="winreg.CloseKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">CloseKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hkey</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.CloseKey" title="Link to this definition">¶</a></dt>
<dd><p>Closes a previously opened registry key.  The <em>hkey</em> argument specifies a
previously opened key.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <em>hkey</em> is not closed using this method (or via <a class="reference internal" href="#winreg.PyHKEY.Close" title="winreg.PyHKEY.Close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hkey.Close()</span></code></a>), it is closed when the <em>hkey</em> object is destroyed by
Python.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.ConnectRegistry">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">ConnectRegistry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">computer_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.ConnectRegistry" title="Link to this definition">¶</a></dt>
<dd><p>Establishes a connection to a predefined registry handle on another computer,
and returns a <a class="reference internal" href="#handle-object"><span class="std std-ref">handle object</span></a>.</p>
<p><em>computer_name</em> is the name of the remote computer, of the form
<code class="docutils literal notranslate"><span class="pre">r&quot;\\computername&quot;</span></code>.  If <code class="docutils literal notranslate"><span class="pre">None</span></code>, the local computer is used.</p>
<p><em>key</em> is the predefined handle to connect to.</p>
<p>The return value is the handle of the opened key. If the function fails, an
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is raised.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.ConnectRegistry</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">computer_name</span></code>, <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.CreateKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">CreateKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.CreateKey" title="Link to this definition">¶</a></dt>
<dd><p>Creates or opens the specified key, returning a
<a class="reference internal" href="#handle-object"><span class="std std-ref">handle object</span></a>.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that names the key this method opens or creates.</p>
<p>If <em>key</em> is one of the predefined keys, <em>sub_key</em> may be <code class="docutils literal notranslate"><span class="pre">None</span></code>. In that
case, the handle returned is the same key handle passed in to the function.</p>
<p>If the key already exists, this function opens the existing key.</p>
<p>The return value is the handle of the opened key. If the function fails, an
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is raised.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.CreateKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.OpenKey/result</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.CreateKeyEx">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">CreateKeyEx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reserved</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">access</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">KEY_WRITE</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.CreateKeyEx" title="Link to this definition">¶</a></dt>
<dd><p>Creates or opens the specified key, returning a
<a class="reference internal" href="#handle-object"><span class="std std-ref">handle object</span></a>.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that names the key this method opens or creates.</p>
<p><em>reserved</em> is a reserved integer, and must be zero. The default is zero.</p>
<p><em>access</em> is an integer that specifies an access mask that describes the desired
security access for the key.  Default is <a class="reference internal" href="#winreg.KEY_WRITE" title="winreg.KEY_WRITE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_WRITE</span></code></a>.  See
<a class="reference internal" href="#access-rights"><span class="std std-ref">Access Rights</span></a> for other allowed values.</p>
<p>If <em>key</em> is one of the predefined keys, <em>sub_key</em> may be <code class="docutils literal notranslate"><span class="pre">None</span></code>. In that
case, the handle returned is the same key handle passed in to the function.</p>
<p>If the key already exists, this function opens the existing key.</p>
<p>The return value is the handle of the opened key. If the function fails, an
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is raised.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.CreateKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.OpenKey/result</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.DeleteKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">DeleteKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.DeleteKey" title="Link to this definition">¶</a></dt>
<dd><p>Deletes the specified key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that must be a subkey of the key identified by the <em>key</em>
parameter.  This value must not be <code class="docutils literal notranslate"><span class="pre">None</span></code>, and the key may not have subkeys.</p>
<p><em>This method can not delete keys with subkeys.</em></p>
<p>If the method succeeds, the entire key, including all of its values, is removed.
If the method fails, an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is raised.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.DeleteKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.DeleteKeyEx">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">DeleteKeyEx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">access</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">KEY_WOW64_64KEY</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reserved</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.DeleteKeyEx" title="Link to this definition">¶</a></dt>
<dd><p>Deletes the specified key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that must be a subkey of the key identified by the
<em>key</em> parameter. This value must not be <code class="docutils literal notranslate"><span class="pre">None</span></code>, and the key may not have
subkeys.</p>
<p><em>reserved</em> is a reserved integer, and must be zero. The default is zero.</p>
<p><em>access</em> is an integer that specifies an access mask that describes the
desired security access for the key.  Default is <a class="reference internal" href="#winreg.KEY_WOW64_64KEY" title="winreg.KEY_WOW64_64KEY"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_WOW64_64KEY</span></code></a>.
On 32-bit Windows, the WOW64 constants are ignored.
See <a class="reference internal" href="#access-rights"><span class="std std-ref">Access Rights</span></a> for other allowed values.</p>
<p><em>This method can not delete keys with subkeys.</em></p>
<p>If the method succeeds, the entire key, including all of its values, is
removed. If the method fails, an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is raised.</p>
<p>On unsupported Windows versions, <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> is raised.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.DeleteKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.DeleteValue">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">DeleteValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.DeleteValue" title="Link to this definition">¶</a></dt>
<dd><p>Removes a named value from a registry key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>value</em> is a string that identifies the value to remove.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.DeleteValue</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.EnumKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">EnumKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.EnumKey" title="Link to this definition">¶</a></dt>
<dd><p>Enumerates subkeys of an open registry key, returning a string.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>index</em> is an integer that identifies the index of the key to retrieve.</p>
<p>The function retrieves the name of one subkey each time it is called.  It is
typically called repeatedly until an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is
raised, indicating, no more values are available.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.EnumKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">index</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.EnumValue">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">EnumValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.EnumValue" title="Link to this definition">¶</a></dt>
<dd><p>Enumerates values of an open registry key, returning a tuple.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>index</em> is an integer that identifies the index of the value to retrieve.</p>
<p>The function retrieves the name of one subkey each time it is called. It is
typically called repeatedly, until an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception is
raised, indicating no more values.</p>
<p>The result is a tuple of 3 items:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Index</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
<td><p>A string that identifies the value name</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p>An object that holds the value data, and
whose type depends on the underlying
registry type</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p>An integer that identifies the type of the
value data (see table in docs for
<a class="reference internal" href="#winreg.SetValueEx" title="winreg.SetValueEx"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetValueEx()</span></code></a>)</p></td>
</tr>
</tbody>
</table>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.EnumValue</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">index</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function" id="index-0">
<dt class="sig sig-object py" id="winreg.ExpandEnvironmentStrings">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">ExpandEnvironmentStrings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.ExpandEnvironmentStrings" title="Link to this definition">¶</a></dt>
<dd><p>Expands environment variable placeholders <code class="docutils literal notranslate"><span class="pre">%NAME%</span></code> in strings like
<a class="reference internal" href="#winreg.REG_EXPAND_SZ" title="winreg.REG_EXPAND_SZ"><code class="xref py py-const docutils literal notranslate"><span class="pre">REG_EXPAND_SZ</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ExpandEnvironmentStrings</span><span class="p">(</span><span class="s1">&#39;%windir%&#39;</span><span class="p">)</span>
<span class="go">&#39;C:\\Windows&#39;</span>
</pre></div>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.ExpandEnvironmentStrings</span></code> with argument <code class="docutils literal notranslate"><span class="pre">str</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.FlushKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">FlushKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.FlushKey" title="Link to this definition">¶</a></dt>
<dd><p>Writes all the attributes of a key to the registry.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p>It is not necessary to call <a class="reference internal" href="#winreg.FlushKey" title="winreg.FlushKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">FlushKey()</span></code></a> to change a key. Registry changes are
flushed to disk by the registry using its lazy flusher.  Registry changes are
also flushed to disk at system shutdown.  Unlike <a class="reference internal" href="#winreg.CloseKey" title="winreg.CloseKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">CloseKey()</span></code></a>, the
<a class="reference internal" href="#winreg.FlushKey" title="winreg.FlushKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">FlushKey()</span></code></a> method returns only when all the data has been written to the
registry. An application should only call <a class="reference internal" href="#winreg.FlushKey" title="winreg.FlushKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">FlushKey()</span></code></a> if it requires
absolute certainty that registry changes are on disk.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you don’t know whether a <a class="reference internal" href="#winreg.FlushKey" title="winreg.FlushKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">FlushKey()</span></code></a> call is required, it probably
isn’t.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.LoadKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">LoadKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.LoadKey" title="Link to this definition">¶</a></dt>
<dd><p>Creates a subkey under the specified key and stores registration information
from a specified file into that subkey.</p>
<p><em>key</em> is a handle returned by <a class="reference internal" href="#winreg.ConnectRegistry" title="winreg.ConnectRegistry"><code class="xref py py-func docutils literal notranslate"><span class="pre">ConnectRegistry()</span></code></a> or one of the constants
<a class="reference internal" href="#winreg.HKEY_USERS" title="winreg.HKEY_USERS"><code class="xref py py-const docutils literal notranslate"><span class="pre">HKEY_USERS</span></code></a> or <a class="reference internal" href="#winreg.HKEY_LOCAL_MACHINE" title="winreg.HKEY_LOCAL_MACHINE"><code class="xref py py-const docutils literal notranslate"><span class="pre">HKEY_LOCAL_MACHINE</span></code></a>.</p>
<p><em>sub_key</em> is a string that identifies the subkey to load.</p>
<p><em>file_name</em> is the name of the file to load registry data from. This file must
have been created with the <a class="reference internal" href="#winreg.SaveKey" title="winreg.SaveKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">SaveKey()</span></code></a> function. Under the file allocation
table (FAT) file system, the filename may not have an extension.</p>
<p>A call to <a class="reference internal" href="#winreg.LoadKey" title="winreg.LoadKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">LoadKey()</span></code></a> fails if the calling process does not have the
<code class="xref c c-data docutils literal notranslate"><span class="pre">SE_RESTORE_PRIVILEGE</span></code> privilege.  Note that privileges are different
from permissions – see the <a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms724889%28v=VS.85%29.aspx">RegLoadKey documentation</a> for
more details.</p>
<p>If <em>key</em> is a handle returned by <a class="reference internal" href="#winreg.ConnectRegistry" title="winreg.ConnectRegistry"><code class="xref py py-func docutils literal notranslate"><span class="pre">ConnectRegistry()</span></code></a>, then the path
specified in <em>file_name</em> is relative to the remote computer.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.LoadKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">file_name</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.OpenKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">OpenKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reserved</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">access</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">KEY_READ</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.OpenKey" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="winreg.OpenKeyEx">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">OpenKeyEx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reserved</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">access</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">KEY_READ</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.OpenKeyEx" title="Link to this definition">¶</a></dt>
<dd><p>Opens the specified key, returning a <a class="reference internal" href="#handle-object"><span class="std std-ref">handle object</span></a>.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that identifies the sub_key to open.</p>
<p><em>reserved</em> is a reserved integer, and must be zero.  The default is zero.</p>
<p><em>access</em> is an integer that specifies an access mask that describes the desired
security access for the key.  Default is <a class="reference internal" href="#winreg.KEY_READ" title="winreg.KEY_READ"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_READ</span></code></a>.  See <a class="reference internal" href="#access-rights"><span class="std std-ref">Access
Rights</span></a> for other allowed values.</p>
<p>The result is a new handle to the specified key.</p>
<p>If the function fails, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.OpenKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">access</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.OpenKey/result</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Allow the use of named arguments.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>See <a class="reference internal" href="#exception-changed"><span class="std std-ref">above</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.QueryInfoKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">QueryInfoKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.QueryInfoKey" title="Link to this definition">¶</a></dt>
<dd><p>Returns information about a key, as a tuple.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p>The result is a tuple of 3 items:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Index</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
<td><p>An integer giving the number of sub keys
this key has.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p>An integer giving the number of values this
key has.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p>An integer giving when the key was last
modified (if available) as 100’s of
nanoseconds since Jan 1, 1601.</p></td>
</tr>
</tbody>
</table>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.QueryInfoKey</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.QueryValue">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">QueryValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.QueryValue" title="Link to this definition">¶</a></dt>
<dd><p>Retrieves the unnamed value for a key, as a string.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that holds the name of the subkey with which the value is
associated.  If this parameter is <code class="docutils literal notranslate"><span class="pre">None</span></code> or empty, the function retrieves the
value set by the <a class="reference internal" href="#winreg.SetValue" title="winreg.SetValue"><code class="xref py py-func docutils literal notranslate"><span class="pre">SetValue()</span></code></a> method for the key identified by <em>key</em>.</p>
<p>Values in the registry have name, type, and data components. This method
retrieves the data for a key’s first value that has a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> name. But the
underlying API call doesn’t return the type, so always use
<a class="reference internal" href="#winreg.QueryValueEx" title="winreg.QueryValueEx"><code class="xref py py-func docutils literal notranslate"><span class="pre">QueryValueEx()</span></code></a> if possible.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.QueryValue</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">value_name</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.QueryValueEx">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">QueryValueEx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.QueryValueEx" title="Link to this definition">¶</a></dt>
<dd><p>Retrieves the type and data for a specified value name associated with
an open registry key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>value_name</em> is a string indicating the value to query.</p>
<p>The result is a tuple of 2 items:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Index</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
<td><p>The value of the registry item.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p>An integer giving the registry type for
this value (see table in docs for
<a class="reference internal" href="#winreg.SetValueEx" title="winreg.SetValueEx"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetValueEx()</span></code></a>)</p></td>
</tr>
</tbody>
</table>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.QueryValue</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">value_name</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.SaveKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">SaveKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.SaveKey" title="Link to this definition">¶</a></dt>
<dd><p>Saves the specified key, and all its subkeys to the specified file.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>file_name</em> is the name of the file to save registry data to.  This file
cannot already exist. If this filename includes an extension, it cannot be
used on file allocation table (FAT) file systems by the <a class="reference internal" href="#winreg.LoadKey" title="winreg.LoadKey"><code class="xref py py-meth docutils literal notranslate"><span class="pre">LoadKey()</span></code></a>
method.</p>
<p>If <em>key</em> represents a key on a remote computer, the path described by
<em>file_name</em> is relative to the remote computer. The caller of this method must
possess the <strong>SeBackupPrivilege</strong> security privilege.  Note that
privileges are different than permissions – see the
<a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms724878%28v=VS.85%29.aspx">Conflicts Between User Rights and Permissions documentation</a>
for more details.</p>
<p>This function passes <code class="docutils literal notranslate"><span class="pre">NULL</span></code> for <em>security_attributes</em> to the API.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.SaveKey</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">file_name</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.SetValue">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">SetValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.SetValue" title="Link to this definition">¶</a></dt>
<dd><p>Associates a value with a specified key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>sub_key</em> is a string that names the subkey with which the value is associated.</p>
<p><em>type</em> is an integer that specifies the type of the data. Currently this must be
<a class="reference internal" href="#winreg.REG_SZ" title="winreg.REG_SZ"><code class="xref py py-const docutils literal notranslate"><span class="pre">REG_SZ</span></code></a>, meaning only strings are supported.  Use the <a class="reference internal" href="#winreg.SetValueEx" title="winreg.SetValueEx"><code class="xref py py-func docutils literal notranslate"><span class="pre">SetValueEx()</span></code></a>
function for support for other data types.</p>
<p><em>value</em> is a string that specifies the new value.</p>
<p>If the key specified by the <em>sub_key</em> parameter does not exist, the SetValue
function creates it.</p>
<p>Value lengths are limited by available memory. Long values (more than 2048
bytes) should be stored as files with the filenames stored in the configuration
registry.  This helps the registry perform efficiently.</p>
<p>The key identified by the <em>key</em> parameter must have been opened with
<a class="reference internal" href="#winreg.KEY_SET_VALUE" title="winreg.KEY_SET_VALUE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_SET_VALUE</span></code></a> access.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.SetValue</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">type</span></code>, <code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.SetValueEx">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">SetValueEx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reserved</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.SetValueEx" title="Link to this definition">¶</a></dt>
<dd><p>Stores data in the value field of an open registry key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p><em>value_name</em> is a string that names the subkey with which the value is
associated.</p>
<p><em>reserved</em> can be anything – zero is always passed to the API.</p>
<p><em>type</em> is an integer that specifies the type of the data. See
<a class="reference internal" href="#value-types"><span class="std std-ref">Value Types</span></a> for the available types.</p>
<p><em>value</em> is a string that specifies the new value.</p>
<p>This method can also set additional value and type information for the specified
key.  The key identified by the key parameter must have been opened with
<a class="reference internal" href="#winreg.KEY_SET_VALUE" title="winreg.KEY_SET_VALUE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_SET_VALUE</span></code></a> access.</p>
<p>To open the key, use the <a class="reference internal" href="#winreg.CreateKey" title="winreg.CreateKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">CreateKey()</span></code></a> or <a class="reference internal" href="#winreg.OpenKey" title="winreg.OpenKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">OpenKey()</span></code></a> methods.</p>
<p>Value lengths are limited by available memory. Long values (more than 2048
bytes) should be stored as files with the filenames stored in the configuration
registry.  This helps the registry perform efficiently.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.SetValue</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">key</span></code>, <code class="docutils literal notranslate"><span class="pre">sub_key</span></code>, <code class="docutils literal notranslate"><span class="pre">type</span></code>, <code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.DisableReflectionKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">DisableReflectionKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.DisableReflectionKey" title="Link to this definition">¶</a></dt>
<dd><p>Disables registry reflection for 32-bit processes running on a 64-bit
operating system.</p>
<p><em>key</em> is an already open key, or one of the predefined <a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p>Will generally raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> if executed on a 32-bit operating
system.</p>
<p>If the key is not on the reflection list, the function succeeds but has no
effect.  Disabling reflection for a key does not affect reflection of any
subkeys.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.DisableReflectionKey</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.EnableReflectionKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">EnableReflectionKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.EnableReflectionKey" title="Link to this definition">¶</a></dt>
<dd><p>Restores registry reflection for the specified disabled key.</p>
<p><em>key</em> is an already open key, or one of the predefined <a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p>Will generally raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> if executed on a 32-bit operating
system.</p>
<p>Restoring reflection for a key does not affect reflection of any subkeys.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.EnableReflectionKey</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winreg.QueryReflectionKey">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">QueryReflectionKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.QueryReflectionKey" title="Link to this definition">¶</a></dt>
<dd><p>Determines the reflection state for the specified key.</p>
<p><em>key</em> is an already open key, or one of the predefined
<a class="reference internal" href="#hkey-constants"><span class="std std-ref">HKEY_* constants</span></a>.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if reflection is disabled.</p>
<p>Will generally raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> if executed on a 32-bit
operating system.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.QueryReflectionKey</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>

</section>
<section id="constants">
<span id="id2"></span><h2>Constants<a class="headerlink" href="#constants" title="Link to this heading">¶</a></h2>
<p>The following constants are defined for use in many <a class="reference internal" href="#module-winreg" title="winreg: Routines and objects for manipulating the Windows registry. (Windows)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code></a> functions.</p>
<section id="hkey-constants">
<span id="id3"></span><h3>HKEY_* Constants<a class="headerlink" href="#hkey-constants" title="Link to this heading">¶</a></h3>
<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_CLASSES_ROOT">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_CLASSES_ROOT</span></span><a class="headerlink" href="#winreg.HKEY_CLASSES_ROOT" title="Link to this definition">¶</a></dt>
<dd><p>Registry entries subordinate to this key define types (or classes) of
documents and the properties associated with those types. Shell and
COM applications use the information stored under this key.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_CURRENT_USER">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_CURRENT_USER</span></span><a class="headerlink" href="#winreg.HKEY_CURRENT_USER" title="Link to this definition">¶</a></dt>
<dd><p>Registry entries subordinate to this key define the preferences of
the current user. These preferences include the settings of
environment variables, data about program groups, colors, printers,
network connections, and application preferences.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_LOCAL_MACHINE">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_LOCAL_MACHINE</span></span><a class="headerlink" href="#winreg.HKEY_LOCAL_MACHINE" title="Link to this definition">¶</a></dt>
<dd><p>Registry entries subordinate to this key define the physical state
of the computer, including data about the bus type, system memory,
and installed hardware and software.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_USERS">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_USERS</span></span><a class="headerlink" href="#winreg.HKEY_USERS" title="Link to this definition">¶</a></dt>
<dd><p>Registry entries subordinate to this key define the default user
configuration for new users on the local computer and the user
configuration for the current user.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_PERFORMANCE_DATA">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_PERFORMANCE_DATA</span></span><a class="headerlink" href="#winreg.HKEY_PERFORMANCE_DATA" title="Link to this definition">¶</a></dt>
<dd><p>Registry entries subordinate to this key allow you to access
performance data. The data is not actually stored in the registry;
the registry functions cause the system to collect the data from
its source.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_CURRENT_CONFIG">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_CURRENT_CONFIG</span></span><a class="headerlink" href="#winreg.HKEY_CURRENT_CONFIG" title="Link to this definition">¶</a></dt>
<dd><p>Contains information about the current hardware profile of the
local computer system.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.HKEY_DYN_DATA">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">HKEY_DYN_DATA</span></span><a class="headerlink" href="#winreg.HKEY_DYN_DATA" title="Link to this definition">¶</a></dt>
<dd><p>This key is not used in versions of Windows after 98.</p>
</dd></dl>

</section>
<section id="access-rights">
<span id="id4"></span><h3>Access Rights<a class="headerlink" href="#access-rights" title="Link to this heading">¶</a></h3>
<p>For more information, see <a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms724878%28v=VS.85%29.aspx">Registry Key Security and Access</a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_ALL_ACCESS">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_ALL_ACCESS</span></span><a class="headerlink" href="#winreg.KEY_ALL_ACCESS" title="Link to this definition">¶</a></dt>
<dd><p>Combines the STANDARD_RIGHTS_REQUIRED, <a class="reference internal" href="#winreg.KEY_QUERY_VALUE" title="winreg.KEY_QUERY_VALUE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_QUERY_VALUE</span></code></a>,
<a class="reference internal" href="#winreg.KEY_SET_VALUE" title="winreg.KEY_SET_VALUE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_SET_VALUE</span></code></a>, <a class="reference internal" href="#winreg.KEY_CREATE_SUB_KEY" title="winreg.KEY_CREATE_SUB_KEY"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_CREATE_SUB_KEY</span></code></a>,
<a class="reference internal" href="#winreg.KEY_ENUMERATE_SUB_KEYS" title="winreg.KEY_ENUMERATE_SUB_KEYS"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_ENUMERATE_SUB_KEYS</span></code></a>, <a class="reference internal" href="#winreg.KEY_NOTIFY" title="winreg.KEY_NOTIFY"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_NOTIFY</span></code></a>,
and <a class="reference internal" href="#winreg.KEY_CREATE_LINK" title="winreg.KEY_CREATE_LINK"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_CREATE_LINK</span></code></a> access rights.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_WRITE">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_WRITE</span></span><a class="headerlink" href="#winreg.KEY_WRITE" title="Link to this definition">¶</a></dt>
<dd><p>Combines the STANDARD_RIGHTS_WRITE, <a class="reference internal" href="#winreg.KEY_SET_VALUE" title="winreg.KEY_SET_VALUE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_SET_VALUE</span></code></a>, and
<a class="reference internal" href="#winreg.KEY_CREATE_SUB_KEY" title="winreg.KEY_CREATE_SUB_KEY"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_CREATE_SUB_KEY</span></code></a> access rights.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_READ">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_READ</span></span><a class="headerlink" href="#winreg.KEY_READ" title="Link to this definition">¶</a></dt>
<dd><p>Combines the STANDARD_RIGHTS_READ, <a class="reference internal" href="#winreg.KEY_QUERY_VALUE" title="winreg.KEY_QUERY_VALUE"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_QUERY_VALUE</span></code></a>,
<a class="reference internal" href="#winreg.KEY_ENUMERATE_SUB_KEYS" title="winreg.KEY_ENUMERATE_SUB_KEYS"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_ENUMERATE_SUB_KEYS</span></code></a>, and <a class="reference internal" href="#winreg.KEY_NOTIFY" title="winreg.KEY_NOTIFY"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_NOTIFY</span></code></a> values.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_EXECUTE">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_EXECUTE</span></span><a class="headerlink" href="#winreg.KEY_EXECUTE" title="Link to this definition">¶</a></dt>
<dd><p>Equivalent to <a class="reference internal" href="#winreg.KEY_READ" title="winreg.KEY_READ"><code class="xref py py-const docutils literal notranslate"><span class="pre">KEY_READ</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_QUERY_VALUE">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_QUERY_VALUE</span></span><a class="headerlink" href="#winreg.KEY_QUERY_VALUE" title="Link to this definition">¶</a></dt>
<dd><p>Required to query the values of a registry key.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_SET_VALUE">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_SET_VALUE</span></span><a class="headerlink" href="#winreg.KEY_SET_VALUE" title="Link to this definition">¶</a></dt>
<dd><p>Required to create, delete, or set a registry value.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_CREATE_SUB_KEY">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_CREATE_SUB_KEY</span></span><a class="headerlink" href="#winreg.KEY_CREATE_SUB_KEY" title="Link to this definition">¶</a></dt>
<dd><p>Required to create a subkey of a registry key.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_ENUMERATE_SUB_KEYS">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_ENUMERATE_SUB_KEYS</span></span><a class="headerlink" href="#winreg.KEY_ENUMERATE_SUB_KEYS" title="Link to this definition">¶</a></dt>
<dd><p>Required to enumerate the subkeys of a registry key.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_NOTIFY">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_NOTIFY</span></span><a class="headerlink" href="#winreg.KEY_NOTIFY" title="Link to this definition">¶</a></dt>
<dd><p>Required to request change notifications for a registry key or for
subkeys of a registry key.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_CREATE_LINK">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_CREATE_LINK</span></span><a class="headerlink" href="#winreg.KEY_CREATE_LINK" title="Link to this definition">¶</a></dt>
<dd><p>Reserved for system use.</p>
</dd></dl>

<section id="bit-specific">
<span id="bit-access-rights"></span><h4>64-bit Specific<a class="headerlink" href="#bit-specific" title="Link to this heading">¶</a></h4>
<p>For more information, see <a class="reference external" href="https://msdn.microsoft.com/en-us/library/aa384129(v=VS.85).aspx">Accessing an Alternate Registry View</a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_WOW64_64KEY">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_WOW64_64KEY</span></span><a class="headerlink" href="#winreg.KEY_WOW64_64KEY" title="Link to this definition">¶</a></dt>
<dd><p>Indicates that an application on 64-bit Windows should operate on
the 64-bit registry view. On 32-bit Windows, this constant is ignored.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.KEY_WOW64_32KEY">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">KEY_WOW64_32KEY</span></span><a class="headerlink" href="#winreg.KEY_WOW64_32KEY" title="Link to this definition">¶</a></dt>
<dd><p>Indicates that an application on 64-bit Windows should operate on
the 32-bit registry view. On 32-bit Windows, this constant is ignored.</p>
</dd></dl>

</section>
</section>
<section id="value-types">
<span id="id5"></span><h3>Value Types<a class="headerlink" href="#value-types" title="Link to this heading">¶</a></h3>
<p>For more information, see <a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms724884%28v=VS.85%29.aspx">Registry Value Types</a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_BINARY">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_BINARY</span></span><a class="headerlink" href="#winreg.REG_BINARY" title="Link to this definition">¶</a></dt>
<dd><p>Binary data in any form.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_DWORD">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_DWORD</span></span><a class="headerlink" href="#winreg.REG_DWORD" title="Link to this definition">¶</a></dt>
<dd><p>32-bit number.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_DWORD_LITTLE_ENDIAN">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_DWORD_LITTLE_ENDIAN</span></span><a class="headerlink" href="#winreg.REG_DWORD_LITTLE_ENDIAN" title="Link to this definition">¶</a></dt>
<dd><p>A 32-bit number in little-endian format. Equivalent to <a class="reference internal" href="#winreg.REG_DWORD" title="winreg.REG_DWORD"><code class="xref py py-const docutils literal notranslate"><span class="pre">REG_DWORD</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_DWORD_BIG_ENDIAN">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_DWORD_BIG_ENDIAN</span></span><a class="headerlink" href="#winreg.REG_DWORD_BIG_ENDIAN" title="Link to this definition">¶</a></dt>
<dd><p>A 32-bit number in big-endian format.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_EXPAND_SZ">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_EXPAND_SZ</span></span><a class="headerlink" href="#winreg.REG_EXPAND_SZ" title="Link to this definition">¶</a></dt>
<dd><p>Null-terminated string containing references to environment
variables (<code class="docutils literal notranslate"><span class="pre">%PATH%</span></code>).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_LINK">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_LINK</span></span><a class="headerlink" href="#winreg.REG_LINK" title="Link to this definition">¶</a></dt>
<dd><p>A Unicode symbolic link.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_MULTI_SZ">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_MULTI_SZ</span></span><a class="headerlink" href="#winreg.REG_MULTI_SZ" title="Link to this definition">¶</a></dt>
<dd><p>A sequence of null-terminated strings, terminated by two null characters.
(Python handles this termination automatically.)</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_NONE">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_NONE</span></span><a class="headerlink" href="#winreg.REG_NONE" title="Link to this definition">¶</a></dt>
<dd><p>No defined value type.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_QWORD">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_QWORD</span></span><a class="headerlink" href="#winreg.REG_QWORD" title="Link to this definition">¶</a></dt>
<dd><p>A 64-bit number.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_QWORD_LITTLE_ENDIAN">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_QWORD_LITTLE_ENDIAN</span></span><a class="headerlink" href="#winreg.REG_QWORD_LITTLE_ENDIAN" title="Link to this definition">¶</a></dt>
<dd><p>A 64-bit number in little-endian format. Equivalent to <a class="reference internal" href="#winreg.REG_QWORD" title="winreg.REG_QWORD"><code class="xref py py-const docutils literal notranslate"><span class="pre">REG_QWORD</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_RESOURCE_LIST">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_RESOURCE_LIST</span></span><a class="headerlink" href="#winreg.REG_RESOURCE_LIST" title="Link to this definition">¶</a></dt>
<dd><p>A device-driver resource list.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_FULL_RESOURCE_DESCRIPTOR">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_FULL_RESOURCE_DESCRIPTOR</span></span><a class="headerlink" href="#winreg.REG_FULL_RESOURCE_DESCRIPTOR" title="Link to this definition">¶</a></dt>
<dd><p>A hardware setting.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_RESOURCE_REQUIREMENTS_LIST">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_RESOURCE_REQUIREMENTS_LIST</span></span><a class="headerlink" href="#winreg.REG_RESOURCE_REQUIREMENTS_LIST" title="Link to this definition">¶</a></dt>
<dd><p>A hardware resource list.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winreg.REG_SZ">
<span class="sig-prename descclassname"><span class="pre">winreg.</span></span><span class="sig-name descname"><span class="pre">REG_SZ</span></span><a class="headerlink" href="#winreg.REG_SZ" title="Link to this definition">¶</a></dt>
<dd><p>A null-terminated string.</p>
</dd></dl>

</section>
</section>
<section id="registry-handle-objects">
<span id="handle-object"></span><h2>Registry Handle Objects<a class="headerlink" href="#registry-handle-objects" title="Link to this heading">¶</a></h2>
<p>This object wraps a Windows HKEY object, automatically closing it when the
object is destroyed.  To guarantee cleanup, you can call either the
<a class="reference internal" href="#winreg.PyHKEY.Close" title="winreg.PyHKEY.Close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Close()</span></code></a> method on the object, or the <a class="reference internal" href="#winreg.CloseKey" title="winreg.CloseKey"><code class="xref py py-func docutils literal notranslate"><span class="pre">CloseKey()</span></code></a> function.</p>
<p>All registry functions in this module return one of these objects.</p>
<p>All registry functions in this module which accept a handle object also accept
an integer, however, use of the handle object is encouraged.</p>
<p>Handle objects provide semantics for <a class="reference internal" href="../reference/datamodel.html#object.__bool__" title="object.__bool__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__bool__()</span></code></a> – thus</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">handle</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Yes&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>will print <code class="docutils literal notranslate"><span class="pre">Yes</span></code> if the handle is currently valid (has not been closed or
detached).</p>
<p>The object also support comparison semantics, so handle objects will compare
true if they both reference the same underlying Windows handle value.</p>
<p>Handle objects can be converted to an integer (e.g., using the built-in
<a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> function), in which case the underlying Windows handle value is
returned.  You can also use the <a class="reference internal" href="#winreg.PyHKEY.Detach" title="winreg.PyHKEY.Detach"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Detach()</span></code></a> method to return the
integer handle, and also disconnect the Windows handle from the handle object.</p>
<dl class="py method">
<dt class="sig sig-object py" id="winreg.PyHKEY.Close">
<span class="sig-prename descclassname"><span class="pre">PyHKEY.</span></span><span class="sig-name descname"><span class="pre">Close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#winreg.PyHKEY.Close" title="Link to this definition">¶</a></dt>
<dd><p>Closes the underlying Windows handle.</p>
<p>If the handle is already closed, no error is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="winreg.PyHKEY.Detach">
<span class="sig-prename descclassname"><span class="pre">PyHKEY.</span></span><span class="sig-name descname"><span class="pre">Detach</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#winreg.PyHKEY.Detach" title="Link to this definition">¶</a></dt>
<dd><p>Detaches the Windows handle from the handle object.</p>
<p>The result is an integer that holds the value of the handle before it is
detached.  If the handle is already detached or closed, this will return
zero.</p>
<p>After calling this function, the handle is effectively invalidated, but the
handle is not closed.  You would call this function when you need the
underlying Win32 handle to exist beyond the lifetime of the handle object.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">winreg.PyHKEY.Detach</span></code> with argument <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="winreg.PyHKEY.__enter__">
<span class="sig-prename descclassname"><span class="pre">PyHKEY.</span></span><span class="sig-name descname"><span class="pre">__enter__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#winreg.PyHKEY.__enter__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="winreg.PyHKEY.__exit__">
<span class="sig-prename descclassname"><span class="pre">PyHKEY.</span></span><span class="sig-name descname"><span class="pre">__exit__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">exc_info</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winreg.PyHKEY.__exit__" title="Link to this definition">¶</a></dt>
<dd><p>The HKEY object implements <a class="reference internal" href="../reference/datamodel.html#object.__enter__" title="object.__enter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__enter__()</span></code></a> and
<a class="reference internal" href="../reference/datamodel.html#object.__exit__" title="object.__exit__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__exit__()</span></code></a> and thus supports the context protocol for the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">OpenKey</span><span class="p">(</span><span class="n">HKEY_LOCAL_MACHINE</span><span class="p">,</span> <span class="s2">&quot;foo&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">key</span><span class="p">:</span>
    <span class="o">...</span>  <span class="c1"># work with key</span>
</pre></div>
</div>
<p>will automatically close <em>key</em> when control leaves the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> block.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#constants">Constants</a><ul>
<li><a class="reference internal" href="#hkey-constants">HKEY_* Constants</a></li>
<li><a class="reference internal" href="#access-rights">Access Rights</a><ul>
<li><a class="reference internal" href="#bit-specific">64-bit Specific</a></li>
</ul>
</li>
<li><a class="reference internal" href="#value-types">Value Types</a></li>
</ul>
</li>
<li><a class="reference internal" href="#registry-handle-objects">Registry Handle Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="msvcrt.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code> — Useful routines from the MS VC++ runtime</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="winsound.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winsound</span></code> — Sound-playing interface for Windows</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/winreg.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="winsound.html" title="winsound — Sound-playing interface for Windows"
             >next</a> |</li>
        <li class="right" >
          <a href="msvcrt.html" title="msvcrt — Useful routines from the MS VC++ runtime"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="windows.html" >MS Windows Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>