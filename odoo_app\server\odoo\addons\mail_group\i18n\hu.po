# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_group
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# krn<PERSON>ris, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: gezza <<EMAIL>>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid ") <span invisible=\"not send_email\">and send them an email</span>."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "<br/> You'll be notified as soon as some new content is posted."
msgstr ""

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_subscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br><br>\n"
"                You have requested to be subscribed to the mailing list <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>\n"
"                <br><br>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_unsubscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br><br>\n"
"                You have requested to be unsubscribed to the mailing list <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>.\n"
"                <br><br>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_guidelines
msgid ""
"<div>\n"
"                <p>Hello <t t-out=\"object.partner_id.name or ''\"></t>,</p>\n"
"                <p>Please find below the guidelines of the <t t-out=\"object.mail_group_id.name\"></t> mailing list.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"></t></p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_name
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"Previous message\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"Next message\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"Show attachments\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"Show replies\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"Hide attachments\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"Hide replies\"/>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "<span class=\"bg-warning\">Pending</span>"
msgstr "<span class=\"bg-warning\">Függőben</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">Whitelisted</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">Banned</span>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<span class=\"mx-2\">-</span>"
msgstr "<span class=\"mx-2\">-</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "<span>By thread</span>"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<span>No Mail Group yet.</span>\n"
"                    <br/>"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Egy Python szótár, ami kiértékelésre kerül az alapértelmezett értékek "
"szolgáltatása érdekében ezen álnév új rekordjainak létrehozásakor."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Accept"
msgstr "Elfogad"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__accepted
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Accepted"
msgstr "Elfogadott"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__action
msgid "Action"
msgstr "Akció"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__active
msgid "Active"
msgstr "Aktív"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Add this email address to white list of people and accept all pending "
"messages from the same author."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Alias"
msgstr "Álnév"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_contact
msgid "Alias Contact Security"
msgstr "Álnév kapcsolat biztonság"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain_id
msgid "Alias Domain"
msgstr "Tartomány álnév"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain
msgid "Alias Domain Name"
msgstr "Alias domain név"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_full_name
msgid "Alias Email"
msgstr "Álnév email"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_name
msgid "Alias Name"
msgstr "Álnév elnevezése"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_status
msgid "Alias Status"
msgstr "Álnév állapot"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_model_id
msgid "Aliased Model"
msgstr "Álnevesített modell"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "All messages of this group"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Allowed Emails"
msgstr "Engedélyezett e-mailek"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Alone we can do so little, together we can do so much"
msgstr "Egyedül olyan keveset tudunk tenni, együtt sokkal többet"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__allow
msgid "Always Allow"
msgstr "Mindig engedélyez"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "An email with instructions has been sent."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Archived"
msgstr "Archivált"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "Archives"
msgstr "Archívumok"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__attachment_ids
msgid "Attachments"
msgstr "Mellékletek"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_id
msgid "Author"
msgstr "Szerző"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_moderation
msgid "Author Moderation Status"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Az üzenet szerzője. Ha nincs beállítva,  az e-mail ettől, egy címet "
"tartalmaz ami nem mutat egyik partnerre sem."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_group_id
msgid "Authorized Group"
msgstr "Jogosult csoport"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify
msgid "Automatic notification"
msgstr "Automatikus értesítés"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__ban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Ban"
msgstr "Tiltás"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Ban the author of the message ("
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Ban this email address and reject all pending messages from the same author "
"and send an email to the author"
msgstr ""

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__ban
msgid "Banned"
msgstr "Tiltott"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Banned Emails"
msgstr "Tiltott e-mailek"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "By date"
msgstr "Dátumonként"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__can_manage_group
msgid "Can Manage"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__can_manage_group
msgid "Can manage the members"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_child_ids
msgid "Children"
msgstr "Alárendelt"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Choose or configure a custom domain"
msgstr ""

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_subscribe
msgid "Confirm subscription to {{ object.name }}"
msgstr ""

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_unsubscribe
msgid "Confirm unsubscription to {{ object.name }}"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__body
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__body
msgid "Contents"
msgstr "Tartalmak"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid "Create a Mail Group"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Create a new group"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__is_moderator
msgid "Current user is a moderator of the group"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_bounced_content
msgid "Custom Bounced Message"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_defaults
msgid "Default Values"
msgstr "Alapértelmezett értékek"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__description
msgid "Description"
msgstr "Leírás"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Discard"
msgstr "Elvetés"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__email
msgid "Email"
msgstr "E-mail"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Email %s is invalid"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_email
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Email Alias"
msgstr "E-mail álnév"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__email_from_normalized
msgid "Email From"
msgstr "Feladó e-mail címe"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"A küldő e-mail címei. Ez a mező lesz beállítva, ha nem talált egyező "
"partnert és kiváltja a author_id mezőt a társalgásban."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Email domain pl. 'example.com' az '<EMAIL>'-ban"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails"
msgstr "E-mailek"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails waiting an action for this group"
msgstr ""

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__public
msgid "Everyone"
msgstr "Mindenki"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Follow-Ups"
msgstr "Emlékeztető"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "From"
msgstr "Forrás"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__mail_group_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Group"
msgstr "Csoport"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Group By"
msgstr "Csoportosítás"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Group Message"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Group Name"
msgstr "Csoportnév"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Group message can only be linked to mail group. Current model is %s."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines_msg
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Guidelines"
msgstr "Irányelvek"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_guidelines
msgid "Guidelines of group {{ object.mail_group_id.name }}"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Hello"
msgstr "Üdv"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__id
msgid "ID"
msgstr "Azonosító"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Az álnevet tartalmazó szülő rekord azonosítója (példa: a feladat "
"létrehozására szolgáló álnevet tartalmazó projekt)"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__image_128
msgid "Image"
msgstr "Kép"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Invalid action for URL generation (%s)"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_moderation.py:0
#: code:addons/mail_group/models/mail_group_moderation.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Érvénytelen e-mail cím: %r"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.invalid_token_subscription
msgid "Invalid or expired confirmation link."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Allowed"
msgstr "Engedélyezett"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Banned"
msgstr "Tiltott"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__is_group_moderated
msgid "Is Group Moderated"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_member
msgid "Is Member"
msgstr "Tag"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Join"
msgstr "Csatlakozás"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Leave"
msgstr "Elhagyás"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "Let people subscribe to your list online or manually add them here."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Mail Group"
msgstr ""

#. module: mail_group
#: model:res.groups,name:mail_group.group_mail_group_manager
msgid "Mail Group Administrator"
msgstr ""

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_subscribe
msgid "Mail Group: Mailing List Subscription"
msgstr ""

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_unsubscribe
msgid "Mail Group: Mailing List Unsubscription"
msgstr ""

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_guidelines
msgid "Mail Group: Send Guidelines"
msgstr ""

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_action
#: model:ir.ui.menu,name:mail_group.mail_group_menu
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Mail Groups"
msgstr ""

#. module: mail_group
#: model:ir.actions.server,name:mail_group.ir_cron_mail_notify_group_moderators_ir_actions_server
msgid "Mail List: Notify group moderators"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_message_id
msgid "Mail Message"
msgstr ""

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_member
msgid "Mailing List Member"
msgstr ""

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message
msgid "Mailing List Message"
msgstr ""

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_moderation
msgid "Mailing List black/white list"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.portal_breadcrumbs_group
msgid "Mailing Lists"
msgstr "Levelezőlisták"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid ""
"Mailing groups are communities that like to discuss a specific topic "
"together."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Mailing-List:"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Member"
msgstr "Tag"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_member_action
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Members"
msgstr "Tagok"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_count
msgid "Members Count"
msgstr "Tagok száma"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Members of this group"
msgstr "A csoport tagjai"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__members
msgid "Members only"
msgstr "Csak tagok"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__mail_group_message_id
msgid "Message"
msgstr "Üzenet"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_reject_action
msgid "Message Rejection Explanation"
msgstr ""

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_action
msgid "Messages"
msgstr "Üzenetek"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_count
msgid "Messages Count"
msgstr "Üzenetek száma"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_last_month_count
msgid "Messages Per Month"
msgstr "Üzenet havonta"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Messages are pending moderation"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Messages that need an action"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Moderate Messages"
msgstr "Üzenetek moderálása"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation
msgid "Moderate this group"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderated"
msgstr "Moderált"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderator_id
msgid "Moderated By"
msgstr "Moderálta"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_ids
msgid "Moderated Emails"
msgstr "Moderált e-mailek"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_count
msgid "Moderated emails count"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderated emails in this group"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Moderated group must have moderators."
msgstr ""

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_moderation_action
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderation"
msgstr "Moderálás"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_tree
msgid "Moderation Lists"
msgstr ""

#. module: mail_group
#: model:ir.ui.menu,name:mail_group.mail_group_moderation_menu
msgid "Moderation Rules"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderations"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_moderator
msgid "Moderator"
msgstr "Moderátor"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderator_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderators"
msgstr "Moderátorok"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Moderators must have an email address."
msgstr ""

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_1
msgid "My Company News"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__name
msgid "Name"
msgstr "Név"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"Need to unsubscribe? <br/>It's right here! <span class=\"oi fa-2x oi-arrow-"
"down float-end\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_guidelines
msgid ""
"Newcomers on this moderated group will automatically receive the guidelines."
msgstr ""

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "No Members in this list yet!"
msgstr ""

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid "No Messages in this list yet!"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email_normalized
msgid "Normalized Email"
msgstr "Normalizált email"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from_normalized
msgid "Normalized From"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify_msg
msgid "Notification message"
msgstr "Értesítő üzenet"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Notify Members"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_count
msgid "Number of message in this group"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid ""
"Only an administrator or a moderator can send guidelines to group members."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Only members can send email to the mailing list."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Only selected groups of users can send email to the mailing list."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Az üzenetlista (rekord) választható azonosítója, amelyhez minden beérkező "
"üzenet csatolásra kerül, még akkor is, ha senki sem válaszolt azokra. Ha "
"beállításra kerül, az új rekordok létrehozása teljesen kikapcsolásra kerül."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_parent_id
msgid "Parent"
msgstr "Szülő"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_model_id
msgid "Parent Model"
msgstr "Szülő modell"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Szülő rekord szál azonosító"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Az álnevet tartalmazó szülő modell. Az álnév hivatkozást tartalmazó modell "
"nem feltétlenül azonos az alias_model_id által megadott modellel (például: "
"projekt (parent_model) és feladat (model))."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__partner_id
msgid "Partner"
msgstr "Partner"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_partner_ids
msgid "Partners Member"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_ids
msgid "Pending Messages"
msgstr "Függő üzenetek"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Pending Messages Count"
msgstr "Függő üzenetek száma"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr ""

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__ban
msgid "Permanent Ban"
msgstr "Állandó tiltás"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"A dokumentumra küldött üzenetek szabályai a levélátjáró használatával.\n"
"- mindenki: mindenki tud üzenni\n"
"- partnerek: csak a hitelesített partnerek\n"
"- követők: csak a kapcsolódó dokumentum követői vagy a következő csatornák tagjai\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Post to:"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_date
msgid "Posted"
msgstr "Beküldött"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_mode
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Privacy"
msgstr "Adatvédelem"

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_2
msgid "Public Mailing List"
msgstr "Nyilvános levelező lista"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/wizard/mail_group_message_reject.py:0
#, python-format
msgid "Re: %s"
msgstr "Vissza: %s"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Bejegyzés üzenetlista azonosító"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Reference"
msgstr "Hivatkozás"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__reject
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Reject"
msgstr "Elutasít"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message_reject
msgid "Reject Group Message"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject Silently"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject the message"
msgstr ""

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__rejected
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Rejected"
msgstr "Elutasított"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Remove message with explanation"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Responsible Users"
msgstr "Felelős felhasználó"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "Search Group Message"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_member_view_search
msgid "Search Mail Group Member"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Search Mail group"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Search Moderation List"
msgstr ""

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__groups
msgid "Selected group of users"
msgstr "Kiválasztott felhasználók csoportja"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Send"
msgstr "Küldés"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Ban"
msgstr "Küldés és tiltás"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Reject"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__send_email
msgid "Send Email"
msgstr "E-mail küldés"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Send Guidelines"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message_reject__send_email
msgid "Send an email to the author of the message"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr ""

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_guidelines
msgid "Sent to people who subscribed to a mailing group with group guidelines"
msgstr ""

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_unsubscribe
msgid "Sent to people who unsubscribed from a mailing group"
msgstr ""

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderation_status
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__status
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Status"
msgstr "Státusz"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Stay in touch with our Community"
msgstr "Maradjon kapcsolatba a közösségünkkel"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__subject
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__subject
msgid "Subject"
msgstr "Tárgy"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
#, python-format
msgid "Subscribe"
msgstr "Feliratkozás"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_subscribe
msgid "Subscription confirmation to a mailing group"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid ""
"Template \"mail_group.mail_template_guidelines\" was not found. No email has"
" been sent. Please contact an administrator to fix this issue."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Thank you!"
msgstr "Köszönjük!"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The \"Authorized Group\" is missing."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "The email"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "The email \"%s\" is not valid."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The group of the message do not match."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The guidelines description is empty."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The guidelines description is missing."
msgstr ""

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"A modell (Odoo dokumentum típus), amelyre ez az álnév vonatkozik. Bármely "
"bejövő e-mail, amely nem válaszol egy meglévő rekordra, ezen modell egy új "
"rekordjának létrehozását fogja előidézni (pl. egy projekt feladatot)"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Az e-mail álnév neve, pl. 'állások', ha erre az e-mailcímre szeretne "
"leveleket kapni: <<EMAIL>>"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The notification message is missing."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The partner can not be found."
msgstr "A partner nem található."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "The record of the message should be the group."
msgstr ""

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "This email is already subscribed."
msgstr ""

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "This email is not subscribed."
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "This message can not be moderated"
msgstr ""

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_member_unique_partner
msgid "This partner is already subscribed to the group"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Those messages can not be moderated: %s."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "To Review"
msgstr "Átnézésre vár"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
#, python-format
msgid "Unsubscribe"
msgstr "Leiratkozás"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Unsubscribe:"
msgstr "Leiratkozás:"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid ""
"When people send an email to the alias of the list, they will appear here."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Whitelist"
msgstr ""

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__allow
msgid "Whitelisted"
msgstr ""

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Wrong status (%s)"
msgstr ""

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_moderation_mail_group_email_uniq
msgid "You can create only one rule for a given email address in a group."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "_______________________________________________"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "and send an email to the author ("
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid "attachments"
msgstr "mellékletek"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "by"
msgstr "által"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. \"Newsletter\""
msgstr "pl.: „Hírlevél”"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. domain.com"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "group"
msgstr "csoport"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "has been"
msgstr "lett"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"members<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"
msgstr ""
"tagok<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "messages / month"
msgstr "üzenetek / hónap"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "more replies"
msgstr "több válasz"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "replies"
msgstr "válaszok"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "subscribed to"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "the list"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "unsubscribed from"
msgstr ""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "your email..."
msgstr "emailje..."
