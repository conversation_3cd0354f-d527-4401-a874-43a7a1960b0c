<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="crm.ColumnProgress" t-inherit="web.ColumnProgress" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('o_column_progress')]" position="attributes">
            <attribute name="class" remove="w-75" add="w-50" separator=" "/>
        </xpath>
        <AnimatedNumber position="after">
            <t t-if="showRecurringRevenue">
                <t t-set="rrmAggregate" t-value="getRecurringRevenueGroupAggregate(props.group)"/>
                <AnimatedNumber
                    value="rrmAggregate.value"
                    title="rrmAggregate.title"
                    duration="1000"
                    currency="props.aggregate.currency"
                    animationClass="'o_animated_grow_huge'"
                >
                    <t t-set-slot="prefix">
                        <strong>+</strong>
                    </t>
                </AnimatedNumber>
            </t>
        </AnimatedNumber>
    </t>
</templates>
