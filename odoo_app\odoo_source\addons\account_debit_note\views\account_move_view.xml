<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_debit" model="ir.ui.view">
        <field name="name">account.move.form.debit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <button name="action_reverse" position="after">
                <field name="debit_origin_id" invisible="1"/>
                <button name="%(action_view_account_move_debit)d" string='Add Debit Note'
                                type='action' groups="account.group_account_invoice"
                                attrs="{'invisible': ['|', '|', ('debit_origin_id', '!=', False),
                                        ('move_type', 'not in', ('out_invoice', 'in_invoice', 'out_refund', 'in_refund')), ('state', '!=', 'posted')]}"/>
            </button>
            <div class="oe_button_box" position="inside">
                <button type="object" class="oe_stat_button" name="action_view_debit_notes" icon="fa-plus" attrs="{'invisible': [('debit_note_count', '=', 0)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="debit_note_count"/></span>
                        <span class="o_stat_text">Debit Notes</span>
                    </div>
                </button>
            </div>
            <field name="invoice_origin" position="after">
                <field name="debit_origin_id" attrs="{'invisible': [('debit_origin_id', '=', False)]}"/>
            </field>
        </field>
    </record>
</odoo>
