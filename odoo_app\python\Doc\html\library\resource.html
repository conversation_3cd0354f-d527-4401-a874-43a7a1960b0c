<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="resource — Resource usage information" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/resource.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides basic mechanisms for measuring and controlling system resources utilized by a program. Availability: Unix, not Emscripten, not WASI. Symbolic constants are used to specify part..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides basic mechanisms for measuring and controlling system resources utilized by a program. Availability: Unix, not Emscripten, not WASI. Symbolic constants are used to specify part..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>resource — Resource usage information &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="syslog — Unix syslog library routines" href="syslog.html" />
    <link rel="prev" title="fcntl — The fcntl and ioctl system calls" href="fcntl.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/resource.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code> — Resource usage information</a><ul>
<li><a class="reference internal" href="#resource-limits">Resource Limits</a></li>
<li><a class="reference internal" href="#resource-usage">Resource Usage</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="fcntl.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fcntl</span></code> — The <code class="docutils literal notranslate"><span class="pre">fcntl</span></code> and <code class="docutils literal notranslate"><span class="pre">ioctl</span></code> system calls</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="syslog.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code> — Unix syslog library routines</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/resource.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="syslog.html" title="syslog — Unix syslog library routines"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="fcntl.html" title="fcntl — The fcntl and ioctl system calls"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" accesskey="U">Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code> — Resource usage information</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-resource">
<span id="resource-resource-usage-information"></span><h1><a class="reference internal" href="#module-resource" title="resource: An interface to provide resource usage information on the current process. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code></a> — Resource usage information<a class="headerlink" href="#module-resource" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module provides basic mechanisms for measuring and controlling system
resources utilized by a program.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, not Emscripten, not WASI.</p>
</div>
<p>Symbolic constants are used to specify particular system resources and to
request usage information about either the current process or its children.</p>
<p>An <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised on syscall failure.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="resource.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#resource.error" title="Link to this definition">¶</a></dt>
<dd><p>A deprecated alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Following <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3151/"><strong>PEP 3151</strong></a>, this class was made an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<section id="resource-limits">
<h2>Resource Limits<a class="headerlink" href="#resource-limits" title="Link to this heading">¶</a></h2>
<p>Resources usage can be limited using the <a class="reference internal" href="#resource.setrlimit" title="resource.setrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">setrlimit()</span></code></a> function described
below. Each resource is controlled by a pair of limits: a soft limit and a hard
limit. The soft limit is the current limit, and may be lowered or raised by a
process over time. The soft limit can never exceed the hard limit. The hard
limit can be lowered to any value greater than the soft limit, but not raised.
(Only processes with the effective UID of the super-user can raise a hard
limit.)</p>
<p>The specific resources that can be limited are system dependent. They are
described in the <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/getrlimit(2)">getrlimit(2)</a></em> man page.  The resources listed below
are supported when the underlying operating system supports them; resources
which cannot be checked or controlled by the operating system are not defined in
this module for those platforms.</p>
<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIM_INFINITY">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIM_INFINITY</span></span><a class="headerlink" href="#resource.RLIM_INFINITY" title="Link to this definition">¶</a></dt>
<dd><p>Constant used to represent the limit for an unlimited resource.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="resource.getrlimit">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">getrlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#resource.getrlimit" title="Link to this definition">¶</a></dt>
<dd><p>Returns a tuple <code class="docutils literal notranslate"><span class="pre">(soft,</span> <span class="pre">hard)</span></code> with the current soft and hard limits of
<em>resource</em>. Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if an invalid resource is specified, or
<a class="reference internal" href="#resource.error" title="resource.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error</span></code></a> if the underlying system call fails unexpectedly.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="resource.setrlimit">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">setrlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limits</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#resource.setrlimit" title="Link to this definition">¶</a></dt>
<dd><p>Sets new limits of consumption of <em>resource</em>. The <em>limits</em> argument must be a
tuple <code class="docutils literal notranslate"><span class="pre">(soft,</span> <span class="pre">hard)</span></code> of two integers describing the new limits. A value of
<a class="reference internal" href="#resource.RLIM_INFINITY" title="resource.RLIM_INFINITY"><code class="xref py py-data docutils literal notranslate"><span class="pre">RLIM_INFINITY</span></code></a> can be used to request a limit that is
unlimited.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if an invalid resource is specified, if the new soft
limit exceeds the hard limit, or if a process tries to raise its hard limit.
Specifying a limit of <a class="reference internal" href="#resource.RLIM_INFINITY" title="resource.RLIM_INFINITY"><code class="xref py py-data docutils literal notranslate"><span class="pre">RLIM_INFINITY</span></code></a> when the hard or
system limit for that resource is not unlimited will result in a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.  A process with the effective UID of super-user can
request any valid limit value, including unlimited, but <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>
will still be raised if the requested limit exceeds the system imposed
limit.</p>
<p><code class="docutils literal notranslate"><span class="pre">setrlimit</span></code> may also raise <a class="reference internal" href="#resource.error" title="resource.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error</span></code></a> if the underlying system call
fails.</p>
<p>VxWorks only supports setting <a class="reference internal" href="#resource.RLIMIT_NOFILE" title="resource.RLIMIT_NOFILE"><code class="xref py py-data docutils literal notranslate"><span class="pre">RLIMIT_NOFILE</span></code></a>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">resource.setrlimit</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">resource</span></code>, <code class="docutils literal notranslate"><span class="pre">limits</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="resource.prlimit">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">prlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pid</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">limits</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#resource.prlimit" title="Link to this definition">¶</a></dt>
<dd><p>Combines <a class="reference internal" href="#resource.setrlimit" title="resource.setrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">setrlimit()</span></code></a> and <a class="reference internal" href="#resource.getrlimit" title="resource.getrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrlimit()</span></code></a> in one function and
supports to get and set the resources limits of an arbitrary process. If
<em>pid</em> is 0, then the call applies to the current process. <em>resource</em> and
<em>limits</em> have the same meaning as in <a class="reference internal" href="#resource.setrlimit" title="resource.setrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">setrlimit()</span></code></a>, except that
<em>limits</em> is optional.</p>
<p>When <em>limits</em> is not given the function returns the <em>resource</em> limit of the
process <em>pid</em>. When <em>limits</em> is given the <em>resource</em> limit of the process is
set and the former resource limit is returned.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ProcessLookupError" title="ProcessLookupError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProcessLookupError</span></code></a> when <em>pid</em> can’t be found and
<a class="reference internal" href="exceptions.html#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a> when the user doesn’t have <code class="docutils literal notranslate"><span class="pre">CAP_SYS_RESOURCE</span></code> for
the process.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">resource.prlimit</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">pid</span></code>, <code class="docutils literal notranslate"><span class="pre">resource</span></code>, <code class="docutils literal notranslate"><span class="pre">limits</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.36 with glibc &gt;= 2.13.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<p>These symbols define resources whose consumption can be controlled using the
<a class="reference internal" href="#resource.setrlimit" title="resource.setrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">setrlimit()</span></code></a> and <a class="reference internal" href="#resource.getrlimit" title="resource.getrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrlimit()</span></code></a> functions described below. The values of
these symbols are exactly the constants used by C programs.</p>
<p>The Unix man page for <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/getrlimit(2)">getrlimit(2)</a></em> lists the available resources.
Note that not all systems use the same symbol or same value to denote the same
resource.  This module does not attempt to mask platform differences — symbols
not defined for a platform will not be available from this module on that
platform.</p>
<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_CORE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_CORE</span></span><a class="headerlink" href="#resource.RLIMIT_CORE" title="Link to this definition">¶</a></dt>
<dd><p>The maximum size (in bytes) of a core file that the current process can create.
This may result in the creation of a partial core file if a larger core would be
required to contain the entire process image.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_CPU">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_CPU</span></span><a class="headerlink" href="#resource.RLIMIT_CPU" title="Link to this definition">¶</a></dt>
<dd><p>The maximum amount of processor time (in seconds) that a process can use. If
this limit is exceeded, a <code class="xref py py-const docutils literal notranslate"><span class="pre">SIGXCPU</span></code> signal is sent to the process. (See
the <a class="reference internal" href="signal.html#module-signal" title="signal: Set handlers for asynchronous events."><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code></a> module documentation for information about how to catch this
signal and do something useful, e.g. flush open files to disk.)</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_FSIZE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_FSIZE</span></span><a class="headerlink" href="#resource.RLIMIT_FSIZE" title="Link to this definition">¶</a></dt>
<dd><p>The maximum size of a file which the process may create.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_DATA">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_DATA</span></span><a class="headerlink" href="#resource.RLIMIT_DATA" title="Link to this definition">¶</a></dt>
<dd><p>The maximum size (in bytes) of the process’s heap.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_STACK">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_STACK</span></span><a class="headerlink" href="#resource.RLIMIT_STACK" title="Link to this definition">¶</a></dt>
<dd><p>The maximum size (in bytes) of the call stack for the current process.  This only
affects the stack of the main thread in a multi-threaded process.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_RSS">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_RSS</span></span><a class="headerlink" href="#resource.RLIMIT_RSS" title="Link to this definition">¶</a></dt>
<dd><p>The maximum resident set size that should be made available to the process.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_NPROC">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_NPROC</span></span><a class="headerlink" href="#resource.RLIMIT_NPROC" title="Link to this definition">¶</a></dt>
<dd><p>The maximum number of processes the current process may create.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_NOFILE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_NOFILE</span></span><a class="headerlink" href="#resource.RLIMIT_NOFILE" title="Link to this definition">¶</a></dt>
<dd><p>The maximum number of open file descriptors for the current process.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_OFILE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_OFILE</span></span><a class="headerlink" href="#resource.RLIMIT_OFILE" title="Link to this definition">¶</a></dt>
<dd><p>The BSD name for <a class="reference internal" href="#resource.RLIMIT_NOFILE" title="resource.RLIMIT_NOFILE"><code class="xref py py-const docutils literal notranslate"><span class="pre">RLIMIT_NOFILE</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_MEMLOCK">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_MEMLOCK</span></span><a class="headerlink" href="#resource.RLIMIT_MEMLOCK" title="Link to this definition">¶</a></dt>
<dd><p>The maximum address space which may be locked in memory.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_VMEM">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_VMEM</span></span><a class="headerlink" href="#resource.RLIMIT_VMEM" title="Link to this definition">¶</a></dt>
<dd><p>The largest area of mapped memory which the process may occupy.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD &gt;= 11.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_AS">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_AS</span></span><a class="headerlink" href="#resource.RLIMIT_AS" title="Link to this definition">¶</a></dt>
<dd><p>The maximum area (in bytes) of address space which may be taken by the process.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_MSGQUEUE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_MSGQUEUE</span></span><a class="headerlink" href="#resource.RLIMIT_MSGQUEUE" title="Link to this definition">¶</a></dt>
<dd><p>The number of bytes that can be allocated for POSIX message queues.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.8.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_NICE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_NICE</span></span><a class="headerlink" href="#resource.RLIMIT_NICE" title="Link to this definition">¶</a></dt>
<dd><p>The ceiling for the process’s nice level (calculated as 20 - rlim_cur).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.12.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_RTPRIO">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_RTPRIO</span></span><a class="headerlink" href="#resource.RLIMIT_RTPRIO" title="Link to this definition">¶</a></dt>
<dd><p>The ceiling of the real-time priority.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.12.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_RTTIME">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_RTTIME</span></span><a class="headerlink" href="#resource.RLIMIT_RTTIME" title="Link to this definition">¶</a></dt>
<dd><p>The time limit (in microseconds) on CPU time that a process can spend
under real-time scheduling without making a blocking syscall.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.25.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_SIGPENDING">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_SIGPENDING</span></span><a class="headerlink" href="#resource.RLIMIT_SIGPENDING" title="Link to this definition">¶</a></dt>
<dd><p>The number of signals which the process may queue.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.8.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_SBSIZE">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_SBSIZE</span></span><a class="headerlink" href="#resource.RLIMIT_SBSIZE" title="Link to this definition">¶</a></dt>
<dd><p>The maximum size (in bytes) of socket buffer usage for this user.
This limits the amount of network memory, and hence the amount of mbufs,
that this user may hold at any time.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_SWAP">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_SWAP</span></span><a class="headerlink" href="#resource.RLIMIT_SWAP" title="Link to this definition">¶</a></dt>
<dd><p>The maximum size (in bytes) of the swap space that may be reserved or
used by all of this user id’s processes.
This limit is enforced only if bit 1 of the vm.overcommit sysctl is set.
Please see
<a class="reference external" href="https://man.freebsd.org/cgi/man.cgi?query=tuning&amp;sektion=7">tuning(7)</a>
for a complete description of this sysctl.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_NPTS">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_NPTS</span></span><a class="headerlink" href="#resource.RLIMIT_NPTS" title="Link to this definition">¶</a></dt>
<dd><p>The maximum number of pseudo-terminals created by this user id.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RLIMIT_KQUEUES">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RLIMIT_KQUEUES</span></span><a class="headerlink" href="#resource.RLIMIT_KQUEUES" title="Link to this definition">¶</a></dt>
<dd><p>The maximum number of kqueues this user id is allowed to create.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD &gt;= 11.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

</section>
<section id="resource-usage">
<h2>Resource Usage<a class="headerlink" href="#resource-usage" title="Link to this heading">¶</a></h2>
<p>These functions are used to retrieve resource usage information:</p>
<dl class="py function">
<dt class="sig sig-object py" id="resource.getrusage">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">getrusage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">who</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#resource.getrusage" title="Link to this definition">¶</a></dt>
<dd><p>This function returns an object that describes the resources consumed by either
the current process or its children, as specified by the <em>who</em> parameter.  The
<em>who</em> parameter should be specified using one of the <code class="xref py py-const docutils literal notranslate"><span class="pre">RUSAGE_*</span></code>
constants described below.</p>
<p>A simple example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">resource</span> <span class="kn">import</span> <span class="o">*</span>
<span class="kn">import</span> <span class="nn">time</span>

<span class="c1"># a non CPU-bound task</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">getrusage</span><span class="p">(</span><span class="n">RUSAGE_SELF</span><span class="p">))</span>

<span class="c1"># a CPU-bound task</span>
<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span> <span class="o">**</span> <span class="mi">8</span><span class="p">):</span>
   <span class="n">_</span> <span class="o">=</span> <span class="mi">1</span> <span class="o">+</span> <span class="mi">1</span>
<span class="nb">print</span><span class="p">(</span><span class="n">getrusage</span><span class="p">(</span><span class="n">RUSAGE_SELF</span><span class="p">))</span>
</pre></div>
</div>
<p>The fields of the return value each describe how a particular system resource
has been used, e.g. amount of time spent running is user mode or number of times
the process was swapped out of main memory. Some values are dependent on the
clock tick internal, e.g. the amount of memory the process is using.</p>
<p>For backward compatibility, the return value is also accessible as a tuple of 16
elements.</p>
<p>The fields <code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_utime</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_stime</span></code> of the return value are
floating point values representing the amount of time spent executing in user
mode and the amount of time spent executing in system mode, respectively. The
remaining values are integers. Consult the <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/getrusage(2)">getrusage(2)</a></em> man page for
detailed information about these values. A brief summary is presented here:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Index</p></th>
<th class="head"><p>Field</p></th>
<th class="head"><p>Resource</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_utime</span></code></p></td>
<td><p>time in user mode (float seconds)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_stime</span></code></p></td>
<td><p>time in system mode (float seconds)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_maxrss</span></code></p></td>
<td><p>maximum resident set size</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">3</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_ixrss</span></code></p></td>
<td><p>shared memory size</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_idrss</span></code></p></td>
<td><p>unshared memory size</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">5</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_isrss</span></code></p></td>
<td><p>unshared stack size</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">6</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_minflt</span></code></p></td>
<td><p>page faults not requiring I/O</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">7</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_majflt</span></code></p></td>
<td><p>page faults requiring I/O</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">8</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_nswap</span></code></p></td>
<td><p>number of swap outs</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">9</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_inblock</span></code></p></td>
<td><p>block input operations</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_oublock</span></code></p></td>
<td><p>block output operations</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">11</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_msgsnd</span></code></p></td>
<td><p>messages sent</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">12</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_msgrcv</span></code></p></td>
<td><p>messages received</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">13</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_nsignals</span></code></p></td>
<td><p>signals received</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">14</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_nvcsw</span></code></p></td>
<td><p>voluntary context switches</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">15</span></code></p></td>
<td><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">ru_nivcsw</span></code></p></td>
<td><p>involuntary context switches</p></td>
</tr>
</tbody>
</table>
<p>This function will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if an invalid <em>who</em> parameter is
specified. It may also raise <a class="reference internal" href="#resource.error" title="resource.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error</span></code></a> exception in unusual circumstances.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="resource.getpagesize">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">getpagesize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#resource.getpagesize" title="Link to this definition">¶</a></dt>
<dd><p>Returns the number of bytes in a system page. (This need not be the same as the
hardware page size.)</p>
</dd></dl>

<p>The following <code class="xref py py-const docutils literal notranslate"><span class="pre">RUSAGE_*</span></code> symbols are passed to the <a class="reference internal" href="#resource.getrusage" title="resource.getrusage"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrusage()</span></code></a>
function to specify which processes information should be provided for.</p>
<dl class="py data">
<dt class="sig sig-object py" id="resource.RUSAGE_SELF">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RUSAGE_SELF</span></span><a class="headerlink" href="#resource.RUSAGE_SELF" title="Link to this definition">¶</a></dt>
<dd><p>Pass to <a class="reference internal" href="#resource.getrusage" title="resource.getrusage"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrusage()</span></code></a> to request resources consumed by the calling
process, which is the sum of resources used by all threads in the process.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RUSAGE_CHILDREN">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RUSAGE_CHILDREN</span></span><a class="headerlink" href="#resource.RUSAGE_CHILDREN" title="Link to this definition">¶</a></dt>
<dd><p>Pass to <a class="reference internal" href="#resource.getrusage" title="resource.getrusage"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrusage()</span></code></a> to request resources consumed by child processes
of the calling process which have been terminated and waited for.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RUSAGE_BOTH">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RUSAGE_BOTH</span></span><a class="headerlink" href="#resource.RUSAGE_BOTH" title="Link to this definition">¶</a></dt>
<dd><p>Pass to <a class="reference internal" href="#resource.getrusage" title="resource.getrusage"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrusage()</span></code></a> to request resources consumed by both the current
process and child processes.  May not be available on all systems.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="resource.RUSAGE_THREAD">
<span class="sig-prename descclassname"><span class="pre">resource.</span></span><span class="sig-name descname"><span class="pre">RUSAGE_THREAD</span></span><a class="headerlink" href="#resource.RUSAGE_THREAD" title="Link to this definition">¶</a></dt>
<dd><p>Pass to <a class="reference internal" href="#resource.getrusage" title="resource.getrusage"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrusage()</span></code></a> to request resources consumed by the current
thread.  May not be available on all systems.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code> — Resource usage information</a><ul>
<li><a class="reference internal" href="#resource-limits">Resource Limits</a></li>
<li><a class="reference internal" href="#resource-usage">Resource Usage</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="fcntl.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fcntl</span></code> — The <code class="docutils literal notranslate"><span class="pre">fcntl</span></code> and <code class="docutils literal notranslate"><span class="pre">ioctl</span></code> system calls</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="syslog.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code> — Unix syslog library routines</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/resource.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="syslog.html" title="syslog — Unix syslog library routines"
             >next</a> |</li>
        <li class="right" >
          <a href="fcntl.html" title="fcntl — The fcntl and ioctl system calls"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" >Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code> — Resource usage information</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>