<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Structured Markup Processing Tools" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/markup.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Python supports a variety of modules to work with various forms of structured data markup. This includes modules to work with the Standard Generalized Markup Language (SGML) and the Hypertext Marku..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Python supports a variety of modules to work with various forms of structured data markup. This includes modules to work with the Standard Generalized Markup Language (SGML) and the Hypertext Marku..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Structured Markup Processing Tools &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="html — HyperText Markup Language support" href="html.html" />
    <link rel="prev" title="quopri — Encode and decode MIME quoted-printable data" href="quopri.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/markup.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="quopri.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">quopri</span></code> — Encode and decode MIME quoted-printable data</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="html.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html</span></code> — HyperText Markup Language support</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/markup.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="html.html" title="html — HyperText Markup Language support"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="quopri.html" title="quopri — Encode and decode MIME quoted-printable data"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Structured Markup Processing Tools</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="structured-markup-processing-tools">
<span id="markup"></span><h1>Structured Markup Processing Tools<a class="headerlink" href="#structured-markup-processing-tools" title="Link to this heading">¶</a></h1>
<p>Python supports a variety of modules to work with various forms of structured
data markup.  This includes modules to work with the Standard Generalized Markup
Language (SGML) and the Hypertext Markup Language (HTML), and several interfaces
for working with the Extensible Markup Language (XML).</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="html.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html</span></code> — HyperText Markup Language support</a></li>
<li class="toctree-l1"><a class="reference internal" href="html.parser.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code> — Simple HTML and XHTML parser</a><ul>
<li class="toctree-l2"><a class="reference internal" href="html.parser.html#example-html-parser-application">Example HTML Parser Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="html.parser.html#htmlparser-methods"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code> Methods</a></li>
<li class="toctree-l2"><a class="reference internal" href="html.parser.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="html.entities.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.entities</span></code> — Definitions of HTML general entities</a></li>
<li class="toctree-l1"><a class="reference internal" href="xml.html">XML Processing Modules</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.html#xml-vulnerabilities">XML vulnerabilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.html#the-defusedxml-package">The <code class="xref py py-mod docutils literal notranslate"><span class="pre">defusedxml</span></code> Package</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.etree.elementtree.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.etree.elementtree.html#tutorial">Tutorial</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#xml-tree-and-elements">XML tree and elements</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#parsing-xml">Parsing XML</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#pull-api-for-non-blocking-parsing">Pull API for non-blocking parsing</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#finding-interesting-elements">Finding interesting elements</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#modifying-an-xml-file">Modifying an XML File</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#building-xml-documents">Building XML documents</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#parsing-xml-with-namespaces">Parsing XML with Namespaces</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="xml.etree.elementtree.html#xpath-support">XPath support</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#example">Example</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#supported-xpath-syntax">Supported XPath syntax</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="xml.etree.elementtree.html#reference">Reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#functions">Functions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="xml.etree.elementtree.html#xinclude-support">XInclude support</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#id3">Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="xml.etree.elementtree.html#id4">Reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#elementinclude-functions">Functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#element-objects">Element Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#elementtree-objects">ElementTree Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#qname-objects">QName Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#treebuilder-objects">TreeBuilder Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#xmlparser-objects">XMLParser Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#xmlpullparser-objects">XMLPullParser Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.etree.elementtree.html#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.dom.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.html#module-contents">Module Contents</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.html#objects-in-the-dom">Objects in the DOM</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#domimplementation-objects">DOMImplementation Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#node-objects">Node Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#nodelist-objects">NodeList Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#documenttype-objects">DocumentType Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#document-objects">Document Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#element-objects">Element Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#attr-objects">Attr Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#namednodemap-objects">NamedNodeMap Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#comment-objects">Comment Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#text-and-cdatasection-objects">Text and CDATASection Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#processinginstruction-objects">ProcessingInstruction Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#exceptions">Exceptions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.html#conformance">Conformance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#type-mapping">Type Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="xml.dom.html#accessor-methods">Accessor Methods</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.dom.minidom.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.minidom.html#dom-objects">DOM Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.minidom.html#dom-example">DOM Example</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.minidom.html#minidom-and-the-dom-standard">minidom and the DOM standard</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.dom.pulldom.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.pulldom</span></code> — Support for building partial DOM trees</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.dom.pulldom.html#domeventstream-objects">DOMEventStream Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.sax.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax</span></code> — Support for SAX2 parsers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.html#saxexception-objects">SAXException Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.sax.handler.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code> — Base classes for SAX handlers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.handler.html#contenthandler-objects">ContentHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.handler.html#dtdhandler-objects">DTDHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.handler.html#entityresolver-objects">EntityResolver Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.handler.html#errorhandler-objects">ErrorHandler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.handler.html#lexicalhandler-objects">LexicalHandler Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="xml.sax.utils.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.saxutils</span></code> — SAX Utilities</a></li>
<li class="toctree-l1"><a class="reference internal" href="xml.sax.reader.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.reader.html#xmlreader-objects">XMLReader Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.reader.html#incrementalparser-objects">IncrementalParser Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.reader.html#locator-objects">Locator Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.reader.html#inputsource-objects">InputSource Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.reader.html#the-attributes-interface">The <code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="xml.sax.reader.html#the-attributesns-interface">The <code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> Interface</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="pyexpat.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a><ul>
<li class="toctree-l2"><a class="reference internal" href="pyexpat.html#xmlparser-objects">XMLParser Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="pyexpat.html#expaterror-exceptions">ExpatError Exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="pyexpat.html#example">Example</a></li>
<li class="toctree-l2"><a class="reference internal" href="pyexpat.html#module-xml.parsers.expat.model">Content Model Descriptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="pyexpat.html#module-xml.parsers.expat.errors">Expat error constants</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="quopri.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">quopri</span></code> — Encode and decode MIME quoted-printable data</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="html.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html</span></code> — HyperText Markup Language support</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/markup.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="html.html" title="html — HyperText Markup Language support"
             >next</a> |</li>
        <li class="right" >
          <a href="quopri.html" title="quopri — Encode and decode MIME quoted-printable data"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Structured Markup Processing Tools</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>