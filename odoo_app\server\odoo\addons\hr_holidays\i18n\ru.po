# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# Андрей <PERSON>е<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# Сергей <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2024
# Collex100, 2024
# <PERSON>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:35+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " days"
msgstr " дней"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " hours"
msgstr " часов"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr "!important &gt;&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr "!important /&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr "!important/&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr "!important; font-size: 10px\" &gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr "!important; font-size: 8px; min-width: 18px\"&gt;"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(employee_name)s - from %(date_from)s to %(date_to)s - %(state)s"
msgstr "%(employee_name)s - от %(date_from)s до %(date_to)s - %(state)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%(first)s, %(second)s and %(amount)s others"
msgstr "%(first)s, %(second)s и %(amount)s другие"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(holiday_name)s has been refused."
msgstr "%(holiday_name)s было отказано."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"%(leave_name)s has been cancelled with the justification: <br/> %(reason)s."
msgstr "%(leave_name)s была отменена с обоснованием: <br/> %(reason)s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s: %(duration).2f days (%(start)s)"
msgstr "%(leave_type)s: %(duration).2f дней (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s: %(duration).2f hours on %(date)s"
msgstr "%(leave_type)s: %(duration).2f часов на %(date)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%(name)s (%(duration)s day(s))"
msgstr "%(name)s (%(duration)s дн.)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%(name)s (%(duration)s hour(s))"
msgstr "%(name)s (%(duration)s ч.)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f days (%(start)s)"
msgstr "%(person)s на %(leave_type)s: %(duration).2f дней (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f hours on %(date)s"
msgstr "%(person)s на %(leave_type)s: %(duration).2f часов на %(date)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s: %(duration).2f days (%(start)s)"
msgstr "%(person)s: %(duration).2f дней (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s: %(duration).2f hours on %(date)s"
msgstr "%(person)s: %(duration).2f часов на %(date)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid "%g remaining out of %g"
msgstr "%g осталось из %g"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to %s)"
msgstr "%s (от %s до %s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to No Limit)"
msgstr "%s (от %s до \"Без ограничений\")"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f day(s)"
msgstr "%s на нерабочее время : %.2f день (ей)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f hour(s)"
msgstr "%s на нерабочее время : %.2f час(ов)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: %.2f days"
msgstr "%s: %.2f дней"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: %.2f hours"
msgstr "%s: %.2f часов"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr "%s: Время отдыха"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "(valid until"
msgstr "(действует до"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10
msgid "10:00 AM"
msgstr "10:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22
msgid "10:00 PM"
msgstr "22:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10_5
msgid "10:30 AM"
msgstr "10:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22_5
msgid "10:30 PM"
msgstr "22:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11
msgid "11:00 AM"
msgstr "11:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23
msgid "11:00 PM"
msgstr "23.00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11_5
msgid "11:30 AM"
msgstr "11:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23_5
msgid "11:30 PM"
msgstr "23.30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0
msgid "12:00 AM"
msgstr "00:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12
msgid "12:00 PM"
msgstr "24:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0_5
msgid "12:30 AM"
msgstr "00:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12_5
msgid "12:30 PM"
msgstr "12:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1
msgid "1:00 AM"
msgstr "1:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13
msgid "1:00 PM"
msgstr "13:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1_5
msgid "1:30 AM"
msgstr "1:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13_5
msgid "1:30 PM"
msgstr "13:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2
msgid "2:00 AM"
msgstr "2:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14
msgid "2:00 PM"
msgstr "14:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2_5
msgid "2:30 AM"
msgstr "2:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14_5
msgid "2:30 PM"
msgstr "14:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3
msgid "3:00 AM"
msgstr "3:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15
msgid "3:00 PM"
msgstr "15:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3_5
msgid "3:30 AM"
msgstr "3:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15_5
msgid "3:30 PM"
msgstr "15:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4
msgid "4:00 AM"
msgstr "4:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16
msgid "4:00 PM"
msgstr "16:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4_5
msgid "4:30 AM"
msgstr "4:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16_5
msgid "4:30 PM"
msgstr "16:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5
msgid "5:00 AM"
msgstr "5:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17
msgid "5:00 PM"
msgstr "17:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5_5
msgid "5:30 AM"
msgstr "5:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17_5
msgid "5:30 PM"
msgstr "17:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6
msgid "6:00 AM"
msgstr "6:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18
msgid "6:00 PM"
msgstr "18:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6_5
msgid "6:30 AM"
msgstr "6:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18_5
msgid "6:30 PM"
msgstr "18:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7
msgid "7:00 AM"
msgstr "7:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19
msgid "7:00 PM"
msgstr "19:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7_5
msgid "7:30 AM"
msgstr "7:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19_5
msgid "7:30 PM"
msgstr "19:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8
msgid "8:00 AM"
msgstr "8:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20
msgid "8:00 PM"
msgstr "20:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8_5
msgid "8:30 AM"
msgstr "8:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20_5
msgid "8:30 PM"
msgstr "20:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9
msgid "9:00 AM"
msgstr "9:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21
msgid "9:00 PM"
msgstr "21:00"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9_5
msgid "9:30 AM"
msgstr "9:30"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21_5
msgid "9:30 PM"
msgstr "21:30"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr "<i class=\"fa fa-check\"/> Подтвердить"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr "<i class=\"fa fa-thumbs-up\"/> Утвердить"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr "<i class=\"fa fa-times\"/> Отказаться"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Days</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Hours</span>"
msgstr ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Дни</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Часы</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "<span class=\"ms-3\">Days</span>"
msgstr "<span class=\"ms-3\">Дни</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Off Till\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            До тех пор пока\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Нерабочее Время\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                           Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                           Свободное время\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr "<span class=\"o_stat_text\">Начисления</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr "<span class=\"o_stat_text\">Распределение</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Свободное время</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\"> to </span>"
msgstr "<span class=\"text-muted\"> на </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">от </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span invisible=\"holiday_type != 'employee'\">\n"
"                        The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone\n"
"                    </span>\n"
"                    <span invisible=\"holiday_type != 'department'\">\n"
"                        The department's company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    <span invisible=\"holiday_type != 'company'\">\n"
"                        The company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    ("
msgstr ""
"<span invisible=\"holiday_type != 'employee'\">\n"
"                        У сотрудника другой часовой пояс, чем у вас! Здесь даты и время отображаются в часовом поясе сотрудника\n"
"                   </span>\n"
"                   <span invisible=\"holiday_type != 'department'\">\n"
"                        Компания отдела имеет часовой пояс, отличный от вашего! Здесь даты и время отображаются в часовом поясе компании\n"
"                   </span>\n"
"                   <span invisible=\"holiday_type != 'company'\">\n"
"                        Часовой пояс компании отличается от вашего! Здесь даты и время отображаются в часовом поясе компании\n"
"                   </span>\n"
"                    ("

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>You can only take this time off in whole days, so if your schedule has"
" half days, it won't be used efficiently.</span>"
msgstr ""
"<span>Взять отгул можно только за целые дни, поэтому, если в вашем "
"расписании есть полдня, он не будет использован эффективно.</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>Департаменты и сотрудники</strong>"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Отличный способ отслеживать количество отпускных дней сотрудников, "
"больничных и статуса одобрения."

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr ""
"Отличный способ отслеживать запросы на отгулы, дни болезни и статус их "
"утверждения."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "A time off cannot be duplicated."
msgstr "Отгул не может быть продублирован."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "Возможность просмотра оставшегося времени отсутствия"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "Неявка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "Неявка на сегодня"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr ""
"Отсутствующий сотрудник (сотрудники), чьи заявки на отгул были подтверждены "
"или одобрены сегодня"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "Отсутствующие сотрудники"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
msgid "Absent Today"
msgstr "Не явились сегодня"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Accrual (Future):"
msgstr "Начисление (будущее):"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "Оплачиваемые резервирования"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr "Уровень начисления"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr "План начисления"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_accrual_plan
msgid "Accrual Plan Available"
msgstr "План начисления Имеется"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "Уровень плана начислений"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "Accrual Plan's Employees"
msgstr "Сотрудники плана с начислениями"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr "Планы начисления"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
msgid "Accrual Time Off: Updates the number of time off"
msgstr "Оплачиваемые отсутствия: Обновляет количество отсуствий"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr "Начисления"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr "Подсчет начислений"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrued_gain_time
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__accrued_gain_time
msgid "Accrued Gain Time"
msgstr "Начисленное рабочее время"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Active"
msgstr "Активный"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "Активные распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Employee"
msgstr "Активный сотрудник"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Active Time Off"
msgstr "Активное Нерабочее Время"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Types"
msgstr "Активные типы"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "Активность"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "Состояние активности"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Значок типа активности"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "Типы Активности"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Add a description..."
msgstr "Добавить описание..."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "Добавьте причину..."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Add some description for the people that will validate it"
msgstr "Добавьте описание для людей, которые будут его проверять"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__added_value_type
msgid "Added Value Type"
msgstr "Тип добавленной стоимости"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "Администратор"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr "После периода этого начисления"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "После полудня"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "Все распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__all_employee_ids
msgid "All Employee"
msgstr "Все сотрудники"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "Все отсутствия"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__all
msgid "All accrued time carried over"
msgstr "Все начисленное время переносится на следующий период"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__last_several_days
msgid "All day"
msgstr "Весь день"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Allocated ("
msgstr "Распределенный ("

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr "Распределено (Дней/Часов)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation"
msgstr "Распределение"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
msgid "Allocation Approval"
msgstr "Согласование распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__private_name
msgid "Allocation Description"
msgstr "Описание распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr "Отображение отгулов"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_type
msgid "Allocation Mode"
msgstr "Режим распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr "Тип уведомления о выделении времени"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_remaining_display
msgid "Allocation Remaining Display"
msgstr "Отображение остатка ассигнований"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#, python-format
msgid "Allocation Request"
msgstr "Запрос на выделение средств"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "Заявки на выделение средств"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "Тип распределения"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Allocation of %s: %.2f %s to %s"
msgstr "Распределение %s: %.2f %s - %s"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr "Распределение по"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request must be confirmed or validated in order to refuse it."
msgstr ""
"Запрос на распределение должен быть подтвержден или одобрен для того, чтобы "
"его отклонить."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr "Распределение к согласованию"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_count
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Allocations"
msgstr "Распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allows_negative
msgid "Allow Negative Cap"
msgstr "Разрешить отрицательный конденсатор"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Attach Supporting Document"
msgstr "Разрешите приложить подтверждающий документ"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__holiday_type
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""
"Позволяет создавать заявки пакетно:\n"
"- По работнику: для конкретного работнику\n"
"- По компании: все работники указанной компании\n"
"- По отделу: все работники указанного отдела\n"
"- По тегу работнику: все работники определенной категории группы работников"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__already_accrued
msgid "Already Accrued"
msgstr "Уже начислено"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Amount"
msgstr "Сумма"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid "Amount in Negative"
msgstr "Сумма в отрицательном значении"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "An employee already booked time off which overlaps with this period:%s"
msgstr ""
"Сотрудник уже забронировал отгул, который совпадает с этим периодом:%s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "Анализируйте из"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "Аттестационный анализ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "Одобрение"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Approve"
msgstr "Одобрить"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr "Согласовать распределения"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#, python-format
msgid "Approved"
msgstr "Одобрено"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr "Одобренные запросы"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__officer
msgid "Approved by Time Off Officer"
msgstr "Утверждено сотрудником по вопросам отгулов"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Approved:"
msgstr "Одобрено:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__apr
msgid "April"
msgstr "Апрель"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Archive Employee Allocations"
msgstr "Архивное распределение сотрудников"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Archived"
msgstr "Архивировано"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Archived Time Off"
msgstr "Архивные отгулы"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_search_my
msgid "Archived Time Off Type"
msgstr "Тип архивного отгула"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Вы уверены, что хотите удалить?"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__allocation
msgid "At the allocation date"
msgstr "На дату распределения"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__end
msgid "At the end of the accrual period"
msgstr "В конце периода начисления"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__start
msgid "At the start of the accrual period"
msgstr "В начале периода начисления"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__year_start
msgid "At the start of the year"
msgstr "В начале года"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "At work"
msgstr "На работе"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr "Прикрепить файл"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "Вложения"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__aug
msgid "August"
msgstr "Август"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Available"
msgstr "Доступно"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Available:"
msgstr "Доступно:"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
#, python-format
msgid "Away"
msgstr "Нет на месте"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "Balance at the"
msgstr "Баланс на"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid "Based on worked time"
msgstr "На основе отработанного времени"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "Основной сотрудник"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "Подтверждено и согласовано"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__company
msgid "By Company"
msgstr "По компании"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__department
msgid "By Department"
msgstr "По подразделению"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__employee
msgid "By Employee"
msgstr "Работник"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__category
msgid "By Employee Tag"
msgstr "По метке сотрудника"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr "Утвержденный сотрудником"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr "Утверждающий сотрудник и сотрудник, ответственный за отгулы"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__holiday_type
msgid ""
"By Employee: Allocation/Request for individual Employee, By Employee Tag: "
"Allocation/Request for group of employees in category"
msgstr ""
"По сотруднику: Распределение/Запрос для сотрудника, По тегу сотрудника: "
"Распределение/запрос для группы сотрудников в категории"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "По времени работы"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr "Может согласовать"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_cancel
msgid "Can Cancel"
msgstr "Можно отменить"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__can_modify_value_type
msgid "Can Modify Value Type"
msgstr "Может изменять тип значения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
msgid "Can reset"
msgstr "Может сбросить"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Отменить"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel Future Time Off"
msgstr "Отмена отгулов в будущем"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
#, python-format
msgid "Cancel Time Off"
msgstr "Отмена отгулов"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_cancel_leave
msgid "Cancel Time Off Wizard"
msgstr "Мастер отмены отгулов"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel all time off after this date."
msgstr "Отмените все отгулы после этой даты."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Canceled"
msgstr "Отменено"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Canceled Time Off"
msgstr "Отмененные отгулы"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Cancelled"
msgstr "Отменен"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid "Cap accrued time"
msgstr "Начисленное время"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Cap:"
msgstr "Кап:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry over"
msgstr "Перенос"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__maximum
msgid "Carry over with a maximum"
msgstr "Перенос с максимальным"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry over:"
msgstr "Перенос:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry-Over Date"
msgstr "Дата переноса"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_date
msgid "Carry-Over Time"
msgstr "Время переноса"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day
msgid "Carryover Day"
msgstr "День переноса"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day_display
msgid "Carryover Day Display"
msgstr "Дисплей дня переноса"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_month
msgid "Carryover Month"
msgstr "Месяц переноса"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__category_id
msgid "Category of Employee"
msgstr "Категория сотрудника"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
#, python-format
msgid ""
"Changing this working schedule results in the affected employee(s) not "
"having enough leaves allocated to accomodate for their leaves already taken "
"in the future. Please review this employee's leaves and adjust their "
"allocation accordingly."
msgstr ""
"Изменение этого графика работы приведет к тому, что затронутому сотруднику "
"(сотрудникам) не хватит выделенных отпусков, чтобы компенсировать уже взятые"
" отпуска в будущем. Пожалуйста, проверьте отпуска этого сотрудника и "
"скорректируйте их распределение соответствующим образом."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual."
msgstr "Выберите лимит для этого начисления."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_ids
msgid ""
"Choose the Time Off Officers who will be notified to approve allocation or "
"Time Off Request. If empty, nobody will be notified"
msgstr ""
"Выберите сотрудников, которым будет отправлено уведомление об утверждении "
"распределения или запроса на отгул. Если пусто, никто не будет уведомлен"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Click on any date or on this button to request a time-off"
msgstr "Нажмите на любую дату или на эту кнопку, чтобы запросить отгул"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "Цвет"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "Компания"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__mode_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__mode_company_id
msgid "Company Mode"
msgstr "Режим работы компании"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "Компенсационные дни"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Configuration"
msgstr "Конфигурация"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Confirmation"
msgstr "Подтверждение"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
#, python-format
msgid "Confirmed"
msgstr "Подтверждено"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#, python-format
msgid "Confirmed and Approved"
msgstr "Подтверждено и утверждено"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Congrats, we can see that your request has been validated."
msgstr "Поздравляем, мы видим, что ваш запрос был подтвержден."

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of allocations for this time off type (approved or waiting for "
"approbation) with a validity period starting this year."
msgstr ""
"Количество распределений для данного типа отгулов (утвержденных или "
"ожидающих утверждения) со сроком действия, начиная с этого года."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Count of plans linked to this time off type."
msgstr "Количество планов, связанных с этим типом отгулов."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of time off requests for this time off type (approved or waiting for "
"approbation) with a start date in the current year."
msgstr ""
"Количество заявок на отгулы для данного типа отгулов (одобренных или "
"ожидающих одобрения) с датой начала в текущем году."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "Изображение обложки"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "Создать распределение отсуствия"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "Создать запрос на распределение отсуствия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "Создано"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__current_leave_state
msgid "Current Time Off Status"
msgstr "Статус Текущего Отпуска"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "Тип Текущего Отпуска"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Current Year"
msgstr "Текущий год"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Currently Valid"
msgstr "В настоящее время действует"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "Нестандартные часы"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "Ежедневно"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "Панель управления"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Date"
msgstr "Дата"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "Начало периода"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr "Дата последнего распределения начислений"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr "Дата следующего оплачиваемого распределения"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "Даты"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "День"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__day
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
#, python-format
msgid "Days"
msgstr "Дней"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__dec
msgid "December"
msgstr "Декабрь"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid ""
"Define the maximum level of negative days this kind of time off can reach. "
"Value must be at least 1."
msgstr ""
"Определите максимальный уровень отрицательных дней, которого может достичь "
"этот вид отгулов. Значение должно быть не менее 1."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "Delete"
msgstr "Удалить"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#, python-format
msgid "Delete Confirmation"
msgstr "Подтверждение Удаления"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Delete Time Off"
msgstr "Удалить отгулы"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "Отдел"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr "Поиск по отделам"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__department_ids
msgid "Departments"
msgstr "Отделения"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Мастер Ухода"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
msgid "Description"
msgstr "Описание"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr "Описание с действительностью"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Discard"
msgstr "Отменить"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr "Варианты отображения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "Отображение Отпуска в Календаре"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Due to a change in global time offs, %s extra day(s) have been taken from "
"your allocation. Please review this leave if you need it to be changed."
msgstr ""
"В связи с изменением глобальных отгулов, %s дополнительных дня(ов) были "
"взяты из вашего отпуска. Пожалуйста, пересмотрите этот отпуск, если вам "
"нужно его изменить."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Due to a change in global time offs, this leave no longer has the required "
"amount of available allocation and has been set to refused. Please review "
"this leave."
msgstr ""
"В связи с изменениями в глобальных отгулах этот отпуск больше не имеет "
"необходимого количества доступных ассигнований и был отменен. Пожалуйста, "
"пересмотрите этот отпуск."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Due to a change in global time offs, you have been granted %s day(s) back."
msgstr ""
"В связи с изменением глобальных отгулов вам было предоставлено %s дня(ов) "
"отдыха."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "Продолжительность"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Days)"
msgstr "Длительность (дни)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours
msgid "Duration (Hours)"
msgstr "Продолжительность (часы)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "Продолжительность (дней)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "Длительность(часы)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days_display
msgid "Duration in days"
msgstr "Длительность в днях"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr "Длительность в днях. Ссылочное поле используется при необходимости."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_display
msgid "Duration in hours"
msgstr "Длительность в часах"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr "Редактировать распределение"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "Редактировать Отпуск"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "Сотрудник"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
msgid "Employee Active"
msgstr "Активный сотрудник"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
msgid "Employee Company"
msgstr "Компания-работодатель"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr "Запросы сотрудников"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__category_id
msgid "Employee Tag"
msgstr "Метка сотрудника"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Employee accrue"
msgstr "Начисление сотрудникам"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "Сотрудник(и)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_ids
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Employees"
msgstr "Сотрудники"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Employees Off Today"
msgstr "Сотрудники сегодня не работают"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__end_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "Дата окончания"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr "Разрешены дополнительные дни"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__employee_requests
msgid ""
"Extra Days Requests Allowed: User can request an allocation for himself.\n"
"\n"
"        Not Allowed: User cannot request an allocation."
msgstr ""
"Разрешены запросы на дополнительные дни: Пользователь может запросить выделение для себя.\n"
"\n"
"        Не разрешено: Пользователь не может запросить выделение."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__feb
msgid "February"
msgstr "Февраль"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr ""
"Поле позволяет увидеть продолжительности распределения в днях или часах в "
"зависимости от type_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr ""
"Поле позволяет увидеть длительность отсутствия в запросе в днях и часах в "
"зависимости от leave_type_request_unit"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid ""
"Filters only on allocations that belong to a time off type that is 'active' "
"(active field is True)"
msgstr ""
"Фильтрует только те распределения, которые принадлежат к типу \"активное "
"время\" (поле \"активное\" равно True)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "First Approval"
msgstr "Первое согласование"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr "Первый день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr "Дисплей первого дня"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr "Первый месяц"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr "Первый месяц День"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr "Отображение дня первого месяца"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Шрифт, отличный значок, например. к.-а.-задачи"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"For an Accrual Allocation, this field contains the theorical amount of time "
"given to the employee, due to a previous start date, on the first run of the"
" plan. This can be manually edited."
msgstr ""
"Для распределения по начислениям это поле содержит теоретическое количество "
"времени, предоставленное сотруднику в связи с предыдущей датой начала работы"
" при первом выполнении плана. Это поле можно редактировать вручную."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "Частота"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "Пятница"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "От"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "С даты"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "Планируемые действия"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "Grant Time"
msgstr "Время предоставления гранта"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "Группировать по"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr "Отсутствия"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr "HR Подтверждение"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__report_note
msgid "HR Comments"
msgstr "HR комментарии"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr "Краткий отчет об отпусках по сотрудникам"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__half_day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "Часть дня"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_mandatory_day
msgid "Has Mandatory Day"
msgstr "Обязательный день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr "Имеет действительное распределение"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr "Вылупившиеся"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Holds whether this allocation concerns more than 1 employee"
msgstr "Указывает, относится ли данное распределение к более чем 1 сотруднику"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr "Праздничный статус"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr "Сводный отчет о праздниках"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_home_working
#: model:mail.message.subtype,name:hr_holidays.mt_leave_home_working
msgid "Home Working"
msgstr "Работа на дому"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "Час от"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "Час до"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__hourly
msgid "Hourly"
msgstr "Каждый час"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
#, python-format
msgid "Hours"
msgstr "Часов"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr "Отображение значков часов"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий исключение Дела."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "Idle"
msgstr "Неактивно"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid ""
"If checked, the accrual period will be calculated according to the work "
"days, not calendar days."
msgstr ""
"Если флажок установлен, период начисления будет рассчитываться по рабочим, а"
" не календарным дням."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allows_negative
msgid ""
"If checked, users request can exceed the allocated days and balance can go "
"in negative."
msgstr ""
"Если флажок установлен, запрос пользователя может превысить отведенные дни, "
"и баланс может стать отрицательным."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Если значение активного поля \"ложно\", это позволит вам скрыть запись "
"ресурса, не удаляя ее."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr ""
"Если активное поле установлено в значение false, то это позволит скрыть тип "
"отпуска без его удаления."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr ""
"Если Вы хотите изменить количество дней, Вам следует использовать режим "
"'период'"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "Немедленно"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Incorrect state for new allocation"
msgstr "Неправильное состояние для нового распределения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr "Офицер"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "Не оплачивается"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_user_only_responsible
msgid "Is User Only Responsible"
msgstr "Несет ли ответственность только пользователь"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jan
msgid "January"
msgstr "Январь"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "Работа"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "Место работы"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jul
msgid "July"
msgstr "Июль"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jun
msgid "June"
msgstr "Июнь"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr "Следите за своими отгулами."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Time Off"
msgstr "Виды отгулов"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "Поздние Мероприятия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_increases_duration
msgid "Leave Type Increases Duration"
msgstr "Тип отпуска Увеличивает продолжительность"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "Слева"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Legend"
msgstr "Легенда"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's approve it"
msgstr "Давайте утвердим его"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's discover the Time Off application"
msgstr "Давайте откроем приложение Time Off"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's go validate it"
msgstr "Давайте подтвердим это"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Let's try to create a Sick Time Off, select it in the list"
msgstr "Давайте попробуем создать отгул по болезни, выберите его в списке"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "Уровни"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr "Ограничение"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__linked_request_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__linked_request_ids
msgid "Linked Requests"
msgstr "Связанные запросы"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_management
msgid "Management"
msgstr "Управление"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
msgid "Manager"
msgstr "Руководитель"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr "Утверждение менеджера"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_mandatory_day
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
msgid "Mandatory Day"
msgstr "Обязательный день"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_mandatory_day_action
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_mandatory_day_menu_configuration
#, python-format
msgid "Mandatory Days"
msgstr "Обязательные дни"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__mar
msgid "March"
msgstr "Март"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Mark as Draft"
msgstr "Пометить как черновик"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr "Макс Отпуска"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr "Макс Отпуска:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "Максимум допустимо"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr ""
"Максимальный разрешенный отпуск - Отпуск уже взято - Отпуск ожидание "
"одобрения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Maximum amount of accruals to transfer"
msgstr "Максимальная сумма начислений для перевода"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__may
msgid "May"
msgstr "Май"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr "Встречайте панель управления отгулами."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "Встреча"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Подтипы сообщений"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
msgid "Milestone"
msgstr "Этап"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Milestone Transition"
msgstr "Переходный этап"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Milestone reached"
msgstr "Достигнутый рубеж"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Mode"
msgstr "Режим"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "Понедельник"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "Месяц"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "Ежемесячно"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "Months"
msgstr "Месяцев"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "Утро"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Multi Employee"
msgstr "Несколько сотрудников"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "Мои резервирования"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr "Мой отдел"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "Мои запросы"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "Моя команда"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
msgid "My Time"
msgstr "Мое время"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "Мои отсутсвия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "Имя"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Negative Cap"
msgstr "Отрицательный колпачок"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__draft
#, python-format
msgid "New"
msgstr "Новый"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "New %(leave_type)s Request created by %(user)s"
msgstr "Новый %(leave_type)s запрос, созданный %(user)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#, python-format
msgid "New Allocation"
msgstr "Новое распределение"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "New Allocation Request"
msgstr "Новое распределение"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr ""
"Новый запрос на выделение средств, созданный %(user)s: %(count)s Дни из "
"%(allocation_type)s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "New Milestone"
msgstr "Новый этап"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "New Time Off"
msgstr "Новое время отдыха"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следующее событие календаря активности"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Следующий срок мероприятия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего действия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "Следующий Тип Мероприятия"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "Без ограничений"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "Без проверки"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.act_hr_employee_holiday_type
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid "No data to display"
msgstr "Нет данных для отображения"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_department
msgid "No data yet!"
msgstr "Пока нет данных!"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "Безлимитный"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rule has been set up for this accrual plan."
msgstr "Для этого плана начислений не было разработано никаких правил."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no
msgid "No validation needed"
msgstr "Проверка не требуется"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Nobody will be notified"
msgstr "Никто не будет уведомлен"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#, python-format
msgid "None"
msgstr "Нет"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "None. Accrued time reset to 0"
msgstr "Нет. Начисленное время обнуляется до 0"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr "Не разрешено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_ids
msgid "Notified Time Off Officer"
msgstr "Сотрудник, получивший отгул"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__nov
msgid "November"
msgstr "Ноябрь"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_text
msgid "Number Of Hours Text"
msgstr "Количество часов Текст"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
msgid "Number of Days"
msgstr "Количество дней"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr "Количество отсутствий"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days_display
msgid ""
"Number of days of the time off request according to your working schedule. "
"Used for interface."
msgstr ""
"Количество дней запроса на отпуск в соответствии с вашим рабочим графиком. "
"Используется для интерфейса."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid "Number of days of the time off request. Used in the calculation."
msgstr "Количество дней отгула. Используется при расчете."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours_display
msgid ""
"Number of hours of the time off request according to your working schedule. "
"Used for interface."
msgstr ""
"Количество часов запроса на отпуск в соответствии с вашим рабочим графиком. "
"Используется для интерфейса."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours
msgid "Number of hours of the time off request. Used in the calculation."
msgstr "Количество часов запроса на отгул. Используется при расчете."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__oct
msgid "October"
msgstr "Октябрь"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Off Till"
msgstr "Пока"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr "Сегодня не работает"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer: Manage all requests"
msgstr "Офицер: Управление всеми запросами"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "On Time Off"
msgstr "Время отдыха"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr "В отпуске"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
#, python-format
msgid "Online"
msgstr "Онлайн"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Only"
msgstr "Только"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a refused leave."
msgstr "Отменить отказ от отпуска может только менеджер по работе с отгулами."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a started leave."
msgstr "Сбросить начатый отпуск может только менеджер по отгулам."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset other people leaves."
msgstr "Только менеджер по отгулам может сбрасывать отпуска других людей."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Only a Time Off Officer or Manager can approve/refuse its own requests."
msgstr ""
"Только сотрудник или менеджер может утверждать/отклонять свои запросы."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Only a time off Manager can approve its own requests."
msgstr "Только менеджер по отпуску может утверждать свои собственные заявки."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only a time off Officer/Responsible or Manager can approve or refuse time "
"off requests."
msgstr ""
"Только Офицер / Ответственный или Менеджер может утверждать или отклонять "
"запросы на предоставление отгулов."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
#, python-format
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__other
msgid "Other"
msgstr "Другое"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
#, python-format
msgid "Out of office"
msgstr "Вне офиса"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/persona_service_patch.js:0
#, python-format
msgid "Out of office until %s"
msgstr "Вне офиса до %s"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "Обзор"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr "Оплачиваемое отсутствие"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__parent_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__parent_id
msgid "Parent"
msgstr "Родитель"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr "Отпуска по уходу за ребенком"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "Период"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "Проектируемый"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Planned:"
msgstr "Планируется:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr "Присутствует, но находится в отпуске"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Печать"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Provide a reason to delete an approved time off"
msgstr "Укажите причину удаления утвержденного отгула"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public"
msgstr "Публичный"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
#, python-format
msgid "Public Holidays"
msgstr "Государственные праздники"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "Ставка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__rating_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__reason
msgid "Reason"
msgstr "Причина"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
msgid "Reasons"
msgstr "Причины"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Refuse"
msgstr "Отказаться"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#, python-format
msgid "Refused"
msgstr "Отказано"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Refused Time Off"
msgstr "Отказ от отгулов"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
msgid "Regular Allocation"
msgstr "Обычное резервирование"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Пользователь управляющий доступом к ресурсу."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "Оставшиеся дни"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Remaining Paid Time Off"
msgstr "Оставшийся оплачиваемый отпуск"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "Оставшиеся отсуствия"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Remove employee from existing accrual plans."
msgstr "Исключите сотрудника из существующих планов начислений."

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Отчет"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr "Запрос на отгул"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "Дата окончания запроса"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "Запрос Начальной даты"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr "Запрос на Отпуск"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "Тип запроса"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "Запрашиваемый (дни/часы)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr "Требуется распределение"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__resource_calendar_id
msgid "Resource Calendar"
msgstr "Календарь ресурсов"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "Время отдыха"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Подробности отсутствий"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr "Рабочее время"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "Ответственный пользователь"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "Правила"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "Работайте до тех пор, пока"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "Суббота"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#, python-format
msgid "Save"
msgstr "Сохранить"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr "Поиск Отпуска"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "Поиск Типа Отпуска"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr "Поиск распределений"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Second Approval"
msgstr "Второе согласование"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr "Второй день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr "Дисплей второго дня"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr "Второй месяц"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr "День второго месяца"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr "Отображение дня второго месяца"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Second approval request for %(leave_type)s"
msgstr "Второй запрос на утверждение для %(leave_type)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Select Time Off"
msgstr "Выберите время отдыха"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "Выбор Типа Отпуска"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__validation_type
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allocation_validation_type
msgid ""
"Select the level of approval needed in case of request by employee\n"
"        - No validation needed: The employee's request is automatically approved.\n"
"        - Approved by Time Off Officer: The employee's request need to be manually approved by the Time Off Officer."
msgstr ""
"Выберите уровень одобрения, необходимый в случае запроса от сотрудника\n"
"        - Утверждение не требуется: Запрос сотрудника автоматически утверждается.\n"
"        - Одобрено сотрудником по отгулам: Запрос сотрудника должен быть вручную одобрен сотрудником, ответственным за отгулы."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Select the request you just created"
msgstr "Выберите запрос, который вы только что создали"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Выберите пользователя, ответственного за утверждение \"Отгулов\" данного сотрудника.\n"
"Если пусто, то утверждение производится Администратором или Утвердителем (определяется в настройках/пользователи)."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__sep
msgid "September"
msgstr "Сентябрь"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr ""
"Последовательность формируется автоматически по дельте времени запуска."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Set a maximum of accruals an allocation keeps at the end of the year."
msgstr ""
"Установите максимальный размер начислений, которые распределение сохраняет в"
" конце года."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__show_transition_mode
msgid "Show Transition Mode"
msgstr "Показать режим перехода"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия наступает до "
"сегодняшнего дня"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr "Отсутствие по болезни"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "Some leaves cannot be linked to any allocation. To see those leaves,"
msgstr ""
"Некоторые листья не могут быть связаны ни с одним распределением. Чтобы "
"увидеть эти листья,"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid ""
"Some of the accrual plans you're trying to delete are linked to an existing "
"allocation. Delete or cancel them first."
msgstr ""
"Некоторые планы начислений, которые вы пытаетесь удалить, связаны с уже "
"существующими документами распределения. Сначала удалите или отмените эти "
"документы."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""
"Укажите, может ли этот план начислений использоваться только с данным типом отгулов.\n"
"                Оставьте пустым, если этот план начислений можно использовать с любым типом отгулов."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Specify what occurs if a level transition takes place in the middle of a pay period.\n"
"\n"
"                'Immediately' will switch the employee to the new accrual level on the exact date during the ongoing pay period.\n"
"\n"
"                'After this accrual's period' will keep the employee on the same accrual level until the ongoing pay period is complete.\n"
"                After it is complete, the new level will take effect when the next pay period begins."
msgstr ""
"Укажите, что произойдет, если переход на другой уровень произойдет в середине периода оплаты.\n"
"\n"
"                'Немедленно' переведет сотрудника на новый уровень начислений в точную дату в течение текущего периода оплаты.\n"
"\n"
"                'После периода этого начисления' оставит сотрудника на прежнем уровне начислений до завершения текущего периода оплаты.\n"
"                После его завершения новый уровень вступит в силу с началом следующего периода оплаты."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__start_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Start Date"
msgstr "Дата начала"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr "Начало после"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "Область"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "Статус"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус, основанный на мероприятии\n"
"Просроченная: Дата, уже прошла\n"
"Сегодня: Дата мероприятия сегодня\n"
"Запланировано: будущая деятельность."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr "Страйк"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid "Submit your request"
msgstr "Отправить запрос"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "Сумма"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "Воскресенье"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr "Поддерживаемые идентификаторы вложений Счетчик"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr "Вспомогательный документ"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Supporting Documents"
msgstr "Вспомогательные документы"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "Взять отпуск"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
#, python-format
msgid "Taken"
msgstr "Принятые"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The Start Date of the Validity Period must be anterior to the End Date."
msgstr "Дата начала периода действия должна быть раньше даты окончания."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr ""
"Начисление начинается через определенный период времени после даты начала "
"распределения. Это поле определяет количество дней, месяцев или лет, по "
"истечении которых используется начисление."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid ""
"The allocation requirement of a time off type cannot be changed once leaves "
"of that type have been taken. You should create a new time off type instead."
msgstr ""
"Требование по распределению для данного вида отпуска нельзя изменить, если "
"уже были оформлены отпуска этого вида. Рекомендуется создать новый вид "
"отпуска."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr ""
"Выбранный здесь цвет будет использоваться на всех экранах с типом \"Отгул\"."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr "Установленные вами даты не верны. Пожалуйста, проверьте их."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"The distinction between working time (ex. Attendance) and absence (ex. "
"Training) will be used in the computation of Accrual's plan rate."
msgstr ""
"Различие между рабочим временем (например, посещение) и отсутствием "
"(например, обучение) будет использоваться при расчете плановой ставки "
"начисления."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr "Продолжительность должна быть больше 0."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_type_value
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_value
msgid ""
"The employee, department, company or employee category of this request is "
"missing. Please make sure that your user login is linked to an employee."
msgstr ""
"Сотрудник, категория сотрудника, подразделение или компания отсутствуют в "
"запросе. Убедитесь, что ваша учетная запись пользователя связана с "
"работником."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr ""
"Следующие сотрудники не должны работать в этот период:\n"
" %s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid ""
"The leaves planned in the future are exceeding the maximum value of the allocation.\n"
"                It will not be possible to take all of them."
msgstr ""
"Отпуска, запланированные на будущее, превышают максимальную величину ассигнований.\n"
"                Все их взять не удастся."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_check_negative
msgid ""
"The negative amount must be greater than 0. If you want to set 0, disable "
"the negative cap instead."
msgstr ""
"Отрицательная величина должна быть больше 0. Если вы хотите установить 0, "
"отключите отрицательный колпачок."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr ""
"Количество часов/дней, которое будет увеличиваться в указанном типе отгулов "
"за каждый период"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check3
msgid ""
"The request start date must be before or equal to the request end date."
msgstr ""
"Дата начала запроса должна быть раньше или равна дате окончания запроса."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_mandatory_day_date_from_after_day_to
msgid "The start date must be anterior than the end date."
msgstr "Дата начала должна быть раньше даты окончания."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be before or equal to the end date."
msgstr "Дата начала должна быть раньше или равна дате окончания."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"Статус установлен на ‘Отправить’, когда создается запрос на отпуск.\n"
"Статус ‘Подтвердить’, когда запрос на отпуск подтверждается пользователем.\n"
"Статус ‘Отказаться’, когда запрос на отпуск отклонен менеджером.\n"
"Статус ‘Утвержден’, когда запрос на отпуск одобрен менеджером."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is set to 'To Submit', when an allocation request is created.\n"
"The status is 'To Approve', when an allocation request is confirmed by user.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""
"Статус установлен на ‘Отправить’, когда создается запрос на отгул.\n"
"Статус «Подтвердить», когда заявка на отгул подтверждена пользователем.\n"
"Статус « Отказано», когда заявка на отгул отклонена менеджером.\n"
"Статус «Утверждена», когда заявкана отгул утверждена менеджером."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "The time off has been automatically approved"
msgstr "Отпуск был автоматически одобрен"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "The time off has been canceled: %s"
msgstr "Отгул был отменен: %s"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr ""
"Тип с наименьшей последовательностью является значением по умолчанию в "
"запросе на отпуск."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "There is no valid allocation to cover that request."
msgstr ""
"Для удовлетворения этого запроса не существует действующих ассигнований."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, delete and create a new one."
msgstr ""
"Это распределение уже было запущено однажды, любое изменение не будет "
"действовать на дни, выделенные сотруднику. Если вам нужно изменить "
"конфигурацию распределения, удалите его и создайте новое."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr ""
"Эта область автоматически заполняется пользователем, который проверяет "
"соответствие времени отпуска"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr ""
"Эта область автоматически заполняется пользователем, который утверждает "
"отсутствие на втором уровне (если тип отсутствие требует второго "
"утверждения)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr ""
"Эта область автоматически заполняется пользователем, который подтверждает "
"распределение"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr ""
"Это поле определяет единицу времени, после которой начинается начисление."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr "Указывает, что всё ещё можно использовать этот тип отсутствия"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "This modification is not allowed in the current state."
msgstr "Данная модификация не допускается в текущем статусе."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "This time off cannot be canceled."
msgstr "Этот отгул не может быть отменен."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr ""
"Данное значение задается суммой всех запросов на отпуск с отрицательным "
"значением."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr "Данное значение задается суммой всех принятых запросов на отсутствие."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "Четверг"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#, python-format
msgid "Time Off"
msgstr "Отпуск"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Распределение отпусков"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.act_hr_employee_holiday_type
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_department
#, python-format
msgid "Time Off Analysis"
msgstr "Анализ Отпусков"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
msgid "Time Off Approval"
msgstr "Согласование отсутствия"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr "Утвердитель отгулов"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Отпуск Календарь"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar__associated_leaves_count
msgid "Time Off Count"
msgstr "Учет свободного времени"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee.py:0
#, python-format
msgid "Time Off Dashboard"
msgstr "Приборная панель отгулов"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "Время отдыха Описание"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr "Тип уведомления о времени отсутствия"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""
"Руководители выделяют сотрудникам нерабочие дни (например, оплачиваемый отпуск). <br>\n"
"                 Сотрудники запрашивают выделение им свободного времени (например, дни для восстановления сил)."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#, python-format
msgid "Time Off Request"
msgstr "Запрос Отпуска"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "Запросы на отсутствие"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr "Ответственное время"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr "Второе согласование отсутствия"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "Отпуска Резюме"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "Отпуска Резюме / Отчет"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr "Отпуска, взятые:"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "Тип отсутсвия"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "Типы отсутствий"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Time Off Validation"
msgstr "Проверка отгулов"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr "Отпуска участника Вашей команды"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr "Отпуска для Утверждения"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "Отпуска."

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_cron_cancel_invalid_ir_actions_server
msgid "Time Off: Cancel invalid leaves"
msgstr "Отгулы: отмена недействительных отпусков"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Time off"
msgstr "Свободное время"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr "Отпуска уже взятые"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr "Анализ отгулов по сотрудникам и типам отгулов"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_pivot
msgid "Time off Summary"
msgstr "Отгулы Резюме"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Time off Taken"
msgstr "Взятые отгулы"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr "Отпуска людей, для которых вы являетесь менеджером"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""
"Запрос на отпуск должен быть подтвержден («Утвердить») для его утверждения."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed in order to approve it."
msgstr "Запрос на отпуск должен быть подтвержден для его утверждения."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr ""
"Запрос на отпуск должен быть подтвержден или одобрен для того, чтобы "
"отказаться в нем."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be in Draft state (\"To Submit\") in order to confirm "
"it."
msgstr ""
"Запрос на отпуск должен быть в состоянии Черновик («Для отправки»), чтобы "
"его можно было подтвердить."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request state must be \"Refused\" or \"To Approve\" in order to be "
"reset to draft."
msgstr ""
"Состояние запроса на отпуск должно быть «Отказано» или «Утвердить», чтобы "
"быть перенесенным в черновик."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "Часовой пояс"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "Кому"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#, python-format
msgid "To Approve"
msgstr "К согласованию"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "To Approve or Approved Allocations"
msgstr "Утвердить или одобрить распределение"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "До даты"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__draft
msgid "To Submit"
msgstr "К Подтверждению"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
#, python-format
msgid "Today"
msgstr "Сегодня"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "Сегодняшние Дела"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr "Общее количество ассигнований"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr "Общее количество распределенных дней."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"Общее количество оплачиваемых отгулов, выделенных данному сотруднику, "
"изменить данное значение для создания заявки на выделение/отгул. Всего по "
"всем видам отгулов без превышения лимита."

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_training
msgid "Training Time Off"
msgstr "Учебное время"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Попробуйте добавить несколько записей или убедитесь, что в строке поиска нет"
" активного фильтра."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "Вторник"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr "Дважды в месяц"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr "Два раза в год"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
#, python-format
msgid ""
"Two public holidays cannot overlap each other for the same working hours."
msgstr ""
"Два праздничных дня не могут накладываться друг на друга в течение одного и "
"того же рабочего дня."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "Тип"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
msgid "Type Request Unit"
msgstr "Тип Запрос Единица измерения"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип Дела для исключения в записи."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr "Временная зона"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr "Несоответствие временной зоны"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Unlimited"
msgstr "Неограниченно"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "Неоплаченный"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "Неоплачиваемое отсутствие"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Up to"
msgstr "Вплоть до"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "Пользователь"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "User is idle"
msgstr "Пользователь бездействует"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "User is online"
msgstr "Пользователь онлайн"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#, python-format
msgid "User is out of office"
msgstr "Пользователь вне офиса"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Validate"
msgstr "Подтвердить "

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Validated"
msgstr "Подтверждено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr "Тип Валидации"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr "Срок действия"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr "Начало срока действия"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr "Остановка действия"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr "Виртуальный оставшийся отпуск"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "Ожидает одобрения"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Waiting For Me"
msgstr "Список задач"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "Ожидает второго согласования"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr "Ожидает утверждения"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "Среда"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/year/calendar_year_renderer.js:0
#, python-format
msgid "Week"
msgstr "Неделя"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "Еженедельно"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Worked Time"
msgstr "Отработанное время"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__resource_calendar_id
msgid "Working Hours"
msgstr "Часы работы"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "Ежегодно"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr "День в году"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr "Отображение дней в году"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr "Ежегодно Месяц"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "Years"
msgstr "Лет"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "Да"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__requires_allocation
msgid ""
"Yes: Time off requests need to have a valid allocation.\n"
"\n"
"              No Limit: Time Off requests can be taken without any prior allocation."
msgstr ""
"Да: Заявки на отгулы должны иметь действительное распределение.\n"
"\n"
"              Без ограничений: заявки на отгулы можно принимать без предварительного распределения."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You are not allowed to request a time off on a Mandatory Day."
msgstr "Вы не имеете права просить отгул в обязательный день."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You can not have 2 time off that overlaps on the same day."
msgstr ""
"Вы не можете иметь два отгула, которые накладываются друг на друга в один и "
"тот же день."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr "Вы не можете начать начисление в прошлом."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
#, python-format
msgid ""
"You can select the period you need to take off, from start date to end date"
msgstr ""
"Вы можете выбрать период, за который нужно снять деньги, от даты начала до "
"даты окончания"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You can't manually archive/unarchive a time off."
msgstr "Вы не можете вручную архивировать/разархивировать отгулы."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot archive an allocation which is in confirm or validate state."
msgstr ""
"Вы не можете архивировать распределение, находящееся в состоянии "
"подтверждения или проверки."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off assigned to several employees"
msgstr "Вы не можете удалить отгул, назначенный нескольким сотрудникам"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in %s state"
msgstr "Вы не можете удалить отпуск, находящийся в %s состоянии"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in the past"
msgstr "Вы не можете удалить отгул, который был в прошлом"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr ""
"Вы не можете удалить запрос на распределение, который имеет несколько "
"подтвержденных листьев."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "You cannot delete an allocation request which is in %s state."
msgstr "Нельзя удалить заявку на отгул, которая находится в состоянии %s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr ""
"Вы не можете сначала утвердить отгул для %s, потому что вы не являетесь его "
"менеджером по отгулам"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
#, python-format
msgid ""
"You cannot have a cap on accrued time without setting a maximum amount."
msgstr ""
"Нельзя установить ограничение на начисляемое время без задания максимального"
" значения."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot reduce the duration below the duration of leaves already taken by"
" the employee."
msgstr ""
"Нельзя уменьшить продолжительность ниже уже использованных сотрудником дней "
"отпуска."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot refuse this allocation request since the employee has already "
"taken leaves for it. Please refuse or delete those leaves first."
msgstr ""
"Вы не можете отклонить этот запрос на распределение, поскольку сотрудник уже"
" брал для него отпуск. Пожалуйста, сначала откажитесь или удалите эти "
"отпуска."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr "У вас нет прав на повторное одобрение заявки на отгул"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You must be %s's Manager to approve this leave"
msgstr "Вы должны быть менеджером %s, чтобы одобрить этот отпуск"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr ""
"Вы должны быть менеджером %s или менеджером по отгулам, чтобы одобрить этот "
"отпуск"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must either be a Time off Officer or Time off Manager to approve this "
"leave"
msgstr ""
"Вы должны быть сотрудником отдела отгулов или менеджером отдела отгулов, "
"чтобы одобрить этот отпуск"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr "Для уровней плана начисления необходимо указать коэффициент больше 0."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr ""
"Вы должны иметь права менеджера изменять/проверять отпуск, который уже "
"начался"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You've already booked time off which overlaps with this period:\n"
"%s\n"
"Attempting to double-book your time off won't magically make your vacation 2x better!\n"
msgstr ""
"Вы уже забронировали отгул, который совпадает с этим периодом:\n"
"%s\n"
"Попытка дважды забронировать время отдыха не сделает ваш отпуск волшебным образом в 2 раза лучше!\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr "Ваш %(leave_type)s, запланированный на %(date)s, был принят"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr "Ваш %(leave_type)s, запланированный на %(date)s, был отклонен"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your Time Off"
msgstr "Ваше свободное время"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_holidays_cancel_leave.py:0
#, python-format
msgid "Your time off has been canceled."
msgstr "Ваш отгул был отменен."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after"
msgstr "после"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation start date"
msgstr "после даты начала распределения"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "all"
msgstr "все"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "and"
msgstr "и"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr "и на"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "available"
msgstr "доступно"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "по Сотруднику"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "по Типу"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "can be used before the allocation expires."
msgstr "можно использовать до истечения срока действия."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "click here"
msgstr "нажмите здесь"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr "день месяца"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#, python-format
msgid "day(s)"
msgstr "дней"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "days"
msgstr "дней"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr "дни месяцев"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "days)"
msgstr "дней)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "deleted by %s (uid=%d)."
msgstr "удалено %s (uid=%d)."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr ""
"например, тип отгула (С начала срока действия до конца срока действия / без "
"ограничений)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "from %(date_from)s to %(date_to)s - %(state)s"
msgstr "от %(date_from)s до %(date_to)s - %(state)s"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "hours"
msgstr "часов"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
#, python-format
msgid "in"
msgstr "в"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "initially"
msgstr "изначально"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
#, python-format
msgid "last day"
msgstr "последний день"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "new request"
msgstr "новый запрос"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "no"
msgstr "нет"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "of"
msgstr "из"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "из"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr "месяца"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "вкл"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr "на"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr "отказался"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr "последовательность"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "taken"
msgstr "взято"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "the accruated amount is insufficient for that duration."
msgstr "начисленная сумма недостаточна для этого срока."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "в"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "to refuse"
msgstr "отказываться"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "up to"
msgstr "вплоть до"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#, python-format
msgid "valid until"
msgstr "действителен до"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr "подтвердить"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr "проверено"
