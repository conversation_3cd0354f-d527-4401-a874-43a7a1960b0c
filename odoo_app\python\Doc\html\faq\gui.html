<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Graphic User Interface FAQ" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/faq/gui.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Contents: Graphic User Interface FAQ- General GUI Questions, What GUI toolkits exist for Python?, Tkin<PERSON> questions- How do I freeze Tkinter applications?, Can I have Tk events handled while waitin..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Contents: Graphic User Interface FAQ- General GUI Questions, What GUI toolkits exist for Python?, Tkinter questions- How do I freeze Tkinter applications?, Can I have Tk events handled while waitin..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Graphic User Interface FAQ &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="“Why is Python Installed on my Computer?” FAQ" href="installed.html" />
    <link rel="prev" title="Python on Windows FAQ" href="windows.html" />
    <link rel="canonical" href="https://docs.python.org/3/faq/gui.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Graphic User Interface FAQ</a><ul>
<li><a class="reference internal" href="#general-gui-questions">General GUI Questions</a></li>
<li><a class="reference internal" href="#what-gui-toolkits-exist-for-python">What GUI toolkits exist for Python?</a></li>
<li><a class="reference internal" href="#tkinter-questions">Tkinter questions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="windows.html"
                          title="previous chapter">Python on Windows FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="installed.html"
                          title="next chapter">“Why is Python Installed on my Computer?” FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/gui.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="installed.html" title="“Why is Python Installed on my Computer?” FAQ"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="windows.html" title="Python on Windows FAQ"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Graphic User Interface FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="graphic-user-interface-faq">
<h1><a class="toc-backref" href="#id1" role="doc-backlink">Graphic User Interface FAQ</a><a class="headerlink" href="#graphic-user-interface-faq" title="Link to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#graphic-user-interface-faq" id="id1">Graphic User Interface FAQ</a></p>
<ul>
<li><p><a class="reference internal" href="#general-gui-questions" id="id2">General GUI Questions</a></p></li>
<li><p><a class="reference internal" href="#what-gui-toolkits-exist-for-python" id="id3">What GUI toolkits exist for Python?</a></p></li>
<li><p><a class="reference internal" href="#tkinter-questions" id="id4">Tkinter questions</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-freeze-tkinter-applications" id="id5">How do I freeze Tkinter applications?</a></p></li>
<li><p><a class="reference internal" href="#can-i-have-tk-events-handled-while-waiting-for-i-o" id="id6">Can I have Tk events handled while waiting for I/O?</a></p></li>
<li><p><a class="reference internal" href="#i-can-t-get-key-bindings-to-work-in-tkinter-why" id="id7">I can’t get key bindings to work in Tkinter: why?</a></p></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
<section id="general-gui-questions">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">General GUI Questions</a><a class="headerlink" href="#general-gui-questions" title="Link to this heading">¶</a></h2>
</section>
<section id="what-gui-toolkits-exist-for-python">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">What GUI toolkits exist for Python?</a><a class="headerlink" href="#what-gui-toolkits-exist-for-python" title="Link to this heading">¶</a></h2>
<p>Standard builds of Python include an object-oriented interface to the Tcl/Tk
widget set, called <a class="reference internal" href="../library/tk.html#tkinter"><span class="std std-ref">tkinter</span></a>.  This is probably the easiest to
install (since it comes included with most
<a class="reference external" href="https://www.python.org/downloads/">binary distributions</a> of Python) and use.
For more info about Tk, including pointers to the source, see the
<a class="reference external" href="https://www.tcl.tk">Tcl/Tk home page</a>.  Tcl/Tk is fully portable to the
macOS, Windows, and Unix platforms.</p>
<p>Depending on what platform(s) you are aiming at, there are also several
alternatives. A <a class="reference external" href="https://wiki.python.org/moin/GuiProgramming#Cross-Platform_Frameworks">list of cross-platform</a> and
<a class="reference external" href="https://wiki.python.org/moin/GuiProgramming#Platform-specific_Frameworks">platform-specific</a> GUI
frameworks can be found on the python wiki.</p>
</section>
<section id="tkinter-questions">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Tkinter questions</a><a class="headerlink" href="#tkinter-questions" title="Link to this heading">¶</a></h2>
<section id="how-do-i-freeze-tkinter-applications">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">How do I freeze Tkinter applications?</a><a class="headerlink" href="#how-do-i-freeze-tkinter-applications" title="Link to this heading">¶</a></h3>
<p>Freeze is a tool to create stand-alone applications.  When freezing Tkinter
applications, the applications will not be truly stand-alone, as the application
will still need the Tcl and Tk libraries.</p>
<p>One solution is to ship the application with the Tcl and Tk libraries, and point
to them at run-time using the <code class="xref std std-envvar docutils literal notranslate"><span class="pre">TCL_LIBRARY</span></code> and <code class="xref std std-envvar docutils literal notranslate"><span class="pre">TK_LIBRARY</span></code>
environment variables.</p>
<p>To get truly stand-alone applications, the Tcl scripts that form the library
have to be integrated into the application as well. One tool supporting that is
SAM (stand-alone modules), which is part of the Tix distribution
(<a class="reference external" href="https://tix.sourceforge.net/">https://tix.sourceforge.net/</a>).</p>
<p>Build Tix with SAM enabled, perform the appropriate call to
<code class="xref c c-func docutils literal notranslate"><span class="pre">Tclsam_init()</span></code>, etc. inside Python’s
<code class="file docutils literal notranslate"><span class="pre">Modules/tkappinit.c</span></code>, and link with libtclsam and libtksam (you
might include the Tix libraries as well).</p>
</section>
<section id="can-i-have-tk-events-handled-while-waiting-for-i-o">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Can I have Tk events handled while waiting for I/O?</a><a class="headerlink" href="#can-i-have-tk-events-handled-while-waiting-for-i-o" title="Link to this heading">¶</a></h3>
<p>On platforms other than Windows, yes, and you don’t even
need threads!  But you’ll have to restructure your I/O
code a bit.  Tk has the equivalent of Xt’s <code class="xref c c-func docutils literal notranslate"><span class="pre">XtAddInput()</span></code> call, which allows you
to register a callback function which will be called from the Tk mainloop when
I/O is possible on a file descriptor.  See <a class="reference internal" href="../library/tkinter.html#tkinter-file-handlers"><span class="std std-ref">File Handlers</span></a>.</p>
</section>
<section id="i-can-t-get-key-bindings-to-work-in-tkinter-why">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">I can’t get key bindings to work in Tkinter: why?</a><a class="headerlink" href="#i-can-t-get-key-bindings-to-work-in-tkinter-why" title="Link to this heading">¶</a></h3>
<p>An often-heard complaint is that event handlers <a class="reference internal" href="../library/tkinter.html#bindings-and-events"><span class="std std-ref">bound</span></a>
to events with the <code class="xref py py-meth docutils literal notranslate"><span class="pre">bind()</span></code> method
don’t get handled even when the appropriate key is pressed.</p>
<p>The most common cause is that the widget to which the binding applies doesn’t
have “keyboard focus”.  Check out the Tk documentation for the focus command.
Usually a widget is given the keyboard focus by clicking in it (but not for
labels; see the takefocus option).</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Graphic User Interface FAQ</a><ul>
<li><a class="reference internal" href="#general-gui-questions">General GUI Questions</a></li>
<li><a class="reference internal" href="#what-gui-toolkits-exist-for-python">What GUI toolkits exist for Python?</a></li>
<li><a class="reference internal" href="#tkinter-questions">Tkinter questions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="windows.html"
                          title="previous chapter">Python on Windows FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="installed.html"
                          title="next chapter">“Why is Python Installed on my Computer?” FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/gui.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="installed.html" title="“Why is Python Installed on my Computer?” FAQ"
             >next</a> |</li>
        <li class="right" >
          <a href="windows.html" title="Python on Windows FAQ"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Graphic User Interface FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>