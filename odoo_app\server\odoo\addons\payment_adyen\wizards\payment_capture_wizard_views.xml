<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_capture_wizard_view_form" model="ir.ui.view">
        <field name="name">payment.adyen.capture.wizard.form</field>
        <field name="model">payment.capture.wizard</field>
        <field name="inherit_id" ref="payment.payment_capture_wizard_view_form"/>
        <field name="arch" type="xml">
            <footer position="before">
                <field name="has_adyen_tx" invisible="1"/>
                <div id="alert_delayed_capture_tx"
                     role="alert"
                     class="alert alert-info mb-2"
                     invisible="not has_adyen_tx">
                    The capture or void of the transaction might take a few minutes to be
                    processed by <PERSON><PERSON><PERSON> and reflected in Odoo.
                </div>
            </footer>
        </field>
    </record>

</odoo>
