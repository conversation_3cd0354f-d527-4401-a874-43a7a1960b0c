<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xml.dom.minidom — Minimal DOM implementation" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xml.dom.minidom.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xml/dom/minidom.py xml.dom.minidom is a minimal implementation of the Document Object Model interface, with an API similar to that in other languages. It is intended to be simpler ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xml/dom/minidom.py xml.dom.minidom is a minimal implementation of the Document Object Model interface, with an API similar to that in other languages. It is intended to be simpler ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xml.dom.minidom — Minimal DOM implementation &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xml.dom.pulldom — Support for building partial DOM trees" href="xml.dom.pulldom.html" />
    <link rel="prev" title="xml.dom — The Document Object Model API" href="xml.dom.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xml.dom.minidom.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a><ul>
<li><a class="reference internal" href="#dom-objects">DOM Objects</a></li>
<li><a class="reference internal" href="#dom-example">DOM Example</a></li>
<li><a class="reference internal" href="#minidom-and-the-dom-standard">minidom and the DOM standard</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.dom.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.dom.pulldom.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.pulldom</span></code> — Support for building partial DOM trees</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.dom.minidom.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.dom.pulldom.html" title="xml.dom.pulldom — Support for building partial DOM trees"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xml.dom.html" title="xml.dom — The Document Object Model API"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml.dom.minidom">
<span id="xml-dom-minidom-minimal-dom-implementation"></span><h1><a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> — Minimal DOM implementation<a class="headerlink" href="#module-xml.dom.minidom" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xml/dom/minidom.py">Lib/xml/dom/minidom.py</a></p>
<hr class="docutils" />
<p><a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> is a minimal implementation of the Document Object
Model interface, with an API similar to that in other languages.  It is intended
to be simpler than the full DOM and also significantly smaller.  Users who are
not already proficient with the DOM should consider using the
<a class="reference internal" href="xml.etree.elementtree.html#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> module for their XML processing instead.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> module is not secure against
maliciously constructed data.  If you need to parse untrusted or
unauthenticated data see <a class="reference internal" href="xml.html#xml-vulnerabilities"><span class="std std-ref">XML vulnerabilities</span></a>.</p>
</div>
<p>DOM applications typically start by parsing some XML into a DOM.  With
<a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>, this is done through the parse functions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xml.dom.minidom</span> <span class="kn">import</span> <span class="n">parse</span><span class="p">,</span> <span class="n">parseString</span>

<span class="n">dom1</span> <span class="o">=</span> <span class="n">parse</span><span class="p">(</span><span class="s1">&#39;c:</span><span class="se">\\</span><span class="s1">temp</span><span class="se">\\</span><span class="s1">mydata.xml&#39;</span><span class="p">)</span>  <span class="c1"># parse an XML file by name</span>

<span class="n">datasource</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;c:</span><span class="se">\\</span><span class="s1">temp</span><span class="se">\\</span><span class="s1">mydata.xml&#39;</span><span class="p">)</span>
<span class="n">dom2</span> <span class="o">=</span> <span class="n">parse</span><span class="p">(</span><span class="n">datasource</span><span class="p">)</span>  <span class="c1"># parse an open file</span>

<span class="n">dom3</span> <span class="o">=</span> <span class="n">parseString</span><span class="p">(</span><span class="s1">&#39;&lt;myxml&gt;Some data&lt;empty/&gt; some more data&lt;/myxml&gt;&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#xml.dom.minidom.parse" title="xml.dom.minidom.parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse()</span></code></a> function can take either a filename or an open file object.</p>
<dl class="py function">
<dt class="sig sig-object py" id="xml.dom.minidom.parse">
<span class="sig-prename descclassname"><span class="pre">xml.dom.minidom.</span></span><span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename_or_file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bufsize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.minidom.parse" title="Link to this definition">¶</a></dt>
<dd><p>Return a <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> from the given input. <em>filename_or_file</em> may be
either a file name, or a file-like object. <em>parser</em>, if given, must be a SAX2
parser object. This function will change the document handler of the parser and
activate namespace support; other parser configuration (like setting an entity
resolver) must have been done in advance.</p>
</dd></dl>

<p>If you have XML in a string, you can use the <a class="reference internal" href="#xml.dom.minidom.parseString" title="xml.dom.minidom.parseString"><code class="xref py py-func docutils literal notranslate"><span class="pre">parseString()</span></code></a> function
instead:</p>
<dl class="py function">
<dt class="sig sig-object py" id="xml.dom.minidom.parseString">
<span class="sig-prename descclassname"><span class="pre">xml.dom.minidom.</span></span><span class="sig-name descname"><span class="pre">parseString</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.minidom.parseString" title="Link to this definition">¶</a></dt>
<dd><p>Return a <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> that represents the <em>string</em>. This method creates an
<a class="reference internal" href="io.html#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.StringIO</span></code></a> object for the string and passes that on to <a class="reference internal" href="#xml.dom.minidom.parse" title="xml.dom.minidom.parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse()</span></code></a>.</p>
</dd></dl>

<p>Both functions return a <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object representing the content of the
document.</p>
<p>What the <a class="reference internal" href="#xml.dom.minidom.parse" title="xml.dom.minidom.parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse()</span></code></a> and <a class="reference internal" href="#xml.dom.minidom.parseString" title="xml.dom.minidom.parseString"><code class="xref py py-func docutils literal notranslate"><span class="pre">parseString()</span></code></a> functions do is connect an XML
parser with a “DOM builder” that can accept parse events from any SAX parser and
convert them into a DOM tree.  The name of the functions are perhaps misleading,
but are easy to grasp when learning the interfaces.  The parsing of the document
will be completed before these functions return; it’s simply that these
functions do not provide a parser implementation themselves.</p>
<p>You can also create a <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> by calling a method on a “DOM
Implementation” object.  You can get this object either by calling the
<code class="xref py py-func docutils literal notranslate"><span class="pre">getDOMImplementation()</span></code> function in the <a class="reference internal" href="xml.dom.html#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a> package or the
<a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> module.  Once you have a <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code>, you
can add child nodes to it to populate the DOM:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xml.dom.minidom</span> <span class="kn">import</span> <span class="n">getDOMImplementation</span>

<span class="n">impl</span> <span class="o">=</span> <span class="n">getDOMImplementation</span><span class="p">()</span>

<span class="n">newdoc</span> <span class="o">=</span> <span class="n">impl</span><span class="o">.</span><span class="n">createDocument</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="s2">&quot;some_tag&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
<span class="n">top_element</span> <span class="o">=</span> <span class="n">newdoc</span><span class="o">.</span><span class="n">documentElement</span>
<span class="n">text</span> <span class="o">=</span> <span class="n">newdoc</span><span class="o">.</span><span class="n">createTextNode</span><span class="p">(</span><span class="s1">&#39;Some textual content.&#39;</span><span class="p">)</span>
<span class="n">top_element</span><span class="o">.</span><span class="n">appendChild</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
</pre></div>
</div>
<p>Once you have a DOM document object, you can access the parts of your XML
document through its properties and methods.  These properties are defined in
the DOM specification.  The main property of the document object is the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">documentElement</span></code> property.  It gives you the main element in the XML
document: the one that holds all others.  Here is an example program:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">dom3</span> <span class="o">=</span> <span class="n">parseString</span><span class="p">(</span><span class="s2">&quot;&lt;myxml&gt;Some data&lt;/myxml&gt;&quot;</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">dom3</span><span class="o">.</span><span class="n">documentElement</span><span class="o">.</span><span class="n">tagName</span> <span class="o">==</span> <span class="s2">&quot;myxml&quot;</span>
</pre></div>
</div>
<p>When you are finished with a DOM tree, you may optionally call the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code> method to encourage early cleanup of the now-unneeded
objects.  <code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code> is an <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>-specific
extension to the DOM API that renders the node and its descendants
essentially useless.  Otherwise, Python’s garbage collector will
eventually take care of the objects in the tree.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://www.w3.org/TR/REC-DOM-Level-1/">Document Object Model (DOM) Level 1 Specification</a></dt><dd><p>The W3C recommendation for the DOM supported by <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>.</p>
</dd>
</dl>
</div>
<section id="dom-objects">
<span id="minidom-objects"></span><h2>DOM Objects<a class="headerlink" href="#dom-objects" title="Link to this heading">¶</a></h2>
<p>The definition of the DOM API for Python is given as part of the <a class="reference internal" href="xml.dom.html#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a>
module documentation.  This section lists the differences between the API and
<a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.minidom.Node.unlink">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">unlink</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.minidom.Node.unlink" title="Link to this definition">¶</a></dt>
<dd><p>Break internal references within the DOM so that it will be garbage collected on
versions of Python without cyclic GC.  Even when cyclic GC is available, using
this can make large amounts of memory available sooner, so calling this on DOM
objects as soon as they are no longer needed is good practice.  This only needs
to be called on the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object, but may be called on child nodes
to discard children of that node.</p>
<p>You can avoid calling this method explicitly by using the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a>
statement. The following code will automatically unlink <em>dom</em> when the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> block is exited:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">xml</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">minidom</span><span class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="n">datasource</span><span class="p">)</span> <span class="k">as</span> <span class="n">dom</span><span class="p">:</span>
    <span class="o">...</span> <span class="c1"># Work with dom.</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.minidom.Node.writexml">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">writexml</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">writer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">addindent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newl</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">standalone</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.minidom.Node.writexml" title="Link to this definition">¶</a></dt>
<dd><p>Write XML to the writer object.  The writer receives texts but not bytes as input,
it should have a <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> method which matches that of the file object
interface.  The <em>indent</em> parameter is the indentation of the current node.
The <em>addindent</em> parameter is the incremental indentation to use for subnodes
of the current one.  The <em>newl</em> parameter specifies the string to use to
terminate newlines.</p>
<p>For the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> node, an additional keyword argument <em>encoding</em> can
be used to specify the encoding field of the XML header.</p>
<p>Similarly, explicitly stating the <em>standalone</em> argument causes the
standalone document declarations to be added to the prologue of the XML
document.
If the value is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, <code class="docutils literal notranslate"><span class="pre">standalone=&quot;yes&quot;</span></code> is added,
otherwise it is set to <code class="docutils literal notranslate"><span class="pre">&quot;no&quot;</span></code>.
Not stating the argument will omit the declaration from the document.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.dom.minidom.Node.writexml" title="xml.dom.minidom.Node.writexml"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writexml()</span></code></a> method now preserves the attribute order specified
by the user.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>standalone</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.minidom.Node.toxml">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">toxml</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">standalone</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.minidom.Node.toxml" title="Link to this definition">¶</a></dt>
<dd><p>Return a string or byte string containing the XML represented by
the DOM node.</p>
<p>With an explicit <em>encoding</em> <a class="footnote-reference brackets" href="#id3" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> argument, the result is a byte
string in the specified encoding.
With no <em>encoding</em> argument, the result is a Unicode string, and the
XML declaration in the resulting string does not specify an
encoding. Encoding this string in an encoding other than UTF-8 is
likely incorrect, since UTF-8 is the default encoding of XML.</p>
<p>The <em>standalone</em> argument behaves exactly as in <a class="reference internal" href="#xml.dom.minidom.Node.writexml" title="xml.dom.minidom.Node.writexml"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writexml()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.dom.minidom.Node.toxml" title="xml.dom.minidom.Node.toxml"><code class="xref py py-meth docutils literal notranslate"><span class="pre">toxml()</span></code></a> method now preserves the attribute order specified
by the user.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>standalone</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.minidom.Node.toprettyxml">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">toprettyxml</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'\t'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newl</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'\n'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">standalone</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.minidom.Node.toprettyxml" title="Link to this definition">¶</a></dt>
<dd><p>Return a pretty-printed version of the document. <em>indent</em> specifies the
indentation string and defaults to a tabulator; <em>newl</em> specifies the string
emitted at the end of each line and defaults to <code class="docutils literal notranslate"><span class="pre">\n</span></code>.</p>
<p>The <em>encoding</em> argument behaves like the corresponding argument of
<a class="reference internal" href="#xml.dom.minidom.Node.toxml" title="xml.dom.minidom.Node.toxml"><code class="xref py py-meth docutils literal notranslate"><span class="pre">toxml()</span></code></a>.</p>
<p>The <em>standalone</em> argument behaves exactly as in <a class="reference internal" href="#xml.dom.minidom.Node.writexml" title="xml.dom.minidom.Node.writexml"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writexml()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.dom.minidom.Node.toprettyxml" title="xml.dom.minidom.Node.toprettyxml"><code class="xref py py-meth docutils literal notranslate"><span class="pre">toprettyxml()</span></code></a> method now preserves the attribute order specified
by the user.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>standalone</em> parameter was added.</p>
</div>
</dd></dl>

</section>
<section id="dom-example">
<span id="id2"></span><h2>DOM Example<a class="headerlink" href="#dom-example" title="Link to this heading">¶</a></h2>
<p>This example program is a fairly realistic example of a simple program. In this
particular case, we do not take much advantage of the flexibility of the DOM.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xml.dom.minidom</span>

<span class="n">document</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;</span><span class="se">\</span>
<span class="s2">&lt;slideshow&gt;</span>
<span class="s2">&lt;title&gt;Demo slideshow&lt;/title&gt;</span>
<span class="s2">&lt;slide&gt;&lt;title&gt;Slide title&lt;/title&gt;</span>
<span class="s2">&lt;point&gt;This is a demo&lt;/point&gt;</span>
<span class="s2">&lt;point&gt;Of a program for processing slides&lt;/point&gt;</span>
<span class="s2">&lt;/slide&gt;</span>

<span class="s2">&lt;slide&gt;&lt;title&gt;Another demo slide&lt;/title&gt;</span>
<span class="s2">&lt;point&gt;It is important&lt;/point&gt;</span>
<span class="s2">&lt;point&gt;To have more than&lt;/point&gt;</span>
<span class="s2">&lt;point&gt;one slide&lt;/point&gt;</span>
<span class="s2">&lt;/slide&gt;</span>
<span class="s2">&lt;/slideshow&gt;</span>
<span class="s2">&quot;&quot;&quot;</span>

<span class="n">dom</span> <span class="o">=</span> <span class="n">xml</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">minidom</span><span class="o">.</span><span class="n">parseString</span><span class="p">(</span><span class="n">document</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">getText</span><span class="p">(</span><span class="n">nodelist</span><span class="p">):</span>
    <span class="n">rc</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">nodelist</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">node</span><span class="o">.</span><span class="n">nodeType</span> <span class="o">==</span> <span class="n">node</span><span class="o">.</span><span class="n">TEXT_NODE</span><span class="p">:</span>
            <span class="n">rc</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">node</span><span class="o">.</span><span class="n">data</span><span class="p">)</span>
    <span class="k">return</span> <span class="s1">&#39;&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">rc</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handleSlideshow</span><span class="p">(</span><span class="n">slideshow</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;&lt;html&gt;&quot;</span><span class="p">)</span>
    <span class="n">handleSlideshowTitle</span><span class="p">(</span><span class="n">slideshow</span><span class="o">.</span><span class="n">getElementsByTagName</span><span class="p">(</span><span class="s2">&quot;title&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
    <span class="n">slides</span> <span class="o">=</span> <span class="n">slideshow</span><span class="o">.</span><span class="n">getElementsByTagName</span><span class="p">(</span><span class="s2">&quot;slide&quot;</span><span class="p">)</span>
    <span class="n">handleToc</span><span class="p">(</span><span class="n">slides</span><span class="p">)</span>
    <span class="n">handleSlides</span><span class="p">(</span><span class="n">slides</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;&lt;/html&gt;&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handleSlides</span><span class="p">(</span><span class="n">slides</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">slide</span> <span class="ow">in</span> <span class="n">slides</span><span class="p">:</span>
        <span class="n">handleSlide</span><span class="p">(</span><span class="n">slide</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handleSlide</span><span class="p">(</span><span class="n">slide</span><span class="p">):</span>
    <span class="n">handleSlideTitle</span><span class="p">(</span><span class="n">slide</span><span class="o">.</span><span class="n">getElementsByTagName</span><span class="p">(</span><span class="s2">&quot;title&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
    <span class="n">handlePoints</span><span class="p">(</span><span class="n">slide</span><span class="o">.</span><span class="n">getElementsByTagName</span><span class="p">(</span><span class="s2">&quot;point&quot;</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">handleSlideshowTitle</span><span class="p">(</span><span class="n">title</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;&lt;title&gt;</span><span class="si">{</span><span class="n">getText</span><span class="p">(</span><span class="n">title</span><span class="o">.</span><span class="n">childNodes</span><span class="p">)</span><span class="si">}</span><span class="s2">&lt;/title&gt;&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handleSlideTitle</span><span class="p">(</span><span class="n">title</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;&lt;h2&gt;</span><span class="si">{</span><span class="n">getText</span><span class="p">(</span><span class="n">title</span><span class="o">.</span><span class="n">childNodes</span><span class="p">)</span><span class="si">}</span><span class="s2">&lt;/h2&gt;&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handlePoints</span><span class="p">(</span><span class="n">points</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;&lt;ul&gt;&quot;</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">point</span> <span class="ow">in</span> <span class="n">points</span><span class="p">:</span>
        <span class="n">handlePoint</span><span class="p">(</span><span class="n">point</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;&lt;/ul&gt;&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handlePoint</span><span class="p">(</span><span class="n">point</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;&lt;li&gt;</span><span class="si">{</span><span class="n">getText</span><span class="p">(</span><span class="n">point</span><span class="o">.</span><span class="n">childNodes</span><span class="p">)</span><span class="si">}</span><span class="s2">&lt;/li&gt;&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">handleToc</span><span class="p">(</span><span class="n">slides</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">slide</span> <span class="ow">in</span> <span class="n">slides</span><span class="p">:</span>
        <span class="n">title</span> <span class="o">=</span> <span class="n">slide</span><span class="o">.</span><span class="n">getElementsByTagName</span><span class="p">(</span><span class="s2">&quot;title&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;&lt;p&gt;</span><span class="si">{</span><span class="n">getText</span><span class="p">(</span><span class="n">title</span><span class="o">.</span><span class="n">childNodes</span><span class="p">)</span><span class="si">}</span><span class="s2">&lt;/p&gt;&quot;</span><span class="p">)</span>

<span class="n">handleSlideshow</span><span class="p">(</span><span class="n">dom</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="minidom-and-the-dom-standard">
<span id="minidom-and-dom"></span><h2>minidom and the DOM standard<a class="headerlink" href="#minidom-and-the-dom-standard" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> module is essentially a DOM 1.0-compatible DOM with
some DOM 2 features (primarily namespace features).</p>
<p>Usage of the DOM interface in Python is straight-forward.  The following mapping
rules apply:</p>
<ul class="simple">
<li><p>Interfaces are accessed through instance objects. Applications should not
instantiate the classes themselves; they should use the creator functions
available on the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object. Derived interfaces support all
operations (and attributes) from the base interfaces, plus any new operations.</p></li>
<li><p>Operations are used as methods. Since the DOM uses only <a class="reference internal" href="../reference/expressions.html#in"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">in</span></code></a>
parameters, the arguments are passed in normal order (from left to right).
There are no optional arguments. <code class="docutils literal notranslate"><span class="pre">void</span></code> operations return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></li>
<li><p>IDL attributes map to instance attributes. For compatibility with the OMG IDL
language mapping for Python, an attribute <code class="docutils literal notranslate"><span class="pre">foo</span></code> can also be accessed through
accessor methods <code class="xref py py-meth docutils literal notranslate"><span class="pre">_get_foo()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">_set_foo()</span></code>.  <code class="docutils literal notranslate"><span class="pre">readonly</span></code>
attributes must not be changed; this is not enforced at runtime.</p></li>
<li><p>The types <code class="docutils literal notranslate"><span class="pre">short</span> <span class="pre">int</span></code>, <code class="docutils literal notranslate"><span class="pre">unsigned</span> <span class="pre">int</span></code>, <code class="docutils literal notranslate"><span class="pre">unsigned</span> <span class="pre">long</span> <span class="pre">long</span></code>, and
<code class="docutils literal notranslate"><span class="pre">boolean</span></code> all map to Python integer objects.</p></li>
<li><p>The type <code class="docutils literal notranslate"><span class="pre">DOMString</span></code> maps to Python strings. <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> supports
either bytes or strings, but will normally produce strings.
Values of type <code class="docutils literal notranslate"><span class="pre">DOMString</span></code> may also be <code class="docutils literal notranslate"><span class="pre">None</span></code> where allowed to have the IDL
<code class="docutils literal notranslate"><span class="pre">null</span></code> value by the DOM specification from the W3C.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">const</span></code> declarations map to variables in their respective scope (e.g.
<code class="docutils literal notranslate"><span class="pre">xml.dom.minidom.Node.PROCESSING_INSTRUCTION_NODE</span></code>); they must not be changed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOMException</span></code> is currently not supported in <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>.
Instead, <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a> uses standard Python exceptions such as
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> and <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a>.</p></li>
<li><p><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code> objects are implemented using Python’s built-in list type.
These objects provide the interface defined in the DOM specification, but with
earlier versions of Python they do not support the official API.  They are,
however, much more “Pythonic” than the interface defined in the W3C
recommendations.</p></li>
</ul>
<p>The following interfaces have no implementation in <a class="reference internal" href="#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>:</p>
<ul class="simple">
<li><p><code class="xref py py-class docutils literal notranslate"><span class="pre">DOMTimeStamp</span></code></p></li>
<li><p><code class="xref py py-class docutils literal notranslate"><span class="pre">EntityReference</span></code></p></li>
</ul>
<p>Most of these reflect information in the XML document that is not of general
utility to most DOM users.</p>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id3" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>The encoding name included in the XML output should conform to
the appropriate standards. For example, “UTF-8” is valid, but
“UTF8” is not valid in an XML document’s declaration, even though
Python accepts it as an encoding name.
See <a class="reference external" href="https://www.w3.org/TR/2006/REC-xml11-20060816/#NT-EncodingDecl">https://www.w3.org/TR/2006/REC-xml11-20060816/#NT-EncodingDecl</a>
and <a class="reference external" href="https://www.iana.org/assignments/character-sets/character-sets.xhtml">https://www.iana.org/assignments/character-sets/character-sets.xhtml</a>.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a><ul>
<li><a class="reference internal" href="#dom-objects">DOM Objects</a></li>
<li><a class="reference internal" href="#dom-example">DOM Example</a></li>
<li><a class="reference internal" href="#minidom-and-the-dom-standard">minidom and the DOM standard</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.dom.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.dom.pulldom.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.pulldom</span></code> — Support for building partial DOM trees</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.dom.minidom.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.dom.pulldom.html" title="xml.dom.pulldom — Support for building partial DOM trees"
             >next</a> |</li>
        <li class="right" >
          <a href="xml.dom.html" title="xml.dom — The Document Object Model API"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>