<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- This group is only allowed to deal with equipment registration and maintenance -->
    <record id="group_equipment_manager" model="res.groups">
        <field name="name">Equipment Manager</field>
        <field name="category_id" ref="base.module_category_manufacturing_maintenance"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        <field name="comment">The user will be able to manage equipment.</field>
    </record>

    <data noupdate="1">

    <!-- Rules -->
    <record id="equipment_request_rule_user" model="ir.rule">
        <field name="name">Users are allowed to access their own maintenance requests</field>
        <field name="model_id" ref="model_maintenance_request"/>
        <field name="domain_force">['|', '|', ('owner_user_id', '=', user.id), ('message_partner_ids', 'in', [user.partner_id.id]), ('user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="equipment_rule_user" model="ir.rule">
        <field name="name">Users are allowed to access equipment they follow</field>
        <field name="model_id" ref="model_maintenance_equipment"/>
        <field name="domain_force">[('message_partner_ids', 'in', [user.partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="equipment_request_rule_admin_user" model="ir.rule">
        <field name="name">Administrator of maintenance requests</field>
        <field name="model_id" ref="model_maintenance_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_equipment_manager'))]"/>
    </record>

    <record id="equipment_rule_admin_user" model="ir.rule">
        <field name="name">Equipment administrator</field>
        <field name="model_id" ref="model_maintenance_equipment"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_equipment_manager'))]"/>
    </record>

    <record id="maintenance_request_comp_rule" model="ir.rule">
        <field name="name">Maintenance Request Multi-company rule</field>
        <field name="model_id" ref="model_maintenance_request"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="maintenance_equipment_comp_rule" model="ir.rule">
        <field name="name">Maintenance Equipment Multi-company rule</field>
        <field name="model_id" ref="model_maintenance_equipment"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="maintenance_team_comp_rule" model="ir.rule">
        <field name="name">Maintenance Team Multi-company rule</field>
        <field name="model_id" ref="model_maintenance_team"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="maintenance_equipment_category_comp_rule" model="ir.rule">
        <field name="name">Maintenance Equipment Category Multi-company rule</field>
        <field name="model_id" ref="model_maintenance_equipment_category"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    </data>

</odoo>
