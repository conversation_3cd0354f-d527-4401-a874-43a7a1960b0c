# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_fleet
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# W<PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_form_inherit_hr
msgid ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Employee</span>"
msgstr ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Werknemer</span>"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Sjabloon activiteitenplan"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Toewijzing"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_view_list
msgid "Attachments"
msgstr "Bijlagen"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
#, python-format
msgid "Cannot remove address from employees with linked cars."
msgstr "Kan adres niet verwijderen van werknemers met gekoppelde auto's."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__employee_cars_count
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__employee_cars_count
msgid "Cars"
msgstr "Auto's"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.hr_departure_wizard_view_form
msgid "Company Car"
msgstr "Bedrijfsauto"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
msgid "Current Driver"
msgstr "Huidige bestuurder"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Vertrek wizard"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "Driver"
msgstr "Bestuurder"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_contract__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_services__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_odometer__driver_employee_id
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_services_view_form_inherit_hr
msgid "Driver (Employee)"
msgstr "Chauffeur (Werknemer)"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Bestuurder zijn rijdgeschiedenis met voertuig"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_search_inherit_hr
msgid "Employee"
msgstr "Werknemer"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
#, python-format
msgid "Employee %s is not linked to a vehicle."
msgstr "Werknemer %s is niet gekoppeld aan een voertuig."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_name
msgid "Employee Name"
msgstr "Naam werknemer"

#. module: hr_fleet
#: model:ir.model.fields.selection,name:hr_fleet.selection__mail_activity_plan_template__responsible_type__fleet_manager
msgid "Fleet Manager"
msgstr "Wagenparkbeheerder"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
#, python-format
msgid "Fleet Manager is limited to Employee plans."
msgstr "Wagenparkbeheerder is beperkt tot plannen van werknemer."

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Fleet Mobility Card"
msgstr "Wagenpark mobiliteitskaart"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__future_driver_employee_id
msgid "Future Driver (Employee)"
msgstr "Toekomstige chauffeur (Werknemer)"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
#, python-format
msgid "History Employee Cars"
msgstr "Medewerker autogeschiedenis"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__license_plate
msgid "License Plate"
msgstr "Kentekenplaat"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__mobility_card
msgid "Mobility Card"
msgstr "Mobiliteitskaart"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__attachment_number
msgid "Number of Attachments"
msgstr "Aantal bijlagen"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Kilometertellerlog van een voertuig"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee_public
msgid "Public Employee"
msgstr "Openbare werknemer"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Related Employee"
msgstr "Verwante werknemer"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release Company Car"
msgstr "Bedrijfsauto vrijgeven"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Diensten voor voertuigen"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
#, python-format
msgid "The vehicle of employee %(employee)s is not linked to a fleet manager."
msgstr ""
"Het voertuig van de werknemer %(employee)s is niet gekoppeld aan een "
"wagenparkbeheerder."

#. module: hr_fleet
#. odoo-javascript
#: code:addons/hr_fleet/static/src/views/hr_fleet_kanban/hr_fleet_kanban_controller.xml:0
#, python-format
msgid "Upload"
msgstr "Upload"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_res_users
msgid "User"
msgstr "Gebruiker"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr "Voertuig"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Voertuigcontract"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__car_ids
msgid "Vehicles (private)"
msgstr "Voertuigen (privé)"
