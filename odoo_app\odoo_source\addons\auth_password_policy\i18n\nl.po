# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr "Minimale wachtwoordlengte"

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr ""
"Minimaal aantal tekens dat wachtwoorden moeten bevatten, stel in op 0 om uit"
" te schakelen."

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_field.js:0
#, python-format
msgid "Password"
msgstr "Wachtwoord"

#. module: auth_password_policy
#. odoo-python
#: code:addons/auth_password_policy/models/res_users.py:0
#, python-format
msgid "Passwords must have at least %d characters, got %d."
msgstr "Wachtwoord moet tenminste %d karakters bevatten, bevat %d."

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_meter.js:0
#, python-format
msgid ""
"Required: %s\n"
"\n"
"Hint: to increase password strength, increase length, use multiple words, and use non-letter characters."
msgstr ""
"Vereist: %s\n"
"\n"
"Hint: om de sterkte van het wachtwoord te vergroten, gebruik meer karakters, leestekens en symbolen."

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "User"
msgstr "Gebruiker"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
#, python-format
msgid "at least %s character classes"
msgstr "tenminste %s karakter klassen"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
#, python-format
msgid "at least %s characters"
msgstr "tenminste %s karakters"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
#, python-format
msgid "at least %s words"
msgstr "tenminste %s woorden"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_meter.js:0
#, python-format
msgid "no requirements"
msgstr "geen verplichtingen"
