<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="ensurepip — Bootstrapping the pip installer" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/ensurepip.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/ensurepip The ensurepip package provides support for bootstrapping the pip installer into an existing Python installation or virtual environment. This bootstrapping approach reflec..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/ensurepip The ensurepip package provides support for bootstrapping the pip installer into an existing Python installation or virtual environment. This bootstrapping approach reflec..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>ensurepip — Bootstrapping the pip installer &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="venv — Creation of virtual environments" href="venv.html" />
    <link rel="prev" title="Software Packaging and Distribution" href="distribution.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/ensurepip.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer</a><ul>
<li><a class="reference internal" href="#command-line-interface">Command line interface</a></li>
<li><a class="reference internal" href="#module-api">Module API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="distribution.html"
                          title="previous chapter">Software Packaging and Distribution</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="venv.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> — Creation of virtual environments</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ensurepip.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="venv.html" title="venv — Creation of virtual environments"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="distribution.html" title="Software Packaging and Distribution"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="distribution.html" accesskey="U">Software Packaging and Distribution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-ensurepip">
<span id="ensurepip-bootstrapping-the-pip-installer"></span><h1><a class="reference internal" href="#module-ensurepip" title="ensurepip: Bootstrapping the &quot;pip&quot; installer into an existing Python installation or virtual environment."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code></a> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer<a class="headerlink" href="#module-ensurepip" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/ensurepip">Lib/ensurepip</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-ensurepip" title="ensurepip: Bootstrapping the &quot;pip&quot; installer into an existing Python installation or virtual environment."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code></a> package provides support for bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code>
installer into an existing Python installation or virtual environment. This
bootstrapping approach reflects the fact that <code class="docutils literal notranslate"><span class="pre">pip</span></code> is an independent
project with its own release cycle, and the latest available stable version
is bundled with maintenance and feature releases of the CPython reference
interpreter.</p>
<p>In most cases, end users of Python shouldn’t need to invoke this module
directly (as <code class="docutils literal notranslate"><span class="pre">pip</span></code> should be bootstrapped by default), but it may be
needed if installing <code class="docutils literal notranslate"><span class="pre">pip</span></code> was skipped when installing Python (or
when creating a virtual environment) or after explicitly uninstalling
<code class="docutils literal notranslate"><span class="pre">pip</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This module <em>does not</em> access the internet. All of the components
needed to bootstrap <code class="docutils literal notranslate"><span class="pre">pip</span></code> are included as internal parts of the
package.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="../installing/index.html#installing-index"><span class="std std-ref">Installing Python Modules</span></a></dt><dd><p>The end user guide for installing Python packages</p>
</dd>
<dt><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0453/"><strong>PEP 453</strong></a>: Explicit bootstrapping of pip in Python installations</dt><dd><p>The original rationale and specification for this module.</p>
</dd>
</dl>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<section id="command-line-interface">
<h2>Command line interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<p>The command line interface is invoked using the interpreter’s <code class="docutils literal notranslate"><span class="pre">-m</span></code> switch.</p>
<p>The simplest possible invocation is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">ensurepip</span>
</pre></div>
</div>
<p>This invocation will install <code class="docutils literal notranslate"><span class="pre">pip</span></code> if it is not already installed,
but otherwise does nothing. To ensure the installed version of <code class="docutils literal notranslate"><span class="pre">pip</span></code>
is at least as recent as the one available in <code class="docutils literal notranslate"><span class="pre">ensurepip</span></code>, pass the
<code class="docutils literal notranslate"><span class="pre">--upgrade</span></code> option:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">ensurepip</span> <span class="o">--</span><span class="n">upgrade</span>
</pre></div>
</div>
<p>By default, <code class="docutils literal notranslate"><span class="pre">pip</span></code> is installed into the current virtual environment
(if one is active) or into the system site packages (if there is no
active virtual environment). The installation location can be controlled
through two additional command line options:</p>
<ul class="simple">
<li><p><code class="samp docutils literal notranslate"><span class="pre">--root</span> <em><span class="pre">dir</span></em></code>: Installs <code class="docutils literal notranslate"><span class="pre">pip</span></code> relative to the given root directory
rather than the root of the currently active virtual environment (if any)
or the default root for the current Python installation.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--user</span></code>: Installs <code class="docutils literal notranslate"><span class="pre">pip</span></code> into the user site packages directory rather
than globally for the current Python installation (this option is not
permitted inside an active virtual environment).</p></li>
</ul>
<p>By default, the scripts <code class="docutils literal notranslate"><span class="pre">pipX</span></code> and <code class="docutils literal notranslate"><span class="pre">pipX.Y</span></code> will be installed (where
X.Y stands for the version of Python used to invoke <code class="docutils literal notranslate"><span class="pre">ensurepip</span></code>). The
scripts installed can be controlled through two additional command line
options:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">--altinstall</span></code>: if an alternate installation is requested, the <code class="docutils literal notranslate"><span class="pre">pipX</span></code>
script will <em>not</em> be installed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--default-pip</span></code>: if a “default pip” installation is requested, the
<code class="docutils literal notranslate"><span class="pre">pip</span></code> script will be installed in addition to the two regular scripts.</p></li>
</ul>
<p>Providing both of the script selection options will trigger an exception.</p>
</section>
<section id="module-api">
<h2>Module API<a class="headerlink" href="#module-api" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#module-ensurepip" title="ensurepip: Bootstrapping the &quot;pip&quot; installer into an existing Python installation or virtual environment."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code></a> exposes two functions for programmatic use:</p>
<dl class="py function">
<dt class="sig sig-object py" id="ensurepip.version">
<span class="sig-prename descclassname"><span class="pre">ensurepip.</span></span><span class="sig-name descname"><span class="pre">version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ensurepip.version" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string specifying the available version of pip that will be
installed when bootstrapping an environment.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ensurepip.bootstrap">
<span class="sig-prename descclassname"><span class="pre">ensurepip.</span></span><span class="sig-name descname"><span class="pre">bootstrap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">upgrade</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">altinstall</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_pip</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbosity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ensurepip.bootstrap" title="Link to this definition">¶</a></dt>
<dd><p>Bootstraps <code class="docutils literal notranslate"><span class="pre">pip</span></code> into the current or designated environment.</p>
<p><em>root</em> specifies an alternative root directory to install relative to.
If <em>root</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, then installation uses the default install location
for the current environment.</p>
<p><em>upgrade</em> indicates whether or not to upgrade an existing installation
of an earlier version of <code class="docutils literal notranslate"><span class="pre">pip</span></code> to the available version.</p>
<p><em>user</em> indicates whether to use the user scheme rather than installing
globally.</p>
<p>By default, the scripts <code class="docutils literal notranslate"><span class="pre">pipX</span></code> and <code class="docutils literal notranslate"><span class="pre">pipX.Y</span></code> will be installed (where
X.Y stands for the current version of Python).</p>
<p>If <em>altinstall</em> is set, then <code class="docutils literal notranslate"><span class="pre">pipX</span></code> will <em>not</em> be installed.</p>
<p>If <em>default_pip</em> is set, then <code class="docutils literal notranslate"><span class="pre">pip</span></code> will be installed in addition to
the two regular scripts.</p>
<p>Setting both <em>altinstall</em> and <em>default_pip</em> will trigger
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p><em>verbosity</em> controls the level of output to <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a> from the
bootstrapping operation.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">ensurepip.bootstrap</span></code> with argument <code class="docutils literal notranslate"><span class="pre">root</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The bootstrapping process has side effects on both <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> and
<code class="docutils literal notranslate"><span class="pre">os.environ</span></code>. Invoking the command line interface in a subprocess
instead allows these side effects to be avoided.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The bootstrapping process may install additional modules required by
<code class="docutils literal notranslate"><span class="pre">pip</span></code>, but other software should not assume those dependencies will
always be present by default (as the dependencies may be removed in a
future version of <code class="docutils literal notranslate"><span class="pre">pip</span></code>).</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer</a><ul>
<li><a class="reference internal" href="#command-line-interface">Command line interface</a></li>
<li><a class="reference internal" href="#module-api">Module API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="distribution.html"
                          title="previous chapter">Software Packaging and Distribution</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="venv.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> — Creation of virtual environments</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ensurepip.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="venv.html" title="venv — Creation of virtual environments"
             >next</a> |</li>
        <li class="right" >
          <a href="distribution.html" title="Software Packaging and Distribution"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="distribution.html" >Software Packaging and Distribution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>