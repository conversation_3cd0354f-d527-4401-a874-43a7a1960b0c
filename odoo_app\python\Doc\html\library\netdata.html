<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Internet Data Handling" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/netdata.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This chapter describes modules which support handling data formats commonly used on the internet. email — An email and MIME handling package- email.message: Representing an email message, email.par..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This chapter describes modules which support handling data formats commonly used on the internet. email — An email and MIME handling package- email.message: Representing an email message, email.par..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Internet Data Handling &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="email — An email and MIME handling package" href="email.html" />
    <link rel="prev" title="mmap — Memory-mapped file support" href="mmap.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/netdata.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="mmap.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code> — Memory-mapped file support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/netdata.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.html" title="email — An email and MIME handling package"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="mmap.html" title="mmap — Memory-mapped file support"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Internet Data Handling</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="internet-data-handling">
<span id="netdata"></span><h1>Internet Data Handling<a class="headerlink" href="#internet-data-handling" title="Link to this heading">¶</a></h1>
<p>This chapter describes modules which support handling data formats commonly used
on the internet.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="email.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="email.message.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.message</span></code>: Representing an email message</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.parser.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code>: Parsing email messages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="email.parser.html#feedparser-api">FeedParser API</a></li>
<li class="toctree-l3"><a class="reference internal" href="email.parser.html#parser-api">Parser API</a></li>
<li class="toctree-l3"><a class="reference internal" href="email.parser.html#additional-notes">Additional notes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="email.generator.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.generator</span></code>: Generating MIME documents</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.policy.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.policy</span></code>: Policy Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.errors.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.errors</span></code>: Exception and Defect classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.headerregistry.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.headerregistry</span></code>: Custom Header Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.contentmanager.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.contentmanager</span></code>: Managing MIME Content</a><ul>
<li class="toctree-l3"><a class="reference internal" href="email.contentmanager.html#content-manager-instances">Content Manager Instances</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="email.examples.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code>: Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.compat32-message.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.message.Message</span></code>: Representing an email message using the <code class="xref py py-data docutils literal notranslate"><span class="pre">compat32</span></code> API</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.mime.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.mime</span></code>: Creating email and MIME objects from scratch</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.header.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.header</span></code>: Internationalized headers</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.charset.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.charset</span></code>: Representing character sets</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.encoders.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.encoders</span></code>: Encoders</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.utils.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.utils</span></code>: Miscellaneous utilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.iterators.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.iterators</span></code>: Iterators</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="json.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a><ul>
<li class="toctree-l2"><a class="reference internal" href="json.html#basic-usage">Basic Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="json.html#encoders-and-decoders">Encoders and Decoders</a></li>
<li class="toctree-l2"><a class="reference internal" href="json.html#exceptions">Exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="json.html#standard-compliance-and-interoperability">Standard Compliance and Interoperability</a><ul>
<li class="toctree-l3"><a class="reference internal" href="json.html#character-encodings">Character Encodings</a></li>
<li class="toctree-l3"><a class="reference internal" href="json.html#infinite-and-nan-number-values">Infinite and NaN Number Values</a></li>
<li class="toctree-l3"><a class="reference internal" href="json.html#repeated-names-within-an-object">Repeated Names Within an Object</a></li>
<li class="toctree-l3"><a class="reference internal" href="json.html#top-level-non-object-non-array-values">Top-level Non-Object, Non-Array Values</a></li>
<li class="toctree-l3"><a class="reference internal" href="json.html#implementation-limitations">Implementation Limitations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="json.html#module-json.tool">Command Line Interface</a><ul>
<li class="toctree-l3"><a class="reference internal" href="json.html#command-line-options">Command line options</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="mailbox.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a><ul>
<li class="toctree-l2"><a class="reference internal" href="mailbox.html#mailbox-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Mailbox</span></code> objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#maildir-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Maildir</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#mbox-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">mbox</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#mh-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MH</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#babyl-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Babyl</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#mmdf-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDF</span></code> objects</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="mailbox.html#message-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code> objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#maildirmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MaildirMessage</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#mboxmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">mboxMessage</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#mhmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MHMessage</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#babylmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">BabylMessage</span></code> objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="mailbox.html#mmdfmessage-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">MMDFMessage</span></code> objects</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="mailbox.html#exceptions">Exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="mailbox.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="mimetypes.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a><ul>
<li class="toctree-l2"><a class="reference internal" href="mimetypes.html#mimetypes-objects">MimeTypes Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="base64.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">base64</span></code> — Base16, Base32, Base64, Base85 Data Encodings</a><ul>
<li class="toctree-l2"><a class="reference internal" href="base64.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="binascii.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">binascii</span></code> — Convert between binary and ASCII</a></li>
<li class="toctree-l1"><a class="reference internal" href="quopri.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">quopri</span></code> — Encode and decode MIME quoted-printable data</a></li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="mmap.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mmap</span></code> — Memory-mapped file support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/netdata.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.html" title="email — An email and MIME handling package"
             >next</a> |</li>
        <li class="right" >
          <a href="mmap.html" title="mmap — Memory-mapped file support"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Internet Data Handling</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>