# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_self_order
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <y.shad<PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <ifara<PERSON><EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# odooers ir, 2023
# fardin marda<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# zakariya moradi, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: zakariya moradi, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_self_order
#: model:ir.actions.report,print_report_name:pos_self_order.report_self_order_qr_codes_page
msgid "\"QR codes\""
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid ""
"%s is not available anymore, it has thus been removed from your order. "
"Please review your order and validate it again."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "/ Tracker number:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"<br/>\n"
"                    URL:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"<span>Please note that the kiosk only works with Adyen &amp; Stripe "
"terminals</span>"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
msgid ""
"Access rights of this user will be used when visiting self order website "
"when no session is open."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__active
msgid "Active"
msgstr "فعال"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add Languages"
msgstr "افزودن زبان‌ها"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add an image to brand your header."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Add images"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Add to Cart"
msgstr "افزودن به سبد"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Add to cart"
msgstr "افزودن به سبد"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "All the items will be removed from the cart."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Alternative Fiscal Position"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "An error has occurred"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Any items already sent will not be canceled"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Are you sure you want to cancel this order?"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
#, python-format
msgid "Available"
msgstr "در دسترس"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Available Languages"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Available in Self"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__self_order_available
msgid "Available in Self Order"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Available interface languages"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Back"
msgstr "بازگشت"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Cancel"
msgstr "لغو"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Cancel Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.js:0
#, python-format
msgid "Cancel order"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Choose the kiosk mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Choose the self ordering mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Choose when the customer will pay"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#, python-format
msgid "Choose your"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Choose your eating location"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
#, python-format
msgid "Close"
msgstr "بستن"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Close Session"
msgstr "بستن جلسه"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Combo"
msgstr "کمبو"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "خطوط ترکیبی"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "والد ترکیبی"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_id
msgid "Combo line reference"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Confirm"
msgstr "تایید کردن"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Connection lost, please try again later"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Could you please confirm your table number?"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.js:0
#, python-format
msgid "Current"
msgstr "جاری"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "Custom Links"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_self_order_custom_link
msgid ""
"Custom links that the restaurant can configure to be displayed on the self "
"order screen"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Customize Header"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__danger
msgid "Danger"
msgstr "خطر"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__dark
msgid "Dark"
msgstr "تاریک"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default"
msgstr "پیش‌فرض"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default Language"
msgstr "زبان پیشفرض"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default User"
msgstr "کاربر پیش‌فرض"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default language for the kiosk mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__nothing
msgid "Disable"
msgstr "غیر فعال کردن"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#: code:addons/pos_self_order/static/src/app/components/language_popup/language_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Discard"
msgstr "رها کردن"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Download QR Codes"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Each Order"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Each table in your floor plan is assigned a unique QR code based on your configuration. For security reasons,\n"
"                    both the point of sale and table names are encrypted in the generated URL, as shown in the example below:."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Eat In"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Eat in / Take out"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Edit"
msgstr "ویرایش"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/attribute_selection/attribute_selection.xml:0
#, python-format
msgid "Enter your custom value"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Feel free to use and print this QR code as many times as needed according to"
" your requirements."
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Fiscal Positions"
msgstr "موقعیت های مالی"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Follow instructions on the terminal"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "From your Kiosk, open this URL:"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Generic"
msgstr "عمومی"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Get a tracker and enter its number here"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid ""
"Hey, looks like you forgot to create products or add them to pos_config. "
"Please add them before using the Self Order"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Home buttons"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Hope you enjoyed your meal!"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to customize"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to use"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__id
msgid "ID"
msgstr "شناسه"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,help:pos_self_order.field_product_template__self_order_available
msgid "If this product is available in the Self Order screens"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"If you need customized QR codes, start by scanning the relevant QR code to acquire the URL. Then, make\n"
"                use of a QR code generator like https://www.qrcode-monkey.com or https://www.qr-code-generator.com"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Image to display on the self order screen"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid ""
"In Self-Order mode, you must have at least one table to generate QR codes"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__inactive
msgid "Inactive"
msgstr "غیرفعال"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__info
msgid "Info"
msgstr "اطلاعات"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Information about your product for Self Order and Kiosk"
msgstr ""

#. module: pos_self_order
#: model:ir.actions.act_window,name:pos_self_order.action_pos_self_order_search_view
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__kiosk
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_search_view
msgid "Kiosk"
msgstr "کیوسک"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__name
msgid "Label"
msgstr "بر چسب"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Language"
msgstr "زبان"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Languages available for the kiosk mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Let your customers order using their mobile or a kiosk."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__light
msgid "Light"
msgstr "روشن"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Loading..."
msgstr "بارگذاری..."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online or order with the QR codes on your tables"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online with the QR codes on your tables"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Meal"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Mobile Menu"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Mobile self-order & Kiosk"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Orders"
msgstr "سفارشات من"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "جدید"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "یادداشت"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Name of the image to display on the self order screen"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Next"
msgstr "بعدی"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "No"
msgstr "خیر"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "No order found"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "No products found"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Not available"
msgstr "در دسترس نیست"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Not available in Self"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Ok"
msgstr "تأیید"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "Only pay after each is available with kiosk mode."
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Open Kiosk"
msgstr "باز کردن باجه"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "Open in New Tab"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Order"
msgstr "سفارش"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: model:pos_self_order.custom_link,name:pos_self_order.default_custom_link
#, python-format
msgid "Order Now"
msgstr "اکنون سفارش دهید"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Order number:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Order to pick-up at the counter"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Orders not found on server"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Out of stock"
msgstr "ناموجود"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Pay"
msgstr "پرداخت"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Pay After:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Pay after"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Pay at the cashier"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Personalize your splash screen by adding one or multiple images to create a "
"slideshow"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Pickup At Counter"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__counter
msgid "Pickup zone"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Please wait until the price is loaded"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_session.py:0
#, python-format
msgid "PoS Order by Session"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_config
msgid "Point of Sale Configuration"
msgstr "پیکربندی پایانه فروش"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "سطرهای سفارش پایانه فروش"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order
msgid "Point of Sale Orders"
msgstr "سفارشات پایانه فروش"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "روش های پرداخت پایانه فروش"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_session
msgid "Point of Sale Session"
msgstr "نشست پایانه فروش"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Point of sale:"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid "Points of Sale"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__link_html
msgid "Preview"
msgstr "پیش‌نمایش"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Preview Web interface"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__primary
msgid "Primary"
msgstr "اولیه"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Print QR Codes"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_template
msgid "Product"
msgstr "محصول"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__description_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__description_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Product Description for Self Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "اطلاعات محصول"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_product
msgid "Product Variant"
msgstr "گونه محصول"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Product image"
msgstr "تصویر محصول"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product is not available"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product not found"
msgstr "محصول یافت نشد"

#. module: pos_self_order
#: model:ir.actions.report,name:pos_self_order.report_self_order_qr_codes_page
msgid "QR Codes"
msgstr "کدهای QR"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "QR codes can only be generated in mobile or consultation mode."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__consultation
msgid "QR menu"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "QR menu & Kiosk activation"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__mobile
msgid "QR menu + Ordering"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Quantity select"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Reset QR Codes"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_restaurant_table
msgid "Restaurant Table"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Retry"
msgstr "تلاش مجدد"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "Search product"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__secondary
msgid "Secondary"
msgstr "ثانویه"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__access_token
#: model:ir.model.fields,field_description:pos_self_order.field_restaurant_table__identifier
msgid "Security Token"
msgstr "توکن امنیتی"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid ""
"Select for which points of sale you want to display this link. Leave empty "
"to display it for all points of sale. You have to select among the points of"
" sale that have the 'QR Code Menu' feature enabled."
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Self Kiosk"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
msgid "Self Order Kiosk Image Brand"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Self Order Kiosk Image Brand Name"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Self Order:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Self Ordering"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Self Ordering Enabled"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Self Ordering Mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_url
msgid "Self Ordering Url"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Self-ordering availability:"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Service"
msgstr "خدمت"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Service at"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Service at Table"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Service at table"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Splash screens"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Start Kiosk"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__status
msgid "Status"
msgstr "وضعیت"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__style
msgid "Style"
msgstr "استایل"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__success
msgid "Success"
msgstr "موفقیت"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__table
msgid "Table"
msgstr "میز"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__table_stand_number
msgid "Table Stand Number"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Table Tracker:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Table detective time!"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Table:"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__take_away
msgid "Take Away"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Take Out"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_takeaway
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_takeaway
msgid "Takeaway"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Tax:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Taxes:"
msgstr "مالیاتها:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Thanks a lot!"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "The Self-Order default user must be a POS user"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "The user must be a POS user"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"این برای رستوران‌هایی با خدمات در محل و همراه با نرخ‌های مالیاتی خاص مفید "
"است."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Total:"
msgstr "مجموع:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__url
msgid "URL"
msgstr "URL"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "URL:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Uncategorised"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__warning
msgid "Warning"
msgstr "هشدار"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "We're currently closed"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid "We're currently closed."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "We're preparing your order!"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Yes"
msgstr "بله"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.js:0
#, python-format
msgid "You cannot edit a posted orderline !"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "You're not authorized to perform this action"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Your Order"
msgstr "سفارش شما"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Your Selection"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been canceled"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been paid"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Your order number"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order status has been changed"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "empty = all points of sale"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "https://odoo.com"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "odoo"
msgstr "اودو"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "options"
msgstr ""
