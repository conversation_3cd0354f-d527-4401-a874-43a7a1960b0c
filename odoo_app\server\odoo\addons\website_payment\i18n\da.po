# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
".\n"
"                        <br/>\n"
"                        Vi sætter pris på din støtte til vores organisation som sådan.\n"
"                        <br/>\n"
"                        Hilsen."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>Kommentar:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>Donationsdato:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>Donor Email:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>Donor Navn:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>Betalings-ID:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>Betalingsmetode:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Land...</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Kunne ikke finde nogen passende betalingsmulighed.</strong><br/>\n"
"                                Hvis du mener at dette er en fejl, bedes du venligst kontakte hjemmesiden administrator."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Advarsel</strong> Valutaen mangler eller er ugyldig."

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "En donation er blevet foretaget på din hjemmeside"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "Et år med kulturel opvågning."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Payments"
msgstr "Aktiver betalinger"

#. module: website_payment
#: model:ir.actions.server,name:website_payment.action_activate_stripe
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Stripe"
msgstr "Aktiver Stripe"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Tilføj en beskrivelse her"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "Tilføj ny forudfyldt valgmulighed"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Amount"
msgstr "Beløb"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Amount ("
msgstr "Beløb ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "Beløb("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "Pasning af en baby i 1 måned."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "Vælg dit beløb"

#. module: website_payment
#: model:ir.model,name:website_payment.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/res_config_settings.py:0
#, python-format
msgid "Configure %s"
msgstr "Konfigurer %s"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#: model:ir.model,name:website_payment.model_res_country
#, python-format
msgid "Country"
msgstr "Land"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Country\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Land\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "Land er påkrævet."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "Tilpasset beløb"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "Kære"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "Standardbeløb"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Descriptions"
msgstr "Beskrivelser"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "Vil muligheder"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donate"
msgstr "Doner"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "Doner nu"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation"
msgstr "Donation"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation Button"
msgstr "Donations knap"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "Donationsbeløbet skal være mindst %.2f."

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "Donationsbekræftelse"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "Donation notifikation"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "E-mailen er ugyldig"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "E-mail er påkrævet."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "Feltet '%s' er påkrævet"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__first_provider_label
msgid "First Provider Label"
msgstr "Første udbyder-etiket"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "Modtagelse"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "Er donation"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__is_stripe_supported_country
#: model:ir.model.fields,field_description:website_payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "Er Stripe Understøttet Land"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "Er donation"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "Foretag en donation"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Maximum"
msgstr "Maksimum"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Minimum"
msgstr "Minimum"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Name"
msgstr "Navn"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Name\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Navn\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "Navn er påkrævet."

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__none
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "Ingen"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "Et år i folkeskolen."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "Et år i gymnasiet."

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__other_than_paypal
msgid "Other than Paypal"
msgstr "Andet end Paypal"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Payment Details"
msgstr "Betalingsdetaljer"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_provider
msgid "Payment Provider"
msgstr "Betalingsudbyder"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransaktion"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "Behandlingen af betaling mislykkedes"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "Betaling modtaget ved donation med følgende oplysninger:"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "Betalinger"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__paypal_only
msgid "Paypal Only"
msgstr "Kun Paypal"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "Vælg eller indtast venligst et beløb"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "Forudfyldte valgmuligheder"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__providers_state
msgid "Providers State"
msgstr "Udbydere Stat"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "Modtager email"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Shop - Payment"
msgstr "Shop - Betaling"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "Slider"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "Lille eller stor, dit bidrag er afgørende."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "Der mangler nogle oplysninger til at behandle din betaling."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Step"
msgstr "Trin"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect er ikke tilgængelig i dit land. Brug venligst en anden "
"betalingsudbyder."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple "
"Pay, etc. as well as recurring charges."
msgstr ""
"Understøtter de fleste betalingsmetoder; Visa, Mastercard, Maestro, Google "
"Pay, Apple Pay osv. samt tilbagevendende opkrævninger."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "Tak for din donation af"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "Det mindste donationsbeløb er%s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "Der er ikke noget at betale."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View Alternatives"
msgstr "Se alternativer"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View other providers"
msgstr "Se andre udbydere"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_provider__website_id
msgid "Website"
msgstr "Hjemmeside"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
msgid "Website: Donation"
msgstr "Hjemmeside: Donation"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Write us a comment"
msgstr "Skriv en kommentar til os"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Your comment"
msgstr "Din kommentar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "lavet på"
