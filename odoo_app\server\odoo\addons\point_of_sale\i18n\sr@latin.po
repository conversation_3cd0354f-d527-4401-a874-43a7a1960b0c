# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * point_of_sale
#
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2017-10-10 11:34+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"Language: sr@latin\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "% Disc"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerListScreen.js:0
#, python-format
msgid "%s customer(s) found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "%s fiscal position(s) added to the configuration."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "%s has a total amount of %s, are you sure you want to delete this order?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidget.js:0
#, python-format
msgid "%s product(s) found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "& invoice"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "(Both will be sent by email)"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "(as of opening)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(ažuriraj)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "+ New Shop"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "/pos/ticket and use the code below to request an invoice online"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "00014-001-0001"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__ticket_code
msgid "5 digits alphanumeric code to be used by portal user to request an invoice"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid "<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping cart\" title=\"Shopping cart\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>How to manage tax-included prices"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "<p>Dear %(client_name)s,<br/>Here is your electronic ticket for the %(pos_name)s. </p>"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid "<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first session.</p>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "<span attrs=\"{'invisible': [('is_total_cost_computed','=', True)]}\">TBD</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Barcodes</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Cash Balance</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Pregled</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Amount of discounts</strong>:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<strong>Amounting to:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Config names</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>End of session note:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Number of discounts</strong>:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Opening of session note:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerDetailsEdit.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid "A globally unique identifier for this pos configuration, used to prevent conflicts in client-generated data."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid "A sequence number that is incremented each time a user resumes the pos session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "A session is a period of time, usually one day, during which you sell through the Point of Sale."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "A session is currently opened for this PoS. Some settings can only be changed after the session is closed."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Stripe payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_warning
msgid "Access warning"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Računovodstvo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Accounting journal used to post POS session journal entries and POS invoice payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Aktivan"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#, python-format
msgid "Add"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.js:0
#, python-format
msgid "Add Customer Note"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Add Tip"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a 5-digit code on the receipt to allow the user to request the invoice for an order on the portal."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Add a QR code on the ticket, which the user can scan to request the invoice linked to its order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Add a closing note..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a custom message to header and footer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#, python-format
msgid "Add a customer"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid "Add a new restaurant order printer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Add an opening note..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required information:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required invoicing information:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required user information:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#, python-format
msgid "Address"
msgstr "Adrese"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adds a button to set a global discount"
msgstr ""

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Advanced Cash Control"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "All active orders"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "All available pricelists must be in the same currency as the company or as the Sales Journal set on this point of sale if you use the Accounting application."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "All payment methods must be in the same currency as the Sales Journal or the company currency if that is not set."
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Sve prodajne linije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow Ship Later"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow cashiers to set a discount per line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to access each other's active orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to log and switch between selected Employees"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allowed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid "Allows to know if all the total cost of the order lines have already been computed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "Iznos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Ukupan Iznos"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "An error occurred when loading product prices. Make sure all pricelists are available in the POS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__name
msgid "An internal identification of the printer"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (faster)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "Atributi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Authorized Difference"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__auto_validate_terminal_payment
msgid "Auto Validate Terminal Payment"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validate order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_pos_config__auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validates orders paid with a payment terminal."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Available"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_available_pricelist_ids
msgid "Available Pricelists"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Srednja cijena"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ControlButtonPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Back"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/BackendButton.xml:0
#, python-format
msgid "Backend"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/NumberPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenNumpad.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Backspace"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#, python-format
msgid "Balance"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Stavka izvoda"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Barcode"
msgstr "Barkod"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "Barkod čitač"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Scanner/Card Reader"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Billing address:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Bills & Receipts"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift Card, Loyalty. Specific conditions can be set (products, customers, minimum purchase amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Buffer:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "CASH"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "CHANGE"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Can't change customer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Odustani"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "Poništeno"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#, python-format
msgid "Cash"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash In"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Cash In/Out"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_line_ids
msgid "Cash Lines"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash Out"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Cash Register"
msgstr "Kasa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Rounding"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_rounding
msgid "Cash Rounding (PoS)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Loss)"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Profit)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_rounding_method
msgid "Cash rounding"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cashdrawer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
#, python-format
msgid "Cashier"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Change"
msgstr "Izmjeni"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Change Tip"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid "Check if the product should be weighted using the hardware scale integration."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Check if you want to group products of this category in point of sale orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Check the internet connection then try to sync again by clicking on the red wifi button (upper right of the screen)."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Choose a specific fiscal position at the order depending on the kind of customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "City"
msgstr "Grad"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Click here to close the session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "klijent"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/HeaderButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Datum Zatvaranja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Closing Session"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Closing note"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Closing session error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Coins/Bills"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Preduzeće"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_company_has_template
msgid "Company has chart of accounts"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Postavka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Configure at least one Point of Sale."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/js/Popups/DatePickerPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Potvrdi"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#, python-format
msgid "Confirm?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect device to your PoS without an IoT Box"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect devices using an IoT Box"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connected Devices"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CustomerFacingDisplayButton.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/loader/loader.xml:0
#, python-format
msgid "Connecting to devices"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Connection error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#: model:ir.model,name:point_of_sale.model_res_partner
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Continue selling"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Cost:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/error_handlers/error_handlers.js:0
#, python-format
msgid "Couldn't connect to the server"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Counted"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Country"
msgstr "Država"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#, python-format
msgid "Create"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Create a new PoS"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "Create new products"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_header_or_footer
msgid "Custom Header & Footer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/CustomerButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Kupac"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Customer Display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "Faktura kupca"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer Required"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CustomerFacingDisplayButton.xml:0
#, python-format
msgid "Customer Screen"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CustomerFacingDisplayButton.xml:0
#, python-format
msgid "Customer Screen Connected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CustomerFacingDisplayButton.xml:0
#, python-format
msgid "Customer Screen Disconnected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CustomerFacingDisplayButton.js:0
#, python-format
msgid "Customer Screen Unsupported. Please upgrade the IoT Box"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CustomerFacingDisplayButton.xml:0
#, python-format
msgid "Customer Screen Warning"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerLine.xml:0
#, python-format
msgid "DETAILS"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Daily Report"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Daily Sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "Daily sessions hold sales from your Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
#, python-format
msgid "Date"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/DatePickerPopup.js:0
#, python-format
msgid "DatePicker"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Days"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Debug Window"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Journals"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Default Price"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_id
msgid "Default Pricelist"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Temporary Account"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default journals for orders and invoices"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid "Defines the name of the payment method that will be displayed in the Point of Sale when the payments are selected."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Kasnjenje Potvrde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Delete"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Paid Orders?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Unpaid Orders?"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Difference"
msgstr "Razlika"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid "Difference between the theoretical closing balance and the real closing balance."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/js/Popups/DatePickerPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Discard"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CustomerFacingDisplayButton.js:0
#, python-format
msgid "Disconnected"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Discount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Napomena Popusta"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discount:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discounts"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Discounts:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Dismiss"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Display orders on the preparation display"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Do you want to accept payments difference and post a profit/loss journal entry?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Don't show again"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "Done by"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Download a report with all the sales of the current PoS Session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Download error traceback"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"            Proxy where the printer can be found, and a list of product categories.\n"
"            An Order Printer will only print updates for products belonging to one of\n"
"            its categories."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Edit"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Either the server is inaccessible or browser is not connected online."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Electronic Scale"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Email sent."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Employees can scan their badge or enter a PIN to log in to a PoS session. These credentials are configurable in the *HR Settings* tab of the employee form."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Empty Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Enable barcode scanning with a remotely connected barcode scanner and card swiping with a Vantiv card reader."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerDetailsEdit.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Završni datum"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Završni saldo"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Error with Traceback"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Existing orderlines"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Exit Pos"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Expected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Expected delivery:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Dodatni podaci"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Financials"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "Fiskalne pozicije"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Fiscal data module error"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Pricelists"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Taxes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Footer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Force Done"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Force done"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid "Forces to set a customer when using this payment method and splits the journal entries for each customer. It could slow down the closing process."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid "Generate a code on ticket"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "Generate demo data"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Generation of your order references"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Get my invoice"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Daje redosled sekvenci pri prikazu liste kategorije proizvoda."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_discount
msgid "Global Discounts"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Grupiši po"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Events"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Status"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_has_active_session
msgid "Has Active Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__has_image
msgid "Has Image"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Header"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Home"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "How would you like to receive your receipt"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid "IP Address"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "If this orderline is a refund, then the refunded orderline is specified in this field."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "If you deliver all products at once, the delivery order will be scheduled based on the greatest product lead time. Otherwise, it will be based on the shortest."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__image
msgid "Image"
msgstr "Slika"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Import Orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "U toku"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time (accurate but slower)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#, python-format
msgid "Info"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_start_categ_id
msgid "Initial Category"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Insert a positive amount"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Intermediary account used for unidentified customers."
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Interne beleške"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Invalid action"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Invalid amount"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Invalid email."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Invalid product lot"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "Skladište"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Invoice Journal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Invoice Request"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "Fakturisano"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Invoices"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Invoicing confirmation"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box IP Address"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__is_default_pricelist_displayed
msgid "Is Default Pricelist Displayed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid "It is forbidden to modify a tax used in a POS order not posted. You must close the POS sessions before modifying the tax."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Journal"
msgstr "Dnevnik"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Sadrzaj dnevnika"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Journal Items"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Language"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last preparation change"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last printed state of the order"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_manual_discount
msgid "Line Discounts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_product_menu
msgid "Load Product Menu"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerDetailsEdit.js:0
#, python-format
msgid "Loading Image Error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Logo"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/OrderWidget.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Uradi placanje"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid "Make several pricelists available in the Point of Sale. You can also apply a pricelist to specific customers from their contact form (in Sales tab). To be valid, this pricelist must be listed here as an available pricelist. Otherwise the default pricelist will apply."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept the certificate of your IoT Box."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion that will grant customers discounts or gifts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Margin:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Margins & Costs"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Maximum value reached"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Metod"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Method Name"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Miscellaneous"
msgstr "Razno"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Mobile"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Module Pos Hr"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#, python-format
msgid "More info"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "More settings:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#, python-format
msgid "More..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Multi Employees per Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Need customer to invoice"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Need profit account for the following journals to post the gained amount: %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidget.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Network Error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Novi"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "New Order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Next Order List"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "No"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "No Point of Sale selected"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "No Taxes"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode_reader_service.js:0
#, python-format
msgid "No barcode nomenclature has been configured. This can be changed in the configuration settings."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "No cash statement found for this session. Unable to record returned cash."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "No chart of account configured, go to the \"configuration / settings\" menu, and install one from the Invoicing tab."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerListScreen.js:0
#, python-format
msgid "No more customer found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidget.js:0
#, python-format
msgid "No more product found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
#, python-format
msgid "No orders found"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "No product found for"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "No sale order found."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "No sessions found"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "None"
msgstr "Prazno"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Nije fakturisano"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "Zabilješke"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Br. Odstampanih"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_rescue_session
msgid "Number of Rescue Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Number of transactions:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "OK"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "OPENING CASH CONTROL"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Offline Error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Offline Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/AlertPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Ongoing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Only a negative quantity is allowed for this refund line. Click on +/- to modify the quantity to be refunded."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_only_round_cash_method
msgid "Only apply rounding on cash"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only on cash methods"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only users with Manager access rights for PoS app can modify the product prices on orders."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerDetailsEdit.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Open session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Datum Otvaranja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Opening balance summed to all cash transactions."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Opening cash"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation Type"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Nalog"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Order %s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Datum naloga"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "Order IDs Sequence"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Stavke narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_printer_ids
msgid "Order Printers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"            order updates in the kitchen/bar when the waiter updates the order."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "Veze naloga"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Order Reference"
msgstr "Oznaka naloga"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Order is empty"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Linije zahteva"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order reference"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SaveButton.js:0
#, python-format
msgid "Order saved for later"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""

#. module: point_of_sale
#. odoo-python
#. odoo-javascript
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "Narudžbe"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Other Devices"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Others"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "Linija POS zahteva"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "POS nalozi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "POS Nalozi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "Linije POS ZAhteva"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer_form
msgid "POS Printer"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "Plaćeno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Roditeljska kategorija"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Pay"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Plaćanje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Datum plaćanja"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Methods"
msgstr "Metodi plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Oznaka plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Payment Successful"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Payment method"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment methods available"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Payment request pending"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Payment reversed"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "Plaćanja"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Payments Difference"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Payments in"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Payments:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid "Person who uses the cash register. It can be a reliever, a student or an interim employee."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Phone"
msgstr "Telefon:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pick which product categories are available"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Biranje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking_type
msgid "Picking Type"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Picture"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid "Please close all the point of sale sessions in this period before closing it. Open sessions are: %s "
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Please close and validate the following open PoS Sessions before modifying this payment method.\n"
"Open sessions: %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Please create/select a Point of Sale above to show the configuration options."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please define income account for this product: '%s' (id:%d)."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Please enter your billing information <small class=\"text-muted\">or</small>"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "Please fill all the required fields."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please go on the %s journal and define a Loss Account. This account will be used to record cash difference."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please go on the %s journal and define a Profit Account. This account will be used to record cash difference."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Molim postavi partnera za prodaju"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "Please reload this page once products have been created"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Please select a payment method."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please select the Customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Interface"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Product Categories"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale"
msgstr "Mesto Prodaje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "POS Analiza"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
msgid "Point of Sale Configurations"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Point of Sale Journal"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_tree
msgid "Point of Sale List"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_printer
msgid "Point of Sale Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_list
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Point of Sales"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_url
msgid "Portal Access URL"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_allowed_pricelist_ids
msgid "Pos Allowed Pricelist"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_selectable_categ_ids
msgid "Pos Selectable Categ"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_posbox
msgid "PosBox"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Postcode"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Proknjiženo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_preparation_display
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation Display"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_printer_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer
msgid "Preparation Printers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Previous Order List"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Price"
msgstr "Cijena"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Price Control"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Price excl. VAT:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_extra
msgid "Price extra"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#, python-format
msgid "Price list"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "Cjenovnik"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cjenovnici"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Odredjivanje cijene"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Štampaj"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ControlButtons/ReprintReceiptButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#, python-format
msgid "Print Receipt"
msgstr "Stampa Racuna"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print a QR code on the receipt to allow the user to easily request the invoice for an order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_via_proxy
msgid "Print via Proxy"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Printer"
msgstr "Printer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__name
msgid "Printer Name"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__printer_type
msgid "Printer Type"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Printers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Printing failed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers due to no default printing protocol is available. It is possible to print your tickets by making use of an IoT Box."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Proizvod"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Grupa proizvoda"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "JM proizvoda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
msgid "Product Variant"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Product information"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidget.js:0
#, python-format
msgid "Product is not loaded. Tried loading the product from the server but there is a network error."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product prices on receipts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tipproduct
msgid "Product tips"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Proizvodi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Products:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Connected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_printer.py:0
#, python-format
msgid "Proxy IP cannot be empty."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Warning"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Qty"
msgstr "Kol."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "Količina"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "REASON"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "REFUNDED:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rating_ids
msgid "Ratings"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Reason"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Receipt"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_footer
msgid "Receipt Footer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_header
msgid "Receipt Header"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Receipt Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Refresh Display"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#, python-format
msgid "Refund"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#, python-format
msgid "Refunded"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "Refunding %s in "
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Reload POS Menu"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Remaining"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Remove"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Replenishment"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Izvještavanje"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Reprint Invoice"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Request invoice"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Request sent"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Reset"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_limit_categories
msgid "Restrict Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restrict price modification to managers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Resume Order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Retry"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of POS closing entry %s for order %s from session %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Review"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Rounding"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "SN"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "SOLD:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Linija prodaje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sales"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "detalji Prodaje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
msgid "Sales Journal"
msgstr "Dnevnik Prodaje"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SaveButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scale"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Scan me to request an invoice for your purchase."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scanner"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#, python-format
msgid "Search Customers..."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Search Orders..."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Search Products..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Pretrazi Prodajne Naloge"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "Search more"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_token
msgid "Security Token"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#, python-format
msgid "Select"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Select PoS to start sharing orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Select a payment method to validate the order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select an order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Select the pricelist"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Select the shipping date"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell products and deliver them later."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Send"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Send Payment Request"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Send by email"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Send receipt by email"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending in progress."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Served by"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Server Error"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session ID:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_daily_sales_reports
#: model:ir.ui.menu,name:point_of_sale.menu_report_daily_details
msgid "Session Report"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Session ids:"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set Maximum Difference"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Set Weight"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set a maximum difference allowed between the expected and counted money during the closing of the session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set a maximum difference allowed between the expected and counted money during the closing of the session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#, python-format
msgid "Set fiscal position"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set of coins/bills that will be used in opening and closing control"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Set the new quantity"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
msgid "Settings"
msgstr "Podešavanja"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Settings on this page will apply to this point of sale."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Share Open Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_ship_later
#, python-format
msgid "Ship Later"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__shipping_date
msgid "Shipping Date"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "Shipping Policy"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Shopping cart"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show checkout to customers through a second display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Show checkout to customers."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Show employee login screen"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs on product information"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_preparation_display
msgid "Show orders on the preparation display screen."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Sign in"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__skip_change
msgid "Skip line when sending ticket to kitchen printers."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Some orders could not be submitted to the server due to configuration errors. You can exit the Point of Sale, but do not close the session before the issue has been resolved."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "Some orders could not be submitted to the server due to internet connection issues. You can exit the Point of Sale, but do not close the session before the issue has been resolved."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Specific route"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_route_id
msgid "Spefic route for products delivered later."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_start_category
msgid "Start Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Početni datum"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Start category should belong in the available categories."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Start selling from a default product category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Početni saldo"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "State"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "Status"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock input for %s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock output for %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Street"
msgstr "Ulica"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Stripe"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_stripe
msgid "Stripe Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Međuzbir"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Medjuzbir w/o poreza"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Successfully imported"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Suma Subtotala"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Summary"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "TOTAL"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Porez"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Iznos poreza"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tax_included
msgid "Tax Display"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "Tax ID"
msgstr "ID takse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tax_regime_selection
msgid "Tax Regime Selection value"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Porezi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on refunds"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on sales"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Taxes:"
msgstr "Porezi:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The %s must be filled in your details."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#, python-format
msgid "The Point of Sale could not find any product, customer, employee or action associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid "The Point of Sale only supports the \"add a rounding line\" rounding strategy."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The Ticket Number should be at least 14 characters long."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The amount cannot be higher than the due amount if you don't have a cash payment method configured."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The amount of your payment lines must be rounded to validate the transaction."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must belong to no company or the company of the point of sale."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default tip product is missing. Please manually specify the tip product. (See Tips field.)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The fiscal data module encountered an error while receiving your order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "The function to load %s has not been implemented."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid "The hostname or ip address of the hardware proxy, Will be autodetected if left empty."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The invoice journal must be in the same currency as the Sales Journal or the company currency if that is not set."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The invoice journal of the point of sale %s must belong to the same company."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"The maximum difference allowed is %s.\n"
"\n"
"                        Please contact your manager to accept the closing difference."
msgstr ""

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/error_handlers/error_handlers.js:0
#, python-format
msgid "The operation couldn't be completed because you are offline. Check your internet connection and try again."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/error_handlers/error_handlers.js:0
#, python-format
msgid "The order couldn't be sent to the server because you are offline"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "The order has been already paid."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order has been synchronized earlier. Please make the invoice from the backend for the order: "
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "The payment method selected is not allowed in the config of the POS session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The payment methods for the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid "The point of sale will display this product category by default. If no category is specified, all available products will be shown."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid "The point of sale will only display products which are within one of the selected category trees. If no category is specified, all available products will be shown"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid "The pricelist used if no customer is selected or if the customer has no Sale Pricelist configured if any."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid "The rate of the currency to the currency of rate applicable at the date of the order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "The receipt screen will be skipped if the receipt can be printed automatically."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "The requested quantity to be refunded is higher than the ordered quantity. %s is requested while only %s can be refunded."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "The requested quantity to be refunded is higher than the refundable quantity of %s."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The sales journal of the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The selected pricelists must belong to no company or the company of the point of sale."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "The session has been already closed by another User. All sales completed in the meantime have been saved in a Rescue Session, which can be reviewed anytime and posted to Accounting from Point of Sale's dashboard."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "The session has been opened for an unusually long period. Please consider closing."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "The transactions are processed by Adyen. Set your Adyen credentials on the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "The transactions are processed by Six. Set the IP address of the terminal on the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_stripe
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "The transactions are processed by Stripe. Set your Stripe credentials on the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "The transactions are processed by Vantiv. Set your Vantiv credentials on the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "There are no products in this category."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is a difference between the amounts to post and the amounts of the orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "There is no Chart of Accounts configured on the company. Please go to the invoicing settings to install a Chart of Accounts."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash payment method for this PoS Session"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There must be at least one product in your order before it can be validated and invoiced."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"This cash payment method is already used in another Point of Sale.\n"
"A new cash payment method should be created for this Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "This field depicts the maximum difference allowed between the ending balance and the theoretical cash when closing a session, for non-POS managers. If this maximum is reached, the user will have an error message at the closing of his session saying that he needs to contact his manager."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid "This field is there to pass the id of the pos manager group to the point of sale client."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid "This field is there to pass the id of the pos user group to the point of sale client."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "This invoice has been created from the point of sale session: %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid "This is useful for restaurants with onsite and take-away services that imply specific tax rates."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid "This journal is associated with a payment method. You cannot modify its type"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "This operation will destroy all unpaid orders in the browser. You will lose all the unsaved data and exit the point of sale. This operation cannot be undone."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "This operation will permanently destroy all paid orders from the local storage. You will lose all the data. This operation cannot be undone."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/pos_store.js:0
#, python-format
msgid "This order already has refund lines for %s. We can't change the customer associated to it. Create a new order for the new customer."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#, python-format
msgid "This order is empty"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "This order is not yet synced to server. Make sure it is synced then try again."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This product is used as reference on customer receipts."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid "This sequence is automatically created by Odoo but you can change it to customize the reference numbers of your orders lines."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "This sequence is automatically created by Odoo but you can change it to customize the reference numbers of your orders."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Those settings are common to all PoS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__ticket_code
msgid "Ticket Code"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Ticket Nr"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Tip"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tip Product"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tips"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "To Pay"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "To Refund: %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "Ukupno"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Total (VAT excluded)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Cost:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Ukupni Popust"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Total Due"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Margin:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Ukupna cijena"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Price excl. VAT:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Total Taxes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Ukupna kol."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Ukupno:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Trusted POS"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__trusted_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_trusted_config_ids
msgid "Trusted Point of Sale Configurations"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to download invoice."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Unable to modify this PoS Configuration because you can't modify %s while a session is open."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode_reader_service.js:0
#, python-format
msgid "Unable to parse barcode"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/error_handlers/error_handlers.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Unique Code:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Unique code"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Jed. cena"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#, python-format
msgid "Unknown Barcode"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/error_handlers/error_handlers.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PartnerListScreen/PartnerDetailsEdit.js:0
#, python-format
msgid "Unsupported File Format"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Use QR code on ticket"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_use_pricelist
msgid "Use a pricelist."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use fiscal positions to get different taxes by order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Used to record product pickings. Products are consumed from its default source location."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "Korisnik"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__uuid
msgid "Uuid"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "PDV:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Valid product lot"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Validate"
msgstr "Potvrdi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Waiting for card"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Warehouse"
msgstr "Skladište"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_warehouse_id
msgid "Warehouse (PoS)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Weighing"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid "When disabled, only PoS manager can view the margin and cost of product among the Product info."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Whenever you close a session, one entry is generated in the following accounting journal for all the orders not invoiced. Invoices are recorded in accounting separately."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "With a"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Yes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You are not allowed to change the cash rounding configuration while a pos session using it is already opened."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "You can go to"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You can only have one cash payment method."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You can't: create a pos order from the backend interface, or unset the pricelist, or create a pos.order in a python test with Form tool, or edit the form view in studio if no PoS order exist"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid "You cannot archive '%s' as it is used by a POS configuration '%s'."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid "You cannot delete a bank statement line linked to Point of Sale session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "You cannot delete a point of sale category while a session is still opened."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "You cannot delete a product saleable in point of sale while a session is still opened."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_partner.py:0
#, python-format
msgid "You cannot delete contacts while there are active PoS sessions. Close the session(s) %s first."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SaveButton.js:0
#, python-format
msgid "You cannot save an empty order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You cannot share open orders with configuration that does not use the same currency."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You cannot use the same journal on multiples cash payment methods."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You don't have the access rights to get the point of sale closing control data."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You have enabled the \"Identify Customer\" option for %s payment method,but the order %s does not contain a customer."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You must have at least one payment method configured to launch a session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You need to select the customer before you can invoice or ship an order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Your PoS Session is open since %(date)s, we advise you to close it and to create a new one."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Your address is missing or incomplete. <br/>\n"
"                                Please make sure to"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerDetailsEdit.xml:0
#, python-format
msgid "ZIP"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "at"
msgstr "u"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "available,"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "before continuing."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "belong to another session:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "discount"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Company Address, Website"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "ePos Printer"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "fill all relevant information"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "for"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "for an order of"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "forecasted"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidget.xml:0
#, python-format
msgid "in this category."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#, python-format
msgid "items"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "orders:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "outstanding rescue session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "paid orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#, python-format
msgid "powered by"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "qx9h1"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Screens/PartnerListScreen/PartnerLine.xml:0
#, python-format
msgid "unselect"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr ""
