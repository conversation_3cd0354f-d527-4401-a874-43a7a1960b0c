<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="trace — Trace or track Python statement execution" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/trace.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/trace.py The trace module allows you to trace program execution, generate annotated statement coverage listings, print caller/callee relationships and list functions executed durin..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/trace.py The trace module allows you to trace program execution, generate annotated statement coverage listings, print caller/callee relationships and list functions executed durin..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>trace — Trace or track Python statement execution &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tracemalloc — Trace memory allocations" href="tracemalloc.html" />
    <link rel="prev" title="timeit — Measure execution time of small code snippets" href="timeit.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/trace.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code> — Trace or track Python statement execution</a><ul>
<li><a class="reference internal" href="#command-line-usage">Command-Line Usage</a><ul>
<li><a class="reference internal" href="#main-options">Main options</a></li>
<li><a class="reference internal" href="#modifiers">Modifiers</a></li>
<li><a class="reference internal" href="#filters">Filters</a></li>
</ul>
</li>
<li><a class="reference internal" href="#programmatic-interface">Programmatic Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="timeit.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">timeit</span></code> — Measure execution time of small code snippets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tracemalloc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code> — Trace memory allocations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/trace.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tracemalloc.html" title="tracemalloc — Trace memory allocations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="timeit.html" title="timeit — Measure execution time of small code snippets"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="debug.html" accesskey="U">Debugging and Profiling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code> — Trace or track Python statement execution</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-trace">
<span id="trace-trace-or-track-python-statement-execution"></span><h1><a class="reference internal" href="#module-trace" title="trace: Trace or track Python statement execution."><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code></a> — Trace or track Python statement execution<a class="headerlink" href="#module-trace" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/trace.py">Lib/trace.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-trace" title="trace: Trace or track Python statement execution."><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code></a> module allows you to trace program execution, generate
annotated statement coverage listings, print caller/callee relationships and
list functions executed during a program run.  It can be used in another program
or from the command line.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://coverage.readthedocs.io/">Coverage.py</a></dt><dd><p>A popular third-party coverage tool that provides HTML
output along with advanced features such as branch coverage.</p>
</dd>
</dl>
</div>
<section id="command-line-usage">
<span id="trace-cli"></span><h2>Command-Line Usage<a class="headerlink" href="#command-line-usage" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-trace" title="trace: Trace or track Python statement execution."><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code></a> module can be invoked from the command line.  It can be as
simple as</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">trace</span> <span class="o">--</span><span class="n">count</span> <span class="o">-</span><span class="n">C</span> <span class="o">.</span> <span class="n">somefile</span><span class="o">.</span><span class="n">py</span> <span class="o">...</span>
</pre></div>
</div>
<p>The above will execute <code class="file docutils literal notranslate"><span class="pre">somefile.py</span></code> and generate annotated listings of
all Python modules imported during the execution into the current directory.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-help">
<span class="sig-name descname"><span class="pre">--help</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-help" title="Link to this definition">¶</a></dt>
<dd><p>Display usage and exit.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-version">
<span class="sig-name descname"><span class="pre">--version</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-version" title="Link to this definition">¶</a></dt>
<dd><p>Display the version of the module and exit.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span>Added <code class="docutils literal notranslate"><span class="pre">--module</span></code> option that allows to run an executable module.</p>
</div>
<section id="main-options">
<h3>Main options<a class="headerlink" href="#main-options" title="Link to this heading">¶</a></h3>
<p>At least one of the following options must be specified when invoking
<a class="reference internal" href="#module-trace" title="trace: Trace or track Python statement execution."><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code></a>.  The <a class="reference internal" href="#cmdoption-trace-l"><code class="xref std std-option docutils literal notranslate"><span class="pre">--listfuncs</span></code></a> option is mutually exclusive with
the <a class="reference internal" href="#cmdoption-trace-t"><code class="xref std std-option docutils literal notranslate"><span class="pre">--trace</span></code></a> and <a class="reference internal" href="#cmdoption-trace-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">--count</span></code></a> options. When
<a class="reference internal" href="#cmdoption-trace-l"><code class="xref std std-option docutils literal notranslate"><span class="pre">--listfuncs</span></code></a> is provided, neither <a class="reference internal" href="#cmdoption-trace-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">--count</span></code></a> nor
<a class="reference internal" href="#cmdoption-trace-t"><code class="xref std std-option docutils literal notranslate"><span class="pre">--trace</span></code></a> are accepted, and vice versa.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-c">
<span id="cmdoption-trace-count"></span><span class="sig-name descname"><span class="pre">-c</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--count</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-c" title="Link to this definition">¶</a></dt>
<dd><p>Produce a set of annotated listing files upon program completion that shows
how many times each statement was executed.  See also
<a class="reference internal" href="#cmdoption-trace-C"><code class="xref std std-option docutils literal notranslate"><span class="pre">--coverdir</span></code></a>, <a class="reference internal" href="#cmdoption-trace-f"><code class="xref std std-option docutils literal notranslate"><span class="pre">--file</span></code></a> and
<a class="reference internal" href="#cmdoption-trace-R"><code class="xref std std-option docutils literal notranslate"><span class="pre">--no-report</span></code></a> below.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-t">
<span id="cmdoption-trace-trace"></span><span class="sig-name descname"><span class="pre">-t</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--trace</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-t" title="Link to this definition">¶</a></dt>
<dd><p>Display lines as they are executed.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-l">
<span id="cmdoption-trace-listfuncs"></span><span class="sig-name descname"><span class="pre">-l</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--listfuncs</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-l" title="Link to this definition">¶</a></dt>
<dd><p>Display the functions executed by running the program.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-r">
<span id="cmdoption-trace-report"></span><span class="sig-name descname"><span class="pre">-r</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--report</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-r" title="Link to this definition">¶</a></dt>
<dd><p>Produce an annotated list from an earlier program run that used the
<a class="reference internal" href="#cmdoption-trace-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">--count</span></code></a> and <a class="reference internal" href="#cmdoption-trace-f"><code class="xref std std-option docutils literal notranslate"><span class="pre">--file</span></code></a> option.  This does not
execute any code.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-T">
<span id="cmdoption-trace-trackcalls"></span><span class="sig-name descname"><span class="pre">-T</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--trackcalls</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-T" title="Link to this definition">¶</a></dt>
<dd><p>Display the calling relationships exposed by running the program.</p>
</dd></dl>

</section>
<section id="modifiers">
<h3>Modifiers<a class="headerlink" href="#modifiers" title="Link to this heading">¶</a></h3>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-f">
<span id="cmdoption-trace-file"></span><span class="sig-name descname"><span class="pre">-f</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--file</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;file&gt;</span></span><a class="headerlink" href="#cmdoption-trace-f" title="Link to this definition">¶</a></dt>
<dd><p>Name of a file to accumulate counts over several tracing runs.  Should be
used with the <a class="reference internal" href="#cmdoption-trace-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">--count</span></code></a> option.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-C">
<span id="cmdoption-trace-coverdir"></span><span class="sig-name descname"><span class="pre">-C</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--coverdir</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;dir&gt;</span></span><a class="headerlink" href="#cmdoption-trace-C" title="Link to this definition">¶</a></dt>
<dd><p>Directory where the report files go.  The coverage report for
<code class="docutils literal notranslate"><span class="pre">package.module</span></code> is written to file <code class="file docutils literal notranslate"><em><span class="pre">dir</span></em><span class="pre">/</span><em><span class="pre">package</span></em><span class="pre">/</span><em><span class="pre">module</span></em><span class="pre">.cover</span></code>.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-m">
<span id="cmdoption-trace-missing"></span><span class="sig-name descname"><span class="pre">-m</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--missing</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-m" title="Link to this definition">¶</a></dt>
<dd><p>When generating annotated listings, mark lines which were not executed with
<code class="docutils literal notranslate"><span class="pre">&gt;&gt;&gt;&gt;&gt;&gt;</span></code>.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-s">
<span id="cmdoption-trace-summary"></span><span class="sig-name descname"><span class="pre">-s</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--summary</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-s" title="Link to this definition">¶</a></dt>
<dd><p>When using <a class="reference internal" href="#cmdoption-trace-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">--count</span></code></a> or <a class="reference internal" href="#cmdoption-trace-r"><code class="xref std std-option docutils literal notranslate"><span class="pre">--report</span></code></a>, write a brief
summary to stdout for each file processed.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-R">
<span id="cmdoption-trace-no-report"></span><span class="sig-name descname"><span class="pre">-R</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--no-report</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-R" title="Link to this definition">¶</a></dt>
<dd><p>Do not generate annotated listings.  This is useful if you intend to make
several runs with <a class="reference internal" href="#cmdoption-trace-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">--count</span></code></a>, and then produce a single set of
annotated listings at the end.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-g">
<span id="cmdoption-trace-timing"></span><span class="sig-name descname"><span class="pre">-g</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--timing</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-trace-g" title="Link to this definition">¶</a></dt>
<dd><p>Prefix each line with the time since the program started.  Only used while
tracing.</p>
</dd></dl>

</section>
<section id="filters">
<h3>Filters<a class="headerlink" href="#filters" title="Link to this heading">¶</a></h3>
<p>These options may be repeated multiple times.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-ignore-module">
<span class="sig-name descname"><span class="pre">--ignore-module</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;mod&gt;</span></span><a class="headerlink" href="#cmdoption-trace-ignore-module" title="Link to this definition">¶</a></dt>
<dd><p>Ignore each of the given module names and its submodules (if it is a
package).  The argument can be a list of names separated by a comma.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-trace-ignore-dir">
<span class="sig-name descname"><span class="pre">--ignore-dir</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;dir&gt;</span></span><a class="headerlink" href="#cmdoption-trace-ignore-dir" title="Link to this definition">¶</a></dt>
<dd><p>Ignore all modules and packages in the named directory and subdirectories.
The argument can be a list of directories separated by <a class="reference internal" href="os.html#os.pathsep" title="os.pathsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.pathsep</span></code></a>.</p>
</dd></dl>

</section>
</section>
<section id="programmatic-interface">
<span id="trace-api"></span><h2>Programmatic Interface<a class="headerlink" href="#programmatic-interface" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="trace.Trace">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">trace.</span></span><span class="sig-name descname"><span class="pre">Trace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">countfuncs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">countcallers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignoremods</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignoredirs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">infile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outfile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trace.Trace" title="Link to this definition">¶</a></dt>
<dd><p>Create an object to trace execution of a single statement or expression.  All
parameters are optional.  <em>count</em> enables counting of line numbers.  <em>trace</em>
enables line execution tracing.  <em>countfuncs</em> enables listing of the
functions called during the run.  <em>countcallers</em> enables call relationship
tracking.  <em>ignoremods</em> is a list of modules or packages to ignore.
<em>ignoredirs</em> is a list of directories whose modules or packages should be
ignored.  <em>infile</em> is the name of the file from which to read stored count
information.  <em>outfile</em> is the name of the file in which to write updated
count information.  <em>timing</em> enables a timestamp relative to when tracing was
started to be displayed.</p>
<dl class="py method">
<dt class="sig sig-object py" id="trace.Trace.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trace.Trace.run" title="Link to this definition">¶</a></dt>
<dd><p>Execute the command and gather statistics from the execution with
the current tracing parameters.  <em>cmd</em> must be a string or code object,
suitable for passing into <a class="reference internal" href="functions.html#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="trace.Trace.runctx">
<span class="sig-name descname"><span class="pre">runctx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trace.Trace.runctx" title="Link to this definition">¶</a></dt>
<dd><p>Execute the command and gather statistics from the execution with the
current tracing parameters, in the defined global and local
environments.  If not defined, <em>globals</em> and <em>locals</em> default to empty
dictionaries.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="trace.Trace.runfunc">
<span class="sig-name descname"><span class="pre">runfunc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trace.Trace.runfunc" title="Link to this definition">¶</a></dt>
<dd><p>Call <em>func</em> with the given arguments under control of the <a class="reference internal" href="#trace.Trace" title="trace.Trace"><code class="xref py py-class docutils literal notranslate"><span class="pre">Trace</span></code></a>
object with the current tracing parameters.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="trace.Trace.results">
<span class="sig-name descname"><span class="pre">results</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#trace.Trace.results" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#trace.CoverageResults" title="trace.CoverageResults"><code class="xref py py-class docutils literal notranslate"><span class="pre">CoverageResults</span></code></a> object that contains the cumulative
results of all previous calls to <code class="docutils literal notranslate"><span class="pre">run</span></code>, <code class="docutils literal notranslate"><span class="pre">runctx</span></code> and <code class="docutils literal notranslate"><span class="pre">runfunc</span></code>
for the given <a class="reference internal" href="#trace.Trace" title="trace.Trace"><code class="xref py py-class docutils literal notranslate"><span class="pre">Trace</span></code></a> instance.  Does not reset the accumulated
trace results.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="trace.CoverageResults">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">trace.</span></span><span class="sig-name descname"><span class="pre">CoverageResults</span></span><a class="headerlink" href="#trace.CoverageResults" title="Link to this definition">¶</a></dt>
<dd><p>A container for coverage results, created by <a class="reference internal" href="#trace.Trace.results" title="trace.Trace.results"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Trace.results()</span></code></a>.  Should
not be created directly by the user.</p>
<dl class="py method">
<dt class="sig sig-object py" id="trace.CoverageResults.update">
<span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trace.CoverageResults.update" title="Link to this definition">¶</a></dt>
<dd><p>Merge in data from another <a class="reference internal" href="#trace.CoverageResults" title="trace.CoverageResults"><code class="xref py py-class docutils literal notranslate"><span class="pre">CoverageResults</span></code></a> object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="trace.CoverageResults.write_results">
<span class="sig-name descname"><span class="pre">write_results</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">summary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">coverdir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trace.CoverageResults.write_results" title="Link to this definition">¶</a></dt>
<dd><p>Write coverage results.  Set <em>show_missing</em> to show lines that had no
hits.  Set <em>summary</em> to include in the output the coverage summary per
module.  <em>coverdir</em> specifies the directory into which the coverage
result files will be output.  If <code class="docutils literal notranslate"><span class="pre">None</span></code>, the results for each source
file are placed in its directory.</p>
</dd></dl>

</dd></dl>

<p>A simple example demonstrating the use of the programmatic interface:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">import</span> <span class="nn">trace</span>

<span class="c1"># create a Trace object, telling it what to ignore, and whether to</span>
<span class="c1"># do tracing or line-counting or both.</span>
<span class="n">tracer</span> <span class="o">=</span> <span class="n">trace</span><span class="o">.</span><span class="n">Trace</span><span class="p">(</span>
    <span class="n">ignoredirs</span><span class="o">=</span><span class="p">[</span><span class="n">sys</span><span class="o">.</span><span class="n">prefix</span><span class="p">,</span> <span class="n">sys</span><span class="o">.</span><span class="n">exec_prefix</span><span class="p">],</span>
    <span class="n">trace</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
    <span class="n">count</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

<span class="c1"># run the new command using the given tracer</span>
<span class="n">tracer</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="s1">&#39;main()&#39;</span><span class="p">)</span>

<span class="c1"># make a report, placing output in the current directory</span>
<span class="n">r</span> <span class="o">=</span> <span class="n">tracer</span><span class="o">.</span><span class="n">results</span><span class="p">()</span>
<span class="n">r</span><span class="o">.</span><span class="n">write_results</span><span class="p">(</span><span class="n">show_missing</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">coverdir</span><span class="o">=</span><span class="s2">&quot;.&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code> — Trace or track Python statement execution</a><ul>
<li><a class="reference internal" href="#command-line-usage">Command-Line Usage</a><ul>
<li><a class="reference internal" href="#main-options">Main options</a></li>
<li><a class="reference internal" href="#modifiers">Modifiers</a></li>
<li><a class="reference internal" href="#filters">Filters</a></li>
</ul>
</li>
<li><a class="reference internal" href="#programmatic-interface">Programmatic Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="timeit.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">timeit</span></code> — Measure execution time of small code snippets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tracemalloc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code> — Trace memory allocations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/trace.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tracemalloc.html" title="tracemalloc — Trace memory allocations"
             >next</a> |</li>
        <li class="right" >
          <a href="timeit.html" title="timeit — Measure execution time of small code snippets"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="debug.html" >Debugging and Profiling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">trace</span></code> — Trace or track Python statement execution</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>