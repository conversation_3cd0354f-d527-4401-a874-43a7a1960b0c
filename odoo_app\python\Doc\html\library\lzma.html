<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="lzma — Compression using the LZMA algorithm" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/lzma.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/lzma.py This module provides classes and convenience functions for compressing and decompressing data using the LZMA compression algorithm. Also included is a file interface suppor..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/lzma.py This module provides classes and convenience functions for compressing and decompressing data using the LZMA compression algorithm. Also included is a file interface suppor..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>lzma — Compression using the LZMA algorithm &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="zipfile — Work with ZIP archives" href="zipfile.html" />
    <link rel="prev" title="bz2 — Support for bzip2 compression" href="bz2.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/lzma.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code> — Compression using the LZMA algorithm</a><ul>
<li><a class="reference internal" href="#reading-and-writing-compressed-files">Reading and writing compressed files</a></li>
<li><a class="reference internal" href="#compressing-and-decompressing-data-in-memory">Compressing and decompressing data in memory</a></li>
<li><a class="reference internal" href="#miscellaneous">Miscellaneous</a></li>
<li><a class="reference internal" href="#specifying-custom-filter-chains">Specifying custom filter chains</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="bz2.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code> — Support for <strong class="program">bzip2</strong> compression</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="zipfile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/lzma.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="zipfile.html" title="zipfile — Work with ZIP archives"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="bz2.html" title="bz2 — Support for bzip2 compression"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" accesskey="U">Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code> — Compression using the LZMA algorithm</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-lzma">
<span id="lzma-compression-using-the-lzma-algorithm"></span><h1><a class="reference internal" href="#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> — Compression using the LZMA algorithm<a class="headerlink" href="#module-lzma" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/lzma.py">Lib/lzma.py</a></p>
<hr class="docutils" />
<p>This module provides classes and convenience functions for compressing and
decompressing data using the LZMA compression algorithm. Also included is a file
interface supporting the <code class="docutils literal notranslate"><span class="pre">.xz</span></code> and legacy <code class="docutils literal notranslate"><span class="pre">.lzma</span></code> file formats used by the
<strong class="program">xz</strong> utility, as well as raw compressed streams.</p>
<p>The interface provided by this module is very similar to that of the <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a>
module. Note that <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> and <a class="reference internal" href="bz2.html#bz2.BZ2File" title="bz2.BZ2File"><code class="xref py py-class docutils literal notranslate"><span class="pre">bz2.BZ2File</span></code></a> are <em>not</em>
thread-safe, so if you need to use a single <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> instance
from multiple threads, it is necessary to protect it with a lock.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="lzma.LZMAError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">LZMAError</span></span><a class="headerlink" href="#lzma.LZMAError" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised when an error occurs during compression or
decompression, or while initializing the compressor/decompressor state.</p>
</dd></dl>

<section id="reading-and-writing-compressed-files">
<h2>Reading and writing compressed files<a class="headerlink" href="#reading-and-writing-compressed-files" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="lzma.open">
<span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'rb'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.open" title="Link to this definition">¶</a></dt>
<dd><p>Open an LZMA-compressed file in binary or text mode, returning a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file
object</span></a>.</p>
<p>The <em>filename</em> argument can be either an actual file name (given as a
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like</span></a> object), in
which case the named file is opened, or it can be an existing file object
to read from or write to.</p>
<p>The <em>mode</em> argument can be any of <code class="docutils literal notranslate"><span class="pre">&quot;r&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;rb&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;w&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;wb&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;x&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;xb&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;a&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;ab&quot;</span></code> for binary mode, or <code class="docutils literal notranslate"><span class="pre">&quot;rt&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;wt&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;xt&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;at&quot;</span></code> for text mode. The default is <code class="docutils literal notranslate"><span class="pre">&quot;rb&quot;</span></code>.</p>
<p>When opening a file for reading, the <em>format</em> and <em>filters</em> arguments have
the same meanings as for <a class="reference internal" href="#lzma.LZMADecompressor" title="lzma.LZMADecompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMADecompressor</span></code></a>. In this case, the <em>check</em>
and <em>preset</em> arguments should not be used.</p>
<p>When opening a file for writing, the <em>format</em>, <em>check</em>, <em>preset</em> and
<em>filters</em> arguments have the same meanings as for <a class="reference internal" href="#lzma.LZMACompressor" title="lzma.LZMACompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMACompressor</span></code></a>.</p>
<p>For binary mode, this function is equivalent to the <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a>
constructor: <code class="docutils literal notranslate"><span class="pre">LZMAFile(filename,</span> <span class="pre">mode,</span> <span class="pre">...)</span></code>. In this case, the <em>encoding</em>,
<em>errors</em> and <em>newline</em> arguments must not be provided.</p>
<p>For text mode, a <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> object is created, and wrapped in an
<a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> instance with the specified encoding, error
handling behavior, and line ending(s).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for the <code class="docutils literal notranslate"><span class="pre">&quot;x&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;xb&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;xt&quot;</span></code> modes.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="lzma.LZMAFile">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">LZMAFile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMAFile" title="Link to this definition">¶</a></dt>
<dd><p>Open an LZMA-compressed file in binary mode.</p>
<p>An <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> can wrap an already-open <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>, or operate
directly on a named file. The <em>filename</em> argument specifies either the file
object to wrap, or the name of the file to open (as a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>,
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like</span></a> object). When wrapping an
existing file object, the wrapped file will not be closed when the
<a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> is closed.</p>
<p>The <em>mode</em> argument can be either <code class="docutils literal notranslate"><span class="pre">&quot;r&quot;</span></code> for reading (default), <code class="docutils literal notranslate"><span class="pre">&quot;w&quot;</span></code> for
overwriting, <code class="docutils literal notranslate"><span class="pre">&quot;x&quot;</span></code> for exclusive creation, or <code class="docutils literal notranslate"><span class="pre">&quot;a&quot;</span></code> for appending. These
can equivalently be given as <code class="docutils literal notranslate"><span class="pre">&quot;rb&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;wb&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;xb&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;ab&quot;</span></code>
respectively.</p>
<p>If <em>filename</em> is a file object (rather than an actual file name), a mode of
<code class="docutils literal notranslate"><span class="pre">&quot;w&quot;</span></code> does not truncate the file, and is instead equivalent to <code class="docutils literal notranslate"><span class="pre">&quot;a&quot;</span></code>.</p>
<p>When opening a file for reading, the input file may be the concatenation of
multiple separate compressed streams. These are transparently decoded as a
single logical stream.</p>
<p>When opening a file for reading, the <em>format</em> and <em>filters</em> arguments have
the same meanings as for <a class="reference internal" href="#lzma.LZMADecompressor" title="lzma.LZMADecompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMADecompressor</span></code></a>. In this case, the <em>check</em>
and <em>preset</em> arguments should not be used.</p>
<p>When opening a file for writing, the <em>format</em>, <em>check</em>, <em>preset</em> and
<em>filters</em> arguments have the same meanings as for <a class="reference internal" href="#lzma.LZMACompressor" title="lzma.LZMACompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMACompressor</span></code></a>.</p>
<p><a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> supports all the members specified by
<a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedIOBase</span></code></a>, except for <a class="reference internal" href="io.html#io.BufferedIOBase.detach" title="io.BufferedIOBase.detach"><code class="xref py py-meth docutils literal notranslate"><span class="pre">detach()</span></code></a>
and <a class="reference internal" href="io.html#io.IOBase.truncate" title="io.IOBase.truncate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">truncate()</span></code></a>.
Iteration and the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement are supported.</p>
<p>The following method is also provided:</p>
<dl class="py method">
<dt class="sig sig-object py" id="lzma.LZMAFile.peek">
<span class="sig-name descname"><span class="pre">peek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMAFile.peek" title="Link to this definition">¶</a></dt>
<dd><p>Return buffered data without advancing the file position. At least one
byte of data will be returned, unless EOF has been reached. The exact
number of bytes returned is unspecified (the <em>size</em> argument is ignored).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>While calling <a class="reference internal" href="#lzma.LZMAFile.peek" title="lzma.LZMAFile.peek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">peek()</span></code></a> does not change the file position of
the <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a>, it may change the position of the underlying
file object (e.g. if the <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a> was constructed by passing a
file object for <em>filename</em>).</p>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for the <code class="docutils literal notranslate"><span class="pre">&quot;x&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;xb&quot;</span></code> modes.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <a class="reference internal" href="io.html#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> method now accepts an argument of
<code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

</section>
<section id="compressing-and-decompressing-data-in-memory">
<h2>Compressing and decompressing data in memory<a class="headerlink" href="#compressing-and-decompressing-data-in-memory" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="lzma.LZMACompressor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">LZMACompressor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">FORMAT_XZ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMACompressor" title="Link to this definition">¶</a></dt>
<dd><p>Create a compressor object, which can be used to compress data incrementally.</p>
<p>For a more convenient way of compressing a single chunk of data, see
<a class="reference internal" href="#lzma.compress" title="lzma.compress"><code class="xref py py-func docutils literal notranslate"><span class="pre">compress()</span></code></a>.</p>
<p>The <em>format</em> argument specifies what container format should be used.
Possible values are:</p>
<ul class="simple">
<li><dl class="simple">
<dt><code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_XZ</span></code>: The <code class="docutils literal notranslate"><span class="pre">.xz</span></code> container format.</dt><dd><p>This is the default format.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_ALONE</span></code>: The legacy <code class="docutils literal notranslate"><span class="pre">.lzma</span></code> container format.</dt><dd><p>This format is more limited than <code class="docutils literal notranslate"><span class="pre">.xz</span></code> – it does not support integrity
checks or multiple filters.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_RAW</span></code>: A raw data stream, not using any container format.</dt><dd><p>This format specifier does not support integrity checks, and requires that
you always specify a custom filter chain (for both compression and
decompression). Additionally, data compressed in this manner cannot be
decompressed using <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_AUTO</span></code> (see <a class="reference internal" href="#lzma.LZMADecompressor" title="lzma.LZMADecompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMADecompressor</span></code></a>).</p>
</dd>
</dl>
</li>
</ul>
<p>The <em>check</em> argument specifies the type of integrity check to include in the
compressed data. This check is used when decompressing, to ensure that the
data has not been corrupted. Possible values are:</p>
<ul class="simple">
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_NONE</span></code>: No integrity check.
This is the default (and the only acceptable value) for
<code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_ALONE</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_RAW</span></code>.</p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_CRC32</span></code>: 32-bit Cyclic Redundancy Check.</p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_CRC64</span></code>: 64-bit Cyclic Redundancy Check.
This is the default for <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_XZ</span></code>.</p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_SHA256</span></code>: 256-bit Secure Hash Algorithm.</p></li>
</ul>
<p>If the specified check is not supported, an <a class="reference internal" href="#lzma.LZMAError" title="lzma.LZMAError"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAError</span></code></a> is raised.</p>
<p>The compression settings can be specified either as a preset compression
level (with the <em>preset</em> argument), or in detail as a custom filter chain
(with the <em>filters</em> argument).</p>
<p>The <em>preset</em> argument (if provided) should be an integer between <code class="docutils literal notranslate"><span class="pre">0</span></code> and
<code class="docutils literal notranslate"><span class="pre">9</span></code> (inclusive), optionally OR-ed with the constant
<code class="xref py py-const docutils literal notranslate"><span class="pre">PRESET_EXTREME</span></code>. If neither <em>preset</em> nor <em>filters</em> are given, the
default behavior is to use <code class="xref py py-const docutils literal notranslate"><span class="pre">PRESET_DEFAULT</span></code> (preset level <code class="docutils literal notranslate"><span class="pre">6</span></code>).
Higher presets produce smaller output, but make the compression process
slower.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In addition to being more CPU-intensive, compression with higher presets
also requires much more memory (and produces output that needs more memory
to decompress). With preset <code class="docutils literal notranslate"><span class="pre">9</span></code> for example, the overhead for an
<a class="reference internal" href="#lzma.LZMACompressor" title="lzma.LZMACompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMACompressor</span></code></a> object can be as high as 800 MiB. For this reason,
it is generally best to stick with the default preset.</p>
</div>
<p>The <em>filters</em> argument (if provided) should be a filter chain specifier.
See <a class="reference internal" href="#filter-chain-specs"><span class="std std-ref">Specifying custom filter chains</span></a> for details.</p>
<dl class="py method">
<dt class="sig sig-object py" id="lzma.LZMACompressor.compress">
<span class="sig-name descname"><span class="pre">compress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMACompressor.compress" title="Link to this definition">¶</a></dt>
<dd><p>Compress <em>data</em> (a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object), returning a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>
object containing compressed data for at least part of the input. Some of
<em>data</em> may be buffered internally, for use in later calls to
<a class="reference internal" href="#lzma.compress" title="lzma.compress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">compress()</span></code></a> and <a class="reference internal" href="#lzma.LZMACompressor.flush" title="lzma.LZMACompressor.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a>. The returned data should be
concatenated with the output of any previous calls to <a class="reference internal" href="#lzma.compress" title="lzma.compress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">compress()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="lzma.LZMACompressor.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMACompressor.flush" title="Link to this definition">¶</a></dt>
<dd><p>Finish the compression process, returning a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object
containing any data stored in the compressor’s internal buffers.</p>
<p>The compressor cannot be used after this method has been called.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="lzma.LZMADecompressor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">LZMADecompressor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">FORMAT_AUTO</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">memlimit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMADecompressor" title="Link to this definition">¶</a></dt>
<dd><p>Create a decompressor object, which can be used to decompress data
incrementally.</p>
<p>For a more convenient way of decompressing an entire compressed stream at
once, see <a class="reference internal" href="#lzma.decompress" title="lzma.decompress"><code class="xref py py-func docutils literal notranslate"><span class="pre">decompress()</span></code></a>.</p>
<p>The <em>format</em> argument specifies the container format that should be used. The
default is <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_AUTO</span></code>, which can decompress both <code class="docutils literal notranslate"><span class="pre">.xz</span></code> and
<code class="docutils literal notranslate"><span class="pre">.lzma</span></code> files. Other possible values are <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_XZ</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_ALONE</span></code>, and <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_RAW</span></code>.</p>
<p>The <em>memlimit</em> argument specifies a limit (in bytes) on the amount of memory
that the decompressor can use. When this argument is used, decompression will
fail with an <a class="reference internal" href="#lzma.LZMAError" title="lzma.LZMAError"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAError</span></code></a> if it is not possible to decompress the input
within the given memory limit.</p>
<p>The <em>filters</em> argument specifies the filter chain that was used to create
the stream being decompressed. This argument is required if <em>format</em> is
<code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_RAW</span></code>, but should not be used for other formats.
See <a class="reference internal" href="#filter-chain-specs"><span class="std std-ref">Specifying custom filter chains</span></a> for more information about filter chains.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This class does not transparently handle inputs containing multiple
compressed streams, unlike <a class="reference internal" href="#lzma.decompress" title="lzma.decompress"><code class="xref py py-func docutils literal notranslate"><span class="pre">decompress()</span></code></a> and <a class="reference internal" href="#lzma.LZMAFile" title="lzma.LZMAFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMAFile</span></code></a>. To
decompress a multi-stream input with <a class="reference internal" href="#lzma.LZMADecompressor" title="lzma.LZMADecompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMADecompressor</span></code></a>, you must
create a new decompressor for each stream.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="lzma.LZMADecompressor.decompress">
<span class="sig-name descname"><span class="pre">decompress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.LZMADecompressor.decompress" title="Link to this definition">¶</a></dt>
<dd><p>Decompress <em>data</em> (a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>), returning
uncompressed data as bytes. Some of <em>data</em> may be buffered
internally, for use in later calls to <a class="reference internal" href="#lzma.decompress" title="lzma.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a>. The
returned data should be concatenated with the output of any
previous calls to <a class="reference internal" href="#lzma.decompress" title="lzma.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a>.</p>
<p>If <em>max_length</em> is nonnegative, returns at most <em>max_length</em>
bytes of decompressed data. If this limit is reached and further
output can be produced, the <a class="reference internal" href="#lzma.LZMADecompressor.needs_input" title="lzma.LZMADecompressor.needs_input"><code class="xref py py-attr docutils literal notranslate"><span class="pre">needs_input</span></code></a> attribute will
be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>. In this case, the next call to
<a class="reference internal" href="#lzma.LZMADecompressor.decompress" title="lzma.LZMADecompressor.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> may provide <em>data</em> as <code class="docutils literal notranslate"><span class="pre">b''</span></code> to obtain
more of the output.</p>
<p>If all of the input data was decompressed and returned (either
because this was less than <em>max_length</em> bytes, or because
<em>max_length</em> was negative), the <a class="reference internal" href="#lzma.LZMADecompressor.needs_input" title="lzma.LZMADecompressor.needs_input"><code class="xref py py-attr docutils literal notranslate"><span class="pre">needs_input</span></code></a> attribute
will be set to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<p>Attempting to decompress data after the end of stream is reached
raises an <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a>.  Any data found after the end of the
stream is ignored and saved in the <a class="reference internal" href="#lzma.LZMADecompressor.unused_data" title="lzma.LZMADecompressor.unused_data"><code class="xref py py-attr docutils literal notranslate"><span class="pre">unused_data</span></code></a> attribute.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>max_length</em> parameter.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="lzma.LZMADecompressor.check">
<span class="sig-name descname"><span class="pre">check</span></span><a class="headerlink" href="#lzma.LZMADecompressor.check" title="Link to this definition">¶</a></dt>
<dd><p>The ID of the integrity check used by the input stream. This may be
<code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_UNKNOWN</span></code> until enough of the input has been decoded to
determine what integrity check it uses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="lzma.LZMADecompressor.eof">
<span class="sig-name descname"><span class="pre">eof</span></span><a class="headerlink" href="#lzma.LZMADecompressor.eof" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the end-of-stream marker has been reached.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="lzma.LZMADecompressor.unused_data">
<span class="sig-name descname"><span class="pre">unused_data</span></span><a class="headerlink" href="#lzma.LZMADecompressor.unused_data" title="Link to this definition">¶</a></dt>
<dd><p>Data found after the end of the compressed stream.</p>
<p>Before the end of the stream is reached, this will be <code class="docutils literal notranslate"><span class="pre">b&quot;&quot;</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="lzma.LZMADecompressor.needs_input">
<span class="sig-name descname"><span class="pre">needs_input</span></span><a class="headerlink" href="#lzma.LZMADecompressor.needs_input" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">False</span></code> if the <a class="reference internal" href="#lzma.LZMADecompressor.decompress" title="lzma.LZMADecompressor.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> method can provide more
decompressed data before requiring new uncompressed input.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="lzma.compress">
<span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">compress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">FORMAT_XZ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.compress" title="Link to this definition">¶</a></dt>
<dd><p>Compress <em>data</em> (a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object), returning the compressed data as a
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
<p>See <a class="reference internal" href="#lzma.LZMACompressor" title="lzma.LZMACompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMACompressor</span></code></a> above for a description of the <em>format</em>, <em>check</em>,
<em>preset</em> and <em>filters</em> arguments.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="lzma.decompress">
<span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">decompress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">FORMAT_AUTO</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">memlimit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.decompress" title="Link to this definition">¶</a></dt>
<dd><p>Decompress <em>data</em> (a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object), returning the uncompressed data
as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
<p>If <em>data</em> is the concatenation of multiple distinct compressed streams,
decompress all of these streams, and return the concatenation of the results.</p>
<p>See <a class="reference internal" href="#lzma.LZMADecompressor" title="lzma.LZMADecompressor"><code class="xref py py-class docutils literal notranslate"><span class="pre">LZMADecompressor</span></code></a> above for a description of the <em>format</em>,
<em>memlimit</em> and <em>filters</em> arguments.</p>
</dd></dl>

</section>
<section id="miscellaneous">
<h2>Miscellaneous<a class="headerlink" href="#miscellaneous" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="lzma.is_check_supported">
<span class="sig-prename descclassname"><span class="pre">lzma.</span></span><span class="sig-name descname"><span class="pre">is_check_supported</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">check</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#lzma.is_check_supported" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the given integrity check is supported on this system.</p>
<p><code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_NONE</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_CRC32</span></code> are always supported.
<code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_CRC64</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">CHECK_SHA256</span></code> may be unavailable if you are
using a version of <strong class="program">liblzma</strong> that was compiled with a limited
feature set.</p>
</dd></dl>

</section>
<section id="specifying-custom-filter-chains">
<span id="filter-chain-specs"></span><h2>Specifying custom filter chains<a class="headerlink" href="#specifying-custom-filter-chains" title="Link to this heading">¶</a></h2>
<p>A filter chain specifier is a sequence of dictionaries, where each dictionary
contains the ID and options for a single filter. Each dictionary must contain
the key <code class="docutils literal notranslate"><span class="pre">&quot;id&quot;</span></code>, and may contain additional keys to specify filter-dependent
options. Valid filter IDs are as follows:</p>
<ul class="simple">
<li><p>Compression filters:</p>
<ul>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_LZMA1</span></code> (for use with <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_ALONE</span></code>)</p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_LZMA2</span></code> (for use with <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_XZ</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">FORMAT_RAW</span></code>)</p></li>
</ul>
</li>
<li><p>Delta filter:</p>
<ul>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_DELTA</span></code></p></li>
</ul>
</li>
<li><p>Branch-Call-Jump (BCJ) filters:</p>
<ul>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_X86</span></code></p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_IA64</span></code></p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_ARM</span></code></p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_ARMTHUMB</span></code></p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_POWERPC</span></code></p></li>
<li><p><code class="xref py py-const docutils literal notranslate"><span class="pre">FILTER_SPARC</span></code></p></li>
</ul>
</li>
</ul>
<p>A filter chain can consist of up to 4 filters, and cannot be empty. The last
filter in the chain must be a compression filter, and any other filters must be
delta or BCJ filters.</p>
<p>Compression filters support the following options (specified as additional
entries in the dictionary representing the filter):</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">preset</span></code>: A compression preset to use as a source of default values for
options that are not specified explicitly.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dict_size</span></code>: Dictionary size in bytes. This should be between 4 KiB and
1.5 GiB (inclusive).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">lc</span></code>: Number of literal context bits.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">lp</span></code>: Number of literal position bits. The sum <code class="docutils literal notranslate"><span class="pre">lc</span> <span class="pre">+</span> <span class="pre">lp</span></code> must be at
most 4.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pb</span></code>: Number of position bits; must be at most 4.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mode</span></code>: <code class="xref py py-const docutils literal notranslate"><span class="pre">MODE_FAST</span></code> or <code class="xref py py-const docutils literal notranslate"><span class="pre">MODE_NORMAL</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">nice_len</span></code>: What should be considered a “nice length” for a match.
This should be 273 or less.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mf</span></code>: What match finder to use – <code class="xref py py-const docutils literal notranslate"><span class="pre">MF_HC3</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">MF_HC4</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">MF_BT2</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">MF_BT3</span></code>, or <code class="xref py py-const docutils literal notranslate"><span class="pre">MF_BT4</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">depth</span></code>: Maximum search depth used by match finder. 0 (default) means to
select automatically based on other filter options.</p></li>
</ul>
<p>The delta filter stores the differences between bytes, producing more repetitive
input for the compressor in certain circumstances. It supports one option,
<code class="docutils literal notranslate"><span class="pre">dist</span></code>. This indicates the distance between bytes to be subtracted. The
default is 1, i.e. take the differences between adjacent bytes.</p>
<p>The BCJ filters are intended to be applied to machine code. They convert
relative branches, calls and jumps in the code to use absolute addressing, with
the aim of increasing the redundancy that can be exploited by the compressor.
These filters support one option, <code class="docutils literal notranslate"><span class="pre">start_offset</span></code>. This specifies the address
that should be mapped to the beginning of the input data. The default is 0.</p>
</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>Reading in a compressed file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">lzma</span>
<span class="k">with</span> <span class="n">lzma</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;file.xz&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">file_content</span> <span class="o">=</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
</pre></div>
</div>
<p>Creating a compressed file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">lzma</span>
<span class="n">data</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot;Insert Data Here&quot;</span>
<span class="k">with</span> <span class="n">lzma</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;file.xz&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
</pre></div>
</div>
<p>Compressing data in memory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">lzma</span>
<span class="n">data_in</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot;Insert Data Here&quot;</span>
<span class="n">data_out</span> <span class="o">=</span> <span class="n">lzma</span><span class="o">.</span><span class="n">compress</span><span class="p">(</span><span class="n">data_in</span><span class="p">)</span>
</pre></div>
</div>
<p>Incremental compression:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">lzma</span>
<span class="n">lzc</span> <span class="o">=</span> <span class="n">lzma</span><span class="o">.</span><span class="n">LZMACompressor</span><span class="p">()</span>
<span class="n">out1</span> <span class="o">=</span> <span class="n">lzc</span><span class="o">.</span><span class="n">compress</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Some data</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">out2</span> <span class="o">=</span> <span class="n">lzc</span><span class="o">.</span><span class="n">compress</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Another piece of data</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">out3</span> <span class="o">=</span> <span class="n">lzc</span><span class="o">.</span><span class="n">compress</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Even more data</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">out4</span> <span class="o">=</span> <span class="n">lzc</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
<span class="c1"># Concatenate all the partial results:</span>
<span class="n">result</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="n">out1</span><span class="p">,</span> <span class="n">out2</span><span class="p">,</span> <span class="n">out3</span><span class="p">,</span> <span class="n">out4</span><span class="p">])</span>
</pre></div>
</div>
<p>Writing compressed data to an already-open file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">lzma</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;file.xz&quot;</span><span class="p">,</span> <span class="s2">&quot;wb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;This data will not be compressed</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">with</span> <span class="n">lzma</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">lzf</span><span class="p">:</span>
        <span class="n">lzf</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;This *will* be compressed</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;Not compressed</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Creating a compressed file using a custom filter chain:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">lzma</span>
<span class="n">my_filters</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">{</span><span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="n">lzma</span><span class="o">.</span><span class="n">FILTER_DELTA</span><span class="p">,</span> <span class="s2">&quot;dist&quot;</span><span class="p">:</span> <span class="mi">5</span><span class="p">},</span>
    <span class="p">{</span><span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="n">lzma</span><span class="o">.</span><span class="n">FILTER_LZMA2</span><span class="p">,</span> <span class="s2">&quot;preset&quot;</span><span class="p">:</span> <span class="mi">7</span> <span class="o">|</span> <span class="n">lzma</span><span class="o">.</span><span class="n">PRESET_EXTREME</span><span class="p">},</span>
<span class="p">]</span>
<span class="k">with</span> <span class="n">lzma</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;file.xz&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">,</span> <span class="n">filters</span><span class="o">=</span><span class="n">my_filters</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;blah blah blah&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code> — Compression using the LZMA algorithm</a><ul>
<li><a class="reference internal" href="#reading-and-writing-compressed-files">Reading and writing compressed files</a></li>
<li><a class="reference internal" href="#compressing-and-decompressing-data-in-memory">Compressing and decompressing data in memory</a></li>
<li><a class="reference internal" href="#miscellaneous">Miscellaneous</a></li>
<li><a class="reference internal" href="#specifying-custom-filter-chains">Specifying custom filter chains</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="bz2.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code> — Support for <strong class="program">bzip2</strong> compression</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="zipfile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/lzma.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="zipfile.html" title="zipfile — Work with ZIP archives"
             >next</a> |</li>
        <li class="right" >
          <a href="bz2.html" title="bz2 — Support for bzip2 compression"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" >Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code> — Compression using the LZMA algorithm</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>