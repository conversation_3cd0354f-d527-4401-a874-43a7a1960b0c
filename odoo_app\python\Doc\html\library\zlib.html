<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="zlib — Compression compatible with gzip" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/zlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="For applications that require data compression, the functions in this module allow compression and decompression, using the zlib library. The zlib library has its own home page at https://www.zlib...." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="For applications that require data compression, the functions in this module allow compression and decompression, using the zlib library. The zlib library has its own home page at https://www.zlib...." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>zlib — Compression compatible with gzip &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="gzip — Support for gzip files" href="gzip.html" />
    <link rel="prev" title="Data Compression and Archiving" href="archiving.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/zlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="archiving.html"
                          title="previous chapter">Data Compression and Archiving</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gzip.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code> — Support for <strong class="program">gzip</strong> files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/zlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gzip.html" title="gzip — Support for gzip files"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="archiving.html" title="Data Compression and Archiving"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" accesskey="U">Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code> — Compression compatible with <strong class="program">gzip</strong></a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-zlib">
<span id="zlib-compression-compatible-with-gzip"></span><h1><a class="reference internal" href="#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> — Compression compatible with <strong class="program">gzip</strong><a class="headerlink" href="#module-zlib" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>For applications that require data compression, the functions in this module
allow compression and decompression, using the zlib library. The zlib library
has its own home page at <a class="reference external" href="https://www.zlib.net">https://www.zlib.net</a>.   There are known
incompatibilities between the Python module and versions of the zlib library
earlier than 1.1.3; 1.1.3 has a <a class="reference external" href="https://zlib.net/zlib_faq.html#faq33">security vulnerability</a>, so we recommend using
1.1.4 or later.</p>
<p>zlib’s functions have many options and often need to be used in a particular
order.  This documentation doesn’t attempt to cover all of the permutations;
consult the zlib manual at <a class="reference external" href="http://www.zlib.net/manual.html">http://www.zlib.net/manual.html</a> for authoritative
information.</p>
<p>For reading and writing <code class="docutils literal notranslate"><span class="pre">.gz</span></code> files see the <a class="reference internal" href="gzip.html#module-gzip" title="gzip: Interfaces for gzip compression and decompression using file objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code></a> module.</p>
<p>The available exception and functions in this module are:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="zlib.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#zlib.error" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised on compression and decompression errors.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zlib.adler32">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">adler32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.adler32" title="Link to this definition">¶</a></dt>
<dd><p>Computes an Adler-32 checksum of <em>data</em>.  (An Adler-32 checksum is almost as
reliable as a CRC32 but can be computed much more quickly.)  The result
is an unsigned 32-bit integer.  If <em>value</em> is present, it is used as
the starting value of the checksum; otherwise, a default value of 1
is used.  Passing in <em>value</em> allows computing a running checksum over the
concatenation of several inputs.  The algorithm is not cryptographically
strong, and should not be used for authentication or digital signatures.  Since
the algorithm is designed for use as a checksum algorithm, it is not suitable
for use as a general hash algorithm.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.0: </span>The result is always unsigned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zlib.compress">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">compress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">wbits</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">MAX_WBITS</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zlib.compress" title="Link to this definition">¶</a></dt>
<dd><p>Compresses the bytes in <em>data</em>, returning a bytes object containing compressed data.
<em>level</em> is an integer from <code class="docutils literal notranslate"><span class="pre">0</span></code> to <code class="docutils literal notranslate"><span class="pre">9</span></code> or <code class="docutils literal notranslate"><span class="pre">-1</span></code> controlling the level of compression;
<code class="docutils literal notranslate"><span class="pre">1</span></code> (Z_BEST_SPEED) is fastest and produces the least compression, <code class="docutils literal notranslate"><span class="pre">9</span></code> (Z_BEST_COMPRESSION)
is slowest and produces the most.  <code class="docutils literal notranslate"><span class="pre">0</span></code> (Z_NO_COMPRESSION) is no compression.
The default value is <code class="docutils literal notranslate"><span class="pre">-1</span></code> (Z_DEFAULT_COMPRESSION).  Z_DEFAULT_COMPRESSION represents a default
compromise between speed and compression (currently equivalent to level 6).</p>
<p id="compress-wbits">The <em>wbits</em> argument controls the size of the history buffer (or the
“window size”) used when compressing data, and whether a header and
trailer is included in the output.  It can take several ranges of values,
defaulting to <code class="docutils literal notranslate"><span class="pre">15</span></code> (MAX_WBITS):</p>
<ul class="simple">
<li><p>+9 to +15: The base-two logarithm of the window size, which
therefore ranges between 512 and 32768.  Larger values produce
better compression at the expense of greater memory usage.  The
resulting output will include a zlib-specific header and trailer.</p></li>
<li><p>−9 to −15: Uses the absolute value of <em>wbits</em> as the
window size logarithm, while producing a raw output stream with no
header or trailing checksum.</p></li>
<li><p>+25 to +31 = 16 + (9 to 15): Uses the low 4 bits of the value as the
window size logarithm, while including a basic <strong class="program">gzip</strong> header
and trailing checksum in the output.</p></li>
</ul>
<p>Raises the <a class="reference internal" href="#zlib.error" title="zlib.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error</span></code></a> exception if any error occurs.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>level</em> can now be used as a keyword parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <em>wbits</em> parameter is now available to set window bits and
compression type.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zlib.compressobj">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">compressobj</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level=-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method=DEFLATED</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">wbits=MAX_WBITS</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">memLevel=DEF_MEM_LEVEL</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strategy=Z_DEFAULT_STRATEGY</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">zdict</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.compressobj" title="Link to this definition">¶</a></dt>
<dd><p>Returns a compression object, to be used for compressing data streams that won’t
fit into memory at once.</p>
<p><em>level</em> is the compression level – an integer from <code class="docutils literal notranslate"><span class="pre">0</span></code> to <code class="docutils literal notranslate"><span class="pre">9</span></code> or <code class="docutils literal notranslate"><span class="pre">-1</span></code>.
A value of <code class="docutils literal notranslate"><span class="pre">1</span></code> (Z_BEST_SPEED) is fastest and produces the least compression,
while a value of <code class="docutils literal notranslate"><span class="pre">9</span></code> (Z_BEST_COMPRESSION) is slowest and produces the most.
<code class="docutils literal notranslate"><span class="pre">0</span></code> (Z_NO_COMPRESSION) is no compression.  The default value is <code class="docutils literal notranslate"><span class="pre">-1</span></code> (Z_DEFAULT_COMPRESSION).
Z_DEFAULT_COMPRESSION represents a default compromise between speed and compression
(currently equivalent to level 6).</p>
<p><em>method</em> is the compression algorithm. Currently, the only supported value is
<code class="xref py py-const docutils literal notranslate"><span class="pre">DEFLATED</span></code>.</p>
<p>The <em>wbits</em> parameter controls the size of the history buffer (or the
“window size”), and what header and trailer format will be used. It has
the same meaning as <a class="reference external" href="#compress-wbits">described for compress()</a>.</p>
<p>The <em>memLevel</em> argument controls the amount of memory used for the
internal compression state. Valid values range from <code class="docutils literal notranslate"><span class="pre">1</span></code> to <code class="docutils literal notranslate"><span class="pre">9</span></code>.
Higher values use more memory, but are faster and produce smaller output.</p>
<p><em>strategy</em> is used to tune the compression algorithm. Possible values are
<code class="xref py py-const docutils literal notranslate"><span class="pre">Z_DEFAULT_STRATEGY</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FILTERED</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_HUFFMAN_ONLY</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">Z_RLE</span></code> (zlib 1.2.0.1) and <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FIXED</span></code> (zlib 1.2.2.2).</p>
<p><em>zdict</em> is a predefined compression dictionary. This is a sequence of bytes
(such as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object) containing subsequences that are expected
to occur frequently in the data that is to be compressed. Those subsequences
that are expected to be most common should come at the end of the dictionary.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>zdict</em> parameter and keyword argument support.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zlib.crc32">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">crc32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.crc32" title="Link to this definition">¶</a></dt>
<dd><p id="index-0">Computes a CRC (Cyclic Redundancy Check) checksum of <em>data</em>. The
result is an unsigned 32-bit integer. If <em>value</em> is present, it is used
as the starting value of the checksum; otherwise, a default value of 0
is used.  Passing in <em>value</em> allows computing a running checksum over the
concatenation of several inputs.  The algorithm is not cryptographically
strong, and should not be used for authentication or digital signatures.  Since
the algorithm is designed for use as a checksum algorithm, it is not suitable
for use as a general hash algorithm.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.0: </span>The result is always unsigned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zlib.decompress">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">decompress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">wbits</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">MAX_WBITS</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bufsize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEF_BUF_SIZE</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zlib.decompress" title="Link to this definition">¶</a></dt>
<dd><p>Decompresses the bytes in <em>data</em>, returning a bytes object containing the
uncompressed data.  The <em>wbits</em> parameter depends on
the format of <em>data</em>, and is discussed further below.
If <em>bufsize</em> is given, it is used as the initial size of the output
buffer.  Raises the <a class="reference internal" href="#zlib.error" title="zlib.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error</span></code></a> exception if any error occurs.</p>
<p id="decompress-wbits">The <em>wbits</em> parameter controls the size of the history buffer
(or “window size”), and what header and trailer format is expected.
It is similar to the parameter for <a class="reference internal" href="#zlib.compressobj" title="zlib.compressobj"><code class="xref py py-func docutils literal notranslate"><span class="pre">compressobj()</span></code></a>, but accepts
more ranges of values:</p>
<ul class="simple">
<li><p>+8 to +15: The base-two logarithm of the window size.  The input
must include a zlib header and trailer.</p></li>
<li><p>0: Automatically determine the window size from the zlib header.
Only supported since zlib *******.</p></li>
<li><p>−8 to −15: Uses the absolute value of <em>wbits</em> as the window size
logarithm.  The input must be a raw stream with no header or trailer.</p></li>
<li><p>+24 to +31 = 16 + (8 to 15): Uses the low 4 bits of the value as
the window size logarithm.  The input must include a gzip header and
trailer.</p></li>
<li><p>+40 to +47 = 32 + (8 to 15): Uses the low 4 bits of the value as
the window size logarithm, and automatically accepts either
the zlib or gzip format.</p></li>
</ul>
<p>When decompressing a stream, the window size must not be smaller
than the size originally used to compress the stream; using a too-small
value may result in an <a class="reference internal" href="#zlib.error" title="zlib.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">error</span></code></a> exception. The default <em>wbits</em> value
corresponds to the largest window size and requires a zlib header and
trailer to be included.</p>
<p><em>bufsize</em> is the initial size of the buffer used to hold decompressed data.  If
more space is required, the buffer size will be increased as needed, so you
don’t have to get this value exactly right; tuning it will only save a few calls
to <code class="xref c c-func docutils literal notranslate"><span class="pre">malloc()</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>wbits</em> and <em>bufsize</em> can be used as keyword arguments.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zlib.decompressobj">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">decompressobj</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">wbits=MAX_WBITS</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">zdict</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.decompressobj" title="Link to this definition">¶</a></dt>
<dd><p>Returns a decompression object, to be used for decompressing data streams that
won’t fit into memory at once.</p>
<p>The <em>wbits</em> parameter controls the size of the history buffer (or the
“window size”), and what header and trailer format is expected.  It has
the same meaning as <a class="reference external" href="#decompress-wbits">described for decompress()</a>.</p>
<p>The <em>zdict</em> parameter specifies a predefined compression dictionary. If
provided, this must be the same dictionary as was used by the compressor that
produced the data that is to be decompressed.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <em>zdict</em> is a mutable object (such as a <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>), you must not
modify its contents between the call to <a class="reference internal" href="#zlib.decompressobj" title="zlib.decompressobj"><code class="xref py py-func docutils literal notranslate"><span class="pre">decompressobj()</span></code></a> and the first
call to the decompressor’s <code class="docutils literal notranslate"><span class="pre">decompress()</span></code> method.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>zdict</em> parameter.</p>
</div>
</dd></dl>

<p>Compression objects support the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="zlib.Compress.compress">
<span class="sig-prename descclassname"><span class="pre">Compress.</span></span><span class="sig-name descname"><span class="pre">compress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zlib.Compress.compress" title="Link to this definition">¶</a></dt>
<dd><p>Compress <em>data</em>, returning a bytes object containing compressed data for at least
part of the data in <em>data</em>.  This data should be concatenated to the output
produced by any preceding calls to the <a class="reference internal" href="#zlib.compress" title="zlib.compress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">compress()</span></code></a> method.  Some input may
be kept in internal buffers for later processing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zlib.Compress.flush">
<span class="sig-prename descclassname"><span class="pre">Compress.</span></span><span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.Compress.flush" title="Link to this definition">¶</a></dt>
<dd><p>All pending input is processed, and a bytes object containing the remaining compressed
output is returned.  <em>mode</em> can be selected from the constants
<code class="xref py py-const docutils literal notranslate"><span class="pre">Z_NO_FLUSH</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_PARTIAL_FLUSH</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_SYNC_FLUSH</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FULL_FLUSH</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_BLOCK</span></code> (zlib 1.2.3.4), or <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FINISH</span></code>,
defaulting to <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FINISH</span></code>.  Except <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FINISH</span></code>, all constants
allow compressing further bytestrings of data, while <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FINISH</span></code> finishes the
compressed stream and prevents compressing any more data.  After calling <a class="reference internal" href="#zlib.Compress.flush" title="zlib.Compress.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a>
with <em>mode</em> set to <code class="xref py py-const docutils literal notranslate"><span class="pre">Z_FINISH</span></code>, the <a class="reference internal" href="#zlib.compress" title="zlib.compress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">compress()</span></code></a> method cannot be called again;
the only realistic action is to delete the object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zlib.Compress.copy">
<span class="sig-prename descclassname"><span class="pre">Compress.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.Compress.copy" title="Link to this definition">¶</a></dt>
<dd><p>Returns a copy of the compression object.  This can be used to efficiently
compress a set of data that share a common initial prefix.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added <a class="reference internal" href="copy.html#copy.copy" title="copy.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> and <a class="reference internal" href="copy.html#copy.deepcopy" title="copy.deepcopy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.deepcopy()</span></code></a> support to compression
objects.</p>
</div>
<p>Decompression objects support the following methods and attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="zlib.Decompress.unused_data">
<span class="sig-prename descclassname"><span class="pre">Decompress.</span></span><span class="sig-name descname"><span class="pre">unused_data</span></span><a class="headerlink" href="#zlib.Decompress.unused_data" title="Link to this definition">¶</a></dt>
<dd><p>A bytes object which contains any bytes past the end of the compressed data. That is,
this remains <code class="docutils literal notranslate"><span class="pre">b&quot;&quot;</span></code> until the last byte that contains compression data is
available.  If the whole bytestring turned out to contain compressed data, this is
<code class="docutils literal notranslate"><span class="pre">b&quot;&quot;</span></code>, an empty bytes object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zlib.Decompress.unconsumed_tail">
<span class="sig-prename descclassname"><span class="pre">Decompress.</span></span><span class="sig-name descname"><span class="pre">unconsumed_tail</span></span><a class="headerlink" href="#zlib.Decompress.unconsumed_tail" title="Link to this definition">¶</a></dt>
<dd><p>A bytes object that contains any data that was not consumed by the last
<a class="reference internal" href="#zlib.decompress" title="zlib.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> call because it exceeded the limit for the uncompressed data
buffer.  This data has not yet been seen by the zlib machinery, so you must feed
it (possibly with further data concatenated to it) back to a subsequent
<a class="reference internal" href="#zlib.decompress" title="zlib.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> method call in order to get correct output.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zlib.Decompress.eof">
<span class="sig-prename descclassname"><span class="pre">Decompress.</span></span><span class="sig-name descname"><span class="pre">eof</span></span><a class="headerlink" href="#zlib.Decompress.eof" title="Link to this definition">¶</a></dt>
<dd><p>A boolean indicating whether the end of the compressed data stream has been
reached.</p>
<p>This makes it possible to distinguish between a properly formed compressed
stream, and an incomplete or truncated one.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zlib.Decompress.decompress">
<span class="sig-prename descclassname"><span class="pre">Decompress.</span></span><span class="sig-name descname"><span class="pre">decompress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zlib.Decompress.decompress" title="Link to this definition">¶</a></dt>
<dd><p>Decompress <em>data</em>, returning a bytes object containing the uncompressed data
corresponding to at least part of the data in <em>string</em>.  This data should be
concatenated to the output produced by any preceding calls to the
<a class="reference internal" href="#zlib.decompress" title="zlib.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> method.  Some of the input data may be preserved in internal
buffers for later processing.</p>
<p>If the optional parameter <em>max_length</em> is non-zero then the return value will be
no longer than <em>max_length</em>. This may mean that not all of the compressed input
can be processed; and unconsumed data will be stored in the attribute
<a class="reference internal" href="#zlib.Decompress.unconsumed_tail" title="zlib.Decompress.unconsumed_tail"><code class="xref py py-attr docutils literal notranslate"><span class="pre">unconsumed_tail</span></code></a>. This bytestring must be passed to a subsequent call to
<a class="reference internal" href="#zlib.decompress" title="zlib.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> if decompression is to continue.  If <em>max_length</em> is zero
then the whole input is decompressed, and <a class="reference internal" href="#zlib.Decompress.unconsumed_tail" title="zlib.Decompress.unconsumed_tail"><code class="xref py py-attr docutils literal notranslate"><span class="pre">unconsumed_tail</span></code></a> is empty.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>max_length</em> can be used as a keyword argument.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zlib.Decompress.flush">
<span class="sig-prename descclassname"><span class="pre">Decompress.</span></span><span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.Decompress.flush" title="Link to this definition">¶</a></dt>
<dd><p>All pending input is processed, and a bytes object containing the remaining
uncompressed output is returned.  After calling <a class="reference internal" href="#zlib.Decompress.flush" title="zlib.Decompress.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a>, the
<a class="reference internal" href="#zlib.decompress" title="zlib.decompress"><code class="xref py py-meth docutils literal notranslate"><span class="pre">decompress()</span></code></a> method cannot be called again; the only realistic action is
to delete the object.</p>
<p>The optional parameter <em>length</em> sets the initial size of the output buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zlib.Decompress.copy">
<span class="sig-prename descclassname"><span class="pre">Decompress.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zlib.Decompress.copy" title="Link to this definition">¶</a></dt>
<dd><p>Returns a copy of the decompression object.  This can be used to save the state
of the decompressor midway through the data stream in order to speed up random
seeks into the stream at a future point.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added <a class="reference internal" href="copy.html#copy.copy" title="copy.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> and <a class="reference internal" href="copy.html#copy.deepcopy" title="copy.deepcopy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.deepcopy()</span></code></a> support to decompression
objects.</p>
</div>
<p>Information about the version of the zlib library in use is available through
the following constants:</p>
<dl class="py data">
<dt class="sig sig-object py" id="zlib.ZLIB_VERSION">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">ZLIB_VERSION</span></span><a class="headerlink" href="#zlib.ZLIB_VERSION" title="Link to this definition">¶</a></dt>
<dd><p>The version string of the zlib library that was used for building the module.
This may be different from the zlib library actually used at runtime, which
is available as <a class="reference internal" href="#zlib.ZLIB_RUNTIME_VERSION" title="zlib.ZLIB_RUNTIME_VERSION"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZLIB_RUNTIME_VERSION</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zlib.ZLIB_RUNTIME_VERSION">
<span class="sig-prename descclassname"><span class="pre">zlib.</span></span><span class="sig-name descname"><span class="pre">ZLIB_RUNTIME_VERSION</span></span><a class="headerlink" href="#zlib.ZLIB_RUNTIME_VERSION" title="Link to this definition">¶</a></dt>
<dd><p>The version string of the zlib library actually loaded by the interpreter.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="gzip.html#module-gzip" title="gzip: Interfaces for gzip compression and decompression using file objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code></a></dt><dd><p>Reading and writing <strong class="program">gzip</strong>-format files.</p>
</dd>
<dt><a class="reference external" href="http://www.zlib.net">http://www.zlib.net</a></dt><dd><p>The zlib library home page.</p>
</dd>
<dt><a class="reference external" href="http://www.zlib.net/manual.html">http://www.zlib.net/manual.html</a></dt><dd><p>The zlib manual explains  the semantics and usage of the library’s many
functions.</p>
</dd>
</dl>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="archiving.html"
                          title="previous chapter">Data Compression and Archiving</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gzip.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code> — Support for <strong class="program">gzip</strong> files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/zlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gzip.html" title="gzip — Support for gzip files"
             >next</a> |</li>
        <li class="right" >
          <a href="archiving.html" title="Data Compression and Archiving"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" >Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code> — Compression compatible with <strong class="program">gzip</strong></a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>