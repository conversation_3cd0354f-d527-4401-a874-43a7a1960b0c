# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* maintenance
# 
# Translators:
# <PERSON><PERSON>, 2023
# Sarah Park, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Category:</b>"
msgstr "<b>범주 :</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Model Number:</b>"
msgstr "<b>모델 번호 :</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Request to:</b>"
msgstr "<b>요청 :</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Serial Number:</b>"
msgstr "<b>일련번호 :</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"badge text-bg-warning float-end\">Canceled</span>"
msgstr "<span class=\"badge text-bg-warning float-end\">취쇠됨</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<span class=\"badge text-bg-warning float-end\">Cancelled</span>"
msgstr "<span class=\"badge text-bg-warning float-end\">취소됨</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"ml8\">hours</span>"
msgstr "<span class=\"ml8\">시간</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Reporting</span>"
msgstr "<span>보고</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Requests</span>"
msgstr "<span>요청</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Equipment:</strong>"
msgstr "<strong>장비:</strong>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Maintenance:</strong>"
msgstr "<strong>유지보수 :</strong>"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "이 별칭에 대한 새 레코드를 만들 때 기본값을 제공하도록 평가되는 Python 사전입니다."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer3
#: model:maintenance.equipment,name:maintenance.equipment_computer5
msgid "Acer Laptop"
msgstr "Acer Laptop"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__active
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__active
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Active"
msgstr "활성화"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_ids
msgid "Activities"
msgstr "활동"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.mail_activity_type_action_config_maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_menu_config_activity_type
msgid "Activity Types"
msgstr "활동 유형"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid "Add a new equipment"
msgstr "새 장비 추가"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_category_action
msgid "Add a new equipment category"
msgstr "새 장비 카테고리 추가"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid "Add a new maintenance request"
msgstr "신규 유지보수 요청 추가"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_stage_action
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_dashboard_action
msgid "Add a new stage in the maintenance request"
msgstr "신규 유지보수 요청 단계 추가"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_team_action_settings
msgid "Add a team in the maintenance request"
msgstr "유지보수 요청에 팀 추가"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_id
msgid "Alias"
msgstr "별칭"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "별칭 연락처 보안"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain_id
msgid "Alias Domain"
msgstr "별칭 도메인"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Alias Domain Name"
msgstr "별칭 도메인 이름"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_full_name
msgid "Alias Email"
msgstr "이메일 별칭"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_name
msgid "Alias Name"
msgstr "별칭 이름"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias Status"
msgstr "별칭 상태"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias status assessed on the last message received."
msgstr "최근 받은메일에서 확인한 별칭 상태입니다."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_model_id
msgid "Aliased Model"
msgstr "별칭 모델"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "All"
msgstr "전체"

#. module: maintenance
#: model:ir.model.constraint,message:maintenance.constraint_maintenance_equipment_serial_no
msgid "Another asset already exists with this serial number!"
msgstr "이 일련번호가 있는 자산이 이미 있습니다!"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__archive
msgid "Archive"
msgstr "보관"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Archived"
msgstr "보관됨"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Assign To User"
msgstr "사용자에게 할당"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Assigned"
msgstr "배정됨"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "할당일"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Assigned to"
msgstr "담당자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Blocked"
msgstr "거부됨"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Cancel"
msgstr "취소"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Cancelled"
msgstr "취소됨"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__category_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Category"
msgstr "카테고리"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
msgid "Category Name"
msgstr "범주명"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__close_date
msgid "Close Date"
msgstr "마감일"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__color
msgid "Color Index"
msgstr "색상표"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__note
msgid "Comments"
msgstr "의견"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__company_id
msgid "Company"
msgstr "회사"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr "최근 고장 일자 + MTBF로 계산됨"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_computer
msgid "Computers"
msgstr "컴퓨터"

#. module: maintenance
#: model:ir.model,name:maintenance.model_res_config_settings
msgid "Config Settings"
msgstr "환경설정"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_configuration
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Configuration"
msgstr "설정"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__corrective
msgid "Corrective"
msgstr "시정 조치"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__cost
msgid "Cost"
msgstr "비용"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr "사용자 지정 작업 계획표 서식 만들기"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Created By"
msgstr "작성자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_uid
msgid "Created by"
msgstr "작성자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "사용자가 작성함"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_date
msgid "Created on"
msgstr "작성일자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_open_count
msgid "Current Maintenance"
msgstr "현재 유지보수 단계"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "고객 반송 메시지"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_res_config_settings__module_maintenance_worksheet
msgid "Custom Maintenance Worksheets"
msgstr "사용자 지정 유지관리 워크시트"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Custom Worksheets"
msgstr "사용자 지정 작업 계획표"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_dashboard
msgid "Dashboard"
msgstr "현황판"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__request_date
msgid "Date requested for the maintenance to happen"
msgstr "유지보수 요청 일자"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__schedule_date
msgid ""
"Date the maintenance team plans the maintenance.  It should not differ much "
"from the Request Date. "
msgstr "유지보수 팀이 유지 관리 계획을 수립한 날짜입니다. 요청 받은 날짜와 크게 다르지 않게 설정해야 합니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__close_date
msgid "Date the maintenance was finished. "
msgstr "유지보수가 완료된 날짜."

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__day
msgid "Days"
msgstr "일"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_defaults
msgid "Default Values"
msgstr "기본값"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Delete"
msgstr "삭제"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__description
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Description"
msgstr "설명"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__display_name
msgid "Display Name"
msgstr "표시명"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Done"
msgstr "완료"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__duration
msgid "Duration"
msgstr "소요 시간"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__duration
msgid "Duration in hours."
msgstr "시간으로 된 기간"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Edit..."
msgstr "편집..."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__effective_date
msgid "Effective Date"
msgstr "유효 날짜"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_email
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Email Alias"
msgstr "이메일 별칭"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_id
msgid ""
"Email alias for this equipment category. New emails will automatically "
"create a new equipment under this category."
msgstr "이 장비 카테고리의 이메일 별칭입니다. 이메일이 수신되면 이 카테고리에서 자동으로 새 장비를 생성합니다."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__email_cc
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Email cc"
msgstr "이메일 참조"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "이메일 도메인 예: '<EMAIL>'의 'example.com'"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_until
msgid "End Date"
msgstr "종료일"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action_from_category_form
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__equipment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__equipment_ids
#: model:ir.ui.menu,name:maintenance.menu_equipment_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Equipment"
msgstr "장비"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_cat_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_mat_assign
msgid "Equipment Assigned"
msgstr "장비 할당됨"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_category_action
#: model:ir.ui.menu,name:maintenance.menu_maintenance_cat
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Equipment Categories"
msgstr "장비 카테고리"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__category_id
msgid "Equipment Category"
msgstr "장비 카테고리"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_count
msgid "Equipment Count"
msgstr "장비 수"

#. module: maintenance
#: model:res.groups,name:maintenance.group_equipment_manager
msgid "Equipment Manager"
msgstr "장비 관리"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__name
msgid "Equipment Name"
msgstr "장비명"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Estimated Next Failure"
msgstr "다음 고장 예상 일자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr "다음 고장이 발생할 때까지의 예상 시간 (일)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__expected_mtbf
msgid "Expected MTBF"
msgstr "예상 MTBF"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__expected_mtbf
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Expected Mean Time Between Failure"
msgstr "예상 평균 고장 발생 간격 시간"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__fold
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__fold
msgid "Folded in Maintenance Pipe"
msgstr "유지보수 절차에서 접기"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr "유지보수 요청과 관련된 절차에 따르며 공동 작업자와 의견을 교환합니다."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예: fa-tasks"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__forever
msgid "Forever"
msgstr "영원히"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Future Activities"
msgstr "향후 활동"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_google_slide
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__google_slide
msgid "Google Slide"
msgstr "구글 슬라이드"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Google Slide Link"
msgstr "구글 슬라이드 링크"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Group by..."
msgstr "그룹별..."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_printer1
msgid "HP Inkjet printer"
msgstr "HP Inkjet printer"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer11
#: model:maintenance.equipment,name:maintenance.equipment_computer9
msgid "HP Laptop"
msgstr "HP Laptop"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__3
msgid "High"
msgstr "높음"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "High-priority"
msgstr "높은 우선순위"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__id
msgid "ID"
msgstr "ID"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "별칭을 담고 있는 상위 레코드의 ID (예: 별칭을 생성한 작업을 담고 있는 프로젝트)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "설정할 경우, 권한이 없는 사용자에게 기본 메시지 대신 이 내용을 전송합니다. "

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__normal
#: model:maintenance.stage,name:maintenance.stage_1
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "In Progress"
msgstr "진행 중"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_type
msgid "Instruction"
msgstr "지침"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Instructions"
msgstr "지침"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_maintenance
msgid "Internal Maintenance"
msgstr "유지보수"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Internal Notes"
msgstr "내부 메모"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__kanban_state
msgid "Kanban State"
msgstr "칸반 상태"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Late Activities"
msgstr "지연된 활동"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Latest Failure"
msgstr "최근 고장"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__latest_failure_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__latest_failure_date
msgid "Latest Failure Date"
msgstr "최근 고장 일자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "로컬 부품 기반 입고 감지"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__location
msgid "Location"
msgstr "위치"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_losses
msgid "Losses Analysis"
msgstr "손실 분석"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__1
msgid "Low"
msgstr "낮음"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mtbf
msgid "MTBF"
msgstr "MTBF"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mttr
msgid "MTTR"
msgstr "MTTR"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_ids
#: model:ir.ui.menu,name:maintenance.menu_m_request
#: model:ir.ui.menu,name:maintenance.menu_maintenance_title
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Maintenance"
msgstr "유지보수"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_request_calendar
msgid "Maintenance Calendar"
msgstr "유지보수 일정"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_count
msgid "Maintenance Count"
msgstr "유지보수 횟수"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "유지보수 장비"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment_category
msgid "Maintenance Equipment Category"
msgstr "유지보수 장비 카테고리"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_mixin
msgid "Maintenance Maintained Item"
msgstr "유지관리 항목 관리"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_request
#: model:mail.activity.type,name:maintenance.mail_act_maintenance_request
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_request_view_activity
msgid "Maintenance Request"
msgstr "유지보수 요청"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_cat_req_created
msgid "Maintenance Request Created"
msgstr "유지보수 요청 작성함"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Maintenance Request Search"
msgstr "유지보수 요청 검색"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_tree
msgid "Maintenance Request Stage"
msgstr "유지보수 요청 단계"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_search
msgid "Maintenance Request Stages"
msgstr "유지보수 요청 단계"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_created
msgid "Maintenance Request created"
msgstr "유지보수 요청 작성함"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_cal
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_from_equipment
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_link
#: model:ir.actions.act_window,name:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model:ir.actions.act_window,name:maintenance.maintenance_request_action_reports
#: model:ir.ui.menu,name:maintenance.maintenance_request_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_request_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Maintenance Requests"
msgstr "유지보수 요청"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_stage
msgid "Maintenance Stage"
msgstr "유지보수 단계"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_stage_configuration
msgid "Maintenance Stages"
msgstr "유지보수 단계"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_team_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_tree
msgid "Maintenance Team"
msgstr "유지보수 팀"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_dashboard_action
#: model:ir.model,name:maintenance.model_maintenance_team
#: model:ir.ui.menu,name:maintenance.menu_maintenance_teams
msgid "Maintenance Teams"
msgstr "유지보수 팀"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_type
msgid "Maintenance Type"
msgstr "유지보수 유형"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time Between Failure"
msgstr "고장 발생 평균 시간"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mtbf
msgid ""
"Mean Time Between Failure, computed based on done corrective maintenances."
msgstr "수리가 완료된 유지보수를 기반으로 계산된 평균 고장 발생 시간입니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mttr
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time To Repair"
msgstr "평균 수리 시간"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_ids
msgid "Messages"
msgstr "메시지"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_metrology
msgid "Metrology"
msgstr "검교정"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__model
msgid "Model"
msgstr "모델"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_monitor
msgid "Monitors"
msgstr "모니터"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__month
msgid "Months"
msgstr "월"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__my_activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "My Equipment"
msgstr "내 장비"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "My Maintenances"
msgstr "내 유지보수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Name"
msgstr "이름"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_0
msgid "New Request"
msgstr "새 요청"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_calendar_event_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_summary
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__2
msgid "Normal"
msgstr "일반"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__note
msgid "Note"
msgstr "노트"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Notes"
msgstr "메모"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count
msgid "Number of Requests"
msgstr "요청 횟수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_block
msgid "Number of Requests Blocked"
msgstr "요청 거부 수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_date
msgid "Number of Requests Scheduled"
msgstr "예약된 요청 수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_unscheduled
msgid "Number of Requests Unscheduled"
msgstr "예약안된 요청 수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_high_priority
msgid "Number of Requests in High Priority"
msgstr "우선 순위가 높은 요청 수"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"회신하지 않은 것까지 모든 받는 메시지를 첨부할 스레드(레코드)의 선택사항 ID. 설정하면 완벽하게 새로운 레코드의 생성을 억제합니다."

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_oee
msgid "Overall Equipment Effectiveness (OEE)"
msgstr "전반적인 장비 효율성 (OEE)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__owner_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Owner"
msgstr "소유자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_pdf
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid "Parent Model"
msgstr "상위 모델"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "상위 레코드 스레드 ID"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"별칭이 있는 상위 모델입니다. 별칭 참조를 포함하는 모델은 반드시 alias_model_id(예: 프로젝트(parent_model) 및 "
"태스크(model)에서 제공하는 모델일 필요는 없습니다.)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__instruction_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr "Google 슬라이드의 URL을 붙여 넣습니다. 문서에 대한 접근 권한이 공개되어 있는지 확인하십시오."

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_phone
msgid "Phones"
msgstr "전화"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mailgateway를 사용하여 문서에 메시지를 게시하는 정책.\n"
"- 모든 사용자: 모든 사용자가 게시할 수 있습니다\n"
"- 협력사: 인증된 협력사만 게시할 수 있습니다\n"
"- 팔로워: 관련 문서의 팔로워 또는 팔로잉 중인 채널의 사용자만 게시할 수 있습니다\n"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__preventive
msgid "Preventive"
msgstr "예방 정비"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_printer
msgid "Printers"
msgstr "프린트"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__priority
msgid "Priority"
msgstr "우선 순위"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Product Information"
msgstr "제품 정보"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Ready"
msgstr "준비"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__done
msgid "Ready for next stage"
msgstr "다음 단계 대기"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Record Colour"
msgstr "기록 색상"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid "Record Thread ID"
msgstr "레코드 스레드 ID"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__recurring_maintenance
msgid "Recurrent"
msgstr "반복"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Reopen Request"
msgstr "요청 다시열기"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_3
msgid "Repaired"
msgstr "수리됨"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_interval
msgid "Repeat Every"
msgstr "계속 반복"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_unit
msgid "Repeat Unit"
msgstr "반복되는 단위"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_reports
msgid "Reporting"
msgstr "보고"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__request_ids
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Request"
msgstr "요청"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_created
msgid "Request Created"
msgstr "요청 작성함"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__request_date
msgid "Request Date"
msgstr "요청일"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__done
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__done
msgid "Request Done"
msgstr "요청 완료"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid "Request planned for %s"
msgstr "%s에 대한 계획 요청"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Requested By"
msgstr "요청자"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "Requested by:"
msgstr "요청자:"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_ids
msgid "Requests"
msgstr "요청"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__technician_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Responsible"
msgstr "담당자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_monitor1
#: model:maintenance.equipment,name:maintenance.equipment_monitor4
#: model:maintenance.equipment,name:maintenance.equipment_monitor6
msgid "Samsung Monitor 15\""
msgstr "Samsung Monitor 15\""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Scheduled"
msgstr "예약됨"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__schedule_date
msgid "Scheduled Date"
msgstr "예정일"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_4
msgid "Scrap"
msgstr "폐기"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__scrap_date
msgid "Scrap Date"
msgstr "폐기일"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Search"
msgstr "검색"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__sequence
msgid "Sequence"
msgstr "순서"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__serial_no
msgid "Serial Number"
msgstr "일련번호"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__archive
msgid ""
"Set archive to true to hide the maintenance request without deleting it."
msgstr "유지보수 요청을 삭제하지 않고 숨기려면 보관을 참으로 설정하세요."

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.action_maintenance_configuration
#: model:ir.ui.menu,name:maintenance.menu_maintenance_config
msgid "Settings"
msgstr "설정"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_software
msgid "Software"
msgstr "소프트웨어"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__stage_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Stage"
msgstr "단계"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_stage_action
msgid "Stages"
msgstr "단계"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_status
msgid "Status Changed"
msgstr "상태 변경됨"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동 기준 상태\n"
"기한 초과: 기한이 이미 지났습니다.\n"
"오늘: 활동 날짜가 오늘입니다.\n"
"예정: 향후 계획된 활동입니다."

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_status
msgid "Status changed"
msgstr "상태 변경됨"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_subcontractor
msgid "Subcontractor"
msgstr "외주업체"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__name
msgid "Subjects"
msgstr "제목"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Team"
msgstr "팀"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__member_ids
msgid "Team Members"
msgstr "팀 구성원"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__name
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "Team Name"
msgstr "팀명"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_team_action_settings
msgid "Teams"
msgstr "팀"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Technician"
msgstr "기술자"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_text
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__text
msgid "Text"
msgstr "문자"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"이 별칭에 해당하는 모델(Odoo 문서 종류)입니다. 모든 받는 메일은 기존 레코드로 기록되지 않고 이 모델의 새 레코드로 작성됩니다. "
"(예. 프로젝트 작업)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>>에 대한 이메일을 수신하도록 하려면 이메일의 별칭 이름을 적어주세요. 예: 'jobs'"

#. module: maintenance
#: model:res.groups,comment:maintenance.group_equipment_manager
msgid "The user will be able to manage equipment."
msgstr "사용자는 장비를 관리할 수 있습니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__effective_date
msgid "This date will be used to compute the Mean Time Between Failure."
msgstr "해당 날짜는 평균고장간격 계산에 사용합니다."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "To Do"
msgstr "할 일"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Today Activities"
msgstr "오늘 활동"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Top Priorities"
msgstr "최고 우선 순위"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid ""
"Track equipment and link it to an employee or department.\n"
"                You will be able to manage allocations, issues and maintenance of your equipment."
msgstr ""
"장비를 추적하고 직원 또는 부서에 연결합니다.\n"
"                    장비의 할당, 관련 이슈 추적 및 유지보수 관리가 가능합니다."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unassigned"
msgstr "미지정"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Under Maintenance"
msgstr "유지보수 진행 중"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unread Messages"
msgstr "읽지 않은 메세지"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Unscheduled"
msgstr "예약안됨"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_type
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__until
msgid "Until"
msgstr "반복 종료일"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Upload your PDF file."
msgstr "PDF 파일을 업로드하세요."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Used in location"
msgstr "사용 위치"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Vendor"
msgstr "공급업체"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_ref
msgid "Vendor Reference"
msgstr "공급업체 참조"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__0
msgid "Very Low"
msgstr "매우 낮음"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__warranty_date
msgid "Warranty Expiration Date"
msgstr "보증 유효기간"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__week
msgid "Weeks"
msgstr "주"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__year
msgid "Years"
msgstr "년"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid ""
"You cannot delete an equipment category containing equipment or maintenance "
"requests."
msgstr "장비 또는 유지 보수 요청이 있는 장비 카테고리는 삭제할 수 없습니다."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Your instructions"
msgstr "내 지침"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "days"
msgstr "일"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "e.g. Internal Maintenance"
msgstr "예. 자체 유지 보수"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "e.g. LED Monitor"
msgstr "예: LED 모니터"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. Monitors"
msgstr "예. 모니터"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "e.g. Screen not working"
msgstr "예: 화면이 작동하지 않음"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. domain.com"
msgstr "예: domain.com"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_graph
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_pivot
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_tree
msgid "maintenance Request"
msgstr "유지보수 요청"
