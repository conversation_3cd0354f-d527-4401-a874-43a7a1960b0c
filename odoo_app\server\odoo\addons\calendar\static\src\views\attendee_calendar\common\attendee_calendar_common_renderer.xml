<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="calendar.AttendeeCalendarCommonRenderer.event">
        <div class="fc-content" t-att-class="isMonth ? 'd-flex gap-1 text-truncate' : ''">
            <span t-if="!isTimeHidden and isMonth" class="d-none d-lg-inline fc-time fw-normal" t-esc="startTime" />
            <div t-attf-class="o_event_title text-truncate">
                <i t-if="isAlone" class="fa fa-exclamation-circle align-self-top me-1" data-tooltip="You're alone in this meeting"/>
                <span t-esc="title" t-attf-class="#{(attendeeStatus == 'accepted' || rawRecord.is_highlighted)  ? 'fw-bolder' : ''}"/>
                    <t t-if="!isAllDay and !isMonth and !isSmall">
                        <t t-if="24 > duration and duration > 0.5">
                            <br/><span class="fw-normal mt-1"><t t-esc="startTime"/> - <t t-esc="endTime"/></span>
                        </t>
                        <t t-else="">
                            , <t t-esc="startTime"/>
                        </t>
                    </t>
            </div>
        </div>
    </t>
</templates>
