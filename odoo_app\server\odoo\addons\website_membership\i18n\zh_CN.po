# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_membership
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "全部"

#. module: website_membership
#. odoo-python
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "所有国家"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "协会"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "关闭"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "查找商业伙伴"

#. module: website_membership
#. odoo-python
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "Free Members"
msgstr "免费成员"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Location"
msgstr "位置"

#. module: website_membership
#. odoo-python
#: code:addons/website_membership/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_membership.index
#, python-format
msgid "Members"
msgstr "成员"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Members Page"
msgstr "成员网页"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr "会籍明细行"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "找不到结果。"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "我们的会员目录"

#. module: website_membership
#: model:ir.model,name:website_membership.model_website
msgid "Website"
msgstr "网站"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "World Map"
msgstr "地界地图"
