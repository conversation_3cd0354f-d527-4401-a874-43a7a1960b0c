<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tkinter.font — Tkinter font wrapper" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tkinter.font.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tkinter/font.py The tkinter.font module provides the Font class for creating and using named fonts. The different font weights and slants are:" />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tkinter/font.py The tkinter.font module provides the Font class for creating and using named fonts. The different font weights and slants are:" />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tkinter.font — Tkinter font wrapper &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Tkinter Dialogs" href="dialog.html" />
    <link rel="prev" title="tkinter.colorchooser — Color choosing dialog" href="tkinter.colorchooser.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tkinter.font.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tkinter.colorchooser.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.colorchooser</span></code> — Color choosing dialog</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="dialog.html"
                          title="next chapter">Tkinter Dialogs</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.font.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="dialog.html" title="Tkinter Dialogs"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tkinter.colorchooser.html" title="tkinter.colorchooser — Color choosing dialog"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" accesskey="U">Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.font</span></code> — Tkinter font wrapper</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tkinter.font">
<span id="tkinter-font-tkinter-font-wrapper"></span><h1><a class="reference internal" href="#module-tkinter.font" title="tkinter.font: Tkinter font-wrapping class (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.font</span></code></a> — Tkinter font wrapper<a class="headerlink" href="#module-tkinter.font" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tkinter/font.py">Lib/tkinter/font.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-tkinter.font" title="tkinter.font: Tkinter font-wrapping class (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.font</span></code></a> module provides the <a class="reference internal" href="#tkinter.font.Font" title="tkinter.font.Font"><code class="xref py py-class docutils literal notranslate"><span class="pre">Font</span></code></a> class for creating
and using named fonts.</p>
<p>The different font weights and slants are:</p>
<dl class="py data">
<dt class="sig sig-object py" id="tkinter.font.NORMAL">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">NORMAL</span></span><a class="headerlink" href="#tkinter.font.NORMAL" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="tkinter.font.BOLD">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">BOLD</span></span><a class="headerlink" href="#tkinter.font.BOLD" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="tkinter.font.ITALIC">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">ITALIC</span></span><a class="headerlink" href="#tkinter.font.ITALIC" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="tkinter.font.ROMAN">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">ROMAN</span></span><a class="headerlink" href="#tkinter.font.ROMAN" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.font.Font">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">Font</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exists</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#tkinter.font.Font" title="tkinter.font.Font"><code class="xref py py-class docutils literal notranslate"><span class="pre">Font</span></code></a> class represents a named font. <em>Font</em> instances are given
unique names and can be specified by their family, size, and style
configuration. Named fonts are Tk’s method of creating and identifying
fonts as a single object, rather than specifying a font by its attributes
with each occurrence.</p>
<blockquote>
<div><p>arguments:</p>
<blockquote>
<div><div class="line-block">
<div class="line"><em>font</em> - font specifier tuple (family, size, options)</div>
<div class="line"><em>name</em> - unique font name</div>
<div class="line"><em>exists</em> - self points to existing named font if true</div>
</div>
</div></blockquote>
<p>additional keyword options (ignored if <em>font</em> is specified):</p>
<blockquote>
<div><div class="line-block">
<div class="line"><em>family</em> - font family i.e. Courier, Times</div>
<div class="line"><em>size</em> - font size</div>
<div class="line-block">
<div class="line">If <em>size</em> is positive it is interpreted as size in points.</div>
<div class="line">If <em>size</em> is a negative number its absolute value is treated</div>
<div class="line">as size in pixels.</div>
</div>
<div class="line"><em>weight</em> - font emphasis (NORMAL, BOLD)</div>
<div class="line"><em>slant</em> - ROMAN, ITALIC</div>
<div class="line"><em>underline</em> - font underlining (0 - none, 1 - underline)</div>
<div class="line"><em>overstrike</em> - font strikeout (0 - none, 1 - strikeout)</div>
</div>
</div></blockquote>
</div></blockquote>
<dl class="py method">
<dt class="sig sig-object py" id="tkinter.font.Font.actual">
<span class="sig-name descname"><span class="pre">actual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">option</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">displayof</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font.actual" title="Link to this definition">¶</a></dt>
<dd><p>Return the attributes of the font.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.font.Font.cget">
<span class="sig-name descname"><span class="pre">cget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">option</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font.cget" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve an attribute of the font.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.font.Font.config">
<span class="sig-name descname"><span class="pre">config</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font.config" title="Link to this definition">¶</a></dt>
<dd><p>Modify attributes of the font.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.font.Font.copy">
<span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font.copy" title="Link to this definition">¶</a></dt>
<dd><p>Return new instance of the current font.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.font.Font.measure">
<span class="sig-name descname"><span class="pre">measure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">displayof</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font.measure" title="Link to this definition">¶</a></dt>
<dd><p>Return amount of space the text would occupy on the specified display
when formatted in the current font. If no display is specified then the
main application window is assumed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.font.Font.metrics">
<span class="sig-name descname"><span class="pre">metrics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">options</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.Font.metrics" title="Link to this definition">¶</a></dt>
<dd><p>Return font-specific data.
Options include:</p>
<dl class="simple">
<dt><em>ascent</em> - distance between baseline and highest point that a</dt><dd><p>character of the font can occupy</p>
</dd>
<dt><em>descent</em> - distance between baseline and lowest point that a</dt><dd><p>character of the font can occupy</p>
</dd>
<dt><em>linespace</em> - minimum vertical separation necessary between any two</dt><dd><p>characters of the font that ensures no vertical overlap between lines.</p>
</dd>
</dl>
<p><em>fixed</em> - 1 if font is fixed-width else 0</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.font.families">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">families</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">displayof</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.families" title="Link to this definition">¶</a></dt>
<dd><p>Return the different font families.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.font.names">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">names</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.names" title="Link to this definition">¶</a></dt>
<dd><p>Return the names of defined fonts.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.font.nametofont">
<span class="sig-prename descclassname"><span class="pre">tkinter.font.</span></span><span class="sig-name descname"><span class="pre">nametofont</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">root</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.font.nametofont" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#tkinter.font.Font" title="tkinter.font.Font"><code class="xref py py-class docutils literal notranslate"><span class="pre">Font</span></code></a> representation of a tk named font.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>root</em> parameter was added.</p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tkinter.colorchooser.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.colorchooser</span></code> — Color choosing dialog</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="dialog.html"
                          title="next chapter">Tkinter Dialogs</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.font.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="dialog.html" title="Tkinter Dialogs"
             >next</a> |</li>
        <li class="right" >
          <a href="tkinter.colorchooser.html" title="tkinter.colorchooser — Color choosing dialog"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" >Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.font</span></code> — Tkinter font wrapper</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>