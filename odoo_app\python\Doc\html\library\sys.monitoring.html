<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sys.monitoring — Execution event monitoring" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sys.monitoring.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This namespace provides access to the functions and constants necessary to activate and control event monitoring. As programs execute, events occur that might be of interest to tools that monitor e..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This namespace provides access to the functions and constants necessary to activate and control event monitoring. As programs execute, events occur that might be of interest to tools that monitor e..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sys.monitoring — Execution event monitoring &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="sysconfig — Provide access to Python’s configuration information" href="sysconfig.html" />
    <link rel="prev" title="sys — System-specific parameters and functions" href="sys.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sys.monitoring.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a><ul>
<li><a class="reference internal" href="#tool-identifiers">Tool identifiers</a><ul>
<li><a class="reference internal" href="#registering-and-using-tools">Registering and using tools</a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#local-events">Local events</a></li>
<li><a class="reference internal" href="#ancillary-events">Ancillary events</a></li>
<li><a class="reference internal" href="#other-events">Other events</a></li>
<li><a class="reference internal" href="#the-stop-iteration-event">The STOP_ITERATION event</a></li>
</ul>
</li>
<li><a class="reference internal" href="#turning-events-on-and-off">Turning events on and off</a><ul>
<li><a class="reference internal" href="#setting-events-globally">Setting events globally</a></li>
<li><a class="reference internal" href="#per-code-object-events">Per code object events</a></li>
<li><a class="reference internal" href="#disabling-events">Disabling events</a></li>
</ul>
</li>
<li><a class="reference internal" href="#registering-callback-functions">Registering callback functions</a><ul>
<li><a class="reference internal" href="#callback-function-arguments">Callback function arguments</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="sys.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sysconfig.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sys.monitoring.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sysconfig.html" title="sysconfig — Provide access to Python’s configuration information"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="sys.html" title="sys — System-specific parameters and functions"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sys.monitoring">
<span id="sys-monitoring-execution-event-monitoring"></span><h1><a class="reference internal" href="#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a> — Execution event monitoring<a class="headerlink" href="#module-sys.monitoring" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<hr class="docutils" />
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a> is a namespace within the <a class="reference internal" href="sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> module,
not an independent module, so there is no need to
<code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">sys.monitoring</span></code>, simply <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">sys</span></code> and then use
<code class="docutils literal notranslate"><span class="pre">sys.monitoring</span></code>.</p>
</div>
<p>This namespace provides access to the functions and constants necessary to
activate and control event monitoring.</p>
<p>As programs execute, events occur that might be of interest to tools that
monitor execution. The <a class="reference internal" href="#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a> namespace provides means to
receive callbacks when events of interest occur.</p>
<p>The monitoring API consists of three components:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#tool-identifiers">Tool identifiers</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
<li><p><a class="reference internal" href="#callbacks"><span class="std std-ref">Callbacks</span></a></p></li>
</ul>
<section id="tool-identifiers">
<h2>Tool identifiers<a class="headerlink" href="#tool-identifiers" title="Link to this heading">¶</a></h2>
<p>A tool identifier is an integer and the associated name.
Tool identifiers are used to discourage tools from interfering with each
other and to allow multiple tools to operate at the same time.
Currently tools are completely independent and cannot be used to
monitor each other. This restriction may be lifted in the future.</p>
<p>Before registering or activating events, a tool should choose an identifier.
Identifiers are integers in the range 0 to 5 inclusive.</p>
<section id="registering-and-using-tools">
<h3>Registering and using tools<a class="headerlink" href="#registering-and-using-tools" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.use_tool_id">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">use_tool_id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.use_tool_id" title="Link to this definition">¶</a></dt>
<dd><p>Must be called before <em>tool_id</em> can be used.
<em>tool_id</em> must be in the range 0 to 5 inclusive.
Raises a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>tool_id</em> is in use.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.free_tool_id">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">free_tool_id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.free_tool_id" title="Link to this definition">¶</a></dt>
<dd><p>Should be called once a tool no longer requires <em>tool_id</em>.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#sys.monitoring.free_tool_id" title="sys.monitoring.free_tool_id"><code class="xref py py-func docutils literal notranslate"><span class="pre">free_tool_id()</span></code></a> will not disable global or local events associated
with <em>tool_id</em>, nor will it unregister any callback functions. This
function is only intended to be used to notify the VM that the
particular <em>tool_id</em> is no longer in use.</p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.get_tool">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">get_tool</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.get_tool" title="Link to this definition">¶</a></dt>
<dd><p>Returns the name of the tool if <em>tool_id</em> is in use,
otherwise it returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.
<em>tool_id</em> must be in the range 0 to 5 inclusive.</p>
</dd></dl>

<p>All IDs are treated the same by the VM with regard to events, but the
following IDs are pre-defined to make co-operation of tools easier:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">sys</span><span class="o">.</span><span class="n">monitoring</span><span class="o">.</span><span class="n">DEBUGGER_ID</span> <span class="o">=</span> <span class="mi">0</span>
<span class="n">sys</span><span class="o">.</span><span class="n">monitoring</span><span class="o">.</span><span class="n">COVERAGE_ID</span> <span class="o">=</span> <span class="mi">1</span>
<span class="n">sys</span><span class="o">.</span><span class="n">monitoring</span><span class="o">.</span><span class="n">PROFILER_ID</span> <span class="o">=</span> <span class="mi">2</span>
<span class="n">sys</span><span class="o">.</span><span class="n">monitoring</span><span class="o">.</span><span class="n">OPTIMIZER_ID</span> <span class="o">=</span> <span class="mi">5</span>
</pre></div>
</div>
<p>There is no obligation to set an ID, nor is there anything preventing a tool
from using an ID even it is already in use.
However, tools are encouraged to use a unique ID and respect other tools.</p>
</section>
</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">¶</a></h2>
<p>The following events are supported:</p>
<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-BRANCH">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">BRANCH</span></span><a class="headerlink" href="#monitoring-event-BRANCH" title="Link to this definition">¶</a></dt>
<dd><p>A conditional branch is taken (or not).</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-CALL">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">CALL</span></span><a class="headerlink" href="#monitoring-event-CALL" title="Link to this definition">¶</a></dt>
<dd><p>A call in Python code (event occurs before the call).</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-C_RAISE">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">C_RAISE</span></span><a class="headerlink" href="#monitoring-event-C_RAISE" title="Link to this definition">¶</a></dt>
<dd><p>An exception raised from any callable, except for Python functions (event occurs after the exit).</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-C_RETURN">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">C_RETURN</span></span><a class="headerlink" href="#monitoring-event-C_RETURN" title="Link to this definition">¶</a></dt>
<dd><p>Return from any callable, except for Python functions (event occurs after the return).</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-EXCEPTION_HANDLED">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">EXCEPTION_HANDLED</span></span><a class="headerlink" href="#monitoring-event-EXCEPTION_HANDLED" title="Link to this definition">¶</a></dt>
<dd><p>An exception is handled.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-INSTRUCTION">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">INSTRUCTION</span></span><a class="headerlink" href="#monitoring-event-INSTRUCTION" title="Link to this definition">¶</a></dt>
<dd><p>A VM instruction is about to be executed.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-JUMP">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">JUMP</span></span><a class="headerlink" href="#monitoring-event-JUMP" title="Link to this definition">¶</a></dt>
<dd><p>An unconditional jump in the control flow graph is made.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-LINE">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">LINE</span></span><a class="headerlink" href="#monitoring-event-LINE" title="Link to this definition">¶</a></dt>
<dd><p>An instruction is about to be executed that has a different line number from the preceding instruction.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-PY_RESUME">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">PY_RESUME</span></span><a class="headerlink" href="#monitoring-event-PY_RESUME" title="Link to this definition">¶</a></dt>
<dd><p>Resumption of a Python function (for generator and coroutine functions), except for <code class="docutils literal notranslate"><span class="pre">throw()</span></code> calls.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-PY_RETURN">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">PY_RETURN</span></span><a class="headerlink" href="#monitoring-event-PY_RETURN" title="Link to this definition">¶</a></dt>
<dd><p>Return from a Python function (occurs immediately before the return, the callee’s frame will be on the stack).</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-PY_START">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">PY_START</span></span><a class="headerlink" href="#monitoring-event-PY_START" title="Link to this definition">¶</a></dt>
<dd><p>Start of a Python function (occurs immediately after the call, the callee’s frame will be on the stack)</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-PY_THROW">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">PY_THROW</span></span><a class="headerlink" href="#monitoring-event-PY_THROW" title="Link to this definition">¶</a></dt>
<dd><p>A Python function is resumed by a <code class="docutils literal notranslate"><span class="pre">throw()</span></code> call.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-PY_UNWIND">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">PY_UNWIND</span></span><a class="headerlink" href="#monitoring-event-PY_UNWIND" title="Link to this definition">¶</a></dt>
<dd><p>Exit from a Python function during exception unwinding.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-PY_YIELD">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">PY_YIELD</span></span><a class="headerlink" href="#monitoring-event-PY_YIELD" title="Link to this definition">¶</a></dt>
<dd><p>Yield from a Python function (occurs immediately before the yield, the callee’s frame will be on the stack).</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-RAISE">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">RAISE</span></span><a class="headerlink" href="#monitoring-event-RAISE" title="Link to this definition">¶</a></dt>
<dd><p>An exception is raised, except those that cause a <a class="reference internal" href="#monitoring-event-STOP_ITERATION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code></a> event.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-RERAISE">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">RERAISE</span></span><a class="headerlink" href="#monitoring-event-RERAISE" title="Link to this definition">¶</a></dt>
<dd><p>An exception is re-raised, for example at the end of a <a class="reference internal" href="../reference/compound_stmts.html#finally"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">finally</span></code></a> block.</p>
</dd></dl>

<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-STOP_ITERATION">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">STOP_ITERATION</span></span><a class="headerlink" href="#monitoring-event-STOP_ITERATION" title="Link to this definition">¶</a></dt>
<dd><p>An artificial <a class="reference internal" href="exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> is raised; see <a class="reference internal" href="#the-stop-iteration-event">the STOP_ITERATION event</a>.</p>
</dd></dl>

<p>More events may be added in the future.</p>
<p>These events are attributes of the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring.events</span></code> namespace.
Each event is represented as a power-of-2 integer constant.
To define a set of events, simply bitwise or the individual events together.
For example, to specify both <a class="reference internal" href="#monitoring-event-PY_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RETURN</span></code></a> and <a class="reference internal" href="#monitoring-event-PY_START"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_START</span></code></a>
events, use the expression <code class="docutils literal notranslate"><span class="pre">PY_RETURN</span> <span class="pre">|</span> <span class="pre">PY_START</span></code>.</p>
<dl class="std monitoring-event">
<dt class="sig sig-object std" id="monitoring-event-NO_EVENTS">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.events.</span></span><span class="sig-name descname"><span class="pre">NO_EVENTS</span></span><a class="headerlink" href="#monitoring-event-NO_EVENTS" title="Link to this definition">¶</a></dt>
<dd><p>An alias for <code class="docutils literal notranslate"><span class="pre">0</span></code> so users can do explict comparisions like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">get_events</span><span class="p">(</span><span class="n">DEBUGGER_ID</span><span class="p">)</span> <span class="o">==</span> <span class="n">NO_EVENTS</span><span class="p">:</span>
    <span class="o">...</span>
</pre></div>
</div>
</dd></dl>

<p>Events are divided into three groups:</p>
<section id="local-events">
<h3>Local events<a class="headerlink" href="#local-events" title="Link to this heading">¶</a></h3>
<p>Local events are associated with normal execution of the program and happen
at clearly defined locations. All local events can be disabled.
The local events are:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#monitoring-event-PY_START"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_START</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-PY_RESUME"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RESUME</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-PY_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RETURN</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-PY_YIELD"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_YIELD</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-CALL"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">CALL</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-LINE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">LINE</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-INSTRUCTION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">INSTRUCTION</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-JUMP"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">JUMP</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-BRANCH"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">BRANCH</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-STOP_ITERATION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code></a></p></li>
</ul>
</section>
<section id="ancillary-events">
<h3>Ancillary events<a class="headerlink" href="#ancillary-events" title="Link to this heading">¶</a></h3>
<p>Ancillary events can be monitored like other events, but are controlled
by another event:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#monitoring-event-C_RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RAISE</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-C_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RETURN</span></code></a></p></li>
</ul>
<p>The <a class="reference internal" href="#monitoring-event-C_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RETURN</span></code></a> and <a class="reference internal" href="#monitoring-event-C_RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RAISE</span></code></a> events
are controlled by the <a class="reference internal" href="#monitoring-event-CALL"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">CALL</span></code></a> event.
<a class="reference internal" href="#monitoring-event-C_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RETURN</span></code></a> and <a class="reference internal" href="#monitoring-event-C_RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RAISE</span></code></a> events will only be seen if the
corresponding <a class="reference internal" href="#monitoring-event-CALL"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">CALL</span></code></a> event is being monitored.</p>
</section>
<section id="other-events">
<h3>Other events<a class="headerlink" href="#other-events" title="Link to this heading">¶</a></h3>
<p>Other events are not necessarily tied to a specific location in the
program and cannot be individually disabled.</p>
<p>The other events that can be monitored are:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#monitoring-event-PY_THROW"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_THROW</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-PY_UNWIND"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_UNWIND</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">RAISE</span></code></a></p></li>
<li><p><a class="reference internal" href="#monitoring-event-EXCEPTION_HANDLED"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">EXCEPTION_HANDLED</span></code></a></p></li>
</ul>
</section>
<section id="the-stop-iteration-event">
<h3>The STOP_ITERATION event<a class="headerlink" href="#the-stop-iteration-event" title="Link to this heading">¶</a></h3>
<p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0380/#use-of-stopiteration-to-return-values"><strong>PEP 380</strong></a>
specifies that a <a class="reference internal" href="exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> exception is raised when returning a value
from a generator or coroutine. However, this is a very inefficient way to
return a value, so some Python implementations, notably CPython 3.12+, do not
raise an exception unless it would be visible to other code.</p>
<p>To allow tools to monitor for real exceptions without slowing down generators
and coroutines, the <a class="reference internal" href="#monitoring-event-STOP_ITERATION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code></a> event is provided.
<a class="reference internal" href="#monitoring-event-STOP_ITERATION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code></a> can be locally disabled, unlike <a class="reference internal" href="#monitoring-event-RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">RAISE</span></code></a>.</p>
</section>
</section>
<section id="turning-events-on-and-off">
<h2>Turning events on and off<a class="headerlink" href="#turning-events-on-and-off" title="Link to this heading">¶</a></h2>
<p>In order to monitor an event, it must be turned on and a corresponding callback
must be registered.
Events can be turned on or off by setting the events either globally or
for a particular code object.</p>
<section id="setting-events-globally">
<h3>Setting events globally<a class="headerlink" href="#setting-events-globally" title="Link to this heading">¶</a></h3>
<p>Events can be controlled globally by modifying the set of events being monitored.</p>
<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.get_events">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">get_events</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#sys.monitoring.get_events" title="Link to this definition">¶</a></dt>
<dd><p>Returns the <code class="docutils literal notranslate"><span class="pre">int</span></code> representing all the active events.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.set_events">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">set_events</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_set</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.set_events" title="Link to this definition">¶</a></dt>
<dd><p>Activates all events which are set in <em>event_set</em>.
Raises a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>tool_id</em> is not in use.</p>
</dd></dl>

<p>No events are active by default.</p>
</section>
<section id="per-code-object-events">
<h3>Per code object events<a class="headerlink" href="#per-code-object-events" title="Link to this heading">¶</a></h3>
<p>Events can also be controlled on a per code object basis.</p>
<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.get_local_events">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">get_local_events</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="types.html#types.CodeType" title="types.CodeType"><span class="pre">CodeType</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#sys.monitoring.get_local_events" title="Link to this definition">¶</a></dt>
<dd><p>Returns all the local events for <em>code</em></p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.set_local_events">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">set_local_events</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="types.html#types.CodeType" title="types.CodeType"><span class="pre">CodeType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_set</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.set_local_events" title="Link to this definition">¶</a></dt>
<dd><p>Activates all the local events for <em>code</em> which are set in <em>event_set</em>.
Raises a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>tool_id</em> is not in use.</p>
</dd></dl>

<p>Local events add to global events, but do not mask them.
In other words, all global events will trigger for a code object,
regardless of the local events.</p>
</section>
<section id="disabling-events">
<h3>Disabling events<a class="headerlink" href="#disabling-events" title="Link to this heading">¶</a></h3>
<dl class="py data">
<dt class="sig sig-object py" id="sys.monitoring.DISABLE">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">DISABLE</span></span><a class="headerlink" href="#sys.monitoring.DISABLE" title="Link to this definition">¶</a></dt>
<dd><p>A special value that can be returned from a callback function to disable
events for the current code location.</p>
</dd></dl>

<p>Local events can be disabled for a specific code location by returning
<a class="reference internal" href="#sys.monitoring.DISABLE" title="sys.monitoring.DISABLE"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.monitoring.DISABLE</span></code></a> from a callback function. This does not change
which events are set, or any other code locations for the same event.</p>
<p>Disabling events for specific locations is very important for high
performance monitoring. For example, a program can be run under a
debugger with no overhead if the debugger disables all monitoring
except for a few breakpoints.</p>
<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.restart_events">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">restart_events</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.restart_events" title="Link to this definition">¶</a></dt>
<dd><p>Enable all the events that were disabled by <a class="reference internal" href="#sys.monitoring.DISABLE" title="sys.monitoring.DISABLE"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.monitoring.DISABLE</span></code></a>
for all tools.</p>
</dd></dl>

</section>
</section>
<section id="registering-callback-functions">
<span id="callbacks"></span><h2>Registering callback functions<a class="headerlink" href="#registering-callback-functions" title="Link to this heading">¶</a></h2>
<p>To register a callable for events call</p>
<dl class="py function">
<dt class="sig sig-object py" id="sys.monitoring.register_callback">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">register_callback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tool_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">event</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">func</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><span class="pre">Callable</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="collections.abc.html#collections.abc.Callable" title="collections.abc.Callable"><span class="pre">Callable</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference internal" href="constants.html#None" title="None"><span class="pre">None</span></a></span></span><a class="headerlink" href="#sys.monitoring.register_callback" title="Link to this definition">¶</a></dt>
<dd><p>Registers the callable <em>func</em> for the <em>event</em> with the given <em>tool_id</em></p>
<p>If another callback was registered for the given <em>tool_id</em> and <em>event</em>,
it is unregistered and returned.
Otherwise <a class="reference internal" href="#sys.monitoring.register_callback" title="sys.monitoring.register_callback"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_callback()</span></code></a> returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<p>Functions can be unregistered by calling
<code class="docutils literal notranslate"><span class="pre">sys.monitoring.register_callback(tool_id,</span> <span class="pre">event,</span> <span class="pre">None)</span></code>.</p>
<p>Callback functions can be registered and unregistered at any time.</p>
<p>Registering or unregistering a callback function will generate a <a class="reference internal" href="sys.html#sys.audit" title="sys.audit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.audit()</span></code></a> event.</p>
<section id="callback-function-arguments">
<h3>Callback function arguments<a class="headerlink" href="#callback-function-arguments" title="Link to this heading">¶</a></h3>
<dl class="py data">
<dt class="sig sig-object py" id="sys.monitoring.MISSING">
<span class="sig-prename descclassname"><span class="pre">sys.monitoring.</span></span><span class="sig-name descname"><span class="pre">MISSING</span></span><a class="headerlink" href="#sys.monitoring.MISSING" title="Link to this definition">¶</a></dt>
<dd><p>A special value that is passed to a callback function to indicate
that there are no arguments to the call.</p>
</dd></dl>

<p>When an active event occurs, the registered callback function is called.
Different events will provide the callback function with different arguments, as follows:</p>
<ul>
<li><p><a class="reference internal" href="#monitoring-event-PY_START"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_START</span></code></a> and <a class="reference internal" href="#monitoring-event-PY_RESUME"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RESUME</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">instruction_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
</li>
<li><p><a class="reference internal" href="#monitoring-event-PY_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RETURN</span></code></a> and <a class="reference internal" href="#monitoring-event-PY_YIELD"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_YIELD</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">instruction_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">retval</span><span class="p">:</span> <span class="nb">object</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
</li>
<li><p><a class="reference internal" href="#monitoring-event-CALL"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">CALL</span></code></a>, <a class="reference internal" href="#monitoring-event-C_RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RAISE</span></code></a> and <a class="reference internal" href="#monitoring-event-C_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RETURN</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">instruction_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">callable</span><span class="p">:</span> <span class="nb">object</span><span class="p">,</span> <span class="n">arg0</span><span class="p">:</span> <span class="nb">object</span> <span class="o">|</span> <span class="n">MISSING</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
<p>If there are no arguments, <em>arg0</em> is set to <a class="reference internal" href="#sys.monitoring.MISSING" title="sys.monitoring.MISSING"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.monitoring.MISSING</span></code></a>.</p>
</li>
<li><p><a class="reference internal" href="#monitoring-event-RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">RAISE</span></code></a>, <a class="reference internal" href="#monitoring-event-RERAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">RERAISE</span></code></a>, <a class="reference internal" href="#monitoring-event-EXCEPTION_HANDLED"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">EXCEPTION_HANDLED</span></code></a>,
<a class="reference internal" href="#monitoring-event-PY_UNWIND"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_UNWIND</span></code></a>, <a class="reference internal" href="#monitoring-event-PY_THROW"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_THROW</span></code></a> and <a class="reference internal" href="#monitoring-event-STOP_ITERATION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">instruction_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">exception</span><span class="p">:</span> <span class="ne">BaseException</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
</li>
<li><p><a class="reference internal" href="#monitoring-event-LINE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">LINE</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">line_number</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
</li>
<li><p><a class="reference internal" href="#monitoring-event-BRANCH"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">BRANCH</span></code></a> and <a class="reference internal" href="#monitoring-event-JUMP"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">JUMP</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">instruction_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">destination_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
<p>Note that the <em>destination_offset</em> is where the code will next execute.
For an untaken branch this will be the offset of the instruction following
the branch.</p>
</li>
<li><p><a class="reference internal" href="#monitoring-event-INSTRUCTION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">INSTRUCTION</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">func</span><span class="p">(</span><span class="n">code</span><span class="p">:</span> <span class="n">CodeType</span><span class="p">,</span> <span class="n">instruction_offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DISABLE</span> <span class="o">|</span> <span class="n">Any</span>
</pre></div>
</div>
</li>
</ul>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a><ul>
<li><a class="reference internal" href="#tool-identifiers">Tool identifiers</a><ul>
<li><a class="reference internal" href="#registering-and-using-tools">Registering and using tools</a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#local-events">Local events</a></li>
<li><a class="reference internal" href="#ancillary-events">Ancillary events</a></li>
<li><a class="reference internal" href="#other-events">Other events</a></li>
<li><a class="reference internal" href="#the-stop-iteration-event">The STOP_ITERATION event</a></li>
</ul>
</li>
<li><a class="reference internal" href="#turning-events-on-and-off">Turning events on and off</a><ul>
<li><a class="reference internal" href="#setting-events-globally">Setting events globally</a></li>
<li><a class="reference internal" href="#per-code-object-events">Per code object events</a></li>
<li><a class="reference internal" href="#disabling-events">Disabling events</a></li>
</ul>
</li>
<li><a class="reference internal" href="#registering-callback-functions">Registering callback functions</a><ul>
<li><a class="reference internal" href="#callback-function-arguments">Callback function arguments</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="sys.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sysconfig.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sys.monitoring.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sysconfig.html" title="sysconfig — Provide access to Python’s configuration information"
             >next</a> |</li>
        <li class="right" >
          <a href="sys.html" title="sys — System-specific parameters and functions"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>