<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="General Python FAQ" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/faq/general.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Contents: General Python FAQ- General Information- What is Python?, What is the Python Software Foundation?, Are there copyright restrictions on the use of Python?, Why was Python created in the fi..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Contents: General Python FAQ- General Information- What is Python?, What is the Python Software Foundation?, Are there copyright restrictions on the use of Python?, Why was Python created in the fi..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>General Python FAQ &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Programming FAQ" href="programming.html" />
    <link rel="prev" title="Python Frequently Asked Questions" href="index.html" />
    <link rel="canonical" href="https://docs.python.org/3/faq/general.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">General Python FAQ</a><ul>
<li><a class="reference internal" href="#general-information">General Information</a></li>
<li><a class="reference internal" href="#python-in-the-real-world">Python in the real world</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="index.html"
                          title="previous chapter">Python Frequently Asked Questions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="programming.html"
                          title="next chapter">Programming FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/general.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="programming.html" title="Programming FAQ"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="index.html" title="Python Frequently Asked Questions"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">General Python FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="general-python-faq">
<h1><a class="toc-backref" href="#id2" role="doc-backlink">General Python FAQ</a><a class="headerlink" href="#general-python-faq" title="Link to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#general-python-faq" id="id2">General Python FAQ</a></p>
<ul>
<li><p><a class="reference internal" href="#general-information" id="id3">General Information</a></p>
<ul>
<li><p><a class="reference internal" href="#what-is-python" id="id4">What is Python?</a></p></li>
<li><p><a class="reference internal" href="#what-is-the-python-software-foundation" id="id5">What is the Python Software Foundation?</a></p></li>
<li><p><a class="reference internal" href="#are-there-copyright-restrictions-on-the-use-of-python" id="id6">Are there copyright restrictions on the use of Python?</a></p></li>
<li><p><a class="reference internal" href="#why-was-python-created-in-the-first-place" id="id7">Why was Python created in the first place?</a></p></li>
<li><p><a class="reference internal" href="#what-is-python-good-for" id="id8">What is Python good for?</a></p></li>
<li><p><a class="reference internal" href="#how-does-the-python-version-numbering-scheme-work" id="id9">How does the Python version numbering scheme work?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-obtain-a-copy-of-the-python-source" id="id10">How do I obtain a copy of the Python source?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-get-documentation-on-python" id="id11">How do I get documentation on Python?</a></p></li>
<li><p><a class="reference internal" href="#i-ve-never-programmed-before-is-there-a-python-tutorial" id="id12">I’ve never programmed before. Is there a Python tutorial?</a></p></li>
<li><p><a class="reference internal" href="#is-there-a-newsgroup-or-mailing-list-devoted-to-python" id="id13">Is there a newsgroup or mailing list devoted to Python?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-get-a-beta-test-version-of-python" id="id14">How do I get a beta test version of Python?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-submit-bug-reports-and-patches-for-python" id="id15">How do I submit bug reports and patches for Python?</a></p></li>
<li><p><a class="reference internal" href="#are-there-any-published-articles-about-python-that-i-can-reference" id="id16">Are there any published articles about Python that I can reference?</a></p></li>
<li><p><a class="reference internal" href="#are-there-any-books-on-python" id="id17">Are there any books on Python?</a></p></li>
<li><p><a class="reference internal" href="#where-in-the-world-is-www-python-org-located" id="id18">Where in the world is www.python.org located?</a></p></li>
<li><p><a class="reference internal" href="#why-is-it-called-python" id="id19">Why is it called Python?</a></p></li>
<li><p><a class="reference internal" href="#do-i-have-to-like-monty-python-s-flying-circus" id="id20">Do I have to like “Monty Python’s Flying Circus”?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#python-in-the-real-world" id="id21">Python in the real world</a></p>
<ul>
<li><p><a class="reference internal" href="#how-stable-is-python" id="id22">How stable is Python?</a></p></li>
<li><p><a class="reference internal" href="#how-many-people-are-using-python" id="id23">How many people are using Python?</a></p></li>
<li><p><a class="reference internal" href="#have-any-significant-projects-been-done-in-python" id="id24">Have any significant projects been done in Python?</a></p></li>
<li><p><a class="reference internal" href="#what-new-developments-are-expected-for-python-in-the-future" id="id25">What new developments are expected for Python in the future?</a></p></li>
<li><p><a class="reference internal" href="#is-it-reasonable-to-propose-incompatible-changes-to-python" id="id26">Is it reasonable to propose incompatible changes to Python?</a></p></li>
<li><p><a class="reference internal" href="#is-python-a-good-language-for-beginning-programmers" id="id27">Is Python a good language for beginning programmers?</a></p></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
<section id="general-information">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">General Information</a><a class="headerlink" href="#general-information" title="Link to this heading">¶</a></h2>
<section id="what-is-python">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">What is Python?</a><a class="headerlink" href="#what-is-python" title="Link to this heading">¶</a></h3>
<p>Python is an interpreted, interactive, object-oriented programming language.  It
incorporates modules, exceptions, dynamic typing, very high level dynamic data
types, and classes.  It supports multiple programming paradigms beyond
object-oriented programming, such as procedural and functional programming.
Python combines remarkable power with very clear syntax. It has interfaces to
many system calls and libraries, as well as to various window systems, and is
extensible in C or C++.  It is also usable as an extension language for
applications that need a programmable interface. Finally, Python is portable:
it runs on many Unix variants including Linux and macOS, and on Windows.</p>
<p>To find out more, start with <a class="reference internal" href="../tutorial/index.html#tutorial-index"><span class="std std-ref">The Python Tutorial</span></a>.  The <a class="reference external" href="https://wiki.python.org/moin/BeginnersGuide">Beginner’s Guide to
Python</a> links to other
introductory tutorials and resources for learning Python.</p>
</section>
<section id="what-is-the-python-software-foundation">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">What is the Python Software Foundation?</a><a class="headerlink" href="#what-is-the-python-software-foundation" title="Link to this heading">¶</a></h3>
<p>The Python Software Foundation is an independent non-profit organization that
holds the copyright on Python versions 2.1 and newer.  The PSF’s mission is to
advance open source technology related to the Python programming language and to
publicize the use of Python.  The PSF’s home page is at
<a class="reference external" href="https://www.python.org/psf/">https://www.python.org/psf/</a>.</p>
<p>Donations to the PSF are tax-exempt in the US.  If you use Python and find it
helpful, please contribute via <a class="reference external" href="https://www.python.org/psf/donations/">the PSF donation page</a>.</p>
</section>
<section id="are-there-copyright-restrictions-on-the-use-of-python">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Are there copyright restrictions on the use of Python?</a><a class="headerlink" href="#are-there-copyright-restrictions-on-the-use-of-python" title="Link to this heading">¶</a></h3>
<p>You can do anything you want with the source, as long as you leave the
copyrights in and display those copyrights in any documentation about Python
that you produce.  If you honor the copyright rules, it’s OK to use Python for
commercial use, to sell copies of Python in source or binary form (modified or
unmodified), or to sell products that incorporate Python in some form.  We would
still like to know about all commercial use of Python, of course.</p>
<p>See <a class="reference external" href="https://docs.python.org/3/license.html">the license page</a> to find further
explanations and the full text of the PSF License.</p>
<p>The Python logo is trademarked, and in certain cases permission is required to
use it.  Consult <a class="reference external" href="https://www.python.org/psf/trademarks/">the Trademark Usage Policy</a> for more information.</p>
</section>
<section id="why-was-python-created-in-the-first-place">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Why was Python created in the first place?</a><a class="headerlink" href="#why-was-python-created-in-the-first-place" title="Link to this heading">¶</a></h3>
<p>Here’s a <em>very</em> brief summary of what started it all, written by Guido van
Rossum:</p>
<blockquote>
<div><p>I had extensive experience with implementing an interpreted language in the
ABC group at CWI, and from working with this group I had learned a lot about
language design.  This is the origin of many Python features, including the
use of indentation for statement grouping and the inclusion of
very-high-level data types (although the details are all different in
Python).</p>
<p>I had a number of gripes about the ABC language, but also liked many of its
features.  It was impossible to extend the ABC language (or its
implementation) to remedy my complaints – in fact its lack of extensibility
was one of its biggest problems.  I had some experience with using Modula-2+
and talked with the designers of Modula-3 and read the Modula-3 report.
Modula-3 is the origin of the syntax and semantics used for exceptions, and
some other Python features.</p>
<p>I was working in the Amoeba distributed operating system group at CWI.  We
needed a better way to do system administration than by writing either C
programs or Bourne shell scripts, since Amoeba had its own system call
interface which wasn’t easily accessible from the Bourne shell.  My
experience with error handling in Amoeba made me acutely aware of the
importance of exceptions as a programming language feature.</p>
<p>It occurred to me that a scripting language with a syntax like ABC but with
access to the Amoeba system calls would fill the need.  I realized that it
would be foolish to write an Amoeba-specific language, so I decided that I
needed a language that was generally extensible.</p>
<p>During the 1989 Christmas holidays, I had a lot of time on my hand, so I
decided to give it a try.  During the next year, while still mostly working
on it in my own time, Python was used in the Amoeba project with increasing
success, and the feedback from colleagues made me add many early
improvements.</p>
<p>In February 1991, after just over a year of development, I decided to post to
USENET.  The rest is in the <code class="docutils literal notranslate"><span class="pre">Misc/HISTORY</span></code> file.</p>
</div></blockquote>
</section>
<section id="what-is-python-good-for">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">What is Python good for?</a><a class="headerlink" href="#what-is-python-good-for" title="Link to this heading">¶</a></h3>
<p>Python is a high-level general-purpose programming language that can be applied
to many different classes of problems.</p>
<p>The language comes with a large standard library that covers areas such as
string processing (regular expressions, Unicode, calculating differences between
files), internet protocols (HTTP, FTP, SMTP, XML-RPC, POP, IMAP),
software engineering (unit testing, logging, profiling, parsing
Python code), and operating system interfaces (system calls, filesystems, TCP/IP
sockets).  Look at the table of contents for <a class="reference internal" href="../library/index.html#library-index"><span class="std std-ref">The Python Standard Library</span></a> to get an idea
of what’s available.  A wide variety of third-party extensions are also
available.  Consult <a class="reference external" href="https://pypi.org">the Python Package Index</a> to
find packages of interest to you.</p>
</section>
<section id="how-does-the-python-version-numbering-scheme-work">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">How does the Python version numbering scheme work?</a><a class="headerlink" href="#how-does-the-python-version-numbering-scheme-work" title="Link to this heading">¶</a></h3>
<p>Python versions are numbered “A.B.C” or “A.B”:</p>
<ul class="simple">
<li><p><em>A</em> is the major version number – it is only incremented for really major
changes in the language.</p></li>
<li><p><em>B</em> is the minor version number – it is incremented for less earth-shattering
changes.</p></li>
<li><p><em>C</em> is the micro version number – it is incremented for each bugfix release.</p></li>
</ul>
<p>Not all releases are bugfix releases.  In the run-up to a new feature release, a
series of development releases are made, denoted as alpha, beta, or release
candidate.  Alphas are early releases in which interfaces aren’t yet finalized;
it’s not unexpected to see an interface change between two alpha releases.
Betas are more stable, preserving existing interfaces but possibly adding new
modules, and release candidates are frozen, making no changes except as needed
to fix critical bugs.</p>
<p>Alpha, beta and release candidate versions have an additional suffix:</p>
<ul class="simple">
<li><p>The suffix for an alpha version is “aN” for some small number <em>N</em>.</p></li>
<li><p>The suffix for a beta version is “bN” for some small number <em>N</em>.</p></li>
<li><p>The suffix for a release candidate version is “rcN” for some small number <em>N</em>.</p></li>
</ul>
<p>In other words, all versions labeled <em>2.0aN</em> precede the versions labeled
<em>2.0bN</em>, which precede versions labeled <em>2.0rcN</em>, and <em>those</em> precede 2.0.</p>
<p>You may also find version numbers with a “+” suffix, e.g. “2.2+”.  These are
unreleased versions, built directly from the CPython development repository.  In
practice, after a final minor release is made, the version is incremented to the
next minor version, which becomes the “a0” version, e.g. “2.4a0”.</p>
<p>See the <a class="reference external" href="https://devguide.python.org/developer-workflow/development-cycle/">Developer’s Guide</a>
for more information about the development cycle, and
<span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0387/"><strong>PEP 387</strong></a> to learn more about Python’s backward compatibility policy.  See also
the documentation for <a class="reference internal" href="../library/sys.html#sys.version" title="sys.version"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.version</span></code></a>, <a class="reference internal" href="../library/sys.html#sys.hexversion" title="sys.hexversion"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.hexversion</span></code></a>, and
<a class="reference internal" href="../library/sys.html#sys.version_info" title="sys.version_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.version_info</span></code></a>.</p>
</section>
<section id="how-do-i-obtain-a-copy-of-the-python-source">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">How do I obtain a copy of the Python source?</a><a class="headerlink" href="#how-do-i-obtain-a-copy-of-the-python-source" title="Link to this heading">¶</a></h3>
<p>The latest Python source distribution is always available from python.org, at
<a class="reference external" href="https://www.python.org/downloads/">https://www.python.org/downloads/</a>.  The latest development sources can be obtained
at <a class="reference external" href="https://github.com/python/cpython/">https://github.com/python/cpython/</a>.</p>
<p>The source distribution is a gzipped tar file containing the complete C source,
Sphinx-formatted documentation, Python library modules, example programs, and
several useful pieces of freely distributable software.  The source will compile
and run out of the box on most UNIX platforms.</p>
<p>Consult the <a class="reference external" href="https://devguide.python.org/setup/">Getting Started section of the Python Developer’s Guide</a> for more
information on getting the source code and compiling it.</p>
</section>
<section id="how-do-i-get-documentation-on-python">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">How do I get documentation on Python?</a><a class="headerlink" href="#how-do-i-get-documentation-on-python" title="Link to this heading">¶</a></h3>
<p>The standard documentation for the current stable version of Python is available
at <a class="reference external" href="https://docs.python.org/3/">https://docs.python.org/3/</a>.  PDF, plain text, and downloadable HTML versions are
also available at <a class="reference external" href="https://docs.python.org/3/download.html">https://docs.python.org/3/download.html</a>.</p>
<p>The documentation is written in reStructuredText and processed by <a class="reference external" href="https://www.sphinx-doc.org/">the Sphinx
documentation tool</a>.  The reStructuredText source for
the documentation is part of the Python source distribution.</p>
</section>
<section id="i-ve-never-programmed-before-is-there-a-python-tutorial">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">I’ve never programmed before. Is there a Python tutorial?</a><a class="headerlink" href="#i-ve-never-programmed-before-is-there-a-python-tutorial" title="Link to this heading">¶</a></h3>
<p>There are numerous tutorials and books available.  The standard documentation
includes <a class="reference internal" href="../tutorial/index.html#tutorial-index"><span class="std std-ref">The Python Tutorial</span></a>.</p>
<p>Consult <a class="reference external" href="https://wiki.python.org/moin/BeginnersGuide">the Beginner’s Guide</a> to
find information for beginning Python programmers, including lists of tutorials.</p>
</section>
<section id="is-there-a-newsgroup-or-mailing-list-devoted-to-python">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Is there a newsgroup or mailing list devoted to Python?</a><a class="headerlink" href="#is-there-a-newsgroup-or-mailing-list-devoted-to-python" title="Link to this heading">¶</a></h3>
<p>There is a newsgroup, <em class="newsgroup">comp.lang.python</em>, and a mailing list,
<a class="reference external" href="https://mail.python.org/mailman/listinfo/python-list">python-list</a>.  The
newsgroup and mailing list are gatewayed into each other – if you can read news
it’s unnecessary to subscribe to the mailing list.
<em class="newsgroup">comp.lang.python</em> is high-traffic, receiving hundreds of postings
every day, and Usenet readers are often more able to cope with this volume.</p>
<p>Announcements of new software releases and events can be found in
comp.lang.python.announce, a low-traffic moderated list that receives about five
postings per day.  It’s available as <a class="reference external" href="https://mail.python.org/mailman3/lists/python-announce-list.python.org/">the python-announce mailing list</a>.</p>
<p>More info about other mailing lists and newsgroups
can be found at <a class="reference external" href="https://www.python.org/community/lists/">https://www.python.org/community/lists/</a>.</p>
</section>
<section id="how-do-i-get-a-beta-test-version-of-python">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">How do I get a beta test version of Python?</a><a class="headerlink" href="#how-do-i-get-a-beta-test-version-of-python" title="Link to this heading">¶</a></h3>
<p>Alpha and beta releases are available from <a class="reference external" href="https://www.python.org/downloads/">https://www.python.org/downloads/</a>.  All
releases are announced on the comp.lang.python and comp.lang.python.announce
newsgroups and on the Python home page at <a class="reference external" href="https://www.python.org/">https://www.python.org/</a>; an RSS feed of
news is available.</p>
<p>You can also access the development version of Python through Git.  See
<a class="reference external" href="https://devguide.python.org/">The Python Developer’s Guide</a> for details.</p>
</section>
<section id="how-do-i-submit-bug-reports-and-patches-for-python">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">How do I submit bug reports and patches for Python?</a><a class="headerlink" href="#how-do-i-submit-bug-reports-and-patches-for-python" title="Link to this heading">¶</a></h3>
<p>To report a bug or submit a patch, use the issue tracker at
<a class="reference external" href="https://github.com/python/cpython/issues">https://github.com/python/cpython/issues</a>.</p>
<p>For more information on how Python is developed, consult <a class="reference external" href="https://devguide.python.org/">the Python Developer’s
Guide</a>.</p>
</section>
<section id="are-there-any-published-articles-about-python-that-i-can-reference">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">Are there any published articles about Python that I can reference?</a><a class="headerlink" href="#are-there-any-published-articles-about-python-that-i-can-reference" title="Link to this heading">¶</a></h3>
<p>It’s probably best to cite your favorite book about Python.</p>
<p>The <a class="reference external" href="https://ir.cwi.nl/pub/18204">very first article</a> about Python was
written in 1991 and is now quite outdated.</p>
<blockquote>
<div><p>Guido van Rossum and Jelke de Boer, “Interactively Testing Remote Servers
Using the Python Programming Language”, CWI Quarterly, Volume 4, Issue 4
(December 1991), Amsterdam, pp 283–303.</p>
</div></blockquote>
</section>
<section id="are-there-any-books-on-python">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Are there any books on Python?</a><a class="headerlink" href="#are-there-any-books-on-python" title="Link to this heading">¶</a></h3>
<p>Yes, there are many, and more are being published.  See the python.org wiki at
<a class="reference external" href="https://wiki.python.org/moin/PythonBooks">https://wiki.python.org/moin/PythonBooks</a> for a list.</p>
<p>You can also search online bookstores for “Python” and filter out the Monty
Python references; or perhaps search for “Python” and “language”.</p>
</section>
<section id="where-in-the-world-is-www-python-org-located">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">Where in the world is www.python.org located?</a><a class="headerlink" href="#where-in-the-world-is-www-python-org-located" title="Link to this heading">¶</a></h3>
<p>The Python project’s infrastructure is located all over the world and is managed
by the Python Infrastructure Team. Details <a class="reference external" href="https://infra.psf.io">here</a>.</p>
</section>
<section id="why-is-it-called-python">
<h3><a class="toc-backref" href="#id19" role="doc-backlink">Why is it called Python?</a><a class="headerlink" href="#why-is-it-called-python" title="Link to this heading">¶</a></h3>
<p>When he began implementing Python, Guido van Rossum was also reading the
published scripts from <a class="reference external" href="https://en.wikipedia.org/wiki/Monty_Python">“Monty Python’s Flying Circus”</a>, a BBC comedy series from the 1970s.  Van Rossum
thought he needed a name that was short, unique, and slightly mysterious, so he
decided to call the language Python.</p>
</section>
<section id="do-i-have-to-like-monty-python-s-flying-circus">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">Do I have to like “Monty Python’s Flying Circus”?</a><a class="headerlink" href="#do-i-have-to-like-monty-python-s-flying-circus" title="Link to this heading">¶</a></h3>
<p>No, but it helps.  :)</p>
</section>
</section>
<section id="python-in-the-real-world">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Python in the real world</a><a class="headerlink" href="#python-in-the-real-world" title="Link to this heading">¶</a></h2>
<section id="how-stable-is-python">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">How stable is Python?</a><a class="headerlink" href="#how-stable-is-python" title="Link to this heading">¶</a></h3>
<p>Very stable.  New, stable releases have been coming out roughly every 6 to 18
months since 1991, and this seems likely to continue.  As of version 3.9,
Python will have a new feature release every 12 months (<span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0602/"><strong>PEP 602</strong></a>).</p>
<p>The developers issue bugfix releases of older versions, so the stability of
existing releases gradually improves.  Bugfix releases, indicated by a third
component of the version number (e.g. 3.5.3, 3.6.2), are managed for stability;
only fixes for known problems are included in a bugfix release, and it’s
guaranteed that interfaces will remain the same throughout a series of bugfix
releases.</p>
<p>The latest stable releases can always be found on the <a class="reference external" href="https://www.python.org/downloads/">Python download page</a>.  There are two production-ready versions
of Python: 2.x and 3.x. The recommended version is 3.x, which is supported by
most widely used libraries.  Although 2.x is still widely used, <a class="reference external" href="https://peps.python.org/pep-0373/">it is not
maintained anymore</a>.</p>
</section>
<section id="how-many-people-are-using-python">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">How many people are using Python?</a><a class="headerlink" href="#how-many-people-are-using-python" title="Link to this heading">¶</a></h3>
<p>There are probably millions of users, though it’s difficult to obtain an exact
count.</p>
<p>Python is available for free download, so there are no sales figures, and it’s
available from many different sites and packaged with many Linux distributions,
so download statistics don’t tell the whole story either.</p>
<p>The comp.lang.python newsgroup is very active, but not all Python users post to
the group or even read it.</p>
</section>
<section id="have-any-significant-projects-been-done-in-python">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">Have any significant projects been done in Python?</a><a class="headerlink" href="#have-any-significant-projects-been-done-in-python" title="Link to this heading">¶</a></h3>
<p>See <a class="reference external" href="https://www.python.org/about/success">https://www.python.org/about/success</a> for a list of projects that use Python.
Consulting the proceedings for <a class="reference external" href="https://www.python.org/community/workshops/">past Python conferences</a> will reveal contributions from many
different companies and organizations.</p>
<p>High-profile Python projects include <a class="reference external" href="https://www.list.org">the Mailman mailing list manager</a> and <a class="reference external" href="https://www.zope.dev">the Zope application server</a>.  Several Linux distributions, most notably <a class="reference external" href="https://www.redhat.com">Red Hat</a>, have written part or all of their installer and
system administration software in Python.  Companies that use Python internally
include Google, Yahoo, and Lucasfilm Ltd.</p>
</section>
<section id="what-new-developments-are-expected-for-python-in-the-future">
<h3><a class="toc-backref" href="#id25" role="doc-backlink">What new developments are expected for Python in the future?</a><a class="headerlink" href="#what-new-developments-are-expected-for-python-in-the-future" title="Link to this heading">¶</a></h3>
<p>See <a class="reference external" href="https://peps.python.org/">https://peps.python.org/</a> for the Python Enhancement Proposals
(PEPs). PEPs are design documents describing a suggested new feature for Python,
providing a concise technical specification and a rationale.  Look for a PEP
titled “Python X.Y Release Schedule”, where X.Y is a version that hasn’t been
publicly released yet.</p>
<p>New development is discussed on <a class="reference external" href="https://mail.python.org/mailman3/lists/python-dev.python.org/">the python-dev mailing list</a>.</p>
</section>
<section id="is-it-reasonable-to-propose-incompatible-changes-to-python">
<h3><a class="toc-backref" href="#id26" role="doc-backlink">Is it reasonable to propose incompatible changes to Python?</a><a class="headerlink" href="#is-it-reasonable-to-propose-incompatible-changes-to-python" title="Link to this heading">¶</a></h3>
<p>In general, no.  There are already millions of lines of Python code around the
world, so any change in the language that invalidates more than a very small
fraction of existing programs has to be frowned upon.  Even if you can provide a
conversion program, there’s still the problem of updating all documentation;
many books have been written about Python, and we don’t want to invalidate them
all at a single stroke.</p>
<p>Providing a gradual upgrade path is necessary if a feature has to be changed.
<span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0005/"><strong>PEP 5</strong></a> describes the procedure followed for introducing backward-incompatible
changes while minimizing disruption for users.</p>
</section>
<section id="is-python-a-good-language-for-beginning-programmers">
<h3><a class="toc-backref" href="#id27" role="doc-backlink">Is Python a good language for beginning programmers?</a><a class="headerlink" href="#is-python-a-good-language-for-beginning-programmers" title="Link to this heading">¶</a></h3>
<p>Yes.</p>
<p>It is still common to start students with a procedural and statically typed
language such as Pascal, C, or a subset of C++ or Java.  Students may be better
served by learning Python as their first language.  Python has a very simple and
consistent syntax and a large standard library and, most importantly, using
Python in a beginning programming course lets students concentrate on important
programming skills such as problem decomposition and data type design.  With
Python, students can be quickly introduced to basic concepts such as loops and
procedures.  They can probably even work with user-defined objects in their very
first course.</p>
<p>For a student who has never programmed before, using a statically typed language
seems unnatural.  It presents additional complexity that the student must master
and slows the pace of the course.  The students are trying to learn to think
like a computer, decompose problems, design consistent interfaces, and
encapsulate data.  While learning to use a statically typed language is
important in the long term, it is not necessarily the best topic to address in
the students’ first programming course.</p>
<p>Many other aspects of Python make it a good first language.  Like Java, Python
has a large standard library so that students can be assigned programming
projects very early in the course that <em>do</em> something.  Assignments aren’t
restricted to the standard four-function calculator and check balancing
programs.  By using the standard library, students can gain the satisfaction of
working on realistic applications as they learn the fundamentals of programming.
Using the standard library also teaches students about code reuse.  Third-party
modules such as PyGame are also helpful in extending the students’ reach.</p>
<p>Python’s interactive interpreter enables students to test language features
while they’re programming.  They can keep a window with the interpreter running
while they enter their program’s source in another window.  If they can’t
remember the methods for a list, they can do something like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">L</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">L</span><span class="p">)</span> 
<span class="go">[&#39;__add__&#39;, &#39;__class__&#39;, &#39;__contains__&#39;, &#39;__delattr__&#39;, &#39;__delitem__&#39;,</span>
<span class="go">&#39;__dir__&#39;, &#39;__doc__&#39;, &#39;__eq__&#39;, &#39;__format__&#39;, &#39;__ge__&#39;,</span>
<span class="go">&#39;__getattribute__&#39;, &#39;__getitem__&#39;, &#39;__gt__&#39;, &#39;__hash__&#39;, &#39;__iadd__&#39;,</span>
<span class="go">&#39;__imul__&#39;, &#39;__init__&#39;, &#39;__iter__&#39;, &#39;__le__&#39;, &#39;__len__&#39;, &#39;__lt__&#39;,</span>
<span class="go">&#39;__mul__&#39;, &#39;__ne__&#39;, &#39;__new__&#39;, &#39;__reduce__&#39;, &#39;__reduce_ex__&#39;,</span>
<span class="go">&#39;__repr__&#39;, &#39;__reversed__&#39;, &#39;__rmul__&#39;, &#39;__setattr__&#39;, &#39;__setitem__&#39;,</span>
<span class="go">&#39;__sizeof__&#39;, &#39;__str__&#39;, &#39;__subclasshook__&#39;, &#39;append&#39;, &#39;clear&#39;,</span>
<span class="go">&#39;copy&#39;, &#39;count&#39;, &#39;extend&#39;, &#39;index&#39;, &#39;insert&#39;, &#39;pop&#39;, &#39;remove&#39;,</span>
<span class="go">&#39;reverse&#39;, &#39;sort&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">d</span> <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="nb">dir</span><span class="p">(</span><span class="n">L</span><span class="p">)</span> <span class="k">if</span> <span class="s1">&#39;__&#39;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">d</span><span class="p">]</span>
<span class="go">[&#39;append&#39;, &#39;clear&#39;, &#39;copy&#39;, &#39;count&#39;, &#39;extend&#39;, &#39;index&#39;, &#39;insert&#39;, &#39;pop&#39;, &#39;remove&#39;, &#39;reverse&#39;, &#39;sort&#39;]</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">help</span><span class="p">(</span><span class="n">L</span><span class="o">.</span><span class="n">append</span><span class="p">)</span>
<span class="go">Help on built-in function append:</span>

<span class="go">append(...)</span>
<span class="go">    L.append(object) -&gt; None -- append object to end</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">L</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">L</span>
<span class="go">[1]</span>
</pre></div>
</div>
<p>With the interpreter, documentation is never far from the student as they are
programming.</p>
<p>There are also good IDEs for Python.  IDLE is a cross-platform IDE for Python
that is written in Python using Tkinter.
Emacs users will be happy to know that there is a very good Python mode for
Emacs.  All of these programming environments provide syntax highlighting,
auto-indenting, and access to the interactive interpreter while coding.  Consult
<a class="reference external" href="https://wiki.python.org/moin/PythonEditors">the Python wiki</a> for a full list
of Python editing environments.</p>
<p>If you want to discuss Python’s use in education, you may be interested in
joining <a class="reference external" href="https://www.python.org/community/sigs/current/edu-sig">the edu-sig mailing list</a>.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">General Python FAQ</a><ul>
<li><a class="reference internal" href="#general-information">General Information</a></li>
<li><a class="reference internal" href="#python-in-the-real-world">Python in the real world</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="index.html"
                          title="previous chapter">Python Frequently Asked Questions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="programming.html"
                          title="next chapter">Programming FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/general.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="programming.html" title="Programming FAQ"
             >next</a> |</li>
        <li class="right" >
          <a href="index.html" title="Python Frequently Asked Questions"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">General Python FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>